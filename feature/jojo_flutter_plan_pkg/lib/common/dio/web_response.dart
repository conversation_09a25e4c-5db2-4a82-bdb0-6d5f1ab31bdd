import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/net/base_interceptor.dart';
import 'package:jojo_flutter_base/utils/log.dart';

class WebRespInterceptor extends BaseInterceptor {
  WebRespInterceptor() : super(tag: 'WebRespInterceptor');

  @override
  Future onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) async {
    l.i(tag, '自定义web响应拦截');

    handler.next(response);
  }
}
