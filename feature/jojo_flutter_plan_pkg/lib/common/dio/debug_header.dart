import 'dart:math';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/net/base_interceptor.dart';

class DebugHeaderInterceptor extends BaseInterceptor {
  DebugHeaderInterceptor() : super(tag: 'DebugHeaderInterceptor');

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (BaseConfig.debug) {
      // options.headers["X-UAGW-userId"] = '10022400';
      // options.headers["X-UAGW-userId"] = '10036050';
      options.headers["X-UAGW-userId"] = '200500520';
            // options.headers["X-UAGW-userId"] = '200529000';

      // options.headers["X-UAGW-userId"] = '200452851'; // 无科目纯享版
      // options.headers["X-UAGW-userId"] = '200453040'; // 有科目纯享版
      // options.headers["X-UAGW-userId"] = '10025897';
      // options.headers["X-UAGW-userId"] = '100102';
      // options.headers["X-UAGW-userId"] = '70010367';
      // options.headers["X-UAGW-userId"] = '70010425';
      // options.headers["X-UAGW-userId"] = '10029432';
      // options.headers["X-UAGW-userId"] = '10023636';
      // options.headers["X-UAGW-userId"] = '643298';
      // options.headers["X-UAGW-userId"] = '30959';
      // options.headers["X-UAGW-userId"] = '31455';
      options.headers["X-UAGW-authMode"] = 1;
      options.headers["X-UAGW-Request-ID"] = random();
      // options.headers["x-mock-invoke-mode"] = 2; // mock模式
      // options.headers["X-UAGW-grayLaneRuleKey"] = 'fat1';
      options.headers["TM-UserAgent-productVersion"] = '2.5.0';
    }
    return handler.next(options);
  }

  String random() {
    const String chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    Random random = Random();

    return String.fromCharCodes(Iterable.generate(
      10,
      (_) => chars.codeUnitAt(random.nextInt(chars.length)),
    ));
  }
}
