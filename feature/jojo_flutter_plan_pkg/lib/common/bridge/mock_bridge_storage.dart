import 'package:jojo_flutter_base/base.dart';
import 'mock_brige.dart';

class StorageTestJoJoBridgeCommonMocker extends JoJoBridgeCommonMocker {
  // 内存存储模拟
  final Map<String, String> _mockStorage = {};

  @override
  Future<JoJoBridgeResponse<NativeValue>> operationNativeValueGet({
    required String key,
    bool? isUserData,
  }) async {
    final value = _mockStorage[key];
    return JoJoBridgeResponse(
      200,
      'ok',
      value != null ? NativeValue(value: value) : null,
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> operationNativeValueSet({
    required String key,
    required String value,
    bool? isUserData,
  }) async {
    _mockStorage[key] = value;
    return JoJoBridgeResponse(200, 'ok', null);
  }

  /// 辅助方法：设置 mock 值（用于测试初始化）
  void setMockValue(String key, String value) {
    _mockStorage[key] = value;
  }

  /// 辅助方法：获取 mock 值（验证使用）
  String? getMockValue(String key) {
    return _mockStorage[key];
  }

  /// 清空所有 mock 数据（用于隔离测试）
  void clear() {
    _mockStorage.clear();
  }
}