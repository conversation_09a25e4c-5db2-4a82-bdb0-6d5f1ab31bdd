class EnvWeb {
  // 构建环境常量
  static String ENV_NAME = const String.fromEnvironment(
    'ENV_NAME',
    defaultValue: 'pro',
  ).toUpperCase();

  // 当前构建tag号
  static String CI_TAG_NAME = const String.fromEnvironment('CI_TAG_NAME');

  // 页面路由访问前缀，应该跟index.html中保持一致
  static String ENV_BASE = const String.fromEnvironment(
    'ENV_BASE',
    defaultValue: '/read/plan/',
  );

  // 静态资源访问域名
  static String CDN_DOMAIN = const String.fromEnvironment(
    'CDN_DOMAIN',
    defaultValue: 'https://jojopublic.tinman.cn',
  );

  // 静态资源访问前缀
  static String CDN_PREFIX = const String.fromEnvironment(
    'CDN_PREFIX',
    defaultValue: '/jojoread/flutter-plan-pkg/',
  );

  // 静态资源访问完整路径
  static String get CDN_URI {
    return CDN_DOMAIN + CDN_PREFIX;
  }
}
