// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medal_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MyMedalsData _$MyMedalsDataFromJson(Map<String, dynamic> json) {
  return _MyMedalsData.fromJson(json);
}

/// @nodoc
mixin _$MyMedalsData {
  /// 课程信息
  CourseInfo? get courseInfo => throw _privateConstructorUsedError;

  /// 分享信息
  ShareInfo? get shareInfo => throw _privateConstructorUsedError;

  /// 奖章列表
  List<MedalData>? get medalList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MyMedalsDataCopyWith<MyMedalsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MyMedalsDataCopyWith<$Res> {
  factory $MyMedalsDataCopyWith(
          MyMedalsData value, $Res Function(MyMedalsData) then) =
      _$MyMedalsDataCopyWithImpl<$Res, MyMedalsData>;
  @useResult
  $Res call(
      {CourseInfo? courseInfo,
      ShareInfo? shareInfo,
      List<MedalData>? medalList});

  $CourseInfoCopyWith<$Res>? get courseInfo;
  $ShareInfoCopyWith<$Res>? get shareInfo;
}

/// @nodoc
class _$MyMedalsDataCopyWithImpl<$Res, $Val extends MyMedalsData>
    implements $MyMedalsDataCopyWith<$Res> {
  _$MyMedalsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? shareInfo = freezed,
    Object? medalList = freezed,
  }) {
    return _then(_value.copyWith(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      medalList: freezed == medalList
          ? _value.medalList
          : medalList // ignore: cast_nullable_to_non_nullable
              as List<MedalData>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseInfoCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $CourseInfoCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ShareInfoCopyWith<$Res>? get shareInfo {
    if (_value.shareInfo == null) {
      return null;
    }

    return $ShareInfoCopyWith<$Res>(_value.shareInfo!, (value) {
      return _then(_value.copyWith(shareInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MyMedalsDataCopyWith<$Res>
    implements $MyMedalsDataCopyWith<$Res> {
  factory _$$_MyMedalsDataCopyWith(
          _$_MyMedalsData value, $Res Function(_$_MyMedalsData) then) =
      __$$_MyMedalsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CourseInfo? courseInfo,
      ShareInfo? shareInfo,
      List<MedalData>? medalList});

  @override
  $CourseInfoCopyWith<$Res>? get courseInfo;
  @override
  $ShareInfoCopyWith<$Res>? get shareInfo;
}

/// @nodoc
class __$$_MyMedalsDataCopyWithImpl<$Res>
    extends _$MyMedalsDataCopyWithImpl<$Res, _$_MyMedalsData>
    implements _$$_MyMedalsDataCopyWith<$Res> {
  __$$_MyMedalsDataCopyWithImpl(
      _$_MyMedalsData _value, $Res Function(_$_MyMedalsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? shareInfo = freezed,
    Object? medalList = freezed,
  }) {
    return _then(_$_MyMedalsData(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      medalList: freezed == medalList
          ? _value._medalList
          : medalList // ignore: cast_nullable_to_non_nullable
              as List<MedalData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MyMedalsData implements _MyMedalsData {
  _$_MyMedalsData(
      {required this.courseInfo,
      required this.shareInfo,
      required final List<MedalData>? medalList})
      : _medalList = medalList;

  factory _$_MyMedalsData.fromJson(Map<String, dynamic> json) =>
      _$$_MyMedalsDataFromJson(json);

  /// 课程信息
  @override
  final CourseInfo? courseInfo;

  /// 分享信息
  @override
  final ShareInfo? shareInfo;

  /// 奖章列表
  final List<MedalData>? _medalList;

  /// 奖章列表
  @override
  List<MedalData>? get medalList {
    final value = _medalList;
    if (value == null) return null;
    if (_medalList is EqualUnmodifiableListView) return _medalList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MyMedalsData(courseInfo: $courseInfo, shareInfo: $shareInfo, medalList: $medalList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MyMedalsData &&
            (identical(other.courseInfo, courseInfo) ||
                other.courseInfo == courseInfo) &&
            (identical(other.shareInfo, shareInfo) ||
                other.shareInfo == shareInfo) &&
            const DeepCollectionEquality()
                .equals(other._medalList, _medalList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseInfo, shareInfo,
      const DeepCollectionEquality().hash(_medalList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MyMedalsDataCopyWith<_$_MyMedalsData> get copyWith =>
      __$$_MyMedalsDataCopyWithImpl<_$_MyMedalsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MyMedalsDataToJson(
      this,
    );
  }
}

abstract class _MyMedalsData implements MyMedalsData {
  factory _MyMedalsData(
      {required final CourseInfo? courseInfo,
      required final ShareInfo? shareInfo,
      required final List<MedalData>? medalList}) = _$_MyMedalsData;

  factory _MyMedalsData.fromJson(Map<String, dynamic> json) =
      _$_MyMedalsData.fromJson;

  @override

  /// 课程信息
  CourseInfo? get courseInfo;
  @override

  /// 分享信息
  ShareInfo? get shareInfo;
  @override

  /// 奖章列表
  List<MedalData>? get medalList;
  @override
  @JsonKey(ignore: true)
  _$$_MyMedalsDataCopyWith<_$_MyMedalsData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfo _$CourseInfoFromJson(Map<String, dynamic> json) {
  return _CourseInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseInfo {
  /// 科目类型
  int? get subjectType => throw _privateConstructorUsedError;

  /// 课程键
  String? get courseKey => throw _privateConstructorUsedError;

  /// 课程ID
  int? get courseId => throw _privateConstructorUsedError;

  /// 课程段代码
  int? get courseSegmentCode => throw _privateConstructorUsedError;

  /// 课程段名称
  String? get courseSegmentName => throw _privateConstructorUsedError;

  /// 课程名称
  String? get courseName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoCopyWith<CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoCopyWith<$Res> {
  factory $CourseInfoCopyWith(
          CourseInfo value, $Res Function(CourseInfo) then) =
      _$CourseInfoCopyWithImpl<$Res, CourseInfo>;
  @useResult
  $Res call(
      {int? subjectType,
      String? courseKey,
      int? courseId,
      int? courseSegmentCode,
      String? courseSegmentName,
      String? courseName});
}

/// @nodoc
class _$CourseInfoCopyWithImpl<$Res, $Val extends CourseInfo>
    implements $CourseInfoCopyWith<$Res> {
  _$CourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? courseKey = freezed,
    Object? courseId = freezed,
    Object? courseSegmentCode = freezed,
    Object? courseSegmentName = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoCopyWith<$Res>
    implements $CourseInfoCopyWith<$Res> {
  factory _$$_CourseInfoCopyWith(
          _$_CourseInfo value, $Res Function(_$_CourseInfo) then) =
      __$$_CourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? courseKey,
      int? courseId,
      int? courseSegmentCode,
      String? courseSegmentName,
      String? courseName});
}

/// @nodoc
class __$$_CourseInfoCopyWithImpl<$Res>
    extends _$CourseInfoCopyWithImpl<$Res, _$_CourseInfo>
    implements _$$_CourseInfoCopyWith<$Res> {
  __$$_CourseInfoCopyWithImpl(
      _$_CourseInfo _value, $Res Function(_$_CourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? courseKey = freezed,
    Object? courseId = freezed,
    Object? courseSegmentCode = freezed,
    Object? courseSegmentName = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_$_CourseInfo(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfo implements _CourseInfo {
  _$_CourseInfo(
      {required this.subjectType,
      required this.courseKey,
      required this.courseId,
      required this.courseSegmentCode,
      required this.courseSegmentName,
      required this.courseName});

  factory _$_CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoFromJson(json);

  /// 科目类型
  @override
  final int? subjectType;

  /// 课程键
  @override
  final String? courseKey;

  /// 课程ID
  @override
  final int? courseId;

  /// 课程段代码
  @override
  final int? courseSegmentCode;

  /// 课程段名称
  @override
  final String? courseSegmentName;

  /// 课程名称
  @override
  final String? courseName;

  @override
  String toString() {
    return 'CourseInfo(subjectType: $subjectType, courseKey: $courseKey, courseId: $courseId, courseSegmentCode: $courseSegmentCode, courseSegmentName: $courseSegmentName, courseName: $courseName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfo &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseSegmentCode, courseSegmentCode) ||
                other.courseSegmentCode == courseSegmentCode) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, courseKey, courseId,
      courseSegmentCode, courseSegmentName, courseName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      __$$_CourseInfoCopyWithImpl<_$_CourseInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoToJson(
      this,
    );
  }
}

abstract class _CourseInfo implements CourseInfo {
  factory _CourseInfo(
      {required final int? subjectType,
      required final String? courseKey,
      required final int? courseId,
      required final int? courseSegmentCode,
      required final String? courseSegmentName,
      required final String? courseName}) = _$_CourseInfo;

  factory _CourseInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseInfo.fromJson;

  @override

  /// 科目类型
  int? get subjectType;
  @override

  /// 课程键
  String? get courseKey;
  @override

  /// 课程ID
  int? get courseId;
  @override

  /// 课程段代码
  int? get courseSegmentCode;
  @override

  /// 课程段名称
  String? get courseSegmentName;
  @override

  /// 课程名称
  String? get courseName;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

MedalData _$MedalDataFromJson(Map<String, dynamic> json) {
  return _MedalData.fromJson(json);
}

/// @nodoc
mixin _$MedalData {
  /// 奖章ID
  int? get medalId => throw _privateConstructorUsedError;

  /// 是否已完成
  bool? get hasMedal => throw _privateConstructorUsedError;

  /// 奖章名称
  String? get medalName => throw _privateConstructorUsedError;

  /// 奖章描述
  String? get medalRemark => throw _privateConstructorUsedError;

  /// 课程阶段名称
  String? get labelContent => throw _privateConstructorUsedError;

  /// 未解锁图片的URL
  String? get lockImage => throw _privateConstructorUsedError;

  /// 解锁图片的URL
  String? get unlockImage => throw _privateConstructorUsedError;

  /// 安卓资源包的URL
  String? get resourceAndroid => throw _privateConstructorUsedError;

  /// iOS资源包的URL
  String? get resourceIos => throw _privateConstructorUsedError;

  /// Flutter资源包的URL
  String? get resourceFlutter => throw _privateConstructorUsedError;

  /// 获得时间，时间戳
  int? get getTime => throw _privateConstructorUsedError;

  /// 未获得奖章提示
  String? get tipInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalDataCopyWith<MedalData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalDataCopyWith<$Res> {
  factory $MedalDataCopyWith(MedalData value, $Res Function(MedalData) then) =
      _$MedalDataCopyWithImpl<$Res, MedalData>;
  @useResult
  $Res call(
      {int? medalId,
      bool? hasMedal,
      String? medalName,
      String? medalRemark,
      String? labelContent,
      String? lockImage,
      String? unlockImage,
      String? resourceAndroid,
      String? resourceIos,
      String? resourceFlutter,
      int? getTime,
      String? tipInfo});
}

/// @nodoc
class _$MedalDataCopyWithImpl<$Res, $Val extends MedalData>
    implements $MedalDataCopyWith<$Res> {
  _$MedalDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medalId = freezed,
    Object? hasMedal = freezed,
    Object? medalName = freezed,
    Object? medalRemark = freezed,
    Object? labelContent = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceAndroid = freezed,
    Object? resourceIos = freezed,
    Object? resourceFlutter = freezed,
    Object? getTime = freezed,
    Object? tipInfo = freezed,
  }) {
    return _then(_value.copyWith(
      medalId: freezed == medalId
          ? _value.medalId
          : medalId // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMedal: freezed == hasMedal
          ? _value.hasMedal
          : hasMedal // ignore: cast_nullable_to_non_nullable
              as bool?,
      medalName: freezed == medalName
          ? _value.medalName
          : medalName // ignore: cast_nullable_to_non_nullable
              as String?,
      medalRemark: freezed == medalRemark
          ? _value.medalRemark
          : medalRemark // ignore: cast_nullable_to_non_nullable
              as String?,
      labelContent: freezed == labelContent
          ? _value.labelContent
          : labelContent // ignore: cast_nullable_to_non_nullable
              as String?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceAndroid: freezed == resourceAndroid
          ? _value.resourceAndroid
          : resourceAndroid // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceIos: freezed == resourceIos
          ? _value.resourceIos
          : resourceIos // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      tipInfo: freezed == tipInfo
          ? _value.tipInfo
          : tipInfo // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalDataCopyWith<$Res> implements $MedalDataCopyWith<$Res> {
  factory _$$_MedalDataCopyWith(
          _$_MedalData value, $Res Function(_$_MedalData) then) =
      __$$_MedalDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? medalId,
      bool? hasMedal,
      String? medalName,
      String? medalRemark,
      String? labelContent,
      String? lockImage,
      String? unlockImage,
      String? resourceAndroid,
      String? resourceIos,
      String? resourceFlutter,
      int? getTime,
      String? tipInfo});
}

/// @nodoc
class __$$_MedalDataCopyWithImpl<$Res>
    extends _$MedalDataCopyWithImpl<$Res, _$_MedalData>
    implements _$$_MedalDataCopyWith<$Res> {
  __$$_MedalDataCopyWithImpl(
      _$_MedalData _value, $Res Function(_$_MedalData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medalId = freezed,
    Object? hasMedal = freezed,
    Object? medalName = freezed,
    Object? medalRemark = freezed,
    Object? labelContent = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceAndroid = freezed,
    Object? resourceIos = freezed,
    Object? resourceFlutter = freezed,
    Object? getTime = freezed,
    Object? tipInfo = freezed,
  }) {
    return _then(_$_MedalData(
      medalId: freezed == medalId
          ? _value.medalId
          : medalId // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMedal: freezed == hasMedal
          ? _value.hasMedal
          : hasMedal // ignore: cast_nullable_to_non_nullable
              as bool?,
      medalName: freezed == medalName
          ? _value.medalName
          : medalName // ignore: cast_nullable_to_non_nullable
              as String?,
      medalRemark: freezed == medalRemark
          ? _value.medalRemark
          : medalRemark // ignore: cast_nullable_to_non_nullable
              as String?,
      labelContent: freezed == labelContent
          ? _value.labelContent
          : labelContent // ignore: cast_nullable_to_non_nullable
              as String?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceAndroid: freezed == resourceAndroid
          ? _value.resourceAndroid
          : resourceAndroid // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceIos: freezed == resourceIos
          ? _value.resourceIos
          : resourceIos // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      tipInfo: freezed == tipInfo
          ? _value.tipInfo
          : tipInfo // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalData implements _MedalData {
  _$_MedalData(
      {required this.medalId,
      required this.hasMedal,
      required this.medalName,
      required this.medalRemark,
      required this.labelContent,
      required this.lockImage,
      required this.unlockImage,
      required this.resourceAndroid,
      required this.resourceIos,
      required this.resourceFlutter,
      required this.getTime,
      required this.tipInfo});

  factory _$_MedalData.fromJson(Map<String, dynamic> json) =>
      _$$_MedalDataFromJson(json);

  /// 奖章ID
  @override
  final int? medalId;

  /// 是否已完成
  @override
  final bool? hasMedal;

  /// 奖章名称
  @override
  final String? medalName;

  /// 奖章描述
  @override
  final String? medalRemark;

  /// 课程阶段名称
  @override
  final String? labelContent;

  /// 未解锁图片的URL
  @override
  final String? lockImage;

  /// 解锁图片的URL
  @override
  final String? unlockImage;

  /// 安卓资源包的URL
  @override
  final String? resourceAndroid;

  /// iOS资源包的URL
  @override
  final String? resourceIos;

  /// Flutter资源包的URL
  @override
  final String? resourceFlutter;

  /// 获得时间，时间戳
  @override
  final int? getTime;

  /// 未获得奖章提示
  @override
  final String? tipInfo;

  @override
  String toString() {
    return 'MedalData(medalId: $medalId, hasMedal: $hasMedal, medalName: $medalName, medalRemark: $medalRemark, labelContent: $labelContent, lockImage: $lockImage, unlockImage: $unlockImage, resourceAndroid: $resourceAndroid, resourceIos: $resourceIos, resourceFlutter: $resourceFlutter, getTime: $getTime, tipInfo: $tipInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalData &&
            (identical(other.medalId, medalId) || other.medalId == medalId) &&
            (identical(other.hasMedal, hasMedal) ||
                other.hasMedal == hasMedal) &&
            (identical(other.medalName, medalName) ||
                other.medalName == medalName) &&
            (identical(other.medalRemark, medalRemark) ||
                other.medalRemark == medalRemark) &&
            (identical(other.labelContent, labelContent) ||
                other.labelContent == labelContent) &&
            (identical(other.lockImage, lockImage) ||
                other.lockImage == lockImage) &&
            (identical(other.unlockImage, unlockImage) ||
                other.unlockImage == unlockImage) &&
            (identical(other.resourceAndroid, resourceAndroid) ||
                other.resourceAndroid == resourceAndroid) &&
            (identical(other.resourceIos, resourceIos) ||
                other.resourceIos == resourceIos) &&
            (identical(other.resourceFlutter, resourceFlutter) ||
                other.resourceFlutter == resourceFlutter) &&
            (identical(other.getTime, getTime) || other.getTime == getTime) &&
            (identical(other.tipInfo, tipInfo) || other.tipInfo == tipInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      medalId,
      hasMedal,
      medalName,
      medalRemark,
      labelContent,
      lockImage,
      unlockImage,
      resourceAndroid,
      resourceIos,
      resourceFlutter,
      getTime,
      tipInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalDataCopyWith<_$_MedalData> get copyWith =>
      __$$_MedalDataCopyWithImpl<_$_MedalData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalDataToJson(
      this,
    );
  }
}

abstract class _MedalData implements MedalData {
  factory _MedalData(
      {required final int? medalId,
      required final bool? hasMedal,
      required final String? medalName,
      required final String? medalRemark,
      required final String? labelContent,
      required final String? lockImage,
      required final String? unlockImage,
      required final String? resourceAndroid,
      required final String? resourceIos,
      required final String? resourceFlutter,
      required final int? getTime,
      required final String? tipInfo}) = _$_MedalData;

  factory _MedalData.fromJson(Map<String, dynamic> json) =
      _$_MedalData.fromJson;

  @override

  /// 奖章ID
  int? get medalId;
  @override

  /// 是否已完成
  bool? get hasMedal;
  @override

  /// 奖章名称
  String? get medalName;
  @override

  /// 奖章描述
  String? get medalRemark;
  @override

  /// 课程阶段名称
  String? get labelContent;
  @override

  /// 未解锁图片的URL
  String? get lockImage;
  @override

  /// 解锁图片的URL
  String? get unlockImage;
  @override

  /// 安卓资源包的URL
  String? get resourceAndroid;
  @override

  /// iOS资源包的URL
  String? get resourceIos;
  @override

  /// Flutter资源包的URL
  String? get resourceFlutter;
  @override

  /// 获得时间，时间戳
  int? get getTime;
  @override

  /// 未获得奖章提示
  String? get tipInfo;
  @override
  @JsonKey(ignore: true)
  _$$_MedalDataCopyWith<_$_MedalData> get copyWith =>
      throw _privateConstructorUsedError;
}

ShareInfo _$ShareInfoFromJson(Map<String, dynamic> json) {
  return _ShareInfo.fromJson(json);
}

/// @nodoc
mixin _$ShareInfo {
  /// 分享文本
  String? get shareText => throw _privateConstructorUsedError;

  /// 扫码跳转URL
  String? get scanCodeJumpUrl => throw _privateConstructorUsedError;

  /// 扫码跳转原始URL
  String? get scanCodeJumpOriginalUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShareInfoCopyWith<ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShareInfoCopyWith<$Res> {
  factory $ShareInfoCopyWith(ShareInfo value, $Res Function(ShareInfo) then) =
      _$ShareInfoCopyWithImpl<$Res, ShareInfo>;
  @useResult
  $Res call(
      {String? shareText,
      String? scanCodeJumpUrl,
      String? scanCodeJumpOriginalUrl});
}

/// @nodoc
class _$ShareInfoCopyWithImpl<$Res, $Val extends ShareInfo>
    implements $ShareInfoCopyWith<$Res> {
  _$ShareInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shareText = freezed,
    Object? scanCodeJumpUrl = freezed,
    Object? scanCodeJumpOriginalUrl = freezed,
  }) {
    return _then(_value.copyWith(
      shareText: freezed == shareText
          ? _value.shareText
          : shareText // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeJumpUrl: freezed == scanCodeJumpUrl
          ? _value.scanCodeJumpUrl
          : scanCodeJumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeJumpOriginalUrl: freezed == scanCodeJumpOriginalUrl
          ? _value.scanCodeJumpOriginalUrl
          : scanCodeJumpOriginalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShareInfoCopyWith<$Res> implements $ShareInfoCopyWith<$Res> {
  factory _$$_ShareInfoCopyWith(
          _$_ShareInfo value, $Res Function(_$_ShareInfo) then) =
      __$$_ShareInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? shareText,
      String? scanCodeJumpUrl,
      String? scanCodeJumpOriginalUrl});
}

/// @nodoc
class __$$_ShareInfoCopyWithImpl<$Res>
    extends _$ShareInfoCopyWithImpl<$Res, _$_ShareInfo>
    implements _$$_ShareInfoCopyWith<$Res> {
  __$$_ShareInfoCopyWithImpl(
      _$_ShareInfo _value, $Res Function(_$_ShareInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shareText = freezed,
    Object? scanCodeJumpUrl = freezed,
    Object? scanCodeJumpOriginalUrl = freezed,
  }) {
    return _then(_$_ShareInfo(
      shareText: freezed == shareText
          ? _value.shareText
          : shareText // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeJumpUrl: freezed == scanCodeJumpUrl
          ? _value.scanCodeJumpUrl
          : scanCodeJumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      scanCodeJumpOriginalUrl: freezed == scanCodeJumpOriginalUrl
          ? _value.scanCodeJumpOriginalUrl
          : scanCodeJumpOriginalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShareInfo implements _ShareInfo {
  _$_ShareInfo(
      {required this.shareText,
      required this.scanCodeJumpUrl,
      required this.scanCodeJumpOriginalUrl});

  factory _$_ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ShareInfoFromJson(json);

  /// 分享文本
  @override
  final String? shareText;

  /// 扫码跳转URL
  @override
  final String? scanCodeJumpUrl;

  /// 扫码跳转原始URL
  @override
  final String? scanCodeJumpOriginalUrl;

  @override
  String toString() {
    return 'ShareInfo(shareText: $shareText, scanCodeJumpUrl: $scanCodeJumpUrl, scanCodeJumpOriginalUrl: $scanCodeJumpOriginalUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShareInfo &&
            (identical(other.shareText, shareText) ||
                other.shareText == shareText) &&
            (identical(other.scanCodeJumpUrl, scanCodeJumpUrl) ||
                other.scanCodeJumpUrl == scanCodeJumpUrl) &&
            (identical(
                    other.scanCodeJumpOriginalUrl, scanCodeJumpOriginalUrl) ||
                other.scanCodeJumpOriginalUrl == scanCodeJumpOriginalUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, shareText, scanCodeJumpUrl, scanCodeJumpOriginalUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      __$$_ShareInfoCopyWithImpl<_$_ShareInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShareInfoToJson(
      this,
    );
  }
}

abstract class _ShareInfo implements ShareInfo {
  factory _ShareInfo(
      {required final String? shareText,
      required final String? scanCodeJumpUrl,
      required final String? scanCodeJumpOriginalUrl}) = _$_ShareInfo;

  factory _ShareInfo.fromJson(Map<String, dynamic> json) =
      _$_ShareInfo.fromJson;

  @override

  /// 分享文本
  String? get shareText;
  @override

  /// 扫码跳转URL
  String? get scanCodeJumpUrl;
  @override

  /// 扫码跳转原始URL
  String? get scanCodeJumpOriginalUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
