import 'package:freezed_annotation/freezed_annotation.dart';

part 'medal_data.freezed.dart';
part 'medal_data.g.dart';

/// 课程信息类
@freezed
class MyMedalsData with _$MyMedalsData {
  factory MyMedalsData({
    /// 课程信息
    required CourseInfo? courseInfo,

    /// 分享信息
    required ShareInfo? shareInfo,

    /// 奖章列表
    required List<MedalData>? medalList,
  }) = _MyMedalsData;

  factory MyMedalsData.fromJson(Map<String, dynamic> json) =>
      _$MyMedalsDataFromJson(json);
}

/// 课程信息类
@freezed
class CourseInfo with _$CourseInfo {
  factory CourseInfo({
    /// 科目类型
    required int? subjectType,

    /// 课程键
    required String? courseKey,

    /// 课程ID
    required int? courseId,

    /// 课程段代码
    required int? courseSegmentCode,

    /// 课程段名称
    required String? courseSegmentName,

    /// 课程名称
    required String? courseName,
  }) = _CourseInfo;

  factory CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$CourseInfoFromJson(json);
}

/// 奖章数据类
@freezed
class MedalData with _$MedalData {
  factory MedalData({
    /// 奖章ID
    required int? medalId,

    /// 是否已完成
    required bool? hasMedal,

    /// 奖章名称
    required String? medalName,

    /// 奖章描述
    required String? medalRemark,

    /// 课程阶段名称
    required String? labelContent,

    /// 未解锁图片的URL
    required String? lockImage,

    /// 解锁图片的URL
    required String? unlockImage,

    /// 安卓资源包的URL
    required String? resourceAndroid,

    /// iOS资源包的URL
    required String? resourceIos,

    /// Flutter资源包的URL
    required String? resourceFlutter,

    /// 获得时间，时间戳
    required int? getTime,

    /// 未获得奖章提示
    required String? tipInfo,
  }) = _MedalData;

  factory MedalData.fromJson(Map<String, dynamic> json) =>
      _$MedalDataFromJson(json);
}

/// 奖章分享信息类
@freezed
class ShareInfo with _$ShareInfo {
  factory ShareInfo({
    /// 分享文本
    required String? shareText,

    /// 扫码跳转URL
    required String? scanCodeJumpUrl,

    /// 扫码跳转原始URL
    required String? scanCodeJumpOriginalUrl,
  }) = _ShareInfo;

  factory ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$ShareInfoFromJson(json);
}
