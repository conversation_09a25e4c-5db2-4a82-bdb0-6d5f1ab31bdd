// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medal_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MyMedalsData _$$_MyMedalsDataFromJson(Map<String, dynamic> json) =>
    _$_MyMedalsData(
      courseInfo: json['courseInfo'] == null
          ? null
          : CourseInfo.fromJson(json['courseInfo'] as Map<String, dynamic>),
      shareInfo: json['shareInfo'] == null
          ? null
          : ShareInfo.fromJson(json['shareInfo'] as Map<String, dynamic>),
      medalList: (json['medalList'] as List<dynamic>?)
          ?.map((e) => MedalData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MyMedalsDataToJson(_$_MyMedalsData instance) =>
    <String, dynamic>{
      'courseInfo': instance.courseInfo,
      'shareInfo': instance.shareInfo,
      'medalList': instance.medalList,
    };

_$_CourseInfo _$$_CourseInfoFromJson(Map<String, dynamic> json) =>
    _$_CourseInfo(
      subjectType: json['subjectType'] as int?,
      courseKey: json['courseKey'] as String?,
      courseId: json['courseId'] as int?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      courseName: json['courseName'] as String?,
    );

Map<String, dynamic> _$$_CourseInfoToJson(_$_CourseInfo instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'courseKey': instance.courseKey,
      'courseId': instance.courseId,
      'courseSegmentCode': instance.courseSegmentCode,
      'courseSegmentName': instance.courseSegmentName,
      'courseName': instance.courseName,
    };

_$_MedalData _$$_MedalDataFromJson(Map<String, dynamic> json) => _$_MedalData(
      medalId: json['medalId'] as int?,
      hasMedal: json['hasMedal'] as bool?,
      medalName: json['medalName'] as String?,
      medalRemark: json['medalRemark'] as String?,
      labelContent: json['labelContent'] as String?,
      lockImage: json['lockImage'] as String?,
      unlockImage: json['unlockImage'] as String?,
      resourceAndroid: json['resourceAndroid'] as String?,
      resourceIos: json['resourceIos'] as String?,
      resourceFlutter: json['resourceFlutter'] as String?,
      getTime: json['getTime'] as int?,
      tipInfo: json['tipInfo'] as String?,
    );

Map<String, dynamic> _$$_MedalDataToJson(_$_MedalData instance) =>
    <String, dynamic>{
      'medalId': instance.medalId,
      'hasMedal': instance.hasMedal,
      'medalName': instance.medalName,
      'medalRemark': instance.medalRemark,
      'labelContent': instance.labelContent,
      'lockImage': instance.lockImage,
      'unlockImage': instance.unlockImage,
      'resourceAndroid': instance.resourceAndroid,
      'resourceIos': instance.resourceIos,
      'resourceFlutter': instance.resourceFlutter,
      'getTime': instance.getTime,
      'tipInfo': instance.tipInfo,
    };

_$_ShareInfo _$$_ShareInfoFromJson(Map<String, dynamic> json) => _$_ShareInfo(
      shareText: json['shareText'] as String?,
      scanCodeJumpUrl: json['scanCodeJumpUrl'] as String?,
      scanCodeJumpOriginalUrl: json['scanCodeJumpOriginalUrl'] as String?,
    );

Map<String, dynamic> _$$_ShareInfoToJson(_$_ShareInfo instance) =>
    <String, dynamic>{
      'shareText': instance.shareText,
      'scanCodeJumpUrl': instance.scanCodeJumpUrl,
      'scanCodeJumpOriginalUrl': instance.scanCodeJumpOriginalUrl,
    };
