import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievement_page/state.dart';

import '../../../../common/config/config.dart';
import '../../../../common/host_env/host_env.dart';
import '../../../../static/img.dart';
import '../../../../widgets/share/share_action.dart';
import '../plan_home/model/subject_type.dart';
import 'controller.dart';
import 'model/medal_data.dart';

enum PageScrollBehavior {
  scroll, // 进行滚动
  jump, // 进行跳转
}

class AchievementPageView extends StatefulWidget {
  const AchievementPageView({super.key});

  @override
  State<AchievementPageView> createState() {
    return AchievementPageViewState();
  }
}

class AchievementPageViewState extends State<AchievementPageView> {
  ScrollController scrollController = ScrollController(initialScrollOffset: 0);
  final GlobalKey _shareImageKey = GlobalKey();
  late MyAchievementController controller;
  bool canReportViewEvent = true;
  PageController? _pageController;

  int get selectIndex => controller.state.selectedIndex;

  CourseInfo? get courseInfo => controller.state.medalsData?.courseInfo;

  MedalData? get currentMedal {
    if (controller.state.medalsData?.medalList?.isNotEmpty == true) {
      var medalList = controller.state.medalsData!.medalList!;
      return medalList[selectIndex];
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    controller = context.read<MyAchievementController>();
  }

  @override
  void dispose() {
    _pageController?.removeListener(_handlePageChange);
    _pageController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (currentMedal != null && _pageController == null) {
      _pageController = PageController(initialPage: selectIndex);
      _pageController?.addListener(_handlePageChange);
    }

    return BlocListener<MyAchievementController, MyAchievementPageState>(
      listener: (context, state) {
        // 首次赋值 courseInfo 时埋点
        if (canReportViewEvent && courseInfo != null) {
          canReportViewEvent = false;
          _reportTrack(appViewScreenEvent,
              screen_name: "我的成就详情页",
              element_type: getSubjectName(courseInfo?.subjectType ?? 0),
              course_stage: courseInfo?.courseSegmentName ?? "",
              course_key: courseInfo?.courseKey ?? "",
              medal_name: currentMedal?.medalName ?? "",
              custom_state: currentMedal?.hasMedal == true ? "已获得" : "未获得");
        }
      },
      child: Scaffold(
        primary: !JoJoRouter.isWindow,
        appBar: const JoJoAppBar(
          title: '我的成就',
        ),
        body: JoJoPageLoading(
          placeText: controller.state.placeholder,
          status: controller.state.pageStatus,
          child: Stack(
            children: [
              // 分享截图
              RepaintBoundary(
                key: _shareImageKey,
                child: _buildShareImage(),
              ),
              // 页面内容
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                padding: EdgeInsets.only(top: 20.rdp),
                child: Column(children: [
                  BlocBuilder<MyAchievementController, MyAchievementPageState>(
                    buildWhen: (previous, current) {
                      // 只在 medalsData 或 selectedIndex 变化时重新构建
                      return previous.medalsData != current.medalsData ||
                          previous.selectedIndex != current.selectedIndex;
                    },
                    builder: (context, state) {
                      return _buildMedalList();
                    },
                  ),
                  if (controller.state.medalsData?.medalList?.isNotEmpty ==
                      true)
                    _buildMedalPage(),
                ]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 徽章列表
  Widget _buildMedalList() {
    int length = controller.state.medalsData?.medalList?.length ?? 0;

    return SizedBox(
      height: 70.rdp,
      child: SingleChildScrollView(
        controller: scrollController,
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: List.generate(length, (index) {
            return Row(
              children: [
                if (index == 0) SizedBox(width: 20.rdp),
                _buildMedalListItem(index),
                if (index == length - 1) SizedBox(width: 20.rdp),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildMedalPage() {
    return Expanded(
        child: PageView.builder(
      controller: _pageController,
      itemCount: controller.state.medalsData?.medalList?.length ?? 0,
      itemBuilder: (context, index) {
        MedalData? medal = controller.state.medalsData?.medalList?[index];
        return Column(
          children: [
            SizedBox(height: 20.rdp),
            _buildBigMedal(index),
            SizedBox(height: 40.rdp),
            if (medal?.hasMedal == true) _buildShareButton(),
          ],
        );
      },
    ));
  }

  Widget _buildMedalListItem(int index) {
    MedalData? medal = controller.state.medalsData?.medalList?[index];
    MedalResource resource = controller.state.resources[index];
    String imagePath = medal?.hasMedal == true
        ? resource.unlockImageFile
        : resource.lockImageFile;
    String? imageUrl =
        medal?.hasMedal == true ? medal?.unlockImage : medal?.lockImage;
    double itemWidth = selectIndex == index ? 70.rdp : 60.rdp;

    return Container(
      width: itemWidth,
      height: itemWidth,
      decoration: BoxDecoration(
        color: selectIndex == index ? HexColor("#FCF3BA") : Colors.transparent,
        borderRadius: BorderRadius.circular(23.rdp),
        border: Border.all(
            color:
                selectIndex == index ? HexColor("#FCDA00") : Colors.transparent,
            width: selectIndex == index ? 2.rdp : 0),
      ),
      child: Center(
        child: GestureDetector(
          onTap: () {
            _reportTrack(
              appClickEvent,
              element_name: "点击我的成就详情页奖章",
              element_type: getSubjectName(courseInfo?.subjectType ?? 0),
              course_stage: courseInfo?.courseSegmentName ?? "",
              course_key: courseInfo?.courseKey ?? "",
              custom_state: medal?.hasMedal == true ? "已获得" : "未获得",
              medal_name: medal?.medalName ?? "",
            );

            _selectToIndex(index, PageScrollBehavior.jump);
          },
          child: imagePath.isNotEmpty
              ? Image.file(
                  File(imagePath),
                  width: itemWidth,
                  height: itemWidth,
                  fit: BoxFit.cover,
                )
              : ImageNetworkCached(
                  width: itemWidth,
                  height: itemWidth,
                  imageUrl: imageUrl ?? "",
                ),
        ),
      ),
    );
  }

  Widget _buildBigMedal(int index) {
    return Center(
      child: Container(
        width: 335.rdp,
        height: 365.rdp,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.white, width: 4.rdp),
          borderRadius: BorderRadius.circular(28.rdp),
          boxShadow: [
            BoxShadow(
              color: HexColor("#C3C3C3").withOpacity(0.25),
              offset: Offset.zero,
              blurRadius: 20,
            ),
          ],
        ),
        child: _buildMedalCard(index, true, true, true),
      ),
    );
  }

  Widget _buildMedalCard(
      int index, bool topRadiu, bool bottomRadiu, bool animate) {
    MedalResource resource = controller.state.resources[index];
    MedalData? medal = controller.state.medalsData?.medalList?[index];
    bool showBgImage =
        resource.bgImageFile.isNotEmpty == true && medal?.hasMedal == true;
    double radius = 24.rdp;
    String dateString = "未获得";
    if (medal?.getTime != null) {
      int timestamp = medal?.getTime ?? 0;
      DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      dateString = DateFormat('yyyy.MM.dd').format(date);
    }
    JoJoSpineAnimationController spineController =
        JoJoSpineAnimationController();
    // 使用 ValueKey 来确保只有在 atlasFile 或 skelFile 变化时才重建 JoJoSpineAnimationWidget
    Key spineKey = ValueKey('${resource.atlasFile}-${resource.skelFile}');

    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: medal?.hasMedal == true ? HexColor("#FCF3BA") : HexColor("#F5F4F4"),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(topRadiu ? radius : 0),
                  topRight: Radius.circular(topRadiu ? radius : 0),
                  bottomLeft: Radius.circular(bottomRadiu ? radius : 0),
                  bottomRight: Radius.circular(bottomRadiu ? radius : 0)),
            ),
            clipBehavior: Clip.hardEdge,
            child: showBgImage
                ? Image(
                    width: 335.rdp,
                    height: 365.rdp,
                    fit: BoxFit.fill,
                    image: FileImage(File(resource.bgImageFile)),
                  )
                : Container(),
          ),
        ),
        Container(
          padding: EdgeInsets.only(left: 25.rdp, right: 25.rdp, top: 18.rdp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                medal?.labelContent ?? "",
                style: TextStyle(
                  fontSize: 14.rdp,
                  color: HexColor("#404040", 0.4),
                ),
              ),
              Text(
                dateString,
                style: TextStyle(
                  fontSize: 14.rdp,
                  color: HexColor("#404040", 0.4),
                ),
              ),
            ],
          ),
        ),
        if (medal?.hasMedal == false)
          Positioned.fill(
            top: 30.rdp,
            child: Align(
              alignment: Alignment.topCenter,
              child: ImageNetworkCached(
                width: 200.rdp,
                height: 200.rdp,
                imageUrl: medal?.lockImage ?? "",
              ),
            ),
          ),
        if (medal?.hasMedal == true &&
            (resource.skelFile.isEmpty || resource.atlasFile.isEmpty))
          Positioned(
            top: 40.rdp,
            width: 335.rdp,
            child: Center(
              child: ImageNetworkCached(
                  width: 200.rdp,
                  height: 200.rdp,
                  imageUrl: medal?.unlockImage ?? ""),
            ),
          ),
        if (medal?.hasMedal == false)
          Positioned(
            top: 237.rdp,
            width: 335.rdp,
            child: Center(
                child: Column(
              children: [
                Text(
                  medal?.medalName ?? "",
                  style: TextStyle(
                    fontSize: 28.rdp,
                    color: HexColor("#B2B2B2"),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 10.rdp),
                Text(
                  medal?.tipInfo ?? "",
                  style: TextStyle(
                    fontSize: 16.rdp,
                    color: HexColor("#666666"),
                  ),
                )
              ],
            )),
          ),
        if (medal?.hasMedal == true &&
            resource.atlasFile.isNotEmpty &&
            resource.skelFile.isNotEmpty)
          Positioned.fill(
            top: 30.rdp,
            child: Align(
              alignment: Alignment.topCenter,
              child: SizedBox(
                width: 200.rdp,
                height: 200.rdp,
                child: Center(
                  child: JoJoSpineAnimationWidget(
                    key: spineKey,
                    resource.atlasFile,
                    resource.skelFile,
                    LoadMode.file,
                    spineController,
                    onInitialized: (_controller) {
                      if (animate && mounted) {
                        spineController.playAnimation(JoJoSpineAnimation(
                          animaitonName: "normal",
                          trackIndex: 0,
                          loop: false,
                        ));
                      }
                    },
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildShareImage() {
    if (currentMedal == null) {
      return Container();
    }

    return Container(
      width: 335.rdp,
      height: 449.rdp,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.white, width: 4.rdp),
        borderRadius: BorderRadius.circular(28.rdp),
      ),
      clipBehavior: Clip.hardEdge,
      child: Column(
        children: [
          SizedBox(
            width: 335.rdp,
            height: 365.rdp,
            child: _buildMedalCard(selectIndex, true, false, false),
          ),
          Expanded(
            child: Stack(
              children: [
                Positioned(
                    left: 3.rdp,
                    top: 9.rdp,
                    child: ImageAssetWeb(
                        assetName: AssetsImg.JOJO_LOGO,
                        width: 129.rdp,
                        height: 40.rdp,
                        package: Config.package)),
                Positioned(
                  left: 16.rdp,
                  top: 45.rdp,
                  child: Text(
                    controller.state.medalsData?.shareInfo?.shareText ?? "",
                    style: TextStyle(
                      fontFamily: 'PingFang SC',
                      fontSize: 14.rdp,
                      color: HexColor("#666666"),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                Positioned(
                  right: 20.rdp,
                  top: 12.rdp,
                  child: FutureBuilder<Uint8List>(
                    future: _generateQRCode(controller
                            .state.medalsData?.shareInfo?.scanCodeJumpUrl ??
                        ""),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.done) {
                        if (snapshot.hasData) {
                          return Image.memory(
                            snapshot.data!,
                            width: 60.rdp,
                            height: 60.rdp,
                            fit: BoxFit.cover,
                          );
                        } else if (snapshot.hasError) {
                          return Container();
                        }
                      }
                      return const Center(child: CircularProgressIndicator());
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton() {
    return GestureDetector(
      onTap: () async {
        // 生成_buildShareImage这个Widget的截图
        RenderRepaintBoundary boundary = _shareImageKey.currentContext!
            .findRenderObject() as RenderRepaintBoundary;
        var image = await boundary.toImage(pixelRatio: 3.0);
        ByteData? byteData =
            await image.toByteData(format: ui.ImageByteFormat.png);
        Uint8List pngBytes = byteData!.buffer.asUint8List();

        _showShare(pngBytes);
      },
      child: Container(
        width: 280.rdp,
        height: 44.rdp,
        decoration: BoxDecoration(
          color: HexColor("#FCDA00"),
          borderRadius: BorderRadius.circular(22.rdp),
        ),
        child: Center(
          child: Text(
            '分享',
            style: TextStyle(
              fontFamily: 'PingFang SC',
              color: HexColor("#544300"),
              fontSize: 18.rdp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  void _showShare(Uint8List imageBytes) {
    _reportTrack(
      appClickEvent,
      element_name: "点击我的成就详情页_分享按钮",
      element_type: getSubjectName(courseInfo?.subjectType ?? 0),
      course_stage: courseInfo?.courseSegmentName ?? "",
      course_key: courseInfo?.courseKey ?? "",
      medal_name: currentMedal?.medalName ?? "",
    );

    SmartDialog.show(
      alignment: Alignment.bottomCenter,
      animationType: SmartAnimationType.fade,
      builder: (_) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Image.memory(
              imageBytes,
              width: 335.rdp,
              height: 449.rdp,
              fit: BoxFit.cover,
            ),
            SizedBox(height: 30.rdp),
            ShareAction(
              onClick: (type) async {
                String base64Img = base64Encode(imageBytes);
                // 分享埋点
                String? element_name = defaultConfig[type]?["text"];
                _reportTrack(appClickEvent,
                    element_name: "点击分享抽屉内的具体方式",
                    element_type: getSubjectName(courseInfo?.subjectType ?? 0),
                    course_stage: courseInfo?.courseSegmentName ?? "",
                    course_key: courseInfo?.courseKey ?? "",
                    medal_name: currentMedal?.medalName ?? "",
                    custom_state: element_name);

                if (type == ShareType.download) {
                  // 下载图片，原生有 toast 提示结果
                  await jojoNativeBridge.saveBase64Image(
                      base64: 'data:image/png;base64,$base64Img');
                } else {
                  final res = await jojoNativeBridge.shareToPlatform(
                    desc: '',
                    image: 'data:image/png;base64,$base64Img',
                    platformType: 0,
                    scene: type == ShareType.wx ? 0 : 1,
                    shareType: 1,
                    title: '',
                    url: '',
                  );

                  if (res.msg != '' && res.status != 200) {
                    JoJoToast.showWarning(res.msg);
                  }
                }

                SmartDialog.dismiss();
              },
              onClose: () {
                SmartDialog.dismiss();
              },
              config: [
                IConfig(type: ShareType.wx),
                IConfig(type: ShareType.wxCircle),
                IConfig(type: ShareType.download)
              ],
            )
          ],
        );
      },
    );
  }

  /// 选择到指定索引
  void _selectToIndex(int index, PageScrollBehavior behavior,
      {bool animated = true}) {
    _scrollToIndex(index, animated: animated);
    controller.changeSelectedIndex(index);

    if (behavior == PageScrollBehavior.jump) {
      _pageController?.jumpToPage(index);
    }
  }

  void _handlePageChange() {
    final double? page = _pageController?.page;
    if (page == null) return;

    int newPage = page.round();
    if ((page - newPage).abs() < 0.1 && newPage != selectIndex) {
      _reportTrack(appViewScreenEvent,
          screen_name: "我的成就详情页",
          element_type: getSubjectName(courseInfo?.subjectType ?? 0),
          course_stage: courseInfo?.courseSegmentName ?? "",
          course_key: courseInfo?.courseKey ?? "",
          medal_name: currentMedal?.medalName ?? "",
          custom_state: currentMedal?.hasMedal == true ? "已获得" : "未获得");

      _selectToIndex(newPage, PageScrollBehavior.scroll);
    }
  }

  /// 滚动到指定索引的位置
  void _scrollToIndex(int index, {bool animated = true}) {
    if (!scrollController.hasClients) return;

    double pageWidth = MediaQuery.of(context).size.width;
    double offset = scrollController.offset;
    double itemWidth = 60.rdp;
    // item 距离左侧的偏移量
    double targetOffset = itemWidth * index + 20.rdp;
    // item 中心点距离左侧的偏移量
    double targetCenter = targetOffset + itemWidth / 2;
    // 最小触发滚动的偏移量
    double leftTriggerOffset = offset + (pageWidth / 2) - itemWidth;
    // 最大触发滚动的偏移量
    double rightTriggerOffset = offset + (pageWidth / 2) + itemWidth;
    // 滚动的目标位置
    double targetPosition = targetCenter - (pageWidth / 2);
    targetPosition =
        min(max(targetPosition, 0), scrollController.position.maxScrollExtent);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (targetCenter < leftTriggerOffset ||
          targetCenter > rightTriggerOffset) {
        if (animated) {
          scrollController.animateTo(
            targetPosition,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
          );
        } else {
          scrollController.jumpTo(targetPosition);
        }
      }
    });
  }

  Future<Uint8List> _generateQRCode(String content) async {
    final qrCode = QrPainter(
      data: content,
      version: QrVersions.auto,
      errorCorrectionLevel: QrErrorCorrectLevel.L,
    );

    final image = await qrCode.toImage(200);
    final bytes = await image.toByteData(format: ui.ImageByteFormat.png);
    return bytes!.buffer.asUint8List();
  }

  final String appViewScreenEvent = '\$AppViewScreen';
  final String appClickEvent = '\$AppClick';

  _reportTrack(String event,
      {String? screen_name,
      String? element_type,
      String? course_stage,
      String? course_key,
      String? referrer,
      String? element_name,
      String? medal_name,
      String? custom_state}) {
    Map<String, dynamic> sensorData = {};
    if (screen_name != null) sensorData['\$screen_name'] = screen_name;
    if (element_type != null) sensorData['\$element_type'] = element_type;
    if (course_stage != null) sensorData['course_stage'] = course_stage;
    if (course_key != null) sensorData['course_key'] = course_key;
    if (referrer != null) sensorData['\$referrer'] = referrer;
    if (element_name != null) sensorData['\$element_name'] = element_name;
    if (medal_name != null) sensorData['medal_name'] = medal_name;
    if (custom_state != null) sensorData['custom_state'] = custom_state;

    RunEnv.sensorsTrack(event, sensorData);
  }
}
