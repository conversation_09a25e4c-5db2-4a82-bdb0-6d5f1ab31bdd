import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import 'model/medal_data.dart';

class MedalResource {
  String skelFile;
  String atlasFile;
  String bgImageFile;
  String lockImageFile;
  String unlockImageFile;

  MedalResource({
    required this.skelFile,
    required this.atlasFile,
    required this.bgImageFile,
    required this.lockImageFile,
    required this.unlockImageFile,
  });
}

class MyAchievementPageState {
  final PageStatus pageStatus;
  final int selectedIndex;
  final MyMedalsData? medalsData;
  final List<MedalResource> resources;

  MedalResource? get currentResource =>
      resources.isNotEmpty ? resources[selectedIndex] : null;

  /// 异常时的文案
  final String? placeholder;

  MyAchievementPageState(this.pageStatus,
      {this.selectedIndex = 0,
      this.medalsData,
      this.resources = const [],
      this.placeholder});

  MyAchievementPageState copyWith({
    PageStatus? pageStatus,
    int? selectedIndex,
    MyMedalsData? medalsData,
    List<MedalResource>? resources,
    String? placeholder,
  }) {
    return MyAchievementPageState(
      pageStatus ?? PageStatus.error,
      selectedIndex: selectedIndex ?? 0,
      medalsData: medalsData,
      resources: resources ?? [],
      placeholder: placeholder,
    );
  }
}
