import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';

import 'view.dart';
import 'state.dart';
import 'controller.dart';

class AchievementPage extends BasePage {
  final String? classId;
  final String? medalId;

  const AchievementPage(this.classId, this.medalId, {super.key});

  @override
  State<StatefulWidget> createState () {
    return AchievementPageState();
  }
}

class AchievementPageState extends BaseState<AchievementPage> {
  UniqueKey pageKey = UniqueKey();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => MyAchievementController.withDefault(
          widget.classId,
          widget.medalId
      ),
      child: Bloc<PERSON><PERSON>er<MyAchievementController, MyAchievementPageState>(
        builder: (context, state) {
          return AchievementPageView(key: pageKey);
        },
      ),
    );
  }

}