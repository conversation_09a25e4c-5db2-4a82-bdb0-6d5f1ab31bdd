import 'dart:async';
import 'dart:io';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievement_page/state.dart';

import '../../service/medal_api.dart';
import '../../utils/file_util.dart';
import 'model/medal_data.dart';

class MyAchievementController extends Cubit<MyAchievementPageState> {
  final String? classId;
  final String? medalId;
  final MedalAPI medalApi;

  final JoJoResourceManager _resourceManager = JoJoResourceManager(forceFlutterDownload: true);
  Timer? _downloadTimer;
  String TAG = "MyAchievementController";

  MyAchievementController(this.classId, this.medalId, {required this.medalApi})
      : super(MyAchievementPageState(PageStatus.loading)) {
    _refreshData();
  }

  /// 默认构造函数
  MyAchievementController.withDefault(this.classId, this.medalId)
      : medalApi = globalMedalApi,
        //: medalApi = MedalAPIMock(),
        super(MyAchievementPageState(PageStatus.loading)) {
    _refreshData();
  }

  @override
  Future<void> close() async {
    _downloadTimer?.cancel();
    await super.close();
  }

  /// 刷新数据
  Future<void> _refreshData() async {
    var medalsData = await medalApi.getMedalsList(classId ?? "");

    if (medalsData.courseInfo == null) {
      _updateState(PageStatus.error, placeholder: "数据异常，稍候再试");
      return;
    } else if (medalsData.medalList?.isEmpty == true) {
      _updateState(PageStatus.empty, placeholder: "暂无勋章数据");
      return;
    } else {
      int? index = _defaultMedelIndex(medalId, medalsData);
      if (index == -1) {
        index = 0;
        JoJoToast.show("路由参数匹配不到对应勋章", JoJoToastType.error);
        l.e("MyAchievementController",
            "无法在勋章列表中找到对应勋章, classId: $classId, medalId: $medalId");
      }

      downloadMedals(medalsData.medalList, (resources, error) {
        _updateState(
          PageStatus.success,
          medalsData: medalsData,
          selectedIndex: index,
          resources: resources,
        );

        if (error != null) {
          JoJoToast.show(error, JoJoToastType.error);
        }
      });
    }
  }

  void changeSelectedIndex(int index) {
    _updateState(
      state.pageStatus,
      medalsData: state.medalsData,
      selectedIndex: index,
      resources: state.resources,
    );
  }

  void downloadMedals(List<MedalData>? medals,
      Function(List<MedalResource>? resources, String? error) callback) {
    List<String> medalSpineURLs = medals?.map((e) {
          return e.resourceFlutter ?? "";
        }).toList() ??
        [];
    List<String> medalLockImageURLs = medals?.map((e) {
          return e.lockImage ?? "";
        }).toList() ??
        [];
    List<String> medalUnlockImageURLs = medals?.map((e) {
          return e.unlockImage ?? "";
        }).toList() ??
        [];
    if (medalSpineURLs.length != medalLockImageURLs.length ||
        medalSpineURLs.length != medalUnlockImageURLs.length) {
      l.e(TAG, "勋章资源数量匹配异常");
    }

    bool isTimeout = true;
    double currentProgress = 0;

    _downloadTimer?.cancel();

    List<String> urls = [
      ...medalSpineURLs,
      ...medalLockImageURLs,
      ...medalUnlockImageURLs,
    ];
    _resourceManager.downloadUrl(urls, progressListener: (double progress) {
      currentProgress = progress;
    }, successListener: (Map<String, String> results) async {
      isTimeout = false;
      List<MedalResource> resources = [];

      for (int i = 0; i < medalSpineURLs.length; i++) {
        String zipUrl = medalSpineURLs[i];
        String lockImageUrl = medalLockImageURLs[i];
        String unlockImageUrl = medalUnlockImageURLs[i];
        String? zipPath = results[zipUrl];
        String? lockImagePath = results[lockImageUrl] ?? "";
        String? unlockImagePath = results[unlockImageUrl] ?? "";
        String zipDir = zipPath?.substring(0, zipPath.lastIndexOf("/")) ?? "";

        if (zipDir.isNotEmpty) {
          File? atlasFile = findFilesByExtension(zipDir, "atlas.txt");
          File? skelFile = findFilesByExtension(zipDir, "skel.bytes");
          File? bgImageFile = findFilesByExtension(zipDir, "medal_bg.png");

          // 避免重复解压
          if (atlasFile != null && skelFile != null && bgImageFile != null) {
            MedalResource resource = MedalResource(
              atlasFile: atlasFile.path,
              skelFile: skelFile.path,
              bgImageFile: bgImageFile.path,
              lockImageFile: lockImagePath,
              unlockImageFile: unlockImagePath,
            );
            resources.add(resource);
            continue;
          }
        }

        if (zipPath != null && zipPath.isNotEmpty) {
          File? atlasFile;
          File? skelFile;
          File? bgImageFile;
          try {
            String value = await unzip(zipPath);

            atlasFile = findFilesByExtension(value, "atlas.txt");
            skelFile = findFilesByExtension(value, "skel.bytes");
            bgImageFile = findFilesByExtension(value, "medal_bg.png");
          } catch (e) {
            l.e(TAG, "解压文件时发生错误，url: $zipUrl, 错误: $e");
          }

          MedalResource resource = MedalResource(
            atlasFile: atlasFile?.path ?? "",
            skelFile: skelFile?.path ?? "",
            bgImageFile: bgImageFile?.path ?? "",
            lockImageFile: lockImagePath,
            unlockImageFile: unlockImagePath,
          );
          resources.add(resource);

          if (resource.atlasFile.isEmpty) {
            l.e(TAG, "解压后 atlas.txt 文件不存在，url: $zipUrl");
          }
          if (resource.skelFile.isEmpty) {
            l.e(TAG, "解压后 skel.bytes 文件不存在，url: $zipUrl");
          }
          if (resource.bgImageFile.isEmpty) {
            l.e(TAG, "解压后 medal_bg.png 文件不存在，url: $zipUrl");
          }
        } else {
          l.e(TAG, "无效的 zipPath, url: $zipUrl");
        }
      }

      callback(resources, null);
    }, failListener: (UnifiedExceptionData? error) {
      isTimeout = false;
      callback(null, error?.message);
    });

    _downloadTimer = Timer(const Duration(seconds: 15), () {
      if (isTimeout && currentProgress == 0) {
        _resourceManager.cancelDownload();
        callback(null, "徽章动画下载超时");
      }
    });
  }

  /// 获取勋章列表默认选中项
  int? _defaultMedelIndex(String? medalId, MyMedalsData medalsData) {
    if (medalId?.isNotEmpty == true) {
      // 获取指定勋章
      return medalsData.medalList?.indexWhere((element) {
        return element.medalId == int.tryParse(medalId ?? '');
      });
    } else {
      // 获取最新勋章
      return medalsData.medalList?.indexWhere((element) {
        return element.getTime ==
            (medalsData.medalList?.reduce((current, next) {
              return (current.getTime ?? 0) > (next.getTime ?? 0)
                  ? current
                  : next;
            }).getTime);
      });
    }
  }

  void _updateState(PageStatus pageStatus,
      {MyMedalsData? medalsData,
      int? selectedIndex,
      List<MedalResource>? resources,
      String? placeholder}) {
    emit(state.copyWith(
      pageStatus: pageStatus,
      medalsData: medalsData,
      selectedIndex: selectedIndex,
      resources: resources,
      placeholder: placeholder,
    ));
  }
}
