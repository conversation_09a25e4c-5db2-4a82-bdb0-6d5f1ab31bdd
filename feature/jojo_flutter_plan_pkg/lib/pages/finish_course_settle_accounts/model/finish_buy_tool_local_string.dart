import 'package:flutter/cupertino.dart';

import '../../../generated/l10n.dart';

class BuyToolStrings {
  final String propBuyTimeout;
  final String needToWait;
  final String confirm;
  final String thinkAgain;
  final String buyAndUseProp;
  final String useToolFailTip;
  final String buyFailed;
  final String buyFailedAndBack;
  final String pleaseWait;

  BuyToolStrings({
    required this.propBuyTimeout,
    required this.needToWait,
    required this.confirm,
    required this.thinkAgain,
    required this.buyAndUseProp,
    required this.useToolFailTip,
    required this.buyFailed,
    required this.buyFailedAndBack,
    required this.pleaseWait,
  });

  /// 从 S.of(context) 构建文案对象
  static BuyToolStrings fromContext(BuildContext context) {
    final s = S.of(context);
    return BuyToolStrings(
      propBuyTimeout: s.propBuyTimeout,
      needToWait: s.needToWait,
      confirm: s.confirm,
      thinkAgain: s.thinkAgain,
      buyAndUseProp: s.buyAndUseProp,
      useToolFailTip: s.useToolFailTip,
      buyFailed: s.buyFailed,
      buyFailedAndBack: s.buyFailedAndBack,
      pleaseWait: s.pleaseWait,
    );
  }
}