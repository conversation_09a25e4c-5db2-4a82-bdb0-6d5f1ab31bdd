// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'finish_course_settle_accounts_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_FinishCourseSettleAccountsData _$$_FinishCourseSettleAccountsDataFromJson(
        Map<String, dynamic> json) =>
    _$_FinishCourseSettleAccountsData(
      popups: (json['popups'] as List<dynamic>?)
          ?.map((e) => Popup.fromJson(e as Map<String, dynamic>))
          .toList(),
      disablePopups: json['disablePopups'] as bool?,
    );

Map<String, dynamic> _$$_FinishCourseSettleAccountsDataToJson(
        _$_FinishCourseSettleAccountsData instance) =>
    <String, dynamic>{
      'popups': instance.popups,
      'disablePopups': instance.disablePopups,
    };

_$_Popup _$$_PopupFromJson(Map<String, dynamic> json) => _$_Popup(
      type: json['type'] as String?,
      name: json['name'] as String?,
      order: json['order'] as int?,
      route: json['route'] as String?,
      res: json['res'] == null
          ? null
          : Res.fromJson(json['res'] as Map<String, dynamic>),
      extend: json['extend'] == null
          ? null
          : Extend.fromJson(json['extend'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_PopupToJson(_$_Popup instance) => <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'order': instance.order,
      'route': instance.route,
      'res': instance.res,
      'extend': instance.extend,
    };

_$_Extend _$$_ExtendFromJson(Map<String, dynamic> json) => _$_Extend(
      attendanceInfo: json['attendanceInfo'] == null
          ? null
          : AttendanceInfo.fromJson(
              json['attendanceInfo'] as Map<String, dynamic>),
      burialResources: json['burialResources'] == null
          ? null
          : BurialResources.fromJson(
              json['burialResources'] as Map<String, dynamic>),
      lessonTaskList: (json['lessonTaskList'] as List<dynamic>?)
          ?.map((e) => LessonTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
      assets: (json['assets'] as List<dynamic>?)
          ?.map((e) => Asset.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ExtendToJson(_$_Extend instance) => <String, dynamic>{
      'attendanceInfo': instance.attendanceInfo,
      'burialResources': instance.burialResources,
      'lessonTaskList': instance.lessonTaskList,
      'assets': instance.assets,
    };

_$_Asset _$$_AssetFromJson(Map<String, dynamic> json) => _$_Asset(
      propId: json['propId'] as String?,
      type: json['type'] as int?,
      count: json['count'] as int?,
    );

Map<String, dynamic> _$$_AssetToJson(_$_Asset instance) => <String, dynamic>{
      'propId': instance.propId,
      'type': instance.type,
      'count': instance.count,
    };

_$_BuyAsset _$$_BuyAssetFromJson(Map<String, dynamic> json) => _$_BuyAsset(
      propId: json['propId'] as String?,
      type: json['type'] as int?,
      count: json['count'] as int?,
      price: json['price'] as int?,
    );

Map<String, dynamic> _$$_BuyAssetToJson(_$_BuyAsset instance) =>
    <String, dynamic>{
      'propId': instance.propId,
      'type': instance.type,
      'count': instance.count,
      'price': instance.price,
    };

_$_AttendanceInfo _$$_AttendanceInfoFromJson(Map<String, dynamic> json) =>
    _$_AttendanceInfo(
      state: json['state'] as int?,
      lastState: json['lastState'] as int?,
      dayCount: json['dayCount'] as int?,
      historyCount: json['historyCount'] as int?,
      bestDayCount: json['bestDayCount'] as int?,
      featureCount: json['featureCount'] as int?,
      featureBestCount: json['featureBestCount'] as int?,
      frostDayCount: json['frostDayCount'] as int?,
      canIncCount: json['canIncCount'] as int?,
      stateTips: json['stateTips'] as String?,
      ipType: json['ipType'] as int?,
      useAssetType: json['useAssetType'] as int?,
      prop: (json['prop'] as List<dynamic>?)
          ?.map((e) => Asset.fromJson(e as Map<String, dynamic>))
          .toList(),
      buyProp: (json['buyProp'] as List<dynamic>?)
          ?.map((e) => BuyAsset.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseKey: json['courseKey'] as String?,
      lessonId: json['lessonId'] as int?,
      dieEnterAudioUrl: json['dieEnterAudioUrl'] as String?,
      audioUrl: json['audioUrl'] as String?,
      guidePage: json['guidePage'] == null
          ? null
          : GuidePage.fromJson(json['guidePage'] as Map<String, dynamic>),
      audioList: (json['audioList'] as List<dynamic>?)
          ?.map((e) => SceneValue.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageTipsList: (json['pageTipsList'] as List<dynamic>?)
          ?.map((e) => SceneValue.fromJson(e as Map<String, dynamic>))
          .toList(),
      shareInfo: json['shareInfo'] == null
          ? null
          : ShareInfo.fromJson(json['shareInfo'] as Map<String, dynamic>),
      purchaseSwitch: json['purchaseSwitch'] as int?,
    );

Map<String, dynamic> _$$_AttendanceInfoToJson(_$_AttendanceInfo instance) =>
    <String, dynamic>{
      'state': instance.state,
      'lastState': instance.lastState,
      'dayCount': instance.dayCount,
      'historyCount': instance.historyCount,
      'bestDayCount': instance.bestDayCount,
      'featureCount': instance.featureCount,
      'featureBestCount': instance.featureBestCount,
      'frostDayCount': instance.frostDayCount,
      'canIncCount': instance.canIncCount,
      'stateTips': instance.stateTips,
      'ipType': instance.ipType,
      'useAssetType': instance.useAssetType,
      'prop': instance.prop,
      'buyProp': instance.buyProp,
      'courseKey': instance.courseKey,
      'lessonId': instance.lessonId,
      'dieEnterAudioUrl': instance.dieEnterAudioUrl,
      'audioUrl': instance.audioUrl,
      'guidePage': instance.guidePage,
      'audioList': instance.audioList,
      'pageTipsList': instance.pageTipsList,
      'shareInfo': instance.shareInfo,
      'purchaseSwitch': instance.purchaseSwitch,
    };

_$_ShareInfo _$$_ShareInfoFromJson(Map<String, dynamic> json) => _$_ShareInfo(
      introduceUrl: json['introduceUrl'] as String?,
      backgroundImg: json['backgroundImg'] as String?,
      dressImg: json['dressImg'] as String?,
      avatarFrameImg: json['avatarFrameImg'] as String?,
      subjectName: json['subjectName'] as String?,
      nickname: json['nickname'] as String?,
      shareDesc: json['shareDesc'] as String?,
      backgroundText: json['backgroundText'] as String?,
    );

Map<String, dynamic> _$$_ShareInfoToJson(_$_ShareInfo instance) =>
    <String, dynamic>{
      'introduceUrl': instance.introduceUrl,
      'backgroundImg': instance.backgroundImg,
      'dressImg': instance.dressImg,
      'avatarFrameImg': instance.avatarFrameImg,
      'subjectName': instance.subjectName,
      'nickname': instance.nickname,
      'shareDesc': instance.shareDesc,
      'backgroundText': instance.backgroundText,
    };

_$_SceneValue _$$_SceneValueFromJson(Map<String, dynamic> json) =>
    _$_SceneValue(
      scene: json['scene'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$$_SceneValueToJson(_$_SceneValue instance) =>
    <String, dynamic>{
      'scene': instance.scene,
      'value': instance.value,
    };

_$_GuidePage _$$_GuidePageFromJson(Map<String, dynamic> json) => _$_GuidePage(
      pageType: json['pageType'] as int?,
      calendar: (json['calendar'] as List<dynamic>?)
          ?.map((e) => Calendar.fromJson(e as Map<String, dynamic>))
          .toList(),
      btns: (json['btns'] as List<dynamic>?)
          ?.map((e) => Btn.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageTips: json['pageTips'] as String?,
      audioUrl: json['audioUrl'] as String?,
      dieToFrozenAudioUrl: json['dieToFrozenAudioUrl'] as String?,
      milestoneInfo: json['milestoneInfo'] == null
          ? null
          : MilestoneInfo.fromJson(
              json['milestoneInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_GuidePageToJson(_$_GuidePage instance) =>
    <String, dynamic>{
      'pageType': instance.pageType,
      'calendar': instance.calendar,
      'btns': instance.btns,
      'pageTips': instance.pageTips,
      'audioUrl': instance.audioUrl,
      'dieToFrozenAudioUrl': instance.dieToFrozenAudioUrl,
      'milestoneInfo': instance.milestoneInfo,
    };

_$_MilestoneInfo _$$_MilestoneInfoFromJson(Map<String, dynamic> json) =>
    _$_MilestoneInfo(
      rewardList: (json['rewardList'] as List<dynamic>?)
          ?.map((e) => MilestoneReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      targetRewardList: (json['targetRewardList'] as List<dynamic>?)
          ?.map((e) => MilestoneReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MilestoneInfoToJson(_$_MilestoneInfo instance) =>
    <String, dynamic>{
      'rewardList': instance.rewardList,
      'targetRewardList': instance.targetRewardList,
    };

_$_MilestoneReward _$$_MilestoneRewardFromJson(Map<String, dynamic> json) =>
    _$_MilestoneReward(
      name: json['name'] as String?,
      type: json['type'] as String?,
      desc: json['desc'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$_MilestoneRewardToJson(_$_MilestoneReward instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'desc': instance.desc,
      'img': instance.img,
    };

_$_Btn _$$_BtnFromJson(Map<String, dynamic> json) => _$_Btn(
      btnName: json['btnName'] as String?,
      btnRouter: json['btnRouter'] as String?,
      btnParams: json['btnParams'] == null
          ? null
          : BtnParams.fromJson(json['btnParams'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_BtnToJson(_$_Btn instance) => <String, dynamic>{
      'btnName': instance.btnName,
      'btnRouter': instance.btnRouter,
      'btnParams': instance.btnParams,
    };

_$_BtnParams _$$_BtnParamsFromJson(Map<String, dynamic> json) => _$_BtnParams(
      classKey: json['classKey'] as String?,
      courseKey: json['courseKey'] as String?,
      lessonId: json['lessonId'] as int?,
    );

Map<String, dynamic> _$$_BtnParamsToJson(_$_BtnParams instance) =>
    <String, dynamic>{
      'classKey': instance.classKey,
      'courseKey': instance.courseKey,
      'lessonId': instance.lessonId,
    };

_$_Calendar _$$_CalendarFromJson(Map<String, dynamic> json) => _$_Calendar(
      date: json['date'] as int?,
      state: json['state'] as int?,
      router: json['router'] as String?,
    );

Map<String, dynamic> _$$_CalendarToJson(_$_Calendar instance) =>
    <String, dynamic>{
      'date': instance.date,
      'state': instance.state,
      'router': instance.router,
    };

_$_BurialResources _$$_BurialResourcesFromJson(Map<String, dynamic> json) =>
    _$_BurialResources(
      courseName: json['courseName'] as String?,
      classId: json['classId'] as String?,
      courseStage: json['courseStage'] as String?,
      courseType: json['courseType'] as String?,
      classKey: json['classKey'] as String?,
      courseKey: json['courseKey'] as String?,
    );

Map<String, dynamic> _$$_BurialResourcesToJson(_$_BurialResources instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'classId': instance.classId,
      'courseStage': instance.courseStage,
      'courseType': instance.courseType,
      'classKey': instance.classKey,
      'courseKey': instance.courseKey,
    };

_$_LessonTaskList _$$_LessonTaskListFromJson(Map<String, dynamic> json) =>
    _$_LessonTaskList(
      id: json['id'] as String?,
      type: json['type'] as int?,
      name: json['name'] as String?,
      desc: json['desc'] as String?,
      status: json['status'] as int?,
      rewardList: (json['rewardList'] as List<dynamic>?)
          ?.map((e) => RewardList.fromJson(e as Map<String, dynamic>))
          .toList(),
      current: json['current'] as int?,
      total: json['total'] as int?,
    );

Map<String, dynamic> _$$_LessonTaskListToJson(_$_LessonTaskList instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'name': instance.name,
      'desc': instance.desc,
      'status': instance.status,
      'rewardList': instance.rewardList,
      'current': instance.current,
      'total': instance.total,
    };

_$_RewardList _$$_RewardListFromJson(Map<String, dynamic> json) =>
    _$_RewardList(
      id: json['id'] as String?,
      type: json['type'] as int?,
      value: json['value'] as int?,
      name: json['name'] as String?,
      rewardData: json['rewardData'] == null
          ? null
          : RewardData.fromJson(json['rewardData'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_RewardListToJson(_$_RewardList instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'value': instance.value,
      'name': instance.name,
      'rewardData': instance.rewardData,
    };

_$_RewardData _$$_RewardDataFromJson(Map<String, dynamic> json) =>
    _$_RewardData(
      medalImg: json['medalImg'] as String?,
      medalUpgradeImg: json['medalUpgradeImg'] as String?,
      medalAudio: json['medalAudio'] as String?,
      flutterRes: json['flutterRes'] as String?,
    );

Map<String, dynamic> _$$_RewardDataToJson(_$_RewardData instance) =>
    <String, dynamic>{
      'medalImg': instance.medalImg,
      'medalUpgradeImg': instance.medalUpgradeImg,
      'medalAudio': instance.medalAudio,
      'flutterRes': instance.flutterRes,
    };

_$_Res _$$_ResFromJson(Map<String, dynamic> json) => _$_Res(
      img: json['img'] as String?,
      audio: json['audio'] as String?,
    );

Map<String, dynamic> _$$_ResToJson(_$_Res instance) => <String, dynamic>{
      'img': instance.img,
      'audio': instance.audio,
    };
