import 'package:jojo_flutter_base/base.dart';

class ContinueLearnSpineAb {
  
  static const String _abKey = 'jojo_client_continue_learn_spine_old';
  
  static Future<bool> getValue(Map<String, Object> parmas) async {
    
    final abTestingInstance = ABTesting.instance;
    final ApiResult<bool> res = await abTestingInstance.getBool(_abKey, context: parmas);
    return res.data?.value ?? false;
  }
}