import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/continue_learn_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/method_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/die_guide_builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/frozen_guide_builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/milestone_guide_builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/normal_guide_builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/today_not_finish_guide_builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/vibrate_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/audio_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/constants.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/delay_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_types.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/spine_status_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/when_complete_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/task_settle/widget/animations/animation_audio_mixin.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/ext.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';


abstract class BaseFromView extends StatefulWidget {
  final ContinueLearnState state;
  final TapCallback? tapCallback;
  final AnimationFinishCallback? animationFinishCallback;
  final FinalStageChangeCallback finalStageChangeCallback;
  final GuideEndCallback? guideEndCallback;
  final AnimationEventCallback? animationEventCallback;// 动画事件回调
  final Color? subjectColor;

  const BaseFromView({
    super.key,
    required this.state,
    this.tapCallback,
    this.animationFinishCallback,
    required this.finalStageChangeCallback,
    this.guideEndCallback,
    required this.animationEventCallback,
    this.subjectColor,
  });
}

abstract class BaseFromState<T extends BaseFromView> extends State<T>
    with
        TickerProviderStateMixin,
        PageStatusProvider,
        AudioProvider,
        AnimationAudioPlayMixin {
  final animKey = GlobalKey();

  late Map<SpineNames, SpineNames> spineNameConvertMap;

  bool showFinalButtons = false; // 后续引导 - 按钮控制（按钮动画会后出来）

  bool showEndPageButtons = false; // 结束页 - 按钮控制（按钮动画会后出来，目前只有里程碑结束页使用）

  bool showEndPageTip = false; // 结束页 - 气泡控制（目前只有里程碑结束页使用）

  bool isNextButtonDisabled = false; // 是否禁用下一步按钮 (目前只有复活道具使用)

  bool _isPlayingFireSequenceAudio = false;

  StreamSubscription? _playerStateStreamSubscription;
  StreamSubscription? _playerCompleteStreamSubscription;

  final int _numberAppearIndex = 0;
  final int _numberChangeIndex = 1;
  final int _boxOpenIndex = 2;
  final int _productOpenIndex = 3;

  bool showBoxWhenProduct = false;

  final boxOpenBgKey = GlobalKey();
  final boxOpenKey = GlobalKey();
  final productOpenKey = GlobalKey();

  /// 结束页 start
  late AnimationController endPageSlideController;
  late AnimationController endPageButtonController;

  late AnimationController shakeController;
  late Animation<double> shakeAnimation;

  /// 结束页 end

  /// 后续引导 start
  late AnimationController _firstNumberTextController;

  late AnimationController secondNumberTextController;

  late AnimationController finalButtonController;

  late Animation<double> _scaleAnimation;

  late AnimationController finalStatusTypeOneAnimController;

  late AnimationController dieToFrozenTextAnimController;

  /// 后续引导 end

  /// fire sequence start
  late AnimationController fireSequenceNumberController;

  late AnimationController fireSequenceDayAppearAnimController;

  /// fire sequence end

  final Map<AnimationController, double> shouldResumeForwardAnimMap = {};
  final Map<AnimationController, double> shouldResumeReverseAnimMap = {};

  final DelayManager _delayManager = DelayManager();

  final WhenCompleteManager _whenCompleteManager = WhenCompleteManager();

  putInWhenCompleteMap(
      AnimationController controller, WhenCompleteHandler handler) {
    _whenCompleteManager.putInWhenCompleteMap(controller, handler);
  }

  WhenCompleteHandler? getFromWhenCompleteMap(AnimationController controller) {
    return _whenCompleteManager.getFromWhenCompleteMap(controller);
  }

  putInDelayMap(
      dynamic key, int delayMilliseconds, WhenCompleteHandler handler) {
    _delayManager.putInDelayMap(key, delayMilliseconds, handler);
  }

  WhenCompleteHandler? getFromDelayMap(dynamic key) {
    final whenCompleteHandler = _delayManager.getFromDelayMap(key);
    if (!mounted) {
      return null;
    }
    return whenCompleteHandler;
  }

  bool animationFinishCallback(SpineNames names);

  SpineNames? customNextSpineNameHandler(SpineNames old);

  @override
  void didUpdateWidget(covariant T oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.state != widget.state) {
      if (widget.state.initSpineNames == SpineNames.dieToFrozen &&
          widget.state.finalStatusPageStage == finalStageThree) {
        ContinueLearnVibrateType.dieToFrozen();
        _playDieToFrozenAudioUrl();
      }

      if (oldWidget.state.showEndPageStatus != widget.state.showEndPageStatus &&
          widget.state.showEndPageStatus) {
        _playEndPageAudio();
        _slideShowEndPage();
      }

      if (oldWidget.state.showFinalStatus != widget.state.showFinalStatus &&
          widget.state.showFinalStatus &&
          widget.state.finalStatusPageStage == finalStageOne) {
        // 经到最终状态时，如果火焰跳动语音已经播放完成，那么播放最终状态音频
        if (!_isPlayingFireSequenceAudio) {
          _playFinalAudioUrl();
        }

        if (widget.state.finalStatus == typeWin) {
          _whenCompleteAnimFinalStatusTypeOne();
        }

        if (widget.state.finalStatus == typeFrozen) {
          _triggerToFinalStepTwo();
        }

        if (widget.state.finalStatus == typeDie) {
          _triggerToFinalStepTwo();
        }

        if (widget.state.finalStatus == typeTodayNotFinish) {
          _triggerToFinalStepTwo();
        }

        if (widget.state.finalStatus == typeMilestone) {
          _whenCompleteAnimFinalStatusTypeMilestone();
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();

    _playerStateStreamSubscription =
        audioPlayer.onPlayerStateChanged.listen((event) {
      final urlSource = audioPlayer.source;
      if (urlSource is UrlSource &&
          urlSource.url == widget.state.fireSequenceAudioUrl &&
          event == PlayerState.playing) {
        _isPlayingFireSequenceAudio = true;
      }
    });

    _playerCompleteStreamSubscription =
        audioPlayer.onPlayerComplete.listen((event) {
      if (_isPlayingFireSequenceAudio) {
        _isPlayingFireSequenceAudio = false;

        // 火焰跳动音频时长过长，播放完成时已经到最终状态了，那么接着播放最终状态音频
        if (widget.state.showFinalStatus) {
          safeAction(() {
            _playFinalAudioUrl();
          });
        }
      }
    });

    showEndPageButtons = false;
    showEndPageTip = false;

    endPageSlideController = AnimationController(
      vsync: this,
      duration: endPageSlideAnimDuration,
    );

    endPageButtonController = AnimationController(
      vsync: this,
      duration: endPageButtonAnimDuration,
    );

    shakeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 266),
    );

    const angle = 2;
    shakeAnimation = TweenSequence<double>([
      TweenSequenceItem(
          tween: Tween(begin: 0.0, end: -angle * pi / 180), weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: -angle * pi / 180, end: angle * pi / 180),
          weight: 2),
      TweenSequenceItem(
          tween: Tween(begin: angle * pi / 180, end: 0.0), weight: 1),
    ]).animate(CurvedAnimation(parent: shakeController, curve: Curves.linear));

    showFinalButtons = false;

    _firstNumberTextController = AnimationController(
      vsync: this,
      duration: forzenFirstNumberAppearAnimDuration,
    );

    secondNumberTextController = AnimationController(
      vsync: this,
      duration: forzenSecondNumberAppearAnimDuration,
    );

    finalButtonController = AnimationController(
      vsync: this,
      duration: finalButtonAnimDuration,
    );

    _scaleAnimation =
        Tween<double>(begin: widget.state.scale(), end: 1.0).animate(
      CurvedAnimation(
        parent: secondNumberTextController,
        curve: Curves.easeInOut,
      ),
    );

    dieToFrozenTextAnimController = AnimationController(
      vsync: this,
      duration: dieToFrozenTextAnimDuration,
    );

    finalStatusTypeOneAnimController = AnimationController(
      vsync: this,
      duration: normalFinalStatusAnimDuration,
    );

    fireSequenceNumberController = AnimationController(
      vsync: this,
      duration: normalNumberAnimDuration,
    );

    fireSequenceDayAppearAnimController = AnimationController(
      vsync: this,
      duration: dayAppearAnimDuration,
    );
  }

  @override
  void dispose() {
    disposePageStatus();
    endPageSlideController.dispose();
    endPageButtonController.dispose();
    shakeController.dispose();
    _firstNumberTextController.dispose();
    secondNumberTextController.dispose();
    finalButtonController.dispose();
    finalStatusTypeOneAnimController.dispose();
    dieToFrozenTextAnimController.dispose();
    fireSequenceNumberController.dispose();
    fireSequenceDayAppearAnimController.dispose();
    _playerStateStreamSubscription?.cancel();
    _playerCompleteStreamSubscription?.cancel();
    audioDispose();
    super.dispose();
  }

  @mustCallSuper
  @override
  handleResume(PageResumeEvent event) {
    /// 处理 whenComplete
    _whenCompleteManager.handleResume();

    /// 处理 delay
    _delayManager.handleResume();

    /// 处理动画暂停
    shouldResumeForwardAnimMap.forEach((key, value) {
      if (key == shakeController) {
        key.repeat();
      } else {
        key.forward(from: key.value).whenComplete(() {
          getFromWhenCompleteMap(key)?.call();
        });
      }
    });
    shouldResumeReverseAnimMap.forEach((key, value) {
      key.reverse(from: key.value).whenComplete(() {
        getFromWhenCompleteMap(key)?.call();
      });
    });

    shouldResumeForwardAnimMap.clear();
    shouldResumeReverseAnimMap.clear();

    audioResume();
  }

  @mustCallSuper
  @override
  handlePause(PagePauseEvent event) {
    /// 处理 whenComplete
    _whenCompleteManager.handlePause();

    /// 处理 delay
    _delayManager.handlePause();

    /// 处理动画暂停
    shouldResumeForwardAnimMap.clear();
    shouldResumeReverseAnimMap.clear();

    pauseAndSaveController(endPageSlideController, shouldResumeForwardAnimMap);
    pauseAndSaveController(endPageButtonController, shouldResumeForwardAnimMap);
    pauseAndSaveController(shakeController, shouldResumeForwardAnimMap);

    pauseAndSaveController(
        _firstNumberTextController, shouldResumeForwardAnimMap);
    pauseAndSaveController(
        secondNumberTextController, shouldResumeForwardAnimMap);
    pauseAndSaveController(finalButtonController, shouldResumeForwardAnimMap);
    pauseAndSaveController(
        finalStatusTypeOneAnimController, shouldResumeForwardAnimMap);
    pauseAndSaveController(
        dieToFrozenTextAnimController, shouldResumeForwardAnimMap);
    pauseAndSaveController(
        fireSequenceNumberController, shouldResumeForwardAnimMap);
    pauseAndSaveController(
        fireSequenceDayAppearAnimController, shouldResumeForwardAnimMap);

    audioPause();
  }

  /// final start
  List<Widget> buildListOfFinalWidget() {
    if (widget.state.finalStatus == typeWin) {
      return buildListOfWidgetFinalWidgetTypeNormal();
    } else if (widget.state.finalStatus == typeFrozen) {
      return buildListOfWidgetFinalWidgetTypeIce();
    } else if (widget.state.finalStatus == typeDie) {
      return buildListOfWidgetFinalWidgetTypeDie();
    } else if (widget.state.finalStatus == typeTodayNotFinish) {
      return buildListOfWidgetFinalWidgetTypeTodayNotFinish();
    } else if (widget.state.finalStatus == typeMilestone) {
      return buildListOfWidgetFinalWidgetTypeMilestone();
    }
    l.i(tag, '''buildListOfFinalWidget 显示空白
    finalStatus=${widget.state.finalStatus}''');
    return [];
  }

  /// 公共通用方法
  bool spineShouldLoop(SpineNames spineNames) {
    if (spineNames == SpineNames.speechlessLoop ||
        spineNames == SpineNames.fireLoop ||
        spineNames == SpineNames.dieLoop) {
      return true;
    }

    if (spineNames == SpineNames.normalLoop) {
      return widget.state.showFinalStatus;
    }

    return false;
  }

  List<Widget> buildListOfWidgetFinalWidgetTypeXXXInterruptFirst() {
    final size = spineJoJoSize.rdp * sPadScale;
    return [
      Positioned.fill(
        child: buildDisappearWidget(
          animationController: _firstNumberTextController,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildSpineAreaOverSize(size),
            ],
          ),
        ),
      ),
    ];
  }

  List<Widget> buildListOfWidgetFinalWidgetTypeXXXFirst() {
    final size = spineJoJoSize.rdp * sPadScale;
    return [
      Positioned.fill(
        child: buildDisappearWidget(
          animationController: _firstNumberTextController,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildSpineAreaOverSize(size),
              SizedBox(width: 20.rdp * sPadScale),
              buildNumberImagesNoAnim(widget.state.dayCount,
                  widget.state.numberHeight().rdp * sPadScale)
            ],
          ),
        ),
      ),
    ];
  }

  Widget buildSpineAreaOverSize(double size,
      {double? customWidth, double? customHeight, Widget? child}) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: customWidth ?? 130.rdp * sPadScale,
      height: customHeight ?? 110.rdp * sPadScale,
      child: overflow(
        child: child ?? buildSpineArea(size),
      ),
    );
  }

  Widget buildSpineArea(double size) {
    return SpineStatusView(
      key: animKey,
      width: size,
      height: size,
      spineType: widget.state.spineType,
      initSpineNames: widget.state.initSpineNames,
      animationFinishCallback: animationFinishCallback,
      customNextSpineNameHandler: customNextSpineNameHandler,
      spineShouldLoopCallback: spineShouldLoop,
      animationEventCallback: widget.animationEventCallback,
    );
  }

  Widget _buildWithScale({required Widget child}) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, widget) {
        return Transform.scale(scale: _scaleAnimation.value, child: child);
      },
    );
  }

  Widget buildWidgetFinalWidgetTypeXXXTopArea() {
    return _buildWithScale(
      child: buildNumberImagesNoAnim(
        widget.state.dayCount,
        widget.state.finalNumberHeight().rdp * sPadScale,
      ),
    );
  }

  String buildRemainCountText(int count) {
    if (count <= 0) {
      return "";
    }
    return "${S.of(context).remain}$count${S.of(context).num}";
  }

  triggerDelayNumberChangeAction() {
    putInDelayMap(
        DelayKeys.changeNumber, numberChangeDelayDuration.inMilliseconds, () {
      if (mounted) {
        ContinueLearnVibrateType.fireToFire();
        _triggerNumberChangeAction();
      }
    });
    Future.delayed(numberChangeDelayDuration, () {
      getFromDelayMap(DelayKeys.changeNumber)?.call();
    });
  }

  _triggerNumberChangeAction() {
    putInWhenCompleteMap(fireSequenceNumberController, () {
      triggerDelayShowDayText();
    });
    fireSequenceNumberController.forward(from: 0).whenComplete(() {
      getFromWhenCompleteMap(fireSequenceNumberController)?.call();
    });

    if (widget.state.finalStatus == typeWin) {
      playNumberAppearAudio();
    }

    if (widget.state.isPlayAudioWhenNumberChange()) {
      playFireSequenceAudioUrl();
    }

    _playNumberChangeAudio();
  }

  _whenCompleteAnimFinalStatusTypeMilestone() {
    putInWhenCompleteMap(finalStatusTypeOneAnimController, () {
      _whenCompleteAnimFinalStatusTypeMilestoneBoxShake();
    });
    finalStatusTypeOneAnimController.forward(from: 0).whenComplete(() {
      getFromWhenCompleteMap(finalStatusTypeOneAnimController)?.call();
    });
  }

  _whenCompleteAnimFinalStatusTypeMilestoneBoxShake() {
    shakeController.repeat();
    putInDelayMap(
        DelayKeys.doGuideEnd, milestoneGuideToBoxDuration.inMilliseconds, () {
      if (mounted) {
        widget.guideEndCallback?.call();
      }
    });
    Future.delayed(milestoneGuideToBoxDuration, () {
      getFromDelayMap(DelayKeys.doGuideEnd)?.call();
    });
  }

  _slideShowEndPage() {
    putInWhenCompleteMap(endPageSlideController, () {
      _delayShowEndPageButtons();
      _showEndPageTip();
    });
    endPageSlideController.forward(from: 0).whenComplete(() {
      getFromWhenCompleteMap(endPageSlideController)?.call();
    });
  }

  _showEndPageTip() {
    if (mounted) {
      setState(() {
        showEndPageTip = true;
      });
    }
  }

  _delayShowEndPageButtons() {
    putInDelayMap(DelayKeys.showEndPageButtons,
        endPageButtonShowDelayDuration.inMilliseconds, () {
      if (mounted) {
        setState(() {
          showEndPageButtons = true;
        });
        endPageButtonController.forward(from: 0);
      }
    });
    Future.delayed(endPageButtonShowDelayDuration, () {
      getFromDelayMap(DelayKeys.showEndPageButtons)?.call();
    });
  }

  _whenCompleteAnimFinalStatusTypeOne() {
    putInWhenCompleteMap(finalStatusTypeOneAnimController, () {
      _delayShowFinalButtons();
    });
    finalStatusTypeOneAnimController.forward(from: 0).whenComplete(() {
      getFromWhenCompleteMap(finalStatusTypeOneAnimController)?.call();
    });
  }

  _whenCompleteShowFinalSecond() {
    putInWhenCompleteMap(secondNumberTextController, () {
      _delayShowFinalButtons();
    });
    secondNumberTextController.forward(from: 0).whenComplete(() {
      getFromWhenCompleteMap(secondNumberTextController)?.call();
    });
  }

  _triggerToFinalStepTwo() {
    putInWhenCompleteMap(_firstNumberTextController, () {
      widget.finalStageChangeCallback(finalStageTwo);
      _whenCompleteShowFinalSecond();
    });
    _firstNumberTextController.forward().whenComplete(() {
      getFromWhenCompleteMap(_firstNumberTextController)?.call();
    });
  }

  _delayShowFinalButtons() {
    putInDelayMap(DelayKeys.showFinalButtons,
        finalPageButtonShowDelayDuration.inMilliseconds, () {
      if (mounted) {
        setState(() {
          showFinalButtons = true;
        });
        finalButtonController.forward(from: 0);
      }
    });
    Future.delayed(finalPageButtonShowDelayDuration, () {
      getFromDelayMap(DelayKeys.showFinalButtons)?.call();
    });
  }

  triggerDelayShowDayText() {
    putInDelayMap(DelayKeys.showDay, normalNumberShowDuration.inMilliseconds,
        () {
      if (mounted) {
        fireSequenceDayAppearAnimController.forward(from: 0);
      }
    });
    Future.delayed(normalNumberShowDuration, () {
      getFromDelayMap(DelayKeys.showDay)?.call();
    });
  }

  _playAudioUrl(String? audioPath) {
    _isPlayingFireSequenceAudio = false;
    if (audioPath.isNotNullOrEmpty()) {
      playAudioUrl(audioPlayer: audioPlayer, url: audioPath!);
    }
  }

  _playLocalAudioByIndex(String? audioPath, int index) {
    if (audioPath.isNotNullOrEmpty()) {
      playAudio(audioPlayer: audioPlayerByIndex(index), path: audioPath!);
    }
  }

  playDieEnterAudioUrl() {
    final dieEnterAudioUrl = widget.state.dieEnterAudioUrl;
    widget.state.dieEnterAudioUrl = null;
    _playAudioUrl(dieEnterAudioUrl);
  }

  playFireSequenceAudioUrl() {
    _playAudioUrl(widget.state.fireSequenceAudioUrl);
  }

  _playFinalAudioUrl() {
    _playAudioUrl(widget.state.finalAudioUrl);
  }

  _playDieToFrozenAudioUrl() {
    _playAudioUrl(widget.state.finalDieToFrozenAudioUrl);
  }

  playNumberAppearAudio() {
    if (!widget.state.isDayChange()) {
      return;
    }

    _playLocalAudioByIndex(
        AssetsAudio.FINISH_COURSE_NUMBER_APPEAR, _numberAppearIndex);
  }

  _playNumberChangeAudio() {
    if (!widget.state.isDayChange()) {
      return;
    }

    _playLocalAudioByIndex(
        AssetsAudio.FINISH_COURSE_NUMBER_CHANGE, _numberChangeIndex);
  }

  _playEndPageAudio() {
    _playAudioUrl(widget.state.endAudioUrl);
  }

  playBoxGetAudio() {
    _playLocalAudioByIndex(AssetsAudio.FINISH_COURSE_BOX_GET, _boxOpenIndex);
  }

  playBoxOpenAudio() {
    _playLocalAudioByIndex(AssetsAudio.FINISH_COURSE_BOX_OPEN, _boxOpenIndex);
  }

  playProductOpenAudio() {
    _playLocalAudioByIndex(
        AssetsAudio.FINISH_COURSE_PRODUCT_OPEN, _productOpenIndex);
  }

  playBuyLockAudio() {
    playAudio(audioPlayer: audioPlayer, path: AssetsAudio.BUY_LOCK_TIP);
  }
  
  String buildListOfWidgetEmptyMessage() {
    return '''showEndPageStatus=${widget.state.showEndPageStatus}
      showBoxSequenceStatus=${widget.state.showBoxSequenceStatus}
      showFinalStatus=${widget.state.showFinalStatus}
      initSpineNames=${widget.state.initSpineNames.name}''';
  }

  setShowBoxWhenProductValue(bool value) {
    if (mounted) {
      setState(() {
        showBoxWhenProduct = value;
      });
    }
  }
}
