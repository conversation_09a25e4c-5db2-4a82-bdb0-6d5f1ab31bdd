import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/box_open_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/local_spine_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/product_open_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

/// 里程碑宝箱打开
extension MilestoneBoxOpenBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildMilestoneBoxOpenWidget() {
    if (widget.state.currentShowProduct != null) {
      return _buildMilestoneProductOpenWidget();
    }

    return [
      Positioned.fill(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap:  () {
            playBoxOpenAudio();
            widget.tapCallback?.call(TapType.openBox,null);
          },
          child: _buildBoxArea(),
        ),
      ),
    ];
  }

  Positioned _buildBoxOpenFg() {
    return Positioned.fill(
        child: Center(
      child: LocalSpineView(
        key: boxOpenBgKey,
        initSpineName: widget.state.getProductBgSpineNames(),
        atlasPath: AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_ATLAS,
        skelPath: AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_SKEL,
        spineShouldLoopCallback: (_) => false,
        spineNeedListenerCallback: (_) => false,
        animationFinishCallback: (_) => true,
        customNextSpineNameHandler: (_) => null,
        scale: backgroundScaleForSPad,
      ),
    ));
  }

  Widget _buildBoxArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        buildBigText(
          context,
          "",
          scale: sPadScale,
        ),
        SizedBox(height: 104.rdp),
        BoxOpenView(
            key: boxOpenKey,
            boxOpenCallback: _boxOpenCallback,
            initSpineName: widget.state.getBoxSpineNames(),
            scale: sPadScale),
        SizedBox(height: 178.rdp),
        buildSmallText(
          context,
          widget.state.showBoxMessage() ? S.of(context).clickAndOpen : "",
          textColor: context.appColors.colorVariant5(widget.state.subjectColor),
          fontWeight: FontWeight.w600,
          scale: sPadScale,
        )
      ],
    );
  }

  List<Widget> _buildMilestoneProductOpenWidget() {
    final isFirstProduct = widget.state.isFirstProduct();
    return [
      if (showBoxWhenProduct && isFirstProduct) Positioned.fill(child: _buildBoxArea()),
      if (isFirstProduct) _buildBoxOpenFg(),
      Positioned.fill(
        child: ProductOpenView(
          key: productOpenKey,
          initSpineName: widget.state.getProductBgSpineNames(),
          localProductData: widget.state.currentShowProduct!,
          dividerText: widget.state.getProductDividerText(),
          tapCallback: widget.tapCallback,
          productOpenCallback: _boxOpenCallback,
          isFirstProduct: isFirstProduct,
          subjectColor: widget.state.subjectColor,
          scale: sPadScale,
        ),
      )
    ];
  }

  _boxOpenCallback(int type) {
    switch (type) {
      case typeInit:
        playBoxGetAudio();
        break;
      case typeOpen:
        playBoxOpenAudio();
        widget.tapCallback?.call(TapType.autoOpenBox,null);
        break;
      case typeShowProduct:
        setShowBoxWhenProductValue(true);
        widget.tapCallback?.call(TapType.openBoxShow,null);
        break;
      case typeBoxOpenFinish:
        setShowBoxWhenProductValue(false);
        break;
      case typeProductOpen:
        playProductOpenAudio();
        break;
      default:
        break;
    }
  }
}
