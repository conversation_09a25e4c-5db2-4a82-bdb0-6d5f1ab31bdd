import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

import '../../../buried_ext.dart';

/// 后续引导 - 普通
extension NormalGuideBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildListOfWidgetFinalWidgetTypeNormal() {
    if (widget.state.isDayChange()) {
      return _buildListOfWidgetFinalWidgetTypeOne();
    }
    return _buildListOfWidgetFinalWidgetTypeOneInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeOneInterrupt() {
    final size = spineJoJoSize.rdp * sPadScale;
    return [
      Positioned.fill(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneInterruptTopSlideArea(size),
          _buildListOfWidgetFinalWidgetTypeOneInterruptBottomAppearArea(),
        ],
      )),
      Positioned(
        bottom: 80.rdp,
        left: 0,
        right: 0,
        child: showFinalButtons
            ? _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea()
            : SizedBox(height: 72.rdp * sPadScale),
      ),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeOneInterruptTopSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp * sPadScale / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp * sPadScale),
            buildSpineAreaOverSize(size),
          ],
        ));
  }

  Widget _buildListOfWidgetFinalWidgetTypeOneInterruptBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneInterruptBottomHorizontalSlideArea(),
          SizedBox(height: 134.rdp),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneInterruptBottomHorizontalSlideArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 12.rdp * sPadScale),
        buildBigText(context, S.of(context).continueNormalDayNotChangeTip,
            textAlign: TextAlign.center, scale: sPadScale),
        buildSmallText(context, widget.state.finalSmallTip ?? "",
            scale: sPadScale),
      ],
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeOne() {
    final size = spineJoJoSize.rdp * sPadScale;
    return [
      Positioned.fill(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneTopVerticalSlideArea(size),
          _buildWidgetFinalWidgetTypeOneBottomAppearArea(),
        ],
      )),
      Positioned(
        bottom: 80.rdp,
        left: 0,
        right: 0,
        child: showFinalButtons
            ? _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea()
            : SizedBox(height: 72.rdp * sPadScale),
      ),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeOneTopVerticalSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp * sPadScale / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp * sPadScale),
            _buildWidgetFinalWidgetTypeOneTopArea(size),
          ],
        ));
  }

  Widget _buildWidgetFinalWidgetTypeOneTopArea(double size) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildWidgetFinalWidgetTypeOneTopSpineArea(size),
        SizedBox(width: 20.rdp * sPadScale),
        Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildNumberImagesNoAnim(widget.state.dayCount,
                  widget.state.numberHeight().rdp * sPadScale),
            ]),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneTopSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: 130.rdp * sPadScale,
      height: widget.state.numberHeight().rdp * sPadScale,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneBottomHorizontalSlideArea(),
          SizedBox(height: 134.rdp),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomHorizontalSlideArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 12.rdp * sPadScale),
        buildBigText(context, S.of(context).continuousPersistence,
            scale: sPadScale),
        buildSmallText(context, widget.state.finalSmallTip ?? "",
            scale: sPadScale),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea() {
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 28.rdp * sPadScale),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons() {
    final btnText = widget.state.isDayChange()
        ? (widget.state.isFirstComplete()
            ? S.of(context).iWillCome
            : S.of(context).continueMaintain)
        : S.of(context).ok;
    if (widget.state.showShareButton()) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
              onTap: () => widget.tapCallback?.call(TapType.iWillCome,btnText),
              child: VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
                child: buildButton(context, btnText,
                  width: 132.rdp * sPadScale,
                  backgroundColor: Colors.white,
                  borderColor: context.appColors.mainColor,
                  scale: sPadScale)),
          ),
          SizedBox(width: 20.rdp * sPadScale),
          GestureDetector(
              onTap: () => widget.tapCallback?.call(TapType.winShare,S.of(context).shareMyHighlightMoment),
              child: VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: S.of(context).shareMyHighlightMoment),
                child:buildShareButton(
                  context,
                  S.of(context).shareMyHighlightMoment,
                  AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_CAMERA,
                  scale: sPadScale,
                )
              ),
          ),
        ],
      );
    } else {
      return GestureDetector(
          onTap: () => widget.tapCallback?.call(TapType.iWillCome,btnText),
          child:  VisibilityObserve(
            onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
            child: buildButton(
              context,
              btnText,
              width: 280.rdp * sPadScale,
              scale: sPadScale,
          ),
        ),
      );
    }
  }
}
