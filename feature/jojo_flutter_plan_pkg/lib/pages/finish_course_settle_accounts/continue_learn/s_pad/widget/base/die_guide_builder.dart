import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/continue_learn_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/revive_button.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/ext.dart';

import '../../../../../../static/img.dart';
import '../../../../model/local_can_buy_prop_data.dart';
import '../../../buried_ext.dart';

/// 后续引导 - 嗝屁
extension DieGuideBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildListOfWidgetFinalWidgetTypeDie() {
    if (widget.state.isDayChange()) {
      return _buildListOfWidgetFinalWidgetTypeThree();
    }
    return _buildListOfWidgetFinalWidgetTypeThreeInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeInterrupt() {
    if (widget.state.finalStatusPageStage == finalStageOne) {
      return buildListOfWidgetFinalWidgetTypeXXXInterruptFirst();
    }
    if (widget.state.finalStatusPageStage == finalStageTwo) {
      return _buildListOfWidgetFinalWidgetTypeThreeSecondInterrupt();
    }
    if (widget.state.finalStatusPageStage == finalStageThree) {
      return _buildListOfWidgetFinalWidgetTypeThreeThird();
    }
    l.i(tag, '''_buildListOfWidgetFinalWidgetTypeThreeInterrupt 显示空白
    finalStatusPageStage=${widget.state.finalStatusPageStage}''');
    return [];
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeSecondInterrupt() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildWidgetFinalWidgetTypeThreeSlideAreaInterrupt(),
          ],
        ),
      ),
      Positioned(
          bottom: 80.rdp,
          left: 0,
          right: 0,
          child: showFinalButtons
              ? _buildWidgetFinalWidgetTypeThreeBottomButtonArea()
              : SizedBox(height: 44.rdp * sPadScale)),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeSlideAreaInterrupt() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 108.rdp),
          buildSpineAreaOverSize(spineJoJoSize.rdp * sPadScale),
          SizedBox(height: 60.rdp),
          buildBigText(context, S.of(context).continueIceDayNotChangeTip, scale: sPadScale),
          buildSmallText(context, widget.state.finalSmallTip ?? "", scale: sPadScale),
        ],
      ),
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThree() {
    if (widget.state.finalStatusPageStage == finalStageOne) {
      return buildListOfWidgetFinalWidgetTypeXXXFirst();
    }
    if (widget.state.finalStatusPageStage == finalStageTwo) {
      return _buildListOfWidgetFinalWidgetTypeThreeSecond();
    }
    if (widget.state.finalStatusPageStage == finalStageThree) {
      return _buildListOfWidgetFinalWidgetTypeThreeThird();
    }
    l.i(tag, '''_buildListOfWidgetFinalWidgetTypeThree 显示空白
    finalStatusPageStage=${widget.state.finalStatusPageStage}''');
    return [];
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeSecond() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 30.rdp),
            _buildWidgetFinalWidgetTypeThreeSlideArea(),
            SizedBox(height: 90.rdp),
          ],
        ),
      ),
      Positioned(
          bottom: 80.rdp,
          left: 0,
          right: 0,
          child: showFinalButtons
              ? _buildWidgetFinalWidgetTypeThreeBottomButtonArea()
              : SizedBox(height: 44.rdp * sPadScale)),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeSlideArea() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildWidgetFinalWidgetTypeXXXTopArea(),
          SizedBox(height: 8.rdp * sPadScale),
          buildBigText(context, S.of(context).continuousPersistence, scale: sPadScale),
          _buildWidgetFinalWidgetTypeThreeCenterArea(),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeCenterArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 8.rdp * sPadScale),
        _buildWidgetFinalWidgetTypeThreeSpineArea(280.rdp * sPadScale),
        SizedBox(height: 12.rdp * sPadScale),
        buildSmallText(context, widget.state.finalSmallTip ?? "", scale: sPadScale),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: (94 / 110 * 130).rdp * sPadScale,
      height: 94.rdp * sPadScale,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeBottomButtonArea() {
    final btn = widget.state.btn;
    final count = widget.state.revivePropIdList.length;
    final canBuyCount = widget.state.canBuyRevivePropIdList.length;
    final bool isShowReviveButton = count > 0 || canBuyCount > 0;
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () => widget.tapCallback?.call(TapType.forceIgnore,S.of(context).forceIgnore),
              child:  VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: S.of(context).forceIgnore),
                child:buildButton(context, S.of(context).forceIgnore,
                  backgroundColor: Colors.white,
                  borderColor: context.appColors.jColorYellow4, scale: sPadScale),
              ),
            ),
            if (btn != null && isShowReviveButton) SizedBox(width: 20.rdp * sPadScale),
            if (btn != null && isShowReviveButton)
            _buildReviveOrBuyButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildReviveOrBuyButton(BuildContext context) {
    final count = widget.state.revivePropIdList.length;
    return count > 0 || widget.state.keepReviveButtonVisible
        ? _buildReviveOnlyButton(context, count)
        : _buildBuyAndUseButton(context);
  }

  Widget _buildReviveOnlyButton(BuildContext context, int count) {
    return GestureDetector(
        onTap: () => widget.tapCallback?.call(TapType.useToolForOtherCourse,widget.state.btn?.btnName ?? S.of(context).reviveTool,),
        child: VisibilityObserve(
        onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: widget.state.btn?.btnName ?? S.of(context).reviveTool),
        child:ReviveButton(
          buttonText: widget.state.btn?.btnName ?? S.of(context).reviveTool,
          buttonBackgroundColor: context.appColors.jColorYellow4,
          remainCountText: buildRemainCountText(count),
        )));
  }

  Widget _buildBuyAndUseButton(BuildContext context) {
    final LocalCanBuyPropData? propData =
        widget.state.canBuyRevivePropIdList.firstOrNull;
    final price = propData?.price ?? 0;
    final useAssetType = propData?.useAssetType ?? 1;
    final imgAssetName = useAssetType == 1
        ? AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_XUE_DOU
        : AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_DOU_XING_BAO_ZUAN;

    return IgnorePointer(
      ignoring: isNextButtonDisabled,
      child: Opacity(
        opacity: isNextButtonDisabled ? 0.5 : 1.0,
        child: GestureDetector(
          onTap: () => widget.tapCallback?.call(TapType.buyAndUseToolForOtherCourse,S.of(context).buyAndUseProp),
          child: VisibilityObserve(
          onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: S.of(context).buyAndUseProp),
          child:ReviveButton(
            buttonText: S.of(context).buyAndUseProp,
            buttonBackgroundColor: context.appColors.jColorYellow4,
            remainCountText: '$price',
            remainIcon: imgAssetName,
            width: 160.rdp * sPadScale,
          ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeThird() {
    return [
      _buildWidgetFinalWidgetTypeThreeThirdSpine(),
      _buildWidgetFinalWidgetTypeThreeThirdText(),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeThirdSpine() {
    return Positioned.fill(
      child: buildSpineAreaOverSize(spineJoJoSize.rdp * sPadScale),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeThirdText() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 70.rdp + (screen.screenHeight - 375.rdp * sPadScale) / 2,
      child: FadeTransition(
        opacity: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(dieToFrozenTextAnimController),
        child: Center(
          child: buildBigText(
            context,
            S.of(context).forzenFinalBigTip, scale: sPadScale
          ),
        ),
      ),
    );
  }
}
