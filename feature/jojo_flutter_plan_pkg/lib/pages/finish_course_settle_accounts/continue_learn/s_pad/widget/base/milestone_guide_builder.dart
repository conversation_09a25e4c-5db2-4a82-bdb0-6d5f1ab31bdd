import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/buried_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
/// 后续引导 - 里程碑
extension MilestoneGuideBuilder<T extends BaseFromView> on BaseFromState<T> {

  List<Widget> buildListOfWidgetFinalWidgetTypeMilestone() {
    final size = spineJoJoSize.rdp * sPadScale;
    return [
      Positioned.fill(
          child: VisibilityObserve(
            onShow: () => buriedContinueLearnAppViewScreen(widget.state, customScreenName: "课程结束_里程碑宝箱结算页面"),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildListOfWidgetFinalWidgetTypeMilestoneTopVerticalSlideArea(size),
                _buildListOfWidgetFinalWidgetTypeMilestoneBottomAppearArea(),
              ],
            ),
          )),
    ];
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneTopVerticalSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp * sPadScale / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp * sPadScale),
            _buildListOfWidgetFinalWidgetTypeMilestoneTopArea(size),
          ],
        ));
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneTopArea(double size) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildListOfWidgetFinalWidgetTypeMilestoneTopSpineArea(size),
        SizedBox(width: 20.rdp * sPadScale),
        Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildNumberImagesNoAnim(
                  widget.state.dayCount, widget.state.numberHeight().rdp * sPadScale),
            ]),
      ],
    );
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneTopSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: 130.rdp * sPadScale,
      height: widget.state.numberHeight().rdp * sPadScale,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 28.rdp * sPadScale),
          buildBigText(context, S.of(context).continuousPersistence, scale: sPadScale),
          SizedBox(height: 8.rdp * sPadScale),
          if (TextUtil.isEmpty(widget.state.finalSmallTip))
            SizedBox(height: 64.rdp * sPadScale)
          else
            Container(
              height: 64.rdp * sPadScale,
              padding: EdgeInsets.symmetric(horizontal: 14.rdp * sPadScale),
              decoration: BoxDecoration(
                border: Border.all(color: context.appColors.jColorGray2, width: 1.rdp),
                borderRadius: BorderRadius.all(Radius.circular(24.rdp * sPadScale)),
                color: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  buildSmallText(context, widget.state.finalSmallTip ?? "", scale: sPadScale),
                  RotationTransition(
                    // 添加旋转动画组件
                    turns: shakeAnimation,
                    alignment: const Alignment(0.0, 0.5),
                    child: ImageAssetWeb(
                      assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_SMALL_BOX,
                      package: Config.package,
                      width: 48.rdp * sPadScale,
                      height: 48.rdp * sPadScale,
                    ),
                  )
                ],
              ),
            ),
          SizedBox(height: 134.rdp),
        ],
      ),
    );
  }
}