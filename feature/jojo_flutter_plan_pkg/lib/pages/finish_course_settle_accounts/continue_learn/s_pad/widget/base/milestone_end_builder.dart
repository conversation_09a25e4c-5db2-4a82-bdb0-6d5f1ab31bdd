import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/buried_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/s_pad/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/milestone_progress_view.dart';

/// 后续引导 - 里程碑 - 最终页面
extension MilestoneEndBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildMilestoneEndWidget() {
    return [
      Positioned.fill(
        child: VisibilityObserve(
          onShow: () => buriedContinueLearnAppViewScreen(widget.state, customScreenName: "课程结束_里程碑进度预告页面"),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 30.rdp),
              _buildMilestoneEndWidgetSlideArea(),
              SizedBox(height: 90.rdp),
            ],
          ),
        ),
      ),
      Positioned(
          bottom: 80.rdp,
          left: 0,
          right: 0,
          child: showEndPageButtons
              ? _buildMilestoneEndWidgetButton(TapType.iWillCome)
              : SizedBox(height: 44.rdp * sPadScale)),
    ];
  }

  Widget _buildMilestoneEndWidgetSlideArea() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: endPageSlideController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildBigText(context, S.of(context).continuousSprintOpen, scale: sPadScale),
          buildSmallText(context, widget.state.endSmallTip ?? "", scale: sPadScale),
          SizedBox(height: 28.rdp * sPadScale),
          MilestoneProgressView(
            initSpineName: widget.state.getMilestoneProgressSpineName(),
            spineType: widget.state.spineType,
            tipPath: showEndPageTip ? widget.state.endBubbleImagePath : null,
            scale: sPadScale,
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneEndWidgetButton(TapType tapType) {
    final btnText = S.of(context).iWillCome;
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: endPageButtonController,
      child: buildAppearWidget(
        animationController: endPageButtonController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
                onTap: () => widget.tapCallback?.call(tapType,btnText),
                child: VisibilityObserve(
                  onShow: ()=> buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
                  child:buildButton(
                      context,
                      btnText,
                      width: 280.rdp * sPadScale, scale: sPadScale)),
              ),
          ],
        ),
      ),
    );
  }
}