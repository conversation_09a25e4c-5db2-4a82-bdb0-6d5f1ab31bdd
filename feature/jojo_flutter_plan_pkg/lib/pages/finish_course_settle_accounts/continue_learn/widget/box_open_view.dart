import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/delay_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_types.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/local_spine_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

typedef BoxOpenCallback = Function(int);

const typeInit = 0; // 首次触发
const typeOpen = 1; // 2s 后自动打开宝箱
const typeShowProduct = 2; // 宝箱打开过程中展示动效+物品
const typeBoxOpenFinish = 3; // 宝箱打开完成
const typeProductOpen = 4; // 物品打开

class BoxOpenView extends ScaleWidget {
  final String initSpineName;
  final BoxOpenCallback boxOpenCallback;

  const BoxOpenView(
      {super.key,
      required this.boxOpenCallback,
      required this.initSpineName,
      super.scale = phoneScale});

  @override
  State<BoxOpenView> createState() => _BoxOpenViewState();
}

class _BoxOpenViewState extends ScaleWidgetState<BoxOpenView> with PageStatusProvider {

  final DelayManager _delayManager = DelayManager();

  putInDelayMap(dynamic key, int delayMilliseconds, WhenCompleteHandler handler) {
    _delayManager.putInDelayMap(key, delayMilliseconds, handler);
  }

  WhenCompleteHandler? getFromDelayMap(dynamic key) {
    final whenCompleteHandler = _delayManager.getFromDelayMap(key);
    if (!mounted) {
      return null;
    }
    return whenCompleteHandler;
  }

  beginAutoOpen() {
    const duration = Duration(seconds: 2);
    putInDelayMap(DelayKeys.boxOpenDelay, duration.inMilliseconds, () {
      if (mounted) {
        widget.boxOpenCallback(typeOpen);
      }
    });
    Future.delayed(duration, () {
      getFromDelayMap(DelayKeys.boxOpenDelay)?.call();
    });
  }

  @override
  void didUpdateWidget(covariant BoxOpenView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initSpineName != widget.initSpineName && widget.initSpineName == BoxSpineNames.boxOpen.name) {
      const duration = Duration(milliseconds: 200);
      putInDelayMap(DelayKeys.boxOpenFinishDelay, duration.inMilliseconds, () {
        if (mounted) {
          widget.boxOpenCallback(typeShowProduct);
        }
      });
      Future.delayed(duration, () {
        getFromDelayMap(DelayKeys.boxOpenFinishDelay)?.call();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.boxOpenCallback(typeInit);
    });
  }

  @override
  void dispose() {
    super.dispose();
    disposePageStatus();
  }

  @override
  handleResume(PageResumeEvent event) {
    /// 处理 delay
    _delayManager.handleResume();
  }

  @override
  handlePause(PagePauseEvent event) {
    /// 处理 delay
    _delayManager.handlePause();
  }


  @override
  Widget build(BuildContext context) {
    return buildOverSize(
        customWidth: _imageScaledRdp(150),
        customHeight: _imageScaledRdp(150),
        child: _buildBoxOpenSpine());
  }

  Widget _buildBoxOpenSpine() {
    return LocalSpineView(
      initSpineName: widget.initSpineName,
      atlasPath: AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_BOX_ATLAS,
      skelPath: AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_BOX_SKEL,
      spineShouldLoopCallback: _spineShouldLoop,
      spineNeedListenerCallback: _spineNeedListener,
      animationFinishCallback: _animationFinish,
      customNextSpineNameHandler: _customNextSpineName,
      width: _imageScaledRdp(300),
      height: _imageScaledRdp(300),
      scale: _imageScale(),
    );
  }

  bool _spineShouldLoop(String curName) {
    return curName == BoxSpineNames.boxLoop.name;
  }

  bool _spineNeedListener(String curName) {
    return curName != BoxSpineNames.boxLoop.name;
  }

  bool _animationFinish(String curName) {
    if (curName == BoxSpineNames.boxOpen.name) {
      if (mounted) {
        widget.boxOpenCallback(typeBoxOpenFinish);
      }
      return true;
    }
    if (curName == BoxSpineNames.boxEnter.name) {
      beginAutoOpen();
    }
    return curName == BoxSpineNames.boxLoop.name;
  }

  String? _customNextSpineName(String curName) {
    if (curName == BoxSpineNames.boxEnter.name) {
      return BoxSpineNames.boxLoop.name;
    }

    return null;
  }

  double _imageScaledRdp(double value) {
    if (widget.scale == sPadScale) {
      return (value * 1.5).rdp;
    }
    return value.rdp;
  }

  double _imageScale() {
    if (widget.scale == sPadScale) {
      return 1.5;
    }
    return 1.0;
  }
}
