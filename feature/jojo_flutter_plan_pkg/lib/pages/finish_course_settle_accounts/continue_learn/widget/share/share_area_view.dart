import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/widgets/share/share_action.dart';

typedef StringValueGetter = String Function(BuildContext context);

typedef ShareTapCallback = void Function(ShareType);

class ShareAreaView extends ScaleWidget {
  final ShareTapCallback shareTapCallback;

  const ShareAreaView(
      {super.key, required this.shareTapCallback, required super.scale});

  @override
  State<ShareAreaView> createState() => _ShareAreaViewState();
}

class _ShareAreaViewState extends ScaleWidgetState<ShareAreaView> {
  final Map<ShareType, Map<String, StringValueGetter>> defaultConfig = {};

  @override
  void initState() {
    super.initState();
    _initMap();
  }

  _initMap() {
    defaultConfig.clear();
    defaultConfig[ShareType.wx] = {
      "icon": (_) => AssetsImg.SHARE_ACTION_WX,
      "text": (ctx) => S.of(ctx).wxFriend,
    };
    defaultConfig[ShareType.wxCircle] = {
      "icon": (_) => AssetsImg.SHARE_ACTION_WX_CIRCLE,
      "text": (ctx) => S.of(ctx).wxCircle,
    };
    defaultConfig[ShareType.qq] = {
      "icon": (_) => AssetsImg.SHARE_ACTION_QQ,
      "text": (ctx) => S.of(ctx).qq,
    };
    defaultConfig[ShareType.download] = {
      "icon": (_) => AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_SAVE_TO_LOCAL,
      "text": (ctx) => S.of(ctx).saveAlbum,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: scaledRdp(487),
      height: scaledRdp(50),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(scaledRdp(25)))),
      child:Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.rdp),
          child: Row(  
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _buildShareItems(context),
          ),
      ),
    );
  }

  List<Widget> _buildShareItems(BuildContext ctx) {
    final List<Widget> itemList = [];
    for (final item in defaultConfig.entries) {
      itemList.add(_buildSingleShareItem(item.key, ctx, item.value));
    }
    return itemList;
  }

  Widget _buildSingleShareItem(ShareType shareType, BuildContext ctx,
      Map<String, StringValueGetter> map) {
    return GestureDetector(
      onTap: () {
        widget.shareTapCallback(shareType);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ImageAssetWeb(
              width: scaledRdp(40),
              height: scaledRdp(40),
              assetName: map['icon']?.call(ctx) ?? "",
              package: Config.package),
          Text(
            map['text']?.call(ctx) ?? "",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: ctx.appColors.textColor,
              fontSize: scaledRdp(15),
              fontWeight: FontWeight.w400,
              height: 1.50,
            ),
          ),
        ],
      ),
    );
  }
}
