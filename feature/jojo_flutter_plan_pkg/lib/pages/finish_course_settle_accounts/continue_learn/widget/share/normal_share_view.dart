import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/share/poster_generate_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/share/share_area_view.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/widgets/share/share_action.dart';

const _tag = 'NormalShareView';

class NormalShareView extends ScaleWidget {
  final Color subjectColor;
  final VoidCallback backCallback;

  final String userName;
  final String avatarUrl;
  final String avatarFrameUrl;
  final String qrcodeUrl;
  final int dayCount;
  final String roleUrl;
  final String subjectName;
  final String shareCardBgUrl;
  final VoidCallback? onShow;
  final ShareTapCallback? onShareClick;
  final String shareDesc;
  final String backgroundText;

  const NormalShareView({
    super.key,
    required super.scale,
    required this.subjectColor,
    required this.backCallback,
    required this.userName,
    required this.avatarUrl,
    required this.avatarFrameUrl,
    required this.qrcodeUrl,
    required this.dayCount,
    required this.roleUrl,
    required this.subjectName,
    required this.shareCardBgUrl,
    required this.shareDesc,
    required this.backgroundText,
    this.onShow,
    this.onShareClick,
  });

  @override
  State<NormalShareView> createState() => _NormalShareViewState();
}

class _NormalShareViewState extends ScaleWidgetState<NormalShareView> {
  final _posterGenerateViewKey = GlobalKey();

  final Map<ShareType, SavePngCallback> _actionMap = {};

  bool _canClick = true;

  @override
  void initState() {
    super.initState();
    _initActionMap();
  }

  _initActionMap() {
    _actionMap.clear();
    _actionMap[ShareType.wx] = _wxShare;
    _actionMap[ShareType.wxCircle] = _wxCircle;
    _actionMap[ShareType.qq] = _qqShare;
    _actionMap[ShareType.download] = _download;
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityObserve(
      onShow: () {
        widget.onShow?.call();
      },
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned.fill(
                child: ColoredBox(
              color: context.appColors.colorVariant1(widget.subjectColor),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  PosterGenerateView(
                    key: _posterGenerateViewKey,
                    scale: widget.scale,
                    userName: widget.userName,
                    avatarUrl: widget.avatarUrl,
                    avatarFrameUrl: widget.avatarFrameUrl,
                    qrcodeUrl: widget.qrcodeUrl,
                    dayCount: widget.dayCount,
                    roleUrl: widget.roleUrl,
                    subjectName: widget.subjectName,
                    shareCardBgUrl: widget.shareCardBgUrl,
                    subjectColor: widget.subjectColor,
                    backgroundText: widget.backgroundText,
                  ),
                  SizedBox(height: _spaceHeight()),
                  ShareAreaView(
                      scale: widget.scale, shareTapCallback: _clickShare),
                ],
              ),
            )),
            Positioned(
                left: scaledRdp(15),
                top: scaledRdp(15),
                child: GestureDetector(
                  onTap: widget.backCallback,
                  child: ImageAssetWeb(
                    assetName:
                        AssetsImg.PLAN_IMAGE_PROMOTE_GUIDE_DIALOG_BACK_ICON,
                    width: scaledRdp(60),
                    height: scaledRdp(60),
                    fit: BoxFit.contain,
                    package: Config.package,
                  ),
                ))
          ],
        ),
      ),
    );
  }

  double _spaceHeight() => widget.scale == sharePhoneScale ? 14.rdp : 40.rdp;

  _clickShare(ShareType type) {
    widget.onShareClick?.call(type);

    if (!_canClick) {
      return;
    }
    _canClick = false;

    final shareWidgetState = _posterGenerateViewKey.currentState;
    if (shareWidgetState == null) {
      _log('posterGenerateViewState is null');
      _canClick = true;
      return;
    }

    final posterGenerateViewState = shareWidgetState as PosterGenerateViewState;
    final action = _actionMap[type];
    if (action == null) {
      _log('action is null');
      _canClick = true;
      return;
    }

    posterGenerateViewState.savePng(action);
  }

  _wxShare(String base64Img) async {
    try {
      final resp = await _share(
        base64Img,
        SharePlatform.wx,
        ShareScene.wx
      );
      _handleResp(resp);
      _canClick = true;
    } catch (e) {
      _log("_wxShare error:$e");
      _canClick = true;
    }
  }

  _wxCircle(String base64Img) async {
    try {
      // if(widget.shareDesc.isNotEmpty){
      //   Clipboard.setData(ClipboardData(text: widget.shareDesc));
      //   JoJoToast.showText(S.of(context).shareCopyTips,duration: 1500);
      //   await Future.delayed(const Duration(milliseconds: 1500));
      // }
      final resp = await _share(
        base64Img,
        SharePlatform.wx,
        ShareScene.wxCircle
      );
      _handleResp(resp);
      _canClick = true;
    } catch (e) {
      _log("_wxCircle error:$e");
      _canClick = true;
    }
  }

  String _buildImageStr(String base64Img) => 'data:image/png;base64,$base64Img';

  _qqShare(String base64Img) async {
    try {
      final resp = await _share(
        base64Img,
        SharePlatform.qq,
        ShareScene.qq
      );
      _handleResp(resp);
      _canClick = true;
    } catch (e) {
      _log("_qqShare error:$e");
      _canClick = true;
    }
  }

  _download(String base64Img) async {
    try {
      await jojoNativeBridge.saveBase64Image(base64: _buildImageStr(base64Img));
      _canClick = true;
    } catch (e) {
      _log("_download error:$e");
      _canClick = true;
    }
  }

  Future<JoJoBridgeResponse<void>?> _share(
    String base64Img,
    SharePlatform platform,
    ShareScene scene,
  
  ) {
    return jojoNativeBridge.shareToPlatform(
      desc: '',
      image: _buildImageStr(base64Img),
      platformType: platform.type,
      scene: scene.type,
      shareType: _shareTypeImage,
      title: '',
      url: '',
    );
  }

  _log(String msg) {
    l.e(_tag, msg);
  }

  _handleResp(JoJoBridgeResponse<void>? resp) {
    if (resp == null) { // ios 分享成功返回 null
      return;
    }

    if (resp.status == 200) { // android 分享成功返回 200
      return;
    }

    if (!mounted) {
      return;
    }

    var msg = resp.msg;
    if (TextUtil.isEmpty(msg)) {
      msg = "${S.of(context).shareFail} status=${resp.status}";
    }
    JoJoToast.showError(msg);
  }
}

const _shareTypeImage = 1;

enum SharePlatform {
  wx(0),
  qq(1);

  const SharePlatform(this.type);

  final int type;
}

enum ShareScene {
  wx(0),
  wxCircle(1),
  qq(2);

  const ShareScene(this.type);

  final int type;
}
