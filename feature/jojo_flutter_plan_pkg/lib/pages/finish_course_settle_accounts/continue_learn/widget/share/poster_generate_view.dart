import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

const _tag = 'PosterGenerateView';

typedef SavePngCallback = Function(String base64Img);

class PosterGenerateView extends ScaleWidget {
  final String userName;
  final String avatarUrl;
  final String avatarFrameUrl;
  final String qrcodeUrl;
  final int dayCount;
  final String roleUrl;
  final String subjectName;
  final String shareCardBgUrl;
  final Color subjectColor;
  final String backgroundText;

  const PosterGenerateView({
    super.key,
    required super.scale,
    required this.userName,
    required this.avatarUrl,
    required this.avatarFrameUrl,
    required this.qrcodeUrl,
    required this.dayCount,
    required this.roleUrl,
    required this.subjectName,
    required this.shareCardBgUrl,
    required this.subjectColor,
    required this.backgroundText,
  });

  @override
  State<PosterGenerateView> createState() => PosterGenerateViewState();
}

class PosterGenerateViewState extends ScaleWidgetState<PosterGenerateView> {
  final GlobalKey _repaintKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final size = scaledRdp(335);
    return RepaintBoundary(
        key: _repaintKey,
        child: SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              _bgWidget(), // 背景图
              _logoWidget(),
              _buildTitle(context), // 标题
              _buildDayCount(widget.dayCount), // 天数
              _buildBottom(context), // 底部
            ],
          ),
        ));
  }

  Widget _bgWidget() {
    return Positioned.fill(
      child: ImageNetworkCached(
        imageUrl: widget.shareCardBgUrl,
        placeholderWidget: _defaultShareCardBg(),
        errorWidget: _defaultShareCardBg(),
        fit: BoxFit.cover,
        memCacheWidth : 400.rdp.toInt() * MediaQuery.of(context).devicePixelRatio.toInt(),
        fadeInDuration: const Duration(milliseconds: 0),
        fadeOutDuration: const Duration(milliseconds: 0),
      ),
    );
  }

  Widget _logoWidget() {
    return Transform.translate(
        offset: Offset(scaledRdp(4), scaledRdp(10)),
        child: ImageAssetWeb(
          assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_SHARE_LOGO,
          package: Config.package,
          width: scaledRdp(82),
          height: scaledRdp(20),
          fit: BoxFit.contain,
        ));
  }

  Widget _buildTitle(
    BuildContext context,
  ) {
    return Positioned.fill(
        child: FractionalTranslation(
      translation: const Offset(0, -0.32),
      child: _continueLearnTitle(), // 我在 叫叫益智 坚持学习了
    ));
  }

  Widget _buildDayCount(int dayCount) {
    
    final length = dayCount.abs().toString().length;
    double fontSize = scaledRdp(68);
    double tianMargin = scaledRdp(18);
    double descMargin = -scaledRdp(16);
    double dayMargin = scaledRdp(6);
    if (length == 1) {
      fontSize = scaledRdp(96);
      tianMargin = scaledRdp(32);
      descMargin = -scaledRdp(36);
      dayMargin = -scaledRdp(2);
    } else if (length == 2) {
      fontSize = scaledRdp(84);
      tianMargin = scaledRdp(26);
      descMargin = -scaledRdp(26);
      dayMargin = scaledRdp(2);
    } else if (length == 3) {
      fontSize = scaledRdp(70);
      tianMargin = scaledRdp(16);
      descMargin = -scaledRdp(18);
      dayMargin = scaledRdp(6);
    }

    return Positioned.fill(
        child: Transform.translate(
      offset: Offset(0, scaledRdp(56)),
      child: Padding(
        padding: EdgeInsets.only(left: scaledRdp(12)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Transform.translate(
            offset: Offset(0, dayMargin),
            child:Row(
            children: [
              Text(
                dayCount.toString(),
                style: TextStyle(
                  fontSize: fontSize,
                  color: Colors.white,
                  fontFamily: 'MohrRounded',
                  package: Config.package,
                  shadows: getShadow(8),
                ),
              ),
              Padding(
                  padding: EdgeInsets.only(top: tianMargin), // 向下偏移 20 像素
                  child: ImageAssetWeb(
                    assetName:
                        AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_DAY_TEXT_WHITE,
                    height: scaledRdp(23),
                    package: Config.package,
                    fit: BoxFit.contain,
                  )),
            ],
            )
          ),
          Transform.translate(
            offset: Offset(0, descMargin),
            child: SizedBox(
              width: scaledRdp(155),
              child: Text(
                widget.backgroundText.toString(),
                style: TextStyle(
                  fontSize: scaledRdp(14),
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  package: Config.package,
                  shadows: getShadow(4),
                ),
              ),
            ),
          ),
        ]),
      ),
    ));
  }

  List<Shadow> getShadow(double blurRadius) {
    return [
      BoxShadow(
        color: const Color.fromRGBO(0, 0, 0, 0.15),
        blurRadius: scaledRdp(blurRadius),
        offset: const Offset(0, 0),
      )
    ];
  }

  Widget _continueLearnTitle() {
    final String firstTip = S.of(context).JoJo + widget.subjectName;
    return Padding(
      padding: EdgeInsets.only(left: scaledRdp(12)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(S.of(context).shareDayCountFirstTip,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: scaledRdp(18),
                color: Colors.white,
                shadows: getShadow(4),
              )),
          SizedBox(width: scaledRdp(2)),
          Text(firstTip,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: scaledRdp(18),
                color: Colors.white,
                shadows: getShadow(4),
              )),
          SizedBox(width: scaledRdp(2)),
          Text(S.of(context).shareDayCountSecondTip,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: scaledRdp(18),
                color: Colors.white,
                shadows: getShadow(4),
              )),
        ],
      ),
    );
  }

  Widget _buildBottom(BuildContext context) {
    return Positioned.fill(
        child: Padding(
            // 先设置内边距
            padding: EdgeInsets.fromLTRB(0, 0, 0, scaledRdp(12)),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildRolePositioned(),
                _buildQrcodeArea(context),
                _nameInfo(),
              ],
            )));
  }

  Widget _buildQrcodeArea(BuildContext context) {
    final String qrcodeUrl = widget.qrcodeUrl;
    if (TextUtil.isEmpty(qrcodeUrl)) {
      return const SizedBox.shrink();
    }
    
    return Transform.translate(
        offset: Offset(-scaledRdp(22), 0), // 向左平移 20px
        child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(scaledRdp(4)), // 圆角半径
              border: Border.all(
                color: hexToColor('#FFEB6B'), // 边框颜色
                width: scaledRdp(1.5), // 边框宽度
                style: BorderStyle.solid, // 边框样式（实线）
              ),
            ),
            width: scaledRdp(60),
            height: scaledRdp(60),
            child: Container(
              margin: EdgeInsets.all(scaledRdp(3)),
              child:QrImage(
                  padding: const EdgeInsets.all(0),
                  data: widget.qrcodeUrl)
                )
            )
          );
  }

  Widget _nameInfo() {
    return Transform.translate(
        offset: Offset(-scaledRdp(12), 0), // 向左平移 20px
        child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${S.of(context).iAm}${widget.userName}',
            style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: scaledRdp(14),
                fontFamily: 'PingFang SC',
                color: hexToColor('#404040')),
          ),
          SizedBox(height: scaledRdp(2)),
          Text(
            S.of(context).scanLearnWithMe,
            style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: scaledRdp(12),
                fontFamily: 'PingFang SC',
                color: hexToColor('#404040')),
          ),
          SizedBox(height: scaledRdp(10)),
        ]));
  }

  Widget _defaultShareCardBg() {
    return ImageAssetWeb(
      assetName:
          AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_CONTINUE_LEARN_SHARE_BG,
      package: Config.package,
    );
  }

  Widget _buildRolePositioned() {
    return ImageNetworkCached(
      // bgColor: Colors.green,
      imageUrl: widget.roleUrl,
      fit: BoxFit.contain,
      width: scaledRdp(80),
      height: scaledRdp(96),
      fadeInDuration: const Duration(milliseconds: 0),
      fadeOutDuration: const Duration(milliseconds: 0),
      errorWidget: const SizedBox(),
      placeholderWidget: const SizedBox(),
    );
  }

  void savePng(SavePngCallback savePngCallback) async {
    if (!mounted) {
      _log('savePng not mounted');
      return;
    }

    JoJoLoading.show(msg: S.of(context).pictureGenerating);

    final renderObject = _repaintKey.currentContext?.findRenderObject();
    if (renderObject == null) {
      JoJoLoading.dismiss();
      _log('savePng renderObject is null');
      return;
    }

    final boundary = renderObject as RenderRepaintBoundary;

    try {
      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ImageByteFormat.png);
      if (byteData == null) {
        JoJoLoading.dismiss();
        _log('savePng byteData is null');
        return;
      }

      final pngBytes = byteData.buffer.asUint8List();
      final base64Img = base64Encode(pngBytes);

      JoJoLoading.dismiss();
      savePngCallback.call(base64Img);
    } catch (e) {
      JoJoLoading.dismiss();
      _log('savePng error: $e');
    }
  }

  _log(String msg) {
    l.e(_tag, msg);
  }
}
