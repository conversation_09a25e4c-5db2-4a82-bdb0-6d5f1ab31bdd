import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/method_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/constants.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/delay_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_types.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/local_spine_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/when_complete_manager.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

const Map<SpineType, String> _typeToAtlasMap = {
  SpineType.jojo: AssetsSpine.SPINE_FINISH_COURSE_JOJO_PROGRESS_ATLAS,
  SpineType.lvdou: AssetsSpine.SPINE_FINISH_COURSE_LVDOU_PROGRESS_ATLAS,
};

const Map<SpineType, String> _typeToSkelMap = {
  SpineType.jojo: AssetsSpine.SPINE_FINISH_COURSE_JOJO_PROGRESS_SKEL,
  SpineType.lvdou: AssetsSpine.SPINE_FINISH_COURSE_LVDOU_PROGRESS_SKEL,
};

class MilestoneProgressView extends ScaleWidget {
  final String initSpineName;
  final String? tipPath;
  final SpineType spineType;

  const MilestoneProgressView({
    required this.initSpineName,
    required this.spineType,
    required this.tipPath,
    super.key,
    super.scale = phoneScale,
  });

  @override
  State<MilestoneProgressView> createState() => _MilestoneProgressViewState();
}

class _MilestoneProgressViewState
    extends ScaleWidgetState<MilestoneProgressView>
    with TickerProviderStateMixin, PageStatusProvider {
  final Map<AnimationController, double> shouldResumeForwardAnimMap = {};
  final Map<AnimationController, double> shouldLoopForwardAnimMap = {};

  final WhenCompleteManager _whenCompleteManager = WhenCompleteManager();

  final DelayManager _delayManager = DelayManager();

  late AnimationController _tipShowController;
  late AnimationController _tipLoopController;

  final Map<String, String> _nextMap = {};

  putInWhenCompleteMap(
      AnimationController controller, WhenCompleteHandler handler) {
    _whenCompleteManager.putInWhenCompleteMap(controller, handler);
  }

  WhenCompleteHandler? getFromWhenCompleteMap(AnimationController controller) {
    return _whenCompleteManager.getFromWhenCompleteMap(controller);
  }

  putInDelayMap(dynamic key, int delayMilliseconds, WhenCompleteHandler handler) {
    _delayManager.putInDelayMap(key, delayMilliseconds, handler);
  }

  WhenCompleteHandler? getFromDelayMap(dynamic key) {
    final whenCompleteHandler = _delayManager.getFromDelayMap(key);
    if (!mounted) {
      return null;
    }
    return whenCompleteHandler;
  }

  @override
  handlePause(PagePauseEvent event) {
    _whenCompleteManager.handlePause();
    _delayManager.handlePause();

    shouldResumeForwardAnimMap.clear();
    pauseAndSaveController(_tipShowController, shouldResumeForwardAnimMap);
    pauseAndSaveController(_tipLoopController, shouldResumeForwardAnimMap);
  }

  @override
  handleResume(PageResumeEvent event) {
    _whenCompleteManager.handleResume();
    _delayManager.handleResume();

    shouldResumeForwardAnimMap.forEach((key, value) {
      if (key == _tipLoopController) {
        key.repeat(reverse: true);
      } else {
        key.forward(from: key.value).whenComplete(() {
          getFromWhenCompleteMap(key)?.call();
        });
      }
    });
    shouldResumeForwardAnimMap.clear();
  }

  @override
  void didUpdateWidget(covariant MilestoneProgressView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tipPath != widget.tipPath &&
        TextUtil.isNotEmpty(widget.tipPath)) {
      _delayShowTip();
    }
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();
    _initMap();

    _tipShowController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _tipLoopController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
  }

  @override
  void dispose() {
    _tipShowController.dispose();
    _tipLoopController.dispose();
    disposePageStatus();
    super.dispose();
  }

  _initMap() {
    _nextMap[MilestoneProgressSpineNames.toStageOne.name] =
        MilestoneProgressSpineNames.stageOneLoop.name;
    _nextMap[MilestoneProgressSpineNames.toStageTwo.name] =
        MilestoneProgressSpineNames.stageTwoLoop.name;
    _nextMap[MilestoneProgressSpineNames.toStageThree.name] =
        MilestoneProgressSpineNames.stageThreeLoop.name;
    _nextMap[MilestoneProgressSpineNames.toStageFour.name] =
        MilestoneProgressSpineNames.stageFourLoop.name;
    _nextMap[MilestoneProgressSpineNames.toStageFive.name] =
        MilestoneProgressSpineNames.stageFiveLoop.name;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: scaledRdp(366.25),
      height: scaledRdp(85),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fill(
            child: LocalSpineView(
              initSpineName: widget.initSpineName,
              atlasPath: _typeToAtlasMap[widget.spineType] ??
                  AssetsSpine.SPINE_FINISH_COURSE_JOJO_PROGRESS_ATLAS,
              skelPath: _typeToSkelMap[widget.spineType] ??
                  AssetsSpine.SPINE_FINISH_COURSE_JOJO_PROGRESS_SKEL,
              spineShouldLoopCallback: _spineShouldLoop,
              spineNeedListenerCallback: _spineNeedListener,
              animationFinishCallback: _animationFinish,
              customNextSpineNameHandler: _customNextSpineName,
              fit: BoxFit.cover,
              width: scaledRdp(366.25),
              height: scaledRdp(85),
            ),
          ),
          if (_canShowTip())
            Positioned(
                right: _tipRight(), top: _tipTop(), child: _buildTipView()),
        ],
      ),
    );
  }

  _delayShowTip() {
    putInDelayMap(DelayKeys.milestoneEndTipDelay, endPageButtonShowDelayDuration.inMilliseconds, () {
      _appearTip();
    });
    Future.delayed(endPageButtonShowDelayDuration, () {
      getFromDelayMap(DelayKeys.milestoneEndTipDelay)?.call();
    });
  }

  _appearTip() {
    putInWhenCompleteMap(_tipShowController, () {
      _loopTip();
    });
    _tipShowController.forward(from: 0.0).whenComplete(() {
      getFromWhenCompleteMap(_tipShowController)?.call();
    });
  }

  _loopTip() {
    _tipLoopController.repeat(reverse: true);
  }

  double _tipRight() {
    final distance = scaledRdp(40);
    return distance - _tipWidth();
  }

  double _tipTop() {
    final distance = scaledRdp(18);
    return distance - _tipHeight();
  }

  double _tipWidth() {
    return scaledRdp(36);
  }

  double _tipHeight() {
    return scaledRdp(36);
  }

  double _imgWidth() {
    return scaledRdp(24);
  }

  double _imgHeight() {
    return scaledRdp(24);
  }

  Widget _buildTipView() {
    return _buildShowScale();
  }

  Widget _buildShowScale() {
    const beginScale = 0.0;
    const endScale = 1.1;
    return ScaleTransition(
        alignment: const Alignment(-0.5, 1.0),
        scale: TweenSequence<double>(
          [
            TweenSequenceItem(
              tween: Tween(begin: beginScale, end: endScale)
                  .chain(CurveTween(curve: Curves.easeOut)),
              weight: 1,
            ),
          ],
        ).animate(_tipShowController),
        child: _buildLoopScale());
  }

  Widget _buildLoopScale() {
    const beginScale = 1.1;
    const endScale = 1.0;
    return ScaleTransition(
        alignment: const Alignment(-0.5, 1.0),
        scale: TweenSequence<double>(
          [
            TweenSequenceItem(
              tween: Tween(begin: beginScale, end: endScale)
                  .chain(CurveTween(curve: Curves.linear)),
              weight: 1,
            ),
          ],
        ).animate(_tipLoopController),
        child: SizedBox(
          width: _tipWidth(),
          height: _tipHeight(),
          child: Stack(
            children: [
              Positioned.fill(child: Center(child: _buildBackgroundImage())),
              Positioned.fill(child: Center(child: _buildImage())),
            ],
          ),
        ));
  }
  
  Widget _buildBackgroundImage() {
    return ImageAssetWeb(
        width: _tipWidth(),
        height: _tipHeight(),
        assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_BUBBLE,
        package: Config.package,
        fit: BoxFit.fill,
    );
  }

  Widget _buildImage() {
    return ImageAssetWeb(
        width: _imgWidth(),
        height: _imgHeight(),
        assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_BUBBLE_ICON,
        package: Config.package,
        fit: BoxFit.fill,
    );
  }

  bool _canShowTip() {
    return TextUtil.isNotEmpty(widget.tipPath);
  }

  bool _spineShouldLoop(String curName) {
    return curName.toLowerCase().contains("loop");
  }

  bool _spineNeedListener(String curName) {
    return !curName.toLowerCase().contains("loop");
  }

  bool _animationFinish(String curName) => false;

  String? _customNextSpineName(String curName) {
    return _nextMap[curName];
  }
}
