import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/animation.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/share/normal_share_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/share/share_area_view.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/utils/num_to_image.dart';

showNormalShareDialog({
  required BuildContext context,
  required double scale,
  required Color subjectColor,
  required String userName,
  required String avatarUrl,
  required String avatarFrameUrl,
  required String qrcodeUrl,
  required int dayCount,
  required String roleUrl,
  required String subjectName,
  required String shareCardBgUrl,
  required String shareDesc,
  required String backgroundText,
  VoidCallback? onShow,
  ShareTapCallback? onShareClick,
}) {
  SmartDialog.show(
    maskColor: Colors.transparent,
    backDismiss: true,
    clickMaskDismiss: false,
    animationBuilder: (animController, child, animationParam) =>
        buildSlideWidget(
            begin: const Offset(0, 1),
            end: Offset.zero,
            animationController: animController,
            child: child),
    animationTime: const Duration(milliseconds: 300),
    onDismiss: () {},
    builder: (_) {
      return NormalShareView(
        scale: scale,
        userName: userName,
        avatarUrl: avatarUrl,
        avatarFrameUrl: avatarFrameUrl,
        qrcodeUrl: qrcodeUrl,
        dayCount: dayCount,
        roleUrl: roleUrl,
        subjectName: subjectName,
        shareCardBgUrl: shareCardBgUrl,
        subjectColor: subjectColor,
        shareDesc: shareDesc,
        backgroundText : backgroundText,
        backCallback: () => SmartDialog.dismiss(),
        onShow: onShow,
        onShareClick: onShareClick,
      );
    },
  );
}

Widget buildAppearWidget({
  required AnimationController animationController,
  required Widget child,
  Curve? curve,
}) {
  return FadeTransition(
      opacity: CurvedAnimation(
          parent: ClampedAnimation(
            parent: Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(animationController),
          ),
          curve: curve ?? Curves.easeIn),
      child: child);
}

Widget buildDisappearWidget({
  required AnimationController animationController,
  required Widget child,
  Curve? curve,
}) {
  return FadeTransition(
      opacity: CurvedAnimation(
        parent: ClampedAnimation(
          parent: Tween<double>(
            begin: 1.0,
            end: 0.0,
          ).animate(animationController),
        ),
        curve: curve ?? Curves.easeInOut,
      ),
      child: child);
}

Widget buildSlideWidget({
  required Offset begin,
  required Offset end,
  required AnimationController animationController,
  required Widget child,
  Curve? curve,
}) {
  return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: end,
      ).animate(
        CurvedAnimation(
            parent: ClampedAnimation(parent: animationController),
            curve: curve ?? Curves.easeInOut),
      ),
      child: child);
}

Widget buildNumberImagesNoAnim(int dayCount, double numberViewHeight) {
  final List<Widget> rowChildren = [];
  rowChildren.addAll(buildNumberImages(
    dayCount,
    numberViewHeight,
    9999,
    isNumberWidthEqual: true,
    customNumberMap: continueLearnMap
  ));

  rowChildren.add(ImageAssetWeb(
    assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_DAY_TEXT,
    height: numberViewHeight,
    package: Config.package,
    fit: BoxFit.contain,
  ));

  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: rowChildren,
  );
}

Widget buildNumberImagesAppear(int dayCount, double numberViewHeight,
    AnimationController animationController,
    {bool showDay = false, AnimationController? dayAnimationController}) {
  final List<Widget> rowChildren = [];
  rowChildren.addAll(buildNumberImages(
    dayCount,
    numberViewHeight,
    9999,
    isNumberWidthEqual: true,
    customNumberMap: continueLearnMap
  ));
  if (showDay && dayAnimationController != null) {
    rowChildren.add(_buildDayText(numberViewHeight, dayAnimationController));
  }
  return buildAppearWidget(
    animationController: animationController,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: rowChildren,
    ),
  );
}

Widget buildNumberImagesDisappear(
  int dayCount,
  double numberViewHeight,
  AnimationController animationController,
) {
  return buildDisappearWidget(
    animationController: animationController,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: buildNumberImages(
        dayCount,
        numberViewHeight,
        9999,
        isNumberWidthEqual: true,
        customNumberMap: continueLearnMap
      ),
    ),
  );
}

Widget buildSmallText(BuildContext context, String text,
    {Color? textColor,
    double scale = 1.0,
    FontWeight fontWeight = FontWeight.w400}) {
  final realTextColor = textColor ?? context.appColors.jColorOrange6;
  return Text(
    text,
    style: TextStyle(
      fontWeight: fontWeight,
      fontSize: 14.rdp * scale,
      color: realTextColor,
      height: 1.5,
    ),
  );
}

Widget buildBigText(BuildContext context, String text,
    {Color? textColor,
    TextAlign? textAlign,
    double scale = 1.0,
    FontWeight fontWeight = FontWeight.w500}) {
  final realTextColor = textColor ?? context.appColors.jColorOrange6;
  return Text(
    text,
    textAlign: textAlign,
    style: TextStyle(
      fontWeight: fontWeight,
      fontSize: 24.rdp * scale,
      color: realTextColor,
      height: 1.5,
    ),
  );
}

Widget buildShareButton(
  BuildContext context,
  String buttonText,
  String shareIconPath, {
  double width = 0,
  double height = 0,
  Color? backgroundColor,
  Color? textColor,
  Color? borderColor,
  double scale = 1.0,
}) {
  final realWidth = width > 0 ? width : 232.rdp * scale;
  final realHeight = height > 0 ? height : 44.rdp * scale;
  final realBgColor = backgroundColor ?? context.appColors.jColorYellow4;
  final realTextColor = textColor ?? context.appColors.jColorYellow6;
  return Container(
    width: realWidth,
    height: realHeight,
    decoration: BoxDecoration(
      color: realBgColor,
      border: borderColor == null
          ? null
          : Border.all(color: borderColor, width: 1.rdp * scale),
      borderRadius: BorderRadius.all(Radius.circular(30.rdp * scale)),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ImageAssetWeb(
          assetName: shareIconPath,
          width: 24.rdp * scale,
          height: 24.rdp * scale,
          package: Config.package,
          fit: BoxFit.contain,
        ),
        SizedBox(width: 4.rdp * scale),
        Text(
          buttonText,
          style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 18.rdp * scale,
              color: realTextColor),
        ),
      ],
    ),
  );
}

Widget buildButton(
  BuildContext context,
  String buttonText, {
  double width = 0,
  double height = 0,
  Color? backgroundColor,
  Color? textColor,
  Color? borderColor,
  double scale = 1.0,
}) {
  final realWidth = width > 0 ? width : 132.rdp * scale;
  final realHeight = height > 0 ? height : 44.rdp * scale;
  final realBgColor = backgroundColor ?? context.appColors.jColorYellow4;
  final realTextColor = textColor ?? context.appColors.jColorYellow6;
  return Container(
    width: realWidth,
    height: realHeight,
    decoration: BoxDecoration(
      color: realBgColor,
      border: borderColor == null
          ? null
          : Border.all(color: borderColor, width: 1.rdp * scale),
      borderRadius: BorderRadius.all(Radius.circular(30.rdp * scale)),
    ),
    child: Center(
      child: Text(
        buttonText,
        style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 18.rdp * scale,
            color: realTextColor),
      ),
    ),
  );
}

Widget _buildDayText(
  double numberViewHeight,
  AnimationController animationController,
) {
  return buildAppearWidget(
    animationController: animationController,
    child: ImageAssetWeb(
      assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_DAY_TEXT,
      height: numberViewHeight,
      package: Config.package,
      fit: BoxFit.contain,
    ),
  );
}
