import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/method_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/box_open_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_types.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/local_spine_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/when_complete_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/model/local_product_data.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

typedef ProductOpenCallback = BoxOpenCallback;

class ProductOpenView extends ScaleWidget {
  final String initSpineName;
  final LocalProductData localProductData;
  final String? dividerText;
  final TapCallback? tapCallback;
  final ProductOpenCallback productOpenCallback;
  final bool isFirstProduct;
  final Color subjectColor;

  const ProductOpenView({
    required this.initSpineName,
    required this.localProductData,
    required this.dividerText,
    required this.productOpenCallback,
    required this.isFirstProduct,
    required this.subjectColor,
    this.tapCallback,
    super.key,
    super.scale = phoneScale,
  });

  @override
  State<ProductOpenView> createState() => _ProductOpenViewState();
}

class _ProductOpenViewState extends ScaleWidgetState<ProductOpenView>
    with TickerProviderStateMixin, PageStatusProvider {
  final Map<AnimationController, double> shouldResumeForwardAnimMap = {};

  final WhenCompleteManager _whenCompleteManager = WhenCompleteManager();

  late AnimationController productOpenController;

  UniqueKey _backgroundSpineKey = UniqueKey();

  putInWhenCompleteMap(
      AnimationController controller, WhenCompleteHandler handler) {
    _whenCompleteManager.putInWhenCompleteMap(controller, handler);
  }

  WhenCompleteHandler? getFromWhenCompleteMap(AnimationController controller) {
    return _whenCompleteManager.getFromWhenCompleteMap(controller);
  }

  @override
  handlePause(PagePauseEvent event) {
    _whenCompleteManager.handlePause();

    shouldResumeForwardAnimMap.clear();
    pauseAndSaveController(productOpenController, shouldResumeForwardAnimMap);
  }

  @override
  handleResume(PageResumeEvent event) {
    _whenCompleteManager.handleResume();

    shouldResumeForwardAnimMap.forEach((key, value) {
      key.forward(from: key.value).whenComplete(() {
        getFromWhenCompleteMap(key)?.call();
      });
    });
    shouldResumeForwardAnimMap.clear();
  }

  @override
  void didUpdateWidget(covariant ProductOpenView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.localProductData != widget.localProductData) {
      if (mounted) {
        _productEnter();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();
    productOpenController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _productEnter();
    });
  }

  _productEnter() {
    _backgroundSpineKey = UniqueKey();
    widget.productOpenCallback(typeProductOpen);
    productOpenController.value = 0;
    productOpenController.forward(from: 0);
  }

  @override
  void dispose() {
    disposePageStatus();
    productOpenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final clickAndContinueText = TextUtil.isEmpty(widget.dividerText)
        ? S.of(context).clickAndContinue
        : "${S.of(context).clickAndContinue}（${widget.dividerText}）";
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _tap,
      child: Stack(
        children: [
          if (!widget.isFirstProduct)
            Positioned.fill(
                child: Center(
              child: LocalSpineView(
                key: _backgroundSpineKey,
                initSpineName: widget.initSpineName,
                atlasPath:
                    AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_ATLAS,
                skelPath:
                    AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_PRIZE_BG_SKEL,
                spineShouldLoopCallback: _spineShouldLoop,
                spineNeedListenerCallback: _spineNeedListener,
                animationFinishCallback: _animationFinish,
                customNextSpineNameHandler: _customNextSpineName,
                scale: backgroundScale(widget.scale),
              ),
            )),
          Positioned.fill(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildAppear(
                  child: buildBigText(
                      context, widget.localProductData.name ?? "",
                      scale: widget.scale, textColor: context.appColors.colorVariant6(widget.subjectColor))),
              SizedBox(
                  height: widget.scale == sPadScale ? 120.rdp : scaledRdp(10)),
              _buildScaledImage(),
              SizedBox(
                  height: widget.scale == sPadScale ? 120.rdp : scaledRdp(12)),
              _buildAppear(
                  child: buildSmallText(
                      context, widget.localProductData.desc ?? "",
                      scale: widget.scale, textColor: context.appColors.colorVariant6(widget.subjectColor))),
              SizedBox(
                  height: widget.scale == sPadScale ? 44.rdp : scaledRdp(34)),
              _buildAppear(
                  child: buildSmallText(context, clickAndContinueText,
                      scale: widget.scale, textColor: context.appColors.colorVariant5(widget.subjectColor), fontWeight: FontWeight.w600)),
            ],
          )),
        ],
      ),
    );
  }

  Widget _buildAppear({required Widget child}) {
    return FadeTransition(
        opacity: TweenSequence<double>(
          [
            TweenSequenceItem(
              tween: Tween(begin: 0.0, end: 0.0)
                  .chain(CurveTween(curve: Curves.easeOut)),
              weight: 2,
            ),
            TweenSequenceItem(
              tween: Tween(begin: 0.0, end: 1.0)
                  .chain(CurveTween(curve: Curves.easeIn)),
              weight: 2,
            ),
          ],
        ).animate(productOpenController),
        child: child);
  }

  Widget _buildScaledImage() {
    const beginScale = 0.0;
    const middleScale = 1.2;
    const endScale = 1.0;
    return ScaleTransition(
        scale: TweenSequence<double>(
          [
            TweenSequenceItem(
              tween: Tween(begin: beginScale, end: middleScale)
                  .chain(CurveTween(curve: Curves.easeOut)),
              weight: 2,
            ),
            TweenSequenceItem(
              tween: Tween(begin: middleScale, end: endScale)
                  .chain(CurveTween(curve: Curves.easeIn)),
              weight: 2,
            ),
          ],
        ).animate(productOpenController),
        child: _buildImage());
  }

  Widget _buildImage() {
    if (TextUtil.isEmpty(widget.localProductData.localImgPath)) {
      return CachedNetworkImage(
        imageUrl: widget.localProductData.imgUrl ?? "",
        width: _imageScaledRdp(150),
        height: _imageScaledRdp(150),
        fit: BoxFit.fill,
        fadeInDuration: const Duration(milliseconds: 0),
        fadeOutDuration: const Duration(milliseconds: 0),
      );
    }
    return Image.file(
      File(widget.localProductData.localImgPath ?? ''),
      width: _imageScaledRdp(150),
      height: _imageScaledRdp(150),
      fit: BoxFit.fill,
    );
  }

  bool _spineShouldLoop(String curName) => false;

  bool _spineNeedListener(String curName) => true;

  bool _animationFinish(String curName) => true;

  String? _customNextSpineName(String curName) => null;

  _tap() {
    if (productOpenController.isAnimating) {
      return;
    }

    widget.tapCallback?.call(TapType.openNextProduct,null);
  }

  double _imageScaledRdp(double value) {
    if (widget.scale == sPadScale) {
      return (value * 1.5).rdp;
    }
    return value.rdp;
  }
}
