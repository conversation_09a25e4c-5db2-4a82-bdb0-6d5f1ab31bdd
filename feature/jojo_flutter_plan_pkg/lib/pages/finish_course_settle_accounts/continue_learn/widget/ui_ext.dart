import 'package:flutter/material.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';

Widget buildOverSize({double? customWidth, double? customHeight, Widget? child}) {
  return Container(
    clipBehavior: Clip.none,
    alignment: Alignment.center,
    width: customWidth,
    height: customHeight,
    child: OverflowBox(
      alignment: Alignment.center,
      maxWidth: double.infinity,
      maxHeight: double.infinity,
      child: child,
    ),
  );
}

Widget overflow({required Widget child}) => OverflowBox(
  alignment: Alignment.center,
  maxWidth: double.infinity,
  maxHeight: double.infinity,
  child: child,
);

const backgroundScaleForPhone = 1.5;
const backgroundScaleForSPad = 2.5;

final _scaleConvertMap = {
  phoneScale: backgroundScaleForPhone,
  sPadScale: backgroundScaleForSPad,
};

double backgroundScale(double scale) {
  return _scaleConvertMap[scale] ?? 1.0;
}
