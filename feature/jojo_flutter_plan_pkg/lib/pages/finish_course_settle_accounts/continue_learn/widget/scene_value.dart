
import 'package:collection/collection.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/model/finish_course_settle_accounts_data.dart';

enum Scene {
  milestoneEnd, // 里程碑结束
  itemPurchaseTimeoutAudio,
  itemPurchaseFailedAudio,
  itemUseFailedAudio,
}

extension SceneExt on List<SceneValue> {
  String? getSceneValue(Scene scene) {
    return firstWhereOrNull((element) => element.scene == scene.name)?.value;
  }
}