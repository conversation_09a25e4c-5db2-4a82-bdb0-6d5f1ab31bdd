import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import '../../../../generated/l10n.dart';

class FinishCourseDialogBody extends StatelessWidget {
  final String text;

  const FinishCourseDialogBody({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      textAlign: TextAlign.center,
      style: context.textstyles.bodyTextEmphasis.pf.copyWith(
        color: context.appColors.textColor,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

class FinishCousreDialogFooter extends StatelessWidget {
  final void Function()? onCancel;
  final void Function()? onOk;
  final String? okText;
  final String? cancelText;

  const FinishCousreDialogFooter({
    super.key,
    this.onCancel,
    this.onOk,
    this.okText,
    this.cancelText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: OutlinedButton(
            style: _outlinedButtonStyle(context),
            onPressed: () => onCancel?.call(),
            child: Text(
              cancelText ?? '',
              style: context.textstyles.headingEmphasis.pf,
            ),
          ),
        ),
        SizedBox(width: pt(16)),
        Expanded(
          child: ElevatedButton(
            style: _elevatedButtonStyle(context),
            onPressed: () => onOk?.call(),
            child: Text(
              okText ?? '',
              style: context.textstyles.headingEmphasis.pf,
            ),
          ),
        ),
      ],
    );
  }

  ButtonStyle _outlinedButtonStyle(BuildContext context) {
    return OutlinedButton.styleFrom(
      side: BorderSide(
        color: context.appColors.mainColor,
        width: 1,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(pt(30)),
      ),
      foregroundColor: context.appColors.jColorYellow6,
      padding: EdgeInsets.all(pt(8)),
    );
  }

  ButtonStyle _elevatedButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: context.appColors.mainColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(pt(30)),
      ),
      foregroundColor: context.appColors.jColorYellow6,
      padding: EdgeInsets.all(pt(8)),
      elevation: 0,
    );
  }
}