import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/events.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/audio_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/page_status_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/scale.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/task_settle/widget/animations/animation_audio_mixin.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/ext.dart'
    as finishCourse;

typedef SpineShouldLoopCallback = bool Function(String);
typedef SpineNeedListenerCallback = bool Function(String);
typedef AnimationFinishCallback = bool Function(String);
typedef CustomNextSpineNameHandler = String? Function(String);

class LocalSpineView extends ScaleWidget {
  final String initSpineName;
  final String atlasPath;
  final String skelPath;
  final double? width;
  final double? height;
  final SpineShouldLoopCallback spineShouldLoopCallback;
  final SpineNeedListenerCallback spineNeedListenerCallback;
  final AnimationFinishCallback animationFinishCallback;
  final CustomNextSpineNameHandler customNextSpineNameHandler;
  final BoxFit? fit;

  const LocalSpineView({
    super.key,
    required this.initSpineName,
    required this.atlasPath,
    required this.skelPath,
    required this.spineShouldLoopCallback,
    required this.spineNeedListenerCallback,
    required this.animationFinishCallback,
    required this.customNextSpineNameHandler,
    this.width,
    this.height,
    this.fit,
    super.scale = phoneScale,
  });

  @override
  State<LocalSpineView> createState() => _LocalSpineViewState();
}

class _LocalSpineViewState extends ScaleWidgetState<LocalSpineView>
    with PageStatusProvider, AudioProvider, AnimationAudioPlayMixin {
  final spineKey = UniqueKey();

  final _spineController = JoJoSpineAnimationController();

  bool abortSpineListener = false;

  bool _shouldContinueSpinePlay = false;

  late String _currentSpineName;

  @override
  handleResume(PageResumeEvent event) {
    if (_shouldContinueSpinePlay) {
      _spineController.resumeAnimation();
    }
    audioResume();
  }

  @override
  handlePause(PagePauseEvent event) {
    _shouldContinueSpinePlay = _spineController.isAnimationPlaying();
    _spineController.pauseAnimation();
    audioPause();
  }

  @override
  void didUpdateWidget(covariant LocalSpineView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initSpineName != widget.initSpineName) {
      abortSpineListener = false;
      if (_spineController.isAnimationPlaying()) {
        _spineController.pauseAnimation();
      }
      _initStatus();
      _play();
    }
  }

  @override
  void initState() {
    super.initState();
    initPageStatus();
    abortSpineListener = false;
    _initStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: JoJoSpineAnimationWidget(
          widget.atlasPath,
          widget.skelPath,
          LoadMode.assets,
          _spineController,
          fit: widget.fit ?? BoxFit.none,
          useRootBoneAlign: true,
          package: Config.package,
          key: spineKey,
          onInitialized: (controller) {
            if (mounted) {
              // 设置缩放比例
              controller.skeleton.setScaleX(widget.scale);
              controller.skeleton.setScaleY(widget.scale);
            }
            try {
              _play();
            } catch (e) {
              l.w(finishCourse.tag, "播放失败");
            }
          },
        ),
      ),
    );
  }

  _play() {
    _spineController.playAnimation(
      JoJoSpineAnimation(
          animaitonName: _currentSpineName,
          trackIndex: 0,
          loop: widget.spineShouldLoopCallback(_currentSpineName),
          delay: 0,

          /// dieLoop循环太快了设置回调可能引起 didUpdateWidget 改变 _currentSpineNames，
          /// 马上触发 _initNextStatus切到下一个动画节点，当前的场景dieLoop无需回调
          listener: widget.spineNeedListenerCallback(_currentSpineName)
              ? _animationEvent
              : null),
    );

    if (!_spineController.isAnimationPlaying()) {
      _spineController.resumeAnimation();
    }
  }

  _animationEvent(AnimationEventType type) {
    if (abortSpineListener) {
      return;
    }
    if (AnimationEventType.complete == type) {
      abortSpineListener =
          widget.animationFinishCallback.call(_currentSpineName) ?? false;
      if (!abortSpineListener) {
        _initNextStatus();
      }
    }
  }

  _initStatus() {
    _currentSpineName = widget.initSpineName;
  }

  _initNextStatus() {
    _setNextAnimationName();
    _play();
  }

  _setNextAnimationName() {
    final newSpineNames =
        widget.customNextSpineNameHandler.call(_currentSpineName);
    if (newSpineNames != null) {
      _currentSpineName = newSpineNames;
    }
  }
}
