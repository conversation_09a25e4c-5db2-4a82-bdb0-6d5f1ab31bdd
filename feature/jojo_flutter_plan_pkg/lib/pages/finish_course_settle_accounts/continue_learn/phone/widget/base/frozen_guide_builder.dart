import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/frozen_calendar_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';

import '../../../buried_ext.dart';

/// 后续引导 - 冰冻
extension FrozenGuideBuilder<T extends BaseFromView> on BaseFromState<T> {

  List<Widget> buildListOfWidgetFinalWidgetTypeIce() {
    if (widget.state.isDayChange()) {
      return _buildListOfWidgetFinalWidgetTypeTwo();
    }
    return _buildListOfWidgetFinalWidgetTypeTwoInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeTwoInterrupt() {
    return widget.state.finalStatusPageStage == 0
        ? buildListOfWidgetFinalWidgetTypeXXXInterruptFirst()
        : _buildListOfWidgetFinalWidgetTypeTwoSecondInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeTwoSecondInterrupt() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildWidgetFinalWidgetTypeTwoSlideAreaInterrupt(),
            SizedBox(height: 8.rdp),
            if (showFinalButtons)
              _buildWidgetFinalWidgetTypeTwoBottomButtonArea()
            else
              SizedBox(height: 44.rdp),
          ],
        ),
      )
    ];
  }

  Widget _buildWidgetFinalWidgetTypeTwoSlideAreaInterrupt() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // buildSpineAreaOverSize(282.rdp, customWidth: 97.rdp, customHeight: 80.rdp),
          SizedBox(height: 8.rdp),
          buildBigText(context, S.of(context).continueIceDayNotChangeTip),
          _buildWidgetFinalWidgetTypeTwoCenterArea(),
        ],
      ),
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeTwo() {
    return widget.state.finalStatusPageStage == 0
        ? buildListOfWidgetFinalWidgetTypeXXXFirst()
        : _buildListOfWidgetFinalWidgetTypeTwoSecond();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeTwoSecond() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildWidgetFinalWidgetTypeTwoSlideArea(),
            SizedBox(height: 8.rdp),
            if (showFinalButtons)
              _buildWidgetFinalWidgetTypeTwoBottomButtonArea()
            else
              SizedBox(height: 44.rdp),
          ],
        ),
      )
    ];
  }

  Widget _buildWidgetFinalWidgetTypeTwoSlideArea() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildWidgetFinalWidgetTypeXXXTopArea(),
          SizedBox(height: 8.rdp),
          buildBigText(context, S.of(context).continuousPersistence),
          _buildWidgetFinalWidgetTypeTwoCenterArea(),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeTwoCenterArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 8.rdp),
        FrozenCalendarView(
          statusList: widget.state.frozenStatusList,
          spineType: widget.state.spineType,
        ),
        SizedBox(height: 4.rdp),
        buildSmallText(context, widget.state.finalSmallTip ?? ""),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeTwoBottomButtonArea() {
    final btn = widget.state.btn;
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: _buildButtons(),
        ),
      ),
    );
  }

  List<Widget> _buildButtons() {
    final List<Widget> btnList = [];
    final btn = widget.state.btn;
    if (btn == null) {
      btnList.addAll([
        GestureDetector(
            onTap: () => widget.tapCallback?.call(TapType.iKnow,S.of(context).iKnow),
            child: buildButton(context, S.of(context).iKnow, width: 280.rdp)),
      ]);
    } else {
      btnList.addAll([
        GestureDetector(
          onTap: () => widget.tapCallback?.call(TapType.forceIgnore,S.of(context).forceIgnore),
          child:  VisibilityObserve(
            onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: S.of(context).forceIgnore),
            child:buildButton(context, S.of(context).forceIgnore,
              backgroundColor: Colors.white,
              borderColor: context.appColors.jColorYellow4),
          ),
        ),
        SizedBox(width: 20.rdp),
        GestureDetector(
            onTap: () => widget.tapCallback?.call(TapType.relearnForAssistance,btn.btnName ?? S.of(context).relearnForAssistance),
            child:  VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btn.btnName ?? S.of(context).relearnForAssistance),
                child:buildButton(
                context, btn.btnName ?? S.of(context).relearnForAssistance)),
        ),
      ]);
    }
    return btnList;
  }
}