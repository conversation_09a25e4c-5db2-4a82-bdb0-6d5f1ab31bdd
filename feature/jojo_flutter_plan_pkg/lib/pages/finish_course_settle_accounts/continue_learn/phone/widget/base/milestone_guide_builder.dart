
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/buried_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
/// 后续引导 - 里程碑
extension MilestoneGuideBuilder<T extends BaseFromView> on BaseFromState<T> {

  List<Widget> buildListOfWidgetFinalWidgetTypeMilestone() {
    final size = spineJoJoSize.rdp;
    return [
      Positioned.fill(
          child: VisibilityObserve(
            onShow: () => buriedContinueLearnAppViewScreen(widget.state, customScreenName: "课程结束_里程碑宝箱结算页面"),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildListOfWidgetFinalWidgetTypeMilestoneVerticalSlideArea(size),
                _buildListOfWidgetFinalWidgetTypeMilestoneBottomAppearArea(),
              ],
            ),
          )),
    ];
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneVerticalSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp),
            _buildListOfWidgetFinalWidgetTypeMilestoneTopArea(size),
          ],
        ));
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneTopArea(double size) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildListOfWidgetFinalWidgetTypeMilestoneSpineArea(size),
        SizedBox(width: 20.rdp),
        Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildNumberImagesNoAnim(
                  widget.state.dayCount, widget.state.numberHeight().rdp),
            ]),
      ],
    );
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: 130.rdp,
      height: widget.state.numberHeight().rdp,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildListOfWidgetFinalWidgetTypeMilestoneBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 28.rdp),
          buildBigText(context, S.of(context).continuousPersistence),
          SizedBox(height: 8.rdp),
          if (TextUtil.isEmpty(widget.state.finalSmallTip))
            SizedBox(height: 64.rdp)
          else
            Container(
              height: 64.rdp,
              padding: EdgeInsets.symmetric(horizontal: 14.rdp),
              decoration: BoxDecoration(
                border: Border.all(color: context.appColors.jColorGray2, width: 1.rdp),
                borderRadius: BorderRadius.all(Radius.circular(24.rdp)),
                color: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  buildSmallText(context, widget.state.finalSmallTip ?? ""),
                  RotationTransition(
                    // 添加旋转动画组件
                    turns: shakeAnimation,
                    alignment: const Alignment(0.0, 0.5),
                    child: ImageAssetWeb(
                      assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_SMALL_BOX,
                      package: Config.package,
                      width: 48.rdp,
                      height: 48.rdp,
                    ),
                  )
                ],
              ),
            ),
        ],
      ),
    );
  }
}