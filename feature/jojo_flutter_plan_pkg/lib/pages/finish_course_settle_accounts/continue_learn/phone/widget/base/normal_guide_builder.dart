import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';

import '../../../buried_ext.dart';

/// 后续引导 - 普通
extension NormalGuideBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildListOfWidgetFinalWidgetTypeNormal() {
    if (widget.state.isDayChange()) {
      return _buildListOfWidgetFinalWidgetTypeOne();
    }
    return _buildListOfWidgetFinalWidgetTypeOneInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeOneInterrupt() {
    final size = spineJoJoSize.rdp;
    return [
      Positioned.fill(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneInterruptTopSlideArea(size),
          _buildListOfWidgetFinalWidgetTypeOneInterruptBottomAppearArea(),
        ],
      )),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeOneInterruptTopSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp),
            buildSpineAreaOverSize(size),
          ],
        ));
  }

  Widget _buildListOfWidgetFinalWidgetTypeOneInterruptBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildListOfWidgetFinalWidgetTypeOneInterruptBottomHorizontalSlideArea(),
          if (showFinalButtons)
            _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea(
                topMargin: 10.rdp)
          else
            SizedBox(height: 54.rdp),
        ],
      ),
    );
  }

  Widget
      _buildListOfWidgetFinalWidgetTypeOneInterruptBottomHorizontalSlideArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 12.rdp),
        buildBigText(context, S.of(context).continueNormalDayNotChangeTip,
            textAlign: TextAlign.center),
        buildSmallText(context, widget.state.finalSmallTip ?? ""),
      ],
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeOne() {
    final size = spineJoJoSize.rdp;
    return [
      Positioned.fill(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneTopVerticalSlideArea(size),
          _buildWidgetFinalWidgetTypeOneBottomAppearArea(),
        ],
      )),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeOneTopVerticalSlideArea(double size) {
    return buildSlideWidget(
        begin: Offset(0, 31.rdp / 2 / screen.screenHeight),
        end: Offset.zero,
        animationController: finalStatusTypeOneAnimController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp),
            _buildWidgetFinalWidgetTypeOneTopArea(size),
          ],
        ));
  }

  Widget _buildWidgetFinalWidgetTypeOneTopArea(double size) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildWidgetFinalWidgetTypeOneTopSpineArea(size),
        SizedBox(width: 20.rdp),
        Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              buildNumberImagesNoAnim(
                  widget.state.dayCount, widget.state.numberHeight().rdp),
            ]),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneTopSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: 130.rdp,
      height: widget.state.numberHeight().rdp,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomAppearArea() {
    return buildAppearWidget(
      animationController: finalStatusTypeOneAnimController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeOneBottomHorizontalSlideArea(),
          if (showFinalButtons)
            _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea()
          else
            SizedBox(height: 72.rdp),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomHorizontalSlideArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 12.rdp),
        buildBigText(context, S.of(context).continuousPersistence),
        buildSmallText(context, widget.state.finalSmallTip ?? ""),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeOneBottomOnlyAppearArea(
      {double? topMargin}) {
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: topMargin ?? 28.rdp),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons() {
    final btnText = widget.state.isDayChange()
        ? (widget.state.isFirstComplete()
            ? S.of(context).iWillCome
            : S.of(context).continueMaintain)
        : S.of(context).ok;
    if (widget.state.showShareButton()) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () => widget.tapCallback?.call(TapType.iWillCome,btnText),
            child: VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(
                    state: widget.state, buttonName: btnText),
                child: buildButton(context, btnText,
                    width: 132.rdp,
                    backgroundColor: Colors.white,
                    borderColor: context.appColors.mainColor)),
          ),
          SizedBox(width: 20.rdp),
          GestureDetector(
            onTap: () => widget.tapCallback?.call(TapType.winShare,S.of(context).shareMyHighlightMoment),
            child: VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(
                    state: widget.state, buttonName: S.of(context).shareMyHighlightMoment),
                child: buildShareButton(
                  context,
                  S.of(context).shareMyHighlightMoment,
                  AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_CAMERA,
                )),
          ),
        ],
      );
    } else {
      return GestureDetector(
          onTap: () => widget.tapCallback?.call(TapType.iWillCome,btnText),
          child: VisibilityObserve(
              onShow: () => buriedContinueButtonViewScreen(
                  state: widget.state, buttonName: btnText),
              child: buildButton(context, btnText, width: 280.rdp)));
    }
  }
}
