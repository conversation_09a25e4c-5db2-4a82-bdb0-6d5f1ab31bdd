import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/drainage_course_dialog/model/drainage_course_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/continue_learn_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/revive_button.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
import '../../../../../../static/img.dart';
import '../../../../model/local_can_buy_prop_data.dart';
import '../../../buried_ext.dart';

/// 后续引导 - 嗝屁
extension DieGuideBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildListOfWidgetFinalWidgetTypeDie() {
    if (widget.state.isDayChange()) {
      return _buildListOfWidgetFinalWidgetTypeThree();
    }
    return _buildListOfWidgetFinalWidgetTypeThreeInterrupt();
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeInterrupt() {
    if (widget.state.finalStatusPageStage == finalStageOne) {
      return buildListOfWidgetFinalWidgetTypeXXXInterruptFirst();
    }
    if (widget.state.finalStatusPageStage == finalStageTwo) {
      return _buildListOfWidgetFinalWidgetTypeThreeSecondInterrupt();
    }
    if (widget.state.finalStatusPageStage == finalStageThree) {
      return _buildListOfWidgetFinalWidgetTypeThreeThird();
    }
    l.i(tag, '''_buildListOfWidgetFinalWidgetTypeThreeInterrupt 显示空白
    finalStatusPageStage=${widget.state.finalStatusPageStage}''');
    return [];
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeSecondInterrupt() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 70.rdp),
            _buildWidgetFinalWidgetTypeThreeSlideAreaInterrupt(),
            SizedBox(height: 28.rdp),
            if (showFinalButtons)
              _buildWidgetFinalWidgetTypeThreeBottomButtonArea()
            else
              SizedBox(height: 44.rdp),
          ],
        ),
      )
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeSlideAreaInterrupt() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildWidgetFinalWidgetTypeThreeSpineArea(280.rdp),
          SizedBox(height: 4.rdp),
          buildBigText(context, S.of(context).continueIceDayNotChangeTip),
          buildSmallText(context, widget.state.finalSmallTip ?? ""),
        ],
      ),
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThree() {
    if (widget.state.finalStatusPageStage == finalStageOne) {
      return buildListOfWidgetFinalWidgetTypeXXXFirst();
    }
    if (widget.state.finalStatusPageStage == finalStageTwo) {
      return _buildListOfWidgetFinalWidgetTypeThreeSecond();
    }
    if (widget.state.finalStatusPageStage == finalStageThree) {
      return _buildListOfWidgetFinalWidgetTypeThreeThird();
    }
    l.i(tag, '''_buildListOfWidgetFinalWidgetTypeThree 显示空白
    finalStatusPageStage=${widget.state.finalStatusPageStage}''');
    return [];
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeSecond() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildWidgetFinalWidgetTypeThreeSlideArea(),
            SizedBox(height: 4.rdp),
            if (showFinalButtons)
              _buildWidgetFinalWidgetTypeThreeBottomButtonArea()
            else
              SizedBox(height: 44.rdp),
          ],
        ),
      )
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeSlideArea() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildWidgetFinalWidgetTypeXXXTopArea(),
          SizedBox(height: 2.rdp),
          buildBigText(context, S.of(context).continuousPersistence),
          _buildWidgetFinalWidgetTypeThreeCenterArea(),
        ],
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeCenterArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 18.rdp),
        _buildWidgetFinalWidgetTypeThreeSpineArea(280.rdp),
        SizedBox(height: 18.rdp),
        buildSmallText(context, widget.state.finalSmallTip ?? ""),
      ],
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: (94 / 110 * 130).rdp,
      height: 94.rdp,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeBottomButtonArea() {
    final btn = widget.state.btn;
    final count = widget.state.revivePropIdList.length;
    final canBuyCount = widget.state.canBuyRevivePropIdList.length;
    final bool isShowReviveButton = count > 0 || canBuyCount > 0;

    final btnText = S.of(context).forceIgnore;
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () => widget.tapCallback?.call(TapType.forceIgnore,btnText),
              child: VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
                child: buildButton(context, btnText,
                  backgroundColor: Colors.white,
                  borderColor: context.appColors.jColorYellow4),
              ),
            ),
            if (btn != null && isShowReviveButton) SizedBox(width: 20.rdp),
            if (btn != null && isShowReviveButton)
              _buildReviveOrBuyButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildReviveOrBuyButton(BuildContext context) {
    final count = widget.state.revivePropIdList.length;
    return count > 0 || widget.state.keepReviveButtonVisible
        ? _buildReviveOnlyButton(context, count)
        : _buildBuyAndUseButton(context);
  }

  Widget _buildReviveOnlyButton(BuildContext context, int count) {
    final btnText = widget.state.btn?.btnName ?? S.of(context).reviveTool;
    return GestureDetector(
        onTap: () => widget.tapCallback?.call(TapType.useToolForOtherCourse,btnText),
        child: VisibilityObserve(
        onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
        child:ReviveButton(
          buttonText: btnText,
          buttonBackgroundColor: context.appColors.jColorYellow4,
          remainCountText: buildRemainCountText(count),
        ))
    );
  }

  Widget _buildBuyAndUseButton(BuildContext context) {
    final LocalCanBuyPropData? propData =
        widget.state.canBuyRevivePropIdList.firstOrNull;
    final price = propData?.price ?? 0;
    final useAssetType = propData?.useAssetType ?? 1;
    final imgAssetName = useAssetType == 1
        ? AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_XUE_DOU
        : AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_DOU_XING_BAO_ZUAN;
    final btnText = S.of(context).buyAndUseProp;
    return IgnorePointer(
      ignoring: isNextButtonDisabled,
      child: Opacity(
        opacity: isNextButtonDisabled ? 0.5 : 1.0,
        child: GestureDetector(
          onTap: () =>
              widget.tapCallback?.call(TapType.buyAndUseToolForOtherCourse,btnText),
          child:VisibilityObserve(
            onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btnText),
            child: ReviveButton(
            buttonText: btnText,
            buttonBackgroundColor: context.appColors.jColorYellow4,
            remainCountText: '$price',
            remainIcon: imgAssetName,
            width: 160.rdp,
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeThreeThird() {
    return [
      _buildWidgetFinalWidgetTypeThreeThirdSpine(),
      _buildWidgetFinalWidgetTypeThreeThirdText(),
    ];
  }

  Widget _buildWidgetFinalWidgetTypeThreeThirdSpine() {
    return Positioned.fill(
      child: buildSpineAreaOverSize(spineJoJoSize.rdp),
    );
  }

  Widget _buildWidgetFinalWidgetTypeThreeThirdText() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 60.rdp + (screen.screenHeight - 375.rdp) / 2,
      child: FadeTransition(
        opacity: Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(dieToFrozenTextAnimController),
        child: Center(
          child: buildBigText(
            context,
            S.of(context).forzenFinalBigTip,
          ),
        ),
      ),
    );
  }
}
