import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/continue_learn_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ui_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/ext.dart';

import '../../../buried_ext.dart';

/// 后续引导 - 今日未正学
extension TodayNotFinishGuideBuilder<T extends BaseFromView>
    on BaseFromState<T> {
  List<Widget> buildListOfWidgetFinalWidgetTypeTodayNotFinish() {
    if (widget.state.finalStatusPageStage == finalStageOne) {
      return widget.state.isDayChange()
          ? buildListOfWidgetFinalWidgetTypeXXXFirst()
          : buildListOfWidgetFinalWidgetTypeXXXInterruptFirst();
    }
    if (widget.state.finalStatusPageStage == finalStageTwo) {
      return _buildListOfWidgetFinalWidgetTypeTodayNotFinishSecond();
    }
    l.i(tag, '''buildListOfWidgetFinalWidgetTypeTodayNotFinish 显示空白
    finalStatusPageStage=${widget.state.finalStatusPageStage}''');
    return [];
  }

  List<Widget> _buildListOfWidgetFinalWidgetTypeTodayNotFinishSecond() {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 40.rdp),
            _buildListOfWidgetFinalWidgetTypeTodayNotFinishSecondSlideArea(),
            SizedBox(height: 28.rdp),
            if (showFinalButtons)
              _buildListOfWidgetFinalWidgetTypeTodayNotFinishBottomButtonArea()
            else
              SizedBox(height: 44.rdp),
          ],
        ),
      )
    ];
  }

  Widget _buildListOfWidgetFinalWidgetTypeTodayNotFinishBottomButtonArea() {
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: finalButtonController,
      child: buildAppearWidget(
        animationController: finalButtonController,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: _buildButtons(),
        ),
      ),
    );
  }

  List<Widget> _buildButtons() {
    final List<Widget> btnList = [];
    final btn = widget.state.btn;
    if (btn == null) {
      btnList.addAll([
        GestureDetector(
            onTap: () => widget.tapCallback?.call(TapType.iKnow,S.of(context).iKnow),
            child: buildButton(context,
                S.of(context).iKnow, width: 280.rdp)),
      ]);
    } else {
      btnList.addAll([
        GestureDetector(
          onTap: () => widget.tapCallback?.call(TapType.forceIgnore,S.of(context).forceIgnore),
          child:  VisibilityObserve(
            onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: S.of(context).forceIgnore),
            child:buildButton(context, S.of(context).forceIgnore,
              backgroundColor: Colors.white,
              borderColor: context.appColors.jColorYellow4),
          ),
        ),
        SizedBox(width: 20.rdp),
        GestureDetector(
              onTap: () => widget.tapCallback?.call(TapType.goToFire,btn.btnName ?? S.of(context).relearnForAssistance),
              child:  VisibilityObserve(
                onShow: () => buriedContinueButtonViewScreen(state: widget.state, buttonName: btn.btnName ?? S.of(context).relearnForAssistance),
                child: buildButton(context,
                  btn.btnName ?? S.of(context).relearnForAssistance)),
        ),
      ]);
    }
    return btnList;
  }

  Widget _buildListOfWidgetFinalWidgetTypeTodayNotFinishSecondSlideArea() {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: secondNumberTextController,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildListOfWidgetFinalWidgetTypeTodayNotFinishSpineArea(280.rdp),
          SizedBox(height: 30.rdp),
          buildBigText(context, S.of(context).continueTodayNoFinishTip),
          buildSmallText(context, widget.state.finalSmallTip ?? ""),
        ],
      ),
    );
  }

  Widget _buildListOfWidgetFinalWidgetTypeTodayNotFinishSpineArea(double size) {
    return Container(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      width: (94 / 110 * 130).rdp,
      height: 94.rdp,
      child: overflow(
        child: buildSpineAreaOverSize(size),
      ),
    );
  }
}
