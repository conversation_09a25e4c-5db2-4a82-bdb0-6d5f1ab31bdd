import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/phone/widget/base/base_from_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/builder.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/device_scales.dart';
/// 火焰跳动
extension FireSequenceBuilder<T extends BaseFromView> on BaseFromState<T> {
  List<Widget> buildListOfFireSequenceWidget(double viewWidth) {
    if (!widget.state.isDayChange()) {
      return _buildFireSequenceWhenDayNotChange();
    }

    final size = spineJoJoSize.rdp;
    final numberWidth =
    getNumberWidth(widget.state.dayCount, widget.state.numberHeight().rdp);
    final withNumberFullWidth = numberWidth + 150.rdp;

    return [
      _buildListOfFireSequenceWidgetAppearArea(size),
      if (widget.state.isDayChange())
        _buildListOfFireSequenceWidgetDisappearNumberArea(viewWidth,withNumberFullWidth),
    ];
  }

  List<Widget> _buildFireSequenceWhenDayNotChange() {
    final size = spineJoJoSize.rdp;
    return [
      buildSpineArea(size)
    ];
  }

  Positioned _buildListOfFireSequenceWidgetDisappearNumberArea(
      double viewWidth,
      double withNumberFullWidth) {
    return Positioned(
      top: 0,
      left: viewWidth / 2 + 85.rdp,
      bottom: 0,
      child: Transform.translate(
        offset: Offset(65.rdp - withNumberFullWidth / 2, 0),
        child: buildSlideWidget(
          begin: Offset.zero,
          end: const Offset(0, -0.1),
          animationController: fireSequenceNumberController,
          child: buildNumberImagesDisappear(widget.state.historyCount,
              widget.state.numberHeight().rdp, fireSequenceNumberController),
        ),
      ),
    );
  }

  Positioned _buildListOfFireSequenceWidgetAppearArea(double size) {
    return Positioned.fill(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            buildSpineAreaOverSize(size),
            SizedBox(width: 20.rdp),
            if (widget.state.isDayChange())
              _buildListOfFireSequenceWidgetAppearNumberArea()
            else
              _buildListOfFireSequenceWidgetNumberArea(),
          ],
        ));
  }

  Column _buildListOfFireSequenceWidgetNumberArea() {
    return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildNumberImagesNoAnim(
            widget.state.dayCount,
            widget.state.numberHeight().rdp,
          ),
        ]);
  }

  Column _buildListOfFireSequenceWidgetAppearNumberArea() {
    return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildSlideWidget(
            begin: const Offset(0, 1),
            end: Offset.zero,
            curve: Curves.easeInOutBack,
            animationController: fireSequenceNumberController,
            child: buildNumberImagesAppear(widget.state.dayCount,
                widget.state.numberHeight().rdp, fireSequenceNumberController,
                showDay: true,
                dayAnimationController: fireSequenceDayAppearAnimController),
          ),
        ]);
  }
}