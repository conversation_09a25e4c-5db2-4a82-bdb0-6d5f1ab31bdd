// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_data_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SegmentData _$SegmentDataFromJson(Map<String, dynamic> json) {
  return _SegmentData.fromJson(json);
}

/// @nodoc
mixin _$SegmentData {
  List<CourseSegmentList>? get courseSegmentList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentDataCopyWith<SegmentData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentDataCopyWith<$Res> {
  factory $SegmentDataCopyWith(
          SegmentData value, $Res Function(SegmentData) then) =
      _$SegmentDataCopyWithImpl<$Res, SegmentData>;
  @useResult
  $Res call({List<CourseSegmentList>? courseSegmentList});
}

/// @nodoc
class _$SegmentDataCopyWithImpl<$Res, $Val extends SegmentData>
    implements $SegmentDataCopyWith<$Res> {
  _$SegmentDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentList = freezed,
  }) {
    return _then(_value.copyWith(
      courseSegmentList: freezed == courseSegmentList
          ? _value.courseSegmentList
          : courseSegmentList // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SegmentDataCopyWith<$Res>
    implements $SegmentDataCopyWith<$Res> {
  factory _$$_SegmentDataCopyWith(
          _$_SegmentData value, $Res Function(_$_SegmentData) then) =
      __$$_SegmentDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CourseSegmentList>? courseSegmentList});
}

/// @nodoc
class __$$_SegmentDataCopyWithImpl<$Res>
    extends _$SegmentDataCopyWithImpl<$Res, _$_SegmentData>
    implements _$$_SegmentDataCopyWith<$Res> {
  __$$_SegmentDataCopyWithImpl(
      _$_SegmentData _value, $Res Function(_$_SegmentData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentList = freezed,
  }) {
    return _then(_$_SegmentData(
      courseSegmentList: freezed == courseSegmentList
          ? _value._courseSegmentList
          : courseSegmentList // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SegmentData with DiagnosticableTreeMixin implements _SegmentData {
  const _$_SegmentData({final List<CourseSegmentList>? courseSegmentList})
      : _courseSegmentList = courseSegmentList;

  factory _$_SegmentData.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentDataFromJson(json);

  final List<CourseSegmentList>? _courseSegmentList;
  @override
  List<CourseSegmentList>? get courseSegmentList {
    final value = _courseSegmentList;
    if (value == null) return null;
    if (_courseSegmentList is EqualUnmodifiableListView)
      return _courseSegmentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SegmentData(courseSegmentList: $courseSegmentList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SegmentData'))
      ..add(DiagnosticsProperty('courseSegmentList', courseSegmentList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SegmentData &&
            const DeepCollectionEquality()
                .equals(other._courseSegmentList, _courseSegmentList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_courseSegmentList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentDataCopyWith<_$_SegmentData> get copyWith =>
      __$$_SegmentDataCopyWithImpl<_$_SegmentData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentDataToJson(
      this,
    );
  }
}

abstract class _SegmentData implements SegmentData {
  const factory _SegmentData(
      {final List<CourseSegmentList>? courseSegmentList}) = _$_SegmentData;

  factory _SegmentData.fromJson(Map<String, dynamic> json) =
      _$_SegmentData.fromJson;

  @override
  List<CourseSegmentList>? get courseSegmentList;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentDataCopyWith<_$_SegmentData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseSegmentList _$CourseSegmentListFromJson(Map<String, dynamic> json) {
  return _CourseSegmentList.fromJson(json);
}

/// @nodoc
mixin _$CourseSegmentList {
  int? get segmentId => throw _privateConstructorUsedError;
  String? get segmentKey => throw _privateConstructorUsedError;
  int? get segmentOrder => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get segmentNameOnNavigation => throw _privateConstructorUsedError;
  List<SegmentScheduleList>? get segmentScheduleList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSegmentListCopyWith<CourseSegmentList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSegmentListCopyWith<$Res> {
  factory $CourseSegmentListCopyWith(
          CourseSegmentList value, $Res Function(CourseSegmentList) then) =
      _$CourseSegmentListCopyWithImpl<$Res, CourseSegmentList>;
  @useResult
  $Res call(
      {int? segmentId,
      String? segmentKey,
      int? segmentOrder,
      String? segmentName,
      String? segmentNameOnNavigation,
      List<SegmentScheduleList>? segmentScheduleList});
}

/// @nodoc
class _$CourseSegmentListCopyWithImpl<$Res, $Val extends CourseSegmentList>
    implements $CourseSegmentListCopyWith<$Res> {
  _$CourseSegmentListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? segmentKey = freezed,
    Object? segmentOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentNameOnNavigation = freezed,
    Object? segmentScheduleList = freezed,
  }) {
    return _then(_value.copyWith(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrder: freezed == segmentOrder
          ? _value.segmentOrder
          : segmentOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentNameOnNavigation: freezed == segmentNameOnNavigation
          ? _value.segmentNameOnNavigation
          : segmentNameOnNavigation // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentScheduleList: freezed == segmentScheduleList
          ? _value.segmentScheduleList
          : segmentScheduleList // ignore: cast_nullable_to_non_nullable
              as List<SegmentScheduleList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseSegmentListCopyWith<$Res>
    implements $CourseSegmentListCopyWith<$Res> {
  factory _$$_CourseSegmentListCopyWith(_$_CourseSegmentList value,
          $Res Function(_$_CourseSegmentList) then) =
      __$$_CourseSegmentListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? segmentId,
      String? segmentKey,
      int? segmentOrder,
      String? segmentName,
      String? segmentNameOnNavigation,
      List<SegmentScheduleList>? segmentScheduleList});
}

/// @nodoc
class __$$_CourseSegmentListCopyWithImpl<$Res>
    extends _$CourseSegmentListCopyWithImpl<$Res, _$_CourseSegmentList>
    implements _$$_CourseSegmentListCopyWith<$Res> {
  __$$_CourseSegmentListCopyWithImpl(
      _$_CourseSegmentList _value, $Res Function(_$_CourseSegmentList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? segmentKey = freezed,
    Object? segmentOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentNameOnNavigation = freezed,
    Object? segmentScheduleList = freezed,
  }) {
    return _then(_$_CourseSegmentList(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrder: freezed == segmentOrder
          ? _value.segmentOrder
          : segmentOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentNameOnNavigation: freezed == segmentNameOnNavigation
          ? _value.segmentNameOnNavigation
          : segmentNameOnNavigation // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentScheduleList: freezed == segmentScheduleList
          ? _value._segmentScheduleList
          : segmentScheduleList // ignore: cast_nullable_to_non_nullable
              as List<SegmentScheduleList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSegmentList
    with DiagnosticableTreeMixin
    implements _CourseSegmentList {
  const _$_CourseSegmentList(
      {this.segmentId,
      this.segmentKey,
      this.segmentOrder,
      this.segmentName,
      this.segmentNameOnNavigation,
      final List<SegmentScheduleList>? segmentScheduleList})
      : _segmentScheduleList = segmentScheduleList;

  factory _$_CourseSegmentList.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSegmentListFromJson(json);

  @override
  final int? segmentId;
  @override
  final String? segmentKey;
  @override
  final int? segmentOrder;
  @override
  final String? segmentName;
  @override
  final String? segmentNameOnNavigation;
  final List<SegmentScheduleList>? _segmentScheduleList;
  @override
  List<SegmentScheduleList>? get segmentScheduleList {
    final value = _segmentScheduleList;
    if (value == null) return null;
    if (_segmentScheduleList is EqualUnmodifiableListView)
      return _segmentScheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CourseSegmentList(segmentId: $segmentId, segmentKey: $segmentKey, segmentOrder: $segmentOrder, segmentName: $segmentName, segmentNameOnNavigation: $segmentNameOnNavigation, segmentScheduleList: $segmentScheduleList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CourseSegmentList'))
      ..add(DiagnosticsProperty('segmentId', segmentId))
      ..add(DiagnosticsProperty('segmentKey', segmentKey))
      ..add(DiagnosticsProperty('segmentOrder', segmentOrder))
      ..add(DiagnosticsProperty('segmentName', segmentName))
      ..add(DiagnosticsProperty(
          'segmentNameOnNavigation', segmentNameOnNavigation))
      ..add(DiagnosticsProperty('segmentScheduleList', segmentScheduleList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseSegmentList &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentKey, segmentKey) ||
                other.segmentKey == segmentKey) &&
            (identical(other.segmentOrder, segmentOrder) ||
                other.segmentOrder == segmentOrder) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(
                    other.segmentNameOnNavigation, segmentNameOnNavigation) ||
                other.segmentNameOnNavigation == segmentNameOnNavigation) &&
            const DeepCollectionEquality()
                .equals(other._segmentScheduleList, _segmentScheduleList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      segmentId,
      segmentKey,
      segmentOrder,
      segmentName,
      segmentNameOnNavigation,
      const DeepCollectionEquality().hash(_segmentScheduleList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSegmentListCopyWith<_$_CourseSegmentList> get copyWith =>
      __$$_CourseSegmentListCopyWithImpl<_$_CourseSegmentList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSegmentListToJson(
      this,
    );
  }
}

abstract class _CourseSegmentList implements CourseSegmentList {
  const factory _CourseSegmentList(
          {final int? segmentId,
          final String? segmentKey,
          final int? segmentOrder,
          final String? segmentName,
          final String? segmentNameOnNavigation,
          final List<SegmentScheduleList>? segmentScheduleList}) =
      _$_CourseSegmentList;

  factory _CourseSegmentList.fromJson(Map<String, dynamic> json) =
      _$_CourseSegmentList.fromJson;

  @override
  int? get segmentId;
  @override
  String? get segmentKey;
  @override
  int? get segmentOrder;
  @override
  String? get segmentName;
  @override
  String? get segmentNameOnNavigation;
  @override
  List<SegmentScheduleList>? get segmentScheduleList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseSegmentListCopyWith<_$_CourseSegmentList> get copyWith =>
      throw _privateConstructorUsedError;
}

SegmentScheduleList _$SegmentScheduleListFromJson(Map<String, dynamic> json) {
  return _SegmentScheduleList.fromJson(json);
}

/// @nodoc
mixin _$SegmentScheduleList {
  int? get lessonId => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;
  int? get lessonOrder => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  int? get unlockTime => throw _privateConstructorUsedError;
  String? get lessonSubTitle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentScheduleListCopyWith<SegmentScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentScheduleListCopyWith<$Res> {
  factory $SegmentScheduleListCopyWith(
          SegmentScheduleList value, $Res Function(SegmentScheduleList) then) =
      _$SegmentScheduleListCopyWithImpl<$Res, SegmentScheduleList>;
  @useResult
  $Res call(
      {int? lessonId,
      int? weekId,
      String? weekName,
      int? lessonOrder,
      String? lessonName,
      int? unlockTime,
      String? lessonSubTitle});
}

/// @nodoc
class _$SegmentScheduleListCopyWithImpl<$Res, $Val extends SegmentScheduleList>
    implements $SegmentScheduleListCopyWith<$Res> {
  _$SegmentScheduleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? lessonOrder = freezed,
    Object? lessonName = freezed,
    Object? unlockTime = freezed,
    Object? lessonSubTitle = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockTime: freezed == unlockTime
          ? _value.unlockTime
          : unlockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SegmentScheduleListCopyWith<$Res>
    implements $SegmentScheduleListCopyWith<$Res> {
  factory _$$_SegmentScheduleListCopyWith(_$_SegmentScheduleList value,
          $Res Function(_$_SegmentScheduleList) then) =
      __$$_SegmentScheduleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? lessonId,
      int? weekId,
      String? weekName,
      int? lessonOrder,
      String? lessonName,
      int? unlockTime,
      String? lessonSubTitle});
}

/// @nodoc
class __$$_SegmentScheduleListCopyWithImpl<$Res>
    extends _$SegmentScheduleListCopyWithImpl<$Res, _$_SegmentScheduleList>
    implements _$$_SegmentScheduleListCopyWith<$Res> {
  __$$_SegmentScheduleListCopyWithImpl(_$_SegmentScheduleList _value,
      $Res Function(_$_SegmentScheduleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? lessonOrder = freezed,
    Object? lessonName = freezed,
    Object? unlockTime = freezed,
    Object? lessonSubTitle = freezed,
  }) {
    return _then(_$_SegmentScheduleList(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockTime: freezed == unlockTime
          ? _value.unlockTime
          : unlockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SegmentScheduleList
    with DiagnosticableTreeMixin
    implements _SegmentScheduleList {
  const _$_SegmentScheduleList(
      {this.lessonId,
      this.weekId,
      this.weekName,
      this.lessonOrder,
      this.lessonName,
      this.unlockTime,
      this.lessonSubTitle});

  factory _$_SegmentScheduleList.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentScheduleListFromJson(json);

  @override
  final int? lessonId;
  @override
  final int? weekId;
  @override
  final String? weekName;
  @override
  final int? lessonOrder;
  @override
  final String? lessonName;
  @override
  final int? unlockTime;
  @override
  final String? lessonSubTitle;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SegmentScheduleList(lessonId: $lessonId, weekId: $weekId, weekName: $weekName, lessonOrder: $lessonOrder, lessonName: $lessonName, unlockTime: $unlockTime, lessonSubTitle: $lessonSubTitle)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SegmentScheduleList'))
      ..add(DiagnosticsProperty('lessonId', lessonId))
      ..add(DiagnosticsProperty('weekId', weekId))
      ..add(DiagnosticsProperty('weekName', weekName))
      ..add(DiagnosticsProperty('lessonOrder', lessonOrder))
      ..add(DiagnosticsProperty('lessonName', lessonName))
      ..add(DiagnosticsProperty('unlockTime', unlockTime))
      ..add(DiagnosticsProperty('lessonSubTitle', lessonSubTitle));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SegmentScheduleList &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.weekName, weekName) ||
                other.weekName == weekName) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.unlockTime, unlockTime) ||
                other.unlockTime == unlockTime) &&
            (identical(other.lessonSubTitle, lessonSubTitle) ||
                other.lessonSubTitle == lessonSubTitle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lessonId, weekId, weekName,
      lessonOrder, lessonName, unlockTime, lessonSubTitle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentScheduleListCopyWith<_$_SegmentScheduleList> get copyWith =>
      __$$_SegmentScheduleListCopyWithImpl<_$_SegmentScheduleList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentScheduleListToJson(
      this,
    );
  }
}

abstract class _SegmentScheduleList implements SegmentScheduleList {
  const factory _SegmentScheduleList(
      {final int? lessonId,
      final int? weekId,
      final String? weekName,
      final int? lessonOrder,
      final String? lessonName,
      final int? unlockTime,
      final String? lessonSubTitle}) = _$_SegmentScheduleList;

  factory _SegmentScheduleList.fromJson(Map<String, dynamic> json) =
      _$_SegmentScheduleList.fromJson;

  @override
  int? get lessonId;
  @override
  int? get weekId;
  @override
  String? get weekName;
  @override
  int? get lessonOrder;
  @override
  String? get lessonName;
  @override
  int? get unlockTime;
  @override
  String? get lessonSubTitle;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentScheduleListCopyWith<_$_SegmentScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}
