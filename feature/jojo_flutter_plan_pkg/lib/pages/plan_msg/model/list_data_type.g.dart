// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_data_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SegmentData _$$_SegmentDataFromJson(Map<String, dynamic> json) =>
    _$_SegmentData(
      courseSegmentList: (json['courseSegmentList'] as List<dynamic>?)
          ?.map((e) => CourseSegmentList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SegmentDataToJson(_$_SegmentData instance) =>
    <String, dynamic>{
      'courseSegmentList': instance.courseSegmentList,
    };

_$_CourseSegmentList _$$_CourseSegmentListFromJson(Map<String, dynamic> json) =>
    _$_CourseSegmentList(
      segmentId: json['segmentId'] as int?,
      segmentKey: json['segmentKey'] as String?,
      segmentOrder: json['segmentOrder'] as int?,
      segmentName: json['segmentName'] as String?,
      segmentNameOnNavigation: json['segmentNameOnNavigation'] as String?,
      segmentScheduleList: (json['segmentScheduleList'] as List<dynamic>?)
          ?.map((e) => SegmentScheduleList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseSegmentListToJson(
        _$_CourseSegmentList instance) =>
    <String, dynamic>{
      'segmentId': instance.segmentId,
      'segmentKey': instance.segmentKey,
      'segmentOrder': instance.segmentOrder,
      'segmentName': instance.segmentName,
      'segmentNameOnNavigation': instance.segmentNameOnNavigation,
      'segmentScheduleList': instance.segmentScheduleList,
    };

_$_SegmentScheduleList _$$_SegmentScheduleListFromJson(
        Map<String, dynamic> json) =>
    _$_SegmentScheduleList(
      lessonId: json['lessonId'] as int?,
      weekId: json['weekId'] as int?,
      weekName: json['weekName'] as String?,
      lessonOrder: json['lessonOrder'] as int?,
      lessonName: json['lessonName'] as String?,
      unlockTime: json['unlockTime'] as int?,
      lessonSubTitle: json['lessonSubTitle'] as String?,
    );

Map<String, dynamic> _$$_SegmentScheduleListToJson(
        _$_SegmentScheduleList instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'weekId': instance.weekId,
      'weekName': instance.weekName,
      'lessonOrder': instance.lessonOrder,
      'lessonName': instance.lessonName,
      'unlockTime': instance.unlockTime,
      'lessonSubTitle': instance.lessonSubTitle,
    };
