// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'logistis.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LogisticsData _$$_LogisticsDataFromJson(Map<String, dynamic> json) =>
    _$_LogisticsData(
      courseLogisticVoList: (json['courseLogisticVoList'] as List<dynamic>?)
          ?.map((e) => CourseLogisticVoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      extendBar: json['extendBar'] == null
          ? null
          : ExtendBar.fromJson(json['extendBar'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_LogisticsDataToJson(_$_LogisticsData instance) =>
    <String, dynamic>{
      'courseLogisticVoList': instance.courseLogisticVoList,
      'extendBar': instance.extendBar,
    };

_$_CourseLogisticVoList _$$_CourseLogisticVoListFromJson(
        Map<String, dynamic> json) =>
    _$_CourseLogisticVoList(
      orderName: json['orderName'] as String?,
      orderNo: json['orderNo'] as String?,
      deliverTime: json['deliverTime'] as String?,
      deliverTimeLong: json['deliverTimeLong'] as int?,
      deliverStatus: json['deliverStatus'] as String?,
      revocationCause: json['revocationCause'] as String?,
      logisticsState: json['logisticsState'] as String?,
      logisticsDesc: json['logisticsDesc'] as String?,
      logisticsDetailUrl: json['logisticsDetailUrl'] as String?,
      recently: json['recently'] as bool?,
      expressCompany: json['expressCompany'] as String?,
      expressNumber: json['expressNumber'] as String?,
      goodsType: json['goodsType'] as int?,
      topicType: json['topicType'] as int?,
      periods: json['periods'] as String?,
    );

Map<String, dynamic> _$$_CourseLogisticVoListToJson(
        _$_CourseLogisticVoList instance) =>
    <String, dynamic>{
      'orderName': instance.orderName,
      'orderNo': instance.orderNo,
      'deliverTime': instance.deliverTime,
      'deliverTimeLong': instance.deliverTimeLong,
      'deliverStatus': instance.deliverStatus,
      'revocationCause': instance.revocationCause,
      'logisticsState': instance.logisticsState,
      'logisticsDesc': instance.logisticsDesc,
      'logisticsDetailUrl': instance.logisticsDetailUrl,
      'recently': instance.recently,
      'expressCompany': instance.expressCompany,
      'expressNumber': instance.expressNumber,
      'goodsType': instance.goodsType,
      'topicType': instance.topicType,
      'periods': instance.periods,
    };

_$_LogisticsDataWithordeOrNameList _$$_LogisticsDataWithordeOrNameListFromJson(
        Map<String, dynamic> json) =>
    _$_LogisticsDataWithordeOrNameList(
      courseLogisticVoList: (json['courseLogisticVoList'] as List<dynamic>?)
          ?.map((e) => CourseLogisticVoListWithordeOrNameList.fromJson(
              e as Map<String, dynamic>))
          .toList(),
      extendBar: json['extendBar'] == null
          ? null
          : ExtendBar.fromJson(json['extendBar'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_LogisticsDataWithordeOrNameListToJson(
        _$_LogisticsDataWithordeOrNameList instance) =>
    <String, dynamic>{
      'courseLogisticVoList': instance.courseLogisticVoList,
      'extendBar': instance.extendBar,
    };

_$_CourseLogisticVoListWithordeOrNameList
    _$$_CourseLogisticVoListWithordeOrNameListFromJson(
            Map<String, dynamic> json) =>
        _$_CourseLogisticVoListWithordeOrNameList(
          orderName: (json['orderName'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
          orderNo: json['orderNo'] as String?,
          deliverTime: json['deliverTime'] as String?,
          deliverTimeLong: json['deliverTimeLong'] as int?,
          deliverStatus: json['deliverStatus'] as String?,
          revocationCause: json['revocationCause'] as String?,
          logisticsState: json['logisticsState'] as String?,
          logisticsDesc: json['logisticsDesc'] as String?,
          logisticsDetailUrl: json['logisticsDetailUrl'] as String?,
          recently: json['recently'] as bool?,
          expressCompany: json['expressCompany'] as String?,
          expressNumber: json['expressNumber'] as String?,
          goodsType: json['goodsType'] as int?,
          topicType: json['topicType'] as int?,
          periods: json['periods'] as String?,
        );

Map<String, dynamic> _$$_CourseLogisticVoListWithordeOrNameListToJson(
        _$_CourseLogisticVoListWithordeOrNameList instance) =>
    <String, dynamic>{
      'orderName': instance.orderName,
      'orderNo': instance.orderNo,
      'deliverTime': instance.deliverTime,
      'deliverTimeLong': instance.deliverTimeLong,
      'deliverStatus': instance.deliverStatus,
      'revocationCause': instance.revocationCause,
      'logisticsState': instance.logisticsState,
      'logisticsDesc': instance.logisticsDesc,
      'logisticsDetailUrl': instance.logisticsDetailUrl,
      'recently': instance.recently,
      'expressCompany': instance.expressCompany,
      'expressNumber': instance.expressNumber,
      'goodsType': instance.goodsType,
      'topicType': instance.topicType,
      'periods': instance.periods,
    };

_$_ExtendBar _$$_ExtendBarFromJson(Map<String, dynamic> json) => _$_ExtendBar(
      contactCustomerContentList: (json['contactCustomerContentList']
              as List<dynamic>?)
          ?.map((e) =>
              ContactCustomerContentList.fromJson(e as Map<String, dynamic>))
          .toList(),
      haveCourseStatus: json['haveCourseStatus'],
      leftFuncList: json['leftFuncList'],
      rightToolList: json['rightToolList'],
    );

Map<String, dynamic> _$$_ExtendBarToJson(_$_ExtendBar instance) =>
    <String, dynamic>{
      'contactCustomerContentList': instance.contactCustomerContentList,
      'haveCourseStatus': instance.haveCourseStatus,
      'leftFuncList': instance.leftFuncList,
      'rightToolList': instance.rightToolList,
    };

_$_ContactCustomerContentList _$$_ContactCustomerContentListFromJson(
        Map<String, dynamic> json) =>
    _$_ContactCustomerContentList(
      color: json['color'] as String?,
      text: json['text'] as String?,
      linkUrl: json['linkUrl'] as String?,
    );

Map<String, dynamic> _$$_ContactCustomerContentListToJson(
        _$_ContactCustomerContentList instance) =>
    <String, dynamic>{
      'color': instance.color,
      'text': instance.text,
      'linkUrl': instance.linkUrl,
    };
