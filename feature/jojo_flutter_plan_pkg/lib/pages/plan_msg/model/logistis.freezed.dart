// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'logistis.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LogisticsData _$LogisticsDataFromJson(Map<String, dynamic> json) {
  return _LogisticsData.fromJson(json);
}

/// @nodoc
mixin _$LogisticsData {
  List<CourseLogisticVoList>? get courseLogisticVoList =>
      throw _privateConstructorUsedError;
  ExtendBar? get extendBar => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogisticsDataCopyWith<LogisticsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogisticsDataCopyWith<$Res> {
  factory $LogisticsDataCopyWith(
          LogisticsData value, $Res Function(LogisticsData) then) =
      _$LogisticsDataCopyWithImpl<$Res, LogisticsData>;
  @useResult
  $Res call(
      {List<CourseLogisticVoList>? courseLogisticVoList, ExtendBar? extendBar});

  $ExtendBarCopyWith<$Res>? get extendBar;
}

/// @nodoc
class _$LogisticsDataCopyWithImpl<$Res, $Val extends LogisticsData>
    implements $LogisticsDataCopyWith<$Res> {
  _$LogisticsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLogisticVoList = freezed,
    Object? extendBar = freezed,
  }) {
    return _then(_value.copyWith(
      courseLogisticVoList: freezed == courseLogisticVoList
          ? _value.courseLogisticVoList
          : courseLogisticVoList // ignore: cast_nullable_to_non_nullable
              as List<CourseLogisticVoList>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as ExtendBar?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtendBarCopyWith<$Res>? get extendBar {
    if (_value.extendBar == null) {
      return null;
    }

    return $ExtendBarCopyWith<$Res>(_value.extendBar!, (value) {
      return _then(_value.copyWith(extendBar: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LogisticsDataCopyWith<$Res>
    implements $LogisticsDataCopyWith<$Res> {
  factory _$$_LogisticsDataCopyWith(
          _$_LogisticsData value, $Res Function(_$_LogisticsData) then) =
      __$$_LogisticsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<CourseLogisticVoList>? courseLogisticVoList, ExtendBar? extendBar});

  @override
  $ExtendBarCopyWith<$Res>? get extendBar;
}

/// @nodoc
class __$$_LogisticsDataCopyWithImpl<$Res>
    extends _$LogisticsDataCopyWithImpl<$Res, _$_LogisticsData>
    implements _$$_LogisticsDataCopyWith<$Res> {
  __$$_LogisticsDataCopyWithImpl(
      _$_LogisticsData _value, $Res Function(_$_LogisticsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLogisticVoList = freezed,
    Object? extendBar = freezed,
  }) {
    return _then(_$_LogisticsData(
      courseLogisticVoList: freezed == courseLogisticVoList
          ? _value._courseLogisticVoList
          : courseLogisticVoList // ignore: cast_nullable_to_non_nullable
              as List<CourseLogisticVoList>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as ExtendBar?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LogisticsData with DiagnosticableTreeMixin implements _LogisticsData {
  const _$_LogisticsData(
      {final List<CourseLogisticVoList>? courseLogisticVoList, this.extendBar})
      : _courseLogisticVoList = courseLogisticVoList;

  factory _$_LogisticsData.fromJson(Map<String, dynamic> json) =>
      _$$_LogisticsDataFromJson(json);

  final List<CourseLogisticVoList>? _courseLogisticVoList;
  @override
  List<CourseLogisticVoList>? get courseLogisticVoList {
    final value = _courseLogisticVoList;
    if (value == null) return null;
    if (_courseLogisticVoList is EqualUnmodifiableListView)
      return _courseLogisticVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ExtendBar? extendBar;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'LogisticsData(courseLogisticVoList: $courseLogisticVoList, extendBar: $extendBar)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'LogisticsData'))
      ..add(DiagnosticsProperty('courseLogisticVoList', courseLogisticVoList))
      ..add(DiagnosticsProperty('extendBar', extendBar));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LogisticsData &&
            const DeepCollectionEquality()
                .equals(other._courseLogisticVoList, _courseLogisticVoList) &&
            (identical(other.extendBar, extendBar) ||
                other.extendBar == extendBar));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_courseLogisticVoList), extendBar);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LogisticsDataCopyWith<_$_LogisticsData> get copyWith =>
      __$$_LogisticsDataCopyWithImpl<_$_LogisticsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LogisticsDataToJson(
      this,
    );
  }
}

abstract class _LogisticsData implements LogisticsData {
  const factory _LogisticsData(
      {final List<CourseLogisticVoList>? courseLogisticVoList,
      final ExtendBar? extendBar}) = _$_LogisticsData;

  factory _LogisticsData.fromJson(Map<String, dynamic> json) =
      _$_LogisticsData.fromJson;

  @override
  List<CourseLogisticVoList>? get courseLogisticVoList;
  @override
  ExtendBar? get extendBar;
  @override
  @JsonKey(ignore: true)
  _$$_LogisticsDataCopyWith<_$_LogisticsData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseLogisticVoList _$CourseLogisticVoListFromJson(Map<String, dynamic> json) {
  return _CourseLogisticVoList.fromJson(json);
}

/// @nodoc
mixin _$CourseLogisticVoList {
  String? get orderName => throw _privateConstructorUsedError;
  String? get orderNo => throw _privateConstructorUsedError;
  String? get deliverTime => throw _privateConstructorUsedError;
  int? get deliverTimeLong => throw _privateConstructorUsedError;
  String? get deliverStatus => throw _privateConstructorUsedError;
  String? get revocationCause => throw _privateConstructorUsedError;
  String? get logisticsState => throw _privateConstructorUsedError;
  String? get logisticsDesc => throw _privateConstructorUsedError;
  String? get logisticsDetailUrl => throw _privateConstructorUsedError;
  bool? get recently => throw _privateConstructorUsedError;
  String? get expressCompany => throw _privateConstructorUsedError;
  String? get expressNumber => throw _privateConstructorUsedError;
  int? get goodsType => throw _privateConstructorUsedError;
  int? get topicType => throw _privateConstructorUsedError;
  String? get periods => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLogisticVoListCopyWith<CourseLogisticVoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLogisticVoListCopyWith<$Res> {
  factory $CourseLogisticVoListCopyWith(CourseLogisticVoList value,
          $Res Function(CourseLogisticVoList) then) =
      _$CourseLogisticVoListCopyWithImpl<$Res, CourseLogisticVoList>;
  @useResult
  $Res call(
      {String? orderName,
      String? orderNo,
      String? deliverTime,
      int? deliverTimeLong,
      String? deliverStatus,
      String? revocationCause,
      String? logisticsState,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      bool? recently,
      String? expressCompany,
      String? expressNumber,
      int? goodsType,
      int? topicType,
      String? periods});
}

/// @nodoc
class _$CourseLogisticVoListCopyWithImpl<$Res,
        $Val extends CourseLogisticVoList>
    implements $CourseLogisticVoListCopyWith<$Res> {
  _$CourseLogisticVoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderName = freezed,
    Object? orderNo = freezed,
    Object? deliverTime = freezed,
    Object? deliverTimeLong = freezed,
    Object? deliverStatus = freezed,
    Object? revocationCause = freezed,
    Object? logisticsState = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? recently = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? goodsType = freezed,
    Object? topicType = freezed,
    Object? periods = freezed,
  }) {
    return _then(_value.copyWith(
      orderName: freezed == orderName
          ? _value.orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTime: freezed == deliverTime
          ? _value.deliverTime
          : deliverTime // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTimeLong: freezed == deliverTimeLong
          ? _value.deliverTimeLong
          : deliverTimeLong // ignore: cast_nullable_to_non_nullable
              as int?,
      deliverStatus: freezed == deliverStatus
          ? _value.deliverStatus
          : deliverStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      revocationCause: freezed == revocationCause
          ? _value.revocationCause
          : revocationCause // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsState: freezed == logisticsState
          ? _value.logisticsState
          : logisticsState // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recently: freezed == recently
          ? _value.recently
          : recently // ignore: cast_nullable_to_non_nullable
              as bool?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsType: freezed == goodsType
          ? _value.goodsType
          : goodsType // ignore: cast_nullable_to_non_nullable
              as int?,
      topicType: freezed == topicType
          ? _value.topicType
          : topicType // ignore: cast_nullable_to_non_nullable
              as int?,
      periods: freezed == periods
          ? _value.periods
          : periods // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLogisticVoListCopyWith<$Res>
    implements $CourseLogisticVoListCopyWith<$Res> {
  factory _$$_CourseLogisticVoListCopyWith(_$_CourseLogisticVoList value,
          $Res Function(_$_CourseLogisticVoList) then) =
      __$$_CourseLogisticVoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? orderName,
      String? orderNo,
      String? deliverTime,
      int? deliverTimeLong,
      String? deliverStatus,
      String? revocationCause,
      String? logisticsState,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      bool? recently,
      String? expressCompany,
      String? expressNumber,
      int? goodsType,
      int? topicType,
      String? periods});
}

/// @nodoc
class __$$_CourseLogisticVoListCopyWithImpl<$Res>
    extends _$CourseLogisticVoListCopyWithImpl<$Res, _$_CourseLogisticVoList>
    implements _$$_CourseLogisticVoListCopyWith<$Res> {
  __$$_CourseLogisticVoListCopyWithImpl(_$_CourseLogisticVoList _value,
      $Res Function(_$_CourseLogisticVoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderName = freezed,
    Object? orderNo = freezed,
    Object? deliverTime = freezed,
    Object? deliverTimeLong = freezed,
    Object? deliverStatus = freezed,
    Object? revocationCause = freezed,
    Object? logisticsState = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? recently = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? goodsType = freezed,
    Object? topicType = freezed,
    Object? periods = freezed,
  }) {
    return _then(_$_CourseLogisticVoList(
      orderName: freezed == orderName
          ? _value.orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTime: freezed == deliverTime
          ? _value.deliverTime
          : deliverTime // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTimeLong: freezed == deliverTimeLong
          ? _value.deliverTimeLong
          : deliverTimeLong // ignore: cast_nullable_to_non_nullable
              as int?,
      deliverStatus: freezed == deliverStatus
          ? _value.deliverStatus
          : deliverStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      revocationCause: freezed == revocationCause
          ? _value.revocationCause
          : revocationCause // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsState: freezed == logisticsState
          ? _value.logisticsState
          : logisticsState // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recently: freezed == recently
          ? _value.recently
          : recently // ignore: cast_nullable_to_non_nullable
              as bool?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsType: freezed == goodsType
          ? _value.goodsType
          : goodsType // ignore: cast_nullable_to_non_nullable
              as int?,
      topicType: freezed == topicType
          ? _value.topicType
          : topicType // ignore: cast_nullable_to_non_nullable
              as int?,
      periods: freezed == periods
          ? _value.periods
          : periods // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLogisticVoList
    with DiagnosticableTreeMixin
    implements _CourseLogisticVoList {
  const _$_CourseLogisticVoList(
      {this.orderName,
      this.orderNo,
      this.deliverTime,
      this.deliverTimeLong,
      this.deliverStatus,
      this.revocationCause,
      this.logisticsState,
      this.logisticsDesc,
      this.logisticsDetailUrl,
      this.recently,
      this.expressCompany,
      this.expressNumber,
      this.goodsType,
      this.topicType,
      this.periods});

  factory _$_CourseLogisticVoList.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLogisticVoListFromJson(json);

  @override
  final String? orderName;
  @override
  final String? orderNo;
  @override
  final String? deliverTime;
  @override
  final int? deliverTimeLong;
  @override
  final String? deliverStatus;
  @override
  final String? revocationCause;
  @override
  final String? logisticsState;
  @override
  final String? logisticsDesc;
  @override
  final String? logisticsDetailUrl;
  @override
  final bool? recently;
  @override
  final String? expressCompany;
  @override
  final String? expressNumber;
  @override
  final int? goodsType;
  @override
  final int? topicType;
  @override
  final String? periods;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CourseLogisticVoList(orderName: $orderName, orderNo: $orderNo, deliverTime: $deliverTime, deliverTimeLong: $deliverTimeLong, deliverStatus: $deliverStatus, revocationCause: $revocationCause, logisticsState: $logisticsState, logisticsDesc: $logisticsDesc, logisticsDetailUrl: $logisticsDetailUrl, recently: $recently, expressCompany: $expressCompany, expressNumber: $expressNumber, goodsType: $goodsType, topicType: $topicType, periods: $periods)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CourseLogisticVoList'))
      ..add(DiagnosticsProperty('orderName', orderName))
      ..add(DiagnosticsProperty('orderNo', orderNo))
      ..add(DiagnosticsProperty('deliverTime', deliverTime))
      ..add(DiagnosticsProperty('deliverTimeLong', deliverTimeLong))
      ..add(DiagnosticsProperty('deliverStatus', deliverStatus))
      ..add(DiagnosticsProperty('revocationCause', revocationCause))
      ..add(DiagnosticsProperty('logisticsState', logisticsState))
      ..add(DiagnosticsProperty('logisticsDesc', logisticsDesc))
      ..add(DiagnosticsProperty('logisticsDetailUrl', logisticsDetailUrl))
      ..add(DiagnosticsProperty('recently', recently))
      ..add(DiagnosticsProperty('expressCompany', expressCompany))
      ..add(DiagnosticsProperty('expressNumber', expressNumber))
      ..add(DiagnosticsProperty('goodsType', goodsType))
      ..add(DiagnosticsProperty('topicType', topicType))
      ..add(DiagnosticsProperty('periods', periods));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseLogisticVoList &&
            (identical(other.orderName, orderName) ||
                other.orderName == orderName) &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo) &&
            (identical(other.deliverTime, deliverTime) ||
                other.deliverTime == deliverTime) &&
            (identical(other.deliverTimeLong, deliverTimeLong) ||
                other.deliverTimeLong == deliverTimeLong) &&
            (identical(other.deliverStatus, deliverStatus) ||
                other.deliverStatus == deliverStatus) &&
            (identical(other.revocationCause, revocationCause) ||
                other.revocationCause == revocationCause) &&
            (identical(other.logisticsState, logisticsState) ||
                other.logisticsState == logisticsState) &&
            (identical(other.logisticsDesc, logisticsDesc) ||
                other.logisticsDesc == logisticsDesc) &&
            (identical(other.logisticsDetailUrl, logisticsDetailUrl) ||
                other.logisticsDetailUrl == logisticsDetailUrl) &&
            (identical(other.recently, recently) ||
                other.recently == recently) &&
            (identical(other.expressCompany, expressCompany) ||
                other.expressCompany == expressCompany) &&
            (identical(other.expressNumber, expressNumber) ||
                other.expressNumber == expressNumber) &&
            (identical(other.goodsType, goodsType) ||
                other.goodsType == goodsType) &&
            (identical(other.topicType, topicType) ||
                other.topicType == topicType) &&
            (identical(other.periods, periods) || other.periods == periods));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      orderName,
      orderNo,
      deliverTime,
      deliverTimeLong,
      deliverStatus,
      revocationCause,
      logisticsState,
      logisticsDesc,
      logisticsDetailUrl,
      recently,
      expressCompany,
      expressNumber,
      goodsType,
      topicType,
      periods);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLogisticVoListCopyWith<_$_CourseLogisticVoList> get copyWith =>
      __$$_CourseLogisticVoListCopyWithImpl<_$_CourseLogisticVoList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLogisticVoListToJson(
      this,
    );
  }
}

abstract class _CourseLogisticVoList implements CourseLogisticVoList {
  const factory _CourseLogisticVoList(
      {final String? orderName,
      final String? orderNo,
      final String? deliverTime,
      final int? deliverTimeLong,
      final String? deliverStatus,
      final String? revocationCause,
      final String? logisticsState,
      final String? logisticsDesc,
      final String? logisticsDetailUrl,
      final bool? recently,
      final String? expressCompany,
      final String? expressNumber,
      final int? goodsType,
      final int? topicType,
      final String? periods}) = _$_CourseLogisticVoList;

  factory _CourseLogisticVoList.fromJson(Map<String, dynamic> json) =
      _$_CourseLogisticVoList.fromJson;

  @override
  String? get orderName;
  @override
  String? get orderNo;
  @override
  String? get deliverTime;
  @override
  int? get deliverTimeLong;
  @override
  String? get deliverStatus;
  @override
  String? get revocationCause;
  @override
  String? get logisticsState;
  @override
  String? get logisticsDesc;
  @override
  String? get logisticsDetailUrl;
  @override
  bool? get recently;
  @override
  String? get expressCompany;
  @override
  String? get expressNumber;
  @override
  int? get goodsType;
  @override
  int? get topicType;
  @override
  String? get periods;
  @override
  @JsonKey(ignore: true)
  _$$_CourseLogisticVoListCopyWith<_$_CourseLogisticVoList> get copyWith =>
      throw _privateConstructorUsedError;
}

LogisticsDataWithordeOrNameList _$LogisticsDataWithordeOrNameListFromJson(
    Map<String, dynamic> json) {
  return _LogisticsDataWithordeOrNameList.fromJson(json);
}

/// @nodoc
mixin _$LogisticsDataWithordeOrNameList {
  List<CourseLogisticVoListWithordeOrNameList>? get courseLogisticVoList =>
      throw _privateConstructorUsedError;
  ExtendBar? get extendBar => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogisticsDataWithordeOrNameListCopyWith<LogisticsDataWithordeOrNameList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogisticsDataWithordeOrNameListCopyWith<$Res> {
  factory $LogisticsDataWithordeOrNameListCopyWith(
          LogisticsDataWithordeOrNameList value,
          $Res Function(LogisticsDataWithordeOrNameList) then) =
      _$LogisticsDataWithordeOrNameListCopyWithImpl<$Res,
          LogisticsDataWithordeOrNameList>;
  @useResult
  $Res call(
      {List<CourseLogisticVoListWithordeOrNameList>? courseLogisticVoList,
      ExtendBar? extendBar});

  $ExtendBarCopyWith<$Res>? get extendBar;
}

/// @nodoc
class _$LogisticsDataWithordeOrNameListCopyWithImpl<$Res,
        $Val extends LogisticsDataWithordeOrNameList>
    implements $LogisticsDataWithordeOrNameListCopyWith<$Res> {
  _$LogisticsDataWithordeOrNameListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLogisticVoList = freezed,
    Object? extendBar = freezed,
  }) {
    return _then(_value.copyWith(
      courseLogisticVoList: freezed == courseLogisticVoList
          ? _value.courseLogisticVoList
          : courseLogisticVoList // ignore: cast_nullable_to_non_nullable
              as List<CourseLogisticVoListWithordeOrNameList>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as ExtendBar?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtendBarCopyWith<$Res>? get extendBar {
    if (_value.extendBar == null) {
      return null;
    }

    return $ExtendBarCopyWith<$Res>(_value.extendBar!, (value) {
      return _then(_value.copyWith(extendBar: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LogisticsDataWithordeOrNameListCopyWith<$Res>
    implements $LogisticsDataWithordeOrNameListCopyWith<$Res> {
  factory _$$_LogisticsDataWithordeOrNameListCopyWith(
          _$_LogisticsDataWithordeOrNameList value,
          $Res Function(_$_LogisticsDataWithordeOrNameList) then) =
      __$$_LogisticsDataWithordeOrNameListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<CourseLogisticVoListWithordeOrNameList>? courseLogisticVoList,
      ExtendBar? extendBar});

  @override
  $ExtendBarCopyWith<$Res>? get extendBar;
}

/// @nodoc
class __$$_LogisticsDataWithordeOrNameListCopyWithImpl<$Res>
    extends _$LogisticsDataWithordeOrNameListCopyWithImpl<$Res,
        _$_LogisticsDataWithordeOrNameList>
    implements _$$_LogisticsDataWithordeOrNameListCopyWith<$Res> {
  __$$_LogisticsDataWithordeOrNameListCopyWithImpl(
      _$_LogisticsDataWithordeOrNameList _value,
      $Res Function(_$_LogisticsDataWithordeOrNameList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLogisticVoList = freezed,
    Object? extendBar = freezed,
  }) {
    return _then(_$_LogisticsDataWithordeOrNameList(
      courseLogisticVoList: freezed == courseLogisticVoList
          ? _value._courseLogisticVoList
          : courseLogisticVoList // ignore: cast_nullable_to_non_nullable
              as List<CourseLogisticVoListWithordeOrNameList>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as ExtendBar?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LogisticsDataWithordeOrNameList
    with DiagnosticableTreeMixin
    implements _LogisticsDataWithordeOrNameList {
  const _$_LogisticsDataWithordeOrNameList(
      {final List<CourseLogisticVoListWithordeOrNameList>? courseLogisticVoList,
      this.extendBar})
      : _courseLogisticVoList = courseLogisticVoList;

  factory _$_LogisticsDataWithordeOrNameList.fromJson(
          Map<String, dynamic> json) =>
      _$$_LogisticsDataWithordeOrNameListFromJson(json);

  final List<CourseLogisticVoListWithordeOrNameList>? _courseLogisticVoList;
  @override
  List<CourseLogisticVoListWithordeOrNameList>? get courseLogisticVoList {
    final value = _courseLogisticVoList;
    if (value == null) return null;
    if (_courseLogisticVoList is EqualUnmodifiableListView)
      return _courseLogisticVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ExtendBar? extendBar;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'LogisticsDataWithordeOrNameList(courseLogisticVoList: $courseLogisticVoList, extendBar: $extendBar)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'LogisticsDataWithordeOrNameList'))
      ..add(DiagnosticsProperty('courseLogisticVoList', courseLogisticVoList))
      ..add(DiagnosticsProperty('extendBar', extendBar));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LogisticsDataWithordeOrNameList &&
            const DeepCollectionEquality()
                .equals(other._courseLogisticVoList, _courseLogisticVoList) &&
            (identical(other.extendBar, extendBar) ||
                other.extendBar == extendBar));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_courseLogisticVoList), extendBar);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LogisticsDataWithordeOrNameListCopyWith<
          _$_LogisticsDataWithordeOrNameList>
      get copyWith => __$$_LogisticsDataWithordeOrNameListCopyWithImpl<
          _$_LogisticsDataWithordeOrNameList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LogisticsDataWithordeOrNameListToJson(
      this,
    );
  }
}

abstract class _LogisticsDataWithordeOrNameList
    implements LogisticsDataWithordeOrNameList {
  const factory _LogisticsDataWithordeOrNameList(
      {final List<CourseLogisticVoListWithordeOrNameList>? courseLogisticVoList,
      final ExtendBar? extendBar}) = _$_LogisticsDataWithordeOrNameList;

  factory _LogisticsDataWithordeOrNameList.fromJson(Map<String, dynamic> json) =
      _$_LogisticsDataWithordeOrNameList.fromJson;

  @override
  List<CourseLogisticVoListWithordeOrNameList>? get courseLogisticVoList;
  @override
  ExtendBar? get extendBar;
  @override
  @JsonKey(ignore: true)
  _$$_LogisticsDataWithordeOrNameListCopyWith<
          _$_LogisticsDataWithordeOrNameList>
      get copyWith => throw _privateConstructorUsedError;
}

CourseLogisticVoListWithordeOrNameList
    _$CourseLogisticVoListWithordeOrNameListFromJson(
        Map<String, dynamic> json) {
  return _CourseLogisticVoListWithordeOrNameList.fromJson(json);
}

/// @nodoc
mixin _$CourseLogisticVoListWithordeOrNameList {
  List<String>? get orderName => throw _privateConstructorUsedError;
  String? get orderNo => throw _privateConstructorUsedError;
  String? get deliverTime => throw _privateConstructorUsedError;
  int? get deliverTimeLong => throw _privateConstructorUsedError;
  String? get deliverStatus => throw _privateConstructorUsedError;
  String? get revocationCause => throw _privateConstructorUsedError;
  String? get logisticsState => throw _privateConstructorUsedError;
  String? get logisticsDesc => throw _privateConstructorUsedError;
  String? get logisticsDetailUrl => throw _privateConstructorUsedError;
  bool? get recently => throw _privateConstructorUsedError;
  String? get expressCompany => throw _privateConstructorUsedError;
  String? get expressNumber => throw _privateConstructorUsedError;
  int? get goodsType => throw _privateConstructorUsedError;
  int? get topicType => throw _privateConstructorUsedError;
  String? get periods => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLogisticVoListWithordeOrNameListCopyWith<
          CourseLogisticVoListWithordeOrNameList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLogisticVoListWithordeOrNameListCopyWith<$Res> {
  factory $CourseLogisticVoListWithordeOrNameListCopyWith(
          CourseLogisticVoListWithordeOrNameList value,
          $Res Function(CourseLogisticVoListWithordeOrNameList) then) =
      _$CourseLogisticVoListWithordeOrNameListCopyWithImpl<$Res,
          CourseLogisticVoListWithordeOrNameList>;
  @useResult
  $Res call(
      {List<String>? orderName,
      String? orderNo,
      String? deliverTime,
      int? deliverTimeLong,
      String? deliverStatus,
      String? revocationCause,
      String? logisticsState,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      bool? recently,
      String? expressCompany,
      String? expressNumber,
      int? goodsType,
      int? topicType,
      String? periods});
}

/// @nodoc
class _$CourseLogisticVoListWithordeOrNameListCopyWithImpl<$Res,
        $Val extends CourseLogisticVoListWithordeOrNameList>
    implements $CourseLogisticVoListWithordeOrNameListCopyWith<$Res> {
  _$CourseLogisticVoListWithordeOrNameListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderName = freezed,
    Object? orderNo = freezed,
    Object? deliverTime = freezed,
    Object? deliverTimeLong = freezed,
    Object? deliverStatus = freezed,
    Object? revocationCause = freezed,
    Object? logisticsState = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? recently = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? goodsType = freezed,
    Object? topicType = freezed,
    Object? periods = freezed,
  }) {
    return _then(_value.copyWith(
      orderName: freezed == orderName
          ? _value.orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTime: freezed == deliverTime
          ? _value.deliverTime
          : deliverTime // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTimeLong: freezed == deliverTimeLong
          ? _value.deliverTimeLong
          : deliverTimeLong // ignore: cast_nullable_to_non_nullable
              as int?,
      deliverStatus: freezed == deliverStatus
          ? _value.deliverStatus
          : deliverStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      revocationCause: freezed == revocationCause
          ? _value.revocationCause
          : revocationCause // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsState: freezed == logisticsState
          ? _value.logisticsState
          : logisticsState // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recently: freezed == recently
          ? _value.recently
          : recently // ignore: cast_nullable_to_non_nullable
              as bool?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsType: freezed == goodsType
          ? _value.goodsType
          : goodsType // ignore: cast_nullable_to_non_nullable
              as int?,
      topicType: freezed == topicType
          ? _value.topicType
          : topicType // ignore: cast_nullable_to_non_nullable
              as int?,
      periods: freezed == periods
          ? _value.periods
          : periods // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLogisticVoListWithordeOrNameListCopyWith<$Res>
    implements $CourseLogisticVoListWithordeOrNameListCopyWith<$Res> {
  factory _$$_CourseLogisticVoListWithordeOrNameListCopyWith(
          _$_CourseLogisticVoListWithordeOrNameList value,
          $Res Function(_$_CourseLogisticVoListWithordeOrNameList) then) =
      __$$_CourseLogisticVoListWithordeOrNameListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String>? orderName,
      String? orderNo,
      String? deliverTime,
      int? deliverTimeLong,
      String? deliverStatus,
      String? revocationCause,
      String? logisticsState,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      bool? recently,
      String? expressCompany,
      String? expressNumber,
      int? goodsType,
      int? topicType,
      String? periods});
}

/// @nodoc
class __$$_CourseLogisticVoListWithordeOrNameListCopyWithImpl<$Res>
    extends _$CourseLogisticVoListWithordeOrNameListCopyWithImpl<$Res,
        _$_CourseLogisticVoListWithordeOrNameList>
    implements _$$_CourseLogisticVoListWithordeOrNameListCopyWith<$Res> {
  __$$_CourseLogisticVoListWithordeOrNameListCopyWithImpl(
      _$_CourseLogisticVoListWithordeOrNameList _value,
      $Res Function(_$_CourseLogisticVoListWithordeOrNameList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderName = freezed,
    Object? orderNo = freezed,
    Object? deliverTime = freezed,
    Object? deliverTimeLong = freezed,
    Object? deliverStatus = freezed,
    Object? revocationCause = freezed,
    Object? logisticsState = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? recently = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? goodsType = freezed,
    Object? topicType = freezed,
    Object? periods = freezed,
  }) {
    return _then(_$_CourseLogisticVoListWithordeOrNameList(
      orderName: freezed == orderName
          ? _value._orderName
          : orderName // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTime: freezed == deliverTime
          ? _value.deliverTime
          : deliverTime // ignore: cast_nullable_to_non_nullable
              as String?,
      deliverTimeLong: freezed == deliverTimeLong
          ? _value.deliverTimeLong
          : deliverTimeLong // ignore: cast_nullable_to_non_nullable
              as int?,
      deliverStatus: freezed == deliverStatus
          ? _value.deliverStatus
          : deliverStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      revocationCause: freezed == revocationCause
          ? _value.revocationCause
          : revocationCause // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsState: freezed == logisticsState
          ? _value.logisticsState
          : logisticsState // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      recently: freezed == recently
          ? _value.recently
          : recently // ignore: cast_nullable_to_non_nullable
              as bool?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsType: freezed == goodsType
          ? _value.goodsType
          : goodsType // ignore: cast_nullable_to_non_nullable
              as int?,
      topicType: freezed == topicType
          ? _value.topicType
          : topicType // ignore: cast_nullable_to_non_nullable
              as int?,
      periods: freezed == periods
          ? _value.periods
          : periods // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLogisticVoListWithordeOrNameList
    with DiagnosticableTreeMixin
    implements _CourseLogisticVoListWithordeOrNameList {
  const _$_CourseLogisticVoListWithordeOrNameList(
      {final List<String>? orderName,
      this.orderNo,
      this.deliverTime,
      this.deliverTimeLong,
      this.deliverStatus,
      this.revocationCause,
      this.logisticsState,
      this.logisticsDesc,
      this.logisticsDetailUrl,
      this.recently,
      this.expressCompany,
      this.expressNumber,
      this.goodsType,
      this.topicType,
      this.periods})
      : _orderName = orderName;

  factory _$_CourseLogisticVoListWithordeOrNameList.fromJson(
          Map<String, dynamic> json) =>
      _$$_CourseLogisticVoListWithordeOrNameListFromJson(json);

  final List<String>? _orderName;
  @override
  List<String>? get orderName {
    final value = _orderName;
    if (value == null) return null;
    if (_orderName is EqualUnmodifiableListView) return _orderName;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? orderNo;
  @override
  final String? deliverTime;
  @override
  final int? deliverTimeLong;
  @override
  final String? deliverStatus;
  @override
  final String? revocationCause;
  @override
  final String? logisticsState;
  @override
  final String? logisticsDesc;
  @override
  final String? logisticsDetailUrl;
  @override
  final bool? recently;
  @override
  final String? expressCompany;
  @override
  final String? expressNumber;
  @override
  final int? goodsType;
  @override
  final int? topicType;
  @override
  final String? periods;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CourseLogisticVoListWithordeOrNameList(orderName: $orderName, orderNo: $orderNo, deliverTime: $deliverTime, deliverTimeLong: $deliverTimeLong, deliverStatus: $deliverStatus, revocationCause: $revocationCause, logisticsState: $logisticsState, logisticsDesc: $logisticsDesc, logisticsDetailUrl: $logisticsDetailUrl, recently: $recently, expressCompany: $expressCompany, expressNumber: $expressNumber, goodsType: $goodsType, topicType: $topicType, periods: $periods)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'CourseLogisticVoListWithordeOrNameList'))
      ..add(DiagnosticsProperty('orderName', orderName))
      ..add(DiagnosticsProperty('orderNo', orderNo))
      ..add(DiagnosticsProperty('deliverTime', deliverTime))
      ..add(DiagnosticsProperty('deliverTimeLong', deliverTimeLong))
      ..add(DiagnosticsProperty('deliverStatus', deliverStatus))
      ..add(DiagnosticsProperty('revocationCause', revocationCause))
      ..add(DiagnosticsProperty('logisticsState', logisticsState))
      ..add(DiagnosticsProperty('logisticsDesc', logisticsDesc))
      ..add(DiagnosticsProperty('logisticsDetailUrl', logisticsDetailUrl))
      ..add(DiagnosticsProperty('recently', recently))
      ..add(DiagnosticsProperty('expressCompany', expressCompany))
      ..add(DiagnosticsProperty('expressNumber', expressNumber))
      ..add(DiagnosticsProperty('goodsType', goodsType))
      ..add(DiagnosticsProperty('topicType', topicType))
      ..add(DiagnosticsProperty('periods', periods));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseLogisticVoListWithordeOrNameList &&
            const DeepCollectionEquality()
                .equals(other._orderName, _orderName) &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo) &&
            (identical(other.deliverTime, deliverTime) ||
                other.deliverTime == deliverTime) &&
            (identical(other.deliverTimeLong, deliverTimeLong) ||
                other.deliverTimeLong == deliverTimeLong) &&
            (identical(other.deliverStatus, deliverStatus) ||
                other.deliverStatus == deliverStatus) &&
            (identical(other.revocationCause, revocationCause) ||
                other.revocationCause == revocationCause) &&
            (identical(other.logisticsState, logisticsState) ||
                other.logisticsState == logisticsState) &&
            (identical(other.logisticsDesc, logisticsDesc) ||
                other.logisticsDesc == logisticsDesc) &&
            (identical(other.logisticsDetailUrl, logisticsDetailUrl) ||
                other.logisticsDetailUrl == logisticsDetailUrl) &&
            (identical(other.recently, recently) ||
                other.recently == recently) &&
            (identical(other.expressCompany, expressCompany) ||
                other.expressCompany == expressCompany) &&
            (identical(other.expressNumber, expressNumber) ||
                other.expressNumber == expressNumber) &&
            (identical(other.goodsType, goodsType) ||
                other.goodsType == goodsType) &&
            (identical(other.topicType, topicType) ||
                other.topicType == topicType) &&
            (identical(other.periods, periods) || other.periods == periods));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_orderName),
      orderNo,
      deliverTime,
      deliverTimeLong,
      deliverStatus,
      revocationCause,
      logisticsState,
      logisticsDesc,
      logisticsDetailUrl,
      recently,
      expressCompany,
      expressNumber,
      goodsType,
      topicType,
      periods);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLogisticVoListWithordeOrNameListCopyWith<
          _$_CourseLogisticVoListWithordeOrNameList>
      get copyWith => __$$_CourseLogisticVoListWithordeOrNameListCopyWithImpl<
          _$_CourseLogisticVoListWithordeOrNameList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLogisticVoListWithordeOrNameListToJson(
      this,
    );
  }
}

abstract class _CourseLogisticVoListWithordeOrNameList
    implements CourseLogisticVoListWithordeOrNameList {
  const factory _CourseLogisticVoListWithordeOrNameList(
      {final List<String>? orderName,
      final String? orderNo,
      final String? deliverTime,
      final int? deliverTimeLong,
      final String? deliverStatus,
      final String? revocationCause,
      final String? logisticsState,
      final String? logisticsDesc,
      final String? logisticsDetailUrl,
      final bool? recently,
      final String? expressCompany,
      final String? expressNumber,
      final int? goodsType,
      final int? topicType,
      final String? periods}) = _$_CourseLogisticVoListWithordeOrNameList;

  factory _CourseLogisticVoListWithordeOrNameList.fromJson(
          Map<String, dynamic> json) =
      _$_CourseLogisticVoListWithordeOrNameList.fromJson;

  @override
  List<String>? get orderName;
  @override
  String? get orderNo;
  @override
  String? get deliverTime;
  @override
  int? get deliverTimeLong;
  @override
  String? get deliverStatus;
  @override
  String? get revocationCause;
  @override
  String? get logisticsState;
  @override
  String? get logisticsDesc;
  @override
  String? get logisticsDetailUrl;
  @override
  bool? get recently;
  @override
  String? get expressCompany;
  @override
  String? get expressNumber;
  @override
  int? get goodsType;
  @override
  int? get topicType;
  @override
  String? get periods;
  @override
  @JsonKey(ignore: true)
  _$$_CourseLogisticVoListWithordeOrNameListCopyWith<
          _$_CourseLogisticVoListWithordeOrNameList>
      get copyWith => throw _privateConstructorUsedError;
}

ExtendBar _$ExtendBarFromJson(Map<String, dynamic> json) {
  return _ExtendBar.fromJson(json);
}

/// @nodoc
mixin _$ExtendBar {
  List<ContactCustomerContentList>? get contactCustomerContentList =>
      throw _privateConstructorUsedError;
  dynamic get haveCourseStatus => throw _privateConstructorUsedError;
  dynamic get leftFuncList => throw _privateConstructorUsedError;
  dynamic get rightToolList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtendBarCopyWith<ExtendBar> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtendBarCopyWith<$Res> {
  factory $ExtendBarCopyWith(ExtendBar value, $Res Function(ExtendBar) then) =
      _$ExtendBarCopyWithImpl<$Res, ExtendBar>;
  @useResult
  $Res call(
      {List<ContactCustomerContentList>? contactCustomerContentList,
      dynamic haveCourseStatus,
      dynamic leftFuncList,
      dynamic rightToolList});
}

/// @nodoc
class _$ExtendBarCopyWithImpl<$Res, $Val extends ExtendBar>
    implements $ExtendBarCopyWith<$Res> {
  _$ExtendBarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactCustomerContentList = freezed,
    Object? haveCourseStatus = freezed,
    Object? leftFuncList = freezed,
    Object? rightToolList = freezed,
  }) {
    return _then(_value.copyWith(
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value.contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<ContactCustomerContentList>?,
      haveCourseStatus: freezed == haveCourseStatus
          ? _value.haveCourseStatus
          : haveCourseStatus // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leftFuncList: freezed == leftFuncList
          ? _value.leftFuncList
          : leftFuncList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      rightToolList: freezed == rightToolList
          ? _value.rightToolList
          : rightToolList // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExtendBarCopyWith<$Res> implements $ExtendBarCopyWith<$Res> {
  factory _$$_ExtendBarCopyWith(
          _$_ExtendBar value, $Res Function(_$_ExtendBar) then) =
      __$$_ExtendBarCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ContactCustomerContentList>? contactCustomerContentList,
      dynamic haveCourseStatus,
      dynamic leftFuncList,
      dynamic rightToolList});
}

/// @nodoc
class __$$_ExtendBarCopyWithImpl<$Res>
    extends _$ExtendBarCopyWithImpl<$Res, _$_ExtendBar>
    implements _$$_ExtendBarCopyWith<$Res> {
  __$$_ExtendBarCopyWithImpl(
      _$_ExtendBar _value, $Res Function(_$_ExtendBar) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactCustomerContentList = freezed,
    Object? haveCourseStatus = freezed,
    Object? leftFuncList = freezed,
    Object? rightToolList = freezed,
  }) {
    return _then(_$_ExtendBar(
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value._contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<ContactCustomerContentList>?,
      haveCourseStatus: freezed == haveCourseStatus
          ? _value.haveCourseStatus
          : haveCourseStatus // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leftFuncList: freezed == leftFuncList
          ? _value.leftFuncList
          : leftFuncList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      rightToolList: freezed == rightToolList
          ? _value.rightToolList
          : rightToolList // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExtendBar with DiagnosticableTreeMixin implements _ExtendBar {
  const _$_ExtendBar(
      {final List<ContactCustomerContentList>? contactCustomerContentList,
      this.haveCourseStatus,
      this.leftFuncList,
      this.rightToolList})
      : _contactCustomerContentList = contactCustomerContentList;

  factory _$_ExtendBar.fromJson(Map<String, dynamic> json) =>
      _$$_ExtendBarFromJson(json);

  final List<ContactCustomerContentList>? _contactCustomerContentList;
  @override
  List<ContactCustomerContentList>? get contactCustomerContentList {
    final value = _contactCustomerContentList;
    if (value == null) return null;
    if (_contactCustomerContentList is EqualUnmodifiableListView)
      return _contactCustomerContentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final dynamic haveCourseStatus;
  @override
  final dynamic leftFuncList;
  @override
  final dynamic rightToolList;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ExtendBar(contactCustomerContentList: $contactCustomerContentList, haveCourseStatus: $haveCourseStatus, leftFuncList: $leftFuncList, rightToolList: $rightToolList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ExtendBar'))
      ..add(DiagnosticsProperty(
          'contactCustomerContentList', contactCustomerContentList))
      ..add(DiagnosticsProperty('haveCourseStatus', haveCourseStatus))
      ..add(DiagnosticsProperty('leftFuncList', leftFuncList))
      ..add(DiagnosticsProperty('rightToolList', rightToolList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExtendBar &&
            const DeepCollectionEquality().equals(
                other._contactCustomerContentList,
                _contactCustomerContentList) &&
            const DeepCollectionEquality()
                .equals(other.haveCourseStatus, haveCourseStatus) &&
            const DeepCollectionEquality()
                .equals(other.leftFuncList, leftFuncList) &&
            const DeepCollectionEquality()
                .equals(other.rightToolList, rightToolList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_contactCustomerContentList),
      const DeepCollectionEquality().hash(haveCourseStatus),
      const DeepCollectionEquality().hash(leftFuncList),
      const DeepCollectionEquality().hash(rightToolList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtendBarCopyWith<_$_ExtendBar> get copyWith =>
      __$$_ExtendBarCopyWithImpl<_$_ExtendBar>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtendBarToJson(
      this,
    );
  }
}

abstract class _ExtendBar implements ExtendBar {
  const factory _ExtendBar(
      {final List<ContactCustomerContentList>? contactCustomerContentList,
      final dynamic haveCourseStatus,
      final dynamic leftFuncList,
      final dynamic rightToolList}) = _$_ExtendBar;

  factory _ExtendBar.fromJson(Map<String, dynamic> json) =
      _$_ExtendBar.fromJson;

  @override
  List<ContactCustomerContentList>? get contactCustomerContentList;
  @override
  dynamic get haveCourseStatus;
  @override
  dynamic get leftFuncList;
  @override
  dynamic get rightToolList;
  @override
  @JsonKey(ignore: true)
  _$$_ExtendBarCopyWith<_$_ExtendBar> get copyWith =>
      throw _privateConstructorUsedError;
}

ContactCustomerContentList _$ContactCustomerContentListFromJson(
    Map<String, dynamic> json) {
  return _ContactCustomerContentList.fromJson(json);
}

/// @nodoc
mixin _$ContactCustomerContentList {
  String? get color => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactCustomerContentListCopyWith<ContactCustomerContentList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactCustomerContentListCopyWith<$Res> {
  factory $ContactCustomerContentListCopyWith(ContactCustomerContentList value,
          $Res Function(ContactCustomerContentList) then) =
      _$ContactCustomerContentListCopyWithImpl<$Res,
          ContactCustomerContentList>;
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class _$ContactCustomerContentListCopyWithImpl<$Res,
        $Val extends ContactCustomerContentList>
    implements $ContactCustomerContentListCopyWith<$Res> {
  _$ContactCustomerContentListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_value.copyWith(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContactCustomerContentListCopyWith<$Res>
    implements $ContactCustomerContentListCopyWith<$Res> {
  factory _$$_ContactCustomerContentListCopyWith(
          _$_ContactCustomerContentList value,
          $Res Function(_$_ContactCustomerContentList) then) =
      __$$_ContactCustomerContentListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class __$$_ContactCustomerContentListCopyWithImpl<$Res>
    extends _$ContactCustomerContentListCopyWithImpl<$Res,
        _$_ContactCustomerContentList>
    implements _$$_ContactCustomerContentListCopyWith<$Res> {
  __$$_ContactCustomerContentListCopyWithImpl(
      _$_ContactCustomerContentList _value,
      $Res Function(_$_ContactCustomerContentList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_$_ContactCustomerContentList(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContactCustomerContentList
    with DiagnosticableTreeMixin
    implements _ContactCustomerContentList {
  const _$_ContactCustomerContentList({this.color, this.text, this.linkUrl});

  factory _$_ContactCustomerContentList.fromJson(Map<String, dynamic> json) =>
      _$$_ContactCustomerContentListFromJson(json);

  @override
  final String? color;
  @override
  final String? text;
  @override
  final String? linkUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ContactCustomerContentList(color: $color, text: $text, linkUrl: $linkUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ContactCustomerContentList'))
      ..add(DiagnosticsProperty('color', color))
      ..add(DiagnosticsProperty('text', text))
      ..add(DiagnosticsProperty('linkUrl', linkUrl));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContactCustomerContentList &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, color, text, linkUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContactCustomerContentListCopyWith<_$_ContactCustomerContentList>
      get copyWith => __$$_ContactCustomerContentListCopyWithImpl<
          _$_ContactCustomerContentList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContactCustomerContentListToJson(
      this,
    );
  }
}

abstract class _ContactCustomerContentList
    implements ContactCustomerContentList {
  const factory _ContactCustomerContentList(
      {final String? color,
      final String? text,
      final String? linkUrl}) = _$_ContactCustomerContentList;

  factory _ContactCustomerContentList.fromJson(Map<String, dynamic> json) =
      _$_ContactCustomerContentList.fromJson;

  @override
  String? get color;
  @override
  String? get text;
  @override
  String? get linkUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ContactCustomerContentListCopyWith<_$_ContactCustomerContentList>
      get copyWith => throw _privateConstructorUsedError;
}
