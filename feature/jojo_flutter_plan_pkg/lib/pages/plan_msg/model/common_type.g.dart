// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'common_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CommonData _$$_CommonDataFromJson(Map<String, dynamic> json) =>
    _$_CommonData(
      courseChildType: json['courseChildType'] as int?,
      classKey: json['classKey'] as String?,
      classId: json['classId'] as int?,
      subjectType: json['subjectType'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      courseLabel: json['courseLabel'] as String?,
      courseTypeIcon: json['courseTypeIcon'] as String?,
      teacherId: json['teacherId'] as int?,
      teacherName: json['teacherName'] as String?,
      activateStatus: json['activateStatus'] as int?,
      currentSegmentId: json['currentSegmentId'] as int?,
      isLogisticsTab: json['isLogisticsTab'] as bool?,
      rollLabelInfo: json['rollLabelInfo'] == null
          ? null
          : RollLabelInfo.fromJson(
              json['rollLabelInfo'] as Map<String, dynamic>),
      tabList: (json['tabList'] as List<dynamic>?)
          ?.map((e) => TabList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CommonDataToJson(_$_CommonData instance) =>
    <String, dynamic>{
      'courseChildType': instance.courseChildType,
      'classKey': instance.classKey,
      'classId': instance.classId,
      'subjectType': instance.subjectType,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'courseLabel': instance.courseLabel,
      'courseTypeIcon': instance.courseTypeIcon,
      'teacherId': instance.teacherId,
      'teacherName': instance.teacherName,
      'activateStatus': instance.activateStatus,
      'currentSegmentId': instance.currentSegmentId,
      'isLogisticsTab': instance.isLogisticsTab,
      'rollLabelInfo': instance.rollLabelInfo,
      'tabList': instance.tabList,
    };

_$_TabList _$$_TabListFromJson(Map<String, dynamic> json) => _$_TabList(
      type: json['type'] as int?,
      tabName: json['tabName'] as String?,
      selected: json['selected'] as bool?,
    );

Map<String, dynamic> _$$_TabListToJson(_$_TabList instance) =>
    <String, dynamic>{
      'type': instance.type,
      'tabName': instance.tabName,
      'selected': instance.selected,
    };

_$_RollLabelInfo _$$_RollLabelInfoFromJson(Map<String, dynamic> json) =>
    _$_RollLabelInfo(
      currentRollLabel: json['currentRollLabel'] as String?,
      rollLabelList: (json['rollLabelList'] as List<dynamic>?)
          ?.map((e) => RollLabelList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_RollLabelInfoToJson(_$_RollLabelInfo instance) =>
    <String, dynamic>{
      'currentRollLabel': instance.currentRollLabel,
      'rollLabelList': instance.rollLabelList,
    };

_$_RollLabelList _$$_RollLabelListFromJson(Map<String, dynamic> json) =>
    _$_RollLabelList(
      classId: json['classId'] as int?,
      rollLabelName: json['rollLabelName'] as String?,
      segmentIdJson: json['segmentIdJson'] as String?,
    );

Map<String, dynamic> _$$_RollLabelListToJson(_$_RollLabelList instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'rollLabelName': instance.rollLabelName,
      'segmentIdJson': instance.segmentIdJson,
    };
