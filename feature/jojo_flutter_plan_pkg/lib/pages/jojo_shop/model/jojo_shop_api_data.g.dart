// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jojo_shop_api_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LearnBean _$$_LearnBeanFromJson(Map<String, dynamic> json) => _$_LearnBean(
      amount: json['amount'] as int?,
      icon: json['icon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
      rightIcon: json['rightIcon'] as String?,
      rightBtnText: json['rightBtnText'] as String?,
      rightBtnRoute: json['rightBtnRoute'] as String?,
    );

Map<String, dynamic> _$$_LearnBeanToJson(_$_LearnBean instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'icon': instance.icon,
      'jumpRoute': instance.jumpRoute,
      'rightIcon': instance.rightIcon,
      'rightBtnText': instance.rightBtnText,
      'rightBtnRoute': instance.rightBtnRoute,
    };

_$_ModuleItem _$$_ModuleItemFromJson(Map<String, dynamic> json) =>
    _$_ModuleItem(
      name: json['name'] as String?,
      img: json['img'] as String?,
      price: json['price'] as String?,
      priceIcon: json['priceIcon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_ModuleItemToJson(_$_ModuleItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'img': instance.img,
      'price': instance.price,
      'priceIcon': instance.priceIcon,
      'jumpRoute': instance.jumpRoute,
    };

_$_BannerModule _$$_BannerModuleFromJson(Map<String, dynamic> json) =>
    _$_BannerModule(
      bannerImg: json['bannerImg'] as String?,
      bannerJumpRoute: json['bannerJumpRoute'] as String?,
    );

Map<String, dynamic> _$$_BannerModuleToJson(_$_BannerModule instance) =>
    <String, dynamic>{
      'bannerImg': instance.bannerImg,
      'bannerJumpRoute': instance.bannerJumpRoute,
    };

_$ItemModule _$$ItemModuleFromJson(Map<String, dynamic> json) => _$ItemModule(
      type: json['type'] as String?,
      name: json['name'] as String?,
      more: json['more'] as String?,
      moreJumpRoute: json['moreJumpRoute'] as String?,
      bannerImg: json['bannerImg'] as String?,
      bannerJumpRoute: json['bannerJumpRoute'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ModuleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ItemModuleToJson(_$ItemModule instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'more': instance.more,
      'moreJumpRoute': instance.moreJumpRoute,
      'bannerImg': instance.bannerImg,
      'bannerJumpRoute': instance.bannerJumpRoute,
      'items': instance.items,
    };

_$_ShopDataModel _$$_ShopDataModelFromJson(Map<String, dynamic> json) =>
    _$_ShopDataModel(
      learnBean: json['learnBean'] == null
          ? null
          : LearnBean.fromJson(json['learnBean'] as Map<String, dynamic>),
      modules: (json['modules'] as List<dynamic>?)
          ?.map((e) => Module.fromJson(e as Map<String, dynamic>))
          .toList(),
      segmentList: (json['segmentList'] as List<dynamic>?)
          ?.map((e) => Segment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ShopDataModelToJson(_$_ShopDataModel instance) =>
    <String, dynamic>{
      'learnBean': instance.learnBean,
      'modules': instance.modules,
      'segmentList': instance.segmentList,
    };

_$_Segment _$$_SegmentFromJson(Map<String, dynamic> json) => _$_Segment(
      name: json['name'] as String?,
      code: json['code'] as int?,
    );

Map<String, dynamic> _$$_SegmentToJson(_$_Segment instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
    };

_$_PropsShopDataModel _$$_PropsShopDataModelFromJson(
        Map<String, dynamic> json) =>
    _$_PropsShopDataModel(
      useAssetType: json['useAssetType'] as int?,
      totalPropCount: json['totalPropCount'] as int?,
      balance: json['balance'] as int?,
      icon: json['icon'] as String?,
      props: (json['props'] as List<dynamic>?)
          ?.map(
              (e) => PropsShopDataItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      purchaseSwitch: json['purchaseSwitch'] as int?,
    );

Map<String, dynamic> _$$_PropsShopDataModelToJson(
        _$_PropsShopDataModel instance) =>
    <String, dynamic>{
      'useAssetType': instance.useAssetType,
      'totalPropCount': instance.totalPropCount,
      'balance': instance.balance,
      'icon': instance.icon,
      'props': instance.props,
      'purchaseSwitch': instance.purchaseSwitch,
    };

_$_PropsShopDataItemModel _$$_PropsShopDataItemModelFromJson(
        Map<String, dynamic> json) =>
    _$_PropsShopDataItemModel(
      type: json['type'] as int?,
      ownedCount: json['ownedCount'] as int?,
      price: json['price'] as int?,
      onShelf: json['onShelf'] as bool?,
      commodity: json['commodity'] as bool?,
      name: json['name'] as String?,
      id: json['id'] as String?,
      icon: json['icon'] as String?,
      functionDesc: json['functionDesc'] as String?,
      sourceDesc: json['sourceDesc'] as String?,
    );

Map<String, dynamic> _$$_PropsShopDataItemModelToJson(
        _$_PropsShopDataItemModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'ownedCount': instance.ownedCount,
      'price': instance.price,
      'onShelf': instance.onShelf,
      'commodity': instance.commodity,
      'name': instance.name,
      'id': instance.id,
      'icon': instance.icon,
      'functionDesc': instance.functionDesc,
      'sourceDesc': instance.sourceDesc,
    };

_$_PropsOrderStatusModel _$$_PropsOrderStatusModelFromJson(
        Map<String, dynamic> json) =>
    _$_PropsOrderStatusModel(
      orderStatus: json['orderStatus'] as int?,
      orderId: json['orderId'] as String?,
      failReason: json['failReason'] as String?,
      batchId: json['batchId'] as String?,
    );

Map<String, dynamic> _$$_PropsOrderStatusModelToJson(
        _$_PropsOrderStatusModel instance) =>
    <String, dynamic>{
      'orderStatus': instance.orderStatus,
      'orderId': instance.orderId,
      'failReason': instance.failReason,
      'batchId': instance.batchId,
    };
