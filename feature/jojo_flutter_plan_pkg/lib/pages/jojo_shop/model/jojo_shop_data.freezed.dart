// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jojo_shop_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ShopItem _$ShopItemFromJson(Map<String, dynamic> json) {
  return _ShopItem.fromJson(json);
}

/// @nodoc
mixin _$ShopItem {
  String? get icon => throw _privateConstructorUsedError; //道具图标
  String? get priceIcon => throw _privateConstructorUsedError; //道具价格图标
  String? get price => throw _privateConstructorUsedError; //道具价格
  String? get title => throw _privateConstructorUsedError; //道具标题
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShopItemCopyWith<ShopItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopItemCopyWith<$Res> {
  factory $ShopItemCopyWith(ShopItem value, $Res Function(ShopItem) then) =
      _$ShopItemCopyWithImpl<$Res, ShopItem>;
  @useResult
  $Res call(
      {String? icon,
      String? priceIcon,
      String? price,
      String? title,
      String? route});
}

/// @nodoc
class _$ShopItemCopyWithImpl<$Res, $Val extends ShopItem>
    implements $ShopItemCopyWith<$Res> {
  _$ShopItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? priceIcon = freezed,
    Object? price = freezed,
    Object? title = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShopItemCopyWith<$Res> implements $ShopItemCopyWith<$Res> {
  factory _$$_ShopItemCopyWith(
          _$_ShopItem value, $Res Function(_$_ShopItem) then) =
      __$$_ShopItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? icon,
      String? priceIcon,
      String? price,
      String? title,
      String? route});
}

/// @nodoc
class __$$_ShopItemCopyWithImpl<$Res>
    extends _$ShopItemCopyWithImpl<$Res, _$_ShopItem>
    implements _$$_ShopItemCopyWith<$Res> {
  __$$_ShopItemCopyWithImpl(
      _$_ShopItem _value, $Res Function(_$_ShopItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? priceIcon = freezed,
    Object? price = freezed,
    Object? title = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_ShopItem(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShopItem implements _ShopItem {
  _$_ShopItem({this.icon, this.priceIcon, this.price, this.title, this.route});

  factory _$_ShopItem.fromJson(Map<String, dynamic> json) =>
      _$$_ShopItemFromJson(json);

  @override
  final String? icon;
//道具图标
  @override
  final String? priceIcon;
//道具价格图标
  @override
  final String? price;
//道具价格
  @override
  final String? title;
//道具标题
  @override
  final String? route;

  @override
  String toString() {
    return 'ShopItem(icon: $icon, priceIcon: $priceIcon, price: $price, title: $title, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShopItem &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.priceIcon, priceIcon) ||
                other.priceIcon == priceIcon) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, icon, priceIcon, price, title, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShopItemCopyWith<_$_ShopItem> get copyWith =>
      __$$_ShopItemCopyWithImpl<_$_ShopItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShopItemToJson(
      this,
    );
  }
}

abstract class _ShopItem implements ShopItem {
  factory _ShopItem(
      {final String? icon,
      final String? priceIcon,
      final String? price,
      final String? title,
      final String? route}) = _$_ShopItem;

  factory _ShopItem.fromJson(Map<String, dynamic> json) = _$_ShopItem.fromJson;

  @override
  String? get icon;
  @override //道具图标
  String? get priceIcon;
  @override //道具价格图标
  String? get price;
  @override //道具价格
  String? get title;
  @override //道具标题
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_ShopItemCopyWith<_$_ShopItem> get copyWith =>
      throw _privateConstructorUsedError;
}

ShopData _$ShopDataFromJson(Map<String, dynamic> json) {
  return _ShopData.fromJson(json);
}

/// @nodoc
mixin _$ShopData {
  String? get title => throw _privateConstructorUsedError;
  String? get more => throw _privateConstructorUsedError;
  List<ShopItem>? get items => throw _privateConstructorUsedError;
  String? get moreRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShopDataCopyWith<ShopData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopDataCopyWith<$Res> {
  factory $ShopDataCopyWith(ShopData value, $Res Function(ShopData) then) =
      _$ShopDataCopyWithImpl<$Res, ShopData>;
  @useResult
  $Res call(
      {String? title, String? more, List<ShopItem>? items, String? moreRoute});
}

/// @nodoc
class _$ShopDataCopyWithImpl<$Res, $Val extends ShopData>
    implements $ShopDataCopyWith<$Res> {
  _$ShopDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? more = freezed,
    Object? items = freezed,
    Object? moreRoute = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShopItem>?,
      moreRoute: freezed == moreRoute
          ? _value.moreRoute
          : moreRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShopDataCopyWith<$Res> implements $ShopDataCopyWith<$Res> {
  factory _$$_ShopDataCopyWith(
          _$_ShopData value, $Res Function(_$_ShopData) then) =
      __$$_ShopDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title, String? more, List<ShopItem>? items, String? moreRoute});
}

/// @nodoc
class __$$_ShopDataCopyWithImpl<$Res>
    extends _$ShopDataCopyWithImpl<$Res, _$_ShopData>
    implements _$$_ShopDataCopyWith<$Res> {
  __$$_ShopDataCopyWithImpl(
      _$_ShopData _value, $Res Function(_$_ShopData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? more = freezed,
    Object? items = freezed,
    Object? moreRoute = freezed,
  }) {
    return _then(_$_ShopData(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShopItem>?,
      moreRoute: freezed == moreRoute
          ? _value.moreRoute
          : moreRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShopData implements _ShopData {
  _$_ShopData(
      {this.title, this.more, final List<ShopItem>? items, this.moreRoute})
      : _items = items;

  factory _$_ShopData.fromJson(Map<String, dynamic> json) =>
      _$$_ShopDataFromJson(json);

  @override
  final String? title;
  @override
  final String? more;
  final List<ShopItem>? _items;
  @override
  List<ShopItem>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? moreRoute;

  @override
  String toString() {
    return 'ShopData(title: $title, more: $more, items: $items, moreRoute: $moreRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShopData &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.more, more) || other.more == more) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.moreRoute, moreRoute) ||
                other.moreRoute == moreRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, more,
      const DeepCollectionEquality().hash(_items), moreRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShopDataCopyWith<_$_ShopData> get copyWith =>
      __$$_ShopDataCopyWithImpl<_$_ShopData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShopDataToJson(
      this,
    );
  }
}

abstract class _ShopData implements ShopData {
  factory _ShopData(
      {final String? title,
      final String? more,
      final List<ShopItem>? items,
      final String? moreRoute}) = _$_ShopData;

  factory _ShopData.fromJson(Map<String, dynamic> json) = _$_ShopData.fromJson;

  @override
  String? get title;
  @override
  String? get more;
  @override
  List<ShopItem>? get items;
  @override
  String? get moreRoute;
  @override
  @JsonKey(ignore: true)
  _$$_ShopDataCopyWith<_$_ShopData> get copyWith =>
      throw _privateConstructorUsedError;
}

BannerData _$BannerDataFromJson(Map<String, dynamic> json) {
  return _BannerData.fromJson(json);
}

/// @nodoc
mixin _$BannerData {
  String? get name => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BannerDataCopyWith<BannerData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerDataCopyWith<$Res> {
  factory $BannerDataCopyWith(
          BannerData value, $Res Function(BannerData) then) =
      _$BannerDataCopyWithImpl<$Res, BannerData>;
  @useResult
  $Res call({String? name, String? image, String? route});
}

/// @nodoc
class _$BannerDataCopyWithImpl<$Res, $Val extends BannerData>
    implements $BannerDataCopyWith<$Res> {
  _$BannerDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? image = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BannerDataCopyWith<$Res>
    implements $BannerDataCopyWith<$Res> {
  factory _$$_BannerDataCopyWith(
          _$_BannerData value, $Res Function(_$_BannerData) then) =
      __$$_BannerDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? image, String? route});
}

/// @nodoc
class __$$_BannerDataCopyWithImpl<$Res>
    extends _$BannerDataCopyWithImpl<$Res, _$_BannerData>
    implements _$$_BannerDataCopyWith<$Res> {
  __$$_BannerDataCopyWithImpl(
      _$_BannerData _value, $Res Function(_$_BannerData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? image = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_BannerData(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BannerData implements _BannerData {
  _$_BannerData({this.name, this.image, this.route});

  factory _$_BannerData.fromJson(Map<String, dynamic> json) =>
      _$$_BannerDataFromJson(json);

  @override
  final String? name;
  @override
  final String? image;
  @override
  final String? route;

  @override
  String toString() {
    return 'BannerData(name: $name, image: $image, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BannerData &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, image, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BannerDataCopyWith<_$_BannerData> get copyWith =>
      __$$_BannerDataCopyWithImpl<_$_BannerData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BannerDataToJson(
      this,
    );
  }
}

abstract class _BannerData implements BannerData {
  factory _BannerData(
      {final String? name,
      final String? image,
      final String? route}) = _$_BannerData;

  factory _BannerData.fromJson(Map<String, dynamic> json) =
      _$_BannerData.fromJson;

  @override
  String? get name;
  @override
  String? get image;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_BannerDataCopyWith<_$_BannerData> get copyWith =>
      throw _privateConstructorUsedError;
}

HeadData _$HeadDataFromJson(Map<String, dynamic> json) {
  return _HeadData.fromJson(json);
}

/// @nodoc
mixin _$HeadData {
  int? get amount => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get rightIcon => throw _privateConstructorUsedError;
  String? get rightBtnText => throw _privateConstructorUsedError;
  String? get rightBtnRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HeadDataCopyWith<HeadData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HeadDataCopyWith<$Res> {
  factory $HeadDataCopyWith(HeadData value, $Res Function(HeadData) then) =
      _$HeadDataCopyWithImpl<$Res, HeadData>;
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class _$HeadDataCopyWithImpl<$Res, $Val extends HeadData>
    implements $HeadDataCopyWith<$Res> {
  _$HeadDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_HeadDataCopyWith<$Res> implements $HeadDataCopyWith<$Res> {
  factory _$$_HeadDataCopyWith(
          _$_HeadData value, $Res Function(_$_HeadData) then) =
      __$$_HeadDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class __$$_HeadDataCopyWithImpl<$Res>
    extends _$HeadDataCopyWithImpl<$Res, _$_HeadData>
    implements _$$_HeadDataCopyWith<$Res> {
  __$$_HeadDataCopyWithImpl(
      _$_HeadData _value, $Res Function(_$_HeadData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_$_HeadData(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_HeadData implements _HeadData {
  _$_HeadData(
      {this.amount,
      this.icon,
      this.jumpRoute,
      this.rightIcon,
      this.rightBtnText,
      this.rightBtnRoute});

  factory _$_HeadData.fromJson(Map<String, dynamic> json) =>
      _$$_HeadDataFromJson(json);

  @override
  final int? amount;
  @override
  final String? icon;
  @override
  final String? jumpRoute;
  @override
  final String? rightIcon;
  @override
  final String? rightBtnText;
  @override
  final String? rightBtnRoute;

  @override
  String toString() {
    return 'HeadData(amount: $amount, icon: $icon, jumpRoute: $jumpRoute, rightIcon: $rightIcon, rightBtnText: $rightBtnText, rightBtnRoute: $rightBtnRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HeadData &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.rightIcon, rightIcon) ||
                other.rightIcon == rightIcon) &&
            (identical(other.rightBtnText, rightBtnText) ||
                other.rightBtnText == rightBtnText) &&
            (identical(other.rightBtnRoute, rightBtnRoute) ||
                other.rightBtnRoute == rightBtnRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, amount, icon, jumpRoute,
      rightIcon, rightBtnText, rightBtnRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HeadDataCopyWith<_$_HeadData> get copyWith =>
      __$$_HeadDataCopyWithImpl<_$_HeadData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_HeadDataToJson(
      this,
    );
  }
}

abstract class _HeadData implements HeadData {
  factory _HeadData(
      {final int? amount,
      final String? icon,
      final String? jumpRoute,
      final String? rightIcon,
      final String? rightBtnText,
      final String? rightBtnRoute}) = _$_HeadData;

  factory _HeadData.fromJson(Map<String, dynamic> json) = _$_HeadData.fromJson;

  @override
  int? get amount;
  @override
  String? get icon;
  @override
  String? get jumpRoute;
  @override
  String? get rightIcon;
  @override
  String? get rightBtnText;
  @override
  String? get rightBtnRoute;
  @override
  @JsonKey(ignore: true)
  _$$_HeadDataCopyWith<_$_HeadData> get copyWith =>
      throw _privateConstructorUsedError;
}
