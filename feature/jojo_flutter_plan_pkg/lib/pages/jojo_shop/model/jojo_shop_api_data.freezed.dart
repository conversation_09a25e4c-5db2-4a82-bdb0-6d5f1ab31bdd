// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jojo_shop_api_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LearnBean _$LearnBeanFromJson(Map<String, dynamic> json) {
  return _LearnBean.fromJson(json);
}

/// @nodoc
mixin _$LearnBean {
  int? get amount => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get rightIcon => throw _privateConstructorUsedError;
  String? get rightBtnText => throw _privateConstructorUsedError;
  String? get rightBtnRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearnBeanCopyWith<LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearnBeanCopyWith<$Res> {
  factory $LearnBeanCopyWith(LearnBean value, $Res Function(LearnBean) then) =
      _$LearnBeanCopyWithImpl<$Res, LearnBean>;
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class _$LearnBeanCopyWithImpl<$Res, $Val extends LearnBean>
    implements $LearnBeanCopyWith<$Res> {
  _$LearnBeanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearnBeanCopyWith<$Res> implements $LearnBeanCopyWith<$Res> {
  factory _$$_LearnBeanCopyWith(
          _$_LearnBean value, $Res Function(_$_LearnBean) then) =
      __$$_LearnBeanCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class __$$_LearnBeanCopyWithImpl<$Res>
    extends _$LearnBeanCopyWithImpl<$Res, _$_LearnBean>
    implements _$$_LearnBeanCopyWith<$Res> {
  __$$_LearnBeanCopyWithImpl(
      _$_LearnBean _value, $Res Function(_$_LearnBean) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_$_LearnBean(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearnBean implements _LearnBean {
  _$_LearnBean(
      {this.amount,
      this.icon,
      this.jumpRoute,
      this.rightIcon,
      this.rightBtnText,
      this.rightBtnRoute});

  factory _$_LearnBean.fromJson(Map<String, dynamic> json) =>
      _$$_LearnBeanFromJson(json);

  @override
  final int? amount;
  @override
  final String? icon;
  @override
  final String? jumpRoute;
  @override
  final String? rightIcon;
  @override
  final String? rightBtnText;
  @override
  final String? rightBtnRoute;

  @override
  String toString() {
    return 'LearnBean(amount: $amount, icon: $icon, jumpRoute: $jumpRoute, rightIcon: $rightIcon, rightBtnText: $rightBtnText, rightBtnRoute: $rightBtnRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LearnBean &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.rightIcon, rightIcon) ||
                other.rightIcon == rightIcon) &&
            (identical(other.rightBtnText, rightBtnText) ||
                other.rightBtnText == rightBtnText) &&
            (identical(other.rightBtnRoute, rightBtnRoute) ||
                other.rightBtnRoute == rightBtnRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, amount, icon, jumpRoute,
      rightIcon, rightBtnText, rightBtnRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      __$$_LearnBeanCopyWithImpl<_$_LearnBean>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearnBeanToJson(
      this,
    );
  }
}

abstract class _LearnBean implements LearnBean {
  factory _LearnBean(
      {final int? amount,
      final String? icon,
      final String? jumpRoute,
      final String? rightIcon,
      final String? rightBtnText,
      final String? rightBtnRoute}) = _$_LearnBean;

  factory _LearnBean.fromJson(Map<String, dynamic> json) =
      _$_LearnBean.fromJson;

  @override
  int? get amount;
  @override
  String? get icon;
  @override
  String? get jumpRoute;
  @override
  String? get rightIcon;
  @override
  String? get rightBtnText;
  @override
  String? get rightBtnRoute;
  @override
  @JsonKey(ignore: true)
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

ModuleItem _$ModuleItemFromJson(Map<String, dynamic> json) {
  return _ModuleItem.fromJson(json);
}

/// @nodoc
mixin _$ModuleItem {
  String? get name => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get price => throw _privateConstructorUsedError;
  String? get priceIcon => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ModuleItemCopyWith<ModuleItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModuleItemCopyWith<$Res> {
  factory $ModuleItemCopyWith(
          ModuleItem value, $Res Function(ModuleItem) then) =
      _$ModuleItemCopyWithImpl<$Res, ModuleItem>;
  @useResult
  $Res call(
      {String? name,
      String? img,
      String? price,
      String? priceIcon,
      String? jumpRoute});
}

/// @nodoc
class _$ModuleItemCopyWithImpl<$Res, $Val extends ModuleItem>
    implements $ModuleItemCopyWith<$Res> {
  _$ModuleItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
    Object? price = freezed,
    Object? priceIcon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ModuleItemCopyWith<$Res>
    implements $ModuleItemCopyWith<$Res> {
  factory _$$_ModuleItemCopyWith(
          _$_ModuleItem value, $Res Function(_$_ModuleItem) then) =
      __$$_ModuleItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name,
      String? img,
      String? price,
      String? priceIcon,
      String? jumpRoute});
}

/// @nodoc
class __$$_ModuleItemCopyWithImpl<$Res>
    extends _$ModuleItemCopyWithImpl<$Res, _$_ModuleItem>
    implements _$$_ModuleItemCopyWith<$Res> {
  __$$_ModuleItemCopyWithImpl(
      _$_ModuleItem _value, $Res Function(_$_ModuleItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
    Object? price = freezed,
    Object? priceIcon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_ModuleItem(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ModuleItem implements _ModuleItem {
  _$_ModuleItem(
      {this.name, this.img, this.price, this.priceIcon, this.jumpRoute});

  factory _$_ModuleItem.fromJson(Map<String, dynamic> json) =>
      _$$_ModuleItemFromJson(json);

  @override
  final String? name;
  @override
  final String? img;
  @override
  final String? price;
  @override
  final String? priceIcon;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'ModuleItem(name: $name, img: $img, price: $price, priceIcon: $priceIcon, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ModuleItem &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceIcon, priceIcon) ||
                other.priceIcon == priceIcon) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, img, price, priceIcon, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ModuleItemCopyWith<_$_ModuleItem> get copyWith =>
      __$$_ModuleItemCopyWithImpl<_$_ModuleItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ModuleItemToJson(
      this,
    );
  }
}

abstract class _ModuleItem implements ModuleItem {
  factory _ModuleItem(
      {final String? name,
      final String? img,
      final String? price,
      final String? priceIcon,
      final String? jumpRoute}) = _$_ModuleItem;

  factory _ModuleItem.fromJson(Map<String, dynamic> json) =
      _$_ModuleItem.fromJson;

  @override
  String? get name;
  @override
  String? get img;
  @override
  String? get price;
  @override
  String? get priceIcon;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_ModuleItemCopyWith<_$_ModuleItem> get copyWith =>
      throw _privateConstructorUsedError;
}

BannerModule _$BannerModuleFromJson(Map<String, dynamic> json) {
  return _BannerModule.fromJson(json);
}

/// @nodoc
mixin _$BannerModule {
  String? get bannerImg => throw _privateConstructorUsedError;
  String? get bannerJumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BannerModuleCopyWith<BannerModule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerModuleCopyWith<$Res> {
  factory $BannerModuleCopyWith(
          BannerModule value, $Res Function(BannerModule) then) =
      _$BannerModuleCopyWithImpl<$Res, BannerModule>;
  @useResult
  $Res call({String? bannerImg, String? bannerJumpRoute});
}

/// @nodoc
class _$BannerModuleCopyWithImpl<$Res, $Val extends BannerModule>
    implements $BannerModuleCopyWith<$Res> {
  _$BannerModuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BannerModuleCopyWith<$Res>
    implements $BannerModuleCopyWith<$Res> {
  factory _$$_BannerModuleCopyWith(
          _$_BannerModule value, $Res Function(_$_BannerModule) then) =
      __$$_BannerModuleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? bannerImg, String? bannerJumpRoute});
}

/// @nodoc
class __$$_BannerModuleCopyWithImpl<$Res>
    extends _$BannerModuleCopyWithImpl<$Res, _$_BannerModule>
    implements _$$_BannerModuleCopyWith<$Res> {
  __$$_BannerModuleCopyWithImpl(
      _$_BannerModule _value, $Res Function(_$_BannerModule) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
  }) {
    return _then(_$_BannerModule(
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BannerModule implements _BannerModule {
  _$_BannerModule({this.bannerImg, this.bannerJumpRoute});

  factory _$_BannerModule.fromJson(Map<String, dynamic> json) =>
      _$$_BannerModuleFromJson(json);

  @override
  final String? bannerImg;
  @override
  final String? bannerJumpRoute;

  @override
  String toString() {
    return 'BannerModule(bannerImg: $bannerImg, bannerJumpRoute: $bannerJumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BannerModule &&
            (identical(other.bannerImg, bannerImg) ||
                other.bannerImg == bannerImg) &&
            (identical(other.bannerJumpRoute, bannerJumpRoute) ||
                other.bannerJumpRoute == bannerJumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bannerImg, bannerJumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BannerModuleCopyWith<_$_BannerModule> get copyWith =>
      __$$_BannerModuleCopyWithImpl<_$_BannerModule>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BannerModuleToJson(
      this,
    );
  }
}

abstract class _BannerModule implements BannerModule {
  factory _BannerModule(
      {final String? bannerImg,
      final String? bannerJumpRoute}) = _$_BannerModule;

  factory _BannerModule.fromJson(Map<String, dynamic> json) =
      _$_BannerModule.fromJson;

  @override
  String? get bannerImg;
  @override
  String? get bannerJumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_BannerModuleCopyWith<_$_BannerModule> get copyWith =>
      throw _privateConstructorUsedError;
}

Module _$ModuleFromJson(Map<String, dynamic> json) {
  return ItemModule.fromJson(json);
}

/// @nodoc
mixin _$Module {
  String? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get more => throw _privateConstructorUsedError;
  String? get moreJumpRoute => throw _privateConstructorUsedError;
  String? get bannerImg => throw _privateConstructorUsedError;
  String? get bannerJumpRoute => throw _privateConstructorUsedError;
  List<ModuleItem>? get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ModuleCopyWith<Module> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModuleCopyWith<$Res> {
  factory $ModuleCopyWith(Module value, $Res Function(Module) then) =
      _$ModuleCopyWithImpl<$Res, Module>;
  @useResult
  $Res call(
      {String? type,
      String? name,
      String? more,
      String? moreJumpRoute,
      String? bannerImg,
      String? bannerJumpRoute,
      List<ModuleItem>? items});
}

/// @nodoc
class _$ModuleCopyWithImpl<$Res, $Val extends Module>
    implements $ModuleCopyWith<$Res> {
  _$ModuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? more = freezed,
    Object? moreJumpRoute = freezed,
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      moreJumpRoute: freezed == moreJumpRoute
          ? _value.moreJumpRoute
          : moreJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ModuleItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ItemModuleCopyWith<$Res> implements $ModuleCopyWith<$Res> {
  factory _$$ItemModuleCopyWith(
          _$ItemModule value, $Res Function(_$ItemModule) then) =
      __$$ItemModuleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? name,
      String? more,
      String? moreJumpRoute,
      String? bannerImg,
      String? bannerJumpRoute,
      List<ModuleItem>? items});
}

/// @nodoc
class __$$ItemModuleCopyWithImpl<$Res>
    extends _$ModuleCopyWithImpl<$Res, _$ItemModule>
    implements _$$ItemModuleCopyWith<$Res> {
  __$$ItemModuleCopyWithImpl(
      _$ItemModule _value, $Res Function(_$ItemModule) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? more = freezed,
    Object? moreJumpRoute = freezed,
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_$ItemModule(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      moreJumpRoute: freezed == moreJumpRoute
          ? _value.moreJumpRoute
          : moreJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ModuleItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ItemModule implements ItemModule {
  const _$ItemModule(
      {this.type,
      this.name,
      this.more,
      this.moreJumpRoute,
      this.bannerImg,
      this.bannerJumpRoute,
      final List<ModuleItem>? items})
      : _items = items;

  factory _$ItemModule.fromJson(Map<String, dynamic> json) =>
      _$$ItemModuleFromJson(json);

  @override
  final String? type;
  @override
  final String? name;
  @override
  final String? more;
  @override
  final String? moreJumpRoute;
  @override
  final String? bannerImg;
  @override
  final String? bannerJumpRoute;
  final List<ModuleItem>? _items;
  @override
  List<ModuleItem>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Module(type: $type, name: $name, more: $more, moreJumpRoute: $moreJumpRoute, bannerImg: $bannerImg, bannerJumpRoute: $bannerJumpRoute, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemModule &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.more, more) || other.more == more) &&
            (identical(other.moreJumpRoute, moreJumpRoute) ||
                other.moreJumpRoute == moreJumpRoute) &&
            (identical(other.bannerImg, bannerImg) ||
                other.bannerImg == bannerImg) &&
            (identical(other.bannerJumpRoute, bannerJumpRoute) ||
                other.bannerJumpRoute == bannerJumpRoute) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, name, more, moreJumpRoute,
      bannerImg, bannerJumpRoute, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemModuleCopyWith<_$ItemModule> get copyWith =>
      __$$ItemModuleCopyWithImpl<_$ItemModule>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemModuleToJson(
      this,
    );
  }
}

abstract class ItemModule implements Module {
  const factory ItemModule(
      {final String? type,
      final String? name,
      final String? more,
      final String? moreJumpRoute,
      final String? bannerImg,
      final String? bannerJumpRoute,
      final List<ModuleItem>? items}) = _$ItemModule;

  factory ItemModule.fromJson(Map<String, dynamic> json) =
      _$ItemModule.fromJson;

  @override
  String? get type;
  @override
  String? get name;
  @override
  String? get more;
  @override
  String? get moreJumpRoute;
  @override
  String? get bannerImg;
  @override
  String? get bannerJumpRoute;
  @override
  List<ModuleItem>? get items;
  @override
  @JsonKey(ignore: true)
  _$$ItemModuleCopyWith<_$ItemModule> get copyWith =>
      throw _privateConstructorUsedError;
}

ShopDataModel _$ShopDataModelFromJson(Map<String, dynamic> json) {
  return _ShopDataModel.fromJson(json);
}

/// @nodoc
mixin _$ShopDataModel {
  LearnBean? get learnBean => throw _privateConstructorUsedError;
  List<Module>? get modules => throw _privateConstructorUsedError;
  List<Segment>? get segmentList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShopDataModelCopyWith<ShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopDataModelCopyWith<$Res> {
  factory $ShopDataModelCopyWith(
          ShopDataModel value, $Res Function(ShopDataModel) then) =
      _$ShopDataModelCopyWithImpl<$Res, ShopDataModel>;
  @useResult
  $Res call(
      {LearnBean? learnBean,
      List<Module>? modules,
      List<Segment>? segmentList});

  $LearnBeanCopyWith<$Res>? get learnBean;
}

/// @nodoc
class _$ShopDataModelCopyWithImpl<$Res, $Val extends ShopDataModel>
    implements $ShopDataModelCopyWith<$Res> {
  _$ShopDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? learnBean = freezed,
    Object? modules = freezed,
    Object? segmentList = freezed,
  }) {
    return _then(_value.copyWith(
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      modules: freezed == modules
          ? _value.modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
      segmentList: freezed == segmentList
          ? _value.segmentList
          : segmentList // ignore: cast_nullable_to_non_nullable
              as List<Segment>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LearnBeanCopyWith<$Res>? get learnBean {
    if (_value.learnBean == null) {
      return null;
    }

    return $LearnBeanCopyWith<$Res>(_value.learnBean!, (value) {
      return _then(_value.copyWith(learnBean: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ShopDataModelCopyWith<$Res>
    implements $ShopDataModelCopyWith<$Res> {
  factory _$$_ShopDataModelCopyWith(
          _$_ShopDataModel value, $Res Function(_$_ShopDataModel) then) =
      __$$_ShopDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LearnBean? learnBean,
      List<Module>? modules,
      List<Segment>? segmentList});

  @override
  $LearnBeanCopyWith<$Res>? get learnBean;
}

/// @nodoc
class __$$_ShopDataModelCopyWithImpl<$Res>
    extends _$ShopDataModelCopyWithImpl<$Res, _$_ShopDataModel>
    implements _$$_ShopDataModelCopyWith<$Res> {
  __$$_ShopDataModelCopyWithImpl(
      _$_ShopDataModel _value, $Res Function(_$_ShopDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? learnBean = freezed,
    Object? modules = freezed,
    Object? segmentList = freezed,
  }) {
    return _then(_$_ShopDataModel(
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      modules: freezed == modules
          ? _value._modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
      segmentList: freezed == segmentList
          ? _value._segmentList
          : segmentList // ignore: cast_nullable_to_non_nullable
              as List<Segment>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShopDataModel implements _ShopDataModel {
  _$_ShopDataModel(
      {this.learnBean,
      final List<Module>? modules,
      final List<Segment>? segmentList})
      : _modules = modules,
        _segmentList = segmentList;

  factory _$_ShopDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_ShopDataModelFromJson(json);

  @override
  final LearnBean? learnBean;
  final List<Module>? _modules;
  @override
  List<Module>? get modules {
    final value = _modules;
    if (value == null) return null;
    if (_modules is EqualUnmodifiableListView) return _modules;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Segment>? _segmentList;
  @override
  List<Segment>? get segmentList {
    final value = _segmentList;
    if (value == null) return null;
    if (_segmentList is EqualUnmodifiableListView) return _segmentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ShopDataModel(learnBean: $learnBean, modules: $modules, segmentList: $segmentList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShopDataModel &&
            (identical(other.learnBean, learnBean) ||
                other.learnBean == learnBean) &&
            const DeepCollectionEquality().equals(other._modules, _modules) &&
            const DeepCollectionEquality()
                .equals(other._segmentList, _segmentList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      learnBean,
      const DeepCollectionEquality().hash(_modules),
      const DeepCollectionEquality().hash(_segmentList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShopDataModelCopyWith<_$_ShopDataModel> get copyWith =>
      __$$_ShopDataModelCopyWithImpl<_$_ShopDataModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShopDataModelToJson(
      this,
    );
  }
}

abstract class _ShopDataModel implements ShopDataModel {
  factory _ShopDataModel(
      {final LearnBean? learnBean,
      final List<Module>? modules,
      final List<Segment>? segmentList}) = _$_ShopDataModel;

  factory _ShopDataModel.fromJson(Map<String, dynamic> json) =
      _$_ShopDataModel.fromJson;

  @override
  LearnBean? get learnBean;
  @override
  List<Module>? get modules;
  @override
  List<Segment>? get segmentList;
  @override
  @JsonKey(ignore: true)
  _$$_ShopDataModelCopyWith<_$_ShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

Segment _$SegmentFromJson(Map<String, dynamic> json) {
  return _Segment.fromJson(json);
}

/// @nodoc
mixin _$Segment {
  String? get name => throw _privateConstructorUsedError;
  int? get code => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentCopyWith<Segment> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentCopyWith<$Res> {
  factory $SegmentCopyWith(Segment value, $Res Function(Segment) then) =
      _$SegmentCopyWithImpl<$Res, Segment>;
  @useResult
  $Res call({String? name, int? code});
}

/// @nodoc
class _$SegmentCopyWithImpl<$Res, $Val extends Segment>
    implements $SegmentCopyWith<$Res> {
  _$SegmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SegmentCopyWith<$Res> implements $SegmentCopyWith<$Res> {
  factory _$$_SegmentCopyWith(
          _$_Segment value, $Res Function(_$_Segment) then) =
      __$$_SegmentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, int? code});
}

/// @nodoc
class __$$_SegmentCopyWithImpl<$Res>
    extends _$SegmentCopyWithImpl<$Res, _$_Segment>
    implements _$$_SegmentCopyWith<$Res> {
  __$$_SegmentCopyWithImpl(_$_Segment _value, $Res Function(_$_Segment) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
  }) {
    return _then(_$_Segment(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Segment implements _Segment {
  _$_Segment({this.name, this.code});

  factory _$_Segment.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentFromJson(json);

  @override
  final String? name;
  @override
  final int? code;

  @override
  String toString() {
    return 'Segment(name: $name, code: $code)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Segment &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, code);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentCopyWith<_$_Segment> get copyWith =>
      __$$_SegmentCopyWithImpl<_$_Segment>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentToJson(
      this,
    );
  }
}

abstract class _Segment implements Segment {
  factory _Segment({final String? name, final int? code}) = _$_Segment;

  factory _Segment.fromJson(Map<String, dynamic> json) = _$_Segment.fromJson;

  @override
  String? get name;
  @override
  int? get code;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentCopyWith<_$_Segment> get copyWith =>
      throw _privateConstructorUsedError;
}

PropsShopDataModel _$PropsShopDataModelFromJson(Map<String, dynamic> json) {
  return _PropsShopDataModel.fromJson(json);
}

/// @nodoc
mixin _$PropsShopDataModel {
  int? get useAssetType => throw _privateConstructorUsedError;
  set useAssetType(int? value) => throw _privateConstructorUsedError;
  int? get totalPropCount => throw _privateConstructorUsedError;
  set totalPropCount(int? value) => throw _privateConstructorUsedError;
  int? get balance => throw _privateConstructorUsedError;
  set balance(int? value) => throw _privateConstructorUsedError; // 资产数量
  String? get icon => throw _privateConstructorUsedError; // 资产数量
  set icon(String? value) => throw _privateConstructorUsedError; // 资产图标
  List<PropsShopDataItemModel>? get props =>
      throw _privateConstructorUsedError; // 资产图标
  set props(List<PropsShopDataItemModel>? value) =>
      throw _privateConstructorUsedError;
  int? get purchaseSwitch => throw _privateConstructorUsedError;
  set purchaseSwitch(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PropsShopDataModelCopyWith<PropsShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PropsShopDataModelCopyWith<$Res> {
  factory $PropsShopDataModelCopyWith(
          PropsShopDataModel value, $Res Function(PropsShopDataModel) then) =
      _$PropsShopDataModelCopyWithImpl<$Res, PropsShopDataModel>;
  @useResult
  $Res call(
      {int? useAssetType,
      int? totalPropCount,
      int? balance,
      String? icon,
      List<PropsShopDataItemModel>? props,
      int? purchaseSwitch});
}

/// @nodoc
class _$PropsShopDataModelCopyWithImpl<$Res, $Val extends PropsShopDataModel>
    implements $PropsShopDataModelCopyWith<$Res> {
  _$PropsShopDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? useAssetType = freezed,
    Object? totalPropCount = freezed,
    Object? balance = freezed,
    Object? icon = freezed,
    Object? props = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_value.copyWith(
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPropCount: freezed == totalPropCount
          ? _value.totalPropCount
          : totalPropCount // ignore: cast_nullable_to_non_nullable
              as int?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      props: freezed == props
          ? _value.props
          : props // ignore: cast_nullable_to_non_nullable
              as List<PropsShopDataItemModel>?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PropsShopDataModelCopyWith<$Res>
    implements $PropsShopDataModelCopyWith<$Res> {
  factory _$$_PropsShopDataModelCopyWith(_$_PropsShopDataModel value,
          $Res Function(_$_PropsShopDataModel) then) =
      __$$_PropsShopDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? useAssetType,
      int? totalPropCount,
      int? balance,
      String? icon,
      List<PropsShopDataItemModel>? props,
      int? purchaseSwitch});
}

/// @nodoc
class __$$_PropsShopDataModelCopyWithImpl<$Res>
    extends _$PropsShopDataModelCopyWithImpl<$Res, _$_PropsShopDataModel>
    implements _$$_PropsShopDataModelCopyWith<$Res> {
  __$$_PropsShopDataModelCopyWithImpl(
      _$_PropsShopDataModel _value, $Res Function(_$_PropsShopDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? useAssetType = freezed,
    Object? totalPropCount = freezed,
    Object? balance = freezed,
    Object? icon = freezed,
    Object? props = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_$_PropsShopDataModel(
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPropCount: freezed == totalPropCount
          ? _value.totalPropCount
          : totalPropCount // ignore: cast_nullable_to_non_nullable
              as int?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      props: freezed == props
          ? _value.props
          : props // ignore: cast_nullable_to_non_nullable
              as List<PropsShopDataItemModel>?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PropsShopDataModel implements _PropsShopDataModel {
  _$_PropsShopDataModel(
      {this.useAssetType,
      this.totalPropCount,
      this.balance,
      this.icon,
      this.props,
      this.purchaseSwitch});

  factory _$_PropsShopDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_PropsShopDataModelFromJson(json);

  @override
  int? useAssetType;
  @override
  int? totalPropCount;
  @override
  int? balance;
// 资产数量
  @override
  String? icon;
// 资产图标
  @override
  List<PropsShopDataItemModel>? props;
  @override
  int? purchaseSwitch;

  @override
  String toString() {
    return 'PropsShopDataModel(useAssetType: $useAssetType, totalPropCount: $totalPropCount, balance: $balance, icon: $icon, props: $props, purchaseSwitch: $purchaseSwitch)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PropsShopDataModelCopyWith<_$_PropsShopDataModel> get copyWith =>
      __$$_PropsShopDataModelCopyWithImpl<_$_PropsShopDataModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PropsShopDataModelToJson(
      this,
    );
  }
}

abstract class _PropsShopDataModel implements PropsShopDataModel {
  factory _PropsShopDataModel(
      {int? useAssetType,
      int? totalPropCount,
      int? balance,
      String? icon,
      List<PropsShopDataItemModel>? props,
      int? purchaseSwitch}) = _$_PropsShopDataModel;

  factory _PropsShopDataModel.fromJson(Map<String, dynamic> json) =
      _$_PropsShopDataModel.fromJson;

  @override
  int? get useAssetType;
  set useAssetType(int? value);
  @override
  int? get totalPropCount;
  set totalPropCount(int? value);
  @override
  int? get balance;
  set balance(int? value);
  @override // 资产数量
  String? get icon; // 资产数量
  set icon(String? value);
  @override // 资产图标
  List<PropsShopDataItemModel>? get props; // 资产图标
  set props(List<PropsShopDataItemModel>? value);
  @override
  int? get purchaseSwitch;
  set purchaseSwitch(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_PropsShopDataModelCopyWith<_$_PropsShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

PropsShopDataItemModel _$PropsShopDataItemModelFromJson(
    Map<String, dynamic> json) {
  return _PropsShopDataItemModel.fromJson(json);
}

/// @nodoc
mixin _$PropsShopDataItemModel {
  int? get type => throw _privateConstructorUsedError;
  set type(int? value) => throw _privateConstructorUsedError;
  int? get ownedCount => throw _privateConstructorUsedError;
  set ownedCount(int? value) => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;
  set price(int? value) => throw _privateConstructorUsedError;
  bool? get onShelf => throw _privateConstructorUsedError;
  set onShelf(bool? value) => throw _privateConstructorUsedError; // 是否上架
  bool? get commodity => throw _privateConstructorUsedError; // 是否上架
  set commodity(bool? value) => throw _privateConstructorUsedError; // 是否是商品
  String? get name => throw _privateConstructorUsedError; // 是否是商品
  set name(String? value) => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  set id(String? value) => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get functionDesc => throw _privateConstructorUsedError;
  set functionDesc(String? value) => throw _privateConstructorUsedError;
  String? get sourceDesc => throw _privateConstructorUsedError;
  set sourceDesc(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PropsShopDataItemModelCopyWith<PropsShopDataItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PropsShopDataItemModelCopyWith<$Res> {
  factory $PropsShopDataItemModelCopyWith(PropsShopDataItemModel value,
          $Res Function(PropsShopDataItemModel) then) =
      _$PropsShopDataItemModelCopyWithImpl<$Res, PropsShopDataItemModel>;
  @useResult
  $Res call(
      {int? type,
      int? ownedCount,
      int? price,
      bool? onShelf,
      bool? commodity,
      String? name,
      String? id,
      String? icon,
      String? functionDesc,
      String? sourceDesc});
}

/// @nodoc
class _$PropsShopDataItemModelCopyWithImpl<$Res,
        $Val extends PropsShopDataItemModel>
    implements $PropsShopDataItemModelCopyWith<$Res> {
  _$PropsShopDataItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? ownedCount = freezed,
    Object? price = freezed,
    Object? onShelf = freezed,
    Object? commodity = freezed,
    Object? name = freezed,
    Object? id = freezed,
    Object? icon = freezed,
    Object? functionDesc = freezed,
    Object? sourceDesc = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      ownedCount: freezed == ownedCount
          ? _value.ownedCount
          : ownedCount // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      onShelf: freezed == onShelf
          ? _value.onShelf
          : onShelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      commodity: freezed == commodity
          ? _value.commodity
          : commodity // ignore: cast_nullable_to_non_nullable
              as bool?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      functionDesc: freezed == functionDesc
          ? _value.functionDesc
          : functionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceDesc: freezed == sourceDesc
          ? _value.sourceDesc
          : sourceDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PropsShopDataItemModelCopyWith<$Res>
    implements $PropsShopDataItemModelCopyWith<$Res> {
  factory _$$_PropsShopDataItemModelCopyWith(_$_PropsShopDataItemModel value,
          $Res Function(_$_PropsShopDataItemModel) then) =
      __$$_PropsShopDataItemModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      int? ownedCount,
      int? price,
      bool? onShelf,
      bool? commodity,
      String? name,
      String? id,
      String? icon,
      String? functionDesc,
      String? sourceDesc});
}

/// @nodoc
class __$$_PropsShopDataItemModelCopyWithImpl<$Res>
    extends _$PropsShopDataItemModelCopyWithImpl<$Res,
        _$_PropsShopDataItemModel>
    implements _$$_PropsShopDataItemModelCopyWith<$Res> {
  __$$_PropsShopDataItemModelCopyWithImpl(_$_PropsShopDataItemModel _value,
      $Res Function(_$_PropsShopDataItemModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? ownedCount = freezed,
    Object? price = freezed,
    Object? onShelf = freezed,
    Object? commodity = freezed,
    Object? name = freezed,
    Object? id = freezed,
    Object? icon = freezed,
    Object? functionDesc = freezed,
    Object? sourceDesc = freezed,
  }) {
    return _then(_$_PropsShopDataItemModel(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      ownedCount: freezed == ownedCount
          ? _value.ownedCount
          : ownedCount // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      onShelf: freezed == onShelf
          ? _value.onShelf
          : onShelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      commodity: freezed == commodity
          ? _value.commodity
          : commodity // ignore: cast_nullable_to_non_nullable
              as bool?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      functionDesc: freezed == functionDesc
          ? _value.functionDesc
          : functionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceDesc: freezed == sourceDesc
          ? _value.sourceDesc
          : sourceDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PropsShopDataItemModel implements _PropsShopDataItemModel {
  _$_PropsShopDataItemModel(
      {this.type,
      this.ownedCount,
      this.price,
      this.onShelf,
      this.commodity,
      this.name,
      this.id,
      this.icon,
      this.functionDesc,
      this.sourceDesc});

  factory _$_PropsShopDataItemModel.fromJson(Map<String, dynamic> json) =>
      _$$_PropsShopDataItemModelFromJson(json);

  @override
  int? type;
  @override
  int? ownedCount;
  @override
  int? price;
  @override
  bool? onShelf;
// 是否上架
  @override
  bool? commodity;
// 是否是商品
  @override
  String? name;
  @override
  String? id;
  @override
  String? icon;
  @override
  String? functionDesc;
  @override
  String? sourceDesc;

  @override
  String toString() {
    return 'PropsShopDataItemModel(type: $type, ownedCount: $ownedCount, price: $price, onShelf: $onShelf, commodity: $commodity, name: $name, id: $id, icon: $icon, functionDesc: $functionDesc, sourceDesc: $sourceDesc)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PropsShopDataItemModelCopyWith<_$_PropsShopDataItemModel> get copyWith =>
      __$$_PropsShopDataItemModelCopyWithImpl<_$_PropsShopDataItemModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PropsShopDataItemModelToJson(
      this,
    );
  }
}

abstract class _PropsShopDataItemModel implements PropsShopDataItemModel {
  factory _PropsShopDataItemModel(
      {int? type,
      int? ownedCount,
      int? price,
      bool? onShelf,
      bool? commodity,
      String? name,
      String? id,
      String? icon,
      String? functionDesc,
      String? sourceDesc}) = _$_PropsShopDataItemModel;

  factory _PropsShopDataItemModel.fromJson(Map<String, dynamic> json) =
      _$_PropsShopDataItemModel.fromJson;

  @override
  int? get type;
  set type(int? value);
  @override
  int? get ownedCount;
  set ownedCount(int? value);
  @override
  int? get price;
  set price(int? value);
  @override
  bool? get onShelf;
  set onShelf(bool? value);
  @override // 是否上架
  bool? get commodity; // 是否上架
  set commodity(bool? value);
  @override // 是否是商品
  String? get name; // 是否是商品
  set name(String? value);
  @override
  String? get id;
  set id(String? value);
  @override
  String? get icon;
  set icon(String? value);
  @override
  String? get functionDesc;
  set functionDesc(String? value);
  @override
  String? get sourceDesc;
  set sourceDesc(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_PropsShopDataItemModelCopyWith<_$_PropsShopDataItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

PropsOrderStatusModel _$PropsOrderStatusModelFromJson(
    Map<String, dynamic> json) {
  return _PropsOrderStatusModel.fromJson(json);
}

/// @nodoc
mixin _$PropsOrderStatusModel {
  int? get orderStatus => throw _privateConstructorUsedError;
  set orderStatus(int? value) => throw _privateConstructorUsedError;
  String? get orderId => throw _privateConstructorUsedError;
  set orderId(String? value) => throw _privateConstructorUsedError;
  String? get failReason => throw _privateConstructorUsedError;
  set failReason(String? value) => throw _privateConstructorUsedError;
  String? get batchId => throw _privateConstructorUsedError;
  set batchId(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PropsOrderStatusModelCopyWith<PropsOrderStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PropsOrderStatusModelCopyWith<$Res> {
  factory $PropsOrderStatusModelCopyWith(PropsOrderStatusModel value,
          $Res Function(PropsOrderStatusModel) then) =
      _$PropsOrderStatusModelCopyWithImpl<$Res, PropsOrderStatusModel>;
  @useResult
  $Res call(
      {int? orderStatus, String? orderId, String? failReason, String? batchId});
}

/// @nodoc
class _$PropsOrderStatusModelCopyWithImpl<$Res,
        $Val extends PropsOrderStatusModel>
    implements $PropsOrderStatusModelCopyWith<$Res> {
  _$PropsOrderStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderStatus = freezed,
    Object? orderId = freezed,
    Object? failReason = freezed,
    Object? batchId = freezed,
  }) {
    return _then(_value.copyWith(
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      failReason: freezed == failReason
          ? _value.failReason
          : failReason // ignore: cast_nullable_to_non_nullable
              as String?,
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PropsOrderStatusModelCopyWith<$Res>
    implements $PropsOrderStatusModelCopyWith<$Res> {
  factory _$$_PropsOrderStatusModelCopyWith(_$_PropsOrderStatusModel value,
          $Res Function(_$_PropsOrderStatusModel) then) =
      __$$_PropsOrderStatusModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? orderStatus, String? orderId, String? failReason, String? batchId});
}

/// @nodoc
class __$$_PropsOrderStatusModelCopyWithImpl<$Res>
    extends _$PropsOrderStatusModelCopyWithImpl<$Res, _$_PropsOrderStatusModel>
    implements _$$_PropsOrderStatusModelCopyWith<$Res> {
  __$$_PropsOrderStatusModelCopyWithImpl(_$_PropsOrderStatusModel _value,
      $Res Function(_$_PropsOrderStatusModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderStatus = freezed,
    Object? orderId = freezed,
    Object? failReason = freezed,
    Object? batchId = freezed,
  }) {
    return _then(_$_PropsOrderStatusModel(
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      failReason: freezed == failReason
          ? _value.failReason
          : failReason // ignore: cast_nullable_to_non_nullable
              as String?,
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PropsOrderStatusModel implements _PropsOrderStatusModel {
  _$_PropsOrderStatusModel(
      {this.orderStatus, this.orderId, this.failReason, this.batchId});

  factory _$_PropsOrderStatusModel.fromJson(Map<String, dynamic> json) =>
      _$$_PropsOrderStatusModelFromJson(json);

  @override
  int? orderStatus;
  @override
  String? orderId;
  @override
  String? failReason;
  @override
  String? batchId;

  @override
  String toString() {
    return 'PropsOrderStatusModel(orderStatus: $orderStatus, orderId: $orderId, failReason: $failReason, batchId: $batchId)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PropsOrderStatusModelCopyWith<_$_PropsOrderStatusModel> get copyWith =>
      __$$_PropsOrderStatusModelCopyWithImpl<_$_PropsOrderStatusModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PropsOrderStatusModelToJson(
      this,
    );
  }
}

abstract class _PropsOrderStatusModel implements PropsOrderStatusModel {
  factory _PropsOrderStatusModel(
      {int? orderStatus,
      String? orderId,
      String? failReason,
      String? batchId}) = _$_PropsOrderStatusModel;

  factory _PropsOrderStatusModel.fromJson(Map<String, dynamic> json) =
      _$_PropsOrderStatusModel.fromJson;

  @override
  int? get orderStatus;
  set orderStatus(int? value);
  @override
  String? get orderId;
  set orderId(String? value);
  @override
  String? get failReason;
  set failReason(String? value);
  @override
  String? get batchId;
  set batchId(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_PropsOrderStatusModelCopyWith<_$_PropsOrderStatusModel> get copyWith =>
      throw _privateConstructorUsedError;
}
