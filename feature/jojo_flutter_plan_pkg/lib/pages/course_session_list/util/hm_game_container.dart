import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/model/hm_game_container_gray_config.dart';

class HmGameContainerGary {
  static String hmContainerAbTestTag = "鸿蒙容器灰度";
  static const String _abKey = 'jojo_flutter_harmony_unity_and_pure_container';
  static bool? _abSwitchConfig = false;

  // 添加用于测试的静态实例
  static ABTesting? _testABTestingInstance;

  HmGameContainerGary._();

  /// 请求灰度
  static Future<void> initConfig({required Map<String, Object> parmas}) async {
    try {
      _abSwitchConfig = await _requestData(parmas);
    } catch (e, stack) {
      l.i(hmContainerAbTestTag,
          "HmGameContainer.init:环节列表 UI AB 灰度配置异常，error=$e, stack=$stack");
    }
  }

  static bool? get abSwitchConfig => _abSwitchConfig;

  static Future<bool> _requestData(Map<String, Object> parmas) async {
    // 使用测试实例或默认实例
    final abTestingInstance = _testABTestingInstance ?? ABTesting.instance;

    final ApiResult<bool> res =
        await abTestingInstance.getBool(_abKey, context: parmas);
    return res.data?.value ?? false;
  }

  static bool enabledHmGameContainer() {
    return _abSwitchConfig ?? false;
  }

  // 测试辅助方法
  static void setABTestingInstance(ABTesting instance) {
    _testABTestingInstance = instance;
  }

  static void resetForTesting() {
    _abSwitchConfig = null;
    _testABTestingInstance = null;
  }
}
