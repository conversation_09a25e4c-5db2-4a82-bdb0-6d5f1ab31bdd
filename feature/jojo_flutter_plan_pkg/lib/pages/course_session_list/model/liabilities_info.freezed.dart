// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'liabilities_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LiabilitiesInfo _$LiabilitiesInfoFromJson(Map<String, dynamic> json) {
  return _LiabilitiesInfo.fromJson(json);
}

/// @nodoc
mixin _$LiabilitiesInfo {
  List<AssetList>? get assetList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LiabilitiesInfoCopyWith<LiabilitiesInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiabilitiesInfoCopyWith<$Res> {
  factory $LiabilitiesInfoCopyWith(
          LiabilitiesInfo value, $Res Function(LiabilitiesInfo) then) =
      _$LiabilitiesInfoCopyWithImpl<$Res, LiabilitiesInfo>;
  @useResult
  $Res call({List<AssetList>? assetList});
}

/// @nodoc
class _$LiabilitiesInfoCopyWithImpl<$Res, $Val extends LiabilitiesInfo>
    implements $LiabilitiesInfoCopyWith<$Res> {
  _$LiabilitiesInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetList = freezed,
  }) {
    return _then(_value.copyWith(
      assetList: freezed == assetList
          ? _value.assetList
          : assetList // ignore: cast_nullable_to_non_nullable
              as List<AssetList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LiabilitiesInfoCopyWith<$Res>
    implements $LiabilitiesInfoCopyWith<$Res> {
  factory _$$_LiabilitiesInfoCopyWith(
          _$_LiabilitiesInfo value, $Res Function(_$_LiabilitiesInfo) then) =
      __$$_LiabilitiesInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AssetList>? assetList});
}

/// @nodoc
class __$$_LiabilitiesInfoCopyWithImpl<$Res>
    extends _$LiabilitiesInfoCopyWithImpl<$Res, _$_LiabilitiesInfo>
    implements _$$_LiabilitiesInfoCopyWith<$Res> {
  __$$_LiabilitiesInfoCopyWithImpl(
      _$_LiabilitiesInfo _value, $Res Function(_$_LiabilitiesInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetList = freezed,
  }) {
    return _then(_$_LiabilitiesInfo(
      assetList: freezed == assetList
          ? _value._assetList
          : assetList // ignore: cast_nullable_to_non_nullable
              as List<AssetList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LiabilitiesInfo implements _LiabilitiesInfo {
  const _$_LiabilitiesInfo({final List<AssetList>? assetList})
      : _assetList = assetList;

  factory _$_LiabilitiesInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LiabilitiesInfoFromJson(json);

  final List<AssetList>? _assetList;
  @override
  List<AssetList>? get assetList {
    final value = _assetList;
    if (value == null) return null;
    if (_assetList is EqualUnmodifiableListView) return _assetList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LiabilitiesInfo(assetList: $assetList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LiabilitiesInfo &&
            const DeepCollectionEquality()
                .equals(other._assetList, _assetList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_assetList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LiabilitiesInfoCopyWith<_$_LiabilitiesInfo> get copyWith =>
      __$$_LiabilitiesInfoCopyWithImpl<_$_LiabilitiesInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LiabilitiesInfoToJson(
      this,
    );
  }
}

abstract class _LiabilitiesInfo implements LiabilitiesInfo {
  const factory _LiabilitiesInfo({final List<AssetList>? assetList}) =
      _$_LiabilitiesInfo;

  factory _LiabilitiesInfo.fromJson(Map<String, dynamic> json) =
      _$_LiabilitiesInfo.fromJson;

  @override
  List<AssetList>? get assetList;
  @override
  @JsonKey(ignore: true)
  _$$_LiabilitiesInfoCopyWith<_$_LiabilitiesInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

AssetList _$AssetListFromJson(Map<String, dynamic> json) {
  return _AssetList.fromJson(json);
}

/// @nodoc
mixin _$AssetList {
  int? get assetType => throw _privateConstructorUsedError;
  int? get balance => throw _privateConstructorUsedError;
  bool? get hideBalance => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  List<Limit>? get limits => throw _privateConstructorUsedError;
  Extra? get extra => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AssetListCopyWith<AssetList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AssetListCopyWith<$Res> {
  factory $AssetListCopyWith(AssetList value, $Res Function(AssetList) then) =
      _$AssetListCopyWithImpl<$Res, AssetList>;
  @useResult
  $Res call(
      {int? assetType,
      int? balance,
      bool? hideBalance,
      String? router,
      String? icon,
      List<Limit>? limits,
      Extra? extra});

  $ExtraCopyWith<$Res>? get extra;
}

/// @nodoc
class _$AssetListCopyWithImpl<$Res, $Val extends AssetList>
    implements $AssetListCopyWith<$Res> {
  _$AssetListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetType = freezed,
    Object? balance = freezed,
    Object? hideBalance = freezed,
    Object? router = freezed,
    Object? icon = freezed,
    Object? limits = freezed,
    Object? extra = freezed,
  }) {
    return _then(_value.copyWith(
      assetType: freezed == assetType
          ? _value.assetType
          : assetType // ignore: cast_nullable_to_non_nullable
              as int?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as int?,
      hideBalance: freezed == hideBalance
          ? _value.hideBalance
          : hideBalance // ignore: cast_nullable_to_non_nullable
              as bool?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      limits: freezed == limits
          ? _value.limits
          : limits // ignore: cast_nullable_to_non_nullable
              as List<Limit>?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as Extra?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtraCopyWith<$Res>? get extra {
    if (_value.extra == null) {
      return null;
    }

    return $ExtraCopyWith<$Res>(_value.extra!, (value) {
      return _then(_value.copyWith(extra: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AssetListCopyWith<$Res> implements $AssetListCopyWith<$Res> {
  factory _$$_AssetListCopyWith(
          _$_AssetList value, $Res Function(_$_AssetList) then) =
      __$$_AssetListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? assetType,
      int? balance,
      bool? hideBalance,
      String? router,
      String? icon,
      List<Limit>? limits,
      Extra? extra});

  @override
  $ExtraCopyWith<$Res>? get extra;
}

/// @nodoc
class __$$_AssetListCopyWithImpl<$Res>
    extends _$AssetListCopyWithImpl<$Res, _$_AssetList>
    implements _$$_AssetListCopyWith<$Res> {
  __$$_AssetListCopyWithImpl(
      _$_AssetList _value, $Res Function(_$_AssetList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetType = freezed,
    Object? balance = freezed,
    Object? hideBalance = freezed,
    Object? router = freezed,
    Object? icon = freezed,
    Object? limits = freezed,
    Object? extra = freezed,
  }) {
    return _then(_$_AssetList(
      assetType: freezed == assetType
          ? _value.assetType
          : assetType // ignore: cast_nullable_to_non_nullable
              as int?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as int?,
      hideBalance: freezed == hideBalance
          ? _value.hideBalance
          : hideBalance // ignore: cast_nullable_to_non_nullable
              as bool?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      limits: freezed == limits
          ? _value._limits
          : limits // ignore: cast_nullable_to_non_nullable
              as List<Limit>?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as Extra?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AssetList implements _AssetList {
  const _$_AssetList(
      {this.assetType,
      this.balance,
      this.hideBalance,
      this.router,
      this.icon,
      final List<Limit>? limits,
      this.extra})
      : _limits = limits;

  factory _$_AssetList.fromJson(Map<String, dynamic> json) =>
      _$$_AssetListFromJson(json);

  @override
  final int? assetType;
  @override
  final int? balance;
  @override
  final bool? hideBalance;
  @override
  final String? router;
  @override
  final String? icon;
  final List<Limit>? _limits;
  @override
  List<Limit>? get limits {
    final value = _limits;
    if (value == null) return null;
    if (_limits is EqualUnmodifiableListView) return _limits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final Extra? extra;

  @override
  String toString() {
    return 'AssetList(assetType: $assetType, balance: $balance, hideBalance: $hideBalance, router: $router, icon: $icon, limits: $limits, extra: $extra)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AssetList &&
            (identical(other.assetType, assetType) ||
                other.assetType == assetType) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.hideBalance, hideBalance) ||
                other.hideBalance == hideBalance) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            const DeepCollectionEquality().equals(other._limits, _limits) &&
            (identical(other.extra, extra) || other.extra == extra));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, assetType, balance, hideBalance,
      router, icon, const DeepCollectionEquality().hash(_limits), extra);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AssetListCopyWith<_$_AssetList> get copyWith =>
      __$$_AssetListCopyWithImpl<_$_AssetList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AssetListToJson(
      this,
    );
  }
}

abstract class _AssetList implements AssetList {
  const factory _AssetList(
      {final int? assetType,
      final int? balance,
      final bool? hideBalance,
      final String? router,
      final String? icon,
      final List<Limit>? limits,
      final Extra? extra}) = _$_AssetList;

  factory _AssetList.fromJson(Map<String, dynamic> json) =
      _$_AssetList.fromJson;

  @override
  int? get assetType;
  @override
  int? get balance;
  @override
  bool? get hideBalance;
  @override
  String? get router;
  @override
  String? get icon;
  @override
  List<Limit>? get limits;
  @override
  Extra? get extra;
  @override
  @JsonKey(ignore: true)
  _$$_AssetListCopyWith<_$_AssetList> get copyWith =>
      throw _privateConstructorUsedError;
}

Extra _$ExtraFromJson(Map<String, dynamic> json) {
  return _Extra.fromJson(json);
}

/// @nodoc
mixin _$Extra {
  int? get guideType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraCopyWith<Extra> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraCopyWith<$Res> {
  factory $ExtraCopyWith(Extra value, $Res Function(Extra) then) =
      _$ExtraCopyWithImpl<$Res, Extra>;
  @useResult
  $Res call({int? guideType});
}

/// @nodoc
class _$ExtraCopyWithImpl<$Res, $Val extends Extra>
    implements $ExtraCopyWith<$Res> {
  _$ExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guideType = freezed,
  }) {
    return _then(_value.copyWith(
      guideType: freezed == guideType
          ? _value.guideType
          : guideType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExtraCopyWith<$Res> implements $ExtraCopyWith<$Res> {
  factory _$$_ExtraCopyWith(_$_Extra value, $Res Function(_$_Extra) then) =
      __$$_ExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? guideType});
}

/// @nodoc
class __$$_ExtraCopyWithImpl<$Res> extends _$ExtraCopyWithImpl<$Res, _$_Extra>
    implements _$$_ExtraCopyWith<$Res> {
  __$$_ExtraCopyWithImpl(_$_Extra _value, $Res Function(_$_Extra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guideType = freezed,
  }) {
    return _then(_$_Extra(
      guideType: freezed == guideType
          ? _value.guideType
          : guideType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Extra implements _Extra {
  const _$_Extra({this.guideType});

  factory _$_Extra.fromJson(Map<String, dynamic> json) =>
      _$$_ExtraFromJson(json);

  @override
  final int? guideType;

  @override
  String toString() {
    return 'Extra(guideType: $guideType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Extra &&
            (identical(other.guideType, guideType) ||
                other.guideType == guideType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, guideType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtraCopyWith<_$_Extra> get copyWith =>
      __$$_ExtraCopyWithImpl<_$_Extra>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtraToJson(
      this,
    );
  }
}

abstract class _Extra implements Extra {
  const factory _Extra({final int? guideType}) = _$_Extra;

  factory _Extra.fromJson(Map<String, dynamic> json) = _$_Extra.fromJson;

  @override
  int? get guideType;
  @override
  @JsonKey(ignore: true)
  _$$_ExtraCopyWith<_$_Extra> get copyWith =>
      throw _privateConstructorUsedError;
}

Limit _$LimitFromJson(Map<String, dynamic> json) {
  return _Limit.fromJson(json);
}

/// @nodoc
mixin _$Limit {
  String? get limitType => throw _privateConstructorUsedError;
  int? get limitCount => throw _privateConstructorUsedError;
  int? get curCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LimitCopyWith<Limit> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LimitCopyWith<$Res> {
  factory $LimitCopyWith(Limit value, $Res Function(Limit) then) =
      _$LimitCopyWithImpl<$Res, Limit>;
  @useResult
  $Res call({String? limitType, int? limitCount, int? curCount});
}

/// @nodoc
class _$LimitCopyWithImpl<$Res, $Val extends Limit>
    implements $LimitCopyWith<$Res> {
  _$LimitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limitType = freezed,
    Object? limitCount = freezed,
    Object? curCount = freezed,
  }) {
    return _then(_value.copyWith(
      limitType: freezed == limitType
          ? _value.limitType
          : limitType // ignore: cast_nullable_to_non_nullable
              as String?,
      limitCount: freezed == limitCount
          ? _value.limitCount
          : limitCount // ignore: cast_nullable_to_non_nullable
              as int?,
      curCount: freezed == curCount
          ? _value.curCount
          : curCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LimitCopyWith<$Res> implements $LimitCopyWith<$Res> {
  factory _$$_LimitCopyWith(_$_Limit value, $Res Function(_$_Limit) then) =
      __$$_LimitCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? limitType, int? limitCount, int? curCount});
}

/// @nodoc
class __$$_LimitCopyWithImpl<$Res> extends _$LimitCopyWithImpl<$Res, _$_Limit>
    implements _$$_LimitCopyWith<$Res> {
  __$$_LimitCopyWithImpl(_$_Limit _value, $Res Function(_$_Limit) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limitType = freezed,
    Object? limitCount = freezed,
    Object? curCount = freezed,
  }) {
    return _then(_$_Limit(
      limitType: freezed == limitType
          ? _value.limitType
          : limitType // ignore: cast_nullable_to_non_nullable
              as String?,
      limitCount: freezed == limitCount
          ? _value.limitCount
          : limitCount // ignore: cast_nullable_to_non_nullable
              as int?,
      curCount: freezed == curCount
          ? _value.curCount
          : curCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Limit implements _Limit {
  const _$_Limit({this.limitType, this.limitCount, this.curCount});

  factory _$_Limit.fromJson(Map<String, dynamic> json) =>
      _$$_LimitFromJson(json);

  @override
  final String? limitType;
  @override
  final int? limitCount;
  @override
  final int? curCount;

  @override
  String toString() {
    return 'Limit(limitType: $limitType, limitCount: $limitCount, curCount: $curCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Limit &&
            (identical(other.limitType, limitType) ||
                other.limitType == limitType) &&
            (identical(other.limitCount, limitCount) ||
                other.limitCount == limitCount) &&
            (identical(other.curCount, curCount) ||
                other.curCount == curCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, limitType, limitCount, curCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LimitCopyWith<_$_Limit> get copyWith =>
      __$$_LimitCopyWithImpl<_$_Limit>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LimitToJson(
      this,
    );
  }
}

abstract class _Limit implements Limit {
  const factory _Limit(
      {final String? limitType,
      final int? limitCount,
      final int? curCount}) = _$_Limit;

  factory _Limit.fromJson(Map<String, dynamic> json) = _$_Limit.fromJson;

  @override
  String? get limitType;
  @override
  int? get limitCount;
  @override
  int? get curCount;
  @override
  @JsonKey(ignore: true)
  _$$_LimitCopyWith<_$_Limit> get copyWith =>
      throw _privateConstructorUsedError;
}
