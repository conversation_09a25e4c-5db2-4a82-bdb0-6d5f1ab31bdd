// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_session_list_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseSessionListInfo _$CourseSessionListInfoFromJson(
    Map<String, dynamic> json) {
  return _CourseSessionListInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseSessionListInfo {
  CourseInfo? get courseInfo => throw _privateConstructorUsedError;
  ClassInfo? get classInfo => throw _privateConstructorUsedError;
  ExtraData? get extraData => throw _privateConstructorUsedError;
  LessonInfo? get lessonInfo => throw _privateConstructorUsedError;
  List<ButtonList>? get buttonList => throw _privateConstructorUsedError;
  List<OuterFunc>? get outerFunc => throw _privateConstructorUsedError;
  List<LessonRefFunc>? get lessonRefFunc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSessionListInfoCopyWith<CourseSessionListInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSessionListInfoCopyWith<$Res> {
  factory $CourseSessionListInfoCopyWith(CourseSessionListInfo value,
          $Res Function(CourseSessionListInfo) then) =
      _$CourseSessionListInfoCopyWithImpl<$Res, CourseSessionListInfo>;
  @useResult
  $Res call(
      {CourseInfo? courseInfo,
      ClassInfo? classInfo,
      ExtraData? extraData,
      LessonInfo? lessonInfo,
      List<ButtonList>? buttonList,
      List<OuterFunc>? outerFunc,
      List<LessonRefFunc>? lessonRefFunc});

  $CourseInfoCopyWith<$Res>? get courseInfo;
  $ClassInfoCopyWith<$Res>? get classInfo;
  $ExtraDataCopyWith<$Res>? get extraData;
  $LessonInfoCopyWith<$Res>? get lessonInfo;
}

/// @nodoc
class _$CourseSessionListInfoCopyWithImpl<$Res,
        $Val extends CourseSessionListInfo>
    implements $CourseSessionListInfoCopyWith<$Res> {
  _$CourseSessionListInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
    Object? extraData = freezed,
    Object? lessonInfo = freezed,
    Object? buttonList = freezed,
    Object? outerFunc = freezed,
    Object? lessonRefFunc = freezed,
  }) {
    return _then(_value.copyWith(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfo?,
      extraData: freezed == extraData
          ? _value.extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as ExtraData?,
      lessonInfo: freezed == lessonInfo
          ? _value.lessonInfo
          : lessonInfo // ignore: cast_nullable_to_non_nullable
              as LessonInfo?,
      buttonList: freezed == buttonList
          ? _value.buttonList
          : buttonList // ignore: cast_nullable_to_non_nullable
              as List<ButtonList>?,
      outerFunc: freezed == outerFunc
          ? _value.outerFunc
          : outerFunc // ignore: cast_nullable_to_non_nullable
              as List<OuterFunc>?,
      lessonRefFunc: freezed == lessonRefFunc
          ? _value.lessonRefFunc
          : lessonRefFunc // ignore: cast_nullable_to_non_nullable
              as List<LessonRefFunc>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseInfoCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $CourseInfoCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassInfoCopyWith<$Res>? get classInfo {
    if (_value.classInfo == null) {
      return null;
    }

    return $ClassInfoCopyWith<$Res>(_value.classInfo!, (value) {
      return _then(_value.copyWith(classInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtraDataCopyWith<$Res>? get extraData {
    if (_value.extraData == null) {
      return null;
    }

    return $ExtraDataCopyWith<$Res>(_value.extraData!, (value) {
      return _then(_value.copyWith(extraData: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LessonInfoCopyWith<$Res>? get lessonInfo {
    if (_value.lessonInfo == null) {
      return null;
    }

    return $LessonInfoCopyWith<$Res>(_value.lessonInfo!, (value) {
      return _then(_value.copyWith(lessonInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseSessionListInfoCopyWith<$Res>
    implements $CourseSessionListInfoCopyWith<$Res> {
  factory _$$_CourseSessionListInfoCopyWith(_$_CourseSessionListInfo value,
          $Res Function(_$_CourseSessionListInfo) then) =
      __$$_CourseSessionListInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CourseInfo? courseInfo,
      ClassInfo? classInfo,
      ExtraData? extraData,
      LessonInfo? lessonInfo,
      List<ButtonList>? buttonList,
      List<OuterFunc>? outerFunc,
      List<LessonRefFunc>? lessonRefFunc});

  @override
  $CourseInfoCopyWith<$Res>? get courseInfo;
  @override
  $ClassInfoCopyWith<$Res>? get classInfo;
  @override
  $ExtraDataCopyWith<$Res>? get extraData;
  @override
  $LessonInfoCopyWith<$Res>? get lessonInfo;
}

/// @nodoc
class __$$_CourseSessionListInfoCopyWithImpl<$Res>
    extends _$CourseSessionListInfoCopyWithImpl<$Res, _$_CourseSessionListInfo>
    implements _$$_CourseSessionListInfoCopyWith<$Res> {
  __$$_CourseSessionListInfoCopyWithImpl(_$_CourseSessionListInfo _value,
      $Res Function(_$_CourseSessionListInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
    Object? extraData = freezed,
    Object? lessonInfo = freezed,
    Object? buttonList = freezed,
    Object? outerFunc = freezed,
    Object? lessonRefFunc = freezed,
  }) {
    return _then(_$_CourseSessionListInfo(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfo?,
      extraData: freezed == extraData
          ? _value.extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as ExtraData?,
      lessonInfo: freezed == lessonInfo
          ? _value.lessonInfo
          : lessonInfo // ignore: cast_nullable_to_non_nullable
              as LessonInfo?,
      buttonList: freezed == buttonList
          ? _value._buttonList
          : buttonList // ignore: cast_nullable_to_non_nullable
              as List<ButtonList>?,
      outerFunc: freezed == outerFunc
          ? _value._outerFunc
          : outerFunc // ignore: cast_nullable_to_non_nullable
              as List<OuterFunc>?,
      lessonRefFunc: freezed == lessonRefFunc
          ? _value._lessonRefFunc
          : lessonRefFunc // ignore: cast_nullable_to_non_nullable
              as List<LessonRefFunc>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSessionListInfo implements _CourseSessionListInfo {
  const _$_CourseSessionListInfo(
      {this.courseInfo,
      this.classInfo,
      this.extraData,
      this.lessonInfo,
      final List<ButtonList>? buttonList,
      final List<OuterFunc>? outerFunc,
      final List<LessonRefFunc>? lessonRefFunc})
      : _buttonList = buttonList,
        _outerFunc = outerFunc,
        _lessonRefFunc = lessonRefFunc;

  factory _$_CourseSessionListInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSessionListInfoFromJson(json);

  @override
  final CourseInfo? courseInfo;
  @override
  final ClassInfo? classInfo;
  @override
  final ExtraData? extraData;
  @override
  final LessonInfo? lessonInfo;
  final List<ButtonList>? _buttonList;
  @override
  List<ButtonList>? get buttonList {
    final value = _buttonList;
    if (value == null) return null;
    if (_buttonList is EqualUnmodifiableListView) return _buttonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<OuterFunc>? _outerFunc;
  @override
  List<OuterFunc>? get outerFunc {
    final value = _outerFunc;
    if (value == null) return null;
    if (_outerFunc is EqualUnmodifiableListView) return _outerFunc;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<LessonRefFunc>? _lessonRefFunc;
  @override
  List<LessonRefFunc>? get lessonRefFunc {
    final value = _lessonRefFunc;
    if (value == null) return null;
    if (_lessonRefFunc is EqualUnmodifiableListView) return _lessonRefFunc;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseSessionListInfo(courseInfo: $courseInfo, classInfo: $classInfo, extraData: $extraData, lessonInfo: $lessonInfo, buttonList: $buttonList, outerFunc: $outerFunc, lessonRefFunc: $lessonRefFunc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseSessionListInfo &&
            (identical(other.courseInfo, courseInfo) ||
                other.courseInfo == courseInfo) &&
            (identical(other.classInfo, classInfo) ||
                other.classInfo == classInfo) &&
            (identical(other.extraData, extraData) ||
                other.extraData == extraData) &&
            (identical(other.lessonInfo, lessonInfo) ||
                other.lessonInfo == lessonInfo) &&
            const DeepCollectionEquality()
                .equals(other._buttonList, _buttonList) &&
            const DeepCollectionEquality()
                .equals(other._outerFunc, _outerFunc) &&
            const DeepCollectionEquality()
                .equals(other._lessonRefFunc, _lessonRefFunc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseInfo,
      classInfo,
      extraData,
      lessonInfo,
      const DeepCollectionEquality().hash(_buttonList),
      const DeepCollectionEquality().hash(_outerFunc),
      const DeepCollectionEquality().hash(_lessonRefFunc));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSessionListInfoCopyWith<_$_CourseSessionListInfo> get copyWith =>
      __$$_CourseSessionListInfoCopyWithImpl<_$_CourseSessionListInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSessionListInfoToJson(
      this,
    );
  }
}

abstract class _CourseSessionListInfo implements CourseSessionListInfo {
  const factory _CourseSessionListInfo(
      {final CourseInfo? courseInfo,
      final ClassInfo? classInfo,
      final ExtraData? extraData,
      final LessonInfo? lessonInfo,
      final List<ButtonList>? buttonList,
      final List<OuterFunc>? outerFunc,
      final List<LessonRefFunc>? lessonRefFunc}) = _$_CourseSessionListInfo;

  factory _CourseSessionListInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseSessionListInfo.fromJson;

  @override
  CourseInfo? get courseInfo;
  @override
  ClassInfo? get classInfo;
  @override
  ExtraData? get extraData;
  @override
  LessonInfo? get lessonInfo;
  @override
  List<ButtonList>? get buttonList;
  @override
  List<OuterFunc>? get outerFunc;
  @override
  List<LessonRefFunc>? get lessonRefFunc;
  @override
  @JsonKey(ignore: true)
  _$$_CourseSessionListInfoCopyWith<_$_CourseSessionListInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ButtonList _$ButtonListFromJson(Map<String, dynamic> json) {
  return _ButtonList.fromJson(json);
}

/// @nodoc
mixin _$ButtonList {
  int? get isLock => throw _privateConstructorUsedError;
  String? get buttonName => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get audio => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;
  int? get isRead => throw _privateConstructorUsedError;
  ButtonListExtend? get extend => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ButtonListCopyWith<ButtonList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ButtonListCopyWith<$Res> {
  factory $ButtonListCopyWith(
          ButtonList value, $Res Function(ButtonList) then) =
      _$ButtonListCopyWithImpl<$Res, ButtonList>;
  @useResult
  $Res call(
      {int? isLock,
      String? buttonName,
      String? icon,
      String? audio,
      String? tip,
      String? text,
      String? router,
      int? isRead,
      ButtonListExtend? extend});

  $ButtonListExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class _$ButtonListCopyWithImpl<$Res, $Val extends ButtonList>
    implements $ButtonListCopyWith<$Res> {
  _$ButtonListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLock = freezed,
    Object? buttonName = freezed,
    Object? icon = freezed,
    Object? audio = freezed,
    Object? tip = freezed,
    Object? text = freezed,
    Object? router = freezed,
    Object? isRead = freezed,
    Object? extend = freezed,
  }) {
    return _then(_value.copyWith(
      isLock: freezed == isLock
          ? _value.isLock
          : isLock // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      isRead: freezed == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as int?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as ButtonListExtend?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonListExtendCopyWith<$Res>? get extend {
    if (_value.extend == null) {
      return null;
    }

    return $ButtonListExtendCopyWith<$Res>(_value.extend!, (value) {
      return _then(_value.copyWith(extend: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ButtonListCopyWith<$Res>
    implements $ButtonListCopyWith<$Res> {
  factory _$$_ButtonListCopyWith(
          _$_ButtonList value, $Res Function(_$_ButtonList) then) =
      __$$_ButtonListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? isLock,
      String? buttonName,
      String? icon,
      String? audio,
      String? tip,
      String? text,
      String? router,
      int? isRead,
      ButtonListExtend? extend});

  @override
  $ButtonListExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class __$$_ButtonListCopyWithImpl<$Res>
    extends _$ButtonListCopyWithImpl<$Res, _$_ButtonList>
    implements _$$_ButtonListCopyWith<$Res> {
  __$$_ButtonListCopyWithImpl(
      _$_ButtonList _value, $Res Function(_$_ButtonList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLock = freezed,
    Object? buttonName = freezed,
    Object? icon = freezed,
    Object? audio = freezed,
    Object? tip = freezed,
    Object? text = freezed,
    Object? router = freezed,
    Object? isRead = freezed,
    Object? extend = freezed,
  }) {
    return _then(_$_ButtonList(
      isLock: freezed == isLock
          ? _value.isLock
          : isLock // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonName: freezed == buttonName
          ? _value.buttonName
          : buttonName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      isRead: freezed == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as int?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as ButtonListExtend?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ButtonList implements _ButtonList {
  const _$_ButtonList(
      {this.isLock,
      this.buttonName,
      this.icon,
      this.audio,
      this.tip,
      this.text,
      this.router,
      this.isRead,
      this.extend});

  factory _$_ButtonList.fromJson(Map<String, dynamic> json) =>
      _$$_ButtonListFromJson(json);

  @override
  final int? isLock;
  @override
  final String? buttonName;
  @override
  final String? icon;
  @override
  final String? audio;
  @override
  final String? tip;
  @override
  final String? text;
  @override
  final String? router;
  @override
  final int? isRead;
  @override
  final ButtonListExtend? extend;

  @override
  String toString() {
    return 'ButtonList(isLock: $isLock, buttonName: $buttonName, icon: $icon, audio: $audio, tip: $tip, text: $text, router: $router, isRead: $isRead, extend: $extend)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ButtonList &&
            (identical(other.isLock, isLock) || other.isLock == isLock) &&
            (identical(other.buttonName, buttonName) ||
                other.buttonName == buttonName) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.audio, audio) || other.audio == audio) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.extend, extend) || other.extend == extend));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, isLock, buttonName, icon, audio,
      tip, text, router, isRead, extend);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ButtonListCopyWith<_$_ButtonList> get copyWith =>
      __$$_ButtonListCopyWithImpl<_$_ButtonList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ButtonListToJson(
      this,
    );
  }
}

abstract class _ButtonList implements ButtonList {
  const factory _ButtonList(
      {final int? isLock,
      final String? buttonName,
      final String? icon,
      final String? audio,
      final String? tip,
      final String? text,
      final String? router,
      final int? isRead,
      final ButtonListExtend? extend}) = _$_ButtonList;

  factory _ButtonList.fromJson(Map<String, dynamic> json) =
      _$_ButtonList.fromJson;

  @override
  int? get isLock;
  @override
  String? get buttonName;
  @override
  String? get icon;
  @override
  String? get audio;
  @override
  String? get tip;
  @override
  String? get text;
  @override
  String? get router;
  @override
  int? get isRead;
  @override
  ButtonListExtend? get extend;
  @override
  @JsonKey(ignore: true)
  _$$_ButtonListCopyWith<_$_ButtonList> get copyWith =>
      throw _privateConstructorUsedError;
}

ButtonListExtend _$ButtonListExtendFromJson(Map<String, dynamic> json) {
  return _ButtonListExtend.fromJson(json);
}

/// @nodoc
mixin _$ButtonListExtend {
  String? get image => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ButtonListExtendCopyWith<ButtonListExtend> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ButtonListExtendCopyWith<$Res> {
  factory $ButtonListExtendCopyWith(
          ButtonListExtend value, $Res Function(ButtonListExtend) then) =
      _$ButtonListExtendCopyWithImpl<$Res, ButtonListExtend>;
  @useResult
  $Res call({String? image});
}

/// @nodoc
class _$ButtonListExtendCopyWithImpl<$Res, $Val extends ButtonListExtend>
    implements $ButtonListExtendCopyWith<$Res> {
  _$ButtonListExtendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
  }) {
    return _then(_value.copyWith(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ButtonListExtendCopyWith<$Res>
    implements $ButtonListExtendCopyWith<$Res> {
  factory _$$_ButtonListExtendCopyWith(
          _$_ButtonListExtend value, $Res Function(_$_ButtonListExtend) then) =
      __$$_ButtonListExtendCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? image});
}

/// @nodoc
class __$$_ButtonListExtendCopyWithImpl<$Res>
    extends _$ButtonListExtendCopyWithImpl<$Res, _$_ButtonListExtend>
    implements _$$_ButtonListExtendCopyWith<$Res> {
  __$$_ButtonListExtendCopyWithImpl(
      _$_ButtonListExtend _value, $Res Function(_$_ButtonListExtend) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
  }) {
    return _then(_$_ButtonListExtend(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ButtonListExtend implements _ButtonListExtend {
  const _$_ButtonListExtend({this.image = null});

  factory _$_ButtonListExtend.fromJson(Map<String, dynamic> json) =>
      _$$_ButtonListExtendFromJson(json);

  @override
  @JsonKey()
  final String? image;

  @override
  String toString() {
    return 'ButtonListExtend(image: $image)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ButtonListExtend &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ButtonListExtendCopyWith<_$_ButtonListExtend> get copyWith =>
      __$$_ButtonListExtendCopyWithImpl<_$_ButtonListExtend>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ButtonListExtendToJson(
      this,
    );
  }
}

abstract class _ButtonListExtend implements ButtonListExtend {
  const factory _ButtonListExtend({final String? image}) = _$_ButtonListExtend;

  factory _ButtonListExtend.fromJson(Map<String, dynamic> json) =
      _$_ButtonListExtend.fromJson;

  @override
  String? get image;
  @override
  @JsonKey(ignore: true)
  _$$_ButtonListExtendCopyWith<_$_ButtonListExtend> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassInfo _$ClassInfoFromJson(Map<String, dynamic> json) {
  return _ClassInfo.fromJson(json);
}

/// @nodoc
mixin _$ClassInfo {
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  String? get className => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassInfoCopyWith<ClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassInfoCopyWith<$Res> {
  factory $ClassInfoCopyWith(ClassInfo value, $Res Function(ClassInfo) then) =
      _$ClassInfoCopyWithImpl<$Res, ClassInfo>;
  @useResult
  $Res call(
      {int? classId,
      String? classKey,
      String? className,
      String? teacherName,
      int? teacherId});
}

/// @nodoc
class _$ClassInfoCopyWithImpl<$Res, $Val extends ClassInfo>
    implements $ClassInfoCopyWith<$Res> {
  _$ClassInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
    Object? teacherName = freezed,
    Object? teacherId = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassInfoCopyWith<$Res> implements $ClassInfoCopyWith<$Res> {
  factory _$$_ClassInfoCopyWith(
          _$_ClassInfo value, $Res Function(_$_ClassInfo) then) =
      __$$_ClassInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? classId,
      String? classKey,
      String? className,
      String? teacherName,
      int? teacherId});
}

/// @nodoc
class __$$_ClassInfoCopyWithImpl<$Res>
    extends _$ClassInfoCopyWithImpl<$Res, _$_ClassInfo>
    implements _$$_ClassInfoCopyWith<$Res> {
  __$$_ClassInfoCopyWithImpl(
      _$_ClassInfo _value, $Res Function(_$_ClassInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
    Object? teacherName = freezed,
    Object? teacherId = freezed,
  }) {
    return _then(_$_ClassInfo(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassInfo implements _ClassInfo {
  const _$_ClassInfo(
      {this.classId,
      this.classKey,
      this.className,
      this.teacherName,
      this.teacherId});

  factory _$_ClassInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassInfoFromJson(json);

  @override
  final int? classId;
  @override
  final String? classKey;
  @override
  final String? className;
  @override
  final String? teacherName;
  @override
  final int? teacherId;

  @override
  String toString() {
    return 'ClassInfo(classId: $classId, classKey: $classKey, className: $className, teacherName: $teacherName, teacherId: $teacherId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassInfo &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.className, className) ||
                other.className == className) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, classId, classKey, className, teacherName, teacherId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassInfoCopyWith<_$_ClassInfo> get copyWith =>
      __$$_ClassInfoCopyWithImpl<_$_ClassInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassInfoToJson(
      this,
    );
  }
}

abstract class _ClassInfo implements ClassInfo {
  const factory _ClassInfo(
      {final int? classId,
      final String? classKey,
      final String? className,
      final String? teacherName,
      final int? teacherId}) = _$_ClassInfo;

  factory _ClassInfo.fromJson(Map<String, dynamic> json) =
      _$_ClassInfo.fromJson;

  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  String? get className;
  @override
  String? get teacherName;
  @override
  int? get teacherId;
  @override
  @JsonKey(ignore: true)
  _$$_ClassInfoCopyWith<_$_ClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfo _$CourseInfoFromJson(Map<String, dynamic> json) {
  return _CourseInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseInfo {
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get language => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoCopyWith<CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoCopyWith<$Res> {
  factory $CourseInfoCopyWith(
          CourseInfo value, $Res Function(CourseInfo) then) =
      _$CourseInfoCopyWithImpl<$Res, CourseInfo>;
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      String? courseSegment,
      int? courseSegmentCode,
      int? subjectType,
      String? subjectTypeDesc,
      String? language,
      int? courseType,
      String? courseTypeDesc});
}

/// @nodoc
class _$CourseInfoCopyWithImpl<$Res, $Val extends CourseInfo>
    implements $CourseInfoCopyWith<$Res> {
  _$CourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? courseSegmentCode = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? language = freezed,
    Object? courseType = freezed,
    Object? courseTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoCopyWith<$Res>
    implements $CourseInfoCopyWith<$Res> {
  factory _$$_CourseInfoCopyWith(
          _$_CourseInfo value, $Res Function(_$_CourseInfo) then) =
      __$$_CourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      String? courseSegment,
      int? courseSegmentCode,
      int? subjectType,
      String? subjectTypeDesc,
      String? language,
      int? courseType,
      String? courseTypeDesc});
}

/// @nodoc
class __$$_CourseInfoCopyWithImpl<$Res>
    extends _$CourseInfoCopyWithImpl<$Res, _$_CourseInfo>
    implements _$$_CourseInfoCopyWith<$Res> {
  __$$_CourseInfoCopyWithImpl(
      _$_CourseInfo _value, $Res Function(_$_CourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? courseSegmentCode = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? language = freezed,
    Object? courseType = freezed,
    Object? courseTypeDesc = freezed,
  }) {
    return _then(_$_CourseInfo(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfo implements _CourseInfo {
  const _$_CourseInfo(
      {this.courseId,
      this.courseKey,
      this.courseName,
      this.courseSegment,
      this.courseSegmentCode,
      this.subjectType,
      this.subjectTypeDesc,
      this.language,
      this.courseType,
      this.courseTypeDesc});

  factory _$_CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoFromJson(json);

  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final String? courseSegment;
  @override
  final int? courseSegmentCode;
  @override
  final int? subjectType;
  @override
  final String? subjectTypeDesc;
  @override
  final String? language;
  @override
  final int? courseType;
  @override
  final String? courseTypeDesc;

  @override
  String toString() {
    return 'CourseInfo(courseId: $courseId, courseKey: $courseKey, courseName: $courseName, courseSegment: $courseSegment, courseSegmentCode: $courseSegmentCode, subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, language: $language, courseType: $courseType, courseTypeDesc: $courseTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfo &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.courseSegmentCode, courseSegmentCode) ||
                other.courseSegmentCode == courseSegmentCode) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseTypeDesc, courseTypeDesc) ||
                other.courseTypeDesc == courseTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseId,
      courseKey,
      courseName,
      courseSegment,
      courseSegmentCode,
      subjectType,
      subjectTypeDesc,
      language,
      courseType,
      courseTypeDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      __$$_CourseInfoCopyWithImpl<_$_CourseInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoToJson(
      this,
    );
  }
}

abstract class _CourseInfo implements CourseInfo {
  const factory _CourseInfo(
      {final int? courseId,
      final String? courseKey,
      final String? courseName,
      final String? courseSegment,
      final int? courseSegmentCode,
      final int? subjectType,
      final String? subjectTypeDesc,
      final String? language,
      final int? courseType,
      final String? courseTypeDesc}) = _$_CourseInfo;

  factory _CourseInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseInfo.fromJson;

  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  String? get courseSegment;
  @override
  int? get courseSegmentCode;
  @override
  int? get subjectType;
  @override
  String? get subjectTypeDesc;
  @override
  String? get language;
  @override
  int? get courseType;
  @override
  String? get courseTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ExtraData _$ExtraDataFromJson(Map<String, dynamic> json) {
  return _ExtraData.fromJson(json);
}

/// @nodoc
mixin _$ExtraData {
  PutOnRecords? get putOnRecords => throw _privateConstructorUsedError;
  BgInfo? get bgInfo => throw _privateConstructorUsedError;
  String? get stepLockedAudioUrl => throw _privateConstructorUsedError;
  int? get isEmotionDiscriminate => throw _privateConstructorUsedError;
  int? get engineType => throw _privateConstructorUsedError;
  int? get gameCourseType => throw _privateConstructorUsedError;
  String? get firstGuideAudioUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraDataCopyWith<ExtraData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraDataCopyWith<$Res> {
  factory $ExtraDataCopyWith(ExtraData value, $Res Function(ExtraData) then) =
      _$ExtraDataCopyWithImpl<$Res, ExtraData>;
  @useResult
  $Res call(
      {PutOnRecords? putOnRecords,
      BgInfo? bgInfo,
      String? stepLockedAudioUrl,
      int? isEmotionDiscriminate,
      int? engineType,
      int? gameCourseType,
      String? firstGuideAudioUrl});

  $PutOnRecordsCopyWith<$Res>? get putOnRecords;
  $BgInfoCopyWith<$Res>? get bgInfo;
}

/// @nodoc
class _$ExtraDataCopyWithImpl<$Res, $Val extends ExtraData>
    implements $ExtraDataCopyWith<$Res> {
  _$ExtraDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? putOnRecords = freezed,
    Object? bgInfo = freezed,
    Object? stepLockedAudioUrl = freezed,
    Object? isEmotionDiscriminate = freezed,
    Object? engineType = freezed,
    Object? gameCourseType = freezed,
    Object? firstGuideAudioUrl = freezed,
  }) {
    return _then(_value.copyWith(
      putOnRecords: freezed == putOnRecords
          ? _value.putOnRecords
          : putOnRecords // ignore: cast_nullable_to_non_nullable
              as PutOnRecords?,
      bgInfo: freezed == bgInfo
          ? _value.bgInfo
          : bgInfo // ignore: cast_nullable_to_non_nullable
              as BgInfo?,
      stepLockedAudioUrl: freezed == stepLockedAudioUrl
          ? _value.stepLockedAudioUrl
          : stepLockedAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmotionDiscriminate: freezed == isEmotionDiscriminate
          ? _value.isEmotionDiscriminate
          : isEmotionDiscriminate // ignore: cast_nullable_to_non_nullable
              as int?,
      engineType: freezed == engineType
          ? _value.engineType
          : engineType // ignore: cast_nullable_to_non_nullable
              as int?,
      gameCourseType: freezed == gameCourseType
          ? _value.gameCourseType
          : gameCourseType // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGuideAudioUrl: freezed == firstGuideAudioUrl
          ? _value.firstGuideAudioUrl
          : firstGuideAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PutOnRecordsCopyWith<$Res>? get putOnRecords {
    if (_value.putOnRecords == null) {
      return null;
    }

    return $PutOnRecordsCopyWith<$Res>(_value.putOnRecords!, (value) {
      return _then(_value.copyWith(putOnRecords: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $BgInfoCopyWith<$Res>? get bgInfo {
    if (_value.bgInfo == null) {
      return null;
    }

    return $BgInfoCopyWith<$Res>(_value.bgInfo!, (value) {
      return _then(_value.copyWith(bgInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ExtraDataCopyWith<$Res> implements $ExtraDataCopyWith<$Res> {
  factory _$$_ExtraDataCopyWith(
          _$_ExtraData value, $Res Function(_$_ExtraData) then) =
      __$$_ExtraDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PutOnRecords? putOnRecords,
      BgInfo? bgInfo,
      String? stepLockedAudioUrl,
      int? isEmotionDiscriminate,
      int? engineType,
      int? gameCourseType,
      String? firstGuideAudioUrl});

  @override
  $PutOnRecordsCopyWith<$Res>? get putOnRecords;
  @override
  $BgInfoCopyWith<$Res>? get bgInfo;
}

/// @nodoc
class __$$_ExtraDataCopyWithImpl<$Res>
    extends _$ExtraDataCopyWithImpl<$Res, _$_ExtraData>
    implements _$$_ExtraDataCopyWith<$Res> {
  __$$_ExtraDataCopyWithImpl(
      _$_ExtraData _value, $Res Function(_$_ExtraData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? putOnRecords = freezed,
    Object? bgInfo = freezed,
    Object? stepLockedAudioUrl = freezed,
    Object? isEmotionDiscriminate = freezed,
    Object? engineType = freezed,
    Object? gameCourseType = freezed,
    Object? firstGuideAudioUrl = freezed,
  }) {
    return _then(_$_ExtraData(
      putOnRecords: freezed == putOnRecords
          ? _value.putOnRecords
          : putOnRecords // ignore: cast_nullable_to_non_nullable
              as PutOnRecords?,
      bgInfo: freezed == bgInfo
          ? _value.bgInfo
          : bgInfo // ignore: cast_nullable_to_non_nullable
              as BgInfo?,
      stepLockedAudioUrl: freezed == stepLockedAudioUrl
          ? _value.stepLockedAudioUrl
          : stepLockedAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmotionDiscriminate: freezed == isEmotionDiscriminate
          ? _value.isEmotionDiscriminate
          : isEmotionDiscriminate // ignore: cast_nullable_to_non_nullable
              as int?,
      engineType: freezed == engineType
          ? _value.engineType
          : engineType // ignore: cast_nullable_to_non_nullable
              as int?,
      gameCourseType: freezed == gameCourseType
          ? _value.gameCourseType
          : gameCourseType // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGuideAudioUrl: freezed == firstGuideAudioUrl
          ? _value.firstGuideAudioUrl
          : firstGuideAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExtraData implements _ExtraData {
  const _$_ExtraData(
      {this.putOnRecords,
      this.bgInfo,
      this.stepLockedAudioUrl,
      this.isEmotionDiscriminate,
      this.engineType,
      this.gameCourseType,
      this.firstGuideAudioUrl});

  factory _$_ExtraData.fromJson(Map<String, dynamic> json) =>
      _$$_ExtraDataFromJson(json);

  @override
  final PutOnRecords? putOnRecords;
  @override
  final BgInfo? bgInfo;
  @override
  final String? stepLockedAudioUrl;
  @override
  final int? isEmotionDiscriminate;
  @override
  final int? engineType;
  @override
  final int? gameCourseType;
  @override
  final String? firstGuideAudioUrl;

  @override
  String toString() {
    return 'ExtraData(putOnRecords: $putOnRecords, bgInfo: $bgInfo, stepLockedAudioUrl: $stepLockedAudioUrl, isEmotionDiscriminate: $isEmotionDiscriminate, engineType: $engineType, gameCourseType: $gameCourseType, firstGuideAudioUrl: $firstGuideAudioUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExtraData &&
            (identical(other.putOnRecords, putOnRecords) ||
                other.putOnRecords == putOnRecords) &&
            (identical(other.bgInfo, bgInfo) || other.bgInfo == bgInfo) &&
            (identical(other.stepLockedAudioUrl, stepLockedAudioUrl) ||
                other.stepLockedAudioUrl == stepLockedAudioUrl) &&
            (identical(other.isEmotionDiscriminate, isEmotionDiscriminate) ||
                other.isEmotionDiscriminate == isEmotionDiscriminate) &&
            (identical(other.engineType, engineType) ||
                other.engineType == engineType) &&
            (identical(other.gameCourseType, gameCourseType) ||
                other.gameCourseType == gameCourseType) &&
            (identical(other.firstGuideAudioUrl, firstGuideAudioUrl) ||
                other.firstGuideAudioUrl == firstGuideAudioUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      putOnRecords,
      bgInfo,
      stepLockedAudioUrl,
      isEmotionDiscriminate,
      engineType,
      gameCourseType,
      firstGuideAudioUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtraDataCopyWith<_$_ExtraData> get copyWith =>
      __$$_ExtraDataCopyWithImpl<_$_ExtraData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtraDataToJson(
      this,
    );
  }
}

abstract class _ExtraData implements ExtraData {
  const factory _ExtraData(
      {final PutOnRecords? putOnRecords,
      final BgInfo? bgInfo,
      final String? stepLockedAudioUrl,
      final int? isEmotionDiscriminate,
      final int? engineType,
      final int? gameCourseType,
      final String? firstGuideAudioUrl}) = _$_ExtraData;

  factory _ExtraData.fromJson(Map<String, dynamic> json) =
      _$_ExtraData.fromJson;

  @override
  PutOnRecords? get putOnRecords;
  @override
  BgInfo? get bgInfo;
  @override
  String? get stepLockedAudioUrl;
  @override
  int? get isEmotionDiscriminate;
  @override
  int? get engineType;
  @override
  int? get gameCourseType;
  @override
  String? get firstGuideAudioUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ExtraDataCopyWith<_$_ExtraData> get copyWith =>
      throw _privateConstructorUsedError;
}

PutOnRecords _$PutOnRecordsFromJson(Map<String, dynamic> json) {
  return _PutOnRecords.fromJson(json);
}

/// @nodoc
mixin _$PutOnRecords {
  String? get name => throw _privateConstructorUsedError;
  String? get internetNum => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PutOnRecordsCopyWith<PutOnRecords> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PutOnRecordsCopyWith<$Res> {
  factory $PutOnRecordsCopyWith(
          PutOnRecords value, $Res Function(PutOnRecords) then) =
      _$PutOnRecordsCopyWithImpl<$Res, PutOnRecords>;
  @useResult
  $Res call({String? name, String? internetNum});
}

/// @nodoc
class _$PutOnRecordsCopyWithImpl<$Res, $Val extends PutOnRecords>
    implements $PutOnRecordsCopyWith<$Res> {
  _$PutOnRecordsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? internetNum = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internetNum: freezed == internetNum
          ? _value.internetNum
          : internetNum // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PutOnRecordsCopyWith<$Res>
    implements $PutOnRecordsCopyWith<$Res> {
  factory _$$_PutOnRecordsCopyWith(
          _$_PutOnRecords value, $Res Function(_$_PutOnRecords) then) =
      __$$_PutOnRecordsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? internetNum});
}

/// @nodoc
class __$$_PutOnRecordsCopyWithImpl<$Res>
    extends _$PutOnRecordsCopyWithImpl<$Res, _$_PutOnRecords>
    implements _$$_PutOnRecordsCopyWith<$Res> {
  __$$_PutOnRecordsCopyWithImpl(
      _$_PutOnRecords _value, $Res Function(_$_PutOnRecords) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? internetNum = freezed,
  }) {
    return _then(_$_PutOnRecords(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internetNum: freezed == internetNum
          ? _value.internetNum
          : internetNum // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PutOnRecords implements _PutOnRecords {
  const _$_PutOnRecords({this.name, this.internetNum});

  factory _$_PutOnRecords.fromJson(Map<String, dynamic> json) =>
      _$$_PutOnRecordsFromJson(json);

  @override
  final String? name;
  @override
  final String? internetNum;

  @override
  String toString() {
    return 'PutOnRecords(name: $name, internetNum: $internetNum)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PutOnRecords &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.internetNum, internetNum) ||
                other.internetNum == internetNum));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, internetNum);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PutOnRecordsCopyWith<_$_PutOnRecords> get copyWith =>
      __$$_PutOnRecordsCopyWithImpl<_$_PutOnRecords>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PutOnRecordsToJson(
      this,
    );
  }
}

abstract class _PutOnRecords implements PutOnRecords {
  const factory _PutOnRecords({final String? name, final String? internetNum}) =
      _$_PutOnRecords;

  factory _PutOnRecords.fromJson(Map<String, dynamic> json) =
      _$_PutOnRecords.fromJson;

  @override
  String? get name;
  @override
  String? get internetNum;
  @override
  @JsonKey(ignore: true)
  _$$_PutOnRecordsCopyWith<_$_PutOnRecords> get copyWith =>
      throw _privateConstructorUsedError;
}

BgInfo _$BgInfoFromJson(Map<String, dynamic> json) {
  return _BgInfo.fromJson(json);
}

/// @nodoc
mixin _$BgInfo {
  String? get bgColor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BgInfoCopyWith<BgInfo> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BgInfoCopyWith<$Res> {
  factory $BgInfoCopyWith(BgInfo value, $Res Function(BgInfo) then) =
      _$BgInfoCopyWithImpl<$Res, BgInfo>;
  @useResult
  $Res call({String? bgColor});
}

/// @nodoc
class _$BgInfoCopyWithImpl<$Res, $Val extends BgInfo>
    implements $BgInfoCopyWith<$Res> {
  _$BgInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bgColor = freezed,
  }) {
    return _then(_value.copyWith(
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BgInfoCopyWith<$Res> implements $BgInfoCopyWith<$Res> {
  factory _$$_BgInfoCopyWith(_$_BgInfo value, $Res Function(_$_BgInfo) then) =
      __$$_BgInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? bgColor});
}

/// @nodoc
class __$$_BgInfoCopyWithImpl<$Res>
    extends _$BgInfoCopyWithImpl<$Res, _$_BgInfo>
    implements _$$_BgInfoCopyWith<$Res> {
  __$$_BgInfoCopyWithImpl(_$_BgInfo _value, $Res Function(_$_BgInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bgColor = freezed,
  }) {
    return _then(_$_BgInfo(
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BgInfo implements _BgInfo {
  const _$_BgInfo({this.bgColor});

  factory _$_BgInfo.fromJson(Map<String, dynamic> json) =>
      _$$_BgInfoFromJson(json);

  @override
  final String? bgColor;

  @override
  String toString() {
    return 'BgInfo(bgColor: $bgColor)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BgInfo &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bgColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BgInfoCopyWith<_$_BgInfo> get copyWith =>
      __$$_BgInfoCopyWithImpl<_$_BgInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BgInfoToJson(
      this,
    );
  }
}

abstract class _BgInfo implements BgInfo {
  const factory _BgInfo({final String? bgColor}) = _$_BgInfo;

  factory _BgInfo.fromJson(Map<String, dynamic> json) = _$_BgInfo.fromJson;

  @override
  String? get bgColor;
  @override
  @JsonKey(ignore: true)
  _$$_BgInfoCopyWith<_$_BgInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfo _$LessonInfoFromJson(Map<String, dynamic> json) {
  return _LessonInfo.fromJson(json);
}

/// @nodoc
mixin _$LessonInfo {
  int? get id => throw _privateConstructorUsedError;
  int? get scheduleId => throw _privateConstructorUsedError;
  String? get orderDesc => throw _privateConstructorUsedError;
  String? get key => throw _privateConstructorUsedError;
  String? get lessonKey => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get subtitle => throw _privateConstructorUsedError;
  bool? get isUnLock => throw _privateConstructorUsedError;
  int? get isFinish => throw _privateConstructorUsedError;
  String? get coverImageUrl => throw _privateConstructorUsedError;
  List<StepList>? get stepList => throw _privateConstructorUsedError;
  List<StepList>? get hideStepList => throw _privateConstructorUsedError;
  List<StepList>? get steps => throw _privateConstructorUsedError;
  String? get version => throw _privateConstructorUsedError;
  String? get segmentKey => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  int? get startTime => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoCopyWith<LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoCopyWith<$Res> {
  factory $LessonInfoCopyWith(
          LessonInfo value, $Res Function(LessonInfo) then) =
      _$LessonInfoCopyWithImpl<$Res, LessonInfo>;
  @useResult
  $Res call(
      {int? id,
      int? scheduleId,
      String? orderDesc,
      String? key,
      String? lessonKey,
      String? name,
      String? subtitle,
      bool? isUnLock,
      int? isFinish,
      String? coverImageUrl,
      List<StepList>? stepList,
      List<StepList>? hideStepList,
      List<StepList>? steps,
      String? version,
      String? segmentKey,
      int? segmentId,
      int? weekId,
      int? startTime,
      int? endTime});
}

/// @nodoc
class _$LessonInfoCopyWithImpl<$Res, $Val extends LessonInfo>
    implements $LessonInfoCopyWith<$Res> {
  _$LessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? scheduleId = freezed,
    Object? orderDesc = freezed,
    Object? key = freezed,
    Object? lessonKey = freezed,
    Object? name = freezed,
    Object? subtitle = freezed,
    Object? isUnLock = freezed,
    Object? isFinish = freezed,
    Object? coverImageUrl = freezed,
    Object? stepList = freezed,
    Object? hideStepList = freezed,
    Object? steps = freezed,
    Object? version = freezed,
    Object? segmentKey = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleId: freezed == scheduleId
          ? _value.scheduleId
          : scheduleId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderDesc: freezed == orderDesc
          ? _value.orderDesc
          : orderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subtitle: freezed == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String?,
      isUnLock: freezed == isUnLock
          ? _value.isUnLock
          : isUnLock // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      coverImageUrl: freezed == coverImageUrl
          ? _value.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      stepList: freezed == stepList
          ? _value.stepList
          : stepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      hideStepList: freezed == hideStepList
          ? _value.hideStepList
          : hideStepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      steps: freezed == steps
          ? _value.steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonInfoCopyWith<$Res>
    implements $LessonInfoCopyWith<$Res> {
  factory _$$_LessonInfoCopyWith(
          _$_LessonInfo value, $Res Function(_$_LessonInfo) then) =
      __$$_LessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? scheduleId,
      String? orderDesc,
      String? key,
      String? lessonKey,
      String? name,
      String? subtitle,
      bool? isUnLock,
      int? isFinish,
      String? coverImageUrl,
      List<StepList>? stepList,
      List<StepList>? hideStepList,
      List<StepList>? steps,
      String? version,
      String? segmentKey,
      int? segmentId,
      int? weekId,
      int? startTime,
      int? endTime});
}

/// @nodoc
class __$$_LessonInfoCopyWithImpl<$Res>
    extends _$LessonInfoCopyWithImpl<$Res, _$_LessonInfo>
    implements _$$_LessonInfoCopyWith<$Res> {
  __$$_LessonInfoCopyWithImpl(
      _$_LessonInfo _value, $Res Function(_$_LessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? scheduleId = freezed,
    Object? orderDesc = freezed,
    Object? key = freezed,
    Object? lessonKey = freezed,
    Object? name = freezed,
    Object? subtitle = freezed,
    Object? isUnLock = freezed,
    Object? isFinish = freezed,
    Object? coverImageUrl = freezed,
    Object? stepList = freezed,
    Object? hideStepList = freezed,
    Object? steps = freezed,
    Object? version = freezed,
    Object? segmentKey = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$_LessonInfo(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleId: freezed == scheduleId
          ? _value.scheduleId
          : scheduleId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderDesc: freezed == orderDesc
          ? _value.orderDesc
          : orderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subtitle: freezed == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String?,
      isUnLock: freezed == isUnLock
          ? _value.isUnLock
          : isUnLock // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      coverImageUrl: freezed == coverImageUrl
          ? _value.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      stepList: freezed == stepList
          ? _value._stepList
          : stepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      hideStepList: freezed == hideStepList
          ? _value._hideStepList
          : hideStepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      steps: freezed == steps
          ? _value._steps
          : steps // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfo implements _LessonInfo {
  const _$_LessonInfo(
      {this.id,
      this.scheduleId,
      this.orderDesc,
      this.key,
      this.lessonKey,
      this.name,
      this.subtitle,
      this.isUnLock,
      this.isFinish,
      this.coverImageUrl,
      final List<StepList>? stepList,
      final List<StepList>? hideStepList,
      final List<StepList>? steps,
      this.version,
      this.segmentKey,
      this.segmentId,
      this.weekId,
      this.startTime,
      this.endTime})
      : _stepList = stepList,
        _hideStepList = hideStepList,
        _steps = steps;

  factory _$_LessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoFromJson(json);

  @override
  final int? id;
  @override
  final int? scheduleId;
  @override
  final String? orderDesc;
  @override
  final String? key;
  @override
  final String? lessonKey;
  @override
  final String? name;
  @override
  final String? subtitle;
  @override
  final bool? isUnLock;
  @override
  final int? isFinish;
  @override
  final String? coverImageUrl;
  final List<StepList>? _stepList;
  @override
  List<StepList>? get stepList {
    final value = _stepList;
    if (value == null) return null;
    if (_stepList is EqualUnmodifiableListView) return _stepList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<StepList>? _hideStepList;
  @override
  List<StepList>? get hideStepList {
    final value = _hideStepList;
    if (value == null) return null;
    if (_hideStepList is EqualUnmodifiableListView) return _hideStepList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<StepList>? _steps;
  @override
  List<StepList>? get steps {
    final value = _steps;
    if (value == null) return null;
    if (_steps is EqualUnmodifiableListView) return _steps;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? version;
  @override
  final String? segmentKey;
  @override
  final int? segmentId;
  @override
  final int? weekId;
  @override
  final int? startTime;
  @override
  final int? endTime;

  @override
  String toString() {
    return 'LessonInfo(id: $id, scheduleId: $scheduleId, orderDesc: $orderDesc, key: $key, lessonKey: $lessonKey, name: $name, subtitle: $subtitle, isUnLock: $isUnLock, isFinish: $isFinish, coverImageUrl: $coverImageUrl, stepList: $stepList, hideStepList: $hideStepList, steps: $steps, version: $version, segmentKey: $segmentKey, segmentId: $segmentId, weekId: $weekId, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.scheduleId, scheduleId) ||
                other.scheduleId == scheduleId) &&
            (identical(other.orderDesc, orderDesc) ||
                other.orderDesc == orderDesc) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.lessonKey, lessonKey) ||
                other.lessonKey == lessonKey) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.subtitle, subtitle) ||
                other.subtitle == subtitle) &&
            (identical(other.isUnLock, isUnLock) ||
                other.isUnLock == isUnLock) &&
            (identical(other.isFinish, isFinish) ||
                other.isFinish == isFinish) &&
            (identical(other.coverImageUrl, coverImageUrl) ||
                other.coverImageUrl == coverImageUrl) &&
            const DeepCollectionEquality().equals(other._stepList, _stepList) &&
            const DeepCollectionEquality()
                .equals(other._hideStepList, _hideStepList) &&
            const DeepCollectionEquality().equals(other._steps, _steps) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.segmentKey, segmentKey) ||
                other.segmentKey == segmentKey) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        scheduleId,
        orderDesc,
        key,
        lessonKey,
        name,
        subtitle,
        isUnLock,
        isFinish,
        coverImageUrl,
        const DeepCollectionEquality().hash(_stepList),
        const DeepCollectionEquality().hash(_hideStepList),
        const DeepCollectionEquality().hash(_steps),
        version,
        segmentKey,
        segmentId,
        weekId,
        startTime,
        endTime
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      __$$_LessonInfoCopyWithImpl<_$_LessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoToJson(
      this,
    );
  }
}

abstract class _LessonInfo implements LessonInfo {
  const factory _LessonInfo(
      {final int? id,
      final int? scheduleId,
      final String? orderDesc,
      final String? key,
      final String? lessonKey,
      final String? name,
      final String? subtitle,
      final bool? isUnLock,
      final int? isFinish,
      final String? coverImageUrl,
      final List<StepList>? stepList,
      final List<StepList>? hideStepList,
      final List<StepList>? steps,
      final String? version,
      final String? segmentKey,
      final int? segmentId,
      final int? weekId,
      final int? startTime,
      final int? endTime}) = _$_LessonInfo;

  factory _LessonInfo.fromJson(Map<String, dynamic> json) =
      _$_LessonInfo.fromJson;

  @override
  int? get id;
  @override
  int? get scheduleId;
  @override
  String? get orderDesc;
  @override
  String? get key;
  @override
  String? get lessonKey;
  @override
  String? get name;
  @override
  String? get subtitle;
  @override
  bool? get isUnLock;
  @override
  int? get isFinish;
  @override
  String? get coverImageUrl;
  @override
  List<StepList>? get stepList;
  @override
  List<StepList>? get hideStepList;
  @override
  List<StepList>? get steps;
  @override
  String? get version;
  @override
  String? get segmentKey;
  @override
  int? get segmentId;
  @override
  int? get weekId;
  @override
  int? get startTime;
  @override
  int? get endTime;
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

StepList _$StepListFromJson(Map<String, dynamic> json) {
  return _StepList.fromJson(json);
}

/// @nodoc
mixin _$StepList {
  int? get stepId => throw _privateConstructorUsedError;
  int get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get needFinish => throw _privateConstructorUsedError;
  int? get needSkip => throw _privateConstructorUsedError;
  int? get showOrder => throw _privateConstructorUsedError;
  int? get isFinish => throw _privateConstructorUsedError;
  bool? get isUnlock => throw _privateConstructorUsedError;
  String? get stepBgUrl => throw _privateConstructorUsedError;
  String? get stepIconUrl => throw _privateConstructorUsedError;
  String? get cocosGameScriptUrl => throw _privateConstructorUsedError;
  String? get jsonRootUrl => throw _privateConstructorUsedError;
  List<String>? get cdnList => throw _privateConstructorUsedError;
  List<StepList>? get subStepList => throw _privateConstructorUsedError;
  int? get score => throw _privateConstructorUsedError;
  int? get totalScore => throw _privateConstructorUsedError;
  int? get showStar => throw _privateConstructorUsedError;
  int? get supportUnity => throw _privateConstructorUsedError;
  String? get unityParam => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StepListCopyWith<StepList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StepListCopyWith<$Res> {
  factory $StepListCopyWith(StepList value, $Res Function(StepList) then) =
      _$StepListCopyWithImpl<$Res, StepList>;
  @useResult
  $Res call(
      {int? stepId,
      int type,
      String? name,
      int? needFinish,
      int? needSkip,
      int? showOrder,
      int? isFinish,
      bool? isUnlock,
      String? stepBgUrl,
      String? stepIconUrl,
      String? cocosGameScriptUrl,
      String? jsonRootUrl,
      List<String>? cdnList,
      List<StepList>? subStepList,
      int? score,
      int? totalScore,
      int? showStar,
      int? supportUnity,
      String? unityParam});
}

/// @nodoc
class _$StepListCopyWithImpl<$Res, $Val extends StepList>
    implements $StepListCopyWith<$Res> {
  _$StepListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stepId = freezed,
    Object? type = null,
    Object? name = freezed,
    Object? needFinish = freezed,
    Object? needSkip = freezed,
    Object? showOrder = freezed,
    Object? isFinish = freezed,
    Object? isUnlock = freezed,
    Object? stepBgUrl = freezed,
    Object? stepIconUrl = freezed,
    Object? cocosGameScriptUrl = freezed,
    Object? jsonRootUrl = freezed,
    Object? cdnList = freezed,
    Object? subStepList = freezed,
    Object? score = freezed,
    Object? totalScore = freezed,
    Object? showStar = freezed,
    Object? supportUnity = freezed,
    Object? unityParam = freezed,
  }) {
    return _then(_value.copyWith(
      stepId: freezed == stepId
          ? _value.stepId
          : stepId // ignore: cast_nullable_to_non_nullable
              as int?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      needFinish: freezed == needFinish
          ? _value.needFinish
          : needFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      needSkip: freezed == needSkip
          ? _value.needSkip
          : needSkip // ignore: cast_nullable_to_non_nullable
              as int?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnlock: freezed == isUnlock
          ? _value.isUnlock
          : isUnlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      stepBgUrl: freezed == stepBgUrl
          ? _value.stepBgUrl
          : stepBgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      stepIconUrl: freezed == stepIconUrl
          ? _value.stepIconUrl
          : stepIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cocosGameScriptUrl: freezed == cocosGameScriptUrl
          ? _value.cocosGameScriptUrl
          : cocosGameScriptUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      jsonRootUrl: freezed == jsonRootUrl
          ? _value.jsonRootUrl
          : jsonRootUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cdnList: freezed == cdnList
          ? _value.cdnList
          : cdnList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subStepList: freezed == subStepList
          ? _value.subStepList
          : subStepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      showStar: freezed == showStar
          ? _value.showStar
          : showStar // ignore: cast_nullable_to_non_nullable
              as int?,
      supportUnity: freezed == supportUnity
          ? _value.supportUnity
          : supportUnity // ignore: cast_nullable_to_non_nullable
              as int?,
      unityParam: freezed == unityParam
          ? _value.unityParam
          : unityParam // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StepListCopyWith<$Res> implements $StepListCopyWith<$Res> {
  factory _$$_StepListCopyWith(
          _$_StepList value, $Res Function(_$_StepList) then) =
      __$$_StepListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? stepId,
      int type,
      String? name,
      int? needFinish,
      int? needSkip,
      int? showOrder,
      int? isFinish,
      bool? isUnlock,
      String? stepBgUrl,
      String? stepIconUrl,
      String? cocosGameScriptUrl,
      String? jsonRootUrl,
      List<String>? cdnList,
      List<StepList>? subStepList,
      int? score,
      int? totalScore,
      int? showStar,
      int? supportUnity,
      String? unityParam});
}

/// @nodoc
class __$$_StepListCopyWithImpl<$Res>
    extends _$StepListCopyWithImpl<$Res, _$_StepList>
    implements _$$_StepListCopyWith<$Res> {
  __$$_StepListCopyWithImpl(
      _$_StepList _value, $Res Function(_$_StepList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stepId = freezed,
    Object? type = null,
    Object? name = freezed,
    Object? needFinish = freezed,
    Object? needSkip = freezed,
    Object? showOrder = freezed,
    Object? isFinish = freezed,
    Object? isUnlock = freezed,
    Object? stepBgUrl = freezed,
    Object? stepIconUrl = freezed,
    Object? cocosGameScriptUrl = freezed,
    Object? jsonRootUrl = freezed,
    Object? cdnList = freezed,
    Object? subStepList = freezed,
    Object? score = freezed,
    Object? totalScore = freezed,
    Object? showStar = freezed,
    Object? supportUnity = freezed,
    Object? unityParam = freezed,
  }) {
    return _then(_$_StepList(
      stepId: freezed == stepId
          ? _value.stepId
          : stepId // ignore: cast_nullable_to_non_nullable
              as int?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      needFinish: freezed == needFinish
          ? _value.needFinish
          : needFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      needSkip: freezed == needSkip
          ? _value.needSkip
          : needSkip // ignore: cast_nullable_to_non_nullable
              as int?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnlock: freezed == isUnlock
          ? _value.isUnlock
          : isUnlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      stepBgUrl: freezed == stepBgUrl
          ? _value.stepBgUrl
          : stepBgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      stepIconUrl: freezed == stepIconUrl
          ? _value.stepIconUrl
          : stepIconUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cocosGameScriptUrl: freezed == cocosGameScriptUrl
          ? _value.cocosGameScriptUrl
          : cocosGameScriptUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      jsonRootUrl: freezed == jsonRootUrl
          ? _value.jsonRootUrl
          : jsonRootUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cdnList: freezed == cdnList
          ? _value._cdnList
          : cdnList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subStepList: freezed == subStepList
          ? _value._subStepList
          : subStepList // ignore: cast_nullable_to_non_nullable
              as List<StepList>?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as int?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      showStar: freezed == showStar
          ? _value.showStar
          : showStar // ignore: cast_nullable_to_non_nullable
              as int?,
      supportUnity: freezed == supportUnity
          ? _value.supportUnity
          : supportUnity // ignore: cast_nullable_to_non_nullable
              as int?,
      unityParam: freezed == unityParam
          ? _value.unityParam
          : unityParam // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StepList implements _StepList {
  const _$_StepList(
      {this.stepId,
      required this.type,
      this.name,
      this.needFinish,
      this.needSkip,
      this.showOrder,
      this.isFinish,
      this.isUnlock,
      this.stepBgUrl,
      this.stepIconUrl,
      this.cocosGameScriptUrl,
      this.jsonRootUrl,
      final List<String>? cdnList,
      final List<StepList>? subStepList,
      this.score,
      this.totalScore,
      this.showStar,
      this.supportUnity,
      this.unityParam})
      : _cdnList = cdnList,
        _subStepList = subStepList;

  factory _$_StepList.fromJson(Map<String, dynamic> json) =>
      _$$_StepListFromJson(json);

  @override
  final int? stepId;
  @override
  final int type;
  @override
  final String? name;
  @override
  final int? needFinish;
  @override
  final int? needSkip;
  @override
  final int? showOrder;
  @override
  final int? isFinish;
  @override
  final bool? isUnlock;
  @override
  final String? stepBgUrl;
  @override
  final String? stepIconUrl;
  @override
  final String? cocosGameScriptUrl;
  @override
  final String? jsonRootUrl;
  final List<String>? _cdnList;
  @override
  List<String>? get cdnList {
    final value = _cdnList;
    if (value == null) return null;
    if (_cdnList is EqualUnmodifiableListView) return _cdnList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<StepList>? _subStepList;
  @override
  List<StepList>? get subStepList {
    final value = _subStepList;
    if (value == null) return null;
    if (_subStepList is EqualUnmodifiableListView) return _subStepList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? score;
  @override
  final int? totalScore;
  @override
  final int? showStar;
  @override
  final int? supportUnity;
  @override
  final String? unityParam;

  @override
  String toString() {
    return 'StepList(stepId: $stepId, type: $type, name: $name, needFinish: $needFinish, needSkip: $needSkip, showOrder: $showOrder, isFinish: $isFinish, isUnlock: $isUnlock, stepBgUrl: $stepBgUrl, stepIconUrl: $stepIconUrl, cocosGameScriptUrl: $cocosGameScriptUrl, jsonRootUrl: $jsonRootUrl, cdnList: $cdnList, subStepList: $subStepList, score: $score, totalScore: $totalScore, showStar: $showStar, supportUnity: $supportUnity, unityParam: $unityParam)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StepList &&
            (identical(other.stepId, stepId) || other.stepId == stepId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.needFinish, needFinish) ||
                other.needFinish == needFinish) &&
            (identical(other.needSkip, needSkip) ||
                other.needSkip == needSkip) &&
            (identical(other.showOrder, showOrder) ||
                other.showOrder == showOrder) &&
            (identical(other.isFinish, isFinish) ||
                other.isFinish == isFinish) &&
            (identical(other.isUnlock, isUnlock) ||
                other.isUnlock == isUnlock) &&
            (identical(other.stepBgUrl, stepBgUrl) ||
                other.stepBgUrl == stepBgUrl) &&
            (identical(other.stepIconUrl, stepIconUrl) ||
                other.stepIconUrl == stepIconUrl) &&
            (identical(other.cocosGameScriptUrl, cocosGameScriptUrl) ||
                other.cocosGameScriptUrl == cocosGameScriptUrl) &&
            (identical(other.jsonRootUrl, jsonRootUrl) ||
                other.jsonRootUrl == jsonRootUrl) &&
            const DeepCollectionEquality().equals(other._cdnList, _cdnList) &&
            const DeepCollectionEquality()
                .equals(other._subStepList, _subStepList) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.totalScore, totalScore) ||
                other.totalScore == totalScore) &&
            (identical(other.showStar, showStar) ||
                other.showStar == showStar) &&
            (identical(other.supportUnity, supportUnity) ||
                other.supportUnity == supportUnity) &&
            (identical(other.unityParam, unityParam) ||
                other.unityParam == unityParam));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        stepId,
        type,
        name,
        needFinish,
        needSkip,
        showOrder,
        isFinish,
        isUnlock,
        stepBgUrl,
        stepIconUrl,
        cocosGameScriptUrl,
        jsonRootUrl,
        const DeepCollectionEquality().hash(_cdnList),
        const DeepCollectionEquality().hash(_subStepList),
        score,
        totalScore,
        showStar,
        supportUnity,
        unityParam
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StepListCopyWith<_$_StepList> get copyWith =>
      __$$_StepListCopyWithImpl<_$_StepList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StepListToJson(
      this,
    );
  }
}

abstract class _StepList implements StepList {
  const factory _StepList(
      {final int? stepId,
      required final int type,
      final String? name,
      final int? needFinish,
      final int? needSkip,
      final int? showOrder,
      final int? isFinish,
      final bool? isUnlock,
      final String? stepBgUrl,
      final String? stepIconUrl,
      final String? cocosGameScriptUrl,
      final String? jsonRootUrl,
      final List<String>? cdnList,
      final List<StepList>? subStepList,
      final int? score,
      final int? totalScore,
      final int? showStar,
      final int? supportUnity,
      final String? unityParam}) = _$_StepList;

  factory _StepList.fromJson(Map<String, dynamic> json) = _$_StepList.fromJson;

  @override
  int? get stepId;
  @override
  int get type;
  @override
  String? get name;
  @override
  int? get needFinish;
  @override
  int? get needSkip;
  @override
  int? get showOrder;
  @override
  int? get isFinish;
  @override
  bool? get isUnlock;
  @override
  String? get stepBgUrl;
  @override
  String? get stepIconUrl;
  @override
  String? get cocosGameScriptUrl;
  @override
  String? get jsonRootUrl;
  @override
  List<String>? get cdnList;
  @override
  List<StepList>? get subStepList;
  @override
  int? get score;
  @override
  int? get totalScore;
  @override
  int? get showStar;
  @override
  int? get supportUnity;
  @override
  String? get unityParam;
  @override
  @JsonKey(ignore: true)
  _$$_StepListCopyWith<_$_StepList> get copyWith =>
      throw _privateConstructorUsedError;
}

OuterFunc _$OuterFuncFromJson(Map<String, dynamic> json) {
  return _OuterFunc.fromJson(json);
}

/// @nodoc
mixin _$OuterFunc {
  int? get type => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;
  OutFuncExtend? get extend => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OuterFuncCopyWith<OuterFunc> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OuterFuncCopyWith<$Res> {
  factory $OuterFuncCopyWith(OuterFunc value, $Res Function(OuterFunc) then) =
      _$OuterFuncCopyWithImpl<$Res, OuterFunc>;
  @useResult
  $Res call(
      {int? type,
      String? icon,
      String? name,
      String? content,
      String? router,
      OutFuncExtend? extend});

  $OutFuncExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class _$OuterFuncCopyWithImpl<$Res, $Val extends OuterFunc>
    implements $OuterFuncCopyWith<$Res> {
  _$OuterFuncCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? content = freezed,
    Object? router = freezed,
    Object? extend = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as OutFuncExtend?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OutFuncExtendCopyWith<$Res>? get extend {
    if (_value.extend == null) {
      return null;
    }

    return $OutFuncExtendCopyWith<$Res>(_value.extend!, (value) {
      return _then(_value.copyWith(extend: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_OuterFuncCopyWith<$Res> implements $OuterFuncCopyWith<$Res> {
  factory _$$_OuterFuncCopyWith(
          _$_OuterFunc value, $Res Function(_$_OuterFunc) then) =
      __$$_OuterFuncCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      String? icon,
      String? name,
      String? content,
      String? router,
      OutFuncExtend? extend});

  @override
  $OutFuncExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class __$$_OuterFuncCopyWithImpl<$Res>
    extends _$OuterFuncCopyWithImpl<$Res, _$_OuterFunc>
    implements _$$_OuterFuncCopyWith<$Res> {
  __$$_OuterFuncCopyWithImpl(
      _$_OuterFunc _value, $Res Function(_$_OuterFunc) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? content = freezed,
    Object? router = freezed,
    Object? extend = freezed,
  }) {
    return _then(_$_OuterFunc(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as OutFuncExtend?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_OuterFunc implements _OuterFunc {
  const _$_OuterFunc(
      {this.type,
      this.icon,
      this.name,
      this.content,
      this.router,
      this.extend});

  factory _$_OuterFunc.fromJson(Map<String, dynamic> json) =>
      _$$_OuterFuncFromJson(json);

  @override
  final int? type;
  @override
  final String? icon;
  @override
  final String? name;
  @override
  final String? content;
  @override
  final String? router;
  @override
  final OutFuncExtend? extend;

  @override
  String toString() {
    return 'OuterFunc(type: $type, icon: $icon, name: $name, content: $content, router: $router, extend: $extend)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_OuterFunc &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.extend, extend) || other.extend == extend));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, icon, name, content, router, extend);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OuterFuncCopyWith<_$_OuterFunc> get copyWith =>
      __$$_OuterFuncCopyWithImpl<_$_OuterFunc>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_OuterFuncToJson(
      this,
    );
  }
}

abstract class _OuterFunc implements OuterFunc {
  const factory _OuterFunc(
      {final int? type,
      final String? icon,
      final String? name,
      final String? content,
      final String? router,
      final OutFuncExtend? extend}) = _$_OuterFunc;

  factory _OuterFunc.fromJson(Map<String, dynamic> json) =
      _$_OuterFunc.fromJson;

  @override
  int? get type;
  @override
  String? get icon;
  @override
  String? get name;
  @override
  String? get content;
  @override
  String? get router;
  @override
  OutFuncExtend? get extend;
  @override
  @JsonKey(ignore: true)
  _$$_OuterFuncCopyWith<_$_OuterFunc> get copyWith =>
      throw _privateConstructorUsedError;
}

OutFuncExtend _$OutFuncExtendFromJson(Map<String, dynamic> json) {
  return _OutFuncExtend.fromJson(json);
}

/// @nodoc
mixin _$OutFuncExtend {
  String get imageUrl => throw _privateConstructorUsedError;
  String get audioUrl => throw _privateConstructorUsedError;
  List<String> get text => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutFuncExtendCopyWith<OutFuncExtend> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutFuncExtendCopyWith<$Res> {
  factory $OutFuncExtendCopyWith(
          OutFuncExtend value, $Res Function(OutFuncExtend) then) =
      _$OutFuncExtendCopyWithImpl<$Res, OutFuncExtend>;
  @useResult
  $Res call({String imageUrl, String audioUrl, List<String> text});
}

/// @nodoc
class _$OutFuncExtendCopyWithImpl<$Res, $Val extends OutFuncExtend>
    implements $OutFuncExtendCopyWith<$Res> {
  _$OutFuncExtendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
    Object? audioUrl = null,
    Object? text = null,
  }) {
    return _then(_value.copyWith(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_OutFuncExtendCopyWith<$Res>
    implements $OutFuncExtendCopyWith<$Res> {
  factory _$$_OutFuncExtendCopyWith(
          _$_OutFuncExtend value, $Res Function(_$_OutFuncExtend) then) =
      __$$_OutFuncExtendCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String imageUrl, String audioUrl, List<String> text});
}

/// @nodoc
class __$$_OutFuncExtendCopyWithImpl<$Res>
    extends _$OutFuncExtendCopyWithImpl<$Res, _$_OutFuncExtend>
    implements _$$_OutFuncExtendCopyWith<$Res> {
  __$$_OutFuncExtendCopyWithImpl(
      _$_OutFuncExtend _value, $Res Function(_$_OutFuncExtend) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
    Object? audioUrl = null,
    Object? text = null,
  }) {
    return _then(_$_OutFuncExtend(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      text: null == text
          ? _value._text
          : text // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_OutFuncExtend implements _OutFuncExtend {
  const _$_OutFuncExtend(
      {this.imageUrl = "",
      this.audioUrl = "",
      final List<String> text = const []})
      : _text = text;

  factory _$_OutFuncExtend.fromJson(Map<String, dynamic> json) =>
      _$$_OutFuncExtendFromJson(json);

  @override
  @JsonKey()
  final String imageUrl;
  @override
  @JsonKey()
  final String audioUrl;
  final List<String> _text;
  @override
  @JsonKey()
  List<String> get text {
    if (_text is EqualUnmodifiableListView) return _text;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_text);
  }

  @override
  String toString() {
    return 'OutFuncExtend(imageUrl: $imageUrl, audioUrl: $audioUrl, text: $text)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_OutFuncExtend &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            const DeepCollectionEquality().equals(other._text, _text));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, imageUrl, audioUrl,
      const DeepCollectionEquality().hash(_text));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OutFuncExtendCopyWith<_$_OutFuncExtend> get copyWith =>
      __$$_OutFuncExtendCopyWithImpl<_$_OutFuncExtend>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_OutFuncExtendToJson(
      this,
    );
  }
}

abstract class _OutFuncExtend implements OutFuncExtend {
  const factory _OutFuncExtend(
      {final String imageUrl,
      final String audioUrl,
      final List<String> text}) = _$_OutFuncExtend;

  factory _OutFuncExtend.fromJson(Map<String, dynamic> json) =
      _$_OutFuncExtend.fromJson;

  @override
  String get imageUrl;
  @override
  String get audioUrl;
  @override
  List<String> get text;
  @override
  @JsonKey(ignore: true)
  _$$_OutFuncExtendCopyWith<_$_OutFuncExtend> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonRefFunc _$LessonRefFuncFromJson(Map<String, dynamic> json) {
  return _LessonRefFunc.fromJson(json);
}

/// @nodoc
mixin _$LessonRefFunc {
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  List<String>? get contentList => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  List<ButtonInfo>? get btnList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonRefFuncCopyWith<LessonRefFunc> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonRefFuncCopyWith<$Res> {
  factory $LessonRefFuncCopyWith(
          LessonRefFunc value, $Res Function(LessonRefFunc) then) =
      _$LessonRefFuncCopyWithImpl<$Res, LessonRefFunc>;
  @useResult
  $Res call(
      {String? icon,
      String? name,
      String? content,
      List<String>? contentList,
      String? router,
      int? type,
      List<ButtonInfo>? btnList});
}

/// @nodoc
class _$LessonRefFuncCopyWithImpl<$Res, $Val extends LessonRefFunc>
    implements $LessonRefFuncCopyWith<$Res> {
  _$LessonRefFuncCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? content = freezed,
    Object? contentList = freezed,
    Object? router = freezed,
    Object? type = freezed,
    Object? btnList = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentList: freezed == contentList
          ? _value.contentList
          : contentList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      btnList: freezed == btnList
          ? _value.btnList
          : btnList // ignore: cast_nullable_to_non_nullable
              as List<ButtonInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonRefFuncCopyWith<$Res>
    implements $LessonRefFuncCopyWith<$Res> {
  factory _$$_LessonRefFuncCopyWith(
          _$_LessonRefFunc value, $Res Function(_$_LessonRefFunc) then) =
      __$$_LessonRefFuncCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? icon,
      String? name,
      String? content,
      List<String>? contentList,
      String? router,
      int? type,
      List<ButtonInfo>? btnList});
}

/// @nodoc
class __$$_LessonRefFuncCopyWithImpl<$Res>
    extends _$LessonRefFuncCopyWithImpl<$Res, _$_LessonRefFunc>
    implements _$$_LessonRefFuncCopyWith<$Res> {
  __$$_LessonRefFuncCopyWithImpl(
      _$_LessonRefFunc _value, $Res Function(_$_LessonRefFunc) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? content = freezed,
    Object? contentList = freezed,
    Object? router = freezed,
    Object? type = freezed,
    Object? btnList = freezed,
  }) {
    return _then(_$_LessonRefFunc(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      contentList: freezed == contentList
          ? _value._contentList
          : contentList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      btnList: freezed == btnList
          ? _value._btnList
          : btnList // ignore: cast_nullable_to_non_nullable
              as List<ButtonInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonRefFunc implements _LessonRefFunc {
  const _$_LessonRefFunc(
      {this.icon,
      this.name,
      this.content,
      final List<String>? contentList,
      this.router,
      this.type,
      final List<ButtonInfo>? btnList})
      : _contentList = contentList,
        _btnList = btnList;

  factory _$_LessonRefFunc.fromJson(Map<String, dynamic> json) =>
      _$$_LessonRefFuncFromJson(json);

  @override
  final String? icon;
  @override
  final String? name;
  @override
  final String? content;
  final List<String>? _contentList;
  @override
  List<String>? get contentList {
    final value = _contentList;
    if (value == null) return null;
    if (_contentList is EqualUnmodifiableListView) return _contentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? router;
  @override
  final int? type;
  final List<ButtonInfo>? _btnList;
  @override
  List<ButtonInfo>? get btnList {
    final value = _btnList;
    if (value == null) return null;
    if (_btnList is EqualUnmodifiableListView) return _btnList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LessonRefFunc(icon: $icon, name: $name, content: $content, contentList: $contentList, router: $router, type: $type, btnList: $btnList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonRefFunc &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality()
                .equals(other._contentList, _contentList) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._btnList, _btnList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      icon,
      name,
      content,
      const DeepCollectionEquality().hash(_contentList),
      router,
      type,
      const DeepCollectionEquality().hash(_btnList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonRefFuncCopyWith<_$_LessonRefFunc> get copyWith =>
      __$$_LessonRefFuncCopyWithImpl<_$_LessonRefFunc>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonRefFuncToJson(
      this,
    );
  }
}

abstract class _LessonRefFunc implements LessonRefFunc {
  const factory _LessonRefFunc(
      {final String? icon,
      final String? name,
      final String? content,
      final List<String>? contentList,
      final String? router,
      final int? type,
      final List<ButtonInfo>? btnList}) = _$_LessonRefFunc;

  factory _LessonRefFunc.fromJson(Map<String, dynamic> json) =
      _$_LessonRefFunc.fromJson;

  @override
  String? get icon;
  @override
  String? get name;
  @override
  String? get content;
  @override
  List<String>? get contentList;
  @override
  String? get router;
  @override
  int? get type;
  @override
  List<ButtonInfo>? get btnList;
  @override
  @JsonKey(ignore: true)
  _$$_LessonRefFuncCopyWith<_$_LessonRefFunc> get copyWith =>
      throw _privateConstructorUsedError;
}

ButtonInfo _$ButtonInfoFromJson(Map<String, dynamic> json) {
  return _ButtonInfo.fromJson(json);
}

/// @nodoc
mixin _$ButtonInfo {
  String? get name => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  ButtonExtra? get extra => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ButtonInfoCopyWith<ButtonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ButtonInfoCopyWith<$Res> {
  factory $ButtonInfoCopyWith(
          ButtonInfo value, $Res Function(ButtonInfo) then) =
      _$ButtonInfoCopyWithImpl<$Res, ButtonInfo>;
  @useResult
  $Res call({String? name, String? router, String? type, ButtonExtra? extra});

  $ButtonExtraCopyWith<$Res>? get extra;
}

/// @nodoc
class _$ButtonInfoCopyWithImpl<$Res, $Val extends ButtonInfo>
    implements $ButtonInfoCopyWith<$Res> {
  _$ButtonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? router = freezed,
    Object? type = freezed,
    Object? extra = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as ButtonExtra?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ButtonExtraCopyWith<$Res>? get extra {
    if (_value.extra == null) {
      return null;
    }

    return $ButtonExtraCopyWith<$Res>(_value.extra!, (value) {
      return _then(_value.copyWith(extra: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ButtonInfoCopyWith<$Res>
    implements $ButtonInfoCopyWith<$Res> {
  factory _$$_ButtonInfoCopyWith(
          _$_ButtonInfo value, $Res Function(_$_ButtonInfo) then) =
      __$$_ButtonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? router, String? type, ButtonExtra? extra});

  @override
  $ButtonExtraCopyWith<$Res>? get extra;
}

/// @nodoc
class __$$_ButtonInfoCopyWithImpl<$Res>
    extends _$ButtonInfoCopyWithImpl<$Res, _$_ButtonInfo>
    implements _$$_ButtonInfoCopyWith<$Res> {
  __$$_ButtonInfoCopyWithImpl(
      _$_ButtonInfo _value, $Res Function(_$_ButtonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? router = freezed,
    Object? type = freezed,
    Object? extra = freezed,
  }) {
    return _then(_$_ButtonInfo(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as ButtonExtra?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ButtonInfo implements _ButtonInfo {
  const _$_ButtonInfo({this.name, this.router, this.type, this.extra});

  factory _$_ButtonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ButtonInfoFromJson(json);

  @override
  final String? name;
  @override
  final String? router;
  @override
  final String? type;
  @override
  final ButtonExtra? extra;

  @override
  String toString() {
    return 'ButtonInfo(name: $name, router: $router, type: $type, extra: $extra)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ButtonInfo &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.extra, extra) || other.extra == extra));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, router, type, extra);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ButtonInfoCopyWith<_$_ButtonInfo> get copyWith =>
      __$$_ButtonInfoCopyWithImpl<_$_ButtonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ButtonInfoToJson(
      this,
    );
  }
}

abstract class _ButtonInfo implements ButtonInfo {
  const factory _ButtonInfo(
      {final String? name,
      final String? router,
      final String? type,
      final ButtonExtra? extra}) = _$_ButtonInfo;

  factory _ButtonInfo.fromJson(Map<String, dynamic> json) =
      _$_ButtonInfo.fromJson;

  @override
  String? get name;
  @override
  String? get router;
  @override
  String? get type;
  @override
  ButtonExtra? get extra;
  @override
  @JsonKey(ignore: true)
  _$$_ButtonInfoCopyWith<_$_ButtonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ButtonExtra _$ButtonExtraFromJson(Map<String, dynamic> json) {
  return _ButtonExtra.fromJson(json);
}

/// @nodoc
mixin _$ButtonExtra {
  String? get image => throw _privateConstructorUsedError;
  String? get audio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ButtonExtraCopyWith<ButtonExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ButtonExtraCopyWith<$Res> {
  factory $ButtonExtraCopyWith(
          ButtonExtra value, $Res Function(ButtonExtra) then) =
      _$ButtonExtraCopyWithImpl<$Res, ButtonExtra>;
  @useResult
  $Res call({String? image, String? audio});
}

/// @nodoc
class _$ButtonExtraCopyWithImpl<$Res, $Val extends ButtonExtra>
    implements $ButtonExtraCopyWith<$Res> {
  _$ButtonExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? audio = freezed,
  }) {
    return _then(_value.copyWith(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ButtonExtraCopyWith<$Res>
    implements $ButtonExtraCopyWith<$Res> {
  factory _$$_ButtonExtraCopyWith(
          _$_ButtonExtra value, $Res Function(_$_ButtonExtra) then) =
      __$$_ButtonExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? image, String? audio});
}

/// @nodoc
class __$$_ButtonExtraCopyWithImpl<$Res>
    extends _$ButtonExtraCopyWithImpl<$Res, _$_ButtonExtra>
    implements _$$_ButtonExtraCopyWith<$Res> {
  __$$_ButtonExtraCopyWithImpl(
      _$_ButtonExtra _value, $Res Function(_$_ButtonExtra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? audio = freezed,
  }) {
    return _then(_$_ButtonExtra(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ButtonExtra implements _ButtonExtra {
  const _$_ButtonExtra({this.image, this.audio});

  factory _$_ButtonExtra.fromJson(Map<String, dynamic> json) =>
      _$$_ButtonExtraFromJson(json);

  @override
  final String? image;
  @override
  final String? audio;

  @override
  String toString() {
    return 'ButtonExtra(image: $image, audio: $audio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ButtonExtra &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.audio, audio) || other.audio == audio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, image, audio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ButtonExtraCopyWith<_$_ButtonExtra> get copyWith =>
      __$$_ButtonExtraCopyWithImpl<_$_ButtonExtra>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ButtonExtraToJson(
      this,
    );
  }
}

abstract class _ButtonExtra implements ButtonExtra {
  const factory _ButtonExtra({final String? image, final String? audio}) =
      _$_ButtonExtra;

  factory _ButtonExtra.fromJson(Map<String, dynamic> json) =
      _$_ButtonExtra.fromJson;

  @override
  String? get image;
  @override
  String? get audio;
  @override
  @JsonKey(ignore: true)
  _$$_ButtonExtraCopyWith<_$_ButtonExtra> get copyWith =>
      throw _privateConstructorUsedError;
}
