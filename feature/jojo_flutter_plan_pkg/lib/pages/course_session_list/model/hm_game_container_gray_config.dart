import 'package:jojo_flutter_base/base.dart';

class HmGameContainerGrayConfig implements JsonConverter<HmGameContainerGrayConfig, Map<String, dynamic>> {
  bool? enabled;

  HmGameContainerGrayConfig({this.enabled});

  @override
  HmGameContainerGrayConfig from<PERSON>son(Map<String, dynamic> json) {
    var enabled = json['enabled'];
    return HmGameContainerGrayConfig(enabled: enabled is bool ? enabled : false);
  }

  @override
  Map<String, dynamic> to<PERSON>son(HmGameContainerGrayConfig object) {
    return {'enabled': object.enabled is bool ? object.enabled : false};
  }
}