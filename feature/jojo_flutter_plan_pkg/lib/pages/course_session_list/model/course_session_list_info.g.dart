// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_session_list_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseSessionListInfo _$$_CourseSessionListInfoFromJson(
        Map<String, dynamic> json) =>
    _$_CourseSessionListInfo(
      courseInfo: json['courseInfo'] == null
          ? null
          : CourseInfo.fromJson(json['courseInfo'] as Map<String, dynamic>),
      classInfo: json['classInfo'] == null
          ? null
          : ClassInfo.fromJson(json['classInfo'] as Map<String, dynamic>),
      extraData: json['extraData'] == null
          ? null
          : ExtraData.fromJson(json['extraData'] as Map<String, dynamic>),
      lessonInfo: json['lessonInfo'] == null
          ? null
          : LessonInfo.fromJson(json['lessonInfo'] as Map<String, dynamic>),
      buttonList: (json['buttonList'] as List<dynamic>?)
          ?.map((e) => ButtonList.fromJson(e as Map<String, dynamic>))
          .toList(),
      outerFunc: (json['outerFunc'] as List<dynamic>?)
          ?.map((e) => OuterFunc.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonRefFunc: (json['lessonRefFunc'] as List<dynamic>?)
          ?.map((e) => LessonRefFunc.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseSessionListInfoToJson(
        _$_CourseSessionListInfo instance) =>
    <String, dynamic>{
      'courseInfo': instance.courseInfo,
      'classInfo': instance.classInfo,
      'extraData': instance.extraData,
      'lessonInfo': instance.lessonInfo,
      'buttonList': instance.buttonList,
      'outerFunc': instance.outerFunc,
      'lessonRefFunc': instance.lessonRefFunc,
    };

_$_ButtonList _$$_ButtonListFromJson(Map<String, dynamic> json) =>
    _$_ButtonList(
      isLock: json['isLock'] as int?,
      buttonName: json['buttonName'] as String?,
      icon: json['icon'] as String?,
      audio: json['audio'] as String?,
      tip: json['tip'] as String?,
      text: json['text'] as String?,
      router: json['router'] as String?,
      isRead: json['isRead'] as int?,
      extend: json['extend'] == null
          ? null
          : ButtonListExtend.fromJson(json['extend'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ButtonListToJson(_$_ButtonList instance) =>
    <String, dynamic>{
      'isLock': instance.isLock,
      'buttonName': instance.buttonName,
      'icon': instance.icon,
      'audio': instance.audio,
      'tip': instance.tip,
      'text': instance.text,
      'router': instance.router,
      'isRead': instance.isRead,
      'extend': instance.extend,
    };

_$_ButtonListExtend _$$_ButtonListExtendFromJson(Map<String, dynamic> json) =>
    _$_ButtonListExtend(
      image: json['image'] as String? ?? null,
    );

Map<String, dynamic> _$$_ButtonListExtendToJson(_$_ButtonListExtend instance) =>
    <String, dynamic>{
      'image': instance.image,
    };

_$_ClassInfo _$$_ClassInfoFromJson(Map<String, dynamic> json) => _$_ClassInfo(
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      className: json['className'] as String?,
      teacherName: json['teacherName'] as String?,
      teacherId: json['teacherId'] as int?,
    );

Map<String, dynamic> _$$_ClassInfoToJson(_$_ClassInfo instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'classKey': instance.classKey,
      'className': instance.className,
      'teacherName': instance.teacherName,
      'teacherId': instance.teacherId,
    };

_$_CourseInfo _$$_CourseInfoFromJson(Map<String, dynamic> json) =>
    _$_CourseInfo(
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      courseSegment: json['courseSegment'] as String?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      language: json['language'] as String?,
      courseType: json['courseType'] as int?,
      courseTypeDesc: json['courseTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_CourseInfoToJson(_$_CourseInfo instance) =>
    <String, dynamic>{
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'courseSegment': instance.courseSegment,
      'courseSegmentCode': instance.courseSegmentCode,
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'language': instance.language,
      'courseType': instance.courseType,
      'courseTypeDesc': instance.courseTypeDesc,
    };

_$_ExtraData _$$_ExtraDataFromJson(Map<String, dynamic> json) => _$_ExtraData(
      putOnRecords: json['putOnRecords'] == null
          ? null
          : PutOnRecords.fromJson(json['putOnRecords'] as Map<String, dynamic>),
      bgInfo: json['bgInfo'] == null
          ? null
          : BgInfo.fromJson(json['bgInfo'] as Map<String, dynamic>),
      stepLockedAudioUrl: json['stepLockedAudioUrl'] as String?,
      isEmotionDiscriminate: json['isEmotionDiscriminate'] as int?,
      engineType: json['engineType'] as int?,
      gameCourseType: json['gameCourseType'] as int?,
      firstGuideAudioUrl: json['firstGuideAudioUrl'] as String?,
    );

Map<String, dynamic> _$$_ExtraDataToJson(_$_ExtraData instance) =>
    <String, dynamic>{
      'putOnRecords': instance.putOnRecords,
      'bgInfo': instance.bgInfo,
      'stepLockedAudioUrl': instance.stepLockedAudioUrl,
      'isEmotionDiscriminate': instance.isEmotionDiscriminate,
      'engineType': instance.engineType,
      'gameCourseType': instance.gameCourseType,
      'firstGuideAudioUrl': instance.firstGuideAudioUrl,
    };

_$_PutOnRecords _$$_PutOnRecordsFromJson(Map<String, dynamic> json) =>
    _$_PutOnRecords(
      name: json['name'] as String?,
      internetNum: json['internetNum'] as String?,
    );

Map<String, dynamic> _$$_PutOnRecordsToJson(_$_PutOnRecords instance) =>
    <String, dynamic>{
      'name': instance.name,
      'internetNum': instance.internetNum,
    };

_$_BgInfo _$$_BgInfoFromJson(Map<String, dynamic> json) => _$_BgInfo(
      bgColor: json['bgColor'] as String?,
    );

Map<String, dynamic> _$$_BgInfoToJson(_$_BgInfo instance) => <String, dynamic>{
      'bgColor': instance.bgColor,
    };

_$_LessonInfo _$$_LessonInfoFromJson(Map<String, dynamic> json) =>
    _$_LessonInfo(
      id: json['id'] as int?,
      scheduleId: json['scheduleId'] as int?,
      orderDesc: json['orderDesc'] as String?,
      key: json['key'] as String?,
      lessonKey: json['lessonKey'] as String?,
      name: json['name'] as String?,
      subtitle: json['subtitle'] as String?,
      isUnLock: json['isUnLock'] as bool?,
      isFinish: json['isFinish'] as int?,
      coverImageUrl: json['coverImageUrl'] as String?,
      stepList: (json['stepList'] as List<dynamic>?)
          ?.map((e) => StepList.fromJson(e as Map<String, dynamic>))
          .toList(),
      hideStepList: (json['hideStepList'] as List<dynamic>?)
          ?.map((e) => StepList.fromJson(e as Map<String, dynamic>))
          .toList(),
      steps: (json['steps'] as List<dynamic>?)
          ?.map((e) => StepList.fromJson(e as Map<String, dynamic>))
          .toList(),
      version: json['version'] as String?,
      segmentKey: json['segmentKey'] as String?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      startTime: json['startTime'] as int?,
      endTime: json['endTime'] as int?,
    );

Map<String, dynamic> _$$_LessonInfoToJson(_$_LessonInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'scheduleId': instance.scheduleId,
      'orderDesc': instance.orderDesc,
      'key': instance.key,
      'lessonKey': instance.lessonKey,
      'name': instance.name,
      'subtitle': instance.subtitle,
      'isUnLock': instance.isUnLock,
      'isFinish': instance.isFinish,
      'coverImageUrl': instance.coverImageUrl,
      'stepList': instance.stepList,
      'hideStepList': instance.hideStepList,
      'steps': instance.steps,
      'version': instance.version,
      'segmentKey': instance.segmentKey,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };

_$_StepList _$$_StepListFromJson(Map<String, dynamic> json) => _$_StepList(
      stepId: json['stepId'] as int?,
      type: json['type'] as int,
      name: json['name'] as String?,
      needFinish: json['needFinish'] as int?,
      needSkip: json['needSkip'] as int?,
      showOrder: json['showOrder'] as int?,
      isFinish: json['isFinish'] as int?,
      isUnlock: json['isUnlock'] as bool?,
      stepBgUrl: json['stepBgUrl'] as String?,
      stepIconUrl: json['stepIconUrl'] as String?,
      cocosGameScriptUrl: json['cocosGameScriptUrl'] as String?,
      jsonRootUrl: json['jsonRootUrl'] as String?,
      cdnList:
          (json['cdnList'] as List<dynamic>?)?.map((e) => e as String).toList(),
      subStepList: (json['subStepList'] as List<dynamic>?)
          ?.map((e) => StepList.fromJson(e as Map<String, dynamic>))
          .toList(),
      score: json['score'] as int?,
      totalScore: json['totalScore'] as int?,
      showStar: json['showStar'] as int?,
      supportUnity: json['supportUnity'] as int?,
      unityParam: json['unityParam'] as String?,
    );

Map<String, dynamic> _$$_StepListToJson(_$_StepList instance) =>
    <String, dynamic>{
      'stepId': instance.stepId,
      'type': instance.type,
      'name': instance.name,
      'needFinish': instance.needFinish,
      'needSkip': instance.needSkip,
      'showOrder': instance.showOrder,
      'isFinish': instance.isFinish,
      'isUnlock': instance.isUnlock,
      'stepBgUrl': instance.stepBgUrl,
      'stepIconUrl': instance.stepIconUrl,
      'cocosGameScriptUrl': instance.cocosGameScriptUrl,
      'jsonRootUrl': instance.jsonRootUrl,
      'cdnList': instance.cdnList,
      'subStepList': instance.subStepList,
      'score': instance.score,
      'totalScore': instance.totalScore,
      'showStar': instance.showStar,
      'supportUnity': instance.supportUnity,
      'unityParam': instance.unityParam,
    };

_$_OuterFunc _$$_OuterFuncFromJson(Map<String, dynamic> json) => _$_OuterFunc(
      type: json['type'] as int?,
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      content: json['content'] as String?,
      router: json['router'] as String?,
      extend: json['extend'] == null
          ? null
          : OutFuncExtend.fromJson(json['extend'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_OuterFuncToJson(_$_OuterFunc instance) =>
    <String, dynamic>{
      'type': instance.type,
      'icon': instance.icon,
      'name': instance.name,
      'content': instance.content,
      'router': instance.router,
      'extend': instance.extend,
    };

_$_OutFuncExtend _$$_OutFuncExtendFromJson(Map<String, dynamic> json) =>
    _$_OutFuncExtend(
      imageUrl: json['imageUrl'] as String? ?? "",
      audioUrl: json['audioUrl'] as String? ?? "",
      text:
          (json['text'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
    );

Map<String, dynamic> _$$_OutFuncExtendToJson(_$_OutFuncExtend instance) =>
    <String, dynamic>{
      'imageUrl': instance.imageUrl,
      'audioUrl': instance.audioUrl,
      'text': instance.text,
    };

_$_LessonRefFunc _$$_LessonRefFuncFromJson(Map<String, dynamic> json) =>
    _$_LessonRefFunc(
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      content: json['content'] as String?,
      contentList: (json['contentList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      router: json['router'] as String?,
      type: json['type'] as int?,
      btnList: (json['btnList'] as List<dynamic>?)
          ?.map((e) => ButtonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LessonRefFuncToJson(_$_LessonRefFunc instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'name': instance.name,
      'content': instance.content,
      'contentList': instance.contentList,
      'router': instance.router,
      'type': instance.type,
      'btnList': instance.btnList,
    };

_$_ButtonInfo _$$_ButtonInfoFromJson(Map<String, dynamic> json) =>
    _$_ButtonInfo(
      name: json['name'] as String?,
      router: json['router'] as String?,
      type: json['type'] as String?,
      extra: json['extra'] == null
          ? null
          : ButtonExtra.fromJson(json['extra'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ButtonInfoToJson(_$_ButtonInfo instance) =>
    <String, dynamic>{
      'name': instance.name,
      'router': instance.router,
      'type': instance.type,
      'extra': instance.extra,
    };

_$_ButtonExtra _$$_ButtonExtraFromJson(Map<String, dynamic> json) =>
    _$_ButtonExtra(
      image: json['image'] as String?,
      audio: json['audio'] as String?,
    );

Map<String, dynamic> _$$_ButtonExtraToJson(_$_ButtonExtra instance) =>
    <String, dynamic>{
      'image': instance.image,
      'audio': instance.audio,
    };
