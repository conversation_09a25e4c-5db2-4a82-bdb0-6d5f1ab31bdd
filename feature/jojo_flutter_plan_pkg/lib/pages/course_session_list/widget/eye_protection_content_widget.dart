import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';

import '../../finish_course_settle_accounts/task_settle/widget/animations/animation_audio_mixin.dart';
import '../mixins/tablet_scale_minx.dart';

class EyeProtectionContentWidget extends StatefulWidget {
  final VoidCallback? onOpen;
  final VoidCallback? onNextTime;
  final VoidCallback? onViewPrinciple;

  const EyeProtectionContentWidget({
    Key? key,
    this.onOpen,
    this.onNextTime,
    this.onViewPrinciple,
  }) : super(key: key);

  @override
  State<EyeProtectionContentWidget> createState() =>
      _EyeProtectionContentWidgetState();
}

class _EyeProtectionContentWidgetState extends State<EyeProtectionContentWidget>
    with TabletScaleMixin, WidgetsBindingObserver, AnimationAudioPlayMixin {
  late final AudioPlayer _audioPlayer;
  bool _wasPlayingBeforePause = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _audioPlayer = AudioPlayer();
    playAudio(
        audioPlayer: _audioPlayer,
        path: AssetsAudio.COURSE_SESSION_EYE_PROTECTION);
  }

  void _pauseAudio() {
    if (_audioPlayer.state == PlayerState.playing) {
      _audioPlayer.pause();
    }
  }

  void _resumeAudio() {
    _audioPlayer.resume();
  }

  void _stopAudio() {
    if (_audioPlayer.state != PlayerState.stopped) {
      _audioPlayer.stop();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopAudio();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _handleAppPaused();
    } else if (state == AppLifecycleState.resumed) {
      _handleAppResumed();
    }
    super.didChangeAppLifecycleState(state);
  }

  void _handleAppPaused() {
    if (_audioPlayer.state == PlayerState.playing) {
      _wasPlayingBeforePause = true;
      _pauseAudio();
    } else {
      _wasPlayingBeforePause = false;
    }
  }

  void _handleAppResumed() {
    if (_wasPlayingBeforePause) {
      _resumeAudio();
    }
  }

  void _executeWithAudioStop(VoidCallback? callback) {
    _stopAudio();
    callback?.call();
  }

  void _handleOpen() => _executeWithAudioStop(widget.onOpen);
  void _handleNextTime() => _executeWithAudioStop(widget.onNextTime);
  void _handleViewPrinciple() => _executeWithAudioStop(widget.onViewPrinciple);

  @override
  Widget build(BuildContext context) {
    final width = (336.rdp).ceilToDouble();
    return Container(
      width: applyTabletScale(context, width),
      constraints: BoxConstraints(
        minWidth: applyTabletScale(context, width),
        maxWidth: applyTabletScale(context, width),
      ),
      padding: EdgeInsets.symmetric(
        vertical: applyTabletScale(context, 32.rdp),
        horizontal: applyTabletScale(context, 28.rdp),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(applyTabletScale(context, 20.rdp)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Text(
              S.of(context).eyeProtectionTitle,
              textAlign: TextAlign.center,
              style: context.textstyles.headingLarge.pf.copyWith(
                color: context.appColors.titleTextColor,
              ),
            ),
          ),
          SizedBox(height: applyTabletScale(context, 20.rdp)),
          Text(
            S.of(context).eyeProtectionDesc,
            textAlign: TextAlign.left,
            style: context.textstyles.bodyText.pf.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          Text(
            S.of(context).eyeProtectionSettingTip,
            textAlign: TextAlign.left,
            style: context.textstyles.bodyText.pf.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          GestureDetector(
            onTap: _handleViewPrinciple,
            child: Text(
              S.of(context).eyeProtectionPrinciple,
              textAlign: TextAlign.left,
              style: context.textstyles.bodyText.pf.copyWith(
                color: context.appColors.mainColor,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
          SizedBox(height: applyTabletScale(context, 24.rdp)),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: Size(0, applyTabletScale(context, 44.rdp)),
                    side: BorderSide(color: context.appColors.mainColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                          applyTabletScale(context, 22.rdp)),
                    ),
                  ),
                  onPressed: _handleNextTime,
                  child: Text(
                    S.of(context).eyeProtectionNextTime,
                    style: context.textstyles.bodyTextLarge.pf.copyWith(
                      color: context.appColors.titleTextColor,
                    ),
                  ),
                ),
              ),
              SizedBox(width: applyTabletScale(context, 16.rdp)),
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.appColors.mainColor,
                    minimumSize: Size(0, applyTabletScale(context, 44.rdp)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                          applyTabletScale(context, 22.rdp)),
                    ),
                    elevation: 0,
                  ),
                  onPressed: _handleOpen,
                  child: Text(
                    S.of(context).eyeProtectionOpen,
                    style: context.textstyles.bodyTextLargeEmphasis.pf.copyWith(
                      color: context.appColors.titleTextColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
