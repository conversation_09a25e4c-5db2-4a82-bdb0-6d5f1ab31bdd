// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CommentData _$$_CommentDataFromJson(Map<String, dynamic> json) =>
    _$_CommentData(
      commentListInfo: json['commentListInfo'] == null
          ? null
          : CommentListInfoType.fromJson(
              json['commentListInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CommentDataToJson(_$_CommentData instance) =>
    <String, dynamic>{
      'commentListInfo': instance.commentListInfo,
    };

_$_CommentListInfoType _$$_CommentListInfoTypeFromJson(
        Map<String, dynamic> json) =>
    _$_CommentListInfoType(
      pageNum: json['pageNum'] as int?,
      pageSize: json['pageSize'] as int?,
      totalRecordNum: json['totalRecordNum'] as int?,
      totalPageNum: json['totalPageNum'] as int?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => CommentItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CommentListInfoTypeToJson(
        _$_CommentListInfoType instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'totalRecordNum': instance.totalRecordNum,
      'totalPageNum': instance.totalPageNum,
      'data': instance.data,
    };

_$_CommentItem _$$_CommentItemFromJson(Map<String, dynamic> json) =>
    _$_CommentItem(
      id: json['id'] as int,
      userId: json['userId'] as int,
      lessonKey: json['lessonKey'] as String,
      classKey: json['classKey'] as String,
      classId: json['classId'] as int,
      subjectKey: json['subjectKey'] as String,
      chapterKey: json['chapterKey'] as String,
      commentTime: json['commentTime'] as int,
      isRead: json['isRead'] as int,
      readCommentTime: json['readCommentTime'] as int,
      lessonOrder: json['lessonOrder'] as String,
      lessonName: json['lessonName'] as String?,
      chapterName: json['chapterName'] as String,
      targetUrl: json['targetUrl'] as String,
      evaluation: json['evaluation'] as int,
      commentType: json['commentType'] as int,
      courseName: json['courseName'] as String,
      reportId: json['reportId'] as int,
      publishTime: json['publishTime'] as int,
    );

Map<String, dynamic> _$$_CommentItemToJson(_$_CommentItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'lessonKey': instance.lessonKey,
      'classKey': instance.classKey,
      'classId': instance.classId,
      'subjectKey': instance.subjectKey,
      'chapterKey': instance.chapterKey,
      'commentTime': instance.commentTime,
      'isRead': instance.isRead,
      'readCommentTime': instance.readCommentTime,
      'lessonOrder': instance.lessonOrder,
      'lessonName': instance.lessonName,
      'chapterName': instance.chapterName,
      'targetUrl': instance.targetUrl,
      'evaluation': instance.evaluation,
      'commentType': instance.commentType,
      'courseName': instance.courseName,
      'reportId': instance.reportId,
      'publishTime': instance.publishTime,
    };
