// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'comment_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CommentData _$CommentDataFromJson(Map<String, dynamic> json) {
  return _CommentData.fromJson(json);
}

/// @nodoc
mixin _$CommentData {
  CommentListInfoType? get commentListInfo =>
      throw _privateConstructorUsedError;
  set commentListInfo(CommentListInfoType? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommentDataCopyWith<CommentData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommentDataCopyWith<$Res> {
  factory $CommentDataCopyWith(
          CommentData value, $Res Function(CommentData) then) =
      _$CommentDataCopyWithImpl<$Res, CommentData>;
  @useResult
  $Res call({CommentListInfoType? commentListInfo});

  $CommentListInfoTypeCopyWith<$Res>? get commentListInfo;
}

/// @nodoc
class _$CommentDataCopyWithImpl<$Res, $Val extends CommentData>
    implements $CommentDataCopyWith<$Res> {
  _$CommentDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commentListInfo = freezed,
  }) {
    return _then(_value.copyWith(
      commentListInfo: freezed == commentListInfo
          ? _value.commentListInfo
          : commentListInfo // ignore: cast_nullable_to_non_nullable
              as CommentListInfoType?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CommentListInfoTypeCopyWith<$Res>? get commentListInfo {
    if (_value.commentListInfo == null) {
      return null;
    }

    return $CommentListInfoTypeCopyWith<$Res>(_value.commentListInfo!, (value) {
      return _then(_value.copyWith(commentListInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CommentDataCopyWith<$Res>
    implements $CommentDataCopyWith<$Res> {
  factory _$$_CommentDataCopyWith(
          _$_CommentData value, $Res Function(_$_CommentData) then) =
      __$$_CommentDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CommentListInfoType? commentListInfo});

  @override
  $CommentListInfoTypeCopyWith<$Res>? get commentListInfo;
}

/// @nodoc
class __$$_CommentDataCopyWithImpl<$Res>
    extends _$CommentDataCopyWithImpl<$Res, _$_CommentData>
    implements _$$_CommentDataCopyWith<$Res> {
  __$$_CommentDataCopyWithImpl(
      _$_CommentData _value, $Res Function(_$_CommentData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commentListInfo = freezed,
  }) {
    return _then(_$_CommentData(
      commentListInfo: freezed == commentListInfo
          ? _value.commentListInfo
          : commentListInfo // ignore: cast_nullable_to_non_nullable
              as CommentListInfoType?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CommentData with DiagnosticableTreeMixin implements _CommentData {
  _$_CommentData({this.commentListInfo});

  factory _$_CommentData.fromJson(Map<String, dynamic> json) =>
      _$$_CommentDataFromJson(json);

  @override
  CommentListInfoType? commentListInfo;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CommentData(commentListInfo: $commentListInfo)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CommentData'))
      ..add(DiagnosticsProperty('commentListInfo', commentListInfo));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CommentDataCopyWith<_$_CommentData> get copyWith =>
      __$$_CommentDataCopyWithImpl<_$_CommentData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CommentDataToJson(
      this,
    );
  }
}

abstract class _CommentData implements CommentData {
  factory _CommentData({CommentListInfoType? commentListInfo}) = _$_CommentData;

  factory _CommentData.fromJson(Map<String, dynamic> json) =
      _$_CommentData.fromJson;

  @override
  CommentListInfoType? get commentListInfo;
  set commentListInfo(CommentListInfoType? value);
  @override
  @JsonKey(ignore: true)
  _$$_CommentDataCopyWith<_$_CommentData> get copyWith =>
      throw _privateConstructorUsedError;
}

CommentListInfoType _$CommentListInfoTypeFromJson(Map<String, dynamic> json) {
  return _CommentListInfoType.fromJson(json);
}

/// @nodoc
mixin _$CommentListInfoType {
  int? get pageNum => throw _privateConstructorUsedError;
  set pageNum(int? value) => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  set pageSize(int? value) => throw _privateConstructorUsedError;
  int? get totalRecordNum => throw _privateConstructorUsedError;
  set totalRecordNum(int? value) => throw _privateConstructorUsedError;
  int? get totalPageNum => throw _privateConstructorUsedError;
  set totalPageNum(int? value) => throw _privateConstructorUsedError;
  List<CommentItem>? get data => throw _privateConstructorUsedError;
  set data(List<CommentItem>? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommentListInfoTypeCopyWith<CommentListInfoType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommentListInfoTypeCopyWith<$Res> {
  factory $CommentListInfoTypeCopyWith(
          CommentListInfoType value, $Res Function(CommentListInfoType) then) =
      _$CommentListInfoTypeCopyWithImpl<$Res, CommentListInfoType>;
  @useResult
  $Res call(
      {int? pageNum,
      int? pageSize,
      int? totalRecordNum,
      int? totalPageNum,
      List<CommentItem>? data});
}

/// @nodoc
class _$CommentListInfoTypeCopyWithImpl<$Res, $Val extends CommentListInfoType>
    implements $CommentListInfoTypeCopyWith<$Res> {
  _$CommentListInfoTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? totalRecordNum = freezed,
    Object? totalPageNum = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalRecordNum: freezed == totalRecordNum
          ? _value.totalRecordNum
          : totalRecordNum // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPageNum: freezed == totalPageNum
          ? _value.totalPageNum
          : totalPageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CommentItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CommentListInfoTypeCopyWith<$Res>
    implements $CommentListInfoTypeCopyWith<$Res> {
  factory _$$_CommentListInfoTypeCopyWith(_$_CommentListInfoType value,
          $Res Function(_$_CommentListInfoType) then) =
      __$$_CommentListInfoTypeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? pageNum,
      int? pageSize,
      int? totalRecordNum,
      int? totalPageNum,
      List<CommentItem>? data});
}

/// @nodoc
class __$$_CommentListInfoTypeCopyWithImpl<$Res>
    extends _$CommentListInfoTypeCopyWithImpl<$Res, _$_CommentListInfoType>
    implements _$$_CommentListInfoTypeCopyWith<$Res> {
  __$$_CommentListInfoTypeCopyWithImpl(_$_CommentListInfoType _value,
      $Res Function(_$_CommentListInfoType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? totalRecordNum = freezed,
    Object? totalPageNum = freezed,
    Object? data = freezed,
  }) {
    return _then(_$_CommentListInfoType(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      totalRecordNum: freezed == totalRecordNum
          ? _value.totalRecordNum
          : totalRecordNum // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPageNum: freezed == totalPageNum
          ? _value.totalPageNum
          : totalPageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CommentItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CommentListInfoType
    with DiagnosticableTreeMixin
    implements _CommentListInfoType {
  _$_CommentListInfoType(
      {this.pageNum,
      this.pageSize,
      this.totalRecordNum,
      this.totalPageNum,
      this.data});

  factory _$_CommentListInfoType.fromJson(Map<String, dynamic> json) =>
      _$$_CommentListInfoTypeFromJson(json);

  @override
  int? pageNum;
  @override
  int? pageSize;
  @override
  int? totalRecordNum;
  @override
  int? totalPageNum;
  @override
  List<CommentItem>? data;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CommentListInfoType(pageNum: $pageNum, pageSize: $pageSize, totalRecordNum: $totalRecordNum, totalPageNum: $totalPageNum, data: $data)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CommentListInfoType'))
      ..add(DiagnosticsProperty('pageNum', pageNum))
      ..add(DiagnosticsProperty('pageSize', pageSize))
      ..add(DiagnosticsProperty('totalRecordNum', totalRecordNum))
      ..add(DiagnosticsProperty('totalPageNum', totalPageNum))
      ..add(DiagnosticsProperty('data', data));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CommentListInfoTypeCopyWith<_$_CommentListInfoType> get copyWith =>
      __$$_CommentListInfoTypeCopyWithImpl<_$_CommentListInfoType>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CommentListInfoTypeToJson(
      this,
    );
  }
}

abstract class _CommentListInfoType implements CommentListInfoType {
  factory _CommentListInfoType(
      {int? pageNum,
      int? pageSize,
      int? totalRecordNum,
      int? totalPageNum,
      List<CommentItem>? data}) = _$_CommentListInfoType;

  factory _CommentListInfoType.fromJson(Map<String, dynamic> json) =
      _$_CommentListInfoType.fromJson;

  @override
  int? get pageNum;
  set pageNum(int? value);
  @override
  int? get pageSize;
  set pageSize(int? value);
  @override
  int? get totalRecordNum;
  set totalRecordNum(int? value);
  @override
  int? get totalPageNum;
  set totalPageNum(int? value);
  @override
  List<CommentItem>? get data;
  set data(List<CommentItem>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CommentListInfoTypeCopyWith<_$_CommentListInfoType> get copyWith =>
      throw _privateConstructorUsedError;
}

CommentItem _$CommentItemFromJson(Map<String, dynamic> json) {
  return _CommentItem.fromJson(json);
}

/// @nodoc
mixin _$CommentItem {
  int get id => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;
  String get lessonKey => throw _privateConstructorUsedError;
  String get classKey => throw _privateConstructorUsedError;
  int get classId => throw _privateConstructorUsedError;
  String get subjectKey => throw _privateConstructorUsedError;
  String get chapterKey => throw _privateConstructorUsedError;
  int get commentTime => throw _privateConstructorUsedError;
  int get isRead => throw _privateConstructorUsedError;
  int get readCommentTime => throw _privateConstructorUsedError;
  String get lessonOrder => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String get chapterName => throw _privateConstructorUsedError;
  String get targetUrl => throw _privateConstructorUsedError;
  int get evaluation => throw _privateConstructorUsedError;
  int get commentType => throw _privateConstructorUsedError;
  String get courseName => throw _privateConstructorUsedError;
  int get reportId => throw _privateConstructorUsedError;
  int get publishTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommentItemCopyWith<CommentItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommentItemCopyWith<$Res> {
  factory $CommentItemCopyWith(
          CommentItem value, $Res Function(CommentItem) then) =
      _$CommentItemCopyWithImpl<$Res, CommentItem>;
  @useResult
  $Res call(
      {int id,
      int userId,
      String lessonKey,
      String classKey,
      int classId,
      String subjectKey,
      String chapterKey,
      int commentTime,
      int isRead,
      int readCommentTime,
      String lessonOrder,
      String? lessonName,
      String chapterName,
      String targetUrl,
      int evaluation,
      int commentType,
      String courseName,
      int reportId,
      int publishTime});
}

/// @nodoc
class _$CommentItemCopyWithImpl<$Res, $Val extends CommentItem>
    implements $CommentItemCopyWith<$Res> {
  _$CommentItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? lessonKey = null,
    Object? classKey = null,
    Object? classId = null,
    Object? subjectKey = null,
    Object? chapterKey = null,
    Object? commentTime = null,
    Object? isRead = null,
    Object? readCommentTime = null,
    Object? lessonOrder = null,
    Object? lessonName = freezed,
    Object? chapterName = null,
    Object? targetUrl = null,
    Object? evaluation = null,
    Object? commentType = null,
    Object? courseName = null,
    Object? reportId = null,
    Object? publishTime = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      lessonKey: null == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String,
      classKey: null == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String,
      classId: null == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int,
      subjectKey: null == subjectKey
          ? _value.subjectKey
          : subjectKey // ignore: cast_nullable_to_non_nullable
              as String,
      chapterKey: null == chapterKey
          ? _value.chapterKey
          : chapterKey // ignore: cast_nullable_to_non_nullable
              as String,
      commentTime: null == commentTime
          ? _value.commentTime
          : commentTime // ignore: cast_nullable_to_non_nullable
              as int,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as int,
      readCommentTime: null == readCommentTime
          ? _value.readCommentTime
          : readCommentTime // ignore: cast_nullable_to_non_nullable
              as int,
      lessonOrder: null == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      chapterName: null == chapterName
          ? _value.chapterName
          : chapterName // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      evaluation: null == evaluation
          ? _value.evaluation
          : evaluation // ignore: cast_nullable_to_non_nullable
              as int,
      commentType: null == commentType
          ? _value.commentType
          : commentType // ignore: cast_nullable_to_non_nullable
              as int,
      courseName: null == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _value.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as int,
      publishTime: null == publishTime
          ? _value.publishTime
          : publishTime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CommentItemCopyWith<$Res>
    implements $CommentItemCopyWith<$Res> {
  factory _$$_CommentItemCopyWith(
          _$_CommentItem value, $Res Function(_$_CommentItem) then) =
      __$$_CommentItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      int userId,
      String lessonKey,
      String classKey,
      int classId,
      String subjectKey,
      String chapterKey,
      int commentTime,
      int isRead,
      int readCommentTime,
      String lessonOrder,
      String? lessonName,
      String chapterName,
      String targetUrl,
      int evaluation,
      int commentType,
      String courseName,
      int reportId,
      int publishTime});
}

/// @nodoc
class __$$_CommentItemCopyWithImpl<$Res>
    extends _$CommentItemCopyWithImpl<$Res, _$_CommentItem>
    implements _$$_CommentItemCopyWith<$Res> {
  __$$_CommentItemCopyWithImpl(
      _$_CommentItem _value, $Res Function(_$_CommentItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? lessonKey = null,
    Object? classKey = null,
    Object? classId = null,
    Object? subjectKey = null,
    Object? chapterKey = null,
    Object? commentTime = null,
    Object? isRead = null,
    Object? readCommentTime = null,
    Object? lessonOrder = null,
    Object? lessonName = freezed,
    Object? chapterName = null,
    Object? targetUrl = null,
    Object? evaluation = null,
    Object? commentType = null,
    Object? courseName = null,
    Object? reportId = null,
    Object? publishTime = null,
  }) {
    return _then(_$_CommentItem(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      lessonKey: null == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String,
      classKey: null == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String,
      classId: null == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int,
      subjectKey: null == subjectKey
          ? _value.subjectKey
          : subjectKey // ignore: cast_nullable_to_non_nullable
              as String,
      chapterKey: null == chapterKey
          ? _value.chapterKey
          : chapterKey // ignore: cast_nullable_to_non_nullable
              as String,
      commentTime: null == commentTime
          ? _value.commentTime
          : commentTime // ignore: cast_nullable_to_non_nullable
              as int,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as int,
      readCommentTime: null == readCommentTime
          ? _value.readCommentTime
          : readCommentTime // ignore: cast_nullable_to_non_nullable
              as int,
      lessonOrder: null == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      chapterName: null == chapterName
          ? _value.chapterName
          : chapterName // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      evaluation: null == evaluation
          ? _value.evaluation
          : evaluation // ignore: cast_nullable_to_non_nullable
              as int,
      commentType: null == commentType
          ? _value.commentType
          : commentType // ignore: cast_nullable_to_non_nullable
              as int,
      courseName: null == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String,
      reportId: null == reportId
          ? _value.reportId
          : reportId // ignore: cast_nullable_to_non_nullable
              as int,
      publishTime: null == publishTime
          ? _value.publishTime
          : publishTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CommentItem with DiagnosticableTreeMixin implements _CommentItem {
  const _$_CommentItem(
      {required this.id,
      required this.userId,
      required this.lessonKey,
      required this.classKey,
      required this.classId,
      required this.subjectKey,
      required this.chapterKey,
      required this.commentTime,
      required this.isRead,
      required this.readCommentTime,
      required this.lessonOrder,
      required this.lessonName,
      required this.chapterName,
      required this.targetUrl,
      required this.evaluation,
      required this.commentType,
      required this.courseName,
      required this.reportId,
      required this.publishTime});

  factory _$_CommentItem.fromJson(Map<String, dynamic> json) =>
      _$$_CommentItemFromJson(json);

  @override
  final int id;
  @override
  final int userId;
  @override
  final String lessonKey;
  @override
  final String classKey;
  @override
  final int classId;
  @override
  final String subjectKey;
  @override
  final String chapterKey;
  @override
  final int commentTime;
  @override
  final int isRead;
  @override
  final int readCommentTime;
  @override
  final String lessonOrder;
  @override
  final String? lessonName;
  @override
  final String chapterName;
  @override
  final String targetUrl;
  @override
  final int evaluation;
  @override
  final int commentType;
  @override
  final String courseName;
  @override
  final int reportId;
  @override
  final int publishTime;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CommentItem(id: $id, userId: $userId, lessonKey: $lessonKey, classKey: $classKey, classId: $classId, subjectKey: $subjectKey, chapterKey: $chapterKey, commentTime: $commentTime, isRead: $isRead, readCommentTime: $readCommentTime, lessonOrder: $lessonOrder, lessonName: $lessonName, chapterName: $chapterName, targetUrl: $targetUrl, evaluation: $evaluation, commentType: $commentType, courseName: $courseName, reportId: $reportId, publishTime: $publishTime)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CommentItem'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('lessonKey', lessonKey))
      ..add(DiagnosticsProperty('classKey', classKey))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('subjectKey', subjectKey))
      ..add(DiagnosticsProperty('chapterKey', chapterKey))
      ..add(DiagnosticsProperty('commentTime', commentTime))
      ..add(DiagnosticsProperty('isRead', isRead))
      ..add(DiagnosticsProperty('readCommentTime', readCommentTime))
      ..add(DiagnosticsProperty('lessonOrder', lessonOrder))
      ..add(DiagnosticsProperty('lessonName', lessonName))
      ..add(DiagnosticsProperty('chapterName', chapterName))
      ..add(DiagnosticsProperty('targetUrl', targetUrl))
      ..add(DiagnosticsProperty('evaluation', evaluation))
      ..add(DiagnosticsProperty('commentType', commentType))
      ..add(DiagnosticsProperty('courseName', courseName))
      ..add(DiagnosticsProperty('reportId', reportId))
      ..add(DiagnosticsProperty('publishTime', publishTime));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CommentItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.lessonKey, lessonKey) ||
                other.lessonKey == lessonKey) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.subjectKey, subjectKey) ||
                other.subjectKey == subjectKey) &&
            (identical(other.chapterKey, chapterKey) ||
                other.chapterKey == chapterKey) &&
            (identical(other.commentTime, commentTime) ||
                other.commentTime == commentTime) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.readCommentTime, readCommentTime) ||
                other.readCommentTime == readCommentTime) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.chapterName, chapterName) ||
                other.chapterName == chapterName) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl) &&
            (identical(other.evaluation, evaluation) ||
                other.evaluation == evaluation) &&
            (identical(other.commentType, commentType) ||
                other.commentType == commentType) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.reportId, reportId) ||
                other.reportId == reportId) &&
            (identical(other.publishTime, publishTime) ||
                other.publishTime == publishTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        lessonKey,
        classKey,
        classId,
        subjectKey,
        chapterKey,
        commentTime,
        isRead,
        readCommentTime,
        lessonOrder,
        lessonName,
        chapterName,
        targetUrl,
        evaluation,
        commentType,
        courseName,
        reportId,
        publishTime
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CommentItemCopyWith<_$_CommentItem> get copyWith =>
      __$$_CommentItemCopyWithImpl<_$_CommentItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CommentItemToJson(
      this,
    );
  }
}

abstract class _CommentItem implements CommentItem {
  const factory _CommentItem(
      {required final int id,
      required final int userId,
      required final String lessonKey,
      required final String classKey,
      required final int classId,
      required final String subjectKey,
      required final String chapterKey,
      required final int commentTime,
      required final int isRead,
      required final int readCommentTime,
      required final String lessonOrder,
      required final String? lessonName,
      required final String chapterName,
      required final String targetUrl,
      required final int evaluation,
      required final int commentType,
      required final String courseName,
      required final int reportId,
      required final int publishTime}) = _$_CommentItem;

  factory _CommentItem.fromJson(Map<String, dynamic> json) =
      _$_CommentItem.fromJson;

  @override
  int get id;
  @override
  int get userId;
  @override
  String get lessonKey;
  @override
  String get classKey;
  @override
  int get classId;
  @override
  String get subjectKey;
  @override
  String get chapterKey;
  @override
  int get commentTime;
  @override
  int get isRead;
  @override
  int get readCommentTime;
  @override
  String get lessonOrder;
  @override
  String? get lessonName;
  @override
  String get chapterName;
  @override
  String get targetUrl;
  @override
  int get evaluation;
  @override
  int get commentType;
  @override
  String get courseName;
  @override
  int get reportId;
  @override
  int get publishTime;
  @override
  @JsonKey(ignore: true)
  _$$_CommentItemCopyWith<_$_CommentItem> get copyWith =>
      throw _privateConstructorUsedError;
}
