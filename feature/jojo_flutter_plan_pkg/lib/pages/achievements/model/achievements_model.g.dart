// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievements_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AchievementsModel _$$_AchievementsModelFromJson(Map<String, dynamic> json) =>
    _$_AchievementsModel(
      onlyTrainingCamp: json['onlyTrainingCamp'] as int?,
      positionImg: json['positionImg'] as String?,
      commonRes: json['commonRes'] as String?,
      title: json['title'] as String?,
      latestMedals: (json['latestMedals'] as List<dynamic>?)
          ?.map((e) => MedalGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      segmentMedals: (json['segmentMedals'] as List<dynamic>?)
          ?.map((e) => SegmentMedal.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AchievementsModelToJson(
        _$_AchievementsModel instance) =>
    <String, dynamic>{
      'onlyTrainingCamp': instance.onlyTrainingCamp,
      'positionImg': instance.positionImg,
      'commonRes': instance.commonRes,
      'title': instance.title,
      'latestMedals': instance.latestMedals,
      'segmentMedals': instance.segmentMedals,
    };

_$_MedalGroup _$$_MedalGroupFromJson(Map<String, dynamic> json) =>
    _$_MedalGroup(
      upgrade: json['upgrade'] as int?,
      groupKey: json['groupKey'] as String?,
      tips: json['tips'] as String?,
      groupMedals: (json['groupMedals'] as List<dynamic>?)
          ?.map((e) => MedalModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MedalGroupToJson(_$_MedalGroup instance) =>
    <String, dynamic>{
      'upgrade': instance.upgrade,
      'groupKey': instance.groupKey,
      'tips': instance.tips,
      'groupMedals': instance.groupMedals,
    };

_$_SegmentMedal _$$_SegmentMedalFromJson(Map<String, dynamic> json) =>
    _$_SegmentMedal(
      segmentTitle: json['segmentTitle'] as String?,
      segmentName: json['segmentName'] as String?,
      segmentCode: json['segmentCode'] as int?,
      category: (json['category'] as List<dynamic>?)
          ?.map((e) => MedalCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SegmentMedalToJson(_$_SegmentMedal instance) =>
    <String, dynamic>{
      'segmentTitle': instance.segmentTitle,
      'segmentName': instance.segmentName,
      'segmentCode': instance.segmentCode,
      'category': instance.category,
    };

_$_MedalCategory _$$_MedalCategoryFromJson(Map<String, dynamic> json) =>
    _$_MedalCategory(
      name: json['name'] as String?,
      medals: (json['medals'] as List<dynamic>?)
          ?.map((e) => MedalGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MedalCategoryToJson(_$_MedalCategory instance) =>
    <String, dynamic>{
      'name': instance.name,
      'medals': instance.medals,
    };
