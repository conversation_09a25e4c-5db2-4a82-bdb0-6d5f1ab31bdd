// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AchievementDetailModel _$$_AchievementDetailModelFromJson(
        Map<String, dynamic> json) =>
    _$_AchievementDetailModel(
      userInfo: json['userInfo'] == null
          ? null
          : UserInfo.fromJson(json['userInfo'] as Map<String, dynamic>),
      shareInfo: json['shareInfo'] == null
          ? null
          : ShareInfo.fromJson(json['shareInfo'] as Map<String, dynamic>),
      commonRes: json['commonRes'] as String?,
      subjectType: json['subjectType'] as int?,
      segmentName: json['segmentName'] as String?,
      onlyTrainingCamp: json['onlyTrainingCamp'] as int?,
      medalGroup: json['medalGroup'] == null
          ? null
          : MedalGroup.fromJson(json['medalGroup'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_AchievementDetailModelToJson(
        _$_AchievementDetailModel instance) =>
    <String, dynamic>{
      'userInfo': instance.userInfo,
      'shareInfo': instance.shareInfo,
      'commonRes': instance.commonRes,
      'subjectType': instance.subjectType,
      'segmentName': instance.segmentName,
      'onlyTrainingCamp': instance.onlyTrainingCamp,
      'medalGroup': instance.medalGroup,
    };

_$_UserInfo _$$_UserInfoFromJson(Map<String, dynamic> json) => _$_UserInfo(
      nickname: json['nickname'] as String?,
      avatar: json['avatar'] as String?,
    );

Map<String, dynamic> _$$_UserInfoToJson(_$_UserInfo instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'avatar': instance.avatar,
    };

_$_ShareInfo _$$_ShareInfoFromJson(Map<String, dynamic> json) => _$_ShareInfo(
      introduceUrl: json['introduceUrl'] as String?,
    );

Map<String, dynamic> _$$_ShareInfoToJson(_$_ShareInfo instance) =>
    <String, dynamic>{
      'introduceUrl': instance.introduceUrl,
    };
