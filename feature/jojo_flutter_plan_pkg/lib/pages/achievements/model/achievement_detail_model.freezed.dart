// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'achievement_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AchievementDetailModel _$AchievementDetailModelFromJson(
    Map<String, dynamic> json) {
  return _AchievementDetailModel.fromJson(json);
}

/// @nodoc
mixin _$AchievementDetailModel {
  /// 用户信息
  UserInfo? get userInfo => throw _privateConstructorUsedError;

  /// 分享信息
  ShareInfo? get shareInfo => throw _privateConstructorUsedError;

  /// 公共资源
  String? get commonRes => throw _privateConstructorUsedError;

  /// 科目类型
  int? get subjectType => throw _privateConstructorUsedError;

  /// 阶段
  String? get segmentName => throw _privateConstructorUsedError;

  /// 是否仅训练营 (1是 0否)
  int? get onlyTrainingCamp => throw _privateConstructorUsedError;

  /// 勋章分组
  MedalGroup? get medalGroup => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AchievementDetailModelCopyWith<AchievementDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AchievementDetailModelCopyWith<$Res> {
  factory $AchievementDetailModelCopyWith(AchievementDetailModel value,
          $Res Function(AchievementDetailModel) then) =
      _$AchievementDetailModelCopyWithImpl<$Res, AchievementDetailModel>;
  @useResult
  $Res call(
      {UserInfo? userInfo,
      ShareInfo? shareInfo,
      String? commonRes,
      int? subjectType,
      String? segmentName,
      int? onlyTrainingCamp,
      MedalGroup? medalGroup});

  $UserInfoCopyWith<$Res>? get userInfo;
  $ShareInfoCopyWith<$Res>? get shareInfo;
  $MedalGroupCopyWith<$Res>? get medalGroup;
}

/// @nodoc
class _$AchievementDetailModelCopyWithImpl<$Res,
        $Val extends AchievementDetailModel>
    implements $AchievementDetailModelCopyWith<$Res> {
  _$AchievementDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userInfo = freezed,
    Object? shareInfo = freezed,
    Object? commonRes = freezed,
    Object? subjectType = freezed,
    Object? segmentName = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? medalGroup = freezed,
  }) {
    return _then(_value.copyWith(
      userInfo: freezed == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      medalGroup: freezed == medalGroup
          ? _value.medalGroup
          : medalGroup // ignore: cast_nullable_to_non_nullable
              as MedalGroup?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<$Res>? get userInfo {
    if (_value.userInfo == null) {
      return null;
    }

    return $UserInfoCopyWith<$Res>(_value.userInfo!, (value) {
      return _then(_value.copyWith(userInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ShareInfoCopyWith<$Res>? get shareInfo {
    if (_value.shareInfo == null) {
      return null;
    }

    return $ShareInfoCopyWith<$Res>(_value.shareInfo!, (value) {
      return _then(_value.copyWith(shareInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MedalGroupCopyWith<$Res>? get medalGroup {
    if (_value.medalGroup == null) {
      return null;
    }

    return $MedalGroupCopyWith<$Res>(_value.medalGroup!, (value) {
      return _then(_value.copyWith(medalGroup: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AchievementDetailModelCopyWith<$Res>
    implements $AchievementDetailModelCopyWith<$Res> {
  factory _$$_AchievementDetailModelCopyWith(_$_AchievementDetailModel value,
          $Res Function(_$_AchievementDetailModel) then) =
      __$$_AchievementDetailModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UserInfo? userInfo,
      ShareInfo? shareInfo,
      String? commonRes,
      int? subjectType,
      String? segmentName,
      int? onlyTrainingCamp,
      MedalGroup? medalGroup});

  @override
  $UserInfoCopyWith<$Res>? get userInfo;
  @override
  $ShareInfoCopyWith<$Res>? get shareInfo;
  @override
  $MedalGroupCopyWith<$Res>? get medalGroup;
}

/// @nodoc
class __$$_AchievementDetailModelCopyWithImpl<$Res>
    extends _$AchievementDetailModelCopyWithImpl<$Res,
        _$_AchievementDetailModel>
    implements _$$_AchievementDetailModelCopyWith<$Res> {
  __$$_AchievementDetailModelCopyWithImpl(_$_AchievementDetailModel _value,
      $Res Function(_$_AchievementDetailModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userInfo = freezed,
    Object? shareInfo = freezed,
    Object? commonRes = freezed,
    Object? subjectType = freezed,
    Object? segmentName = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? medalGroup = freezed,
  }) {
    return _then(_$_AchievementDetailModel(
      userInfo: freezed == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      medalGroup: freezed == medalGroup
          ? _value.medalGroup
          : medalGroup // ignore: cast_nullable_to_non_nullable
              as MedalGroup?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AchievementDetailModel implements _AchievementDetailModel {
  _$_AchievementDetailModel(
      {this.userInfo,
      this.shareInfo,
      this.commonRes,
      this.subjectType,
      this.segmentName,
      this.onlyTrainingCamp,
      this.medalGroup});

  factory _$_AchievementDetailModel.fromJson(Map<String, dynamic> json) =>
      _$$_AchievementDetailModelFromJson(json);

  /// 用户信息
  @override
  final UserInfo? userInfo;

  /// 分享信息
  @override
  final ShareInfo? shareInfo;

  /// 公共资源
  @override
  final String? commonRes;

  /// 科目类型
  @override
  final int? subjectType;

  /// 阶段
  @override
  final String? segmentName;

  /// 是否仅训练营 (1是 0否)
  @override
  final int? onlyTrainingCamp;

  /// 勋章分组
  @override
  final MedalGroup? medalGroup;

  @override
  String toString() {
    return 'AchievementDetailModel(userInfo: $userInfo, shareInfo: $shareInfo, commonRes: $commonRes, subjectType: $subjectType, segmentName: $segmentName, onlyTrainingCamp: $onlyTrainingCamp, medalGroup: $medalGroup)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AchievementDetailModel &&
            (identical(other.userInfo, userInfo) ||
                other.userInfo == userInfo) &&
            (identical(other.shareInfo, shareInfo) ||
                other.shareInfo == shareInfo) &&
            (identical(other.commonRes, commonRes) ||
                other.commonRes == commonRes) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.onlyTrainingCamp, onlyTrainingCamp) ||
                other.onlyTrainingCamp == onlyTrainingCamp) &&
            (identical(other.medalGroup, medalGroup) ||
                other.medalGroup == medalGroup));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userInfo, shareInfo, commonRes,
      subjectType, segmentName, onlyTrainingCamp, medalGroup);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AchievementDetailModelCopyWith<_$_AchievementDetailModel> get copyWith =>
      __$$_AchievementDetailModelCopyWithImpl<_$_AchievementDetailModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AchievementDetailModelToJson(
      this,
    );
  }
}

abstract class _AchievementDetailModel implements AchievementDetailModel {
  factory _AchievementDetailModel(
      {final UserInfo? userInfo,
      final ShareInfo? shareInfo,
      final String? commonRes,
      final int? subjectType,
      final String? segmentName,
      final int? onlyTrainingCamp,
      final MedalGroup? medalGroup}) = _$_AchievementDetailModel;

  factory _AchievementDetailModel.fromJson(Map<String, dynamic> json) =
      _$_AchievementDetailModel.fromJson;

  @override

  /// 用户信息
  UserInfo? get userInfo;
  @override

  /// 分享信息
  ShareInfo? get shareInfo;
  @override

  /// 公共资源
  String? get commonRes;
  @override

  /// 科目类型
  int? get subjectType;
  @override

  /// 阶段
  String? get segmentName;
  @override

  /// 是否仅训练营 (1是 0否)
  int? get onlyTrainingCamp;
  @override

  /// 勋章分组
  MedalGroup? get medalGroup;
  @override
  @JsonKey(ignore: true)
  _$$_AchievementDetailModelCopyWith<_$_AchievementDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) {
  return _UserInfo.fromJson(json);
}

/// @nodoc
mixin _$UserInfo {
  /// 用户昵称
  String? get nickname => throw _privateConstructorUsedError;

  /// 头像
  String? get avatar => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserInfoCopyWith<UserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) then) =
      _$UserInfoCopyWithImpl<$Res, UserInfo>;
  @useResult
  $Res call({String? nickname, String? avatar});
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res, $Val extends UserInfo>
    implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatar = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserInfoCopyWith<$Res> implements $UserInfoCopyWith<$Res> {
  factory _$$_UserInfoCopyWith(
          _$_UserInfo value, $Res Function(_$_UserInfo) then) =
      __$$_UserInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? nickname, String? avatar});
}

/// @nodoc
class __$$_UserInfoCopyWithImpl<$Res>
    extends _$UserInfoCopyWithImpl<$Res, _$_UserInfo>
    implements _$$_UserInfoCopyWith<$Res> {
  __$$_UserInfoCopyWithImpl(
      _$_UserInfo _value, $Res Function(_$_UserInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatar = freezed,
  }) {
    return _then(_$_UserInfo(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserInfo implements _UserInfo {
  _$_UserInfo({this.nickname, this.avatar});

  factory _$_UserInfo.fromJson(Map<String, dynamic> json) =>
      _$$_UserInfoFromJson(json);

  /// 用户昵称
  @override
  final String? nickname;

  /// 头像
  @override
  final String? avatar;

  @override
  String toString() {
    return 'UserInfo(nickname: $nickname, avatar: $avatar)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserInfo &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickname, avatar);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserInfoCopyWith<_$_UserInfo> get copyWith =>
      __$$_UserInfoCopyWithImpl<_$_UserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserInfoToJson(
      this,
    );
  }
}

abstract class _UserInfo implements UserInfo {
  factory _UserInfo({final String? nickname, final String? avatar}) =
      _$_UserInfo;

  factory _UserInfo.fromJson(Map<String, dynamic> json) = _$_UserInfo.fromJson;

  @override

  /// 用户昵称
  String? get nickname;
  @override

  /// 头像
  String? get avatar;
  @override
  @JsonKey(ignore: true)
  _$$_UserInfoCopyWith<_$_UserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ShareInfo _$ShareInfoFromJson(Map<String, dynamic> json) {
  return _ShareInfo.fromJson(json);
}

/// @nodoc
mixin _$ShareInfo {
  /// 转介绍地址
  String? get introduceUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShareInfoCopyWith<ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShareInfoCopyWith<$Res> {
  factory $ShareInfoCopyWith(ShareInfo value, $Res Function(ShareInfo) then) =
      _$ShareInfoCopyWithImpl<$Res, ShareInfo>;
  @useResult
  $Res call({String? introduceUrl});
}

/// @nodoc
class _$ShareInfoCopyWithImpl<$Res, $Val extends ShareInfo>
    implements $ShareInfoCopyWith<$Res> {
  _$ShareInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduceUrl = freezed,
  }) {
    return _then(_value.copyWith(
      introduceUrl: freezed == introduceUrl
          ? _value.introduceUrl
          : introduceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShareInfoCopyWith<$Res> implements $ShareInfoCopyWith<$Res> {
  factory _$$_ShareInfoCopyWith(
          _$_ShareInfo value, $Res Function(_$_ShareInfo) then) =
      __$$_ShareInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? introduceUrl});
}

/// @nodoc
class __$$_ShareInfoCopyWithImpl<$Res>
    extends _$ShareInfoCopyWithImpl<$Res, _$_ShareInfo>
    implements _$$_ShareInfoCopyWith<$Res> {
  __$$_ShareInfoCopyWithImpl(
      _$_ShareInfo _value, $Res Function(_$_ShareInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduceUrl = freezed,
  }) {
    return _then(_$_ShareInfo(
      introduceUrl: freezed == introduceUrl
          ? _value.introduceUrl
          : introduceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShareInfo implements _ShareInfo {
  _$_ShareInfo({this.introduceUrl});

  factory _$_ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ShareInfoFromJson(json);

  /// 转介绍地址
  @override
  final String? introduceUrl;

  @override
  String toString() {
    return 'ShareInfo(introduceUrl: $introduceUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShareInfo &&
            (identical(other.introduceUrl, introduceUrl) ||
                other.introduceUrl == introduceUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, introduceUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      __$$_ShareInfoCopyWithImpl<_$_ShareInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShareInfoToJson(
      this,
    );
  }
}

abstract class _ShareInfo implements ShareInfo {
  factory _ShareInfo({final String? introduceUrl}) = _$_ShareInfo;

  factory _ShareInfo.fromJson(Map<String, dynamic> json) =
      _$_ShareInfo.fromJson;

  @override

  /// 转介绍地址
  String? get introduceUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
