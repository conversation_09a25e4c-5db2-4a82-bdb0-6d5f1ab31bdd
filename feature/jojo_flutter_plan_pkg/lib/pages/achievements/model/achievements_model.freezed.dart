// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'achievements_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AchievementsModel _$AchievementsModelFromJson(Map<String, dynamic> json) {
  return _AchievementsModel.fromJson(json);
}

/// @nodoc
mixin _$AchievementsModel {
  /// 是否仅训练营 (1是 0否)
  int? get onlyTrainingCamp => throw _privateConstructorUsedError;

  /// 页面站位图
  String? get positionImg => throw _privateConstructorUsedError;

  /// 公共资源
  String? get commonRes => throw _privateConstructorUsedError;

  /// 页面标题
  String? get title => throw _privateConstructorUsedError;

  /// 最近获得的勋章
  List<MedalGroup>? get latestMedals => throw _privateConstructorUsedError;

  /// 科目成就 (阶段勋章)
  List<SegmentMedal>? get segmentMedals => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AchievementsModelCopyWith<AchievementsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AchievementsModelCopyWith<$Res> {
  factory $AchievementsModelCopyWith(
          AchievementsModel value, $Res Function(AchievementsModel) then) =
      _$AchievementsModelCopyWithImpl<$Res, AchievementsModel>;
  @useResult
  $Res call(
      {int? onlyTrainingCamp,
      String? positionImg,
      String? commonRes,
      String? title,
      List<MedalGroup>? latestMedals,
      List<SegmentMedal>? segmentMedals});
}

/// @nodoc
class _$AchievementsModelCopyWithImpl<$Res, $Val extends AchievementsModel>
    implements $AchievementsModelCopyWith<$Res> {
  _$AchievementsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? onlyTrainingCamp = freezed,
    Object? positionImg = freezed,
    Object? commonRes = freezed,
    Object? title = freezed,
    Object? latestMedals = freezed,
    Object? segmentMedals = freezed,
  }) {
    return _then(_value.copyWith(
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      positionImg: freezed == positionImg
          ? _value.positionImg
          : positionImg // ignore: cast_nullable_to_non_nullable
              as String?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      latestMedals: freezed == latestMedals
          ? _value.latestMedals
          : latestMedals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
      segmentMedals: freezed == segmentMedals
          ? _value.segmentMedals
          : segmentMedals // ignore: cast_nullable_to_non_nullable
              as List<SegmentMedal>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AchievementsModelCopyWith<$Res>
    implements $AchievementsModelCopyWith<$Res> {
  factory _$$_AchievementsModelCopyWith(_$_AchievementsModel value,
          $Res Function(_$_AchievementsModel) then) =
      __$$_AchievementsModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? onlyTrainingCamp,
      String? positionImg,
      String? commonRes,
      String? title,
      List<MedalGroup>? latestMedals,
      List<SegmentMedal>? segmentMedals});
}

/// @nodoc
class __$$_AchievementsModelCopyWithImpl<$Res>
    extends _$AchievementsModelCopyWithImpl<$Res, _$_AchievementsModel>
    implements _$$_AchievementsModelCopyWith<$Res> {
  __$$_AchievementsModelCopyWithImpl(
      _$_AchievementsModel _value, $Res Function(_$_AchievementsModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? onlyTrainingCamp = freezed,
    Object? positionImg = freezed,
    Object? commonRes = freezed,
    Object? title = freezed,
    Object? latestMedals = freezed,
    Object? segmentMedals = freezed,
  }) {
    return _then(_$_AchievementsModel(
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      positionImg: freezed == positionImg
          ? _value.positionImg
          : positionImg // ignore: cast_nullable_to_non_nullable
              as String?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      latestMedals: freezed == latestMedals
          ? _value._latestMedals
          : latestMedals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
      segmentMedals: freezed == segmentMedals
          ? _value._segmentMedals
          : segmentMedals // ignore: cast_nullable_to_non_nullable
              as List<SegmentMedal>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AchievementsModel implements _AchievementsModel {
  _$_AchievementsModel(
      {this.onlyTrainingCamp,
      this.positionImg,
      this.commonRes,
      this.title,
      final List<MedalGroup>? latestMedals,
      final List<SegmentMedal>? segmentMedals})
      : _latestMedals = latestMedals,
        _segmentMedals = segmentMedals;

  factory _$_AchievementsModel.fromJson(Map<String, dynamic> json) =>
      _$$_AchievementsModelFromJson(json);

  /// 是否仅训练营 (1是 0否)
  @override
  final int? onlyTrainingCamp;

  /// 页面站位图
  @override
  final String? positionImg;

  /// 公共资源
  @override
  final String? commonRes;

  /// 页面标题
  @override
  final String? title;

  /// 最近获得的勋章
  final List<MedalGroup>? _latestMedals;

  /// 最近获得的勋章
  @override
  List<MedalGroup>? get latestMedals {
    final value = _latestMedals;
    if (value == null) return null;
    if (_latestMedals is EqualUnmodifiableListView) return _latestMedals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// 科目成就 (阶段勋章)
  final List<SegmentMedal>? _segmentMedals;

  /// 科目成就 (阶段勋章)
  @override
  List<SegmentMedal>? get segmentMedals {
    final value = _segmentMedals;
    if (value == null) return null;
    if (_segmentMedals is EqualUnmodifiableListView) return _segmentMedals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AchievementsModel(onlyTrainingCamp: $onlyTrainingCamp, positionImg: $positionImg, commonRes: $commonRes, title: $title, latestMedals: $latestMedals, segmentMedals: $segmentMedals)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AchievementsModel &&
            (identical(other.onlyTrainingCamp, onlyTrainingCamp) ||
                other.onlyTrainingCamp == onlyTrainingCamp) &&
            (identical(other.positionImg, positionImg) ||
                other.positionImg == positionImg) &&
            (identical(other.commonRes, commonRes) ||
                other.commonRes == commonRes) &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality()
                .equals(other._latestMedals, _latestMedals) &&
            const DeepCollectionEquality()
                .equals(other._segmentMedals, _segmentMedals));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      onlyTrainingCamp,
      positionImg,
      commonRes,
      title,
      const DeepCollectionEquality().hash(_latestMedals),
      const DeepCollectionEquality().hash(_segmentMedals));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AchievementsModelCopyWith<_$_AchievementsModel> get copyWith =>
      __$$_AchievementsModelCopyWithImpl<_$_AchievementsModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AchievementsModelToJson(
      this,
    );
  }
}

abstract class _AchievementsModel implements AchievementsModel {
  factory _AchievementsModel(
      {final int? onlyTrainingCamp,
      final String? positionImg,
      final String? commonRes,
      final String? title,
      final List<MedalGroup>? latestMedals,
      final List<SegmentMedal>? segmentMedals}) = _$_AchievementsModel;

  factory _AchievementsModel.fromJson(Map<String, dynamic> json) =
      _$_AchievementsModel.fromJson;

  @override

  /// 是否仅训练营 (1是 0否)
  int? get onlyTrainingCamp;
  @override

  /// 页面站位图
  String? get positionImg;
  @override

  /// 公共资源
  String? get commonRes;
  @override

  /// 页面标题
  String? get title;
  @override

  /// 最近获得的勋章
  List<MedalGroup>? get latestMedals;
  @override

  /// 科目成就 (阶段勋章)
  List<SegmentMedal>? get segmentMedals;
  @override
  @JsonKey(ignore: true)
  _$$_AchievementsModelCopyWith<_$_AchievementsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

MedalGroup _$MedalGroupFromJson(Map<String, dynamic> json) {
  return _MedalGroup.fromJson(json);
}

/// @nodoc
mixin _$MedalGroup {
  /// 是否可升级 (1是 0否)
  int? get upgrade => throw _privateConstructorUsedError;

  /// 勋章分组 key
  String? get groupKey => throw _privateConstructorUsedError;

  /// 奖章提示
  String? get tips => throw _privateConstructorUsedError;

  /// 该分组下的勋章列表
  List<MedalModel>? get groupMedals => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalGroupCopyWith<MedalGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalGroupCopyWith<$Res> {
  factory $MedalGroupCopyWith(
          MedalGroup value, $Res Function(MedalGroup) then) =
      _$MedalGroupCopyWithImpl<$Res, MedalGroup>;
  @useResult
  $Res call(
      {int? upgrade,
      String? groupKey,
      String? tips,
      List<MedalModel>? groupMedals});
}

/// @nodoc
class _$MedalGroupCopyWithImpl<$Res, $Val extends MedalGroup>
    implements $MedalGroupCopyWith<$Res> {
  _$MedalGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? upgrade = freezed,
    Object? groupKey = freezed,
    Object? tips = freezed,
    Object? groupMedals = freezed,
  }) {
    return _then(_value.copyWith(
      upgrade: freezed == upgrade
          ? _value.upgrade
          : upgrade // ignore: cast_nullable_to_non_nullable
              as int?,
      groupKey: freezed == groupKey
          ? _value.groupKey
          : groupKey // ignore: cast_nullable_to_non_nullable
              as String?,
      tips: freezed == tips
          ? _value.tips
          : tips // ignore: cast_nullable_to_non_nullable
              as String?,
      groupMedals: freezed == groupMedals
          ? _value.groupMedals
          : groupMedals // ignore: cast_nullable_to_non_nullable
              as List<MedalModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalGroupCopyWith<$Res>
    implements $MedalGroupCopyWith<$Res> {
  factory _$$_MedalGroupCopyWith(
          _$_MedalGroup value, $Res Function(_$_MedalGroup) then) =
      __$$_MedalGroupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? upgrade,
      String? groupKey,
      String? tips,
      List<MedalModel>? groupMedals});
}

/// @nodoc
class __$$_MedalGroupCopyWithImpl<$Res>
    extends _$MedalGroupCopyWithImpl<$Res, _$_MedalGroup>
    implements _$$_MedalGroupCopyWith<$Res> {
  __$$_MedalGroupCopyWithImpl(
      _$_MedalGroup _value, $Res Function(_$_MedalGroup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? upgrade = freezed,
    Object? groupKey = freezed,
    Object? tips = freezed,
    Object? groupMedals = freezed,
  }) {
    return _then(_$_MedalGroup(
      upgrade: freezed == upgrade
          ? _value.upgrade
          : upgrade // ignore: cast_nullable_to_non_nullable
              as int?,
      groupKey: freezed == groupKey
          ? _value.groupKey
          : groupKey // ignore: cast_nullable_to_non_nullable
              as String?,
      tips: freezed == tips
          ? _value.tips
          : tips // ignore: cast_nullable_to_non_nullable
              as String?,
      groupMedals: freezed == groupMedals
          ? _value._groupMedals
          : groupMedals // ignore: cast_nullable_to_non_nullable
              as List<MedalModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalGroup implements _MedalGroup {
  _$_MedalGroup(
      {this.upgrade,
      this.groupKey,
      this.tips,
      final List<MedalModel>? groupMedals})
      : _groupMedals = groupMedals;

  factory _$_MedalGroup.fromJson(Map<String, dynamic> json) =>
      _$$_MedalGroupFromJson(json);

  /// 是否可升级 (1是 0否)
  @override
  final int? upgrade;

  /// 勋章分组 key
  @override
  final String? groupKey;

  /// 奖章提示
  @override
  final String? tips;

  /// 该分组下的勋章列表
  final List<MedalModel>? _groupMedals;

  /// 该分组下的勋章列表
  @override
  List<MedalModel>? get groupMedals {
    final value = _groupMedals;
    if (value == null) return null;
    if (_groupMedals is EqualUnmodifiableListView) return _groupMedals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MedalGroup(upgrade: $upgrade, groupKey: $groupKey, tips: $tips, groupMedals: $groupMedals)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalGroup &&
            (identical(other.upgrade, upgrade) || other.upgrade == upgrade) &&
            (identical(other.groupKey, groupKey) ||
                other.groupKey == groupKey) &&
            (identical(other.tips, tips) || other.tips == tips) &&
            const DeepCollectionEquality()
                .equals(other._groupMedals, _groupMedals));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, upgrade, groupKey, tips,
      const DeepCollectionEquality().hash(_groupMedals));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalGroupCopyWith<_$_MedalGroup> get copyWith =>
      __$$_MedalGroupCopyWithImpl<_$_MedalGroup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalGroupToJson(
      this,
    );
  }
}

abstract class _MedalGroup implements MedalGroup {
  factory _MedalGroup(
      {final int? upgrade,
      final String? groupKey,
      final String? tips,
      final List<MedalModel>? groupMedals}) = _$_MedalGroup;

  factory _MedalGroup.fromJson(Map<String, dynamic> json) =
      _$_MedalGroup.fromJson;

  @override

  /// 是否可升级 (1是 0否)
  int? get upgrade;
  @override

  /// 勋章分组 key
  String? get groupKey;
  @override

  /// 奖章提示
  String? get tips;
  @override

  /// 该分组下的勋章列表
  List<MedalModel>? get groupMedals;
  @override
  @JsonKey(ignore: true)
  _$$_MedalGroupCopyWith<_$_MedalGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

SegmentMedal _$SegmentMedalFromJson(Map<String, dynamic> json) {
  return _SegmentMedal.fromJson(json);
}

/// @nodoc
mixin _$SegmentMedal {
  /// 科目标题
  String? get segmentTitle => throw _privateConstructorUsedError;

  /// 阶段名称
  String? get segmentName => throw _privateConstructorUsedError;

  /// 阶段编码
  int? get segmentCode => throw _privateConstructorUsedError;

  /// 阶段下的分类
  List<MedalCategory>? get category => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentMedalCopyWith<SegmentMedal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentMedalCopyWith<$Res> {
  factory $SegmentMedalCopyWith(
          SegmentMedal value, $Res Function(SegmentMedal) then) =
      _$SegmentMedalCopyWithImpl<$Res, SegmentMedal>;
  @useResult
  $Res call(
      {String? segmentTitle,
      String? segmentName,
      int? segmentCode,
      List<MedalCategory>? category});
}

/// @nodoc
class _$SegmentMedalCopyWithImpl<$Res, $Val extends SegmentMedal>
    implements $SegmentMedalCopyWith<$Res> {
  _$SegmentMedalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentTitle = freezed,
    Object? segmentName = freezed,
    Object? segmentCode = freezed,
    Object? category = freezed,
  }) {
    return _then(_value.copyWith(
      segmentTitle: freezed == segmentTitle
          ? _value.segmentTitle
          : segmentTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentCode: freezed == segmentCode
          ? _value.segmentCode
          : segmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as List<MedalCategory>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SegmentMedalCopyWith<$Res>
    implements $SegmentMedalCopyWith<$Res> {
  factory _$$_SegmentMedalCopyWith(
          _$_SegmentMedal value, $Res Function(_$_SegmentMedal) then) =
      __$$_SegmentMedalCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? segmentTitle,
      String? segmentName,
      int? segmentCode,
      List<MedalCategory>? category});
}

/// @nodoc
class __$$_SegmentMedalCopyWithImpl<$Res>
    extends _$SegmentMedalCopyWithImpl<$Res, _$_SegmentMedal>
    implements _$$_SegmentMedalCopyWith<$Res> {
  __$$_SegmentMedalCopyWithImpl(
      _$_SegmentMedal _value, $Res Function(_$_SegmentMedal) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentTitle = freezed,
    Object? segmentName = freezed,
    Object? segmentCode = freezed,
    Object? category = freezed,
  }) {
    return _then(_$_SegmentMedal(
      segmentTitle: freezed == segmentTitle
          ? _value.segmentTitle
          : segmentTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentCode: freezed == segmentCode
          ? _value.segmentCode
          : segmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value._category
          : category // ignore: cast_nullable_to_non_nullable
              as List<MedalCategory>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SegmentMedal implements _SegmentMedal {
  _$_SegmentMedal(
      {this.segmentTitle,
      this.segmentName,
      this.segmentCode,
      final List<MedalCategory>? category})
      : _category = category;

  factory _$_SegmentMedal.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentMedalFromJson(json);

  /// 科目标题
  @override
  final String? segmentTitle;

  /// 阶段名称
  @override
  final String? segmentName;

  /// 阶段编码
  @override
  final int? segmentCode;

  /// 阶段下的分类
  final List<MedalCategory>? _category;

  /// 阶段下的分类
  @override
  List<MedalCategory>? get category {
    final value = _category;
    if (value == null) return null;
    if (_category is EqualUnmodifiableListView) return _category;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SegmentMedal(segmentTitle: $segmentTitle, segmentName: $segmentName, segmentCode: $segmentCode, category: $category)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SegmentMedal &&
            (identical(other.segmentTitle, segmentTitle) ||
                other.segmentTitle == segmentTitle) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.segmentCode, segmentCode) ||
                other.segmentCode == segmentCode) &&
            const DeepCollectionEquality().equals(other._category, _category));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, segmentTitle, segmentName,
      segmentCode, const DeepCollectionEquality().hash(_category));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentMedalCopyWith<_$_SegmentMedal> get copyWith =>
      __$$_SegmentMedalCopyWithImpl<_$_SegmentMedal>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentMedalToJson(
      this,
    );
  }
}

abstract class _SegmentMedal implements SegmentMedal {
  factory _SegmentMedal(
      {final String? segmentTitle,
      final String? segmentName,
      final int? segmentCode,
      final List<MedalCategory>? category}) = _$_SegmentMedal;

  factory _SegmentMedal.fromJson(Map<String, dynamic> json) =
      _$_SegmentMedal.fromJson;

  @override

  /// 科目标题
  String? get segmentTitle;
  @override

  /// 阶段名称
  String? get segmentName;
  @override

  /// 阶段编码
  int? get segmentCode;
  @override

  /// 阶段下的分类
  List<MedalCategory>? get category;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentMedalCopyWith<_$_SegmentMedal> get copyWith =>
      throw _privateConstructorUsedError;
}

MedalCategory _$MedalCategoryFromJson(Map<String, dynamic> json) {
  return _MedalCategory.fromJson(json);
}

/// @nodoc
mixin _$MedalCategory {
  /// 分类名称
  String? get name => throw _privateConstructorUsedError;

  /// 分类下的勋章组
  List<MedalGroup>? get medals => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalCategoryCopyWith<MedalCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalCategoryCopyWith<$Res> {
  factory $MedalCategoryCopyWith(
          MedalCategory value, $Res Function(MedalCategory) then) =
      _$MedalCategoryCopyWithImpl<$Res, MedalCategory>;
  @useResult
  $Res call({String? name, List<MedalGroup>? medals});
}

/// @nodoc
class _$MedalCategoryCopyWithImpl<$Res, $Val extends MedalCategory>
    implements $MedalCategoryCopyWith<$Res> {
  _$MedalCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? medals = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      medals: freezed == medals
          ? _value.medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalCategoryCopyWith<$Res>
    implements $MedalCategoryCopyWith<$Res> {
  factory _$$_MedalCategoryCopyWith(
          _$_MedalCategory value, $Res Function(_$_MedalCategory) then) =
      __$$_MedalCategoryCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, List<MedalGroup>? medals});
}

/// @nodoc
class __$$_MedalCategoryCopyWithImpl<$Res>
    extends _$MedalCategoryCopyWithImpl<$Res, _$_MedalCategory>
    implements _$$_MedalCategoryCopyWith<$Res> {
  __$$_MedalCategoryCopyWithImpl(
      _$_MedalCategory _value, $Res Function(_$_MedalCategory) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? medals = freezed,
  }) {
    return _then(_$_MedalCategory(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      medals: freezed == medals
          ? _value._medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalCategory implements _MedalCategory {
  _$_MedalCategory({this.name, final List<MedalGroup>? medals})
      : _medals = medals;

  factory _$_MedalCategory.fromJson(Map<String, dynamic> json) =>
      _$$_MedalCategoryFromJson(json);

  /// 分类名称
  @override
  final String? name;

  /// 分类下的勋章组
  final List<MedalGroup>? _medals;

  /// 分类下的勋章组
  @override
  List<MedalGroup>? get medals {
    final value = _medals;
    if (value == null) return null;
    if (_medals is EqualUnmodifiableListView) return _medals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MedalCategory(name: $name, medals: $medals)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalCategory &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._medals, _medals));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, const DeepCollectionEquality().hash(_medals));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalCategoryCopyWith<_$_MedalCategory> get copyWith =>
      __$$_MedalCategoryCopyWithImpl<_$_MedalCategory>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalCategoryToJson(
      this,
    );
  }
}

abstract class _MedalCategory implements MedalCategory {
  factory _MedalCategory({final String? name, final List<MedalGroup>? medals}) =
      _$_MedalCategory;

  factory _MedalCategory.fromJson(Map<String, dynamic> json) =
      _$_MedalCategory.fromJson;

  @override

  /// 分类名称
  String? get name;
  @override

  /// 分类下的勋章组
  List<MedalGroup>? get medals;
  @override
  @JsonKey(ignore: true)
  _$$_MedalCategoryCopyWith<_$_MedalCategory> get copyWith =>
      throw _privateConstructorUsedError;
}
