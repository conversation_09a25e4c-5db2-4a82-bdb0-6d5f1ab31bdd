import 'package:freezed_annotation/freezed_annotation.dart';

part 'medal_model.freezed.dart';
part 'medal_model.g.dart';

/// 勋章信息类
@freezed
class MedalModel with _$MedalModel {
  factory MedalModel({
    /// 勋章ID
    int? id,

    /// 收集系统分布式主键ID
    String? collectionId,

    /// 勋章称号
    String? title,

    /// 勋章资源
    Map<String, String>? resource,

    /// 是否获得 (1是 0否)
    int? isGet,

    /// 获得时间 (时间戳)
    int? getTime,

    /// 是否查看 (1是 0否)
    int? isView,

    /// 勋章关联的任务描述 (主任务)
    String? remark,

    /// 勋章详情路由
    String? detailRoute,

    /// 分享页路由
    String? shareRoute,

    /// 进度信息
    MedalProgress? progress,

    /// 奖章标签
    List<MedalTag>? tags,

    /// 奖章介绍
    String? description,
  }) = _MedalModel;

  factory MedalModel.fromJson(Map<String, dynamic> json) =>
      _$MedalModelFromJson(json);
}

/// 进度信息类
@freezed
class MedalProgress with _$MedalProgress {
  factory MedalProgress({
    /// 奖章进度总量
    int? total,

    /// 奖章当前进度值
    int? current,

    /// 进度单位 (天、字、次)
    String? unit,
  }) = _MedalProgress;

  factory MedalProgress.fromJson(Map<String, dynamic> json) =>
      _$MedalProgressFromJson(json);
}

/// 奖章标签类
@freezed
class MedalTag with _$MedalTag {
  factory MedalTag({
    /// 标签名称
    String? name,
  }) = _MedalTag;

  factory MedalTag.fromJson(Map<String, dynamic> json) =>
      _$MedalTagFromJson(json);
}