import 'package:freezed_annotation/freezed_annotation.dart';
import 'achievements_model.dart';

part 'achievement_detail_model.freezed.dart';
part 'achievement_detail_model.g.dart';

/// 成就详情数据模型
@freezed
class AchievementDetailModel with _$AchievementDetailModel {
  factory AchievementDetailModel({
    /// 用户信息
    UserInfo? userInfo,

    /// 分享信息
    ShareInfo? shareInfo,

    /// 公共资源
    String? commonRes,

    /// 科目类型
    int? subjectType,

    /// 阶段
    String? segmentName,

    /// 是否仅训练营 (1是 0否)
    int? onlyTrainingCamp,

    /// 勋章分组
    MedalGroup? medalGroup,
  }) = _AchievementDetailModel;

  factory AchievementDetailModel.fromJson(Map<String, dynamic> json) =>
      _$AchievementDetailModelFromJson(json);
}

/// 用户信息模型
@freezed
class UserInfo with _$UserInfo {
  factory UserInfo({
    /// 用户昵称
    String? nickname,

    /// 头像
    String? avatar,
  }) = _UserInfo;

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);
}

/// 分享信息模型
@freezed
class ShareInfo with _$ShareInfo {
  factory ShareInfo({
    /// 转介绍地址
    String? introduceUrl,
  }) = _ShareInfo;

  factory ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$ShareInfoFromJson(json);
}