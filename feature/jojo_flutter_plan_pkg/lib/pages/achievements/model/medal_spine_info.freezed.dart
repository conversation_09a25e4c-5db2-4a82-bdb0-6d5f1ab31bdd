// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medal_spine_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MedalSpineInfo _$MedalSpineInfoFromJson(Map<String, dynamic> json) {
  return _MedalSpineInfo.fromJson(json);
}

/// @nodoc
mixin _$MedalSpineInfo {
  /// 标题颜色
  String? get textColor => throw _privateConstructorUsedError;

  /// 卡片资源类型
  String? get medalResType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalSpineInfoCopyWith<MedalSpineInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalSpineInfoCopyWith<$Res> {
  factory $MedalSpineInfoCopyWith(
          MedalSpineInfo value, $Res Function(MedalSpineInfo) then) =
      _$MedalSpineInfoCopyWithImpl<$Res, MedalSpineInfo>;
  @useResult
  $Res call({String? textColor, String? medalResType});
}

/// @nodoc
class _$MedalSpineInfoCopyWithImpl<$Res, $Val extends MedalSpineInfo>
    implements $MedalSpineInfoCopyWith<$Res> {
  _$MedalSpineInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? textColor = freezed,
    Object? medalResType = freezed,
  }) {
    return _then(_value.copyWith(
      textColor: freezed == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as String?,
      medalResType: freezed == medalResType
          ? _value.medalResType
          : medalResType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalSpineInfoCopyWith<$Res>
    implements $MedalSpineInfoCopyWith<$Res> {
  factory _$$_MedalSpineInfoCopyWith(
          _$_MedalSpineInfo value, $Res Function(_$_MedalSpineInfo) then) =
      __$$_MedalSpineInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? textColor, String? medalResType});
}

/// @nodoc
class __$$_MedalSpineInfoCopyWithImpl<$Res>
    extends _$MedalSpineInfoCopyWithImpl<$Res, _$_MedalSpineInfo>
    implements _$$_MedalSpineInfoCopyWith<$Res> {
  __$$_MedalSpineInfoCopyWithImpl(
      _$_MedalSpineInfo _value, $Res Function(_$_MedalSpineInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? textColor = freezed,
    Object? medalResType = freezed,
  }) {
    return _then(_$_MedalSpineInfo(
      textColor: freezed == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as String?,
      medalResType: freezed == medalResType
          ? _value.medalResType
          : medalResType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalSpineInfo implements _MedalSpineInfo {
  _$_MedalSpineInfo({this.textColor, this.medalResType});

  factory _$_MedalSpineInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MedalSpineInfoFromJson(json);

  /// 标题颜色
  @override
  final String? textColor;

  /// 卡片资源类型
  @override
  final String? medalResType;

  @override
  String toString() {
    return 'MedalSpineInfo(textColor: $textColor, medalResType: $medalResType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalSpineInfo &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.medalResType, medalResType) ||
                other.medalResType == medalResType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, textColor, medalResType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalSpineInfoCopyWith<_$_MedalSpineInfo> get copyWith =>
      __$$_MedalSpineInfoCopyWithImpl<_$_MedalSpineInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalSpineInfoToJson(
      this,
    );
  }
}

abstract class _MedalSpineInfo implements MedalSpineInfo {
  factory _MedalSpineInfo(
      {final String? textColor,
      final String? medalResType}) = _$_MedalSpineInfo;

  factory _MedalSpineInfo.fromJson(Map<String, dynamic> json) =
      _$_MedalSpineInfo.fromJson;

  @override

  /// 标题颜色
  String? get textColor;
  @override

  /// 卡片资源类型
  String? get medalResType;
  @override
  @JsonKey(ignore: true)
  _$$_MedalSpineInfoCopyWith<_$_MedalSpineInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
