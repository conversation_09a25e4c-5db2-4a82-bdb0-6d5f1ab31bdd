import 'package:freezed_annotation/freezed_annotation.dart';

part 'medal_spine_info.freezed.dart';
part 'medal_spine_info.g.dart';

@freezed
class MedalSpineInfo with _$MedalSpineInfo {
  factory MedalSpineInfo({
    /// 标题颜色
    String? textColor,
    /// 卡片资源类型
    String? medalResType,
  }) = _MedalSpineInfo;

  factory MedalSpineInfo.fromJson(Map<String, dynamic> json) =>
      _$MedalSpineInfoFromJson(json);
}