// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medal_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MedalModel _$$_MedalModelFromJson(Map<String, dynamic> json) =>
    _$_MedalModel(
      id: json['id'] as int?,
      collectionId: json['collectionId'] as String?,
      title: json['title'] as String?,
      resource: (json['resource'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      isGet: json['isGet'] as int?,
      getTime: json['getTime'] as int?,
      isView: json['isView'] as int?,
      remark: json['remark'] as String?,
      detailRoute: json['detailRoute'] as String?,
      shareRoute: json['shareRoute'] as String?,
      progress: json['progress'] == null
          ? null
          : MedalProgress.fromJson(json['progress'] as Map<String, dynamic>),
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => MedalTag.fromJson(e as Map<String, dynamic>))
          .toList(),
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$_MedalModelToJson(_$_MedalModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'collectionId': instance.collectionId,
      'title': instance.title,
      'resource': instance.resource,
      'isGet': instance.isGet,
      'getTime': instance.getTime,
      'isView': instance.isView,
      'remark': instance.remark,
      'detailRoute': instance.detailRoute,
      'shareRoute': instance.shareRoute,
      'progress': instance.progress,
      'tags': instance.tags,
      'description': instance.description,
    };

_$_MedalProgress _$$_MedalProgressFromJson(Map<String, dynamic> json) =>
    _$_MedalProgress(
      total: json['total'] as int?,
      current: json['current'] as int?,
      unit: json['unit'] as String?,
    );

Map<String, dynamic> _$$_MedalProgressToJson(_$_MedalProgress instance) =>
    <String, dynamic>{
      'total': instance.total,
      'current': instance.current,
      'unit': instance.unit,
    };

_$_MedalTag _$$_MedalTagFromJson(Map<String, dynamic> json) => _$_MedalTag(
      name: json['name'] as String?,
    );

Map<String, dynamic> _$$_MedalTagToJson(_$_MedalTag instance) =>
    <String, dynamic>{
      'name': instance.name,
    };
