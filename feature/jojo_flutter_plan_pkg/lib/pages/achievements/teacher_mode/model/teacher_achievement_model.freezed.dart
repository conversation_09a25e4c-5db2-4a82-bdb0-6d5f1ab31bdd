// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teacher_achievement_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeacherAchievementModel _$TeacherAchievementModelFromJson(
    Map<String, dynamic> json) {
  return _TeacherAchievementModel.fromJson(json);
}

/// @nodoc
mixin _$TeacherAchievementModel {
// 学端分类
  List<TeacherAchievementSegmentModel>? get segments =>
      throw _privateConstructorUsedError; // 学科分类
  List<TeacherAchievementSubjectModel>? get subjects =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherAchievementModelCopyWith<TeacherAchievementModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherAchievementModelCopyWith<$Res> {
  factory $TeacherAchievementModelCopyWith(TeacherAchievementModel value,
          $Res Function(TeacherAchievementModel) then) =
      _$TeacherAchievementModelCopyWithImpl<$Res, TeacherAchievementModel>;
  @useResult
  $Res call(
      {List<TeacherAchievementSegmentModel>? segments,
      List<TeacherAchievementSubjectModel>? subjects});
}

/// @nodoc
class _$TeacherAchievementModelCopyWithImpl<$Res,
        $Val extends TeacherAchievementModel>
    implements $TeacherAchievementModelCopyWith<$Res> {
  _$TeacherAchievementModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segments = freezed,
    Object? subjects = freezed,
  }) {
    return _then(_value.copyWith(
      segments: freezed == segments
          ? _value.segments
          : segments // ignore: cast_nullable_to_non_nullable
              as List<TeacherAchievementSegmentModel>?,
      subjects: freezed == subjects
          ? _value.subjects
          : subjects // ignore: cast_nullable_to_non_nullable
              as List<TeacherAchievementSubjectModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherAchievementModelCopyWith<$Res>
    implements $TeacherAchievementModelCopyWith<$Res> {
  factory _$$_TeacherAchievementModelCopyWith(_$_TeacherAchievementModel value,
          $Res Function(_$_TeacherAchievementModel) then) =
      __$$_TeacherAchievementModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TeacherAchievementSegmentModel>? segments,
      List<TeacherAchievementSubjectModel>? subjects});
}

/// @nodoc
class __$$_TeacherAchievementModelCopyWithImpl<$Res>
    extends _$TeacherAchievementModelCopyWithImpl<$Res,
        _$_TeacherAchievementModel>
    implements _$$_TeacherAchievementModelCopyWith<$Res> {
  __$$_TeacherAchievementModelCopyWithImpl(_$_TeacherAchievementModel _value,
      $Res Function(_$_TeacherAchievementModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segments = freezed,
    Object? subjects = freezed,
  }) {
    return _then(_$_TeacherAchievementModel(
      segments: freezed == segments
          ? _value._segments
          : segments // ignore: cast_nullable_to_non_nullable
              as List<TeacherAchievementSegmentModel>?,
      subjects: freezed == subjects
          ? _value._subjects
          : subjects // ignore: cast_nullable_to_non_nullable
              as List<TeacherAchievementSubjectModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherAchievementModel
    with DiagnosticableTreeMixin
    implements _TeacherAchievementModel {
  const _$_TeacherAchievementModel(
      {final List<TeacherAchievementSegmentModel>? segments,
      final List<TeacherAchievementSubjectModel>? subjects})
      : _segments = segments,
        _subjects = subjects;

  factory _$_TeacherAchievementModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherAchievementModelFromJson(json);

// 学端分类
  final List<TeacherAchievementSegmentModel>? _segments;
// 学端分类
  @override
  List<TeacherAchievementSegmentModel>? get segments {
    final value = _segments;
    if (value == null) return null;
    if (_segments is EqualUnmodifiableListView) return _segments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 学科分类
  final List<TeacherAchievementSubjectModel>? _subjects;
// 学科分类
  @override
  List<TeacherAchievementSubjectModel>? get subjects {
    final value = _subjects;
    if (value == null) return null;
    if (_subjects is EqualUnmodifiableListView) return _subjects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherAchievementModel(segments: $segments, subjects: $subjects)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherAchievementModel'))
      ..add(DiagnosticsProperty('segments', segments))
      ..add(DiagnosticsProperty('subjects', subjects));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherAchievementModel &&
            const DeepCollectionEquality().equals(other._segments, _segments) &&
            const DeepCollectionEquality().equals(other._subjects, _subjects));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_segments),
      const DeepCollectionEquality().hash(_subjects));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherAchievementModelCopyWith<_$_TeacherAchievementModel>
      get copyWith =>
          __$$_TeacherAchievementModelCopyWithImpl<_$_TeacherAchievementModel>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherAchievementModelToJson(
      this,
    );
  }
}

abstract class _TeacherAchievementModel implements TeacherAchievementModel {
  const factory _TeacherAchievementModel(
          {final List<TeacherAchievementSegmentModel>? segments,
          final List<TeacherAchievementSubjectModel>? subjects}) =
      _$_TeacherAchievementModel;

  factory _TeacherAchievementModel.fromJson(Map<String, dynamic> json) =
      _$_TeacherAchievementModel.fromJson;

  @override // 学端分类
  List<TeacherAchievementSegmentModel>? get segments;
  @override // 学科分类
  List<TeacherAchievementSubjectModel>? get subjects;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherAchievementModelCopyWith<_$_TeacherAchievementModel>
      get copyWith => throw _privateConstructorUsedError;
}

TeacherAchievementSegmentModel _$TeacherAchievementSegmentModelFromJson(
    Map<String, dynamic> json) {
  return _TeacherAchievementSegmentModel.fromJson(json);
}

/// @nodoc
mixin _$TeacherAchievementSegmentModel {
  int? get segmentCode => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherAchievementSegmentModelCopyWith<TeacherAchievementSegmentModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherAchievementSegmentModelCopyWith<$Res> {
  factory $TeacherAchievementSegmentModelCopyWith(
          TeacherAchievementSegmentModel value,
          $Res Function(TeacherAchievementSegmentModel) then) =
      _$TeacherAchievementSegmentModelCopyWithImpl<$Res,
          TeacherAchievementSegmentModel>;
  @useResult
  $Res call({int? segmentCode, String? segmentName});
}

/// @nodoc
class _$TeacherAchievementSegmentModelCopyWithImpl<$Res,
        $Val extends TeacherAchievementSegmentModel>
    implements $TeacherAchievementSegmentModelCopyWith<$Res> {
  _$TeacherAchievementSegmentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentCode = freezed,
    Object? segmentName = freezed,
  }) {
    return _then(_value.copyWith(
      segmentCode: freezed == segmentCode
          ? _value.segmentCode
          : segmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherAchievementSegmentModelCopyWith<$Res>
    implements $TeacherAchievementSegmentModelCopyWith<$Res> {
  factory _$$_TeacherAchievementSegmentModelCopyWith(
          _$_TeacherAchievementSegmentModel value,
          $Res Function(_$_TeacherAchievementSegmentModel) then) =
      __$$_TeacherAchievementSegmentModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? segmentCode, String? segmentName});
}

/// @nodoc
class __$$_TeacherAchievementSegmentModelCopyWithImpl<$Res>
    extends _$TeacherAchievementSegmentModelCopyWithImpl<$Res,
        _$_TeacherAchievementSegmentModel>
    implements _$$_TeacherAchievementSegmentModelCopyWith<$Res> {
  __$$_TeacherAchievementSegmentModelCopyWithImpl(
      _$_TeacherAchievementSegmentModel _value,
      $Res Function(_$_TeacherAchievementSegmentModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentCode = freezed,
    Object? segmentName = freezed,
  }) {
    return _then(_$_TeacherAchievementSegmentModel(
      segmentCode: freezed == segmentCode
          ? _value.segmentCode
          : segmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherAchievementSegmentModel
    with DiagnosticableTreeMixin
    implements _TeacherAchievementSegmentModel {
  const _$_TeacherAchievementSegmentModel({this.segmentCode, this.segmentName});

  factory _$_TeacherAchievementSegmentModel.fromJson(
          Map<String, dynamic> json) =>
      _$$_TeacherAchievementSegmentModelFromJson(json);

  @override
  final int? segmentCode;
  @override
  final String? segmentName;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherAchievementSegmentModel(segmentCode: $segmentCode, segmentName: $segmentName)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherAchievementSegmentModel'))
      ..add(DiagnosticsProperty('segmentCode', segmentCode))
      ..add(DiagnosticsProperty('segmentName', segmentName));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherAchievementSegmentModel &&
            (identical(other.segmentCode, segmentCode) ||
                other.segmentCode == segmentCode) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, segmentCode, segmentName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherAchievementSegmentModelCopyWith<_$_TeacherAchievementSegmentModel>
      get copyWith => __$$_TeacherAchievementSegmentModelCopyWithImpl<
          _$_TeacherAchievementSegmentModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherAchievementSegmentModelToJson(
      this,
    );
  }
}

abstract class _TeacherAchievementSegmentModel
    implements TeacherAchievementSegmentModel {
  const factory _TeacherAchievementSegmentModel(
      {final int? segmentCode,
      final String? segmentName}) = _$_TeacherAchievementSegmentModel;

  factory _TeacherAchievementSegmentModel.fromJson(Map<String, dynamic> json) =
      _$_TeacherAchievementSegmentModel.fromJson;

  @override
  int? get segmentCode;
  @override
  String? get segmentName;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherAchievementSegmentModelCopyWith<_$_TeacherAchievementSegmentModel>
      get copyWith => throw _privateConstructorUsedError;
}

TeacherAchievementSubjectModel _$TeacherAchievementSubjectModelFromJson(
    Map<String, dynamic> json) {
  return _TeacherAchievementSubjectModel.fromJson(json);
}

/// @nodoc
mixin _$TeacherAchievementSubjectModel {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  int? get selectedType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherAchievementSubjectModelCopyWith<TeacherAchievementSubjectModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherAchievementSubjectModelCopyWith<$Res> {
  factory $TeacherAchievementSubjectModelCopyWith(
          TeacherAchievementSubjectModel value,
          $Res Function(TeacherAchievementSubjectModel) then) =
      _$TeacherAchievementSubjectModelCopyWithImpl<$Res,
          TeacherAchievementSubjectModel>;
  @useResult
  $Res call({int? subjectType, String? subjectName, int? selectedType});
}

/// @nodoc
class _$TeacherAchievementSubjectModelCopyWithImpl<$Res,
        $Val extends TeacherAchievementSubjectModel>
    implements $TeacherAchievementSubjectModelCopyWith<$Res> {
  _$TeacherAchievementSubjectModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? selectedType = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedType: freezed == selectedType
          ? _value.selectedType
          : selectedType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherAchievementSubjectModelCopyWith<$Res>
    implements $TeacherAchievementSubjectModelCopyWith<$Res> {
  factory _$$_TeacherAchievementSubjectModelCopyWith(
          _$_TeacherAchievementSubjectModel value,
          $Res Function(_$_TeacherAchievementSubjectModel) then) =
      __$$_TeacherAchievementSubjectModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, String? subjectName, int? selectedType});
}

/// @nodoc
class __$$_TeacherAchievementSubjectModelCopyWithImpl<$Res>
    extends _$TeacherAchievementSubjectModelCopyWithImpl<$Res,
        _$_TeacherAchievementSubjectModel>
    implements _$$_TeacherAchievementSubjectModelCopyWith<$Res> {
  __$$_TeacherAchievementSubjectModelCopyWithImpl(
      _$_TeacherAchievementSubjectModel _value,
      $Res Function(_$_TeacherAchievementSubjectModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? selectedType = freezed,
  }) {
    return _then(_$_TeacherAchievementSubjectModel(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedType: freezed == selectedType
          ? _value.selectedType
          : selectedType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherAchievementSubjectModel
    with DiagnosticableTreeMixin
    implements _TeacherAchievementSubjectModel {
  const _$_TeacherAchievementSubjectModel(
      {this.subjectType, this.subjectName, this.selectedType});

  factory _$_TeacherAchievementSubjectModel.fromJson(
          Map<String, dynamic> json) =>
      _$$_TeacherAchievementSubjectModelFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectName;
  @override
  final int? selectedType;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherAchievementSubjectModel(subjectType: $subjectType, subjectName: $subjectName, selectedType: $selectedType)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherAchievementSubjectModel'))
      ..add(DiagnosticsProperty('subjectType', subjectType))
      ..add(DiagnosticsProperty('subjectName', subjectName))
      ..add(DiagnosticsProperty('selectedType', selectedType));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherAchievementSubjectModel &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.selectedType, selectedType) ||
                other.selectedType == selectedType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, subjectType, subjectName, selectedType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherAchievementSubjectModelCopyWith<_$_TeacherAchievementSubjectModel>
      get copyWith => __$$_TeacherAchievementSubjectModelCopyWithImpl<
          _$_TeacherAchievementSubjectModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherAchievementSubjectModelToJson(
      this,
    );
  }
}

abstract class _TeacherAchievementSubjectModel
    implements TeacherAchievementSubjectModel {
  const factory _TeacherAchievementSubjectModel(
      {final int? subjectType,
      final String? subjectName,
      final int? selectedType}) = _$_TeacherAchievementSubjectModel;

  factory _TeacherAchievementSubjectModel.fromJson(Map<String, dynamic> json) =
      _$_TeacherAchievementSubjectModel.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectName;
  @override
  int? get selectedType;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherAchievementSubjectModelCopyWith<_$_TeacherAchievementSubjectModel>
      get copyWith => throw _privateConstructorUsedError;
}

TeacherAchievementMedals _$TeacherAchievementMedalsFromJson(
    Map<String, dynamic> json) {
  return _TeacherAchievementMedals.fromJson(json);
}

/// @nodoc
mixin _$TeacherAchievementMedals {
  List<MedalGroup>? get medals => throw _privateConstructorUsedError;
  String? get commonRes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherAchievementMedalsCopyWith<TeacherAchievementMedals> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherAchievementMedalsCopyWith<$Res> {
  factory $TeacherAchievementMedalsCopyWith(TeacherAchievementMedals value,
          $Res Function(TeacherAchievementMedals) then) =
      _$TeacherAchievementMedalsCopyWithImpl<$Res, TeacherAchievementMedals>;
  @useResult
  $Res call({List<MedalGroup>? medals, String? commonRes});
}

/// @nodoc
class _$TeacherAchievementMedalsCopyWithImpl<$Res,
        $Val extends TeacherAchievementMedals>
    implements $TeacherAchievementMedalsCopyWith<$Res> {
  _$TeacherAchievementMedalsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medals = freezed,
    Object? commonRes = freezed,
  }) {
    return _then(_value.copyWith(
      medals: freezed == medals
          ? _value.medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherAchievementMedalsCopyWith<$Res>
    implements $TeacherAchievementMedalsCopyWith<$Res> {
  factory _$$_TeacherAchievementMedalsCopyWith(
          _$_TeacherAchievementMedals value,
          $Res Function(_$_TeacherAchievementMedals) then) =
      __$$_TeacherAchievementMedalsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<MedalGroup>? medals, String? commonRes});
}

/// @nodoc
class __$$_TeacherAchievementMedalsCopyWithImpl<$Res>
    extends _$TeacherAchievementMedalsCopyWithImpl<$Res,
        _$_TeacherAchievementMedals>
    implements _$$_TeacherAchievementMedalsCopyWith<$Res> {
  __$$_TeacherAchievementMedalsCopyWithImpl(_$_TeacherAchievementMedals _value,
      $Res Function(_$_TeacherAchievementMedals) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medals = freezed,
    Object? commonRes = freezed,
  }) {
    return _then(_$_TeacherAchievementMedals(
      medals: freezed == medals
          ? _value._medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<MedalGroup>?,
      commonRes: freezed == commonRes
          ? _value.commonRes
          : commonRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherAchievementMedals
    with DiagnosticableTreeMixin
    implements _TeacherAchievementMedals {
  const _$_TeacherAchievementMedals(
      {final List<MedalGroup>? medals, this.commonRes})
      : _medals = medals;

  factory _$_TeacherAchievementMedals.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherAchievementMedalsFromJson(json);

  final List<MedalGroup>? _medals;
  @override
  List<MedalGroup>? get medals {
    final value = _medals;
    if (value == null) return null;
    if (_medals is EqualUnmodifiableListView) return _medals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? commonRes;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherAchievementMedals(medals: $medals, commonRes: $commonRes)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherAchievementMedals'))
      ..add(DiagnosticsProperty('medals', medals))
      ..add(DiagnosticsProperty('commonRes', commonRes));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherAchievementMedals &&
            const DeepCollectionEquality().equals(other._medals, _medals) &&
            (identical(other.commonRes, commonRes) ||
                other.commonRes == commonRes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_medals), commonRes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherAchievementMedalsCopyWith<_$_TeacherAchievementMedals>
      get copyWith => __$$_TeacherAchievementMedalsCopyWithImpl<
          _$_TeacherAchievementMedals>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherAchievementMedalsToJson(
      this,
    );
  }
}

abstract class _TeacherAchievementMedals implements TeacherAchievementMedals {
  const factory _TeacherAchievementMedals(
      {final List<MedalGroup>? medals,
      final String? commonRes}) = _$_TeacherAchievementMedals;

  factory _TeacherAchievementMedals.fromJson(Map<String, dynamic> json) =
      _$_TeacherAchievementMedals.fromJson;

  @override
  List<MedalGroup>? get medals;
  @override
  String? get commonRes;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherAchievementMedalsCopyWith<_$_TeacherAchievementMedals>
      get copyWith => throw _privateConstructorUsedError;
}
