import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../model/achievements_model.dart';

part 'teacher_achievement_model.freezed.dart';
part 'teacher_achievement_model.g.dart';

@freezed
class TeacherAchievementModel with _$TeacherAchievementModel {
  const factory TeacherAchievementModel({
    // 学端分类
    List<TeacherAchievementSegmentModel>? segments,
    // 学科分类
    List<TeacherAchievementSubjectModel>? subjects,
  }) = _TeacherAchievementModel;

  factory TeacherAchievementModel.fromJson(Map<String, Object?> json) =>
      _$TeacherAchievementModelFromJson(json);
}

@freezed
class TeacherAchievementSegmentModel with _$TeacherAchievementSegmentModel {
  const factory TeacherAchievementSegmentModel({
    int? segmentCode,
    String? segmentName,
  }) = _TeacherAchievementSegmentModel;

  factory TeacherAchievementSegmentModel.fromJson(Map<String, Object?> json) =>
      _$TeacherAchievementSegmentModelFromJson(json);
}

@freezed
class TeacherAchievementSubjectModel with _$TeacherAchievementSubjectModel {
  const factory TeacherAchievementSubjectModel({
    int? subjectType,
    String? subjectName,
    int? selectedType,
  }) = _TeacherAchievementSubjectModel;

  factory TeacherAchievementSubjectModel.fromJson(Map<String, Object?> json) =>
      _$TeacherAchievementSubjectModelFromJson(json);
}

@freezed
class TeacherAchievementMedals with _$TeacherAchievementMedals {
  const factory TeacherAchievementMedals({
    List<MedalGroup>? medals,
    String? commonRes,
  }) = _TeacherAchievementMedals;

  factory TeacherAchievementMedals.fromJson(Map<String, Object?> json) =>
      _$TeacherAchievementMedalsFromJson(json);
}
