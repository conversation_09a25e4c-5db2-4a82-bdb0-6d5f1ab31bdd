// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teacher_achievement_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeacherAchievementModel _$$_TeacherAchievementModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherAchievementModel(
      segments: (json['segments'] as List<dynamic>?)
          ?.map((e) => TeacherAchievementSegmentModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
      subjects: (json['subjects'] as List<dynamic>?)
          ?.map((e) => TeacherAchievementSubjectModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeacherAchievementModelToJson(
        _$_TeacherAchievementModel instance) =>
    <String, dynamic>{
      'segments': instance.segments,
      'subjects': instance.subjects,
    };

_$_TeacherAchievementSegmentModel _$$_TeacherAchievementSegmentModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherAchievementSegmentModel(
      segmentCode: json['segmentCode'] as int?,
      segmentName: json['segmentName'] as String?,
    );

Map<String, dynamic> _$$_TeacherAchievementSegmentModelToJson(
        _$_TeacherAchievementSegmentModel instance) =>
    <String, dynamic>{
      'segmentCode': instance.segmentCode,
      'segmentName': instance.segmentName,
    };

_$_TeacherAchievementSubjectModel _$$_TeacherAchievementSubjectModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherAchievementSubjectModel(
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      selectedType: json['selectedType'] as int?,
    );

Map<String, dynamic> _$$_TeacherAchievementSubjectModelToJson(
        _$_TeacherAchievementSubjectModel instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'selectedType': instance.selectedType,
    };

_$_TeacherAchievementMedals _$$_TeacherAchievementMedalsFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherAchievementMedals(
      medals: (json['medals'] as List<dynamic>?)
          ?.map((e) => MedalGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      commonRes: json['commonRes'] as String?,
    );

Map<String, dynamic> _$$_TeacherAchievementMedalsToJson(
        _$_TeacherAchievementMedals instance) =>
    <String, dynamic>{
      'medals': instance.medals,
      'commonRes': instance.commonRes,
    };
