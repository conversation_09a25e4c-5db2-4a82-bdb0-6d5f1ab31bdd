import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/teacher_achievement_detail/page.dart';

import '../../model/medal_model.dart';
import '../../my_achievements/viewmodel/view_model.dart';
import 'controller.dart';
import 'state.dart';
import 'teacher_card_row.dart';

class TeacherAchievementsView extends StatefulWidget {
  const TeacherAchievementsView({super.key});

  @override
  State<TeacherAchievementsView> createState() =>
      _TeacherAchievementsViewState();
}

class _TeacherAchievementsViewState extends State<TeacherAchievementsView> {
  TeacherAchievementsController? get pageController =>
      mounted ? context.read<TeacherAchievementsController>() : null;

  TeacherAchievementsState get _state =>
      context.read<TeacherAchievementsController>().state;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: _state.items?.length,
      itemBuilder: (context, index) {
        MyAchievementsItem item = _state.items![index];
        return _buildListItem(item);
      },
    );
  }

  Widget _buildListItem(MyAchievementsItem item) {
    if (item is AchievementsRow) {
      // 勋章行
      return TeacherCardsRow(
        cards: item.cards,
        commonResZip: _state.commonRes ?? "",
        onItemTap: (card) {
          if (card.latestMedal != null) {
            _gotoMedalDetail(card.latestMedal);
          }
        },
      );
    } else {
      return Text('未知的列表项: $item');
    }
  }

  /// 跳转到勋章详情页
  _gotoMedalDetail(MedalModel? medal) {
    if (medal != null) {
      var groupMedal =
          context.read<TeacherAchievementsController>().getMedalGroup(medal);
      Navigator.push(context, MaterialPageRoute(builder: (context) {
        return TeacherAchievementDetailPage(
            medal, groupMedal, _state.commonRes ?? "");
      }));
    }
  }
}
