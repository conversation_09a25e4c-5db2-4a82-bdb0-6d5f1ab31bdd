import 'dart:io';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievements_model.dart';

import '../../../../utils/file_util.dart';
import '../../model/medal_model.dart';
import '../../my_achievements/viewmodel/view_model.dart';
import 'state.dart';
import '../../../../service/teacher_achievements_api.dart';
import '../model/teacher_achievement_model.dart';

class TeacherAchievementsController extends Cubit<TeacherAchievementsState> {
  static const String tag = "TeacherAchievementsController";
  final int subjectType;
  final int segmentCode;
  final TeacherAchievementsApi api;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  TeacherAchievementsController(
      {required this.subjectType, required this.segmentCode, required this.api})
      : super(TeacherAchievementsState(PageStatus.loading));

  TeacherAchievementsController.withDefault(
      {required this.subjectType, required this.segmentCode})
      : api = teacherAchievementsApi,
        super(TeacherAchievementsState(PageStatus.loading)) {
    refreshData();
  }

  List<List<MedalModel>> medalGroups = [];

  Future<void> refreshData() async {
    try {
      TeacherAchievementMedals model = await getMedals();
      if (model.medals == null || model.medals!.isEmpty) {
        updateState(TeacherAchievementsState(PageStatus.empty));
        return;
      }
      var groups = getMedalGroups(model);
      medalGroups = groups;
      List<MedalModel> medals = getAllMedals(medalGroups);

      _downloadZipRes(medals, model.commonRes ?? "", successListener: (urlMap) {
        List<MyAchievementsItem> items = getMedalCardRows(medals);
        final cards = getMedalCards(medals);
        updateState(TeacherAchievementsState(PageStatus.success,
            items: items,
            cards: cards,
            commonRes: urlMap[model.commonRes ?? ""]));
      }, failListener: (error) {
        updateState(TeacherAchievementsState(PageStatus.error,
            exception: Exception(error)));
      });
    } catch (e) {
      updateState(TeacherAchievementsState(PageStatus.error,
          exception: Exception(e.toString())));
    }
  }

  Future<TeacherAchievementMedals> getMedals() async {
    TeacherAchievementMedals model =
        await api.getMedals(subjectType, segmentCode);
    return model;
  }

  List<List<MedalModel>> getMedalGroups(TeacherAchievementMedals model) {
    final List<List<MedalModel>> groupMedals = [];
    for (MedalGroup medal in model.medals ?? []) {
      List<MedalModel> groups = [];
      for (MedalModel card in medal.groupMedals ?? []) {
        MedalModel newCard = card.copyWith(
            id: card.id,
            title: card.title,
            resource: card.resource,
            remark: card.remark,
            isGet: 1,
            isView: 1,
            progress: MedalProgress(total: 1, current: 1));
        groups.add(newCard);
      }
      groupMedals.add(groups);
    }
    return groupMedals;
  }

  List<MedalModel> getAllMedals(List<List<MedalModel>> groupMedals) {
    List<MedalModel> medals = [];
    for (List<MedalModel> group in groupMedals) {
      for (MedalModel card in group) {
        medals.add(card);
      }
    }
    return medals;
  }

  _downloadZipRes(List<MedalModel> medals, String commonResUrl,
      {Function(Map<String, String>)? successListener,
      Function(String)? failListener}) async {
    List<String> urlList = medals
        .map((medal) {
          return medal.resource?["flutterRes"] ?? "";
        })
        .where((url) => url.isNotEmpty)
        .toList();

    // 公共资源
    if (commonResUrl.isNotEmpty) {
      urlList.add(commonResUrl);
    }

    await _resourceManager.downloadUrl(urlList, isNeedCancel: false,
        successListener: (urlMap) async {
      for (String url in urlList) {
        String localPath = urlMap[url] ?? "";
        if (localPath.isNotEmpty) {
          l.i(tag,
              "_downloadZipRes start unzip url: $url localPath: $localPath");

          try {
            String dirPath = await unzip(localPath);
            // 删除多余文件
            await removeUselessFilesAndDirs(Directory(dirPath));

            if (Directory(dirPath).existsSync()) {
              l.i(tag,
                  "_downloadZipRes success url: $url localPath: $localPath");
            } else {
              l.e(tag, "_downloadZipRes fail url: $url localPath: $localPath");
            }
          } catch (e) {
            l.e(tag,
                "_downloadZipRes unzip fail url: $url localPath: $localPath error: $e");
          }
        } else {
          l.e(tag, "_downloadZipRes fail url: $url");
        }
      }
      successListener?.call(urlMap);
    }, failListener: (error) {
      l.e(tag, "_downloadZipRes fail error: $error");
      failListener?.call(error.message??"");
    });
  }

  List<MedalModel> getMedalGroup(MedalModel model) {
    for (List<MedalModel> group in medalGroups) {
      for (MedalModel medal in group) {
        if (medal.id == model.id) {
          return group;
        }
      }
    }
    return [];
  }

  Future<void> updateState(TeacherAchievementsState state) async {
    super.emit(state);
  }

  setPageStatus(PageStatus status) {
    updateState(state.copyWith(pageStatus: status));
  }

  @override
  close() async {
    super.close();
    _resourceManager.cancelDownload();
  }

  List<MyAchievementsItem> getMedalCardRows(List<MedalModel> medals) {
    List<MyAchievementsItem> items = [];

    // 先将所有 medal 分组转换为 AchievementCard 列表
    final achievementCards = medals
        .map<AchievementCard>((card) => AchievementCard(0, "", card, null))
        .toList();

    // 将 AchievementCard 列表分组为 AchievementsRow 列表，每 3 个一行
    // 之所以需要一行一行显示，是为了懒加载优化性能以及避免下载不可见区域的资源
    final rowCount = (achievementCards.length + 2) ~/ 3;
    final List<AchievementsRow> rows = List.generate(rowCount, (index) {
      final start = index * 3;
      final end = ((index + 1) * 3 > achievementCards.length)
          ? achievementCards.length
          : (index + 1) * 3;
      return AchievementsRow(achievementCards.sublist(start, end));
    });
    items.addAll(rows);

    return items;
  }

  List<AchievementCard> getMedalCards(List<MedalModel> medals) {
    return medals
        .map<AchievementCard>((card) => AchievementCard(0, "", card, null))
        .toList();
  }
}
