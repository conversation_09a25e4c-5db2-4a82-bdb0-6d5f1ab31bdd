import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import 'controller.dart';
import 'state.dart';
import 'view.dart';

class TeacherAchievementsPage extends BasePage {
  final int subjectType;
  final String subjectName;

  final int segmentCode;
  final String segmentName;

  const TeacherAchievementsPage(
      {super.key,
      required this.subjectType,
      required this.segmentCode,
      required this.subjectName,
      required this.segmentName});

  @override
  State<StatefulWidget> createState() => TeacherAchievementsPageState();
}

class TeacherAchievementsPageState extends BaseState<TeacherAchievementsPage> {
  final ValueNotifier<double> _appBarOpacityNotifier = ValueNotifier(0.0);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) {
        return TeacherAchievementsController.withDefault(
            subjectType: widget.subjectType, segmentCode: widget.segmentCode);
      },
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<TeacherAchievementsController,
              TeacherAchievementsState>(
            builder: (context, state) {
              return Scaffold(
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(
                    const JoJoAppBar().preferredSize.height,
                  ),
                  child: ValueListenableBuilder<double>(
                    valueListenable: _appBarOpacityNotifier,
                    builder: (context, opacity, child) {
                      return JoJoAppBar(
                        title: '${widget.subjectName} ${widget.segmentName}',
                        backgroundColor: Color.fromRGBO(255, 255, 255, opacity),
                      );
                    },
                  ),
                ),
                body: WillPopScope(
                  onWillPop: () async {
                    return false;
                  },
                  child: JoJoPageLoadingV25(
                      scene: PageScene.common,
                      exception: state.exception,
                      hideProgress: true,
                      status: state.pageStatus,
                      child: const TeacherAchievementsView(),
                      retry: () {
                        context
                            .read<TeacherAchievementsController>()
                            .setPageStatus(PageStatus.loading);
                        context
                            .read<TeacherAchievementsController>()
                            .refreshData();
                      }),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
