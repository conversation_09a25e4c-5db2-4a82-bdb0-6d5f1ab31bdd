import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import '../model/teacher_achievement_model.dart';
import '../../model/medal_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';

class TeacherAchievementsState {
  final PageStatus pageStatus;
  final Exception? exception;
  final List<MyAchievementsItem>? items;
  final List<AchievementCard>? cards;
  final String? commonRes;

  TeacherAchievementsState(
    this.pageStatus, {
    this.exception,
    this.items = const [],
    this.cards = const [],
    this.commonRes = "",
  });

  TeacherAchievementsState copyWith({
    PageStatus? pageStatus,
    Exception? exception,
    List<MyAchievementsItem>? items,
    List<AchievementCard>? cards,
    String? commonRes,
  }) {
    return TeacherAchievementsState(
      pageStatus ?? this.pageStatus,
      exception: exception ?? this.exception,
      items: items ?? this.items,
      cards: cards ?? this.cards,
      commonRes: commonRes ?? this.commonRes,
    );
  }
}
