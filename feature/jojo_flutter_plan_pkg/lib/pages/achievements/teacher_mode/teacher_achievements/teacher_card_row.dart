import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import '../../my_achievements/viewmodel/card_controller.dart';
import '../../my_achievements/viewmodel/card_state.dart';
import '../../my_achievements/viewmodel/view_model.dart';
import '../../widget/achievement_card_wapper.dart';

class TeacherCardsRow extends StatefulWidget {
  final List<AchievementCard> cards;
  final Function(AchievementCard card) onItemTap;
  final String commonResZip;

  const TeacherCardsRow({
    Key? key,
    required this.cards,
    required this.onItemTap,
    required this.commonResZip,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TeacherCardsRowState();
}

class _TeacherCardsRowState extends State<TeacherCardsRow> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // 为解决浮点计算导致的宽度溢出，减去1像素后做计算
        final double maxWidth = constraints.maxWidth - 1;
        final double cardWidth = 100.rdp;
        // final double cardWidth = maxWidth;
        final double horizontalSpacing = 16.rdp;

        // 根据屏幕宽度和卡片尺寸（含间距），计算每行最多能容纳的卡片数量
        int maxCardsPerRow =
            ((maxWidth - horizontalSpacing) / (cardWidth + horizontalSpacing))
                .floor();
        if (maxCardsPerRow < 1) maxCardsPerRow = 1;

        // 满行时所有卡片所占宽度（包含卡片之间的间距）
        final double fullRowWidth = maxCardsPerRow * cardWidth +
            (maxCardsPerRow - 1) * horizontalSpacing;

        // 计算固定的左右内边距，使满行卡片居中
        double horizontalPadding = max((maxWidth - fullRowWidth) / 2, 0.0);

        return Container(
          // 固定左右 padding，即使某行元素不足，也从同一水平起点开始排列
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Wrap(
              alignment: WrapAlignment.start, // 强制在 Container 内左对齐
              spacing: horizontalSpacing,
              runSpacing: horizontalSpacing,
              children:
                  _buildCards(widget.cards, cardWidth, horizontalSpacing)),
        );
      },
    );
  }

  List<Widget> _buildCards(
      List<AchievementCard> cards, double cardWidth, double spacing) {
    List<Widget> widgets = cards.map<Widget>((card) {
      return BlocProvider(
        create: (_) =>
            CardController(card, widget.commonResZip, enablePerfMode: true),
        child: BlocBuilder<CardController, CardState>(
          builder: (context, state) {
            return GestureDetector(
                onTap: () {
                  widget.onItemTap(card);
                },
                child: AchievementCardWrapper(
                    width: cardWidth,
                    height: state.needShowProgress
                        ? cardWidth.rdp * 1.114
                        : cardWidth.rdp));
          },
        ),
      );
    }).toList();
    widgets.add(Container());
    return widgets;
  }
}
