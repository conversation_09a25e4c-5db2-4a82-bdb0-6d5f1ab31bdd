import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import 'state.dart';
import '../../../../service/teacher_achievements_api.dart';
import '../model/teacher_achievement_model.dart';

class TeacherAchievementTypesController
    extends Cubit<TeacherAchievementTypesState> {
  final int subjectType;
  final int? partnerId;
  final TeacherAchievementsApi api;

  // 学科分类
  List<TeacherAchievementSubjectModel>? subjects;
  // 学段分类
  Map<int, List<TeacherAchievementSegmentModel>> segmentForSubjects = {};

  TeacherAchievementTypesController(
      {required this.subjectType, required this.api, this.partnerId})
      : super(TeacherAchievementTypesState(PageStatus.loading));

  TeacherAchievementTypesController.withDefault(
      {required this.subjectType, this.partnerId})
      : api = teacherAchievementsApi,
        super(TeacherAchievementTypesState(PageStatus.loading)) {
    getSegmentData(2);
  }

  Future<void> getSegmentData(int subjectType) async {
    try {
      TeacherAchievementModel model = await api.getSegments(subjectType);

      subjects = model.subjects;
      var subject =
          model.subjects?.firstWhere((element) => element.selectedType == 1);
      var subjectTypeKey = subject?.subjectType;
      if (subjectTypeKey != null) {
        segmentForSubjects[subjectType] = model.segments ?? [];
      }

      updateState(TeacherAchievementTypesState(PageStatus.success,
          segmentForSubjects: segmentForSubjects, subjects: subjects));
    } catch (e) {
      updateState(TeacherAchievementTypesState(PageStatus.error,
          exception: Exception(e.toString())));
    }
  }

  Future<void> updateState(TeacherAchievementTypesState state) async {
    super.emit(state);
  }

  setPageStatus(PageStatus status) {
    updateState(state.copyWith(pageStatus: status));
  }
}
