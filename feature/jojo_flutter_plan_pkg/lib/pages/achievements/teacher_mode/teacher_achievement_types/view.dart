import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_path.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import '../model/teacher_achievement_model.dart';

import '../teacher_achievements/page.dart';
import 'controller.dart';
import 'state.dart';

class TeacherAchievementTypesView extends StatelessWidget {
  final TeacherAchievementTypesState state;

  const TeacherAchievementTypesView({Key? key, required this.state})
      : super(key: key);

  Widget getTitle(
      BuildContext context, TeacherAchievementSubjectModel subject) {
    return Container(
      constraints:
          const BoxConstraints(maxWidth: double.infinity, maxHeight: 58.0),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.black45, width: 0.5)),
      ),
      alignment: Alignment.centerLeft,
      child: MaterialButton(
          onPressed: () {
            context
                .read<TeacherAchievementTypesController>()
                .setPageStatus(PageStatus.loading);
            context
                .read<TeacherAchievementTypesController>()
                .getSegmentData(subject.subjectType ?? 2);
          },
          child: Row(
            children: [
              Text(
                subject.subjectName ?? '??',
                style: const TextStyle(fontSize: 18, color: Colors.black87),
              )
            ],
          )),
    );
  }

  Widget getButton(BuildContext context, TeacherAchievementSubjectModel subject,
      TeacherAchievementSegmentModel model) {
    return Container(
      padding: const EdgeInsets.all(0),
      width: 80,
      height: 38,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          color: Colors.black45,
          width: 1,
        ),
      ),
      child: MaterialButton(
        padding: const EdgeInsets.all(0),
        onPressed: () {
          Navigator.push(context, MaterialPageRoute(builder: (context) {
            return TeacherAchievementsPage(
              subjectType: subject.subjectType ?? 2,
              subjectName: subject.subjectName ?? '',
              segmentCode: model.segmentCode ?? 0,
              segmentName: model.segmentName ?? '',
            );
          }));
        },
        child: Text(
          model.segmentName ?? '??',
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
      ),
    );
  }

  List<Widget> getTypeButtons(BuildContext context) {
    var buttons = <Widget>[];

    state.subjects?.forEach((subject) {
      buttons.add(getTitle(context, subject));
      var subjectType = subject.subjectType;
      var segmentForSubjects = state.segmentForSubjects;
      if (subjectType != null &&
          segmentForSubjects != null &&
          segmentForSubjects.containsKey(subjectType)) {
        for (var segment in (segmentForSubjects[subjectType] ?? [])) {
          buttons.add(getButton(context, subject, segment));
        }
      }
    });

    return buttons;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Wrap(
          spacing: 16,
          runSpacing: 16,
          children: getTypeButtons(context),
        ),
      ),
    );
  }
}
