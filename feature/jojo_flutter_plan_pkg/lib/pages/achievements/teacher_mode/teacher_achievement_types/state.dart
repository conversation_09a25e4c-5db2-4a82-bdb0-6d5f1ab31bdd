import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import '../model/teacher_achievement_model.dart';

class TeacherAchievementTypesState {
  final PageStatus pageStatus;
  final Exception? exception;

  // 学科分类
  List<TeacherAchievementSubjectModel>? subjects;

  // 学端分类
  Map<int, List<TeacherAchievementSegmentModel>>? segmentForSubjects;

  TeacherAchievementTypesState(
    this.pageStatus, {
    this.exception,
    this.segmentForSubjects,
    this.subjects,
  });

  TeacherAchievementTypesState copyWith({
    PageStatus? pageStatus,
    List<TeacherAchievementSubjectModel>? subjects,
    Map<int, List<TeacherAchievementSegmentModel>>? segmentForSubjects,
    Exception? exception,
  }) {
    return TeacherAchievementTypesState(
      pageStatus ?? this.pageStatus,
      subjects: subjects ?? this.subjects,
      segmentForSubjects: segmentForSubjects ?? this.segmentForSubjects,
      exception: exception ?? this.exception,
    );
  }
}
