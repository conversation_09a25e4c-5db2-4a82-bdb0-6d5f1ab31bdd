import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../../../generated/l10n.dart';
import 'controller.dart';
import 'state.dart';
import 'view.dart';

class TeacherAchievementTypesPage extends BasePage {
  final int? subjectType;
  final int? partnerId;
  const TeacherAchievementTypesPage(
      {super.key, this.subjectType, this.partnerId});

  @override
  State<StatefulWidget> createState() => TeacherAchievementTypesPageState();
}

class TeacherAchievementTypesPageState
    extends BaseState<TeacherAchievementTypesPage> {
  final ValueNotifier<double> _appBarOpacityNotifier = ValueNotifier(0.0);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _appBarOpacityNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) {
        return TeacherAchievementTypesController.withDefault(
            subjectType: widget.subjectType ?? 2, partnerId: widget.partnerId);
      },
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<TeacherAchievementTypesController,
              TeacherAchievementTypesState>(
            builder: (context, state) {
              return Scaffold(
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(
                    const JoJoAppBar().preferredSize.height,
                  ),
                  child: ValueListenableBuilder<double>(
                    valueListenable: _appBarOpacityNotifier,
                    builder: (context, opacity, child) {
                      return JoJoAppBar(
                        title: "选择学段",
                        backgroundColor: Color.fromRGBO(255, 255, 255, opacity),
                      );
                    },
                  ),
                ),
                body: WillPopScope(
                  onWillPop: () async {
                    return false;
                  },
                  child: JoJoPageLoadingV25(
                      scene: PageScene.common,
                      exception: state.exception,
                      hideProgress: true,
                      status: state.pageStatus,
                      child: TeacherAchievementTypesView(
                        state: state,
                      ),
                      retry: () {
                        context
                            .read<TeacherAchievementTypesController>()
                            .setPageStatus(PageStatus.loading);
                        context
                            .read<TeacherAchievementTypesController>()
                            .getSegmentData(2);
                      }),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
