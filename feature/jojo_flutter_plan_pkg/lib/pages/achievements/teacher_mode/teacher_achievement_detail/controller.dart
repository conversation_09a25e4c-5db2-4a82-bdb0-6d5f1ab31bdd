import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../../../model/aigc_medias.dart';
import '../../../../service/achievements_api.dart';
import '../../../../service/aigc_medias_api.dart';
import '../../../../utils/file_util.dart';
import '../../model/achievement_detail_model.dart';
import '../../model/medal_model.dart';
import 'state.dart';

class TeacherAchievementDetailController
    extends Cubit<TeacherAchievementDetailState> {
  static const String tag = "AchievementDetailController";

  final AchievementsApi dao;
  final MedalModel selectedMedal;
  final List<MedalModel> medals;
  final String commonResZip;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  TeacherAchievementDetailController({
    required this.selectedMedal,
    required this.medals,
    required this.commonResZip,
    required this.dao,
  }) : super(TeacherAchievementDetailState(PageStatus.loading));

  TeacherAchievementDetailController.withDefault(
      {required this.selectedMedal,
      required this.medals,
      required this.commonResZip})
      : dao = proAchievementsApi,
        super(TeacherAchievementDetailState(PageStatus.loading)) {
    refreshData();
  }

  setPageStatus(PageStatus status) {
    emit(TeacherAchievementDetailState(status));
  }

  refreshData() {
    var index = medals.indexOf(selectedMedal);
    updateDisplayMedalAtIndex(index);
  }

  updateDisplayMedalAtIndex(int medalIndex) {
    if (medalIndex >= medals.length) {
      medalIndex = 0;
    }
    if (medalIndex < 0) {
      medalIndex = medals.length - 1;
    }

    List<String> mediaPaths = [];
    String localMediaPath = "";

    var displayMedal = medals[medalIndex];
    final isRare =
        displayMedal.tags?.any((element) => element.name == "稀有") ?? false;
    final randomNum = Random().nextInt(4) + 1;
    localMediaPath = isRare
        ? "assets/audio/medal_audios/medal_special_00$randomNum.mp3"
        : "assets/audio/medal_audios/medal_general_00$randomNum.mp3";

    emit(TeacherAchievementDetailState(
      PageStatus.success,
      model: null,
      commonResZip: commonResZip,
      aiMediaPaths: mediaPaths,
      localMediaPath: localMediaPath,
      initialIndex: medalIndex,
      medals: medals,
    ));
  }

  /// 获取 AIGC 语音，并将多个下载任务合并为一次下载
  Future<List<String>> _tryGetAigcMedia(MedalModel nextStageMedal) async {
    try {
      final AigcMedias aigcMedias = await aigcMediasApi.getSubjectMedals([
        {
          "sceneKey": "MEDAL_REMARK",
          "sceneParams": {
            "medalKey": "${nextStageMedal.id ?? 0}",
            "MedalName": nextStageMedal.title ?? "",
          }
        }
      ]);

      // 收集所有有效的 URL
      final List<String> urls = [];
      for (final sceneResult in (aigcMedias.sceneResults ?? [])) {
        for (final media in (sceneResult.medias ?? [])) {
          if ((media.url ?? "").isNotEmpty) {
            urls.add(media.url!);
          }
        }
      }
      if (urls.isEmpty) {
        return [];
      }

      final Completer<List<String>> completer = Completer<List<String>>();
      final List<String> paths = List.generate(urls.length, (index) => "");

      await _resourceManager.downloadUrl(
        urls,
        isNeedCancel: false,
        successListener: (urlMap) {
          for (int i = 0; i < urls.length; i++) {
            final url = urls[i];
            final path = urlMap[url] ?? "";
            paths[i] = path;
          }
          if (!completer.isCompleted) completer.complete(paths);
        },
        failListener: (error) {
          if (!completer.isCompleted) completer.complete([]);
        },
      );

      return completer.future;
    } catch (e) {
      return [];
    }
  }

  /// 下载奖章资源的压缩包及公共资源
  Future<void> _downloadZipRes(
    List<MedalModel> medals,
    String commonResUrl, {
    Function(Map<String, String>)? successListener,
    Function(String)? failListener,
  }) async {
    // 收集奖章资源 URL
    final List<String> urlList = medals
        .map((medal) => medal.resource?["flutterRes"] ?? "")
        .where((url) => url.isNotEmpty)
        .toList();

    // 添加公共资源 URL
    if (commonResUrl.isNotEmpty) {
      urlList.add(commonResUrl);
    }

    await _resourceManager.downloadUrl(
      urlList,
      isNeedCancel: false,
      successListener: (urlMap) async {
        for (final url in urlList) {
          final String localPath = urlMap[url] ?? "";
          if (localPath.isNotEmpty) {
            final String dirPath = await unzip(localPath);
            if (Directory(dirPath).existsSync()) {
              l.i(tag,
                  "_downloadZipRes success url: $url localPath: $localPath");
            } else {
              l.i(tag, "_downloadZipRes fail url: $url localPath: $localPath");
            }
          } else {
            l.i(tag, "_downloadZipRes fail url: $url");
          }
        }
        if (successListener != null) {
          successListener(urlMap);
        }
      },
      failListener: (error) {
        l.i(tag, "_downloadZipRes fail error: $error");
        if (failListener != null) {
          failListener.call(error.message ?? "");
        }
      },
    );
  }
}
