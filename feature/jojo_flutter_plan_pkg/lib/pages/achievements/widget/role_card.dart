import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';

import '../../../../utils/file_util.dart';
import '../../../common/config/config.dart';
import '../../../static/spine.dart';
import '../my_achievements/viewmodel/card_controller.dart';
import '../my_achievements/viewmodel/card_state.dart';

/// 角色卡片
class RoleCard extends StatefulWidget {
  const RoleCard({super.key});

  @override
  State<RoleCard> createState() => RoleCardState();
}

class RoleCardState extends State<RoleCard> {
  final String tag = "RoleCard";
  late JoJoSpineAnimationController _bgSpineController;
  late JoJoSpineAnimationController _roleSpineController;
  late JoJoSpineAnimationController _titleSpineController;

  CardState get _state => context.read<CardController>().state;

  @override
  void initState() {
    super.initState();
    _bgSpineController = JoJoSpineAnimationController();
    _roleSpineController = JoJoSpineAnimationController();
    _titleSpineController = JoJoSpineAnimationController();
  }

  @override
  void dispose() {
    _bgSpineController.dispose();
    _roleSpineController.dispose();
    _titleSpineController.dispose();
    super.dispose();
  }

  /// 封装 Spine 动画 Widget 构建的公共逻辑
  Widget _buildSpineWidget({
    required String atlasFile,
    required String skelFile,
    required JoJoSpineAnimationController controller,
    required String animationName,
    bool loop = false,
    int trackIndex = 0,
    Function(AnimationEventType type)? listener,
  }) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      final double maxHeight = constraints.maxHeight;

      return JoJoSpineAnimationWidget(
        atlasFile,
        skelFile,
        LoadMode.file,
        controller,
        useRootBoneAlign: true,
        fit: BoxFit.none,
        onInitialized: (spineController) {
          if (mounted) {
            // 设置缩放比例
            spineController.skeleton.setScaleX(maxHeight / 500);
            spineController.skeleton.setScaleY(maxHeight / 500);

            controller.playAnimation(JoJoSpineAnimation(
              animaitonName: animationName,
              trackIndex: trackIndex,
              loop: loop,
              listener: (type) {
                if (type == AnimationEventType.start && !_state.playing) {
                  controller.pauseAnimation();
                }
                listener?.call(type);
              },
            ));
          }
        },
      );
    });
  }

  /// 根据传入的 [patterns] 列表查找文件，返回第一个匹配的文件路径
  String _findFile(String directory, List<String> patterns) {
    for (final pattern in patterns) {
      try {
        final file = findFilesByEnds(directory, pattern);
        if (file != null) return file.path;
      } catch (e) {
        // 忽略错误
      }
    }
    return "";
  }

  String _findBgImagePath(String directoryPath, String suffix) {
    if (suffix.isEmpty) return "";
    return _findFile(directoryPath, ["img/${suffix}_bg.png"]);
  }

  String _findTitleImagePath(String directoryPath, String suffix) {
    if (suffix.isEmpty) return "";
    return _findFile(directoryPath, ["nameplate/${suffix}_title.png"]);
  }

  String _findTitleAtlasPath(String directoryPath, String prefix) {
    if (prefix.isEmpty) return "";
    return _findFile(directoryPath, ["card/${prefix}_title.atlas.txt"]);
  }

  String _findTitleSkelPath(String directoryPath, String prefix) {
    if (prefix.isEmpty) return "";
    return _findFile(directoryPath,
        ["card/${prefix}_title.skel.txt", "card/${prefix}_title.skel.bytes"]);
  }

  String _findBgAtlasPath(String directoryPath, String prefix) {
    if (prefix.isEmpty) return "";
    return _findFile(directoryPath, ["card/${prefix}_bg.atlas.txt"]);
  }

  String _findBgSkelPath(String directoryPath, String prefix) {
    if (prefix.isEmpty) return "";
    return _findFile(directoryPath,
        ["card/${prefix}_bg.skel.txt", "card/${prefix}_bg.skel.bytes"]);
  }

  String _findAtlasPath(String directoryPath) {
    return _findFile(directoryPath, ["atlas.txt"]);
  }

  String _findSkelPath(String directoryPath) {
    return _findFile(directoryPath, ["skel.txt", "skel.bytes"]);
  }

  @override
  Widget build(BuildContext context) {
    final String roleAtlasFile = _findAtlasPath(_state.spineDirectory);
    final String roleSkelFile = _findSkelPath(_state.spineDirectory);

    return Container(
      child: _state.isGet
          ? _buildRoleSpine(roleAtlasFile, roleSkelFile)
          : _buildLockSpine(roleAtlasFile, roleSkelFile),
    );
  }

  /// 锁定状态下的角色动画
  Widget _buildLockSpine(String roleAtlasFile, String roleSkelFile) {
    _fileNotFoundLogs([
      roleAtlasFile,
      roleSkelFile,
    ]);

    return _buildSpineWidget(
        atlasFile: roleAtlasFile,
        skelFile: roleSkelFile,
        controller: _roleSpineController,
        animationName: "unlock",
        loop: false);
  }

  /// 解锁状态下的角色卡片动画，包括背景、角色、标题图片、标题文本和标题特效
  Widget _buildRoleSpine(String roleAtlasFile, String roleSkelFile) {
    final String medalResType = _state.spineInfo?.medalResType ?? "";
    final String bgAtlasFile =
        _findBgAtlasPath(_state.commonResDirectory, medalResType);
    final String bgSkelFile =
        _findBgSkelPath(_state.commonResDirectory, medalResType);
    final String titleAtlasFile =
        _findTitleAtlasPath(_state.commonResDirectory, medalResType);
    final String titleSkelFile =
        _findTitleSkelPath(_state.commonResDirectory, medalResType);
    final String titleImagePath =
        _findTitleImagePath(_state.commonResDirectory, medalResType);
    final String bgImagePath =
        _findBgImagePath(_state.commonResDirectory, medalResType);

    _fileNotFoundLogs([
      roleAtlasFile,
      roleSkelFile,
      bgAtlasFile,
      bgSkelFile,
      titleAtlasFile,
      titleSkelFile,
      titleImagePath,
      bgImagePath,
    ]);

    return LayoutBuilder(builder: (context, constraints) {
      final double maxHeight = constraints.maxHeight;
      final double maxWidth = constraints.maxWidth;
      double textSize = maxHeight * 0.07;
      double textBottom = 0.0;
      // 使用屏幕宽度的百分比作为左右 padding
      final double padding = constraints.maxWidth * 0.12;

      final double titleBgWidth = maxWidth - padding * 2; // 文字背景图的宽
      double titleBgHeight =
          titleBgWidth * 500 / 378; // 文字背景图的高，背景图比例固定 378:500
      titleBgHeight = min(maxHeight, titleBgHeight); // 背景图高度不能超过卡片高度

      /// scale : 文字背景位置在整张图中的比例
      double _getTextBottom(double scale) {
        double offset = max((maxHeight - titleBgHeight) / 2, 0);
        return titleBgHeight * scale + offset - textSize / 2;
      }

      if (medalResType.startsWith("l")) {
        textBottom = _getTextBottom(0.23);
      } else if (medalResType.startsWith("m")) {
        textBottom = _getTextBottom(0.19);
      } else if (medalResType.startsWith("h")) {
        textBottom = _getTextBottom(0.19);
      } else {
        textBottom = _getTextBottom(0.19);
        l.e(tag, "unknown medalResType: $medalResType");
      }

      return Stack(
        children: [
          if (context.read<CardController>().enablePerfMode)
            Positioned.fill(
              child: Image.file(
                File(bgImagePath),
                fit: BoxFit.contain,
              ),
            ),
          if (!context.read<CardController>().enablePerfMode)
            // 背景 Spine 动画
            _buildSpineWidget(
              atlasFile: bgAtlasFile,
              skelFile: bgSkelFile,
              controller: _bgSpineController,
              animationName: "loop",
              loop: true,
            ),
          // 角色 Spine 动画
          _buildSpineWidget(
            atlasFile: roleAtlasFile,
            skelFile: roleSkelFile,
            controller: _roleSpineController,
            animationName: "in",
            loop: false,
            listener: (AnimationEventType type) {
              if (type == AnimationEventType.complete) {
                _roleSpineController.playAnimation(JoJoSpineAnimation(
                  animaitonName: "loop",
                  trackIndex: 0,
                  loop: true,
                ));
              }
            },
          ),
          // 标题图片
          Positioned.fill(
            left: padding,
            right: padding,
            child: Image.file(
              File(titleImagePath),
              fit: BoxFit.contain,
            ),
          ),
          // 居中的标题文本
          Positioned(
            bottom: textBottom,
            left: 0,
            right: 0,
            child: Center(
              child: Text(
                _state.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: HexColor(_state.spineInfo?.textColor ?? ""),
                  fontSize: textSize,
                  fontWeight: FontWeight.bold,
                  height: 1,
                ),
              ),
            ),
          ),
          if (!context.read<CardController>().enablePerfMode)
            // 标题 Spine 特效
            _buildSpineWidget(
              atlasFile: titleAtlasFile,
              skelFile: titleSkelFile,
              controller: _titleSpineController,
              animationName: "loop",
              loop: true,
            ),
        ],
      );
    });
  }

  void _fileNotFoundLogs(List<String> files) {
    for (final String filePath in files) {
      File(filePath).exists().then((exists) {
        if (!exists) l.w(tag, "file not found: $filePath");
      });
    }
  }
}
