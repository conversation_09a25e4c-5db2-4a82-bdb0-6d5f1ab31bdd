import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';

import '../../../../utils/file_util.dart';
import '../my_achievements/viewmodel/card_controller.dart';
import '../my_achievements/viewmodel/card_state.dart';

class MedalCard extends StatefulWidget {
  const MedalCard({super.key});

  @override
  State<MedalCard> createState() => MedalCardState();
}

class MedalCardState extends State<MedalCard> {
  final String tag = "MedalCard";
  late JoJoSpineAnimationController _spineController;

  CardState get _state => context.read<CardController>().state;

  @override
  void initState() {
    super.initState();
    _spineController = JoJoSpineAnimationController();
  }

  @override
  void dispose() {
    super.dispose();
    _spineController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final String atlasFile = _findAtlasPath(_state.spineDirectory);
    final String skelFile = _findSkelPath(_state.spineDirectory);

    _fileNotFoundLogs([
      atlasFile,
      skelFile,
    ]);

    return LayoutBuilder(
      builder: (context, constraints) {

        return Container(
          child: _state.isGet
              ? _buildMedalSpine(atlasFile, skelFile)
              : _buildLockSpine(atlasFile, skelFile),
        );
      },
    );
  }

  Widget _buildLockSpine(String atlasFile, String skelFile) {
    return _buildSpineWidget(
      atlasFile: atlasFile,
      skelFile: skelFile,
      controller: _spineController,
      animationName: "unlock",
      loop: false,
      trackIndex: 0,
    );
  }

  Widget _buildMedalSpine(String atlasFile, String skelFile) {
    return _buildSpineWidget(
      atlasFile: atlasFile,
      skelFile: skelFile,
      controller: _spineController,
      animationName: "loop",
      loop: true,
      trackIndex: 0,
    );
  }

  /// 封装 Spine 动画 Widget 构建的公共逻辑
  Widget _buildSpineWidget({
    required String atlasFile,
    required String skelFile,
    required JoJoSpineAnimationController controller,
    required String animationName,
    bool loop = false,
    int trackIndex = 0,
    Function(AnimationEventType type)? listener,
  }) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final double maxHeight = constraints.maxHeight;

          return JoJoSpineAnimationWidget(
            atlasFile,
            skelFile,
            LoadMode.file,
            controller,
            useRootBoneAlign: true,
            fit: BoxFit.none,
            onInitialized: (spineController) {
              if (mounted) {
                // 设置缩放比例
                spineController.skeleton.setScaleX(maxHeight/500);
                spineController.skeleton.setScaleY(maxHeight/500);

                controller.playAnimation(JoJoSpineAnimation(
                  animaitonName: animationName,
                  trackIndex: trackIndex,
                  loop: loop,
                  listener: (type) {
                    if (type == AnimationEventType.start && !_state.playing) {
                      controller.pauseAnimation();
                    }
                    listener?.call(type);
                  },
                ));
              }
            },
          );
        }
    );
  }

  String _findAtlasPath(String directoryPath) {
    return _findFile(directoryPath, ["atlas.txt"]);
  }

  String _findSkelPath(String directoryPath) {
    return _findFile(directoryPath, ["skel.txt", "skel.bytes"]);
  }

  /// 根据传入的 [patterns] 列表查找文件，返回第一个匹配的文件路径
  String _findFile(String directory, List<String> patterns) {
    for (final pattern in patterns) {
      try {
        final file = findFilesByEnds(directory, pattern);
        if (file != null) return file.path;
      } catch (e) {
        // 忽略错误
      }
    }
    return "";
  }

  void _fileNotFoundLogs(List<String> files) {
    for (final String filePath in files) {
      File(filePath).exists().then((exists) {
        if (!exists) l.w(tag, "file not found: $filePath");
      });
    }
  }
}
