import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

class NormalMedalTag extends StatelessWidget {
  /// 标题
  final String title;

  const NormalMedalTag(this.title, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
      decoration: BoxDecoration(
        color: context.appColors.jColorYellow4,
        borderRadius: BorderRadius.circular(4.rdp),
        border: Border.all(
          color: context.appColors.jColorYellow5,
          width: 1.rdp,
        ),
      ),
      child: Text(
        title,
        style: context.textstyles.remarkEmphasis.pf.copyWith(
          color: context.appColors.jColorYellow6,
        )
      ),
    );
  }
}

class RareMedalTag extends StatelessWidget {
  /// 标题
  final String title;

  const RareMedalTag(this.title, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(1.rdp),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Color(0xFF0090D9),
            Color(0xFF4C2978),
          ],
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFF33BBFF),
              Color(0xFFAE84E3),
            ],
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          title,
          style: context.textstyles.remarkEmphasis.pf.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ),
    );
  }
}