import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../model/achievement_detail_model.dart';
import '../model/medal_model.dart';

class AchievementDetailState {
  final PageStatus status;
  final Exception? exception;

  final int initialIndex;
  final String commonResZip;
  final List<String> aiMediaPaths;
  final String localMediaPath;
  final List<MedalModel> medals;
  final AchievementDetailModel? model;

  AchievementDetailState(this.status,
      {this.exception,
      this.initialIndex = 0,
      this.commonResZip = "",
      this.aiMediaPaths = const [],
      this.localMediaPath = "",
      this.model,
      this.medals = const []});
}
