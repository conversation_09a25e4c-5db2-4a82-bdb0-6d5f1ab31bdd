import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/widget/medal_tag.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/carousel_indicator.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/simple_progress_bar.dart';

import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';
import '../../plan_home/model/subject_type.dart';
import '../../plan_home/utils/ext.dart';
import '../model/medal_model.dart';
import '../my_achievements/viewmodel/card_controller.dart';
import '../my_achievements/viewmodel/card_state.dart';
import '../my_achievements/viewmodel/view_model.dart';
import '../share_achievement/view.dart';
import '../widget/achievement_card_wapper.dart';
import 'controller.dart';

class AchievementDetailView extends StatefulWidget {
  final AudioPlayer audioPlayer;

  const AchievementDetailView({Key? key, required this.audioPlayer})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => AchievementDetailViewState();
}

class AchievementDetailViewState extends State<AchievementDetailView> {
  late AchievementDetailState _state;
  late ValueNotifier<int> _indicatorController;

  /// 播放音频相关的变量
  int _currentAudioIndex = 0;
  StreamSubscription<void>? _audioCompletionSubscription;

  @override
  void initState() {
    super.initState();
    _state = context.read<AchievementDetailController>().state;
    _indicatorController = ValueNotifier<int>(_state.initialIndex);

    if (_state.aiMediaPaths.isNotEmpty) {
      _playAiAudios();
    } else if (_state.localMediaPath.isNotEmpty) {
      _playLocalAudios();
    }

    // 奖章详情页曝光
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_state.medals.isNotEmpty) {
        RunEnv.sensorsTrack(
            '\$AppViewScreen',
            _sensorsTrackData(
              medalId: "${_state.medals[_state.initialIndex].id ?? 0}",
              medalName: _state.medals[_state.initialIndex].title ?? "",
            ));
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _indicatorController.dispose();
    _audioCompletionSubscription?.cancel();
  }

  _playAiAudios() {
    if (_state.aiMediaPaths.isNotEmpty) {
      _currentAudioIndex = 0;
      widget.audioPlayer
          .play(DeviceFileSource(_state.aiMediaPaths[_currentAudioIndex]));
      _audioCompletionSubscription =
          widget.audioPlayer.onPlayerComplete.listen((event) {
        _currentAudioIndex++;
        if (_currentAudioIndex < _state.aiMediaPaths.length) {
          widget.audioPlayer
              .play(DeviceFileSource(_state.aiMediaPaths[_currentAudioIndex]));
        }
      });
    }
  }

  _playLocalAudios() {
    if (_state.localMediaPath.isNotEmpty) {
      String? package = RunEnv.package;
      String keyName = package == null
          ? _state.localMediaPath
          : 'packages/$package/${_state.localMediaPath}';

      widget.audioPlayer.audioCache.prefix = '';
      widget.audioPlayer.play(AssetSource(keyName));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Positioned(
        //   bottom: 78.rdp,
        //   left: 0,
        //   right: 0,
        //   child: _buildCarouselIndicator(),
        // ),
        Positioned.fill(child: _buildPage()),
      ],
    );
  }

  Widget _buildCarouselIndicator() {
    return CarouselIndicator(
        count: _state.medals.length,
        itemSize: 6.rdp,
        spacing: 8.rdp,
        controller: _indicatorController);
  }

  Widget _buildPage() {
    return BlocProvider(
      create: (_) {
        MedalModel medal = _state.medals[_state.initialIndex];
        AchievementCard card = AchievementCard(
            _state.model?.medalGroup?.upgrade ?? 0,
            _state.model?.medalGroup?.tips ?? "",
            medal.isGet == 1 ? medal : null,
            medal.isGet == 0 ? medal : null);
        var ctrl = CardController(card, _state.commonResZip);

        ctrl.shouldUpdateState = (CardState state) {
          return state.copyWith(
            isView: true,
            needShowProgress: false,
          );
        };
        return ctrl;
      },
      child: BlocBuilder<CardController, CardState>(
        builder: (context, state) {
          return Stack(
            children: [
              Positioned.fill(
                child: SingleChildScrollView(
                  child: _buildDetail(_state.model?.medalGroup?.upgrade == 1,
                      _state.medals, _state.initialIndex),
                ),
              ),
              // 已获得奖章才显示分享按钮
              if (_currentMedal().isGet == 1 && !isInIosAuditMode())
                Positioned(
                    left: 48.rdp,
                    right: 48.rdp,
                    bottom: 60.rdp,
                    child:
                        _buildShareButton())
            ],
          );
        },
      ),
    );
  }

  Widget _buildShareButton() {
    return GestureDetector(
      onTap: () {
        final currentPage = _state.initialIndex;
        RunEnv.sensorsTrack(
            '\$AppClick',
            _sensorsTrackData(
              elementName: "我的奖章_奖章详情页_分享海报按钮点击",
              medalId: "${_state.medals[currentPage].id ?? 0}",
              medalName: _state.medals[currentPage].title ?? "",
            ));

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ShareAchievementView(
              model: _state.model!,
              medalId: _currentMedal().id ?? 0,
              commonResZip: _state.commonResZip,
            ),
          ),
        );
      },
      child: Container(
        width: 280.rdp,
        height: 44.rdp,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: context.appColors.jColorYellow4,
          borderRadius: BorderRadius.circular(22.rdp),
        ),
        child: Center(
          child: Text(
            "生成分享截图",
            style: context.textstyles.bodyTextLargeEmphasis.pf.copyWith(
              color: context.appColors.jColorGray6,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetail(bool canUpgrade, List<MedalModel> medals, int index) {
    MedalModel currentMedal = _currentMedal();
    MedalModel? nextStageMedal =
        index + 1 < medals.length ? medals[index + 1] : null;

    DateFormat dateFormat = DateFormat("yyyy年MM月dd日获得");
    String medalAwardTime = dateFormat
        .format(DateTime.fromMillisecondsSinceEpoch(currentMedal.getTime ?? 0));

    int current = 0;
    int total = 1;

    String progressText = "";
    if (currentMedal.isGet == 1 && nextStageMedal == null && canUpgrade) {
      current = currentMedal.progress?.current ?? 0;
      total = currentMedal.progress?.total ?? 0;
      progressText = S.of(context).highestMedalMessage;
    } else if (currentMedal.isGet == 1 && canUpgrade) {
      current = nextStageMedal?.progress?.current ?? 0;
      total = nextStageMedal?.progress?.total ?? 0;
      progressText = _state.model?.medalGroup?.tips ?? "";
    } else if (currentMedal.isGet == 0 && canUpgrade) {
      current = currentMedal.progress?.current ?? 0;
      total = currentMedal.progress?.total ?? 0;
      progressText = _state.model?.medalGroup?.tips ?? "";
    }

    if (currentMedal.isGet == 1) {
      RunEnv.sensorsTrack(
          'c_element_name',
          _sensorsTrackData(
            cElementName: "分享海报按钮曝光",
            medalId: "${_state.medals[_state.initialIndex].id ?? 0}",
            medalName: _state.medals[_state.initialIndex].title ?? "",
          ));
    }

    return Column(
      children: [
        SizedBox(height: 20.rdp),

        // 奖章动效
        AchievementCardWrapper(width: 180.rdp, height: 180.rdp),
        SizedBox(height: 16.rdp),

        // 奖章名称
        Text(
          currentMedal.title ?? "",
          style: context.textstyles.largestTextEmphasis.pf.copyWith(
            color: context.appColors.jColorGray6,
          ),
        ),
        SizedBox(height: 8.rdp),

        // 奖章标签
        if ((currentMedal.tags?.length ?? 0) > 0) _buildMedalTag(currentMedal),
        if ((currentMedal.tags?.length ?? 0) > 0) SizedBox(height: 8.rdp),

        // 奖章描述
        Text(
          currentMedal.remark ?? "",
          style: context.textstyles.bodyText.pf.copyWith(
            color: context.appColors.jColorGray5,
          ),
        ),
        if (currentMedal.isGet == 1) SizedBox(height: 8.rdp),

        // 奖章获得时间
        if (currentMedal.isGet == 1)
          Text(
            medalAwardTime,
            style: context.textstyles.remark.pf.copyWith(
              color: context.appColors.jColorGray4,
            ),
          ),
        if (canUpgrade && currentMedal.isGet == 1) SizedBox(height: 28.rdp),
        if (canUpgrade && currentMedal.isGet == 0) SizedBox(height: 57.rdp),

        // 进度条
        if (canUpgrade)
          Padding(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            child: Row(
              children: [
                // 当前进度小于总数才显示
                if (current != total)
                  // 测试用例定为不显示负数
                  Text(
                    "${max(current, 0)}/${max(total, 0)}",
                    style: context.textstyles.bodyText.pf.copyWith(
                      color: context.appColors.jColorGray5,
                    ),
                  ),
                if (current != total) SizedBox(width: 16.rdp),
                // 当前进度小于总数，则靠右，否则居中
                Expanded(
                  child: Text(
                    progressText,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign:
                        current != total ? TextAlign.right : TextAlign.center,
                    style: context.textstyles.bodyText.pf.copyWith(
                      color: context.appColors.jColorGray5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        if (canUpgrade) SizedBox(height: 8.rdp),
        if (canUpgrade)
          SizedBox(
            height: 16.rdp,
            child: SimpleProgressBar(
              progress: current / total,
              padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
              progressColor: context.appColors.jColorYellow4,
              backgroundColor: context.appColors.jColorYellow2,
              borderRadius: BorderRadius.circular(8.rdp),
              progressBorderColor: context.appColors.jColorYellow5,
              backgroundBorderColor: context.appColors.jColorYellow2,
            ),
          ),
        if ((currentMedal.description?.length ?? 0) > 0)
          SizedBox(height: 28.rdp),
        if ((currentMedal.description?.length ?? 0) > 0)
          if ((currentMedal.tags?.length ?? 0) == 0) SizedBox(height: 32.rdp),

        // 奖章介绍
        if ((currentMedal.description?.length ?? 0) > 0)
          _buildMedalDetail(currentMedal),
      ],
    );
  }

  Widget _buildMedalDetail(MedalModel? medal) {
    double bottomMargin = 28.rdp;
    if (medal?.isGet == 1 && !isInIosAuditMode()) {
      // 如果分享按钮未展示，不需要太多 margin
      bottomMargin = (60 + 44 + 20).rdp;
    }

    return Container(
      width: double.infinity,
      margin:
          EdgeInsets.only(left: 20.rdp, right: 20.rdp, bottom: bottomMargin),
      padding: EdgeInsets.all(14.rdp),
      decoration: BoxDecoration(
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
        borderRadius:
            BorderRadius.circular(context.dimensions.largeCornerRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).medalDescription,
            style: context.textstyles.bodyTextEmphasis.pf.copyWith(
              color: context.appColors.jColorGray5,
            ),
          ),
          SizedBox(height: 8.rdp),
          Text(
            medal?.description ?? "",
            style: context.textstyles.remark.pf.copyWith(
              color: context.appColors.jColorGray4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMedalTag(MedalModel? medal) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 4.rdp,
      children: [
        for (MedalTag tag in medal?.tags ?? [])
          tag.name == "稀有"
              ? RareMedalTag(tag.name ?? "")
              : NormalMedalTag(tag.name ?? ""),
      ],
    );
  }

  MedalModel _currentMedal() {
    return _state.medals[_state.initialIndex];
  }

  Map<String, dynamic> _sensorsTrackData({
    String? cElementName,
    String? elementName,
    String? medalId,
    String? medalName,
  }) {
    Map<String, dynamic> map = {
      "\$screen_name": "奖章详情页面曝光",
      "custom_state":
          _state.model?.onlyTrainingCamp == 1 ? "纯训练营用户" : "包含年课的用户",
      "material_id": getSubjectName(_state.model?.subjectType ?? 0),
      "course_stage": _state.model?.segmentName ?? "",
      if (cElementName != null) "c_element_name": cElementName,
      if (elementName != null) "\$element_name": elementName,
      if (medalId != null) "medal_id": medalId,
      if (medalName != null) "medal_name": medalName,
    };
    return map;
  }
}
