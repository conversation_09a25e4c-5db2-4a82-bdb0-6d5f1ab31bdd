import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';

import '../../../common/config/config.dart';
import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';
import '../../../static/img.dart';
import '../../../widgets/share/share_action.dart';
import '../../plan_home/model/subject_type.dart';
import '../achievement_detail/widget/medal_tag.dart';
import '../model/achievement_detail_model.dart';
import '../model/medal_model.dart';
import '../my_achievements/viewmodel/card_controller.dart';
import '../my_achievements/viewmodel/card_state.dart';
import '../my_achievements/viewmodel/view_model.dart';
import '../widget/achievement_card_wapper.dart';

class ShareAchievementView extends StatefulWidget {
  final int medalId;
  final AchievementDetailModel model;
  final String commonResZip;

  const ShareAchievementView(
      {super.key,
      required this.model,
      required this.medalId,
      required this.commonResZip});

  @override
  State<StatefulWidget> createState() => ShareAchievementViewState();
}

class ShareAchievementViewState extends State<ShareAchievementView> {
  final GlobalKey _shareImageKey = GlobalKey();
  late MedalModel _medalModel;

  @override
  void initState() {
    super.initState();
    for (MedalModel medal in widget.model.medalGroup?.groupMedals ?? []) {
      if (medal.id == widget.medalId) {
        _medalModel = medal;
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: JoJoAppBar(
        title: S.of(context).achievementsDetail,
      ),
      body: Padding(
          padding: EdgeInsets.only(top: 20.rdp, left: 20.rdp, right: 20.rdp),
          child: Column(
            children: [
              RepaintBoundary(
                key: _shareImageKey,
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                          context.dimensions.smallCornerRadius),
                      color: context.appColors.jColorBrown1),
                  child: _buildShareContent(),
                ),
              ),
              SizedBox(height: 28.rdp),
              _buildShareButtons()
            ],
          )),
    );
  }

  /// 分享内容
  Widget _buildShareContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.rdp),
        // 用户信息
        _buildUserInfo(),
        SizedBox(height: 8.rdp),

        Center(
            child: Text(
          "获得一枚勋章",
          style: context.textstyles.bodyText.pf
              .copyWith(color: context.appColors.jColorYellow5),
        )),

        // 勋章
        SizedBox(height: 16.rdp),
        Center(child: _buildMedalCard(_medalModel)),
        SizedBox(height: 8.rdp),

        // 标题
        Center(
            child: Text(
          _medalModel.title ?? "",
          style: context.textstyles.largestTextEmphasis.pf
              .copyWith(color: context.appColors.jColorGray6),
        )),

        // 标签
        SizedBox(height: 8.rdp),
        Center(child: _buildMedalTag(_medalModel)),
        SizedBox(height: 40.rdp),

        // 分割线
        Container(
            margin: EdgeInsets.symmetric(horizontal: 20.rdp),
            height: 1.rdp,
            decoration: BoxDecoration(
              color: HexColor("#F6E9C8"),
            )),

        // 引流信息
        SizedBox(height: 16.rdp),
        _buildMarketingLeadInfo(),
        SizedBox(height: 20.rdp),
      ],
    );
  }

  /// 勋章标签
  Widget _buildMedalTag(MedalModel medal) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 4.rdp,
      children: [
        for (MedalTag tag in medal.tags ?? [])
          tag.name == "稀有"
              ? RareMedalTag(tag.name ?? "")
              : NormalMedalTag(tag.name ?? ""),
      ],
    );
  }

  /// 用户信息
  Widget _buildUserInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 40.rdp,
          height: 40.rdp,
          decoration:
              BoxDecoration(borderRadius: BorderRadius.circular(20.rdp)),
          clipBehavior: Clip.hardEdge,
          child: CachedNetworkImage(
            imageUrl: widget.model.userInfo?.avatar ?? "",
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(width: 8.rdp),
        Text(widget.model.userInfo?.nickname ?? "",
            style: context.textstyles.bodyText.pf
                .copyWith(color: context.appColors.jColorYellow6))
      ],
    );
  }

  Widget _buildMedalCard(MedalModel medal) {
    return BlocProvider(
      create: (_) {
        AchievementCard card = AchievementCard(0, "", medal, null);
        var ctrl =
            CardController(card, widget.commonResZip, enablePerfMode: true);
        // 始终不显示进度条和 new
        ctrl.shouldUpdateState = (CardState state) {
          return state.copyWith(
            isView: true,
            needShowProgress: false,
            playing: false,
          );
        };
        return ctrl;
      },
      child: BlocBuilder<CardController, CardState>(
        builder: (context, state) {
          return AchievementCardWrapper(width: 180.rdp, height: 180.rdp);
        },
      ),
    );
  }

  /// 引流信息
  Widget _buildMarketingLeadInfo() {
    return Row(
      children: [
        SizedBox(width: 16.rdp),
        ImageAssetWeb(
            assetName: AssetsImg.JOJO_LOGO,
            width: 129.rdp,
            height: 40.rdp,
            package: Config.package),
        Expanded(child: Container()),
        Text("我也要报名", style: context.textstyles.smallestText.pf),
        SizedBox(width: 8.rdp),
        Container(
          width: 50.rdp,
          height: 50.rdp,
          padding: EdgeInsets.all(4.rdp),
          decoration: const BoxDecoration(color: Colors.white),
          child: FutureBuilder<Uint8List>(
            future: _generateQRCode(widget.model.shareInfo?.introduceUrl ?? ""),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                if (snapshot.hasData) {
                  return Image.memory(
                    snapshot.data!,
                    width: 46.rdp,
                    height: 46.rdp,
                    fit: BoxFit.cover,
                  );
                } else if (snapshot.hasError) {
                  return Container();
                }
              }
              return const Center(child: CircularProgressIndicator());
            },
          ),
        ),
        SizedBox(width: 20.rdp),
      ],
    );
  }

  /// 分享按钮
  Widget _buildShareButtons() {
    return Row(children: [
      GestureDetector(
          onTap: () {
            RunEnv.sensorsTrack(
                '\$AppClick',
                _sensorsTrackData(
                  elementName: "我的奖章_海报分享_分享朋友圈",
                  medalId: "${_medalModel.id ?? 0}",
                  medalName: _medalModel.title ?? "",
                ));

            _shareAchievement(ShareType.wxCircle);
          },
          child: _buildShareButton("朋友圈", AssetsImg.SHARE_ACTION_WX_CIRCLE)),
      Expanded(child: Container()),
      GestureDetector(
          onTap: () {
            RunEnv.sensorsTrack(
                '\$AppClick',
                _sensorsTrackData(
                  elementName: "我的奖章_海报分享_保存相册",
                  medalId: "${_medalModel.id ?? 0}",
                  medalName: _medalModel.title ?? "",
                ));

            _shareAchievement(ShareType.download);
          },
          child: _buildShareButton("保存相册", AssetsImg.MEDALS_DOWNLOAD)),
    ]);
  }

  Widget _buildShareButton(String title, String iconName) {
    return Container(
        width: 160.rdp,
        height: 50.rdp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25.rdp),
          border:
              Border.all(color: context.appColors.jColorGray3, width: 1.rdp),
        ),
        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          ImageAssetWeb(
            assetName: iconName,
            width: 40.rdp,
            height: 40.rdp,
            package: Config.package,
          ),
          SizedBox(width: 5.rdp),
          Text(title, style: context.textstyles.smallestText.pf)
        ]));
  }

  Future<void> _shareAchievement(ShareType type) async {
    // 生成_buildShareImage这个Widget的截图
    RenderRepaintBoundary boundary = _shareImageKey.currentContext!
        .findRenderObject() as RenderRepaintBoundary;
    var image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    String base64Img = base64Encode(pngBytes);

    if (type == ShareType.download) {
      // 下载图片，原生有 toast 提示结果
      await jojoNativeBridge.saveBase64Image(
          base64: 'data:image/png;base64,$base64Img');
    } else {
      final res = await jojoNativeBridge.shareToPlatform(
        desc: '',
        image: 'data:image/png;base64,$base64Img',
        platformType: 0,
        scene: type == ShareType.wx ? 0 : 1,
        shareType: 1,
        title: '',
        url: '',
      );

      if (res.msg != '' && res.status != 200) {
        JoJoToast.showWarning(res.msg);
      }
    }
  }

  Future<Uint8List> _generateQRCode(String content) async {
    final qrCode = QrPainter(
      data: content,
      version: QrVersions.auto,
      errorCorrectionLevel: QrErrorCorrectLevel.L,
    );

    final image = await qrCode.toImage(200);
    final bytes = await image.toByteData(format: ui.ImageByteFormat.png);
    return bytes!.buffer.asUint8List();
  }

  Map<String, dynamic> _sensorsTrackData({
    String? cElementName,
    String? elementName,
    String? courseStage,
    String? medalId,
    String? medalName,
  }) {
    Map<String, dynamic> map = {
      "custom_state": widget.model.onlyTrainingCamp == 1 ? "纯训练营用户" : "包含年课的用户",
      "material_id": getSubjectName(widget.model.subjectType ?? 0),
      "course_stage": widget.model.segmentName ?? "",
      if (cElementName != null) "c_element_name": cElementName,
      if (elementName != null) "\$element_name": elementName,
      if (courseStage != null) "course_stage": courseStage,
      if (medalId != null) "medal_id": medalId,
      if (medalName != null) "medal_name": medalName,
    };
    return map;
  }
}
