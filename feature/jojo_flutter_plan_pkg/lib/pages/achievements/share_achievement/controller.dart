import 'dart:io';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/state.dart';

import '../../../service/achievements_api.dart';
import '../../../utils/file_util.dart';
import '../model/achievement_detail_model.dart';
import '../model/medal_model.dart';

class ShareAchievementController extends Cubit<ShareAchievementState> {
  static const String tag = "ShareAchievementController";

  final int medalId;
  final AchievementsApi dao;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  ShareAchievementController({
    required this.medalId,
    required this.dao,
  }) : super(ShareAchievementState(PageStatus.loading));

  ShareAchievementController.withDefault({required this.medalId})
      : dao = proAchievementsApi,
        // : dao = AchievementsApiMock(),
        super(ShareAchievementState(PageStatus.loading)) {
    refreshData();
  }

  @override
  Future<void> close() async {
    await _resourceManager.cancelDownload();
    super.close();
  }

  setPageStatus(PageStatus status) {
    emit(ShareAchievementState(status));
  }

  Future<void> refreshData() async {
    try {
      // 获取数据
      final AchievementDetailModel model = await dao.getMedalDetail(medalId);
      if (model.medalGroup != null) {
        final commonRes = model.commonRes ?? "";

        _downloadZipRes(model.medalGroup?.groupMedals ?? [], commonRes,
            successListener: (urlMap) {
          emit(ShareAchievementState(
            PageStatus.success,
            model: model,
            commonResZip: urlMap[commonRes] ?? "",
          ));
        }, failListener: (error) {
          l.e(tag, "下载资源失败: $error");
          emit(ShareAchievementState(PageStatus.error,
              exception: Exception(error)));
        });
      } else {
        l.e(tag, "没有奖章数据");
        emit(ShareAchievementState(PageStatus.error,
            exception: Exception("没有奖章数据")));
        return;
      }
    } catch (e) {
      l.e(tag, "刷新数据异常: $e");
      if (e is Exception) {
        emit(ShareAchievementState(PageStatus.error, exception: e));
      } else {
        emit(ShareAchievementState(PageStatus.error, exception: Exception(e)));
      }
    }
  }

  _downloadZipRes(List<MedalModel> medals, String commonResUrl,
      {Function(Map<String, String>)? successListener,
      Function(String)? failListener}) async {
    // 奖章的资源
    List<String> urlList = medals
        .map((medal) {
          return medal.resource?["flutterRes"] ?? "";
        })
        .where((url) => url.isNotEmpty)
        .toList();
    // 公共资源
    if (commonResUrl.isNotEmpty) {
      urlList.add(commonResUrl);
    }

    await _resourceManager.downloadUrl(urlList, isNeedCancel: false,
        successListener: (urlMap) async {
      for (String url in urlList) {
        String localPath = urlMap[url] ?? "";
        if (localPath.isNotEmpty) {
          String dirPath = await unzip(localPath);
          // 删除多余文件
          await removeUselessFilesAndDirs(Directory(dirPath));

          if (Directory(dirPath).existsSync()) {
            l.i(tag, "_downloadZipRes success url: $url localPath: $localPath");
          } else {
            l.e(tag, "_downloadZipRes fail url: $url localPath: $localPath");
          }
        } else {
          l.e(tag, "_downloadZipRes fail url: $url");
        }
      }
      successListener?.call(urlMap);
    }, failListener: (error) {
      l.e(tag, "_downloadZipRes fail error: $error");
      failListener?.call(error.message ?? "");
    });
  }
}
