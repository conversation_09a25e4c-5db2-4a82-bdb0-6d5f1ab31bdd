import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/view.dart';

import '../../../generated/l10n.dart';
import 'controller.dart';

class ShareAchievementPage extends StatelessWidget {
  final int medalId;

  const ShareAchievementPage({Key? key, required this.medalId})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) =>
            ShareAchievementController.withDefault(medalId: medalId),
        child: BlocBuilder<ShareAchievementController, ShareAchievementState>(
            builder: (context, state) {
          return state.pageStatus != PageStatus.success
              ? JoJoPageLoadingV25(
                  status: state.pageStatus,
                  hideProgress: true,
                  exception: state.exception,
                  backWidget: Jo<PERSON><PERSON><PERSON>ppBar(
                      title: S.of(context).achievementsDetail,
                      backgroundColor: Colors.transparent),
                  retry: () {
                    context
                        .read<ShareAchievementController>()
                        .setPageStatus(PageStatus.loading);
                    context.read<ShareAchievementController>().refreshData();
                  },
                  child: Container())
              : ShareAchievementView(
                  medalId: medalId,
                  model: state.model!,
                  commonResZip: state.commonResZip!,
                );
        }));
  }
}
