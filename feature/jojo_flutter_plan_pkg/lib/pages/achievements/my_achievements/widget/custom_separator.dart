import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

/// CustomSeparator 用于展示中间一个 widget，两侧有灰色分隔线的控件。
class CustomSeparator extends StatelessWidget {
  /// 两侧与屏幕边缘的间距
  final double sidePadding;

  /// 中间 widget 与两侧分隔线的间距
  final double widgetSpacing;

  /// 中间展示的 widget
  final Widget centerWidget;

  /// 分隔线的粗细（默认 1.0）
  final double lineThickness;

  const CustomSeparator({
    Key? key,
    required this.sidePadding,
    required this.widgetSpacing,
    required this.centerWidget,
    this.lineThickness = 1.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: sidePadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧分隔线
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: widgetSpacing),
              height: lineThickness,
              color: context.appColors.jColorGray4,
            ),
          ),
          // 中间 widget
          centerWidget,
          // 右侧分隔线
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: widgetSpacing),
              height: lineThickness,
              color: context.appColors.jColorGray4,
            ),
          ),
        ],
      ),
    );
  }
}