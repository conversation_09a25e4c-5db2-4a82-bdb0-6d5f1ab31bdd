import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

/// CarouselIndicator 用于展示轮播图的下标指示条
class CarouselIndicator extends StatelessWidget {
  /// 轮播图的总数量
  final int count;

  /// 每个指示 item 的大小（宽高）
  final double itemSize;

  /// item 之间的间隔
  final double spacing;

  /// 通过该 controller 控制当前选中的下标
  final ValueNotifier<int> controller;

  const CarouselIndicator({
    Key? key,
    required this.count,
    required this.itemSize,
    required this.spacing,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent, // 背景设为透明
      child: ValueListenableBuilder<int>(
        valueListenable: controller,
        builder: (context, currentIndex, child) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(count, (index) {
              bool isSelected = index == currentIndex;
              return Container(
                width: itemSize,
                height: itemSize,
                margin: EdgeInsets.symmetric(horizontal: spacing / 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? context.appColors.jColorYellow4
                      : context.appColors.jColorGray2,
                  shape: BoxShape.circle,
                ),
              );
            }),
          );
        },
      ),
    );
  }
}
