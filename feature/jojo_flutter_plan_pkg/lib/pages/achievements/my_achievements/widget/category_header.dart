import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';

class CategoryHeader extends StatelessWidget {
  final String title;

  const CategoryHeader({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.rdp, bottom: 8.rdp),
      child: Text(
          title,
          style: context.textstyles.bodyTextLargeEmphasis.pf.copyWith(
            color: context.appColors.jColorGray6
          )
      ),
    );
  }
}