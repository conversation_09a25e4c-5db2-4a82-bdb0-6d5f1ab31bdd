import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/cards_row.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/category_header.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/custom_separator.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/empty_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/latest_medals_widget.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:async';

import '../../../common/host_env/host_env.dart';
import '../../plan_home/model/subject_type.dart';
import '../achievement_detail/page.dart';
import '../model/medal_model.dart';
import 'controller.dart';
import 'viewmodel/latest_medal_click_event_data.dart';

class MyAchievementsView extends StatefulWidget {
  final ScrollController scrollController;
  final AudioPlayer audioPlayer;

  const MyAchievementsView(
      {Key? key, required this.scrollController, required this.audioPlayer})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => MyAchievementsViewState();
}

class MyAchievementsViewState extends State<MyAchievementsView> {
  final String tag = 'MyAchievementsViewState';

  /// 最近获得奖章的索引
  int latestMedalsWidgetIndex = 0;

  /// 播放音频相关的变量
  int _currentAudioIndex = 0;
  StreamSubscription<void>? _audioCompletionSubscription;

  MyAchievementsState get _state =>
      context.read<MyAchievementsController>().state;

  @override
  void initState() {
    super.initState();

    if (_state.mediaPaths.isNotEmpty) {
      _currentAudioIndex = 0;
      widget.audioPlayer
          .play(DeviceFileSource(_state.mediaPaths[_currentAudioIndex]));
      _audioCompletionSubscription =
          widget.audioPlayer.onPlayerComplete.listen((event) {
        _currentAudioIndex++;
        if (_currentAudioIndex < _state.mediaPaths.length) {
          widget.audioPlayer
              .play(DeviceFileSource(_state.mediaPaths[_currentAudioIndex]));
        }
      });
    }

    RunEnv.sensorsTrack('\$AppViewScreen', _sensorsTrackData());
  }

  @override
  void dispose() {
    _audioCompletionSubscription?.cancel();
    super.dispose();
  }

  void stopAudio() {
    widget.audioPlayer.stop();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: widget.scrollController,
      itemCount: _state.items.length,
      itemBuilder: (context, index) {
        MyAchievementsItem item = _state.items[index];
        return _buildListItem(item);
      },
    );
  }

  Widget _buildListItem(MyAchievementsItem item) {
    if (item is LatestMedals) {
      if (item.latestMedals.isEmpty) {
        return const SizedBox.shrink();
      } else {
        return LatestMedalsWidget(item.latestMedals,
            defaultSelectIndex: latestMedalsWidgetIndex, onItemTap: (medal) {
          RunEnv.sensorsTrack(
              '\$AppClick',
              _sensorsTrackData(
                elementName: "我的奖章_最近获得_点击奖章",
                medalId: "${medal.id ?? 0}",
                medalName: medal.title ?? '',
              ));

          _gotoMedalDetail(medal);
        }, onSelectedItem: (index, medal) {
          latestMedalsWidgetIndex = index;
          RunEnv.sensorsTrack(
              'ElementView',
              _sensorsTrackData(
                cElementName: "我的奖章_最近获得_滑动列表",
                medalId: "${medal.id ?? 0}",
                medalName: medal.title ?? '',
              ));
        });
      }
    } else if (item is SegmentTitle) {
      // 学段标题
      return CustomSeparator(
        sidePadding: 20.rdp,
        widgetSpacing: 10.rdp,
        lineThickness: 0.3,
        centerWidget: Text(
          item.title,
          style: context.textstyles.remark.pf.copyWith(
            color: context.appColors.jColorGray4,
          ),
        ),
      );
    } else if (item is CategoryTitle) {
      // 分类标题
      return CategoryHeader(title: item.title);
    } else if (item is AchievementsRow) {
      // 勋章行
      return CardsRow(
        cards: item.cards,
        onItemTap: (card) {
          int medalId = 0;
          String medalName = '';
          if (card.latestMedal != null) {
            medalId = card.latestMedal?.id ?? 0;
            medalName = card.latestMedal?.title ?? '';

            _gotoMedalDetail(card.latestMedal);
          } else {
            medalId = card.nextStageMedal?.id ?? 0;
            medalName = card.nextStageMedal?.title ?? '';

            _gotoMedalDetail(card.nextStageMedal);
          }

          RunEnv.sensorsTrack(
              '\$AppClick',
              _sensorsTrackData(
                elementName: "我的奖章_奖章列表_点击奖章",
                medalId: medalId.toString(),
                medalName: medalName,
              ));
        },
      );
    } else if (item is FillImage) {
      // 占位图
      return ImageNetworkCached(
        imageUrl: item.image,
        width: double.infinity,
        fit: BoxFit.cover,
      );
    } else if (item is LineSpace) {
      // 占位行
      return SizedBox(
        height: item.height.clamp(0.0, double.infinity),
      );
    } else if (item is EmptyTip) {
      // 缺省页
      return const EmptyView();
    } else {
      l.i(tag, '未知的列表项: $item');
      return Text('未知的列表项: $item');
    }
  }

  /// 跳转到勋章详情页
  _gotoMedalDetail(MedalModel? medal) {
    final isVisitorAttitude = pageController?.isVisitorAttitude() ?? false;
    l.i("isVisitorAttitude", isVisitorAttitude);
    if (isVisitorAttitude) {
      return; //如果是客态页就 return
    }
    stopAudio();

    jojoEventBus.fire(LatestMedalClickEventData(medal?.id ?? 0));

    RunEnv.jumpLink(medal?.detailRoute ?? '');
  }

  Map<String, dynamic> _sensorsTrackData({
    String? elementName,
    String? cElementName,
    String? courseStage,
    String? materialId,
    String? medalId,
    String? medalName,
  }) {
    Map<String, dynamic> map = {
      "\$screen_name": "我的奖章页面曝光",
      "custom_state": _state.onlyTrainingCamp ? "纯训练营用户" : "包含年课的用户",
      "material_id":
          getSubjectName(context.read<MyAchievementsController>().subjectType),
      if (cElementName != null) "c_element_name": cElementName,
      if (elementName != null) "\$element_name": elementName,
      if (courseStage != null) "course_stage": courseStage,
      if (materialId != null) "material_id": materialId,
      if (medalId != null) "medal_id": medalId,
      if (medalName != null) "medal_name": medalName,
    };
    return map;
  }

  MyAchievementsController? get pageController =>
      mounted ? context.read<MyAchievementsController>() : null;
}
