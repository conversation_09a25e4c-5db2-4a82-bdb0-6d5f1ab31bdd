import '../../model/medal_spine_info.dart';

enum CardStatus { show, downloading, error }

enum CardType { role, medal }

class CardState {
  final CardStatus status;
  final CardType type;
  final bool playing;

  final bool isGet;
  final bool isView;

  final bool needShowProgress;
  final double progress;

  final String spineDirectory;
  final String commonResDirectory;

  final String title;
  final MedalSpineInfo? spineInfo;

  CardState({
    this.status = CardStatus.downloading,
    this.type = CardType.medal,
    this.playing = true,
    this.isGet = false,
    this.isView = false,
    this.needShowProgress = false,
    this.progress = 0.0,
    this.spineDirectory = "",
    this.commonResDirectory = "",
    this.title = "",
    this.spineInfo,
  });

  CardState copyWith({
    CardStatus? status,
    CardType? type,
    bool? playing,
    bool? isGet,
    bool? isView,
    bool? needShowProgress,
    double? progress,
    String? spineDirectory,
    String? commonResDirectory,
    String? title,
    MedalSpineInfo? spineInfo,
  }) =>
      CardState(
        status: status ?? this.status,
        type: type ?? this.type,
        playing: playing ?? this.playing,
        isGet: isGet ?? this.isGet,
        isView: isView ?? this.isView,
        needShowProgress: needShowProgress ?? this.needShowProgress,
        progress: progress ?? this.progress,
        spineDirectory: spineDirectory ?? this.spineDirectory,
        commonResDirectory:
            commonResDirectory ?? this.commonResDirectory,
        title: title ?? this.title,
        spineInfo: spineInfo ?? this.spineInfo,
      );
}
