import '../../model/medal_model.dart';

/// 我的成就页列表项
abstract class MyAchievementsItem {
  MyAchievementsItem();
}

/// 缺省提示
class EmptyTip extends MyAchievementsItem {
  EmptyTip();
}

/// 最新获得勋章轮播
class LatestMedals extends MyAchievementsItem {
  final List<MedalModel> latestMedals;

  LatestMedals(this.latestMedals);
}

/// 学段标题
class SegmentTitle extends MyAchievementsItem {
  final String title;

  SegmentTitle(this.title);
}

/// 分类标题
class CategoryTitle extends MyAchievementsItem {
  final String title;

  CategoryTitle(this.title);
}

/// 勋章卡片
class AchievementCard {
  final int upgrade;
  final String tips;
  final MedalModel? latestMedal;
  final MedalModel? nextStageMedal;

  AchievementCard(
      this.upgrade, this.tips, this.latestMedal, this.nextStageMedal);
}

/// 一行勋章
/// 之所以需要一行一行显示，是为了懒加载列表以及避免下载不可见区域的资源
class AchievementsRow extends MyAchievementsItem {
  final List<AchievementCard> cards;

  AchievementsRow(this.cards);
}

/// 占位图
class FillImage extends MyAchievementsItem {
  final String image;

  FillImage(this.image);
}

/// 占位行
class LineSpace extends MyAchievementsItem {
  final double height;

  LineSpace(this.height);
}
