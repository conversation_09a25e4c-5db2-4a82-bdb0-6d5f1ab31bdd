import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:path/path.dart' as p;

import '../../../../utils/file_util.dart';
import '../../model/medal_model.dart';
import '../../model/medal_spine_info.dart';
import 'card_state.dart';
import 'latest_medal_click_event_data.dart';

class CardController extends Cubit<CardState> {
  final String tag = "CardController";
  final AchievementCard card;
  final String commonResZip;
  final bool enablePerfMode;

  static final JoJoResourceManager resourceManager = JoJoResourceManager();

  CardState Function(CardState state)? shouldUpdateState;

  /// 最新点击的奖章流
  StreamSubscription? latestMedalClickEventStream;

  CardController(this.card, this.commonResZip, {this.enablePerfMode = false})
      : super(CardState()) {
    _init();
  }

  @override
  close() async {
    super.close();
    latestMedalClickEventStream?.cancel();
  }

  /// 更新状态，并在需要下载 spine 资源时开始下载。
  Future<void> _init() async {
    try {
      _listenLatestMedalClick();
      final updatedState = await updateState();
      _emitState(updatedState);

      if (updatedState.status == CardStatus.downloading) {
        // 异步下载，然后更新状态
        _downloadSpineResources();
      }
    } catch (e) {
      _emitState(state.copyWith(status: CardStatus.error));
    }
  }

  /// 监听最新点击的奖章，并更新状态
  _listenLatestMedalClick() {
    latestMedalClickEventStream = jojoEventBus.on<LatestMedalClickEventData>().listen((event) {
      // 只有已获得的奖章才需要更新 isView 状态
      if(event.id == card.latestMedal?.id) {
        _emitState(state.copyWith(isView: true));
      }
    });
  }

  /// 统一发射状态，先判断是否已关闭 Cubit，若设置了 shouldUpdateState 则回调转换后再发射状态
  void _emitState(CardState newState) {
    if (!isClosed) {
      if (shouldUpdateState != null) {
        emit(shouldUpdateState!(newState));
      } else {
        emit(newState);
      }
    }
  }

  /// 调用下载方法下载 spine 资源，并在下载完成后重新更新状态。
  void _downloadSpineResources() {
    final latestSpineUrl = card.latestMedal?.resource?["flutterRes"] ?? "";
    final nextSpineUrl = card.nextStageMedal?.resource?["flutterRes"] ?? "";

    List<String> urls = [
      if (latestSpineUrl.isNotEmpty) latestSpineUrl,
      if (nextSpineUrl.isNotEmpty) nextSpineUrl,
    ];
    l.i(tag, "开始下载 spines: $urls");

    resourceManager.downloadUrl(
      urls,
      isNeedCancel: false,
      successListener: (Map<String, String> result) async {
        try {
          final updatedState = await updateState();
          _emitState(updatedState);
        } catch (e, stackTrace) {
          l.e(tag, "下载后更新状态失败：$e \n $stackTrace");
          _emitState(state.copyWith(status: CardStatus.error));
        }
      },
      failListener: (UnifiedExceptionData error) {
        l.e(tag, "spine资源下载失败：$error");
        _emitState(state.copyWith(status: CardStatus.error));
      },
    );
  }

  /// 根据当前的 medal 与 spine 资源状态更新 [CardState]
  Future<CardState> updateState() async {
    try {
      // 根据 medal 判断当前状态（已获得 or 未获得）
      bool isGet = card.latestMedal?.isGet == 1;
      final String title = isGet
          ? card.latestMedal?.title ?? ""
          : card.nextStageMedal?.title ?? "";
      bool isView = isGet
          ? card.latestMedal?.isView == 1
          : card.nextStageMedal?.isView == 1;

      // 获取 spine 资源 URL
      final String latestSpineUrl =
          card.latestMedal?.resource?["flutterRes"] ?? "";
      final String nextSpineUrl =
          card.nextStageMedal?.resource?["flutterRes"] ?? "";

      // 如果两个 URL 均为空，记录错误并返回 error 状态
      if (latestSpineUrl.isEmpty && nextSpineUrl.isEmpty) {
        MedalModel? errMedal = card.latestMedal ?? card.nextStageMedal;
        l.e(tag, "获取奖章的资源信息失败, id: ${errMedal?.id} title: ${errMedal?.title}");
        return state.copyWith(status: CardStatus.error, title: errMedal?.title);
      }

      // 根据 URL 获取 zip 文件的本地路径
      String spineZipPath = latestSpineUrl.isNotEmpty
          ? await resourceManager.getFileLocalPath(latestSpineUrl,
              isOnlyName: true)
          : await resourceManager.getFileLocalPath(nextSpineUrl,
              isOnlyName: true);

      // 使用 path 包提取目录
      final String spineDirectory = p.dirname(spineZipPath);

      // 判断是否需要显示进度条
      bool needShowProgress = card.upgrade == 1;
      double progress = needShowProgress
          ? _calculateProgress(card.nextStageMedal ?? card.latestMedal)
          : 0.0;

      // 解压或验证 spine 资源，返回当前资源状态
      CardStatus status = await _processSpine(
        spineZipPath,
        spineDirectory,
        isGet ? "已获得的奖章 spine 文件解压失败" : "未获得的奖章 spine 文件解压失败",
      );

      // 根据目录中是否存在 medalResInfo.json 判断显示类型：角色或勋章
      String? resInfoJsonPath = _findMedalResInfoJson(spineDirectory);
      CardType type = resInfoJsonPath != null ? CardType.role : CardType.medal;

      // 如果是角色类型，则还需处理公共资源 zip 文件
      if (type == CardType.role) {
        if (commonResZip.isEmpty) {
          l.e(tag, "获取公共资源 zip 文件失败");
          return state.copyWith(status: CardStatus.error);
        }
        final String commonResDirectory = p.dirname(commonResZip);
        MedalSpineInfo? info;

        // 读取 medalResInfo.json 配置
        if (resInfoJsonPath != null && File(resInfoJsonPath).existsSync()) {
          try {
            String jsonData = await File(resInfoJsonPath).readAsString();
            info = MedalSpineInfo.fromJson(json.decode(jsonData));
          } catch (e, stackTrace) {
            l.e(tag, "读取 medalResInfo.json 失败, 资源包文件: ${p.basename(spineZipPath)} error：$e\n$stackTrace");
          }
        } else {
          l.e(
            tag,
            "获取角色奖章的配置失败, id: ${card.latestMedal?.id} title: ${card.latestMedal?.title}",
          );
        }

        return state.copyWith(
          status: status,
          type: type,
          isGet: isGet,
          isView: isView,
          needShowProgress: needShowProgress,
          progress: progress,
          spineDirectory: spineDirectory,
          commonResDirectory: commonResDirectory,
          title: title,
          spineInfo: info);
      } else {
        return state.copyWith(
          status: status,
          type: type,
          isGet: isGet,
          isView: isView,
          needShowProgress: needShowProgress,
          progress: progress,
          spineDirectory: spineDirectory,
          title: title,
        );
      }
    } catch (e, stackTrace) {
      l.e(tag, "updateState 发生异常：$e \n $stackTrace");
      return state.copyWith(status: CardStatus.error);
    }
  }

  /// 检查指定目录中是否存在有效的 spine 资源文件
  bool _hasValidSpine(String directory) {
    File? atlasFile = findFilesByExtension(directory, "atlas.txt");
    File? skelTextFile = findFilesByExtension(directory, "skel.txt");
    File? skelBytesFile = findFilesByExtension(directory, "skel.bytes");
    return atlasFile != null && (skelTextFile != null || skelBytesFile != null);
  }

  /// 查找指定目录中的 medalResInfo.json 文件
  String? _findMedalResInfoJson(String directory) {
    try {
      File? jsonFile = findFilesByName(directory, "MedalResInfo.json");
      if (jsonFile != null &&
          jsonFile.path
              .toLowerCase()
              .endsWith(p.join("json", "medalresinfo.json"))) {
        return jsonFile.path;
      }
    } catch (e) {
      l.w(tag, "查找 MedalResInfo.json 时异常：$e");
    }
    return null;
  }

  /// 处理 spine 文件：
  /// 1. 判断目录中是否存在可用 spine 文件，存在则直接显示；
  /// 2. 如果 zip 文件不存在，则处于下载中状态；
  /// 3. 尝试解压 zip 文件，再次验证 spine 文件；
  /// 4. 解压失败时记录错误日志并返回错误状态。
  Future<CardStatus> _processSpine(
    String zipFile,
    String directory,
    String errorLogMsg,
  ) async {
    try {
      // 目录存在且已包含有效 spine 文件，直接返回显示状态
      if (Directory(directory).existsSync() && _hasValidSpine(directory)) {
        return CardStatus.show;
      }
      // 如果 zip 文件不存在，则说明还在下载中
      if (!File(zipFile).existsSync()) {
        return CardStatus.downloading;
      }

      // 尝试解压 zip 文件
      String unzipDir = await unzip(zipFile);
      // 删除多余文件
      await removeUselessFilesAndDirs(Directory(unzipDir));

      // 解压后验证 spine 文件是否有效
      if (_hasValidSpine(directory)) {
        return CardStatus.show;
      } else {
        l.e(tag, errorLogMsg);
        return CardStatus.error;
      }
    } catch (e, stackTrace) {
      l.e(tag, "处理 spine 过程出错：$e \n $stackTrace");
      return CardStatus.error;
    }
  }

  /// 根据 [MedalModel] 进度数据计算当前进度百分比
  double _calculateProgress(MedalModel? medal) {
    final current = medal?.progress?.current;
    final total = medal?.progress?.total;
    return (current != null && total != null && total > 0)
        ? current / total
        : 0.0;
  }
}
