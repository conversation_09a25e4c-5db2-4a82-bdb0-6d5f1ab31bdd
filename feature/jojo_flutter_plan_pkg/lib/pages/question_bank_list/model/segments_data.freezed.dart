// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'segments_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SegmentsData _$SegmentsDataFromJson(Map<String, dynamic> json) {
  return _SegmentsData.fromJson(json);
}

/// @nodoc
mixin _$SegmentsData {
  int? get modeType => throw _privateConstructorUsedError;
  CourseInfoModel? get courseInfo => throw _privateConstructorUsedError;
  ClassInfoModel? get classInfo => throw _privateConstructorUsedError;
  List<ItemModel>? get itemList => throw _privateConstructorUsedError;
  LocationInfoModel? get locationInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentsDataCopyWith<SegmentsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentsDataCopyWith<$Res> {
  factory $SegmentsDataCopyWith(
          SegmentsData value, $Res Function(SegmentsData) then) =
      _$SegmentsDataCopyWithImpl<$Res, SegmentsData>;
  @useResult
  $Res call(
      {int? modeType,
      CourseInfoModel? courseInfo,
      ClassInfoModel? classInfo,
      List<ItemModel>? itemList,
      LocationInfoModel? locationInfo});

  $CourseInfoModelCopyWith<$Res>? get courseInfo;
  $ClassInfoModelCopyWith<$Res>? get classInfo;
  $LocationInfoModelCopyWith<$Res>? get locationInfo;
}

/// @nodoc
class _$SegmentsDataCopyWithImpl<$Res, $Val extends SegmentsData>
    implements $SegmentsDataCopyWith<$Res> {
  _$SegmentsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modeType = freezed,
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
    Object? itemList = freezed,
    Object? locationInfo = freezed,
  }) {
    return _then(_value.copyWith(
      modeType: freezed == modeType
          ? _value.modeType
          : modeType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfoModel?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfoModel?,
      itemList: freezed == itemList
          ? _value.itemList
          : itemList // ignore: cast_nullable_to_non_nullable
              as List<ItemModel>?,
      locationInfo: freezed == locationInfo
          ? _value.locationInfo
          : locationInfo // ignore: cast_nullable_to_non_nullable
              as LocationInfoModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseInfoModelCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $CourseInfoModelCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassInfoModelCopyWith<$Res>? get classInfo {
    if (_value.classInfo == null) {
      return null;
    }

    return $ClassInfoModelCopyWith<$Res>(_value.classInfo!, (value) {
      return _then(_value.copyWith(classInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LocationInfoModelCopyWith<$Res>? get locationInfo {
    if (_value.locationInfo == null) {
      return null;
    }

    return $LocationInfoModelCopyWith<$Res>(_value.locationInfo!, (value) {
      return _then(_value.copyWith(locationInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SegmentsDataCopyWith<$Res>
    implements $SegmentsDataCopyWith<$Res> {
  factory _$$_SegmentsDataCopyWith(
          _$_SegmentsData value, $Res Function(_$_SegmentsData) then) =
      __$$_SegmentsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? modeType,
      CourseInfoModel? courseInfo,
      ClassInfoModel? classInfo,
      List<ItemModel>? itemList,
      LocationInfoModel? locationInfo});

  @override
  $CourseInfoModelCopyWith<$Res>? get courseInfo;
  @override
  $ClassInfoModelCopyWith<$Res>? get classInfo;
  @override
  $LocationInfoModelCopyWith<$Res>? get locationInfo;
}

/// @nodoc
class __$$_SegmentsDataCopyWithImpl<$Res>
    extends _$SegmentsDataCopyWithImpl<$Res, _$_SegmentsData>
    implements _$$_SegmentsDataCopyWith<$Res> {
  __$$_SegmentsDataCopyWithImpl(
      _$_SegmentsData _value, $Res Function(_$_SegmentsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? modeType = freezed,
    Object? courseInfo = freezed,
    Object? classInfo = freezed,
    Object? itemList = freezed,
    Object? locationInfo = freezed,
  }) {
    return _then(_$_SegmentsData(
      modeType: freezed == modeType
          ? _value.modeType
          : modeType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as CourseInfoModel?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as ClassInfoModel?,
      itemList: freezed == itemList
          ? _value._itemList
          : itemList // ignore: cast_nullable_to_non_nullable
              as List<ItemModel>?,
      locationInfo: freezed == locationInfo
          ? _value.locationInfo
          : locationInfo // ignore: cast_nullable_to_non_nullable
              as LocationInfoModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SegmentsData implements _SegmentsData {
  const _$_SegmentsData(
      {this.modeType,
      this.courseInfo,
      this.classInfo,
      final List<ItemModel>? itemList,
      this.locationInfo})
      : _itemList = itemList;

  factory _$_SegmentsData.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentsDataFromJson(json);

  @override
  final int? modeType;
  @override
  final CourseInfoModel? courseInfo;
  @override
  final ClassInfoModel? classInfo;
  final List<ItemModel>? _itemList;
  @override
  List<ItemModel>? get itemList {
    final value = _itemList;
    if (value == null) return null;
    if (_itemList is EqualUnmodifiableListView) return _itemList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final LocationInfoModel? locationInfo;

  @override
  String toString() {
    return 'SegmentsData(modeType: $modeType, courseInfo: $courseInfo, classInfo: $classInfo, itemList: $itemList, locationInfo: $locationInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SegmentsData &&
            (identical(other.modeType, modeType) ||
                other.modeType == modeType) &&
            (identical(other.courseInfo, courseInfo) ||
                other.courseInfo == courseInfo) &&
            (identical(other.classInfo, classInfo) ||
                other.classInfo == classInfo) &&
            const DeepCollectionEquality().equals(other._itemList, _itemList) &&
            (identical(other.locationInfo, locationInfo) ||
                other.locationInfo == locationInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, modeType, courseInfo, classInfo,
      const DeepCollectionEquality().hash(_itemList), locationInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentsDataCopyWith<_$_SegmentsData> get copyWith =>
      __$$_SegmentsDataCopyWithImpl<_$_SegmentsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentsDataToJson(
      this,
    );
  }
}

abstract class _SegmentsData implements SegmentsData {
  const factory _SegmentsData(
      {final int? modeType,
      final CourseInfoModel? courseInfo,
      final ClassInfoModel? classInfo,
      final List<ItemModel>? itemList,
      final LocationInfoModel? locationInfo}) = _$_SegmentsData;

  factory _SegmentsData.fromJson(Map<String, dynamic> json) =
      _$_SegmentsData.fromJson;

  @override
  int? get modeType;
  @override
  CourseInfoModel? get courseInfo;
  @override
  ClassInfoModel? get classInfo;
  @override
  List<ItemModel>? get itemList;
  @override
  LocationInfoModel? get locationInfo;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentsDataCopyWith<_$_SegmentsData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfoModel _$CourseInfoModelFromJson(Map<String, dynamic> json) {
  return _CourseInfoModel.fromJson(json);
}

/// @nodoc
mixin _$CourseInfoModel {
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  int? get courseSecondaryType => throw _privateConstructorUsedError;
  int? get showSearch => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoModelCopyWith<CourseInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoModelCopyWith<$Res> {
  factory $CourseInfoModelCopyWith(
          CourseInfoModel value, $Res Function(CourseInfoModel) then) =
      _$CourseInfoModelCopyWithImpl<$Res, CourseInfoModel>;
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      String? courseSegment,
      int? courseSegmentCode,
      int? subjectType,
      int? courseSecondaryType,
      int? showSearch});
}

/// @nodoc
class _$CourseInfoModelCopyWithImpl<$Res, $Val extends CourseInfoModel>
    implements $CourseInfoModelCopyWith<$Res> {
  _$CourseInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? courseSegmentCode = freezed,
    Object? subjectType = freezed,
    Object? courseSecondaryType = freezed,
    Object? showSearch = freezed,
  }) {
    return _then(_value.copyWith(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSecondaryType: freezed == courseSecondaryType
          ? _value.courseSecondaryType
          : courseSecondaryType // ignore: cast_nullable_to_non_nullable
              as int?,
      showSearch: freezed == showSearch
          ? _value.showSearch
          : showSearch // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoModelCopyWith<$Res>
    implements $CourseInfoModelCopyWith<$Res> {
  factory _$$_CourseInfoModelCopyWith(
          _$_CourseInfoModel value, $Res Function(_$_CourseInfoModel) then) =
      __$$_CourseInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      String? courseSegment,
      int? courseSegmentCode,
      int? subjectType,
      int? courseSecondaryType,
      int? showSearch});
}

/// @nodoc
class __$$_CourseInfoModelCopyWithImpl<$Res>
    extends _$CourseInfoModelCopyWithImpl<$Res, _$_CourseInfoModel>
    implements _$$_CourseInfoModelCopyWith<$Res> {
  __$$_CourseInfoModelCopyWithImpl(
      _$_CourseInfoModel _value, $Res Function(_$_CourseInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? courseSegmentCode = freezed,
    Object? subjectType = freezed,
    Object? courseSecondaryType = freezed,
    Object? showSearch = freezed,
  }) {
    return _then(_$_CourseInfoModel(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSecondaryType: freezed == courseSecondaryType
          ? _value.courseSecondaryType
          : courseSecondaryType // ignore: cast_nullable_to_non_nullable
              as int?,
      showSearch: freezed == showSearch
          ? _value.showSearch
          : showSearch // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfoModel implements _CourseInfoModel {
  const _$_CourseInfoModel(
      {this.courseId,
      this.courseKey,
      this.courseName,
      this.courseSegment,
      this.courseSegmentCode,
      this.subjectType,
      this.courseSecondaryType,
      this.showSearch});

  factory _$_CourseInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoModelFromJson(json);

  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final String? courseSegment;
  @override
  final int? courseSegmentCode;
  @override
  final int? subjectType;
  @override
  final int? courseSecondaryType;
  @override
  final int? showSearch;

  @override
  String toString() {
    return 'CourseInfoModel(courseId: $courseId, courseKey: $courseKey, courseName: $courseName, courseSegment: $courseSegment, courseSegmentCode: $courseSegmentCode, subjectType: $subjectType, courseSecondaryType: $courseSecondaryType, showSearch: $showSearch)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfoModel &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.courseSegmentCode, courseSegmentCode) ||
                other.courseSegmentCode == courseSegmentCode) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.courseSecondaryType, courseSecondaryType) ||
                other.courseSecondaryType == courseSecondaryType) &&
            (identical(other.showSearch, showSearch) ||
                other.showSearch == showSearch));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseId,
      courseKey,
      courseName,
      courseSegment,
      courseSegmentCode,
      subjectType,
      courseSecondaryType,
      showSearch);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoModelCopyWith<_$_CourseInfoModel> get copyWith =>
      __$$_CourseInfoModelCopyWithImpl<_$_CourseInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoModelToJson(
      this,
    );
  }
}

abstract class _CourseInfoModel implements CourseInfoModel {
  const factory _CourseInfoModel(
      {final int? courseId,
      final String? courseKey,
      final String? courseName,
      final String? courseSegment,
      final int? courseSegmentCode,
      final int? subjectType,
      final int? courseSecondaryType,
      final int? showSearch}) = _$_CourseInfoModel;

  factory _CourseInfoModel.fromJson(Map<String, dynamic> json) =
      _$_CourseInfoModel.fromJson;

  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  String? get courseSegment;
  @override
  int? get courseSegmentCode;
  @override
  int? get subjectType;
  @override
  int? get courseSecondaryType;
  @override
  int? get showSearch;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoModelCopyWith<_$_CourseInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassInfoModel _$ClassInfoModelFromJson(Map<String, dynamic> json) {
  return _ClassInfoModel.fromJson(json);
}

/// @nodoc
mixin _$ClassInfoModel {
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  String? get className => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassInfoModelCopyWith<ClassInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassInfoModelCopyWith<$Res> {
  factory $ClassInfoModelCopyWith(
          ClassInfoModel value, $Res Function(ClassInfoModel) then) =
      _$ClassInfoModelCopyWithImpl<$Res, ClassInfoModel>;
  @useResult
  $Res call(
      {int? classId,
      String? classKey,
      String? className,
      int? teacherId,
      String? teacherName});
}

/// @nodoc
class _$ClassInfoModelCopyWithImpl<$Res, $Val extends ClassInfoModel>
    implements $ClassInfoModelCopyWith<$Res> {
  _$ClassInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassInfoModelCopyWith<$Res>
    implements $ClassInfoModelCopyWith<$Res> {
  factory _$$_ClassInfoModelCopyWith(
          _$_ClassInfoModel value, $Res Function(_$_ClassInfoModel) then) =
      __$$_ClassInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? classId,
      String? classKey,
      String? className,
      int? teacherId,
      String? teacherName});
}

/// @nodoc
class __$$_ClassInfoModelCopyWithImpl<$Res>
    extends _$ClassInfoModelCopyWithImpl<$Res, _$_ClassInfoModel>
    implements _$$_ClassInfoModelCopyWith<$Res> {
  __$$_ClassInfoModelCopyWithImpl(
      _$_ClassInfoModel _value, $Res Function(_$_ClassInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? className = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_$_ClassInfoModel(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      className: freezed == className
          ? _value.className
          : className // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassInfoModel implements _ClassInfoModel {
  const _$_ClassInfoModel(
      {this.classId,
      this.classKey,
      this.className,
      this.teacherId,
      this.teacherName});

  factory _$_ClassInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_ClassInfoModelFromJson(json);

  @override
  final int? classId;
  @override
  final String? classKey;
  @override
  final String? className;
  @override
  final int? teacherId;
  @override
  final String? teacherName;

  @override
  String toString() {
    return 'ClassInfoModel(classId: $classId, classKey: $classKey, className: $className, teacherId: $teacherId, teacherName: $teacherName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassInfoModel &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.className, className) ||
                other.className == className) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, classId, classKey, className, teacherId, teacherName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassInfoModelCopyWith<_$_ClassInfoModel> get copyWith =>
      __$$_ClassInfoModelCopyWithImpl<_$_ClassInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassInfoModelToJson(
      this,
    );
  }
}

abstract class _ClassInfoModel implements ClassInfoModel {
  const factory _ClassInfoModel(
      {final int? classId,
      final String? classKey,
      final String? className,
      final int? teacherId,
      final String? teacherName}) = _$_ClassInfoModel;

  factory _ClassInfoModel.fromJson(Map<String, dynamic> json) =
      _$_ClassInfoModel.fromJson;

  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  String? get className;
  @override
  int? get teacherId;
  @override
  String? get teacherName;
  @override
  @JsonKey(ignore: true)
  _$$_ClassInfoModelCopyWith<_$_ClassInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

ItemModel _$ItemModelFromJson(Map<String, dynamic> json) {
  return _ItemModel.fromJson(json);
}

/// @nodoc
mixin _$ItemModel {
  String? get type => throw _privateConstructorUsedError;
  SegmentInfoModel? get segmentInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ItemModelCopyWith<ItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemModelCopyWith<$Res> {
  factory $ItemModelCopyWith(ItemModel value, $Res Function(ItemModel) then) =
      _$ItemModelCopyWithImpl<$Res, ItemModel>;
  @useResult
  $Res call({String? type, SegmentInfoModel? segmentInfo});

  $SegmentInfoModelCopyWith<$Res>? get segmentInfo;
}

/// @nodoc
class _$ItemModelCopyWithImpl<$Res, $Val extends ItemModel>
    implements $ItemModelCopyWith<$Res> {
  _$ItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? segmentInfo = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentInfo: freezed == segmentInfo
          ? _value.segmentInfo
          : segmentInfo // ignore: cast_nullable_to_non_nullable
              as SegmentInfoModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SegmentInfoModelCopyWith<$Res>? get segmentInfo {
    if (_value.segmentInfo == null) {
      return null;
    }

    return $SegmentInfoModelCopyWith<$Res>(_value.segmentInfo!, (value) {
      return _then(_value.copyWith(segmentInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ItemModelCopyWith<$Res> implements $ItemModelCopyWith<$Res> {
  factory _$$_ItemModelCopyWith(
          _$_ItemModel value, $Res Function(_$_ItemModel) then) =
      __$$_ItemModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? type, SegmentInfoModel? segmentInfo});

  @override
  $SegmentInfoModelCopyWith<$Res>? get segmentInfo;
}

/// @nodoc
class __$$_ItemModelCopyWithImpl<$Res>
    extends _$ItemModelCopyWithImpl<$Res, _$_ItemModel>
    implements _$$_ItemModelCopyWith<$Res> {
  __$$_ItemModelCopyWithImpl(
      _$_ItemModel _value, $Res Function(_$_ItemModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? segmentInfo = freezed,
  }) {
    return _then(_$_ItemModel(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentInfo: freezed == segmentInfo
          ? _value.segmentInfo
          : segmentInfo // ignore: cast_nullable_to_non_nullable
              as SegmentInfoModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ItemModel implements _ItemModel {
  const _$_ItemModel({this.type, this.segmentInfo});

  factory _$_ItemModel.fromJson(Map<String, dynamic> json) =>
      _$$_ItemModelFromJson(json);

  @override
  final String? type;
  @override
  final SegmentInfoModel? segmentInfo;

  @override
  String toString() {
    return 'ItemModel(type: $type, segmentInfo: $segmentInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ItemModel &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.segmentInfo, segmentInfo) ||
                other.segmentInfo == segmentInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, segmentInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ItemModelCopyWith<_$_ItemModel> get copyWith =>
      __$$_ItemModelCopyWithImpl<_$_ItemModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ItemModelToJson(
      this,
    );
  }
}

abstract class _ItemModel implements ItemModel {
  const factory _ItemModel(
      {final String? type, final SegmentInfoModel? segmentInfo}) = _$_ItemModel;

  factory _ItemModel.fromJson(Map<String, dynamic> json) =
      _$_ItemModel.fromJson;

  @override
  String? get type;
  @override
  SegmentInfoModel? get segmentInfo;
  @override
  @JsonKey(ignore: true)
  _$$_ItemModelCopyWith<_$_ItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

SegmentInfoModel _$SegmentInfoModelFromJson(Map<String, dynamic> json) {
  return _SegmentInfoModel.fromJson(json);
}

/// @nodoc
mixin _$SegmentInfoModel {
  int? get segmentId => throw _privateConstructorUsedError;
  String? get segmentKey => throw _privateConstructorUsedError;
  String? get segmentOrderDes => throw _privateConstructorUsedError;
  int? get segmentOrder => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get segmentDescription => throw _privateConstructorUsedError;
  String? get labelName => throw _privateConstructorUsedError;
  int? get labelCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SegmentInfoModelCopyWith<SegmentInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SegmentInfoModelCopyWith<$Res> {
  factory $SegmentInfoModelCopyWith(
          SegmentInfoModel value, $Res Function(SegmentInfoModel) then) =
      _$SegmentInfoModelCopyWithImpl<$Res, SegmentInfoModel>;
  @useResult
  $Res call(
      {int? segmentId,
      String? segmentKey,
      String? segmentOrderDes,
      int? segmentOrder,
      String? segmentName,
      String? segmentDescription,
      String? labelName,
      int? labelCode});
}

/// @nodoc
class _$SegmentInfoModelCopyWithImpl<$Res, $Val extends SegmentInfoModel>
    implements $SegmentInfoModelCopyWith<$Res> {
  _$SegmentInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? segmentKey = freezed,
    Object? segmentOrderDes = freezed,
    Object? segmentOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentDescription = freezed,
    Object? labelName = freezed,
    Object? labelCode = freezed,
  }) {
    return _then(_value.copyWith(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrderDes: freezed == segmentOrderDes
          ? _value.segmentOrderDes
          : segmentOrderDes // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrder: freezed == segmentOrder
          ? _value.segmentOrder
          : segmentOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentDescription: freezed == segmentDescription
          ? _value.segmentDescription
          : segmentDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      labelName: freezed == labelName
          ? _value.labelName
          : labelName // ignore: cast_nullable_to_non_nullable
              as String?,
      labelCode: freezed == labelCode
          ? _value.labelCode
          : labelCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SegmentInfoModelCopyWith<$Res>
    implements $SegmentInfoModelCopyWith<$Res> {
  factory _$$_SegmentInfoModelCopyWith(
          _$_SegmentInfoModel value, $Res Function(_$_SegmentInfoModel) then) =
      __$$_SegmentInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? segmentId,
      String? segmentKey,
      String? segmentOrderDes,
      int? segmentOrder,
      String? segmentName,
      String? segmentDescription,
      String? labelName,
      int? labelCode});
}

/// @nodoc
class __$$_SegmentInfoModelCopyWithImpl<$Res>
    extends _$SegmentInfoModelCopyWithImpl<$Res, _$_SegmentInfoModel>
    implements _$$_SegmentInfoModelCopyWith<$Res> {
  __$$_SegmentInfoModelCopyWithImpl(
      _$_SegmentInfoModel _value, $Res Function(_$_SegmentInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? segmentKey = freezed,
    Object? segmentOrderDes = freezed,
    Object? segmentOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentDescription = freezed,
    Object? labelName = freezed,
    Object? labelCode = freezed,
  }) {
    return _then(_$_SegmentInfoModel(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentKey: freezed == segmentKey
          ? _value.segmentKey
          : segmentKey // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrderDes: freezed == segmentOrderDes
          ? _value.segmentOrderDes
          : segmentOrderDes // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentOrder: freezed == segmentOrder
          ? _value.segmentOrder
          : segmentOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentDescription: freezed == segmentDescription
          ? _value.segmentDescription
          : segmentDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      labelName: freezed == labelName
          ? _value.labelName
          : labelName // ignore: cast_nullable_to_non_nullable
              as String?,
      labelCode: freezed == labelCode
          ? _value.labelCode
          : labelCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SegmentInfoModel implements _SegmentInfoModel {
  const _$_SegmentInfoModel(
      {this.segmentId,
      this.segmentKey,
      this.segmentOrderDes,
      this.segmentOrder,
      this.segmentName,
      this.segmentDescription,
      this.labelName,
      this.labelCode});

  factory _$_SegmentInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_SegmentInfoModelFromJson(json);

  @override
  final int? segmentId;
  @override
  final String? segmentKey;
  @override
  final String? segmentOrderDes;
  @override
  final int? segmentOrder;
  @override
  final String? segmentName;
  @override
  final String? segmentDescription;
  @override
  final String? labelName;
  @override
  final int? labelCode;

  @override
  String toString() {
    return 'SegmentInfoModel(segmentId: $segmentId, segmentKey: $segmentKey, segmentOrderDes: $segmentOrderDes, segmentOrder: $segmentOrder, segmentName: $segmentName, segmentDescription: $segmentDescription, labelName: $labelName, labelCode: $labelCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SegmentInfoModel &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentKey, segmentKey) ||
                other.segmentKey == segmentKey) &&
            (identical(other.segmentOrderDes, segmentOrderDes) ||
                other.segmentOrderDes == segmentOrderDes) &&
            (identical(other.segmentOrder, segmentOrder) ||
                other.segmentOrder == segmentOrder) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.segmentDescription, segmentDescription) ||
                other.segmentDescription == segmentDescription) &&
            (identical(other.labelName, labelName) ||
                other.labelName == labelName) &&
            (identical(other.labelCode, labelCode) ||
                other.labelCode == labelCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      segmentId,
      segmentKey,
      segmentOrderDes,
      segmentOrder,
      segmentName,
      segmentDescription,
      labelName,
      labelCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SegmentInfoModelCopyWith<_$_SegmentInfoModel> get copyWith =>
      __$$_SegmentInfoModelCopyWithImpl<_$_SegmentInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SegmentInfoModelToJson(
      this,
    );
  }
}

abstract class _SegmentInfoModel implements SegmentInfoModel {
  const factory _SegmentInfoModel(
      {final int? segmentId,
      final String? segmentKey,
      final String? segmentOrderDes,
      final int? segmentOrder,
      final String? segmentName,
      final String? segmentDescription,
      final String? labelName,
      final int? labelCode}) = _$_SegmentInfoModel;

  factory _SegmentInfoModel.fromJson(Map<String, dynamic> json) =
      _$_SegmentInfoModel.fromJson;

  @override
  int? get segmentId;
  @override
  String? get segmentKey;
  @override
  String? get segmentOrderDes;
  @override
  int? get segmentOrder;
  @override
  String? get segmentName;
  @override
  String? get segmentDescription;
  @override
  String? get labelName;
  @override
  int? get labelCode;
  @override
  @JsonKey(ignore: true)
  _$$_SegmentInfoModelCopyWith<_$_SegmentInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationInfoModel _$LocationInfoModelFromJson(Map<String, dynamic> json) {
  return _LocationInfoModel.fromJson(json);
}

/// @nodoc
mixin _$LocationInfoModel {
  int? get segmentId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocationInfoModelCopyWith<LocationInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationInfoModelCopyWith<$Res> {
  factory $LocationInfoModelCopyWith(
          LocationInfoModel value, $Res Function(LocationInfoModel) then) =
      _$LocationInfoModelCopyWithImpl<$Res, LocationInfoModel>;
  @useResult
  $Res call({int? segmentId});
}

/// @nodoc
class _$LocationInfoModelCopyWithImpl<$Res, $Val extends LocationInfoModel>
    implements $LocationInfoModelCopyWith<$Res> {
  _$LocationInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
  }) {
    return _then(_value.copyWith(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LocationInfoModelCopyWith<$Res>
    implements $LocationInfoModelCopyWith<$Res> {
  factory _$$_LocationInfoModelCopyWith(_$_LocationInfoModel value,
          $Res Function(_$_LocationInfoModel) then) =
      __$$_LocationInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? segmentId});
}

/// @nodoc
class __$$_LocationInfoModelCopyWithImpl<$Res>
    extends _$LocationInfoModelCopyWithImpl<$Res, _$_LocationInfoModel>
    implements _$$_LocationInfoModelCopyWith<$Res> {
  __$$_LocationInfoModelCopyWithImpl(
      _$_LocationInfoModel _value, $Res Function(_$_LocationInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
  }) {
    return _then(_$_LocationInfoModel(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LocationInfoModel implements _LocationInfoModel {
  const _$_LocationInfoModel({this.segmentId});

  factory _$_LocationInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_LocationInfoModelFromJson(json);

  @override
  final int? segmentId;

  @override
  String toString() {
    return 'LocationInfoModel(segmentId: $segmentId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LocationInfoModel &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, segmentId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LocationInfoModelCopyWith<_$_LocationInfoModel> get copyWith =>
      __$$_LocationInfoModelCopyWithImpl<_$_LocationInfoModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LocationInfoModelToJson(
      this,
    );
  }
}

abstract class _LocationInfoModel implements LocationInfoModel {
  const factory _LocationInfoModel({final int? segmentId}) =
      _$_LocationInfoModel;

  factory _LocationInfoModel.fromJson(Map<String, dynamic> json) =
      _$_LocationInfoModel.fromJson;

  @override
  int? get segmentId;
  @override
  @JsonKey(ignore: true)
  _$$_LocationInfoModelCopyWith<_$_LocationInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}
