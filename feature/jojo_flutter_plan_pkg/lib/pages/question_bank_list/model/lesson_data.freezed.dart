// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonData _$LessonDataFromJson(Map<String, dynamic> json) {
  return _LessonData.fromJson(json);
}

/// @nodoc
mixin _$LessonData {
  String? get lessonTitle => throw _privateConstructorUsedError;
  String? get lessonSubTitle => throw _privateConstructorUsedError;
  String? get lessonOrder => throw _privateConstructorUsedError;
  String? get order => throw _privateConstructorUsedError;
  String? get weekId => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;
  String? get segmentId => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get today => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonDataCopyWith<LessonData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonDataCopyWith<$Res> {
  factory $LessonDataCopyWith(
          LessonData value, $Res Function(LessonData) then) =
      _$LessonDataCopyWithImpl<$Res, LessonData>;
  @useResult
  $Res call(
      {String? lessonTitle,
      String? lessonSubTitle,
      String? lessonOrder,
      String? order,
      String? weekId,
      String? weekName,
      String? segmentId,
      String? segmentName,
      String? today});
}

/// @nodoc
class _$LessonDataCopyWithImpl<$Res, $Val extends LessonData>
    implements $LessonDataCopyWith<$Res> {
  _$LessonDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonTitle = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrder = freezed,
    Object? order = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
  }) {
    return _then(_value.copyWith(
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as String?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as String?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonDataCopyWith<$Res>
    implements $LessonDataCopyWith<$Res> {
  factory _$$_LessonDataCopyWith(
          _$_LessonData value, $Res Function(_$_LessonData) then) =
      __$$_LessonDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lessonTitle,
      String? lessonSubTitle,
      String? lessonOrder,
      String? order,
      String? weekId,
      String? weekName,
      String? segmentId,
      String? segmentName,
      String? today});
}

/// @nodoc
class __$$_LessonDataCopyWithImpl<$Res>
    extends _$LessonDataCopyWithImpl<$Res, _$_LessonData>
    implements _$$_LessonDataCopyWith<$Res> {
  __$$_LessonDataCopyWithImpl(
      _$_LessonData _value, $Res Function(_$_LessonData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonTitle = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrder = freezed,
    Object? order = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
  }) {
    return _then(_$_LessonData(
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as String?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as String?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonData implements _LessonData {
  const _$_LessonData(
      {this.lessonTitle,
      this.lessonSubTitle,
      this.lessonOrder,
      this.order,
      this.weekId,
      this.weekName,
      this.segmentId,
      this.segmentName,
      this.today});

  factory _$_LessonData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonDataFromJson(json);

  @override
  final String? lessonTitle;
  @override
  final String? lessonSubTitle;
  @override
  final String? lessonOrder;
  @override
  final String? order;
  @override
  final String? weekId;
  @override
  final String? weekName;
  @override
  final String? segmentId;
  @override
  final String? segmentName;
  @override
  final String? today;

  @override
  String toString() {
    return 'LessonData(lessonTitle: $lessonTitle, lessonSubTitle: $lessonSubTitle, lessonOrder: $lessonOrder, order: $order, weekId: $weekId, weekName: $weekName, segmentId: $segmentId, segmentName: $segmentName, today: $today)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonData &&
            (identical(other.lessonTitle, lessonTitle) ||
                other.lessonTitle == lessonTitle) &&
            (identical(other.lessonSubTitle, lessonSubTitle) ||
                other.lessonSubTitle == lessonSubTitle) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.weekName, weekName) ||
                other.weekName == weekName) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.today, today) || other.today == today));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lessonTitle, lessonSubTitle,
      lessonOrder, order, weekId, weekName, segmentId, segmentName, today);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonDataCopyWith<_$_LessonData> get copyWith =>
      __$$_LessonDataCopyWithImpl<_$_LessonData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonDataToJson(
      this,
    );
  }
}

abstract class _LessonData implements LessonData {
  const factory _LessonData(
      {final String? lessonTitle,
      final String? lessonSubTitle,
      final String? lessonOrder,
      final String? order,
      final String? weekId,
      final String? weekName,
      final String? segmentId,
      final String? segmentName,
      final String? today}) = _$_LessonData;

  factory _LessonData.fromJson(Map<String, dynamic> json) =
      _$_LessonData.fromJson;

  @override
  String? get lessonTitle;
  @override
  String? get lessonSubTitle;
  @override
  String? get lessonOrder;
  @override
  String? get order;
  @override
  String? get weekId;
  @override
  String? get weekName;
  @override
  String? get segmentId;
  @override
  String? get segmentName;
  @override
  String? get today;
  @override
  @JsonKey(ignore: true)
  _$$_LessonDataCopyWith<_$_LessonData> get copyWith =>
      throw _privateConstructorUsedError;
}
