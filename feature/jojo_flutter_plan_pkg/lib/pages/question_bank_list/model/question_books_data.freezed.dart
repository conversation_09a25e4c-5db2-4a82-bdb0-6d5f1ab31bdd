// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'question_books_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

QuestionBooksModel _$QuestionBooksModelFromJson(Map<String, dynamic> json) {
  return _QuestionBooksModel.fromJson(json);
}

/// @nodoc
mixin _$QuestionBooksModel {
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  int? get fontPack => throw _privateConstructorUsedError;
  bool? get isShowEliminateAllButton => throw _privateConstructorUsedError;
  String? get offset => throw _privateConstructorUsedError;
  int? get totalQuestionCount => throw _privateConstructorUsedError;
  List<UnitModel>? get units => throw _privateConstructorUsedError;
  List<LessonModel>? get lessons => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuestionBooksModelCopyWith<QuestionBooksModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuestionBooksModelCopyWith<$Res> {
  factory $QuestionBooksModelCopyWith(
          QuestionBooksModel value, $Res Function(QuestionBooksModel) then) =
      _$QuestionBooksModelCopyWithImpl<$Res, QuestionBooksModel>;
  @useResult
  $Res call(
      {String? courseKey,
      String? courseName,
      int? fontPack,
      bool? isShowEliminateAllButton,
      String? offset,
      int? totalQuestionCount,
      List<UnitModel>? units,
      List<LessonModel>? lessons});
}

/// @nodoc
class _$QuestionBooksModelCopyWithImpl<$Res, $Val extends QuestionBooksModel>
    implements $QuestionBooksModelCopyWith<$Res> {
  _$QuestionBooksModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? fontPack = freezed,
    Object? isShowEliminateAllButton = freezed,
    Object? offset = freezed,
    Object? totalQuestionCount = freezed,
    Object? units = freezed,
    Object? lessons = freezed,
  }) {
    return _then(_value.copyWith(
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      fontPack: freezed == fontPack
          ? _value.fontPack
          : fontPack // ignore: cast_nullable_to_non_nullable
              as int?,
      isShowEliminateAllButton: freezed == isShowEliminateAllButton
          ? _value.isShowEliminateAllButton
          : isShowEliminateAllButton // ignore: cast_nullable_to_non_nullable
              as bool?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as String?,
      totalQuestionCount: freezed == totalQuestionCount
          ? _value.totalQuestionCount
          : totalQuestionCount // ignore: cast_nullable_to_non_nullable
              as int?,
      units: freezed == units
          ? _value.units
          : units // ignore: cast_nullable_to_non_nullable
              as List<UnitModel>?,
      lessons: freezed == lessons
          ? _value.lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<LessonModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_QuestionBooksModelCopyWith<$Res>
    implements $QuestionBooksModelCopyWith<$Res> {
  factory _$$_QuestionBooksModelCopyWith(_$_QuestionBooksModel value,
          $Res Function(_$_QuestionBooksModel) then) =
      __$$_QuestionBooksModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseKey,
      String? courseName,
      int? fontPack,
      bool? isShowEliminateAllButton,
      String? offset,
      int? totalQuestionCount,
      List<UnitModel>? units,
      List<LessonModel>? lessons});
}

/// @nodoc
class __$$_QuestionBooksModelCopyWithImpl<$Res>
    extends _$QuestionBooksModelCopyWithImpl<$Res, _$_QuestionBooksModel>
    implements _$$_QuestionBooksModelCopyWith<$Res> {
  __$$_QuestionBooksModelCopyWithImpl(
      _$_QuestionBooksModel _value, $Res Function(_$_QuestionBooksModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? fontPack = freezed,
    Object? isShowEliminateAllButton = freezed,
    Object? offset = freezed,
    Object? totalQuestionCount = freezed,
    Object? units = freezed,
    Object? lessons = freezed,
  }) {
    return _then(_$_QuestionBooksModel(
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      fontPack: freezed == fontPack
          ? _value.fontPack
          : fontPack // ignore: cast_nullable_to_non_nullable
              as int?,
      isShowEliminateAllButton: freezed == isShowEliminateAllButton
          ? _value.isShowEliminateAllButton
          : isShowEliminateAllButton // ignore: cast_nullable_to_non_nullable
              as bool?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as String?,
      totalQuestionCount: freezed == totalQuestionCount
          ? _value.totalQuestionCount
          : totalQuestionCount // ignore: cast_nullable_to_non_nullable
              as int?,
      units: freezed == units
          ? _value._units
          : units // ignore: cast_nullable_to_non_nullable
              as List<UnitModel>?,
      lessons: freezed == lessons
          ? _value._lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<LessonModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_QuestionBooksModel implements _QuestionBooksModel {
  const _$_QuestionBooksModel(
      {this.courseKey,
      this.courseName,
      this.fontPack,
      this.isShowEliminateAllButton,
      this.offset,
      this.totalQuestionCount,
      final List<UnitModel>? units,
      final List<LessonModel>? lessons})
      : _units = units,
        _lessons = lessons;

  factory _$_QuestionBooksModel.fromJson(Map<String, dynamic> json) =>
      _$$_QuestionBooksModelFromJson(json);

  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final int? fontPack;
  @override
  final bool? isShowEliminateAllButton;
  @override
  final String? offset;
  @override
  final int? totalQuestionCount;
  final List<UnitModel>? _units;
  @override
  List<UnitModel>? get units {
    final value = _units;
    if (value == null) return null;
    if (_units is EqualUnmodifiableListView) return _units;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<LessonModel>? _lessons;
  @override
  List<LessonModel>? get lessons {
    final value = _lessons;
    if (value == null) return null;
    if (_lessons is EqualUnmodifiableListView) return _lessons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'QuestionBooksModel(courseKey: $courseKey, courseName: $courseName, fontPack: $fontPack, isShowEliminateAllButton: $isShowEliminateAllButton, offset: $offset, totalQuestionCount: $totalQuestionCount, units: $units, lessons: $lessons)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_QuestionBooksModel &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.fontPack, fontPack) ||
                other.fontPack == fontPack) &&
            (identical(
                    other.isShowEliminateAllButton, isShowEliminateAllButton) ||
                other.isShowEliminateAllButton == isShowEliminateAllButton) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.totalQuestionCount, totalQuestionCount) ||
                other.totalQuestionCount == totalQuestionCount) &&
            const DeepCollectionEquality().equals(other._units, _units) &&
            const DeepCollectionEquality().equals(other._lessons, _lessons));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseKey,
      courseName,
      fontPack,
      isShowEliminateAllButton,
      offset,
      totalQuestionCount,
      const DeepCollectionEquality().hash(_units),
      const DeepCollectionEquality().hash(_lessons));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_QuestionBooksModelCopyWith<_$_QuestionBooksModel> get copyWith =>
      __$$_QuestionBooksModelCopyWithImpl<_$_QuestionBooksModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_QuestionBooksModelToJson(
      this,
    );
  }
}

abstract class _QuestionBooksModel implements QuestionBooksModel {
  const factory _QuestionBooksModel(
      {final String? courseKey,
      final String? courseName,
      final int? fontPack,
      final bool? isShowEliminateAllButton,
      final String? offset,
      final int? totalQuestionCount,
      final List<UnitModel>? units,
      final List<LessonModel>? lessons}) = _$_QuestionBooksModel;

  factory _QuestionBooksModel.fromJson(Map<String, dynamic> json) =
      _$_QuestionBooksModel.fromJson;

  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  int? get fontPack;
  @override
  bool? get isShowEliminateAllButton;
  @override
  String? get offset;
  @override
  int? get totalQuestionCount;
  @override
  List<UnitModel>? get units;
  @override
  List<LessonModel>? get lessons;
  @override
  @JsonKey(ignore: true)
  _$$_QuestionBooksModelCopyWith<_$_QuestionBooksModel> get copyWith =>
      throw _privateConstructorUsedError;
}

UnitModel _$UnitModelFromJson(Map<String, dynamic> json) {
  return _UnitModel.fromJson(json);
}

/// @nodoc
mixin _$UnitModel {
  String? get classKey => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get month => throw _privateConstructorUsedError;
  int? get startTimestamp => throw _privateConstructorUsedError;
  String? get unitName => throw _privateConstructorUsedError;
  String? get unitId => throw _privateConstructorUsedError;
  int? get questionCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UnitModelCopyWith<UnitModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnitModelCopyWith<$Res> {
  factory $UnitModelCopyWith(UnitModel value, $Res Function(UnitModel) then) =
      _$UnitModelCopyWithImpl<$Res, UnitModel>;
  @useResult
  $Res call(
      {String? classKey,
      int? classId,
      String? month,
      int? startTimestamp,
      String? unitName,
      String? unitId,
      int? questionCount});
}

/// @nodoc
class _$UnitModelCopyWithImpl<$Res, $Val extends UnitModel>
    implements $UnitModelCopyWith<$Res> {
  _$UnitModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? classId = freezed,
    Object? month = freezed,
    Object? startTimestamp = freezed,
    Object? unitName = freezed,
    Object? unitId = freezed,
    Object? questionCount = freezed,
  }) {
    return _then(_value.copyWith(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      startTimestamp: freezed == startTimestamp
          ? _value.startTimestamp
          : startTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      unitName: freezed == unitName
          ? _value.unitName
          : unitName // ignore: cast_nullable_to_non_nullable
              as String?,
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      questionCount: freezed == questionCount
          ? _value.questionCount
          : questionCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UnitModelCopyWith<$Res> implements $UnitModelCopyWith<$Res> {
  factory _$$_UnitModelCopyWith(
          _$_UnitModel value, $Res Function(_$_UnitModel) then) =
      __$$_UnitModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? classKey,
      int? classId,
      String? month,
      int? startTimestamp,
      String? unitName,
      String? unitId,
      int? questionCount});
}

/// @nodoc
class __$$_UnitModelCopyWithImpl<$Res>
    extends _$UnitModelCopyWithImpl<$Res, _$_UnitModel>
    implements _$$_UnitModelCopyWith<$Res> {
  __$$_UnitModelCopyWithImpl(
      _$_UnitModel _value, $Res Function(_$_UnitModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? classId = freezed,
    Object? month = freezed,
    Object? startTimestamp = freezed,
    Object? unitName = freezed,
    Object? unitId = freezed,
    Object? questionCount = freezed,
  }) {
    return _then(_$_UnitModel(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      startTimestamp: freezed == startTimestamp
          ? _value.startTimestamp
          : startTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
      unitName: freezed == unitName
          ? _value.unitName
          : unitName // ignore: cast_nullable_to_non_nullable
              as String?,
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      questionCount: freezed == questionCount
          ? _value.questionCount
          : questionCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UnitModel implements _UnitModel {
  const _$_UnitModel(
      {this.classKey,
      this.classId,
      this.month,
      this.startTimestamp,
      this.unitName,
      this.unitId,
      this.questionCount});

  factory _$_UnitModel.fromJson(Map<String, dynamic> json) =>
      _$$_UnitModelFromJson(json);

  @override
  final String? classKey;
  @override
  final int? classId;
  @override
  final String? month;
  @override
  final int? startTimestamp;
  @override
  final String? unitName;
  @override
  final String? unitId;
  @override
  final int? questionCount;

  @override
  String toString() {
    return 'UnitModel(classKey: $classKey, classId: $classId, month: $month, startTimestamp: $startTimestamp, unitName: $unitName, unitId: $unitId, questionCount: $questionCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UnitModel &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.startTimestamp, startTimestamp) ||
                other.startTimestamp == startTimestamp) &&
            (identical(other.unitName, unitName) ||
                other.unitName == unitName) &&
            (identical(other.unitId, unitId) || other.unitId == unitId) &&
            (identical(other.questionCount, questionCount) ||
                other.questionCount == questionCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, classKey, classId, month,
      startTimestamp, unitName, unitId, questionCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UnitModelCopyWith<_$_UnitModel> get copyWith =>
      __$$_UnitModelCopyWithImpl<_$_UnitModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UnitModelToJson(
      this,
    );
  }
}

abstract class _UnitModel implements UnitModel {
  const factory _UnitModel(
      {final String? classKey,
      final int? classId,
      final String? month,
      final int? startTimestamp,
      final String? unitName,
      final String? unitId,
      final int? questionCount}) = _$_UnitModel;

  factory _UnitModel.fromJson(Map<String, dynamic> json) =
      _$_UnitModel.fromJson;

  @override
  String? get classKey;
  @override
  int? get classId;
  @override
  String? get month;
  @override
  int? get startTimestamp;
  @override
  String? get unitName;
  @override
  String? get unitId;
  @override
  int? get questionCount;
  @override
  @JsonKey(ignore: true)
  _$$_UnitModelCopyWith<_$_UnitModel> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonModel _$LessonModelFromJson(Map<String, dynamic> json) {
  return _LessonModel.fromJson(json);
}

/// @nodoc
mixin _$LessonModel {
  int? get id => throw _privateConstructorUsedError;
  set id(int? value) => throw _privateConstructorUsedError;
  String? get key => throw _privateConstructorUsedError;
  set key(String? value) => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  set name(String? value) => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  set subTitle(String? value) => throw _privateConstructorUsedError;
  int? get questionCount => throw _privateConstructorUsedError;
  set questionCount(int? value) => throw _privateConstructorUsedError;
  bool? get showPoint => throw _privateConstructorUsedError;
  set showPoint(bool? value) => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  set status(String? value) => throw _privateConstructorUsedError;
  int? get lessonStartTime => throw _privateConstructorUsedError;
  set lessonStartTime(int? value) => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;
  int? get nativeErrorQuestion => throw _privateConstructorUsedError;
  set nativeErrorQuestion(int? value) => throw _privateConstructorUsedError;
  String? get unitId => throw _privateConstructorUsedError;
  set unitId(String? value) => throw _privateConstructorUsedError;
  int? get lessonOrder => throw _privateConstructorUsedError;
  set lessonOrder(int? value) => throw _privateConstructorUsedError;
  int? get finishTime => throw _privateConstructorUsedError;
  set finishTime(int? value) => throw _privateConstructorUsedError;
  int? get lastErrorTime => throw _privateConstructorUsedError;
  set lastErrorTime(int? value) => throw _privateConstructorUsedError;
  String? get teachType => throw _privateConstructorUsedError;
  set teachType(String? value) => throw _privateConstructorUsedError;
  String? get aggregationType => throw _privateConstructorUsedError;
  set aggregationType(String? value) => throw _privateConstructorUsedError;
  int? get fontPack => throw _privateConstructorUsedError;
  set fontPack(int? value) => throw _privateConstructorUsedError;
  bool? get checked => throw _privateConstructorUsedError;
  set checked(bool? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonModelCopyWith<LessonModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonModelCopyWith<$Res> {
  factory $LessonModelCopyWith(
          LessonModel value, $Res Function(LessonModel) then) =
      _$LessonModelCopyWithImpl<$Res, LessonModel>;
  @useResult
  $Res call(
      {int? id,
      String? key,
      String? name,
      String? subTitle,
      int? questionCount,
      bool? showPoint,
      String? status,
      int? lessonStartTime,
      String? route,
      int? nativeErrorQuestion,
      String? unitId,
      int? lessonOrder,
      int? finishTime,
      int? lastErrorTime,
      String? teachType,
      String? aggregationType,
      int? fontPack,
      bool? checked});
}

/// @nodoc
class _$LessonModelCopyWithImpl<$Res, $Val extends LessonModel>
    implements $LessonModelCopyWith<$Res> {
  _$LessonModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? key = freezed,
    Object? name = freezed,
    Object? subTitle = freezed,
    Object? questionCount = freezed,
    Object? showPoint = freezed,
    Object? status = freezed,
    Object? lessonStartTime = freezed,
    Object? route = freezed,
    Object? nativeErrorQuestion = freezed,
    Object? unitId = freezed,
    Object? lessonOrder = freezed,
    Object? finishTime = freezed,
    Object? lastErrorTime = freezed,
    Object? teachType = freezed,
    Object? aggregationType = freezed,
    Object? fontPack = freezed,
    Object? checked = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      questionCount: freezed == questionCount
          ? _value.questionCount
          : questionCount // ignore: cast_nullable_to_non_nullable
              as int?,
      showPoint: freezed == showPoint
          ? _value.showPoint
          : showPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStartTime: freezed == lessonStartTime
          ? _value.lessonStartTime
          : lessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      nativeErrorQuestion: freezed == nativeErrorQuestion
          ? _value.nativeErrorQuestion
          : nativeErrorQuestion // ignore: cast_nullable_to_non_nullable
              as int?,
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lastErrorTime: freezed == lastErrorTime
          ? _value.lastErrorTime
          : lastErrorTime // ignore: cast_nullable_to_non_nullable
              as int?,
      teachType: freezed == teachType
          ? _value.teachType
          : teachType // ignore: cast_nullable_to_non_nullable
              as String?,
      aggregationType: freezed == aggregationType
          ? _value.aggregationType
          : aggregationType // ignore: cast_nullable_to_non_nullable
              as String?,
      fontPack: freezed == fontPack
          ? _value.fontPack
          : fontPack // ignore: cast_nullable_to_non_nullable
              as int?,
      checked: freezed == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonModelCopyWith<$Res>
    implements $LessonModelCopyWith<$Res> {
  factory _$$_LessonModelCopyWith(
          _$_LessonModel value, $Res Function(_$_LessonModel) then) =
      __$$_LessonModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? key,
      String? name,
      String? subTitle,
      int? questionCount,
      bool? showPoint,
      String? status,
      int? lessonStartTime,
      String? route,
      int? nativeErrorQuestion,
      String? unitId,
      int? lessonOrder,
      int? finishTime,
      int? lastErrorTime,
      String? teachType,
      String? aggregationType,
      int? fontPack,
      bool? checked});
}

/// @nodoc
class __$$_LessonModelCopyWithImpl<$Res>
    extends _$LessonModelCopyWithImpl<$Res, _$_LessonModel>
    implements _$$_LessonModelCopyWith<$Res> {
  __$$_LessonModelCopyWithImpl(
      _$_LessonModel _value, $Res Function(_$_LessonModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? key = freezed,
    Object? name = freezed,
    Object? subTitle = freezed,
    Object? questionCount = freezed,
    Object? showPoint = freezed,
    Object? status = freezed,
    Object? lessonStartTime = freezed,
    Object? route = freezed,
    Object? nativeErrorQuestion = freezed,
    Object? unitId = freezed,
    Object? lessonOrder = freezed,
    Object? finishTime = freezed,
    Object? lastErrorTime = freezed,
    Object? teachType = freezed,
    Object? aggregationType = freezed,
    Object? fontPack = freezed,
    Object? checked = freezed,
  }) {
    return _then(_$_LessonModel(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      questionCount: freezed == questionCount
          ? _value.questionCount
          : questionCount // ignore: cast_nullable_to_non_nullable
              as int?,
      showPoint: freezed == showPoint
          ? _value.showPoint
          : showPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStartTime: freezed == lessonStartTime
          ? _value.lessonStartTime
          : lessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      nativeErrorQuestion: freezed == nativeErrorQuestion
          ? _value.nativeErrorQuestion
          : nativeErrorQuestion // ignore: cast_nullable_to_non_nullable
              as int?,
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lastErrorTime: freezed == lastErrorTime
          ? _value.lastErrorTime
          : lastErrorTime // ignore: cast_nullable_to_non_nullable
              as int?,
      teachType: freezed == teachType
          ? _value.teachType
          : teachType // ignore: cast_nullable_to_non_nullable
              as String?,
      aggregationType: freezed == aggregationType
          ? _value.aggregationType
          : aggregationType // ignore: cast_nullable_to_non_nullable
              as String?,
      fontPack: freezed == fontPack
          ? _value.fontPack
          : fontPack // ignore: cast_nullable_to_non_nullable
              as int?,
      checked: freezed == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonModel implements _LessonModel {
  _$_LessonModel(
      {this.id,
      this.key,
      this.name,
      this.subTitle,
      this.questionCount,
      this.showPoint,
      this.status,
      this.lessonStartTime,
      this.route,
      this.nativeErrorQuestion,
      this.unitId,
      this.lessonOrder,
      this.finishTime,
      this.lastErrorTime,
      this.teachType,
      this.aggregationType,
      this.fontPack,
      this.checked});

  factory _$_LessonModel.fromJson(Map<String, dynamic> json) =>
      _$$_LessonModelFromJson(json);

  @override
  int? id;
  @override
  String? key;
  @override
  String? name;
  @override
  String? subTitle;
  @override
  int? questionCount;
  @override
  bool? showPoint;
  @override
  String? status;
  @override
  int? lessonStartTime;
  @override
  String? route;
  @override
  int? nativeErrorQuestion;
  @override
  String? unitId;
  @override
  int? lessonOrder;
  @override
  int? finishTime;
  @override
  int? lastErrorTime;
  @override
  String? teachType;
  @override
  String? aggregationType;
  @override
  int? fontPack;
  @override
  bool? checked;

  @override
  String toString() {
    return 'LessonModel(id: $id, key: $key, name: $name, subTitle: $subTitle, questionCount: $questionCount, showPoint: $showPoint, status: $status, lessonStartTime: $lessonStartTime, route: $route, nativeErrorQuestion: $nativeErrorQuestion, unitId: $unitId, lessonOrder: $lessonOrder, finishTime: $finishTime, lastErrorTime: $lastErrorTime, teachType: $teachType, aggregationType: $aggregationType, fontPack: $fontPack, checked: $checked)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonModelCopyWith<_$_LessonModel> get copyWith =>
      __$$_LessonModelCopyWithImpl<_$_LessonModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonModelToJson(
      this,
    );
  }
}

abstract class _LessonModel implements LessonModel {
  factory _LessonModel(
      {int? id,
      String? key,
      String? name,
      String? subTitle,
      int? questionCount,
      bool? showPoint,
      String? status,
      int? lessonStartTime,
      String? route,
      int? nativeErrorQuestion,
      String? unitId,
      int? lessonOrder,
      int? finishTime,
      int? lastErrorTime,
      String? teachType,
      String? aggregationType,
      int? fontPack,
      bool? checked}) = _$_LessonModel;

  factory _LessonModel.fromJson(Map<String, dynamic> json) =
      _$_LessonModel.fromJson;

  @override
  int? get id;
  set id(int? value);
  @override
  String? get key;
  set key(String? value);
  @override
  String? get name;
  set name(String? value);
  @override
  String? get subTitle;
  set subTitle(String? value);
  @override
  int? get questionCount;
  set questionCount(int? value);
  @override
  bool? get showPoint;
  set showPoint(bool? value);
  @override
  String? get status;
  set status(String? value);
  @override
  int? get lessonStartTime;
  set lessonStartTime(int? value);
  @override
  String? get route;
  set route(String? value);
  @override
  int? get nativeErrorQuestion;
  set nativeErrorQuestion(int? value);
  @override
  String? get unitId;
  set unitId(String? value);
  @override
  int? get lessonOrder;
  set lessonOrder(int? value);
  @override
  int? get finishTime;
  set finishTime(int? value);
  @override
  int? get lastErrorTime;
  set lastErrorTime(int? value);
  @override
  String? get teachType;
  set teachType(String? value);
  @override
  String? get aggregationType;
  set aggregationType(String? value);
  @override
  int? get fontPack;
  set fontPack(int? value);
  @override
  bool? get checked;
  set checked(bool? value);
  @override
  @JsonKey(ignore: true)
  _$$_LessonModelCopyWith<_$_LessonModel> get copyWith =>
      throw _privateConstructorUsedError;
}

GroupQuestionBookModal _$GroupQuestionBookModalFromJson(
    Map<String, dynamic> json) {
  return _GroupQuestionBookModal.fromJson(json);
}

/// @nodoc
mixin _$GroupQuestionBookModal {
  String? get unitId => throw _privateConstructorUsedError;
  String? get unitName => throw _privateConstructorUsedError;
  List<LessonModel>? get lessons => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GroupQuestionBookModalCopyWith<GroupQuestionBookModal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GroupQuestionBookModalCopyWith<$Res> {
  factory $GroupQuestionBookModalCopyWith(GroupQuestionBookModal value,
          $Res Function(GroupQuestionBookModal) then) =
      _$GroupQuestionBookModalCopyWithImpl<$Res, GroupQuestionBookModal>;
  @useResult
  $Res call({String? unitId, String? unitName, List<LessonModel>? lessons});
}

/// @nodoc
class _$GroupQuestionBookModalCopyWithImpl<$Res,
        $Val extends GroupQuestionBookModal>
    implements $GroupQuestionBookModalCopyWith<$Res> {
  _$GroupQuestionBookModalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unitId = freezed,
    Object? unitName = freezed,
    Object? lessons = freezed,
  }) {
    return _then(_value.copyWith(
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      unitName: freezed == unitName
          ? _value.unitName
          : unitName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessons: freezed == lessons
          ? _value.lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<LessonModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GroupQuestionBookModalCopyWith<$Res>
    implements $GroupQuestionBookModalCopyWith<$Res> {
  factory _$$_GroupQuestionBookModalCopyWith(_$_GroupQuestionBookModal value,
          $Res Function(_$_GroupQuestionBookModal) then) =
      __$$_GroupQuestionBookModalCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? unitId, String? unitName, List<LessonModel>? lessons});
}

/// @nodoc
class __$$_GroupQuestionBookModalCopyWithImpl<$Res>
    extends _$GroupQuestionBookModalCopyWithImpl<$Res,
        _$_GroupQuestionBookModal>
    implements _$$_GroupQuestionBookModalCopyWith<$Res> {
  __$$_GroupQuestionBookModalCopyWithImpl(_$_GroupQuestionBookModal _value,
      $Res Function(_$_GroupQuestionBookModal) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unitId = freezed,
    Object? unitName = freezed,
    Object? lessons = freezed,
  }) {
    return _then(_$_GroupQuestionBookModal(
      unitId: freezed == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as String?,
      unitName: freezed == unitName
          ? _value.unitName
          : unitName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessons: freezed == lessons
          ? _value._lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<LessonModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GroupQuestionBookModal implements _GroupQuestionBookModal {
  const _$_GroupQuestionBookModal(
      {this.unitId, this.unitName, final List<LessonModel>? lessons})
      : _lessons = lessons;

  factory _$_GroupQuestionBookModal.fromJson(Map<String, dynamic> json) =>
      _$$_GroupQuestionBookModalFromJson(json);

  @override
  final String? unitId;
  @override
  final String? unitName;
  final List<LessonModel>? _lessons;
  @override
  List<LessonModel>? get lessons {
    final value = _lessons;
    if (value == null) return null;
    if (_lessons is EqualUnmodifiableListView) return _lessons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GroupQuestionBookModal(unitId: $unitId, unitName: $unitName, lessons: $lessons)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GroupQuestionBookModal &&
            (identical(other.unitId, unitId) || other.unitId == unitId) &&
            (identical(other.unitName, unitName) ||
                other.unitName == unitName) &&
            const DeepCollectionEquality().equals(other._lessons, _lessons));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, unitId, unitName,
      const DeepCollectionEquality().hash(_lessons));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GroupQuestionBookModalCopyWith<_$_GroupQuestionBookModal> get copyWith =>
      __$$_GroupQuestionBookModalCopyWithImpl<_$_GroupQuestionBookModal>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GroupQuestionBookModalToJson(
      this,
    );
  }
}

abstract class _GroupQuestionBookModal implements GroupQuestionBookModal {
  const factory _GroupQuestionBookModal(
      {final String? unitId,
      final String? unitName,
      final List<LessonModel>? lessons}) = _$_GroupQuestionBookModal;

  factory _GroupQuestionBookModal.fromJson(Map<String, dynamic> json) =
      _$_GroupQuestionBookModal.fromJson;

  @override
  String? get unitId;
  @override
  String? get unitName;
  @override
  List<LessonModel>? get lessons;
  @override
  @JsonKey(ignore: true)
  _$$_GroupQuestionBookModalCopyWith<_$_GroupQuestionBookModal> get copyWith =>
      throw _privateConstructorUsedError;
}
