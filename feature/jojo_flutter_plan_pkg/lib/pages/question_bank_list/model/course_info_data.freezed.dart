// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseInfoData _$CourseInfoDataFromJson(Map<String, dynamic> json) {
  return _CourseInfoData.fromJson(json);
}

/// @nodoc
mixin _$CourseInfoData {
  int? get courseChildType => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  String? get courseLabel => throw _privateConstructorUsedError;
  String? get courseTypeIcon => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  int? get activateStatus => throw _privateConstructorUsedError;
  int? get currentSegmentId => throw _privateConstructorUsedError;
  bool? get isLogisticsTab => throw _privateConstructorUsedError;
  RollLabelInfo? get rollLabelInfo => throw _privateConstructorUsedError;
  List<TabList>? get tabList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoDataCopyWith<CourseInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoDataCopyWith<$Res> {
  factory $CourseInfoDataCopyWith(
          CourseInfoData value, $Res Function(CourseInfoData) then) =
      _$CourseInfoDataCopyWithImpl<$Res, CourseInfoData>;
  @useResult
  $Res call(
      {int? courseChildType,
      String? classKey,
      int? classId,
      int? subjectType,
      String? courseKey,
      String? courseName,
      String? courseLabel,
      String? courseTypeIcon,
      int? teacherId,
      String? teacherName,
      int? activateStatus,
      int? currentSegmentId,
      bool? isLogisticsTab,
      RollLabelInfo? rollLabelInfo,
      List<TabList>? tabList});

  $RollLabelInfoCopyWith<$Res>? get rollLabelInfo;
}

/// @nodoc
class _$CourseInfoDataCopyWithImpl<$Res, $Val extends CourseInfoData>
    implements $CourseInfoDataCopyWith<$Res> {
  _$CourseInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseChildType = freezed,
    Object? classKey = freezed,
    Object? classId = freezed,
    Object? subjectType = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseLabel = freezed,
    Object? courseTypeIcon = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
    Object? activateStatus = freezed,
    Object? currentSegmentId = freezed,
    Object? isLogisticsTab = freezed,
    Object? rollLabelInfo = freezed,
    Object? tabList = freezed,
  }) {
    return _then(_value.copyWith(
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeIcon: freezed == courseTypeIcon
          ? _value.courseTypeIcon
          : courseTypeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      currentSegmentId: freezed == currentSegmentId
          ? _value.currentSegmentId
          : currentSegmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      isLogisticsTab: freezed == isLogisticsTab
          ? _value.isLogisticsTab
          : isLogisticsTab // ignore: cast_nullable_to_non_nullable
              as bool?,
      rollLabelInfo: freezed == rollLabelInfo
          ? _value.rollLabelInfo
          : rollLabelInfo // ignore: cast_nullable_to_non_nullable
              as RollLabelInfo?,
      tabList: freezed == tabList
          ? _value.tabList
          : tabList // ignore: cast_nullable_to_non_nullable
              as List<TabList>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RollLabelInfoCopyWith<$Res>? get rollLabelInfo {
    if (_value.rollLabelInfo == null) {
      return null;
    }

    return $RollLabelInfoCopyWith<$Res>(_value.rollLabelInfo!, (value) {
      return _then(_value.copyWith(rollLabelInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseInfoDataCopyWith<$Res>
    implements $CourseInfoDataCopyWith<$Res> {
  factory _$$_CourseInfoDataCopyWith(
          _$_CourseInfoData value, $Res Function(_$_CourseInfoData) then) =
      __$$_CourseInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseChildType,
      String? classKey,
      int? classId,
      int? subjectType,
      String? courseKey,
      String? courseName,
      String? courseLabel,
      String? courseTypeIcon,
      int? teacherId,
      String? teacherName,
      int? activateStatus,
      int? currentSegmentId,
      bool? isLogisticsTab,
      RollLabelInfo? rollLabelInfo,
      List<TabList>? tabList});

  @override
  $RollLabelInfoCopyWith<$Res>? get rollLabelInfo;
}

/// @nodoc
class __$$_CourseInfoDataCopyWithImpl<$Res>
    extends _$CourseInfoDataCopyWithImpl<$Res, _$_CourseInfoData>
    implements _$$_CourseInfoDataCopyWith<$Res> {
  __$$_CourseInfoDataCopyWithImpl(
      _$_CourseInfoData _value, $Res Function(_$_CourseInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseChildType = freezed,
    Object? classKey = freezed,
    Object? classId = freezed,
    Object? subjectType = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseLabel = freezed,
    Object? courseTypeIcon = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
    Object? activateStatus = freezed,
    Object? currentSegmentId = freezed,
    Object? isLogisticsTab = freezed,
    Object? rollLabelInfo = freezed,
    Object? tabList = freezed,
  }) {
    return _then(_$_CourseInfoData(
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeIcon: freezed == courseTypeIcon
          ? _value.courseTypeIcon
          : courseTypeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      currentSegmentId: freezed == currentSegmentId
          ? _value.currentSegmentId
          : currentSegmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      isLogisticsTab: freezed == isLogisticsTab
          ? _value.isLogisticsTab
          : isLogisticsTab // ignore: cast_nullable_to_non_nullable
              as bool?,
      rollLabelInfo: freezed == rollLabelInfo
          ? _value.rollLabelInfo
          : rollLabelInfo // ignore: cast_nullable_to_non_nullable
              as RollLabelInfo?,
      tabList: freezed == tabList
          ? _value._tabList
          : tabList // ignore: cast_nullable_to_non_nullable
              as List<TabList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfoData
    with DiagnosticableTreeMixin
    implements _CourseInfoData {
  const _$_CourseInfoData(
      {this.courseChildType,
      this.classKey,
      this.classId,
      this.subjectType,
      this.courseKey,
      this.courseName,
      this.courseLabel,
      this.courseTypeIcon,
      this.teacherId,
      this.teacherName,
      this.activateStatus,
      this.currentSegmentId,
      this.isLogisticsTab,
      this.rollLabelInfo,
      final List<TabList>? tabList})
      : _tabList = tabList;

  factory _$_CourseInfoData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoDataFromJson(json);

  @override
  final int? courseChildType;
  @override
  final String? classKey;
  @override
  final int? classId;
  @override
  final int? subjectType;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final String? courseLabel;
  @override
  final String? courseTypeIcon;
  @override
  final int? teacherId;
  @override
  final String? teacherName;
  @override
  final int? activateStatus;
  @override
  final int? currentSegmentId;
  @override
  final bool? isLogisticsTab;
  @override
  final RollLabelInfo? rollLabelInfo;
  final List<TabList>? _tabList;
  @override
  List<TabList>? get tabList {
    final value = _tabList;
    if (value == null) return null;
    if (_tabList is EqualUnmodifiableListView) return _tabList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CourseInfoData(courseChildType: $courseChildType, classKey: $classKey, classId: $classId, subjectType: $subjectType, courseKey: $courseKey, courseName: $courseName, courseLabel: $courseLabel, courseTypeIcon: $courseTypeIcon, teacherId: $teacherId, teacherName: $teacherName, activateStatus: $activateStatus, currentSegmentId: $currentSegmentId, isLogisticsTab: $isLogisticsTab, rollLabelInfo: $rollLabelInfo, tabList: $tabList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CourseInfoData'))
      ..add(DiagnosticsProperty('courseChildType', courseChildType))
      ..add(DiagnosticsProperty('classKey', classKey))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('subjectType', subjectType))
      ..add(DiagnosticsProperty('courseKey', courseKey))
      ..add(DiagnosticsProperty('courseName', courseName))
      ..add(DiagnosticsProperty('courseLabel', courseLabel))
      ..add(DiagnosticsProperty('courseTypeIcon', courseTypeIcon))
      ..add(DiagnosticsProperty('teacherId', teacherId))
      ..add(DiagnosticsProperty('teacherName', teacherName))
      ..add(DiagnosticsProperty('activateStatus', activateStatus))
      ..add(DiagnosticsProperty('currentSegmentId', currentSegmentId))
      ..add(DiagnosticsProperty('isLogisticsTab', isLogisticsTab))
      ..add(DiagnosticsProperty('rollLabelInfo', rollLabelInfo))
      ..add(DiagnosticsProperty('tabList', tabList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfoData &&
            (identical(other.courseChildType, courseChildType) ||
                other.courseChildType == courseChildType) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseLabel, courseLabel) ||
                other.courseLabel == courseLabel) &&
            (identical(other.courseTypeIcon, courseTypeIcon) ||
                other.courseTypeIcon == courseTypeIcon) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.activateStatus, activateStatus) ||
                other.activateStatus == activateStatus) &&
            (identical(other.currentSegmentId, currentSegmentId) ||
                other.currentSegmentId == currentSegmentId) &&
            (identical(other.isLogisticsTab, isLogisticsTab) ||
                other.isLogisticsTab == isLogisticsTab) &&
            (identical(other.rollLabelInfo, rollLabelInfo) ||
                other.rollLabelInfo == rollLabelInfo) &&
            const DeepCollectionEquality().equals(other._tabList, _tabList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseChildType,
      classKey,
      classId,
      subjectType,
      courseKey,
      courseName,
      courseLabel,
      courseTypeIcon,
      teacherId,
      teacherName,
      activateStatus,
      currentSegmentId,
      isLogisticsTab,
      rollLabelInfo,
      const DeepCollectionEquality().hash(_tabList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoDataCopyWith<_$_CourseInfoData> get copyWith =>
      __$$_CourseInfoDataCopyWithImpl<_$_CourseInfoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoDataToJson(
      this,
    );
  }
}

abstract class _CourseInfoData implements CourseInfoData {
  const factory _CourseInfoData(
      {final int? courseChildType,
      final String? classKey,
      final int? classId,
      final int? subjectType,
      final String? courseKey,
      final String? courseName,
      final String? courseLabel,
      final String? courseTypeIcon,
      final int? teacherId,
      final String? teacherName,
      final int? activateStatus,
      final int? currentSegmentId,
      final bool? isLogisticsTab,
      final RollLabelInfo? rollLabelInfo,
      final List<TabList>? tabList}) = _$_CourseInfoData;

  factory _CourseInfoData.fromJson(Map<String, dynamic> json) =
      _$_CourseInfoData.fromJson;

  @override
  int? get courseChildType;
  @override
  String? get classKey;
  @override
  int? get classId;
  @override
  int? get subjectType;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  String? get courseLabel;
  @override
  String? get courseTypeIcon;
  @override
  int? get teacherId;
  @override
  String? get teacherName;
  @override
  int? get activateStatus;
  @override
  int? get currentSegmentId;
  @override
  bool? get isLogisticsTab;
  @override
  RollLabelInfo? get rollLabelInfo;
  @override
  List<TabList>? get tabList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoDataCopyWith<_$_CourseInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

TabList _$TabListFromJson(Map<String, dynamic> json) {
  return _TabList.fromJson(json);
}

/// @nodoc
mixin _$TabList {
  int? get type => throw _privateConstructorUsedError;
  String? get tabName => throw _privateConstructorUsedError;
  bool? get selected => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TabListCopyWith<TabList> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TabListCopyWith<$Res> {
  factory $TabListCopyWith(TabList value, $Res Function(TabList) then) =
      _$TabListCopyWithImpl<$Res, TabList>;
  @useResult
  $Res call({int? type, String? tabName, bool? selected});
}

/// @nodoc
class _$TabListCopyWithImpl<$Res, $Val extends TabList>
    implements $TabListCopyWith<$Res> {
  _$TabListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? tabName = freezed,
    Object? selected = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      tabName: freezed == tabName
          ? _value.tabName
          : tabName // ignore: cast_nullable_to_non_nullable
              as String?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TabListCopyWith<$Res> implements $TabListCopyWith<$Res> {
  factory _$$_TabListCopyWith(
          _$_TabList value, $Res Function(_$_TabList) then) =
      __$$_TabListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? tabName, bool? selected});
}

/// @nodoc
class __$$_TabListCopyWithImpl<$Res>
    extends _$TabListCopyWithImpl<$Res, _$_TabList>
    implements _$$_TabListCopyWith<$Res> {
  __$$_TabListCopyWithImpl(_$_TabList _value, $Res Function(_$_TabList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? tabName = freezed,
    Object? selected = freezed,
  }) {
    return _then(_$_TabList(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      tabName: freezed == tabName
          ? _value.tabName
          : tabName // ignore: cast_nullable_to_non_nullable
              as String?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TabList with DiagnosticableTreeMixin implements _TabList {
  const _$_TabList({this.type, this.tabName, this.selected});

  factory _$_TabList.fromJson(Map<String, dynamic> json) =>
      _$$_TabListFromJson(json);

  @override
  final int? type;
  @override
  final String? tabName;
  @override
  final bool? selected;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TabList(type: $type, tabName: $tabName, selected: $selected)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TabList'))
      ..add(DiagnosticsProperty('type', type))
      ..add(DiagnosticsProperty('tabName', tabName))
      ..add(DiagnosticsProperty('selected', selected));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TabList &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.tabName, tabName) || other.tabName == tabName) &&
            (identical(other.selected, selected) ||
                other.selected == selected));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, tabName, selected);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TabListCopyWith<_$_TabList> get copyWith =>
      __$$_TabListCopyWithImpl<_$_TabList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TabListToJson(
      this,
    );
  }
}

abstract class _TabList implements TabList {
  const factory _TabList(
      {final int? type,
      final String? tabName,
      final bool? selected}) = _$_TabList;

  factory _TabList.fromJson(Map<String, dynamic> json) = _$_TabList.fromJson;

  @override
  int? get type;
  @override
  String? get tabName;
  @override
  bool? get selected;
  @override
  @JsonKey(ignore: true)
  _$$_TabListCopyWith<_$_TabList> get copyWith =>
      throw _privateConstructorUsedError;
}

RollLabelInfo _$RollLabelInfoFromJson(Map<String, dynamic> json) {
  return _RollLabelInfo.fromJson(json);
}

/// @nodoc
mixin _$RollLabelInfo {
  String? get currentRollLabel => throw _privateConstructorUsedError;
  List<RollLabelList>? get rollLabelList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RollLabelInfoCopyWith<RollLabelInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RollLabelInfoCopyWith<$Res> {
  factory $RollLabelInfoCopyWith(
          RollLabelInfo value, $Res Function(RollLabelInfo) then) =
      _$RollLabelInfoCopyWithImpl<$Res, RollLabelInfo>;
  @useResult
  $Res call({String? currentRollLabel, List<RollLabelList>? rollLabelList});
}

/// @nodoc
class _$RollLabelInfoCopyWithImpl<$Res, $Val extends RollLabelInfo>
    implements $RollLabelInfoCopyWith<$Res> {
  _$RollLabelInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentRollLabel = freezed,
    Object? rollLabelList = freezed,
  }) {
    return _then(_value.copyWith(
      currentRollLabel: freezed == currentRollLabel
          ? _value.currentRollLabel
          : currentRollLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      rollLabelList: freezed == rollLabelList
          ? _value.rollLabelList
          : rollLabelList // ignore: cast_nullable_to_non_nullable
              as List<RollLabelList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RollLabelInfoCopyWith<$Res>
    implements $RollLabelInfoCopyWith<$Res> {
  factory _$$_RollLabelInfoCopyWith(
          _$_RollLabelInfo value, $Res Function(_$_RollLabelInfo) then) =
      __$$_RollLabelInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? currentRollLabel, List<RollLabelList>? rollLabelList});
}

/// @nodoc
class __$$_RollLabelInfoCopyWithImpl<$Res>
    extends _$RollLabelInfoCopyWithImpl<$Res, _$_RollLabelInfo>
    implements _$$_RollLabelInfoCopyWith<$Res> {
  __$$_RollLabelInfoCopyWithImpl(
      _$_RollLabelInfo _value, $Res Function(_$_RollLabelInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentRollLabel = freezed,
    Object? rollLabelList = freezed,
  }) {
    return _then(_$_RollLabelInfo(
      currentRollLabel: freezed == currentRollLabel
          ? _value.currentRollLabel
          : currentRollLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      rollLabelList: freezed == rollLabelList
          ? _value._rollLabelList
          : rollLabelList // ignore: cast_nullable_to_non_nullable
              as List<RollLabelList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RollLabelInfo with DiagnosticableTreeMixin implements _RollLabelInfo {
  const _$_RollLabelInfo(
      {this.currentRollLabel, final List<RollLabelList>? rollLabelList})
      : _rollLabelList = rollLabelList;

  factory _$_RollLabelInfo.fromJson(Map<String, dynamic> json) =>
      _$$_RollLabelInfoFromJson(json);

  @override
  final String? currentRollLabel;
  final List<RollLabelList>? _rollLabelList;
  @override
  List<RollLabelList>? get rollLabelList {
    final value = _rollLabelList;
    if (value == null) return null;
    if (_rollLabelList is EqualUnmodifiableListView) return _rollLabelList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RollLabelInfo(currentRollLabel: $currentRollLabel, rollLabelList: $rollLabelList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RollLabelInfo'))
      ..add(DiagnosticsProperty('currentRollLabel', currentRollLabel))
      ..add(DiagnosticsProperty('rollLabelList', rollLabelList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RollLabelInfo &&
            (identical(other.currentRollLabel, currentRollLabel) ||
                other.currentRollLabel == currentRollLabel) &&
            const DeepCollectionEquality()
                .equals(other._rollLabelList, _rollLabelList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, currentRollLabel,
      const DeepCollectionEquality().hash(_rollLabelList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RollLabelInfoCopyWith<_$_RollLabelInfo> get copyWith =>
      __$$_RollLabelInfoCopyWithImpl<_$_RollLabelInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RollLabelInfoToJson(
      this,
    );
  }
}

abstract class _RollLabelInfo implements RollLabelInfo {
  const factory _RollLabelInfo(
      {final String? currentRollLabel,
      final List<RollLabelList>? rollLabelList}) = _$_RollLabelInfo;

  factory _RollLabelInfo.fromJson(Map<String, dynamic> json) =
      _$_RollLabelInfo.fromJson;

  @override
  String? get currentRollLabel;
  @override
  List<RollLabelList>? get rollLabelList;
  @override
  @JsonKey(ignore: true)
  _$$_RollLabelInfoCopyWith<_$_RollLabelInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

RollLabelList _$RollLabelListFromJson(Map<String, dynamic> json) {
  return _RollLabelList.fromJson(json);
}

/// @nodoc
mixin _$RollLabelList {
  int? get classId => throw _privateConstructorUsedError;
  String? get rollLabelName => throw _privateConstructorUsedError;
  String? get segmentIdJson => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RollLabelListCopyWith<RollLabelList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RollLabelListCopyWith<$Res> {
  factory $RollLabelListCopyWith(
          RollLabelList value, $Res Function(RollLabelList) then) =
      _$RollLabelListCopyWithImpl<$Res, RollLabelList>;
  @useResult
  $Res call({int? classId, String? rollLabelName, String? segmentIdJson});
}

/// @nodoc
class _$RollLabelListCopyWithImpl<$Res, $Val extends RollLabelList>
    implements $RollLabelListCopyWith<$Res> {
  _$RollLabelListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? rollLabelName = freezed,
    Object? segmentIdJson = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      rollLabelName: freezed == rollLabelName
          ? _value.rollLabelName
          : rollLabelName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentIdJson: freezed == segmentIdJson
          ? _value.segmentIdJson
          : segmentIdJson // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RollLabelListCopyWith<$Res>
    implements $RollLabelListCopyWith<$Res> {
  factory _$$_RollLabelListCopyWith(
          _$_RollLabelList value, $Res Function(_$_RollLabelList) then) =
      __$$_RollLabelListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? classId, String? rollLabelName, String? segmentIdJson});
}

/// @nodoc
class __$$_RollLabelListCopyWithImpl<$Res>
    extends _$RollLabelListCopyWithImpl<$Res, _$_RollLabelList>
    implements _$$_RollLabelListCopyWith<$Res> {
  __$$_RollLabelListCopyWithImpl(
      _$_RollLabelList _value, $Res Function(_$_RollLabelList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? rollLabelName = freezed,
    Object? segmentIdJson = freezed,
  }) {
    return _then(_$_RollLabelList(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      rollLabelName: freezed == rollLabelName
          ? _value.rollLabelName
          : rollLabelName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentIdJson: freezed == segmentIdJson
          ? _value.segmentIdJson
          : segmentIdJson // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RollLabelList with DiagnosticableTreeMixin implements _RollLabelList {
  const _$_RollLabelList(
      {this.classId, this.rollLabelName, this.segmentIdJson});

  factory _$_RollLabelList.fromJson(Map<String, dynamic> json) =>
      _$$_RollLabelListFromJson(json);

  @override
  final int? classId;
  @override
  final String? rollLabelName;
  @override
  final String? segmentIdJson;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RollLabelList(classId: $classId, rollLabelName: $rollLabelName, segmentIdJson: $segmentIdJson)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RollLabelList'))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('rollLabelName', rollLabelName))
      ..add(DiagnosticsProperty('segmentIdJson', segmentIdJson));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RollLabelList &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.rollLabelName, rollLabelName) ||
                other.rollLabelName == rollLabelName) &&
            (identical(other.segmentIdJson, segmentIdJson) ||
                other.segmentIdJson == segmentIdJson));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, classId, rollLabelName, segmentIdJson);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RollLabelListCopyWith<_$_RollLabelList> get copyWith =>
      __$$_RollLabelListCopyWithImpl<_$_RollLabelList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RollLabelListToJson(
      this,
    );
  }
}

abstract class _RollLabelList implements RollLabelList {
  const factory _RollLabelList(
      {final int? classId,
      final String? rollLabelName,
      final String? segmentIdJson}) = _$_RollLabelList;

  factory _RollLabelList.fromJson(Map<String, dynamic> json) =
      _$_RollLabelList.fromJson;

  @override
  int? get classId;
  @override
  String? get rollLabelName;
  @override
  String? get segmentIdJson;
  @override
  @JsonKey(ignore: true)
  _$$_RollLabelListCopyWith<_$_RollLabelList> get copyWith =>
      throw _privateConstructorUsedError;
}
