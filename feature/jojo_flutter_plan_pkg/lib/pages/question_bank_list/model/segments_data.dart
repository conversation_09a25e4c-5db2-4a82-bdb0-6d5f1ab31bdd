import 'package:freezed_annotation/freezed_annotation.dart';

part 'segments_data.freezed.dart';
part 'segments_data.g.dart';

@freezed
class SegmentsData with _$SegmentsData {
  const factory SegmentsData({
    int? modeType,
    CourseInfoModel? courseInfo,
    ClassInfoModel? classInfo,
    List<ItemModel>? itemList,
    LocationInfoModel? locationInfo,
  }) = _SegmentsData;

  factory SegmentsData.fromJson(Map<String, dynamic> json) =>
      _$SegmentsDataFromJson(json);
}

@freezed
class CourseInfoModel with _$CourseInfoModel {
  const factory CourseInfoModel({
    int? courseId,
    String? courseKey,
    String? courseName,
    String? courseSegment,
    int? courseSegmentCode,
    int? subjectType,
    int? courseSecondaryType,
    int? showSearch,
  }) = _CourseInfoModel;

  factory CourseInfoModel.fromJson(Map<String, dynamic> json) =>
      _$CourseInfoModelFromJson(json);
}

@freezed
class ClassInfoModel with _$ClassInfoModel {
  const factory ClassInfoModel({
    int? classId,
    String? classKey,
    String? className,
    int? teacherId,
    String? teacherName,
  }) = _ClassInfoModel;

  factory ClassInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ClassInfoModelFromJson(json);
}

@freezed
class ItemModel with _$ItemModel {
  const factory ItemModel({
    String? type,
    SegmentInfoModel? segmentInfo,
  }) = _ItemModel;

  factory ItemModel.fromJson(Map<String, dynamic> json) =>
      _$ItemModelFromJson(json);
}

@freezed
class SegmentInfoModel with _$SegmentInfoModel {
  const factory SegmentInfoModel({
    int? segmentId,
    String? segmentKey,
    String? segmentOrderDes,
    int? segmentOrder,
    String? segmentName,
    String? segmentDescription,
    String? labelName,
    int? labelCode,
  }) = _SegmentInfoModel;

  factory SegmentInfoModel.fromJson(Map<String, dynamic> json) =>
      _$SegmentInfoModelFromJson(json);
}

@freezed
class LocationInfoModel with _$LocationInfoModel {
  const factory LocationInfoModel({
    int? segmentId,
  }) = _LocationInfoModel;

  factory LocationInfoModel.fromJson(Map<String, dynamic> json) =>
      _$LocationInfoModelFromJson(json);
}
