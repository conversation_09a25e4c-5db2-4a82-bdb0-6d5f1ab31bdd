// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'segments_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SegmentsData _$$_SegmentsDataFromJson(Map<String, dynamic> json) =>
    _$_SegmentsData(
      modeType: json['modeType'] as int?,
      courseInfo: json['courseInfo'] == null
          ? null
          : CourseInfoModel.fromJson(
              json['courseInfo'] as Map<String, dynamic>),
      classInfo: json['classInfo'] == null
          ? null
          : ClassInfoModel.fromJson(json['classInfo'] as Map<String, dynamic>),
      itemList: (json['itemList'] as List<dynamic>?)
          ?.map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      locationInfo: json['locationInfo'] == null
          ? null
          : LocationInfoModel.fromJson(
              json['locationInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_SegmentsDataToJson(_$_SegmentsData instance) =>
    <String, dynamic>{
      'modeType': instance.modeType,
      'courseInfo': instance.courseInfo,
      'classInfo': instance.classInfo,
      'itemList': instance.itemList,
      'locationInfo': instance.locationInfo,
    };

_$_CourseInfoModel _$$_CourseInfoModelFromJson(Map<String, dynamic> json) =>
    _$_CourseInfoModel(
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      courseSegment: json['courseSegment'] as String?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
      subjectType: json['subjectType'] as int?,
      courseSecondaryType: json['courseSecondaryType'] as int?,
      showSearch: json['showSearch'] as int?,
    );

Map<String, dynamic> _$$_CourseInfoModelToJson(_$_CourseInfoModel instance) =>
    <String, dynamic>{
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'courseSegment': instance.courseSegment,
      'courseSegmentCode': instance.courseSegmentCode,
      'subjectType': instance.subjectType,
      'courseSecondaryType': instance.courseSecondaryType,
      'showSearch': instance.showSearch,
    };

_$_ClassInfoModel _$$_ClassInfoModelFromJson(Map<String, dynamic> json) =>
    _$_ClassInfoModel(
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      className: json['className'] as String?,
      teacherId: json['teacherId'] as int?,
      teacherName: json['teacherName'] as String?,
    );

Map<String, dynamic> _$$_ClassInfoModelToJson(_$_ClassInfoModel instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'classKey': instance.classKey,
      'className': instance.className,
      'teacherId': instance.teacherId,
      'teacherName': instance.teacherName,
    };

_$_ItemModel _$$_ItemModelFromJson(Map<String, dynamic> json) => _$_ItemModel(
      type: json['type'] as String?,
      segmentInfo: json['segmentInfo'] == null
          ? null
          : SegmentInfoModel.fromJson(
              json['segmentInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ItemModelToJson(_$_ItemModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'segmentInfo': instance.segmentInfo,
    };

_$_SegmentInfoModel _$$_SegmentInfoModelFromJson(Map<String, dynamic> json) =>
    _$_SegmentInfoModel(
      segmentId: json['segmentId'] as int?,
      segmentKey: json['segmentKey'] as String?,
      segmentOrderDes: json['segmentOrderDes'] as String?,
      segmentOrder: json['segmentOrder'] as int?,
      segmentName: json['segmentName'] as String?,
      segmentDescription: json['segmentDescription'] as String?,
      labelName: json['labelName'] as String?,
      labelCode: json['labelCode'] as int?,
    );

Map<String, dynamic> _$$_SegmentInfoModelToJson(_$_SegmentInfoModel instance) =>
    <String, dynamic>{
      'segmentId': instance.segmentId,
      'segmentKey': instance.segmentKey,
      'segmentOrderDes': instance.segmentOrderDes,
      'segmentOrder': instance.segmentOrder,
      'segmentName': instance.segmentName,
      'segmentDescription': instance.segmentDescription,
      'labelName': instance.labelName,
      'labelCode': instance.labelCode,
    };

_$_LocationInfoModel _$$_LocationInfoModelFromJson(Map<String, dynamic> json) =>
    _$_LocationInfoModel(
      segmentId: json['segmentId'] as int?,
    );

Map<String, dynamic> _$$_LocationInfoModelToJson(
        _$_LocationInfoModel instance) =>
    <String, dynamic>{
      'segmentId': instance.segmentId,
    };
