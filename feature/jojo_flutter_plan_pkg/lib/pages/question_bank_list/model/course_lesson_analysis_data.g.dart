// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_lesson_analysis_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseLessonAnalysisData _$$_CourseLessonAnalysisDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseLessonAnalysisData(
      weekId: json['weekId'] as int?,
      weekName: json['weekName'] as String?,
      segmentId: json['segmentId'] as int?,
      segmentName: json['segmentName'] as String?,
      today: json['today'] as bool?,
      lessonInfo: (json['lessonInfo'] as List<dynamic>?)
          ?.map((e) => LessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseLessonAnalysisDataToJson(
        _$_CourseLessonAnalysisData instance) =>
    <String, dynamic>{
      'weekId': instance.weekId,
      'weekName': instance.weekName,
      'segmentId': instance.segmentId,
      'segmentName': instance.segmentName,
      'today': instance.today,
      'lessonInfo': instance.lessonInfo,
    };
