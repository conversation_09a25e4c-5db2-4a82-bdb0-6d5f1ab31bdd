import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_books_data.freezed.dart';
part 'question_books_data.g.dart';

@freezed
class QuestionBooksModel with _$QuestionBooksModel {
  const factory QuestionBooksModel({
    String? courseKey,
    String? courseName,
    int? fontPack,
    bool? isShowEliminateAllButton,
    String? offset,
    int? totalQuestionCount,
    List<UnitModel>? units,
    List<LessonModel>? lessons, // 新增字段
  }) = _QuestionBooksModel;

  factory QuestionBooksModel.fromJson(Map<String, dynamic> json) =>
      _$QuestionBooksModelFromJson(json);
}

@freezed
class UnitModel with _$UnitModel {
  const factory UnitModel({
    String? classKey,
    int? classId,
    String? month,
    int? startTimestamp,
    String? unitName,
    String? unitId,
    int? questionCount,
  }) = _UnitModel;

  factory UnitModel.fromJson(Map<String, dynamic> json) =>
      _$UnitModelFromJson(json);
}

@unfreezed
class LessonModel with _$LessonModel {
  factory LessonModel({
    int? id,
    String? key,
    String? name,
    String? subTitle,
    int? questionCount,
    bool? showPoint,
    String? status,
    int? lessonStartTime,
    String? route,
    int? nativeErrorQuestion,
    String? unitId,
    int? lessonOrder,
    int? finishTime,
    int? lastErrorTime,
    String? teachType,
    String? aggregationType,
    int? fontPack,
    bool? checked,
  }) = _LessonModel;

  factory LessonModel.fromJson(Map<String, dynamic> json) =>
      _$LessonModelFromJson(json);
}

@freezed
class GroupQuestionBookModal with _$GroupQuestionBookModal {
  const factory GroupQuestionBookModal({
    String? unitId,
    String? unitName,
    List<LessonModel>? lessons,
  }) = _GroupQuestionBookModal;

  factory GroupQuestionBookModal.fromJson(Map<String, dynamic> json) =>
      _$GroupQuestionBookModalFromJson(json);
}
