// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_lesson_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseLessonData _$$_CourseLessonDataFromJson(Map<String, dynamic> json) =>
    _$_CourseLessonData(
      selectedNum: json['selectedNum'] as int?,
      lessonList: (json['lessonList'] as List<dynamic>?)
          ?.map((e) => LessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseLessonDataToJson(_$_CourseLessonData instance) =>
    <String, dynamic>{
      'selectedNum': instance.selectedNum,
      'lessonList': instance.lessonList,
    };

_$_LessonInfo _$$_LessonInfoFromJson(Map<String, dynamic> json) =>
    _$_LessonInfo(
      lessonId: json['lessonId'] as int?,
      lessonTitle: json['lessonTitle'] as String?,
      lessonSubTitle: json['lessonSubTitle'] as String?,
      lessonOrder: json['lessonOrder'] as String?,
      order: json['order'] as int?,
      weekId: json['weekId'] as int?,
      weekName: json['weekName'] as String?,
      segmentId: json['segmentId'] as int?,
      segmentName: json['segmentName'] as String?,
      today: json['today'] as bool?,
      checked: json['checked'] as bool?,
      studyStage: json['studyStage'] as int?,
    );

Map<String, dynamic> _$$_LessonInfoToJson(_$_LessonInfo instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'lessonTitle': instance.lessonTitle,
      'lessonSubTitle': instance.lessonSubTitle,
      'lessonOrder': instance.lessonOrder,
      'order': instance.order,
      'weekId': instance.weekId,
      'weekName': instance.weekName,
      'segmentId': instance.segmentId,
      'segmentName': instance.segmentName,
      'today': instance.today,
      'checked': instance.checked,
      'studyStage': instance.studyStage,
    };
