// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_lesson_analysis_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseLessonAnalysisData _$CourseLessonAnalysisDataFromJson(
    Map<String, dynamic> json) {
  return _CourseLessonAnalysisData.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonAnalysisData {
  int? get weekId => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  List<LessonInfo>? get lessonInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonAnalysisDataCopyWith<CourseLessonAnalysisData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonAnalysisDataCopyWith<$Res> {
  factory $CourseLessonAnalysisDataCopyWith(CourseLessonAnalysisData value,
          $Res Function(CourseLessonAnalysisData) then) =
      _$CourseLessonAnalysisDataCopyWithImpl<$Res, CourseLessonAnalysisData>;
  @useResult
  $Res call(
      {int? weekId,
      String? weekName,
      int? segmentId,
      String? segmentName,
      bool? today,
      List<LessonInfo>? lessonInfo});
}

/// @nodoc
class _$CourseLessonAnalysisDataCopyWithImpl<$Res,
        $Val extends CourseLessonAnalysisData>
    implements $CourseLessonAnalysisDataCopyWith<$Res> {
  _$CourseLessonAnalysisDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
    Object? lessonInfo = freezed,
  }) {
    return _then(_value.copyWith(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonInfo: freezed == lessonInfo
          ? _value.lessonInfo
          : lessonInfo // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonAnalysisDataCopyWith<$Res>
    implements $CourseLessonAnalysisDataCopyWith<$Res> {
  factory _$$_CourseLessonAnalysisDataCopyWith(
          _$_CourseLessonAnalysisData value,
          $Res Function(_$_CourseLessonAnalysisData) then) =
      __$$_CourseLessonAnalysisDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? weekId,
      String? weekName,
      int? segmentId,
      String? segmentName,
      bool? today,
      List<LessonInfo>? lessonInfo});
}

/// @nodoc
class __$$_CourseLessonAnalysisDataCopyWithImpl<$Res>
    extends _$CourseLessonAnalysisDataCopyWithImpl<$Res,
        _$_CourseLessonAnalysisData>
    implements _$$_CourseLessonAnalysisDataCopyWith<$Res> {
  __$$_CourseLessonAnalysisDataCopyWithImpl(_$_CourseLessonAnalysisData _value,
      $Res Function(_$_CourseLessonAnalysisData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
    Object? lessonInfo = freezed,
  }) {
    return _then(_$_CourseLessonAnalysisData(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonInfo: freezed == lessonInfo
          ? _value._lessonInfo
          : lessonInfo // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonAnalysisData implements _CourseLessonAnalysisData {
  const _$_CourseLessonAnalysisData(
      {this.weekId,
      this.weekName,
      this.segmentId,
      this.segmentName,
      this.today,
      final List<LessonInfo>? lessonInfo})
      : _lessonInfo = lessonInfo;

  factory _$_CourseLessonAnalysisData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonAnalysisDataFromJson(json);

  @override
  final int? weekId;
  @override
  final String? weekName;
  @override
  final int? segmentId;
  @override
  final String? segmentName;
  @override
  final bool? today;
  final List<LessonInfo>? _lessonInfo;
  @override
  List<LessonInfo>? get lessonInfo {
    final value = _lessonInfo;
    if (value == null) return null;
    if (_lessonInfo is EqualUnmodifiableListView) return _lessonInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseLessonAnalysisData(weekId: $weekId, weekName: $weekName, segmentId: $segmentId, segmentName: $segmentName, today: $today, lessonInfo: $lessonInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseLessonAnalysisData &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.weekName, weekName) ||
                other.weekName == weekName) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.today, today) || other.today == today) &&
            const DeepCollectionEquality()
                .equals(other._lessonInfo, _lessonInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, weekId, weekName, segmentId,
      segmentName, today, const DeepCollectionEquality().hash(_lessonInfo));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonAnalysisDataCopyWith<_$_CourseLessonAnalysisData>
      get copyWith => __$$_CourseLessonAnalysisDataCopyWithImpl<
          _$_CourseLessonAnalysisData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonAnalysisDataToJson(
      this,
    );
  }
}

abstract class _CourseLessonAnalysisData implements CourseLessonAnalysisData {
  const factory _CourseLessonAnalysisData(
      {final int? weekId,
      final String? weekName,
      final int? segmentId,
      final String? segmentName,
      final bool? today,
      final List<LessonInfo>? lessonInfo}) = _$_CourseLessonAnalysisData;

  factory _CourseLessonAnalysisData.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonAnalysisData.fromJson;

  @override
  int? get weekId;
  @override
  String? get weekName;
  @override
  int? get segmentId;
  @override
  String? get segmentName;
  @override
  bool? get today;
  @override
  List<LessonInfo>? get lessonInfo;
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonAnalysisDataCopyWith<_$_CourseLessonAnalysisData>
      get copyWith => throw _privateConstructorUsedError;
}
