// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_lesson_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseLessonData _$CourseLessonDataFromJson(Map<String, dynamic> json) {
  return _CourseLessonData.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonData {
  int? get selectedNum => throw _privateConstructorUsedError;
  List<LessonInfo>? get lessonList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonDataCopyWith<CourseLessonData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonDataCopyWith<$Res> {
  factory $CourseLessonDataCopyWith(
          CourseLessonData value, $Res Function(CourseLessonData) then) =
      _$CourseLessonDataCopyWithImpl<$Res, CourseLessonData>;
  @useResult
  $Res call({int? selectedNum, List<LessonInfo>? lessonList});
}

/// @nodoc
class _$CourseLessonDataCopyWithImpl<$Res, $Val extends CourseLessonData>
    implements $CourseLessonDataCopyWith<$Res> {
  _$CourseLessonDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedNum = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_value.copyWith(
      selectedNum: freezed == selectedNum
          ? _value.selectedNum
          : selectedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonList: freezed == lessonList
          ? _value.lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonDataCopyWith<$Res>
    implements $CourseLessonDataCopyWith<$Res> {
  factory _$$_CourseLessonDataCopyWith(
          _$_CourseLessonData value, $Res Function(_$_CourseLessonData) then) =
      __$$_CourseLessonDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? selectedNum, List<LessonInfo>? lessonList});
}

/// @nodoc
class __$$_CourseLessonDataCopyWithImpl<$Res>
    extends _$CourseLessonDataCopyWithImpl<$Res, _$_CourseLessonData>
    implements _$$_CourseLessonDataCopyWith<$Res> {
  __$$_CourseLessonDataCopyWithImpl(
      _$_CourseLessonData _value, $Res Function(_$_CourseLessonData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedNum = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_$_CourseLessonData(
      selectedNum: freezed == selectedNum
          ? _value.selectedNum
          : selectedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonList: freezed == lessonList
          ? _value._lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonData implements _CourseLessonData {
  const _$_CourseLessonData(
      {this.selectedNum, final List<LessonInfo>? lessonList})
      : _lessonList = lessonList;

  factory _$_CourseLessonData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonDataFromJson(json);

  @override
  final int? selectedNum;
  final List<LessonInfo>? _lessonList;
  @override
  List<LessonInfo>? get lessonList {
    final value = _lessonList;
    if (value == null) return null;
    if (_lessonList is EqualUnmodifiableListView) return _lessonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseLessonData(selectedNum: $selectedNum, lessonList: $lessonList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseLessonData &&
            (identical(other.selectedNum, selectedNum) ||
                other.selectedNum == selectedNum) &&
            const DeepCollectionEquality()
                .equals(other._lessonList, _lessonList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, selectedNum,
      const DeepCollectionEquality().hash(_lessonList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonDataCopyWith<_$_CourseLessonData> get copyWith =>
      __$$_CourseLessonDataCopyWithImpl<_$_CourseLessonData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonDataToJson(
      this,
    );
  }
}

abstract class _CourseLessonData implements CourseLessonData {
  const factory _CourseLessonData(
      {final int? selectedNum,
      final List<LessonInfo>? lessonList}) = _$_CourseLessonData;

  factory _CourseLessonData.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonData.fromJson;

  @override
  int? get selectedNum;
  @override
  List<LessonInfo>? get lessonList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonDataCopyWith<_$_CourseLessonData> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfo _$LessonInfoFromJson(Map<String, dynamic> json) {
  return _LessonInfo.fromJson(json);
}

/// @nodoc
mixin _$LessonInfo {
  int? get lessonId => throw _privateConstructorUsedError;
  set lessonId(int? value) => throw _privateConstructorUsedError;
  String? get lessonTitle => throw _privateConstructorUsedError;
  set lessonTitle(String? value) => throw _privateConstructorUsedError;
  String? get lessonSubTitle => throw _privateConstructorUsedError;
  set lessonSubTitle(String? value) => throw _privateConstructorUsedError;
  String? get lessonOrder => throw _privateConstructorUsedError;
  set lessonOrder(String? value) => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  set order(int? value) => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  set weekId(int? value) => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;
  set weekName(String? value) => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  set segmentId(int? value) => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  set segmentName(String? value) => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  set today(bool? value) => throw _privateConstructorUsedError;
  bool? get checked => throw _privateConstructorUsedError;
  set checked(bool? value) => throw _privateConstructorUsedError;
  int? get studyStage => throw _privateConstructorUsedError;
  set studyStage(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoCopyWith<LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoCopyWith<$Res> {
  factory $LessonInfoCopyWith(
          LessonInfo value, $Res Function(LessonInfo) then) =
      _$LessonInfoCopyWithImpl<$Res, LessonInfo>;
  @useResult
  $Res call(
      {int? lessonId,
      String? lessonTitle,
      String? lessonSubTitle,
      String? lessonOrder,
      int? order,
      int? weekId,
      String? weekName,
      int? segmentId,
      String? segmentName,
      bool? today,
      bool? checked,
      int? studyStage});
}

/// @nodoc
class _$LessonInfoCopyWithImpl<$Res, $Val extends LessonInfo>
    implements $LessonInfoCopyWith<$Res> {
  _$LessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonTitle = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrder = freezed,
    Object? order = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
    Object? checked = freezed,
    Object? studyStage = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      checked: freezed == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool?,
      studyStage: freezed == studyStage
          ? _value.studyStage
          : studyStage // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonInfoCopyWith<$Res>
    implements $LessonInfoCopyWith<$Res> {
  factory _$$_LessonInfoCopyWith(
          _$_LessonInfo value, $Res Function(_$_LessonInfo) then) =
      __$$_LessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? lessonId,
      String? lessonTitle,
      String? lessonSubTitle,
      String? lessonOrder,
      int? order,
      int? weekId,
      String? weekName,
      int? segmentId,
      String? segmentName,
      bool? today,
      bool? checked,
      int? studyStage});
}

/// @nodoc
class __$$_LessonInfoCopyWithImpl<$Res>
    extends _$LessonInfoCopyWithImpl<$Res, _$_LessonInfo>
    implements _$$_LessonInfoCopyWith<$Res> {
  __$$_LessonInfoCopyWithImpl(
      _$_LessonInfo _value, $Res Function(_$_LessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonTitle = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrder = freezed,
    Object? order = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
    Object? segmentId = freezed,
    Object? segmentName = freezed,
    Object? today = freezed,
    Object? checked = freezed,
    Object? studyStage = freezed,
  }) {
    return _then(_$_LessonInfo(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      checked: freezed == checked
          ? _value.checked
          : checked // ignore: cast_nullable_to_non_nullable
              as bool?,
      studyStage: freezed == studyStage
          ? _value.studyStage
          : studyStage // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfo implements _LessonInfo {
  _$_LessonInfo(
      {this.lessonId,
      this.lessonTitle,
      this.lessonSubTitle,
      this.lessonOrder,
      this.order,
      this.weekId,
      this.weekName,
      this.segmentId,
      this.segmentName,
      this.today,
      this.checked,
      this.studyStage});

  factory _$_LessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoFromJson(json);

  @override
  int? lessonId;
  @override
  String? lessonTitle;
  @override
  String? lessonSubTitle;
  @override
  String? lessonOrder;
  @override
  int? order;
  @override
  int? weekId;
  @override
  String? weekName;
  @override
  int? segmentId;
  @override
  String? segmentName;
  @override
  bool? today;
  @override
  bool? checked;
  @override
  int? studyStage;

  @override
  String toString() {
    return 'LessonInfo(lessonId: $lessonId, lessonTitle: $lessonTitle, lessonSubTitle: $lessonSubTitle, lessonOrder: $lessonOrder, order: $order, weekId: $weekId, weekName: $weekName, segmentId: $segmentId, segmentName: $segmentName, today: $today, checked: $checked, studyStage: $studyStage)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      __$$_LessonInfoCopyWithImpl<_$_LessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoToJson(
      this,
    );
  }
}

abstract class _LessonInfo implements LessonInfo {
  factory _LessonInfo(
      {int? lessonId,
      String? lessonTitle,
      String? lessonSubTitle,
      String? lessonOrder,
      int? order,
      int? weekId,
      String? weekName,
      int? segmentId,
      String? segmentName,
      bool? today,
      bool? checked,
      int? studyStage}) = _$_LessonInfo;

  factory _LessonInfo.fromJson(Map<String, dynamic> json) =
      _$_LessonInfo.fromJson;

  @override
  int? get lessonId;
  set lessonId(int? value);
  @override
  String? get lessonTitle;
  set lessonTitle(String? value);
  @override
  String? get lessonSubTitle;
  set lessonSubTitle(String? value);
  @override
  String? get lessonOrder;
  set lessonOrder(String? value);
  @override
  int? get order;
  set order(int? value);
  @override
  int? get weekId;
  set weekId(int? value);
  @override
  String? get weekName;
  set weekName(String? value);
  @override
  int? get segmentId;
  set segmentId(int? value);
  @override
  String? get segmentName;
  set segmentName(String? value);
  @override
  bool? get today;
  set today(bool? value);
  @override
  bool? get checked;
  set checked(bool? value);
  @override
  int? get studyStage;
  set studyStage(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
