// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_books_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_QuestionBooksModel _$$_QuestionBooksModelFromJson(
        Map<String, dynamic> json) =>
    _$_QuestionBooksModel(
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      fontPack: json['fontPack'] as int?,
      isShowEliminateAllButton: json['isShowEliminateAllButton'] as bool?,
      offset: json['offset'] as String?,
      totalQuestionCount: json['totalQuestionCount'] as int?,
      units: (json['units'] as List<dynamic>?)
          ?.map((e) => UnitModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessons: (json['lessons'] as List<dynamic>?)
          ?.map((e) => LessonModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_QuestionBooksModelToJson(
        _$_QuestionBooksModel instance) =>
    <String, dynamic>{
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'fontPack': instance.fontPack,
      'isShowEliminateAllButton': instance.isShowEliminateAllButton,
      'offset': instance.offset,
      'totalQuestionCount': instance.totalQuestionCount,
      'units': instance.units,
      'lessons': instance.lessons,
    };

_$_UnitModel _$$_UnitModelFromJson(Map<String, dynamic> json) => _$_UnitModel(
      classKey: json['classKey'] as String?,
      classId: json['classId'] as int?,
      month: json['month'] as String?,
      startTimestamp: json['startTimestamp'] as int?,
      unitName: json['unitName'] as String?,
      unitId: json['unitId'] as String?,
      questionCount: json['questionCount'] as int?,
    );

Map<String, dynamic> _$$_UnitModelToJson(_$_UnitModel instance) =>
    <String, dynamic>{
      'classKey': instance.classKey,
      'classId': instance.classId,
      'month': instance.month,
      'startTimestamp': instance.startTimestamp,
      'unitName': instance.unitName,
      'unitId': instance.unitId,
      'questionCount': instance.questionCount,
    };

_$_LessonModel _$$_LessonModelFromJson(Map<String, dynamic> json) =>
    _$_LessonModel(
      id: json['id'] as int?,
      key: json['key'] as String?,
      name: json['name'] as String?,
      subTitle: json['subTitle'] as String?,
      questionCount: json['questionCount'] as int?,
      showPoint: json['showPoint'] as bool?,
      status: json['status'] as String?,
      lessonStartTime: json['lessonStartTime'] as int?,
      route: json['route'] as String?,
      nativeErrorQuestion: json['nativeErrorQuestion'] as int?,
      unitId: json['unitId'] as String?,
      lessonOrder: json['lessonOrder'] as int?,
      finishTime: json['finishTime'] as int?,
      lastErrorTime: json['lastErrorTime'] as int?,
      teachType: json['teachType'] as String?,
      aggregationType: json['aggregationType'] as String?,
      fontPack: json['fontPack'] as int?,
      checked: json['checked'] as bool?,
    );

Map<String, dynamic> _$$_LessonModelToJson(_$_LessonModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'key': instance.key,
      'name': instance.name,
      'subTitle': instance.subTitle,
      'questionCount': instance.questionCount,
      'showPoint': instance.showPoint,
      'status': instance.status,
      'lessonStartTime': instance.lessonStartTime,
      'route': instance.route,
      'nativeErrorQuestion': instance.nativeErrorQuestion,
      'unitId': instance.unitId,
      'lessonOrder': instance.lessonOrder,
      'finishTime': instance.finishTime,
      'lastErrorTime': instance.lastErrorTime,
      'teachType': instance.teachType,
      'aggregationType': instance.aggregationType,
      'fontPack': instance.fontPack,
      'checked': instance.checked,
    };

_$_GroupQuestionBookModal _$$_GroupQuestionBookModalFromJson(
        Map<String, dynamic> json) =>
    _$_GroupQuestionBookModal(
      unitId: json['unitId'] as String?,
      unitName: json['unitName'] as String?,
      lessons: (json['lessons'] as List<dynamic>?)
          ?.map((e) => LessonModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_GroupQuestionBookModalToJson(
        _$_GroupQuestionBookModal instance) =>
    <String, dynamic>{
      'unitId': instance.unitId,
      'unitName': instance.unitName,
      'lessons': instance.lessons,
    };
