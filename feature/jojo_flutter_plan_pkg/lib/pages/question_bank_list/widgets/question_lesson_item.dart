import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/question_books_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/checkbox.dart';

/// 错题item
class QuestionLessonItemWidget extends StatefulWidget {
  final LessonModel data;
  final int segmentIndex;
  final int lessonIndex;
  final bool isEnd;
  const QuestionLessonItemWidget({
    super.key,
    required this.data,
    required this.segmentIndex,
    required this.lessonIndex,
    required this.isEnd,
  });

  @override
  State<StatefulWidget> createState() {
    return _QuestionLessonItemWidgetState();
  }
}

class _QuestionLessonItemWidgetState extends State<QuestionLessonItemWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: widget.isEnd ? 0 : 24.rdp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: pt(230)),
                child: Text(
                  widget.data.subTitle ?? '',
                  style: TextStyle(
                    fontSize: pt(16),
                    color: HexColor('#404040'),
                    fontWeight: FontWeight.bold,
                    height: 1.5,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(top: pt(4)),
                constraints: BoxConstraints(maxWidth: pt(230)),
                child: Text(
                  '第${widget.data.lessonOrder}次${widget.data.name}',
                  style: TextStyle(
                    fontSize: pt(14),
                    color: HexColor('#666666'),
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Container(
                padding: EdgeInsets.only(right: 8.rdp),
                child: Text(
                  '共${widget.data.questionCount}道',
                  style: TextStyle(
                    fontSize: pt(14),
                    color: HexColor('#B2B2B2'),
                    height: 1.5,
                  ),
                ),
              ),
              CircularCheckbox(
                value: widget.data.checked == true,
                onChanged: (val) {
                  RunEnv.sensorsTrack('\$AppClick', {
                    'lesson_key': widget.data.key?.toString() ?? '',
                    '\$element_name': '课时选择按钮',
                    '\$screen_name': '打印列表页',
                  });
                  context.read<QuestionBankListCtrl>().toggleQuestionLesson(
                        segmentIndex: widget.segmentIndex,
                        lessonIndex: widget.lessonIndex,
                        isChecked: val ?? false,
                      );
                },
              )
            ],
          )
        ],
      ),
    );
  }
}
