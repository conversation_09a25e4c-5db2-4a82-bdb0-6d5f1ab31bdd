import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/course_lesson_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/checkbox.dart';

/// 课时题目item
class LessonItemWidget extends StatefulWidget {
  final LessonInfo data;
  final int lessonIndex; // 当前数据索引
  final int weekGroupIndex; // 分组数据
  final int studyStageIndex; // 分周的数据
  const LessonItemWidget({
    super.key,
    required this.data,
    required this.lessonIndex,
    required this.weekGroupIndex,
    required this.studyStageIndex,
  });

  @override
  State<StatefulWidget> createState() {
    return _LessonItemWidgetState();
  }
}

class _LessonItemWidgetState extends State<LessonItemWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 24.rdp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.data.lessonSubTitle != null)
                Container(
                  constraints: BoxConstraints(maxWidth: pt(250)),
                  child: Text(
                    widget.data.lessonSubTitle ?? '',
                    style: TextStyle(
                      fontSize: pt(16),
                      color: HexColor('#404040'),
                      fontWeight: FontWeight.bold,
                      height: 1.5,
                    ),
                  ),
                ),
              Container(
                padding: EdgeInsets.only(top: pt(4)),
                constraints: BoxConstraints(maxWidth: pt(250)),
                child: Text(
                  '${widget.data.lessonOrder}${widget.data.lessonTitle}',
                  style: TextStyle(
                    fontSize: pt(14),
                    color: HexColor('#666666'),
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
          CircularCheckbox(
              value: widget.data.checked == true,
              onChanged: (val) {
                RunEnv.sensorsTrack('\$AppClick', {
                  'lesson_key': widget.data.lessonId?.toString() ?? '',
                  '\$element_name': '课时选择按钮',
                  '\$screen_name': '打印列表页',
                });
                context.read<QuestionBankListCtrl>().toggleLesson(
                      weekIndex: widget.studyStageIndex,
                      segmentIndex: widget.weekGroupIndex,
                      lessonIndex: widget.lessonIndex,
                      isChecked: val ?? false,
                    );
              })
        ],
      ),
    );
  }
}
