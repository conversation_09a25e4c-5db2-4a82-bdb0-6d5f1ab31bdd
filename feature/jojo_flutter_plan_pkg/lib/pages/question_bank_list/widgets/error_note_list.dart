import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/question_books_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/checkbox.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/empty_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/question_lesson_item.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

/// Tab 列表
const List<WrongQuestionTab> wrongQuestionTabs = [
  WrongQuestionTab(title: '未消灭', status: WrongQuestionStatus.remaining),
  WrongQuestionTab(title: '已消灭', status: WrongQuestionStatus.eliminated),
];

/// 错误列表
class ErrorNoteListWidget extends StatefulWidget {
  const ErrorNoteListWidget({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _ErrorNoteListWidgetState();
  }
}

class _ErrorNoteListWidgetState extends State<ErrorNoteListWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onRefresh() async {
    final _ctr = context.read<QuestionBankListCtrl>();
    await _ctr.getQuestionBookLessonData();
  }

  void _loadMore() async {
    final _ctr = context.read<QuestionBankListCtrl>();
    await _ctr.loadMoreQuestionList();
  }

  @override
  Widget build(BuildContext context) {
    final _state = context.read<QuestionBankListCtrl>().state;
    final _questionBookLessonInfo = _state.questionBookLessonInfo;

    return Container(
      color: HexColor('#FBFCE8'),
      child: Column(
        children: [
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 20.rdp, vertical: 10.rdp),
            child: Row(
              children: [
                ...wrongQuestionTabs.map(
                  (item) => _buildTabSwitchBtn(
                    tabItem: item,
                    selectStatus: _state.wrongQuestionStatus,
                    onTap: () {
                      changeTab(item.status);
                    },
                  ),
                )
              ],
            ),
          ),
          if (_state.isQuestionLoading == true)
            Container(
              margin: EdgeInsets.only(top: 197.rdp),
              child: Align(
                alignment: Alignment.center, // 保持居中
                child: ImageAssetWeb(
                  assetName: AssetsImg.SXZ,
                  width: 135.rdp,
                  package: Config.package,
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          if (_state.isQuestionLoading == false &&
              _state.isQuestionBookError == true)
            QuestionBankEmptyWidget(
              tipsText: '题目藏起来，点击重试找找！～',
              onRetry: () {
                context
                    .read<QuestionBankListCtrl>()
                    .getQuestionBookLessonData(isShowLoading: true);
              },
            ),
          if (_state.isQuestionLoading == false &&
              _state.isQuestionBookError == false &&
              (_questionBookLessonInfo == null ||
                  _questionBookLessonInfo.isEmpty == true))
            QuestionBankEmptyWidget(
              tipsText:
                  _state.wrongQuestionStatus == WrongQuestionStatus.remaining
                      ? '当前没有未消灭的错题哦！'
                      : '当前没有已消灭的错题哦',
            ),
          if (_questionBookLessonInfo?.isNotEmpty == true &&
              _state.isQuestionLoading == false)
            Expanded(
              child: PullOrRefresh(
                onLoading: _loadMore,
                onRefresh: _onRefresh,
                refreshController: context
                    .read<QuestionBankListCtrl>()
                    .questionRefreshController,
                child: SingleChildScrollView(
                  child: Container(
                    width: 335.rdp,
                    padding: EdgeInsets.all(16.rdp),
                    margin: EdgeInsets.all(20.rdp),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.rdp),
                      color: Colors.white,
                    ),
                    child: Column(
                      children: [
                        ...List.generate(_questionBookLessonInfo!.length,
                            (index) {
                          final _questionBookItem =
                              _questionBookLessonInfo[index];
                          final _lessonList = _questionBookItem.lessons;
                          return Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    constraints:
                                        BoxConstraints(maxWidth: 190.rdp),
                                    margin: EdgeInsets.only(
                                      bottom: 24.rdp,
                                      top: index == 0 ? 0 : 24.rdp,
                                    ),
                                    child: Text(
                                      _questionBookItem.unitName ?? '',
                                      style: TextStyle(
                                        color: HexColor('#A9B52D'),
                                        fontSize: 18.rdp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  CircularCheckbox(
                                    value: context
                                        .read<QuestionBankListCtrl>()
                                        .updateQuestionSegmentChecked(index),
                                    onChanged: (val) {
                                      RunEnv.sensorsTrack('\$AppClick', {
                                        '\$element_name': '课时选择按钮_标题',
                                        '\$screen_name': '打印列表页',
                                      });
                                      context
                                          .read<QuestionBankListCtrl>()
                                          .toggleQuestionSegment(
                                            segmentIndex: index,
                                            isChecked: val ?? false,
                                          );
                                    },
                                  )
                                ],
                              ),
                              if (_lessonList?.isNotEmpty == true)
                                ...List.generate(_lessonList!.length,
                                    (lessonIndex) {
                                  final LessonModel lessonInfo =
                                      _lessonList[lessonIndex];
                                  return QuestionLessonItemWidget(
                                    data: lessonInfo,
                                    segmentIndex: index,
                                    lessonIndex: lessonIndex,
                                    isEnd:
                                        lessonIndex + 1 == _lessonList.length,
                                  );
                                })
                            ],
                          );
                        })
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 切换
  void changeTab(WrongQuestionStatus status) {
    final _ctr = context.read<QuestionBankListCtrl>();

    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': status == WrongQuestionStatus.remaining
          ? '题库打印_错题内容_未消灭'
          : '题库打印_错题内容_已消灭',
      ...context.read<QuestionBankListCtrl>().sensorsParams(),
    });
    _ctr.getQuestionBookLessonData(
      wrongQuestionStatus: status,
      isShowLoading: true,
    );
    _ctr.setQuestionStatus(status);
  }

// 切换按钮
  Widget _buildTabSwitchBtn({
    required WrongQuestionTab tabItem,
    required WrongQuestionStatus selectStatus,
    required Function onTap,
  }) {
    final bool isSelected = selectStatus == tabItem.status;
    return GestureDetector(
      onTap: () {
        onTap();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 24.rdp,
        width: 60.rdp,
        alignment: Alignment.center,
        margin: EdgeInsets.only(right: 18.rdp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(25.rdp)),
          color: isSelected ? HexColor('#FCDA00') : HexColor('#F5F4F4'),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              tabItem.title,
              style: TextStyle(
                color: HexColor('#404040'),
                fontSize: 14.rdp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
