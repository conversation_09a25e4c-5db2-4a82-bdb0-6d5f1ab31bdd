import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

/// 空状态组件&错误重试组件
class QuestionBankEmptyWidget extends StatefulWidget {
  final String tipsText;
  final String? tipsImage;
  final Function? onRetry;
  const QuestionBankEmptyWidget({
    super.key,
    required this.tipsText,
    this.tipsImage,
    this.onRetry,
  });

  @override
  State<StatefulWidget> createState() {
    return _QuestionBankEmptyState();
  }
}

class _QuestionBankEmptyState extends State<QuestionBankEmptyWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 335.rdp,
      margin: EdgeInsets.only(top: 20.rdp),
      padding: EdgeInsets.symmetric(vertical: 60.rdp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(16.rdp)),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          ImageAssetWeb(
            width: 160.rdp,
            height: 160.rdp,
            assetName: AssetsImg.AFTER_LESSON_EMPTY,
            package: Config.package,
          ),
          SizedBox(height: 4.rdp),
          Text(
            widget.tipsText,
            style: TextStyle(
              fontSize: 16.rdp,
              fontWeight: FontWeight.w400,
              color: HexColor('#B2B2B2'),
            ),
          ),
          if (widget.onRetry != null)
            Container(
              margin: EdgeInsets.only(top: 20.rdp),
              child: JoJoBtn(
                text: '重试',
                width: 118.rdp,
                height: 43.rdp,
                color: HexColor('#FCDA00'),
                tapHandle: () {
                  widget.onRetry!();
                },
              ),
            ),
        ],
      ),
    );
  }
}
