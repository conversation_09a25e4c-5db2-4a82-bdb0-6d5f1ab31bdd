import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

/// 全部题目icon提示气泡组件
class GuideTooltipWidget extends StatefulWidget {
  final int? studyStage;
  const GuideTooltipWidget({
    super.key,
    required this.studyStage,
  });

  @override
  State<StatefulWidget> createState() {
    return _GuideTooltipWidgetState();
  }
}

class _GuideTooltipWidgetState extends State<GuideTooltipWidget> {
  final GlobalKey _targetKey = GlobalKey();
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var guideText = '';
    if (widget.studyStage == 1) {
      guideText = '以下内容将在下周进行学习，可提前打印题目进行预习';
    } else if (widget.studyStage == 2) {
      guideText = '本周正在学习以下内容，可打印题目进行配套练习';
    } else if (widget.studyStage == 3) {
      guideText = '往期学过以下内容，可打印题目进行进一步巩固';
    }
    var sensorsName = '下周预习_说明';
    if (widget.studyStage == 1) {
      sensorsName = '下周预习_说明';
    } else if (widget.studyStage == 2) {
      sensorsName = '本周在学_说明';
    } else if (widget.studyStage == 3) {
      sensorsName = '往期内容_说明';
    }
    // 若果文字一行显示不下，两行的话第二行又只有一两个字的时候，能否自动缩短气泡，让第二行文字多一点
    final _width = guideText.length > 23 ? 259.rdp : 330.rdp;
    final _textWidth = guideText.length > 23 ? 202.rdp : 280.rdp;
    return GestureDetector(
      onTap: () {
        RunEnv.sensorsTrack('\$AppClick', {
          '\$element_name': sensorsName,
          ...context.read<QuestionBankListCtrl>().sensorsParams(),
        });
        SmartDialog.showAttach(
          targetContext: _targetKey.currentContext,
          clickMaskDismiss: true,
          usePenetrate: true,
          keepSingle: true,
          debounce: true,
          useAnimation: false,
          alignment: Alignment.bottomRight,
          builder: (_) => Container(
            width: _width,
            margin: EdgeInsets.only(left: 20.rdp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 58.rdp),
                  child: ImageAssetWeb(
                    width: 20.rdp,
                    fit: BoxFit.fitWidth,
                    assetName: AssetsImg.QUESTION_BANK_LIST_TRIANGLE_SYMBOL,
                    package: Config.package,
                  ),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.rdp, vertical: 8.rdp),
                  decoration: BoxDecoration(
                    color: HexColor('#000000').withOpacity(0.7),
                    borderRadius: BorderRadius.circular(16.rdp),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: EdgeInsets.only(right: 8.rdp),
                        width: _textWidth,
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: HexColor('#FFFFFF'),
                              width: 1.rdp,
                            ),
                          ),
                        ),
                        child: Text(
                          guideText,
                          maxLines: 2,
                          style: TextStyle(
                            fontSize: 12.rdp,
                            fontWeight: FontWeight.w400,
                            color: HexColor('#FFFFFF'),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          SmartDialog.dismiss();
                        },
                        behavior: HitTestBehavior.opaque,
                        child: ImageAssetWeb(
                          width: 16.rdp,
                          height: 16.rdp,
                          assetName: AssetsImg.QUESTION_BANK_LIST_TICK,
                          package: Config.package,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
      child: SizedBox(
        key: _targetKey,
        child: SvgAssetWeb(
          assetName: AssetsSvg.TOOLTIP,
          width: 16.rdp,
          height: 16.rdp,
          package: RunEnv.package,
        ),
      ),
    );
  }
}
