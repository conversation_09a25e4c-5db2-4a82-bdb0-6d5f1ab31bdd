import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/custom_width_tab_indicator.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/error_note_list.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/widgets/question_list.dart';

/// tab组件切换
class TabSwitchWidget extends StatefulWidget {
  const TabSwitchWidget({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _TabSwitchState();
  }
}

class _TabSwitchState extends State<TabSwitchWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<Tab> myTabs = <Tab>[
    const Tab(child: Center(child: Text('全部题目'))),
    const Tab(child: Center(child: Text('仅错题'))),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: myTabs.length, vsync: this);
  }

  void onTabChanged(int index) {
    final _ctr = context.read<QuestionBankListCtrl>();
    // 全部题目
    if (index == 0) {
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': '题库打印_练习内容',
        ..._ctr.sensorsParams(),
      });

      _ctr.getLessonsData(isShowLoading: true);
      _ctr.setTabIndex(0);
    } else if (index == 1) {
      // 错题本
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': '题库打印_错题内容',
        ..._ctr.sensorsParams(),
      });
      _ctr.getQuestionBookLessonData(isShowLoading: true);
      _ctr.setTabIndex(1);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        width: screen.screenWidth,
        child: Column(
          children: [
            TabBar(
              controller: _tabController,
              onTap: (index) {
                onTabChanged(index);
              },
              tabs: myTabs,
              labelColor: HexColor('#404040'),
              unselectedLabelColor: HexColor('#B2B2B2'),
              labelStyle: TextStyle(
                fontSize: 16.rdp,
                fontWeight: FontWeight.bold,
                color: HexColor('#404040'),
              ),
              unselectedLabelStyle:
                  TextStyle(fontSize: 16.rdp, color: HexColor('#B2B2B2')),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorPadding: EdgeInsets.only(
                top: 32.rdp,
                bottom: 2.rdp,
                left: 0,
                right: 0,
              ),
              overlayColor:
                  MaterialStateProperty.all(Colors.transparent), // ✅ 取消点击阴影
              indicator: CustomWidthUnderlineTabIndicator(
                customWidth: 24,
                borderSide: BorderSide(width: 3.rdp, color: Colors.amber),
                borderRadius: BorderRadius.circular(2),
              ),
              isScrollable: false,
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                // ignore: prefer_const_literals_to_create_immutables
                children: [
                  // ignore: prefer_const_constructors
                  QuestionListWidget(),
                  // ignore: prefer_const_constructors
                  ErrorNoteListWidget(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
