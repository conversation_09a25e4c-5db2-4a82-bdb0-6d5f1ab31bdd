// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'drainage_course_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SubjectInfo _$SubjectInfoFromJson(Map<String, dynamic> json) {
  return _SubjectInfo.fromJson(json);
}

/// @nodoc
mixin _$SubjectInfo {
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get padPictureUrl => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  String? get titleColor => throw _privateConstructorUsedError;
  String? get descriptionColor => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get subDesc => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  String? get typeDesc => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get selectedIcon => throw _privateConstructorUsedError;
  String? get selectedTextColor => throw _privateConstructorUsedError;
  String? get selectedBgColor => throw _privateConstructorUsedError;
  String? get libId => throw _privateConstructorUsedError;
  List<GradeInfo>? get gradeInfoList => throw _privateConstructorUsedError;
  String? get missionMaterialType => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  int? get lastSelectedTipType => throw _privateConstructorUsedError;
  bool? get lastSelected => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectInfoCopyWith<SubjectInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectInfoCopyWith<$Res> {
  factory $SubjectInfoCopyWith(
          SubjectInfo value, $Res Function(SubjectInfo) then) =
      _$SubjectInfoCopyWithImpl<$Res, SubjectInfo>;
  @useResult
  $Res call(
      {String? pictureUrl,
      String? padPictureUrl,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? btnText,
      String? subDesc,
      int? type,
      String? typeDesc,
      String? subjectTypeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedTextColor,
      String? selectedBgColor,
      String? libId,
      List<GradeInfo>? gradeInfoList,
      String? missionMaterialType,
      String? linkUrl,
      int? lastSelectedTipType,
      bool? lastSelected});
}

/// @nodoc
class _$SubjectInfoCopyWithImpl<$Res, $Val extends SubjectInfo>
    implements $SubjectInfoCopyWith<$Res> {
  _$SubjectInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pictureUrl = freezed,
    Object? padPictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? btnText = freezed,
    Object? subDesc = freezed,
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? subjectTypeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? selectedBgColor = freezed,
    Object? libId = freezed,
    Object? gradeInfoList = freezed,
    Object? missionMaterialType = freezed,
    Object? linkUrl = freezed,
    Object? lastSelectedTipType = freezed,
    Object? lastSelected = freezed,
  }) {
    return _then(_value.copyWith(
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      padPictureUrl: freezed == padPictureUrl
          ? _value.padPictureUrl
          : padPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      subDesc: freezed == subDesc
          ? _value.subDesc
          : subDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      libId: freezed == libId
          ? _value.libId
          : libId // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeInfoList: freezed == gradeInfoList
          ? _value.gradeInfoList
          : gradeInfoList // ignore: cast_nullable_to_non_nullable
              as List<GradeInfo>?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSelectedTipType: freezed == lastSelectedTipType
          ? _value.lastSelectedTipType
          : lastSelectedTipType // ignore: cast_nullable_to_non_nullable
              as int?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectInfoCopyWith<$Res>
    implements $SubjectInfoCopyWith<$Res> {
  factory _$$_SubjectInfoCopyWith(
          _$_SubjectInfo value, $Res Function(_$_SubjectInfo) then) =
      __$$_SubjectInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? pictureUrl,
      String? padPictureUrl,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? btnText,
      String? subDesc,
      int? type,
      String? typeDesc,
      String? subjectTypeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedTextColor,
      String? selectedBgColor,
      String? libId,
      List<GradeInfo>? gradeInfoList,
      String? missionMaterialType,
      String? linkUrl,
      int? lastSelectedTipType,
      bool? lastSelected});
}

/// @nodoc
class __$$_SubjectInfoCopyWithImpl<$Res>
    extends _$SubjectInfoCopyWithImpl<$Res, _$_SubjectInfo>
    implements _$$_SubjectInfoCopyWith<$Res> {
  __$$_SubjectInfoCopyWithImpl(
      _$_SubjectInfo _value, $Res Function(_$_SubjectInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pictureUrl = freezed,
    Object? padPictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? btnText = freezed,
    Object? subDesc = freezed,
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? subjectTypeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? selectedBgColor = freezed,
    Object? libId = freezed,
    Object? gradeInfoList = freezed,
    Object? missionMaterialType = freezed,
    Object? linkUrl = freezed,
    Object? lastSelectedTipType = freezed,
    Object? lastSelected = freezed,
  }) {
    return _then(_$_SubjectInfo(
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      padPictureUrl: freezed == padPictureUrl
          ? _value.padPictureUrl
          : padPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      subDesc: freezed == subDesc
          ? _value.subDesc
          : subDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      libId: freezed == libId
          ? _value.libId
          : libId // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeInfoList: freezed == gradeInfoList
          ? _value._gradeInfoList
          : gradeInfoList // ignore: cast_nullable_to_non_nullable
              as List<GradeInfo>?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSelectedTipType: freezed == lastSelectedTipType
          ? _value.lastSelectedTipType
          : lastSelectedTipType // ignore: cast_nullable_to_non_nullable
              as int?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectInfo implements _SubjectInfo {
  const _$_SubjectInfo(
      {this.pictureUrl,
      this.padPictureUrl,
      this.backgroundColor,
      this.titleColor,
      this.descriptionColor,
      this.btnText,
      this.subDesc,
      this.type,
      this.typeDesc,
      this.subjectTypeDesc,
      this.icon,
      this.selectedIcon,
      this.selectedTextColor,
      this.selectedBgColor,
      this.libId,
      final List<GradeInfo>? gradeInfoList,
      this.missionMaterialType,
      this.linkUrl,
      this.lastSelectedTipType,
      this.lastSelected})
      : _gradeInfoList = gradeInfoList;

  factory _$_SubjectInfo.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectInfoFromJson(json);

  @override
  final String? pictureUrl;
  @override
  final String? padPictureUrl;
  @override
  final String? backgroundColor;
  @override
  final String? titleColor;
  @override
  final String? descriptionColor;
  @override
  final String? btnText;
  @override
  final String? subDesc;
  @override
  final int? type;
  @override
  final String? typeDesc;
  @override
  final String? subjectTypeDesc;
  @override
  final String? icon;
  @override
  final String? selectedIcon;
  @override
  final String? selectedTextColor;
  @override
  final String? selectedBgColor;
  @override
  final String? libId;
  final List<GradeInfo>? _gradeInfoList;
  @override
  List<GradeInfo>? get gradeInfoList {
    final value = _gradeInfoList;
    if (value == null) return null;
    if (_gradeInfoList is EqualUnmodifiableListView) return _gradeInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? missionMaterialType;
  @override
  final String? linkUrl;
  @override
  final int? lastSelectedTipType;
  @override
  final bool? lastSelected;

  @override
  String toString() {
    return 'SubjectInfo(pictureUrl: $pictureUrl, padPictureUrl: $padPictureUrl, backgroundColor: $backgroundColor, titleColor: $titleColor, descriptionColor: $descriptionColor, btnText: $btnText, subDesc: $subDesc, type: $type, typeDesc: $typeDesc, subjectTypeDesc: $subjectTypeDesc, icon: $icon, selectedIcon: $selectedIcon, selectedTextColor: $selectedTextColor, selectedBgColor: $selectedBgColor, libId: $libId, gradeInfoList: $gradeInfoList, missionMaterialType: $missionMaterialType, linkUrl: $linkUrl, lastSelectedTipType: $lastSelectedTipType, lastSelected: $lastSelected)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectInfo &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.padPictureUrl, padPictureUrl) ||
                other.padPictureUrl == padPictureUrl) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.titleColor, titleColor) ||
                other.titleColor == titleColor) &&
            (identical(other.descriptionColor, descriptionColor) ||
                other.descriptionColor == descriptionColor) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.subDesc, subDesc) || other.subDesc == subDesc) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.typeDesc, typeDesc) ||
                other.typeDesc == typeDesc) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.selectedIcon, selectedIcon) ||
                other.selectedIcon == selectedIcon) &&
            (identical(other.selectedTextColor, selectedTextColor) ||
                other.selectedTextColor == selectedTextColor) &&
            (identical(other.selectedBgColor, selectedBgColor) ||
                other.selectedBgColor == selectedBgColor) &&
            (identical(other.libId, libId) || other.libId == libId) &&
            const DeepCollectionEquality()
                .equals(other._gradeInfoList, _gradeInfoList) &&
            (identical(other.missionMaterialType, missionMaterialType) ||
                other.missionMaterialType == missionMaterialType) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.lastSelectedTipType, lastSelectedTipType) ||
                other.lastSelectedTipType == lastSelectedTipType) &&
            (identical(other.lastSelected, lastSelected) ||
                other.lastSelected == lastSelected));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        pictureUrl,
        padPictureUrl,
        backgroundColor,
        titleColor,
        descriptionColor,
        btnText,
        subDesc,
        type,
        typeDesc,
        subjectTypeDesc,
        icon,
        selectedIcon,
        selectedTextColor,
        selectedBgColor,
        libId,
        const DeepCollectionEquality().hash(_gradeInfoList),
        missionMaterialType,
        linkUrl,
        lastSelectedTipType,
        lastSelected
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectInfoCopyWith<_$_SubjectInfo> get copyWith =>
      __$$_SubjectInfoCopyWithImpl<_$_SubjectInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectInfoToJson(
      this,
    );
  }
}

abstract class _SubjectInfo implements SubjectInfo {
  const factory _SubjectInfo(
      {final String? pictureUrl,
      final String? padPictureUrl,
      final String? backgroundColor,
      final String? titleColor,
      final String? descriptionColor,
      final String? btnText,
      final String? subDesc,
      final int? type,
      final String? typeDesc,
      final String? subjectTypeDesc,
      final String? icon,
      final String? selectedIcon,
      final String? selectedTextColor,
      final String? selectedBgColor,
      final String? libId,
      final List<GradeInfo>? gradeInfoList,
      final String? missionMaterialType,
      final String? linkUrl,
      final int? lastSelectedTipType,
      final bool? lastSelected}) = _$_SubjectInfo;

  factory _SubjectInfo.fromJson(Map<String, dynamic> json) =
      _$_SubjectInfo.fromJson;

  @override
  String? get pictureUrl;
  @override
  String? get padPictureUrl;
  @override
  String? get backgroundColor;
  @override
  String? get titleColor;
  @override
  String? get descriptionColor;
  @override
  String? get btnText;
  @override
  String? get subDesc;
  @override
  int? get type;
  @override
  String? get typeDesc;
  @override
  String? get subjectTypeDesc;
  @override
  String? get icon;
  @override
  String? get selectedIcon;
  @override
  String? get selectedTextColor;
  @override
  String? get selectedBgColor;
  @override
  String? get libId;
  @override
  List<GradeInfo>? get gradeInfoList;
  @override
  String? get missionMaterialType;
  @override
  String? get linkUrl;
  @override
  int? get lastSelectedTipType;
  @override
  bool? get lastSelected;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectInfoCopyWith<_$_SubjectInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

GradeInfo _$GradeInfoFromJson(Map<String, dynamic> json) {
  return _GradeInfo.fromJson(json);
}

/// @nodoc
mixin _$GradeInfo {
  bool? get lastSelect => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  String? get gradeDesc => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get resourceId => throw _privateConstructorUsedError;
  List<CourseCardData>? get courseCardDataList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GradeInfoCopyWith<GradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GradeInfoCopyWith<$Res> {
  factory $GradeInfoCopyWith(GradeInfo value, $Res Function(GradeInfo) then) =
      _$GradeInfoCopyWithImpl<$Res, GradeInfo>;
  @useResult
  $Res call(
      {bool? lastSelect,
      int? gradeKey,
      String? gradeTitle,
      String? gradeDesc,
      String? tip,
      String? resourceId,
      List<CourseCardData>? courseCardDataList});
}

/// @nodoc
class _$GradeInfoCopyWithImpl<$Res, $Val extends GradeInfo>
    implements $GradeInfoCopyWith<$Res> {
  _$GradeInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastSelect = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? gradeDesc = freezed,
    Object? tip = freezed,
    Object? resourceId = freezed,
    Object? courseCardDataList = freezed,
  }) {
    return _then(_value.copyWith(
      lastSelect: freezed == lastSelect
          ? _value.lastSelect
          : lastSelect // ignore: cast_nullable_to_non_nullable
              as bool?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeDesc: freezed == gradeDesc
          ? _value.gradeDesc
          : gradeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCardDataList: freezed == courseCardDataList
          ? _value.courseCardDataList
          : courseCardDataList // ignore: cast_nullable_to_non_nullable
              as List<CourseCardData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GradeInfoCopyWith<$Res> implements $GradeInfoCopyWith<$Res> {
  factory _$$_GradeInfoCopyWith(
          _$_GradeInfo value, $Res Function(_$_GradeInfo) then) =
      __$$_GradeInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? lastSelect,
      int? gradeKey,
      String? gradeTitle,
      String? gradeDesc,
      String? tip,
      String? resourceId,
      List<CourseCardData>? courseCardDataList});
}

/// @nodoc
class __$$_GradeInfoCopyWithImpl<$Res>
    extends _$GradeInfoCopyWithImpl<$Res, _$_GradeInfo>
    implements _$$_GradeInfoCopyWith<$Res> {
  __$$_GradeInfoCopyWithImpl(
      _$_GradeInfo _value, $Res Function(_$_GradeInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastSelect = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? gradeDesc = freezed,
    Object? tip = freezed,
    Object? resourceId = freezed,
    Object? courseCardDataList = freezed,
  }) {
    return _then(_$_GradeInfo(
      lastSelect: freezed == lastSelect
          ? _value.lastSelect
          : lastSelect // ignore: cast_nullable_to_non_nullable
              as bool?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeDesc: freezed == gradeDesc
          ? _value.gradeDesc
          : gradeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCardDataList: freezed == courseCardDataList
          ? _value._courseCardDataList
          : courseCardDataList // ignore: cast_nullable_to_non_nullable
              as List<CourseCardData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GradeInfo implements _GradeInfo {
  const _$_GradeInfo(
      {this.lastSelect,
      this.gradeKey,
      this.gradeTitle,
      this.gradeDesc,
      this.tip,
      this.resourceId,
      final List<CourseCardData>? courseCardDataList})
      : _courseCardDataList = courseCardDataList;

  factory _$_GradeInfo.fromJson(Map<String, dynamic> json) =>
      _$$_GradeInfoFromJson(json);

  @override
  final bool? lastSelect;
  @override
  final int? gradeKey;
  @override
  final String? gradeTitle;
  @override
  final String? gradeDesc;
  @override
  final String? tip;
  @override
  final String? resourceId;
  final List<CourseCardData>? _courseCardDataList;
  @override
  List<CourseCardData>? get courseCardDataList {
    final value = _courseCardDataList;
    if (value == null) return null;
    if (_courseCardDataList is EqualUnmodifiableListView)
      return _courseCardDataList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GradeInfo(lastSelect: $lastSelect, gradeKey: $gradeKey, gradeTitle: $gradeTitle, gradeDesc: $gradeDesc, tip: $tip, resourceId: $resourceId, courseCardDataList: $courseCardDataList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GradeInfo &&
            (identical(other.lastSelect, lastSelect) ||
                other.lastSelect == lastSelect) &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.gradeDesc, gradeDesc) ||
                other.gradeDesc == gradeDesc) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.resourceId, resourceId) ||
                other.resourceId == resourceId) &&
            const DeepCollectionEquality()
                .equals(other._courseCardDataList, _courseCardDataList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      lastSelect,
      gradeKey,
      gradeTitle,
      gradeDesc,
      tip,
      resourceId,
      const DeepCollectionEquality().hash(_courseCardDataList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GradeInfoCopyWith<_$_GradeInfo> get copyWith =>
      __$$_GradeInfoCopyWithImpl<_$_GradeInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GradeInfoToJson(
      this,
    );
  }
}

abstract class _GradeInfo implements GradeInfo {
  const factory _GradeInfo(
      {final bool? lastSelect,
      final int? gradeKey,
      final String? gradeTitle,
      final String? gradeDesc,
      final String? tip,
      final String? resourceId,
      final List<CourseCardData>? courseCardDataList}) = _$_GradeInfo;

  factory _GradeInfo.fromJson(Map<String, dynamic> json) =
      _$_GradeInfo.fromJson;

  @override
  bool? get lastSelect;
  @override
  int? get gradeKey;
  @override
  String? get gradeTitle;
  @override
  String? get gradeDesc;
  @override
  String? get tip;
  @override
  String? get resourceId;
  @override
  List<CourseCardData>? get courseCardDataList;
  @override
  @JsonKey(ignore: true)
  _$$_GradeInfoCopyWith<_$_GradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseCardData _$CourseCardDataFromJson(Map<String, dynamic> json) {
  return _CourseCardData.fromJson(json);
}

/// @nodoc
mixin _$CourseCardData {
  List<CourseInfo>? get courseInfoList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseCardDataCopyWith<CourseCardData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseCardDataCopyWith<$Res> {
  factory $CourseCardDataCopyWith(
          CourseCardData value, $Res Function(CourseCardData) then) =
      _$CourseCardDataCopyWithImpl<$Res, CourseCardData>;
  @useResult
  $Res call({List<CourseInfo>? courseInfoList});
}

/// @nodoc
class _$CourseCardDataCopyWithImpl<$Res, $Val extends CourseCardData>
    implements $CourseCardDataCopyWith<$Res> {
  _$CourseCardDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      courseInfoList: freezed == courseInfoList
          ? _value.courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseCardDataCopyWith<$Res>
    implements $CourseCardDataCopyWith<$Res> {
  factory _$$_CourseCardDataCopyWith(
          _$_CourseCardData value, $Res Function(_$_CourseCardData) then) =
      __$$_CourseCardDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CourseInfo>? courseInfoList});
}

/// @nodoc
class __$$_CourseCardDataCopyWithImpl<$Res>
    extends _$CourseCardDataCopyWithImpl<$Res, _$_CourseCardData>
    implements _$$_CourseCardDataCopyWith<$Res> {
  __$$_CourseCardDataCopyWithImpl(
      _$_CourseCardData _value, $Res Function(_$_CourseCardData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfoList = freezed,
  }) {
    return _then(_$_CourseCardData(
      courseInfoList: freezed == courseInfoList
          ? _value._courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseCardData implements _CourseCardData {
  const _$_CourseCardData({final List<CourseInfo>? courseInfoList})
      : _courseInfoList = courseInfoList;

  factory _$_CourseCardData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseCardDataFromJson(json);

  final List<CourseInfo>? _courseInfoList;
  @override
  List<CourseInfo>? get courseInfoList {
    final value = _courseInfoList;
    if (value == null) return null;
    if (_courseInfoList is EqualUnmodifiableListView) return _courseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseCardData(courseInfoList: $courseInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseCardData &&
            const DeepCollectionEquality()
                .equals(other._courseInfoList, _courseInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_courseInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseCardDataCopyWith<_$_CourseCardData> get copyWith =>
      __$$_CourseCardDataCopyWithImpl<_$_CourseCardData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseCardDataToJson(
      this,
    );
  }
}

abstract class _CourseCardData implements CourseCardData {
  const factory _CourseCardData({final List<CourseInfo>? courseInfoList}) =
      _$_CourseCardData;

  factory _CourseCardData.fromJson(Map<String, dynamic> json) =
      _$_CourseCardData.fromJson;

  @override
  List<CourseInfo>? get courseInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseCardDataCopyWith<_$_CourseCardData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfo _$CourseInfoFromJson(Map<String, dynamic> json) {
  return _CourseInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseInfo {
  String? get courseId => throw _privateConstructorUsedError;
  String? get courseTag => throw _privateConstructorUsedError;
  int? get courseTagKey => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  String? get courseDesc => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get cornerMarkerText => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  String? get experienceLink => throw _privateConstructorUsedError;
  String? get linkId => throw _privateConstructorUsedError;
  int? get courseStatus => throw _privateConstructorUsedError;
  List<LessonInfo>? get lessonInfoList => throw _privateConstructorUsedError;
  List<CourseInfo>? get trainingCampCourseInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoCopyWith<CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoCopyWith<$Res> {
  factory $CourseInfoCopyWith(
          CourseInfo value, $Res Function(CourseInfo) then) =
      _$CourseInfoCopyWithImpl<$Res, CourseInfo>;
  @useResult
  $Res call(
      {String? courseId,
      String? courseTag,
      int? courseTagKey,
      int? gradeKey,
      String? gradeTitle,
      String? courseDesc,
      String? tip,
      String? cornerMarkerText,
      String? btnText,
      String? skuId,
      String? skuName,
      String? experienceLink,
      String? linkId,
      int? courseStatus,
      List<LessonInfo>? lessonInfoList,
      List<CourseInfo>? trainingCampCourseInfoList});
}

/// @nodoc
class _$CourseInfoCopyWithImpl<$Res, $Val extends CourseInfo>
    implements $CourseInfoCopyWith<$Res> {
  _$CourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? courseDesc = freezed,
    Object? tip = freezed,
    Object? cornerMarkerText = freezed,
    Object? btnText = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? experienceLink = freezed,
    Object? linkId = freezed,
    Object? courseStatus = freezed,
    Object? lessonInfoList = freezed,
    Object? trainingCampCourseInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      cornerMarkerText: freezed == cornerMarkerText
          ? _value.cornerMarkerText
          : cornerMarkerText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceLink: freezed == experienceLink
          ? _value.experienceLink
          : experienceLink // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfoList: freezed == lessonInfoList
          ? _value.lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
      trainingCampCourseInfoList: freezed == trainingCampCourseInfoList
          ? _value.trainingCampCourseInfoList
          : trainingCampCourseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoCopyWith<$Res>
    implements $CourseInfoCopyWith<$Res> {
  factory _$$_CourseInfoCopyWith(
          _$_CourseInfo value, $Res Function(_$_CourseInfo) then) =
      __$$_CourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseId,
      String? courseTag,
      int? courseTagKey,
      int? gradeKey,
      String? gradeTitle,
      String? courseDesc,
      String? tip,
      String? cornerMarkerText,
      String? btnText,
      String? skuId,
      String? skuName,
      String? experienceLink,
      String? linkId,
      int? courseStatus,
      List<LessonInfo>? lessonInfoList,
      List<CourseInfo>? trainingCampCourseInfoList});
}

/// @nodoc
class __$$_CourseInfoCopyWithImpl<$Res>
    extends _$CourseInfoCopyWithImpl<$Res, _$_CourseInfo>
    implements _$$_CourseInfoCopyWith<$Res> {
  __$$_CourseInfoCopyWithImpl(
      _$_CourseInfo _value, $Res Function(_$_CourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? courseDesc = freezed,
    Object? tip = freezed,
    Object? cornerMarkerText = freezed,
    Object? btnText = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? experienceLink = freezed,
    Object? linkId = freezed,
    Object? courseStatus = freezed,
    Object? lessonInfoList = freezed,
    Object? trainingCampCourseInfoList = freezed,
  }) {
    return _then(_$_CourseInfo(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      cornerMarkerText: freezed == cornerMarkerText
          ? _value.cornerMarkerText
          : cornerMarkerText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceLink: freezed == experienceLink
          ? _value.experienceLink
          : experienceLink // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfoList: freezed == lessonInfoList
          ? _value._lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
      trainingCampCourseInfoList: freezed == trainingCampCourseInfoList
          ? _value._trainingCampCourseInfoList
          : trainingCampCourseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfo implements _CourseInfo {
  const _$_CourseInfo(
      {this.courseId,
      this.courseTag,
      this.courseTagKey,
      this.gradeKey,
      this.gradeTitle,
      this.courseDesc,
      this.tip,
      this.cornerMarkerText,
      this.btnText,
      this.skuId,
      this.skuName,
      this.experienceLink,
      this.linkId,
      this.courseStatus,
      final List<LessonInfo>? lessonInfoList,
      final List<CourseInfo>? trainingCampCourseInfoList})
      : _lessonInfoList = lessonInfoList,
        _trainingCampCourseInfoList = trainingCampCourseInfoList;

  factory _$_CourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoFromJson(json);

  @override
  final String? courseId;
  @override
  final String? courseTag;
  @override
  final int? courseTagKey;
  @override
  final int? gradeKey;
  @override
  final String? gradeTitle;
  @override
  final String? courseDesc;
  @override
  final String? tip;
  @override
  final String? cornerMarkerText;
  @override
  final String? btnText;
  @override
  final String? skuId;
  @override
  final String? skuName;
  @override
  final String? experienceLink;
  @override
  final String? linkId;
  @override
  final int? courseStatus;
  final List<LessonInfo>? _lessonInfoList;
  @override
  List<LessonInfo>? get lessonInfoList {
    final value = _lessonInfoList;
    if (value == null) return null;
    if (_lessonInfoList is EqualUnmodifiableListView) return _lessonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CourseInfo>? _trainingCampCourseInfoList;
  @override
  List<CourseInfo>? get trainingCampCourseInfoList {
    final value = _trainingCampCourseInfoList;
    if (value == null) return null;
    if (_trainingCampCourseInfoList is EqualUnmodifiableListView)
      return _trainingCampCourseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseInfo(courseId: $courseId, courseTag: $courseTag, courseTagKey: $courseTagKey, gradeKey: $gradeKey, gradeTitle: $gradeTitle, courseDesc: $courseDesc, tip: $tip, cornerMarkerText: $cornerMarkerText, btnText: $btnText, skuId: $skuId, skuName: $skuName, experienceLink: $experienceLink, linkId: $linkId, courseStatus: $courseStatus, lessonInfoList: $lessonInfoList, trainingCampCourseInfoList: $trainingCampCourseInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfo &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseTag, courseTag) ||
                other.courseTag == courseTag) &&
            (identical(other.courseTagKey, courseTagKey) ||
                other.courseTagKey == courseTagKey) &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.courseDesc, courseDesc) ||
                other.courseDesc == courseDesc) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.cornerMarkerText, cornerMarkerText) ||
                other.cornerMarkerText == cornerMarkerText) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.experienceLink, experienceLink) ||
                other.experienceLink == experienceLink) &&
            (identical(other.linkId, linkId) || other.linkId == linkId) &&
            (identical(other.courseStatus, courseStatus) ||
                other.courseStatus == courseStatus) &&
            const DeepCollectionEquality()
                .equals(other._lessonInfoList, _lessonInfoList) &&
            const DeepCollectionEquality().equals(
                other._trainingCampCourseInfoList,
                _trainingCampCourseInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseId,
      courseTag,
      courseTagKey,
      gradeKey,
      gradeTitle,
      courseDesc,
      tip,
      cornerMarkerText,
      btnText,
      skuId,
      skuName,
      experienceLink,
      linkId,
      courseStatus,
      const DeepCollectionEquality().hash(_lessonInfoList),
      const DeepCollectionEquality().hash(_trainingCampCourseInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      __$$_CourseInfoCopyWithImpl<_$_CourseInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoToJson(
      this,
    );
  }
}

abstract class _CourseInfo implements CourseInfo {
  const factory _CourseInfo(
      {final String? courseId,
      final String? courseTag,
      final int? courseTagKey,
      final int? gradeKey,
      final String? gradeTitle,
      final String? courseDesc,
      final String? tip,
      final String? cornerMarkerText,
      final String? btnText,
      final String? skuId,
      final String? skuName,
      final String? experienceLink,
      final String? linkId,
      final int? courseStatus,
      final List<LessonInfo>? lessonInfoList,
      final List<CourseInfo>? trainingCampCourseInfoList}) = _$_CourseInfo;

  factory _CourseInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseInfo.fromJson;

  @override
  String? get courseId;
  @override
  String? get courseTag;
  @override
  int? get courseTagKey;
  @override
  int? get gradeKey;
  @override
  String? get gradeTitle;
  @override
  String? get courseDesc;
  @override
  String? get tip;
  @override
  String? get cornerMarkerText;
  @override
  String? get btnText;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  String? get experienceLink;
  @override
  String? get linkId;
  @override
  int? get courseStatus;
  @override
  List<LessonInfo>? get lessonInfoList;
  @override
  List<CourseInfo>? get trainingCampCourseInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoCopyWith<_$_CourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfo _$LessonInfoFromJson(Map<String, dynamic> json) {
  return _LessonInfo.fromJson(json);
}

/// @nodoc
mixin _$LessonInfo {
  String? get lessonId => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String? get lessonDesc => throw _privateConstructorUsedError;
  String? get lessonCoverUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoCopyWith<LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoCopyWith<$Res> {
  factory $LessonInfoCopyWith(
          LessonInfo value, $Res Function(LessonInfo) then) =
      _$LessonInfoCopyWithImpl<$Res, LessonInfo>;
  @useResult
  $Res call(
      {String? lessonId,
      String? lessonName,
      String? lessonDesc,
      String? lessonCoverUrl});
}

/// @nodoc
class _$LessonInfoCopyWithImpl<$Res, $Val extends LessonInfo>
    implements $LessonInfoCopyWith<$Res> {
  _$LessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonDesc = freezed,
    Object? lessonCoverUrl = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonDesc: freezed == lessonDesc
          ? _value.lessonDesc
          : lessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverUrl: freezed == lessonCoverUrl
          ? _value.lessonCoverUrl
          : lessonCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonInfoCopyWith<$Res>
    implements $LessonInfoCopyWith<$Res> {
  factory _$$_LessonInfoCopyWith(
          _$_LessonInfo value, $Res Function(_$_LessonInfo) then) =
      __$$_LessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lessonId,
      String? lessonName,
      String? lessonDesc,
      String? lessonCoverUrl});
}

/// @nodoc
class __$$_LessonInfoCopyWithImpl<$Res>
    extends _$LessonInfoCopyWithImpl<$Res, _$_LessonInfo>
    implements _$$_LessonInfoCopyWith<$Res> {
  __$$_LessonInfoCopyWithImpl(
      _$_LessonInfo _value, $Res Function(_$_LessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonDesc = freezed,
    Object? lessonCoverUrl = freezed,
  }) {
    return _then(_$_LessonInfo(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonDesc: freezed == lessonDesc
          ? _value.lessonDesc
          : lessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverUrl: freezed == lessonCoverUrl
          ? _value.lessonCoverUrl
          : lessonCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfo implements _LessonInfo {
  const _$_LessonInfo(
      {this.lessonId, this.lessonName, this.lessonDesc, this.lessonCoverUrl});

  factory _$_LessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoFromJson(json);

  @override
  final String? lessonId;
  @override
  final String? lessonName;
  @override
  final String? lessonDesc;
  @override
  final String? lessonCoverUrl;

  @override
  String toString() {
    return 'LessonInfo(lessonId: $lessonId, lessonName: $lessonName, lessonDesc: $lessonDesc, lessonCoverUrl: $lessonCoverUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonInfo &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonDesc, lessonDesc) ||
                other.lessonDesc == lessonDesc) &&
            (identical(other.lessonCoverUrl, lessonCoverUrl) ||
                other.lessonCoverUrl == lessonCoverUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, lessonId, lessonName, lessonDesc, lessonCoverUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      __$$_LessonInfoCopyWithImpl<_$_LessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoToJson(
      this,
    );
  }
}

abstract class _LessonInfo implements LessonInfo {
  const factory _LessonInfo(
      {final String? lessonId,
      final String? lessonName,
      final String? lessonDesc,
      final String? lessonCoverUrl}) = _$_LessonInfo;

  factory _LessonInfo.fromJson(Map<String, dynamic> json) =
      _$_LessonInfo.fromJson;

  @override
  String? get lessonId;
  @override
  String? get lessonName;
  @override
  String? get lessonDesc;
  @override
  String? get lessonCoverUrl;
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
