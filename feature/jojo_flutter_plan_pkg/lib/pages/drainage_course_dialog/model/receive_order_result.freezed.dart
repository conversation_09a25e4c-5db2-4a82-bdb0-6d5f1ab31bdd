// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'receive_order_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ReceiveOrderResult _$ReceiveOrderResultFromJson(Map<String, dynamic> json) {
  return _ReceiveOrderResult.fromJson(json);
}

/// @nodoc
mixin _$ReceiveOrderResult {
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get orderId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReceiveOrderResultCopyWith<ReceiveOrderResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceiveOrderResultCopyWith<$Res> {
  factory $ReceiveOrderResultCopyWith(
          ReceiveOrderResult value, $Res Function(ReceiveOrderResult) then) =
      _$ReceiveOrderResultCopyWithImpl<$Res, ReceiveOrderResult>;
  @useResult
  $Res call({String? linkUrl, String? orderId});
}

/// @nodoc
class _$ReceiveOrderResultCopyWithImpl<$Res, $Val extends ReceiveOrderResult>
    implements $ReceiveOrderResultCopyWith<$Res> {
  _$ReceiveOrderResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? linkUrl = freezed,
    Object? orderId = freezed,
  }) {
    return _then(_value.copyWith(
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ReceiveOrderResultCopyWith<$Res>
    implements $ReceiveOrderResultCopyWith<$Res> {
  factory _$$_ReceiveOrderResultCopyWith(_$_ReceiveOrderResult value,
          $Res Function(_$_ReceiveOrderResult) then) =
      __$$_ReceiveOrderResultCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? linkUrl, String? orderId});
}

/// @nodoc
class __$$_ReceiveOrderResultCopyWithImpl<$Res>
    extends _$ReceiveOrderResultCopyWithImpl<$Res, _$_ReceiveOrderResult>
    implements _$$_ReceiveOrderResultCopyWith<$Res> {
  __$$_ReceiveOrderResultCopyWithImpl(
      _$_ReceiveOrderResult _value, $Res Function(_$_ReceiveOrderResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? linkUrl = freezed,
    Object? orderId = freezed,
  }) {
    return _then(_$_ReceiveOrderResult(
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ReceiveOrderResult implements _ReceiveOrderResult {
  const _$_ReceiveOrderResult({this.linkUrl, this.orderId});

  factory _$_ReceiveOrderResult.fromJson(Map<String, dynamic> json) =>
      _$$_ReceiveOrderResultFromJson(json);

  @override
  final String? linkUrl;
  @override
  final String? orderId;

  @override
  String toString() {
    return 'ReceiveOrderResult(linkUrl: $linkUrl, orderId: $orderId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ReceiveOrderResult &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, linkUrl, orderId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ReceiveOrderResultCopyWith<_$_ReceiveOrderResult> get copyWith =>
      __$$_ReceiveOrderResultCopyWithImpl<_$_ReceiveOrderResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ReceiveOrderResultToJson(
      this,
    );
  }
}

abstract class _ReceiveOrderResult implements ReceiveOrderResult {
  const factory _ReceiveOrderResult(
      {final String? linkUrl, final String? orderId}) = _$_ReceiveOrderResult;

  factory _ReceiveOrderResult.fromJson(Map<String, dynamic> json) =
      _$_ReceiveOrderResult.fromJson;

  @override
  String? get linkUrl;
  @override
  String? get orderId;
  @override
  @JsonKey(ignore: true)
  _$$_ReceiveOrderResultCopyWith<_$_ReceiveOrderResult> get copyWith =>
      throw _privateConstructorUsedError;
}
