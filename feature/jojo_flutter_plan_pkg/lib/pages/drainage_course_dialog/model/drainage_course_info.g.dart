// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'drainage_course_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SubjectInfo _$$_SubjectInfoFromJson(Map<String, dynamic> json) =>
    _$_SubjectInfo(
      pictureUrl: json['pictureUrl'] as String?,
      padPictureUrl: json['padPictureUrl'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      titleColor: json['titleColor'] as String?,
      descriptionColor: json['descriptionColor'] as String?,
      btnText: json['btnText'] as String?,
      subDesc: json['subDesc'] as String?,
      type: json['type'] as int?,
      typeDesc: json['typeDesc'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      icon: json['icon'] as String?,
      selectedIcon: json['selectedIcon'] as String?,
      selectedTextColor: json['selectedTextColor'] as String?,
      selectedBgColor: json['selectedBgColor'] as String?,
      libId: json['libId'] as String?,
      gradeInfoList: (json['gradeInfoList'] as List<dynamic>?)
          ?.map((e) => GradeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      missionMaterialType: json['missionMaterialType'] as String?,
      linkUrl: json['linkUrl'] as String?,
      lastSelectedTipType: json['lastSelectedTipType'] as int?,
      lastSelected: json['lastSelected'] as bool?,
    );

Map<String, dynamic> _$$_SubjectInfoToJson(_$_SubjectInfo instance) =>
    <String, dynamic>{
      'pictureUrl': instance.pictureUrl,
      'padPictureUrl': instance.padPictureUrl,
      'backgroundColor': instance.backgroundColor,
      'titleColor': instance.titleColor,
      'descriptionColor': instance.descriptionColor,
      'btnText': instance.btnText,
      'subDesc': instance.subDesc,
      'type': instance.type,
      'typeDesc': instance.typeDesc,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'icon': instance.icon,
      'selectedIcon': instance.selectedIcon,
      'selectedTextColor': instance.selectedTextColor,
      'selectedBgColor': instance.selectedBgColor,
      'libId': instance.libId,
      'gradeInfoList': instance.gradeInfoList,
      'missionMaterialType': instance.missionMaterialType,
      'linkUrl': instance.linkUrl,
      'lastSelectedTipType': instance.lastSelectedTipType,
      'lastSelected': instance.lastSelected,
    };

_$_GradeInfo _$$_GradeInfoFromJson(Map<String, dynamic> json) => _$_GradeInfo(
      lastSelect: json['lastSelect'] as bool?,
      gradeKey: json['gradeKey'] as int?,
      gradeTitle: json['gradeTitle'] as String?,
      gradeDesc: json['gradeDesc'] as String?,
      tip: json['tip'] as String?,
      resourceId: json['resourceId'] as String?,
      courseCardDataList: (json['courseCardDataList'] as List<dynamic>?)
          ?.map((e) => CourseCardData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_GradeInfoToJson(_$_GradeInfo instance) =>
    <String, dynamic>{
      'lastSelect': instance.lastSelect,
      'gradeKey': instance.gradeKey,
      'gradeTitle': instance.gradeTitle,
      'gradeDesc': instance.gradeDesc,
      'tip': instance.tip,
      'resourceId': instance.resourceId,
      'courseCardDataList': instance.courseCardDataList,
    };

_$_CourseCardData _$$_CourseCardDataFromJson(Map<String, dynamic> json) =>
    _$_CourseCardData(
      courseInfoList: (json['courseInfoList'] as List<dynamic>?)
          ?.map((e) => CourseInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseCardDataToJson(_$_CourseCardData instance) =>
    <String, dynamic>{
      'courseInfoList': instance.courseInfoList,
    };

_$_CourseInfo _$$_CourseInfoFromJson(Map<String, dynamic> json) =>
    _$_CourseInfo(
      courseId: json['courseId'] as String?,
      courseTag: json['courseTag'] as String?,
      courseTagKey: json['courseTagKey'] as int?,
      gradeKey: json['gradeKey'] as int?,
      gradeTitle: json['gradeTitle'] as String?,
      courseDesc: json['courseDesc'] as String?,
      tip: json['tip'] as String?,
      cornerMarkerText: json['cornerMarkerText'] as String?,
      btnText: json['btnText'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      experienceLink: json['experienceLink'] as String?,
      linkId: json['linkId'] as String?,
      courseStatus: json['courseStatus'] as int?,
      lessonInfoList: (json['lessonInfoList'] as List<dynamic>?)
          ?.map((e) => LessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      trainingCampCourseInfoList:
          (json['trainingCampCourseInfoList'] as List<dynamic>?)
              ?.map((e) => CourseInfo.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_CourseInfoToJson(_$_CourseInfo instance) =>
    <String, dynamic>{
      'courseId': instance.courseId,
      'courseTag': instance.courseTag,
      'courseTagKey': instance.courseTagKey,
      'gradeKey': instance.gradeKey,
      'gradeTitle': instance.gradeTitle,
      'courseDesc': instance.courseDesc,
      'tip': instance.tip,
      'cornerMarkerText': instance.cornerMarkerText,
      'btnText': instance.btnText,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'experienceLink': instance.experienceLink,
      'linkId': instance.linkId,
      'courseStatus': instance.courseStatus,
      'lessonInfoList': instance.lessonInfoList,
      'trainingCampCourseInfoList': instance.trainingCampCourseInfoList,
    };

_$_LessonInfo _$$_LessonInfoFromJson(Map<String, dynamic> json) =>
    _$_LessonInfo(
      lessonId: json['lessonId'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonDesc: json['lessonDesc'] as String?,
      lessonCoverUrl: json['lessonCoverUrl'] as String?,
    );

Map<String, dynamic> _$$_LessonInfoToJson(_$_LessonInfo instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'lessonName': instance.lessonName,
      'lessonDesc': instance.lessonDesc,
      'lessonCoverUrl': instance.lessonCoverUrl,
    };
