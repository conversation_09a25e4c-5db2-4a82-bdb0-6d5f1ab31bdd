
import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';

class CoursePromoteLessonFinishProgressWidget extends StatefulWidget {

  final PromoteLessonFinishModel? data;
  final double parentWidth;

  const CoursePromoteLessonFinishProgressWidget({super.key, this.data, required this.parentWidth});

  @override
  State<StatefulWidget> createState() {
    return CoursePromoteLessonFinishProgressWidgetState();
  }
}

class CoursePromoteLessonFinishProgressWidgetState extends State<CoursePromoteLessonFinishProgressWidget> with TickerProviderStateMixin{

  late AnimationController _animationController;
  late Animation<double> _shrinkAnimation;
  StreamSubscription? _promoteFinishAniEventBus;
  StreamSubscription? _dialogShowEventBus;
  bool _animationPlaying = false;
  bool _isAllFinish = false;
  bool _isVisible = false;
  int _currentFinishCnt = 0;
  int _totalCnt = 0;
  double _preFinishWidth = 0.0;  // 用于处理多阶段进度条增长的显示
  PromoteLessonFinishModel? _prePromoteFinishModel;  // 用来处理完课活动节点数>4时的动画问题

  //监听促完课动画事件
  void _listenPromoteFinishAnimation() {
    _promoteFinishAniEventBus = jojoEventBus.on<EventBusPromoteFinishAnimationDialogData>().listen((event) async {
      if (!mounted) return;
      if (event.animationType == AnimationType.progress) {
        _animationPlaying = true;
        _changeProgress();
      }
      if (event.animationType == AnimationType.none) {
        _animationPlaying = false;
        setState(() {});
      }
      if (event.animationType == AnimationType.nodeMadelGet) {
        setState(() {
          _prePromoteFinishModel = widget.data;
          _preFinishWidth = _getMultistageFinishWidth(_prePromoteFinishModel);
        });
      }
    });
  }

  void _listenCompleteUpdate() {
    //监听促完课弹窗显示事件（如果没收到通知，则倒计时结束会判断是否应该显示弹窗）
    _dialogShowEventBus = jojoEventBus.on<EventBusPromoteDialogShowData>().listen((event) async {
      if (!mounted) return;
      if (event.dialogType == AnimationDialogType.updateCompleteCountNoAnimation) {
        _currentFinishCnt = event.courseCompleteCount;
        _animationPlaying = false;
        _changeProgress();
      }
    });
  }

  void _changeProgress() {
    int completeLessonCount = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
    double currentProgress = _currentFinishCnt * 1.0/ _totalCnt;
    double targetProgress = completeLessonCount * 1.0/ _totalCnt;

    // 多阶段的进度值需要根据完成情况特殊计算（处理节点遮挡进度条的问题）
    bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
    if (isMultistage) {
      List<LessonInProgressGiftModel> giftList = widget.data?.lessonInProgress?.giftList ?? [];
      double finishWidth = 1.0;
      LessonInProgressGiftModel? currentGiftModel = widget.data?.lessonInProgress?.currentGiftModel;
      if (giftList.length > 4 && currentGiftModel != null && currentGiftModel.index == completeLessonCount) {
        finishWidth = _getMultistageFinishSpecialWidth(_prePromoteFinishModel);
      } else {
        finishWidth = _getMultistageFinishWidth(widget.data);
      }
      currentProgress = _preFinishWidth / finishWidth;
      targetProgress = 1.0;
      _preFinishWidth = finishWidth;
    }

    _animationController.reset();
    _shrinkAnimation = Tween<double>(begin: currentProgress, end: targetProgress).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );
    _animationController.forward();
  }

  /// 初始化动画
  void _initAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed && _isVisible) {
        _animationPlaying = false;
        _animationController.reset();
        // 更新当前完课数据
        _currentFinishCnt = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
        _isAllFinish = false;
        if ((widget.data?.lessonInProgress?.lessonCount ?? 0) > 0) {
          _isAllFinish = widget.data?.lessonInProgress?.lessonCount == widget.data?.lessonInProgress?.completeLessonCount;
        }
        setState(() {});
      }
    });
  }

  @override
  void initState() {
    _totalCnt = max(1, widget.data?.lessonInProgress?.lessonCount ?? 1);
    _currentFinishCnt = widget.data?.lessonInProgress?.preCompleteLessonCount ?? 0;
    _isAllFinish = _currentFinishCnt == _totalCnt;
    _prePromoteFinishModel = widget.data;
    _preFinishWidth = _getMultistageFinishWidth(_prePromoteFinishModel);
    _initAnimation();
    _listenPromoteFinishAnimation();
    _listenCompleteUpdate();
    super.initState();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _promoteFinishAniEventBus?.cancel();
    _promoteFinishAniEventBus = null;
    _dialogShowEventBus?.cancel();
    _dialogShowEventBus = null;
    super.dispose();
  }

  Widget _buildProgressWidget() {
    final endColor = HexColor(widget.data?.lessonInProgress?.progressEndColor ?? "#4AF3F0");
    int completeLessonCount = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
    PromoteLessonFinishSpineResourceInfo? spineResourceInfo = widget.data?.lessonInProgress?.spineResourceInfo;
    double currentProgress = _currentFinishCnt * 1.0 / _totalCnt;
    if (spineResourceInfo != null) {
      if (spineResourceInfo.getDownStatus() == false) {
        _currentFinishCnt = completeLessonCount;
      }
    } else {
      _currentFinishCnt = completeLessonCount;
    }
    if (_currentFinishCnt > _totalCnt) {
      _currentFinishCnt = _totalCnt;
    }
    double finishWidth = widget.parentWidth;
    bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
    if (isMultistage) {
      currentProgress = 1;  // 对于多阶段的活动，无动画时这里已经计算出了具体的宽度，则并不需要再X进度值
      finishWidth = _preFinishWidth;
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: finishWidth * (_animationPlaying ? _shrinkAnimation.value : currentProgress),
          height: 16.rdp,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [endColor, endColor],
            ),
            borderRadius: _isAllFinish ? BorderRadius.all(Radius.circular(10.rdp)) : BorderRadius.only(topLeft: Radius.circular(10.rdp), bottomLeft: Radius.circular(10.rdp)),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key("${widget.data?.subjectType}_${widget.data?.lessonInfo?.classId}_${widget.data?.lessonInfo?.courseId}_top_progress"),
      onVisibilityChanged: (VisibilityInfo info) {
        _isVisible = info.visibleFraction == 1;
      },
      child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            // 获取父视图的宽度
            double parentWidth = constraints.maxWidth;
            return Container(
              height: 20.rdp,
              width: parentWidth,
              padding: EdgeInsets.all(ConfigSize.progressBorderWidth),
              decoration : BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(10.rdp))
              ),
              child: Stack(
                  children: [
                    _buildProgressWidget()
                  ]
              ),
            );
          },
      ),
    );
  }

  double _getMultistageFinishSpecialWidth(PromoteLessonFinishModel? dataModel) {
    double finishWidth = widget.parentWidth;
    int currentFinishCnt = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
    double currentProgress = currentFinishCnt * 1.0 / _totalCnt;
    if (currentProgress == 1) return finishWidth;
    LessonInProgressGiftModel? nextGiftModel = dataModel?.lessonInProgress?.nextGiftModel;
    double targetNodeWidth = finishWidth + 8.rdp - ConfigSize.nodeItemWidth;
    if (nextGiftModel != null) {
      targetNodeWidth = finishWidth - 12.rdp - ConfigSize.nodeItemWidth;
    }
    return targetNodeWidth;
  }

  // 获取多阶段奖励的最终进度条的宽度
  double _getMultistageFinishWidth(PromoteLessonFinishModel? dataModel) {
    int currentFinishCnt = dataModel?.lessonInProgress?.completeLessonCount ?? 0;
    double currentProgress = currentFinishCnt * 1.0 / _totalCnt;
    bool hasCompleteButNotAllFinish = currentFinishCnt != _totalCnt && currentFinishCnt != 0;
    double nextNodeProgress = dataModel?.lessonInProgress?.progress ?? 0;
    double finishNodeProgress = dataModel?.lessonInProgress?.finishNodeProgress ?? 0;
    double targetNodeProgress = dataModel?.lessonInProgress?.targetNodeProgress ?? 0;
    double finishWidth = widget.parentWidth;
    final currentGiftModel = dataModel?.lessonInProgress?.currentGiftModel;
    final preFinishGiftModel = dataModel?.lessonInProgress?.preFinishGiftModel;
    final nextGiftModel = dataModel?.lessonInProgress?.nextGiftModel;
    // 已全部完成直接进度条拉满
    if (currentFinishCnt == _totalCnt) {
      return finishWidth;
    }
    if (dataModel?.lessonInProgress?.isMultistage == true) {
      List<LessonInProgressGiftModel> giftList = dataModel?.lessonInProgress?.giftList ?? [];
      if (giftList.length > 4) {
        return _getMultistageFinishWidthInSpecial(finishWidth, nextNodeProgress, preFinishGiftModel, nextGiftModel);
      } else {
        return _getMultistageFinishWidthInNormal(
            finishWidth,
            currentProgress,
            nextNodeProgress,
            finishNodeProgress,
            targetNodeProgress,
            hasCompleteButNotAllFinish,
            currentGiftModel);
      }
    }
    return currentProgress * widget.parentWidth;
  }

  // 阶段数小于等于4时的进度条逻辑处理
  double _getMultistageFinishWidthInNormal(double finishWidth, double currentProgress, double nextNodeProgress, double finishNodeProgress, double targetNodeProgress, bool hasCompleteButNotAllFinish, LessonInProgressGiftModel? currentGiftModel) {
    // 修复节点覆盖进度条产生歧义的问题
    if (currentGiftModel == null && hasCompleteButNotAllFinish && nextNodeProgress > 0) {
      double targetNodeWidth = targetNodeProgress * finishWidth;
      double finishNodeWidth = finishNodeProgress * finishWidth;
      double nodeWidth = ConfigSize.nodeItemWidth;
      if (targetNodeProgress == 1) {
        targetNodeWidth = finishWidth;
      }
      if (finishNodeWidth > 0) {
        if (targetNodeWidth == finishWidth) {
          finishWidth = finishNodeWidth + nodeWidth/2.0 + (targetNodeWidth + 8.rdp - nodeWidth - finishNodeWidth - nodeWidth/2.0) * nextNodeProgress;
        } else {
          finishWidth = finishNodeWidth + nodeWidth/2.0 + (targetNodeWidth - finishNodeWidth - nodeWidth) * nextNodeProgress;
        }
        finishWidth -= ConfigSize.progressBorderWidth;
      } else {
        finishWidth = (targetNodeWidth - nodeWidth/2.0) * nextNodeProgress;
      }
    } else {
      finishWidth = currentProgress * widget.parentWidth;
    }
    return finishWidth;
  }

  // 阶段数大于4时的进度条逻辑处理
  double _getMultistageFinishWidthInSpecial(double finishWidth, double nextNodeProgress, LessonInProgressGiftModel? preFinishGiftModel, LessonInProgressGiftModel? nextGiftModel) {
    double targetNodeWidth = finishWidth + 8.rdp - ConfigSize.nodeItemWidth;
    double finishNodeWidth = 0.0;
    if (preFinishGiftModel != null) {
      // 存在已完成的节点，进度条需要从已完成的节点图标右侧开始计算，反之则从0开始计算
      finishNodeWidth = 20.rdp + ConfigSize.nodeItemWidth - ConfigSize.progressBorderWidth;
    }
    if (nextGiftModel != null) {
      targetNodeWidth = finishWidth - 12.rdp - ConfigSize.nodeItemWidth;
    }
    if (nextNodeProgress <= 0) {
      finishWidth = finishNodeWidth - ConfigSize.nodeItemWidth/2.0;
    } else {
      finishWidth = finishNodeWidth + (targetNodeWidth - finishNodeWidth) * nextNodeProgress;
    }
    return finishWidth;
  }
}