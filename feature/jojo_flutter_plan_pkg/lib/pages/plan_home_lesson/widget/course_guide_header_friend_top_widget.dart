import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_utils.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:lottie/lottie.dart';

import '../../../static/lottie.dart';
import '../model/card_course_theme_info.dart';
import '../utils/course_helper.dart';

class CourseGuideHeaderFriendTopWidget extends StatefulWidget {
  final CourseHeader? courseGuide;

  const CourseGuideHeaderFriendTopWidget(
      {super.key, required this.courseGuide});

  @override
  State<StatefulWidget> createState() {
    return CourseGuideHeaderFriendTopWidgetState();
  }
}

class CourseGuideHeaderFriendTopWidgetState
    extends State<CourseGuideHeaderFriendTopWidget>
    with SingleTickerProviderStateMixin {
  String? _expireTime; //过期时间
  Timer? _timer;
  bool _isExpire = false;
  AnimationController? _lottieController;

  @override
  void initState() {
    super.initState();
    _lottieController = AnimationController(vsync: this);
    _isExpire = _checkExpire();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _lottieController?.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          safeAction(() {
            setState(() {
              context.read<PlanHomeLessonCtrl>().isFriendPlayScanAnimation = true;
            });
          });
        }
      });
      safeAction(() {
        setState(() {
          _expireTime = _formatExpireTime();
        });
      });
      _checkOpenTimer();
    });
  }

  @override
  void didUpdateWidget(covariant CourseGuideHeaderFriendTopWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _isExpire = _checkExpire();
    _expireTime = _formatExpireTime();
    _checkOpenTimer();
  }

  bool _checkExpire() {
    return (widget.courseGuide?.friendCardInfo?.validPeriod ?? 0) <
        DateTime.now().millisecondsSinceEpoch;
  }

  String _formatExpireTime() {
    return CourseUtils.formatRemainingTime(
        (widget.courseGuide?.friendCardInfo?.validPeriod ??
                DateTime.now().millisecondsSinceEpoch) -
            DateTime.now().millisecondsSinceEpoch,
        context);
  }

  _checkOpenTimer() {
    bool isAfter24Hour = CourseUtils.isTwentyFourHoursLater(
        widget.courseGuide?.friendCardInfo?.validPeriod ??
            DateTime.now().millisecondsSinceEpoch);
    if (!isAfter24Hour && !_isExpire) {
      //开启定时器，倒计时分钟
      _setupLooperTimer(CourseUtils.getTimeDifferenceInMinutes(
          widget.courseGuide?.friendCardInfo?.validPeriod ??
              DateTime.now().millisecondsSinceEpoch));
    }
  }

  _setupLooperTimer(int seconds) {
    var newSeconds = seconds;
    if (_timer != null) {
      _timer?.cancel();
      _timer = null;
    }
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      newSeconds -= 1;
      try {
        if (_timer == null) {
          return;
        }
        if (newSeconds == 0 && !_isExpire) {
          //1秒的误差，服务期可能还未结束
          context
              .read<PlanHomeLessonCtrl>()
              .onRefresh(context, widget.courseGuide?.classKey);
          timer.cancel();
          _timer = null;
          _isExpire = true;
        }
        safeAction(() {
          setState(() {
            _expireTime =
                CourseUtils.formatRemainingTime(newSeconds * 1000, context);
          });
        });
      } catch (e) {
        timer.cancel();
      }
    });
  }

  Widget _buildFriendWidget(PlanHomeLessonCtrl ctrl) {
    return Expanded(
      child: Row(
        children: [
          ImageNetworkCached(
            imageUrl: widget.courseGuide?.friendCardInfo?.icon ?? "",
            fit: BoxFit.cover,
            width: 48.rdp,
            height: 48.rdp,
          ),
          SizedBox(width: 8.rdp), // 图标与文案之间的间距
          Expanded(
            child: Text(
              widget.courseGuide?.friendCardInfo?.title ?? '',
              maxLines: 1,
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight:
                      RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600),
              overflow: TextOverflow.ellipsis, // 超出部分显示省略号
            ),
          ),
          Text(
            _expireTime ?? "",
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          Image.asset(
            color: Colors.white,
            AssetsImg.PLAN_IMAGE_PROMOTE_TOP_CARD_ARROW,
            width: 14.rdp,
            height: 14.rdp,
            fit: BoxFit.cover,
            package: RunEnv.package,
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
    return VisibilityObserve(
      fraction: 1.0,
      onShow: () async {
        if (!mounted) {
          return;
        }
        CourseHelper.sensorLessonTrackView(
            widget.courseGuide, '课前准备_好友卡', false);
      },
      onClick: () {
        // 点击整个区域进行跳转
        CourseHelper.sensorLessonTrackClick(
            widget.courseGuide, '课前准备_好友卡', false, false);
        var jumpRouter = widget.courseGuide?.friendCardInfo?.jumpRoute;
        if (jumpRouter != null && jumpRouter.isNotNullOrEmpty()) {
          RunEnv.jumpLink(jumpRouter);
        } else {
          l.e("好友卡活动", "好友卡跳转路由为空");
        }
      },
      child: ClipRect(
          child: Container(
              margin: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
              decoration: BoxDecoration(
                  color: ctrl.getColor(context, 4),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24.rdp),
                      topRight: Radius.circular(24.rdp))),
              height: 66.rdp,
              child: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 14.rdp, right: 14.rdp),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      // 左右对齐
                      children: [
                        _buildFriendWidget(ctrl),
                      ],
                    ),
                  ),
                  if (!ctrl.isFriendPlayScanAnimation)
                    IgnorePointer(
                      ignoring: true,
                      child: SizedBox(
                          width: 335,
                          height: 66,
                          child: Lottie.asset(AssetsLottie.COURSE_FRIEND_SCAN,
                              repeat: false,
                              package: RunEnv.package, onLoaded: (composition) {
                            _lottieController
                              ?..duration = composition.duration
                              ..forward();
                          })),
                    )
                ],
              ))),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _isExpire = false;
    _lottieController?.dispose();
    _lottieController = null;
    super.dispose();
  }
}
