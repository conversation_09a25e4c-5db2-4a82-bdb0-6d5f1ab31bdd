import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/customization/landscape_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/spine_download_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/widget/course_top_promote_lesson_finish_progress.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/widget/course_top_promote_lesson_finish_title.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_bubble.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_madel_get.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class CoursePromoteLessonFinishTopWidget extends StatefulWidget {

  final PromoteLessonFinishModel? data;

  const CoursePromoteLessonFinishTopWidget({super.key, required this.data});

  static double getHeight(PromoteLessonFinishModel? data) {
    return data?.lessonInProgress?.spineResourceInfo == null ? 0 : ConfigSize.cardHeight + 10.rdp;
  }

  @override
  State<StatefulWidget> createState() {
    return CoursePromoteLessonFinishTopWidgetState();
  }
}

class CoursePromoteLessonFinishTopWidgetState extends State<CoursePromoteLessonFinishTopWidget> with TickerProviderStateMixin{

  StreamSubscription? _promoteFinishAniEventBus;
  StreamSubscription? _dialogShowEventBus;
  StreamSubscription? _lifeCycleEventBus;
  StreamSubscription? _playerStateSubscription;
  final _gotSpineController = JoJoSpineAnimationController();
  late final AnimationController _animationController;
  late final Animation<double> _shrinkAnimation;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isInitialized = false;
  bool _isVisible = false;
  bool _audioPlayEnd = true;
  bool _needStopAnimation = false;
  double _progressItemWidth = 0.0;
  double _nextNodeLeftSpace = 0.0;
  int _currentFinishCnt = 0;
  PlanHomeLessonCtrl? _ctrl;
  PromoteLessonFinishModel? _prePromoteFinishModel;  // 用来处理完课活动节点数>4时的动画问题

  //监听促完课事件
  void _listenPromoteFinishAnimation() {
    //监听首页列表生命周期事件
    _lifeCycleEventBus = jojoEventBus.on<EventBusPromoteFinishLifecycleData>().listen((event) async {
      if (!mounted) return;
      if (event.lifecycleType == LifecycleType.resumed) {
        // 从本地标记中获取是否需要定位到要学习的课程
        String key = "activity_task_study_button_click";
        NativeValue? mp = await jojoNativeBridge.operationNativeValueGet(key: key).then((value) => value.data);
        String value = mp?.value ?? '';
        if (value == "1") {
          jojoNativeBridge.operationNativeValueSet(key: key, value: "0");
          // 直接定位，不需要播放语音
          _ctrl?.searchFirstUnFinishLessonAndPosition(widget.data?.lessonInProgress?.isMultistage ?? false);
        }
      }
      try {
        if (event.lifecycleType == LifecycleType.paused && _audioPlayer != null) {
          // 暂停语音
          _audioPlayer.pause();
          _audioPlayEnd = true;
        }
      } catch (e) {
        l.e("促完课活动", "进行中的顶部卡片生命周期事件监听异常");
      }
    });

    //监听促完课动画事件
    _promoteFinishAniEventBus = jojoEventBus.on<EventBusPromoteFinishAnimationDialogData>().listen((event) async {
      if (!mounted) return;
      bool isAllFinish = false;
      if ((widget.data?.lessonInProgress?.lessonCount ?? 0) > 0) {
        isAllFinish = widget.data?.lessonInProgress?.lessonCount == widget.data?.lessonInProgress?.completeLessonCount;
      }
      if (event.animationType == AnimationType.progress) {
        _animationController.reset();
        _animationController.forward();
        setState(() {
          _currentFinishCnt = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
        });
        if (!isAllFinish && _isInitialized) {
          _playAnimation();
        }
      }
    });

    //监听促完课弹窗显示事件（如果没收到通知，则倒计时结束会判断是否应该显示弹窗）
    _dialogShowEventBus = jojoEventBus.on<EventBusPromoteDialogShowData>().listen((event) async {
      if (event.dialogType == AnimationDialogType.request && _isVisible) {
        try {
          // 根据数据判断是否应该显示弹窗（有可能当前显示的卡片并不是引起动画执行弹窗的数据来源）
          PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
          jojoEventBus.fire(EventBusPromoteDialogShowData(AnimationDialogType.hasActivityCard, ctrl.promoteFinishCardTopSpace, _progressItemWidth, _nextNodeLeftSpace, 0));
        } catch (e) {
          l.e("促完课活动", "进行中的顶部卡片无法得到控制器");
        }
      }
      if (event.dialogType == AnimationDialogType.updateCompleteCountNoAnimation) {
        setState(() {
          _currentFinishCnt = event.courseCompleteCount;
        });
      }
    });

    //监听语音播放状态
    _playerStateSubscription = _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      if (!mounted) return;
      if (state == PlayerState.completed) {
        _audioPlayEnd = true;
      } 
    });
  }

  // 浏览埋点
  void _dealWithViewBuried() {
    if (widget.data?.activityId != null) {
      int activityId = widget.data?.activityId ?? 0;
      int status = widget.data?.status ?? 0;
      int medalId = widget.data?.lessonInProgress?.medalId ?? 0;
      JoJoPromoteFinishBuriedUtils.ActivityCardScreen(activityId, status, widget.data?.lessonInfo);
      var gitStatus = medalId !=0 && widget.data?.classKey != null ? PromoteLessonMedalGitStatus.GIT : PromoteLessonMedalGitStatus.NOT_GIT;
      bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
      int? taskType;
      if (isMultistage) {
        taskType = widget.data?.lessonInProgress?.allFinishGiftModel?.type;
      }
      JoJoPromoteFinishBuriedUtils.ActivityCardMedalScreen(gitStatus, activityId, status, widget.data?.lessonInfo, taskType);
      if (widget.data?.lessonInProgress?.preFinishGiftModel != null) {
        // 已完成节点 曝光
        JoJoPromoteFinishBuriedUtils.ActivityFinishNodeScreen(activityId, status, widget.data?.lessonInfo, taskType);
      }
    }
  }

  // 点击埋点
  void _dealWithAppClickBuried() {
    if (widget.data?.activityId != null) {
      int activityId = widget.data?.activityId ?? 0;
      int status = widget.data?.status ?? 0;
      JoJoPromoteFinishBuriedUtils.appClickActivityCard(activityId, status, widget.data?.lessonInfo);
    }
  }

  /// 初始化动画
  void _initAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _shrinkAnimation = Tween<double>(begin: 1, end: 1.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );

    _animationController.addStatusListener((status) async {
      if (status == AnimationStatus.completed && _isVisible) {
        bool isAllFinish = widget.data?.lessonInProgress?.lessonCount == widget.data?.lessonInProgress?.completeLessonCount;
        if (!isAllFinish && widget.data?.lessonInProgress?.currentGiftModel != null) {
          // 如果节点数目超过4,则这里需要更新达成节点的图标状态
          List<LessonInProgressGiftModel> giftList = _prePromoteFinishModel?.lessonInProgress?.giftList ?? [];
          if (giftList.length > 4 && _prePromoteFinishModel?.lessonInProgress?.nextGiftModel != null) {
            setState(() {
              _prePromoteFinishModel?.lessonInProgress?.nextGiftModel?.isGet = true;
              _currentFinishCnt = widget.data?.lessonInProgress?.completeLessonCount ?? 0;
            });
          }
        }
        await _animationController.reverse();
        if (isAllFinish) {
          jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.badgeShowInCenter));
        } else {
          if (widget.data?.lessonInProgress?.currentGiftModel != null) {
            // 达成奖励，弹出奖励获得弹窗
            jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.nodeMadelGet));
          } else if (widget.data?.lessonInProgress?.nextGiftModel != null) {
            // 未达成奖励，显示奖励气泡以及语音(需要把进度条的宽度传递过去)
            jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.madelPreview));
          } else {
            // 无后续动画时，直接关闭弹窗
            jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.none));
          }
        }
        setState(() {
          _prePromoteFinishModel = widget.data;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    try {
      _ctrl = context.read<PlanHomeLessonCtrl>();
      _listenPromoteFinishAnimation();
      _initAnimation();
      _dealWithViewBuried();
      _currentFinishCnt = widget.data?.lessonInProgress?.preCompleteLessonCount ?? 0;
      _prePromoteFinishModel = widget.data;
    } catch (e) {
      l.e("促完课活动", "进行中的顶部卡片init异常");
    }
  }

  @override
  void dispose() {
    _promoteFinishAniEventBus?.cancel();
    _promoteFinishAniEventBus = null;
    _dialogShowEventBus?.cancel();
    _dialogShowEventBus = null;
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _playerStateSubscription?.cancel();
    _playerStateSubscription = null;
    _animationController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _playAnimation() {
    String animationName = SpineAnimationConstants.badgeInfoAnimationNormal;
    if (_gotSpineController.spineController?.skeletonData.findAnimation(animationName) == null) {
      l.i("促完课活动", "奖章动效资源中缺少normal动画");
    }
    _gotSpineController.playAnimation(JoJoSpineAnimation(
        animaitonName: animationName,
        trackIndex: 0,
        loop: false,
        delay: 0, listener: _spineAnimationEvent));
  }

  Widget _buildAnimationWidget(String? atlasFilePath, String? skelFilePath, String? animationName) {
    return JoJoSpineAnimationWidget(
      atlasFilePath ?? "",
      skelFilePath ?? "",
      LoadMode.file,
      _gotSpineController,
      useRootBoneAlign: true,
      fit: BoxFit.none,
      onInitialized: (controller) {
        if (mounted) {
          controller.skeleton
        ..setScaleX(70.rdp/500)
        ..setScaleY(70.rdp/500);
        _isInitialized = true;
          _playAnimation();
          _needStopAnimation = true;
        }
      },
    );
  }

  /// 动效动画完成回调
  _spineAnimationEvent(AnimationEventType type) {
    if(AnimationEventType.start == type && _needStopAnimation) {
      _gotSpineController.pauseAnimation();
      _needStopAnimation = false;
    }
  }

  Widget _buildSpineWidget() {
    PromoteLessonFinishSpineResourceInfo? spineResourceInfo = widget.data?.lessonInProgress?.spineResourceInfo;
    if (spineResourceInfo == null) return Container();
    if (spineResourceInfo.targetResource.isDowned == false) return Container();
    String? atlasFilePath = spineResourceInfo.targetResource.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = spineResourceInfo.targetResource.getFilePath(SpineResourceConstants.SPINE_SKL);
    if (atlasFilePath.isEmpty || skelFilePath.isEmpty) return Container();

    Key spineKey = ValueKey('$atlasFilePath-$skelFilePath');
    return Positioned(
      top: 8.rdp,
      right: ConfigSize.contentPadding,
      child: GestureDetector(
        onTap: () {
          // 未开课不处理
          if (widget.data?.status == PromoteLessonFinishStatus.IN_PROGRESS) {
            int medalId = widget.data?.lessonInProgress?.medalId ?? 0;
            int activityId = widget.data?.activityId ?? 0;
            int status = widget.data?.status ?? 0;
            bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
            bool isAllFinish = widget.data?.lessonInProgress?.lessonCount == widget.data?.lessonInProgress?.completeLessonCount;
            if (isAllFinish) {
              // 全都完成了，可以弹出最终奖励弹窗
              _showTargetGiftPage(widget.data?.lessonInProgress?.allFinishGiftModel);
            } else {
              _playAudioAndPosition();
            }
            int? taskType;
            if (isMultistage) {
              taskType = widget.data?.lessonInProgress?.allFinishGiftModel?.type;
            }
            int git = medalId != 0 && widget.data?.classKey != null ? PromoteLessonMedalGitStatus.GIT : PromoteLessonMedalGitStatus.NOT_GIT;
            JoJoPromoteFinishBuriedUtils.appClickActivityCardMedal(git, activityId, status, widget.data?.lessonInfo, taskType);
          } else {
            jumpToDetail();
          }
        },
        child: SizedBox(
          key: spineKey,
      width: ConfigSize.madelSpineItemWidth,
      height: ConfigSize.madelSpineItemHeight,
      child: _buildAnimationWidget(atlasFilePath, skelFilePath, SpineAnimationConstants.badgeInfoAnimationNormal),
    ),
      ));
  }

  // 播放语音并定位
  void _playAudioAndPosition() {
    String audioUrl = widget.data?.lessonInProgress?.audioUrl ?? "";
    if (audioUrl.isNotEmpty && _audioPlayEnd) {
      // 提示用户，还需要继续学习
      _audioPlayer.play(UrlSource(audioUrl));
      _audioPlayEnd = false;
    }
    _ctrl?.searchFirstUnFinishLessonAndPosition(widget.data?.lessonInProgress?.isMultistage ?? false);
  }

  List<Widget> _buildChildList() {
    List<Widget> list = [];
    list.add(
        Positioned(
        top: 6.rdp,
        child: SizedBox(
            height: ConfigSize.progressItemHeight,
            width: _progressItemWidth,
            child: CoursePromoteLessonFinishProgressWidget(data: widget.data,parentWidth: _progressItemWidth,)
        )
    ));

    if (_ctrl?.needUpdateActivityData == true) {
      _prePromoteFinishModel = widget.data;
      _ctrl?.needUpdateActivityData = false;
    }

    List<LessonInProgressGiftModel> giftList = _prePromoteFinishModel?.lessonInProgress?.giftList ?? [];
    if (giftList.length > 4) {
      if (_prePromoteFinishModel?.lessonInProgress?.preFinishGiftModel != null) {
        // 已完成节点
        list.add(Positioned(
            left: 20.rdp,
            child: _buildMultiStageContent(_progressItemWidth, _prePromoteFinishModel!.lessonInProgress!.preFinishGiftModel!, null)
        ));
      }
      LessonInProgressGiftModel lastGiftModel = giftList.last;
      if (_prePromoteFinishModel?.lessonInProgress?.nextGiftModel != null) {
        // 最终节点
        list.add(Positioned(
            left: _progressItemWidth - 4.rdp - ConfigSize.nodeItemWidth,
            child: _buildMultiStageContent(_progressItemWidth, lastGiftModel, null)
        ));
        // 下一个目标节点
        _nextNodeLeftSpace = _progressItemWidth - 12.rdp - ConfigSize.nodeItemWidth/2.0 - ConfigSize.madelPreViewBubbleWidth/2.0;
        list.add(Positioned(
            left: _progressItemWidth - 12.rdp - ConfigSize.nodeItemWidth,
            child: _buildMultiStageContent(_progressItemWidth, _prePromoteFinishModel!.lessonInProgress!.nextGiftModel!, _nextNodeLeftSpace)
        ));
      } else {
        // 处理最终节点
        list.add(Positioned(
            left: _progressItemWidth + 8.rdp - ConfigSize.nodeItemWidth,
            child: _buildMultiStageContent(_progressItemWidth, lastGiftModel, null)
        ));
      }
    } else {
      for (var element in giftList) {
        if (element != giftList.last) {
          list.add(
              Positioned(
                  left: _progressItemWidth * element.index * 1.0 / element.lessonCount - ConfigSize.nodeItemWidth/2.0,
                  child: _buildMultiStageContent(_progressItemWidth, element, null)
              ));
        } else {
          list.add(Positioned(
              left: _progressItemWidth + 8.rdp - ConfigSize.nodeItemWidth,
              child: _buildMultiStageContent(_progressItemWidth, element, null)
          ));
        }
      }
    }
    return list;
  }

  void _showTargetGiftPage(LessonInProgressGiftModel? item) {
    jojoNativeBridge.showHomePageTabs(show: false);
    SmartDialog.show(
        clickMaskDismiss: true,
        useAnimation: false,
        onDismiss: () {
          jojoNativeBridge.showHomePageTabs(show: true);
        },
        alignment: Alignment.center,
        builder: (_) => CoursePromoteFinishAnimationDialogMadelGetWidget(
            model: widget.data,
            giftModel: item,
            isFromClick: false,
            subjectColor: _ctrl?.getMainColor(),
            isAllLessonFirstGet: false,
            disMissCallBack: () {
              SmartDialog.dismiss();
            }));
  }

  Widget _buildNodeStatusIcon(bool isFinish, LessonInProgressGiftModel item) {
    if (isFinish) {
      return Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned(
                top: (ConfigSize.nodeItemHeight - ConfigSize.nodeItemFinishBgHeight)/2.0,
                left: (ConfigSize.nodeItemWidth - ConfigSize.nodeItemFinishBgWidth)/2.0,
                child: Container(
                  width: ConfigSize.nodeItemFinishBgWidth,
                  height: ConfigSize.nodeItemFinishBgHeight,
                  decoration: BoxDecoration(
                      color: HexColor(widget.data?.lessonInProgress?.progressEndColor ?? "#f4f4f4"), // 填充颜色,
                      border: Border.all(
                        color: Colors.white, // 边框颜色
                        width: 2.rdp, // 边框宽度
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(ConfigSize.nodeItemFinishBgHeight/2.0))),
                )
            ),
          ],
        ),
      );
    } else {
      return ImageAssetWeb(
        assetName: AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_NODE_ICON,
        fit: BoxFit.cover,
        package: Config.package,
      );
    }
  }

  Widget _buildMultiStageContent(double parentWidth, LessonInProgressGiftModel item, double? leftSpace) {
    bool isFinish = item.isGet && _currentFinishCnt >= item.index;
    return GestureDetector(
      onTap: () {
        if (!mounted) return;
        if (item.spineResourceVo?.isDowned == true) {
          int status = widget.data?.status ?? 0;
          if (isFinish) {
            // 埋点
            int activityId = widget.data?.activityId ?? 0;
            bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
            int? taskType;
            if (isMultistage) {
              taskType = widget.data?.lessonInProgress?.allFinishGiftModel?.type;
            }
            JoJoPromoteFinishBuriedUtils.appClickActivityFinishNode(activityId, status, widget.data?.lessonInfo, taskType);
            // 已完成的节点，点击之后显示奖励结算页
            _showTargetGiftPage(item);
          } else {
            // 如果是预告期，不展示气泡，直接走跳转逻辑
            if (status != PromoteLessonFinishStatus.IN_PROGRESS) {
              jumpToDetail();
            }
            try {
              PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
              // 未完成的节点，点击之后显示预告内容
              List<LessonInProgressGiftModel> giftList = _prePromoteFinishModel?.lessonInProgress?.giftList ?? [];
              bool hasAnimationResource = CoursePromoteFinishAnimationDialogBubbleWidget.hasAnimationResource(item);
              if (item != giftList.last && item.spineResourceVo?.isDowned == true && hasAnimationResource) {
                SmartDialog.show(
                    clickMaskDismiss: false,
                    useAnimation: false,
                    onDismiss: () {},
                    maskColor: Colors.transparent,
                    maskWidget: null,
                    alignment: Alignment.center,
                    builder: (_) => CoursePromoteFinishAnimationDialogBubbleWidget(
                        giftModel: item,
                        isOnClick: true,
                        leftSpace: leftSpace,
                        topSpace: ctrl.promoteFinishCardTopSpace,
                        isLandSpace: LandscapeUtils.isLandscape(),
                        progressWidth: _progressItemWidth,
                        disMissCallBack: () {
                          SmartDialog.dismiss();
                        }
                    ));
              } else if (item == giftList.last) {
                _playAnimation();
              }
            } catch (e) {
              l.i("促完课活动", "阶段奖励未完成节点点击异常");
            }
          }
        }
      },
      child: Container(
        width: ConfigSize.nodeItemWidth,
        height: ConfigSize.nodeItemHeight,
        color: Colors.transparent,
        child: Stack(
          children: [
            _buildNodeStatusIcon(isFinish, item),
            Positioned.fill(
                child: Center(
                  child: Text(
                      "${item.index}",
                      style: TextStyle(
                          fontSize: 16.rdp,
                          color: Colors.white,
                          fontFamily: "MohrRounded_Bold",
                          overflow: TextOverflow.ellipsis,
                          package: 'jojo_flutter_base'
                      )),
                ))
          ],
        ),
      ),
    );
  }

  Widget _buildBottomWidget(PlanHomeLessonCtrl ctrl) {
    int lessonCount = widget.data?.lessonInProgress?.lessonCount ?? 0;
    String title = widget.data?.lessonInProgress?.message ?? "";
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(
          left: ConfigSize.contentPadding, right: ConfigSize.contentPadding, top: 15.rdp, bottom: 10.rdp),
      child: Column(
        children: [
          CoursePromoteLessonFinishTopTitleWidget(
            title: title,finishLessonCount: _currentFinishCnt,allLessonCount: lessonCount,
          ),
          SizedBox(height: 8.rdp,),
          LayoutBuilder(
              builder: (context, constraints) {
                _progressItemWidth = constraints.maxWidth - ConfigSize.madelSpineItemWidth;
                return SizedBox(
                  height: ConfigSize.nodeItemHeight,
                  child: Stack(
                    children: _buildChildList(),
                  ),
                );
              }),
        ],
      ),
    );
  }
  
  void jumpToDetail() {
    // 如果配置了落地页，则跳转到落地页，反之走h5页面
    int advanceRedirectPageId = widget.data?.lessonInProgress?.advanceRedirectPageId ?? 0;
    int activityRedirectPageId = widget.data?.lessonInProgress?.activityRedirectPageId ?? 0;
    bool isNeedJumpToActivityDetailPage = advanceRedirectPageId != 0 || activityRedirectPageId != 0;
    if (isNeedJumpToActivityDetailPage) {
      _dealWithAppClickBuried();
      // 跳转到落地页
      _jumpToActivityDetailPage();
    } else {
      // 点击后跳转到详情页
      if (widget.data?.lessonInProgress?.route == null) {
        l.e("促完课活动", "进行中的顶部卡片跳转失败，route为空");
      } else {
        _dealWithAppClickBuried();
        RunEnv.jumpLink(widget.data?.lessonInProgress?.route ?? "");
      }
    }
  }

  // 跳转到落地页
  void _jumpToActivityDetailPage() {
    // 取消当前的下载内容
    GlobalDownloadManager downloadManager = GlobalDownloadManager();
    downloadManager.cancelAllDownloads();
    int activityId = widget.data?.activityId ?? 0;
    int status = widget.data?.status ?? 0;
    JoJoPromoteFinishBuriedUtils.ActivityCardScreen(activityId, status, widget.data?.lessonInfo);
    bool isMultistage = widget.data?.lessonInProgress?.isMultistage ?? false;
    int? taskType;
    if (isMultistage) {
      taskType = widget.data?.lessonInProgress?.allFinishGiftModel?.type;
    }
    Map<String, dynamic> buriedMap = JoJoPromoteFinishBuriedUtils.getBuriedParams(activityId, status, widget.data?.lessonInfo);
    String business_type = JoJoPromoteFinishBuriedUtils.getBusinessType(taskType);
    Map<String, dynamic> properties = {
      'business_type': business_type,
      ...buriedMap,
    };
    int classId = widget.data?.lessonInfo?.classId ?? 0;
    int courseId = widget.data?.lessonInfo?.courseId ?? 0;
    int advanceRedirectPageId = widget.data?.lessonInProgress?.advanceRedirectPageId ?? 0;
    int activityRedirectPageId = widget.data?.lessonInProgress?.activityRedirectPageId ?? 0;
    int pageId = status == PromoteLessonFinishStatus.IN_PROGRESS? activityRedirectPageId : advanceRedirectPageId;
    RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/activityDetailPage?windowType=window&subjectColor=${Uri.encodeComponent(widget.data?.lessonInfo?.subjectColor ?? "#FF9045")}&activityId=$activityId&classId=$classId&pageId=$pageId&courseId=$courseId&buriedString=${Uri.encodeComponent(jsonEncode(properties))}");
  }
  
  @override
  Widget build(BuildContext context) {
    PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
    if (widget.data?.lessonInProgress == null) return Container();

    return VisibilityDetector(
      key: Key("${widget.data?.subjectType}_${widget.data?.lessonInfo?.classId}_${widget.data?.lessonInfo?.courseId}_top_widget"),
      onVisibilityChanged: (VisibilityInfo info) {
        _isVisible = info.visibleFraction == 1;
      },
      child: GestureDetector(
      onTap: () {
        jumpToDetail();
      },
      child: Container(
        padding: EdgeInsets.zero,
        child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                        scale: _shrinkAnimation.value,
                        child:Container(
      height: ConfigSize.cardHeight,
      margin: EdgeInsets.only(left: ConfigSize.lrSpace, right: ConfigSize.lrSpace),
      decoration : BoxDecoration(
        color: ctrl.getColor(context, 2),
        borderRadius: BorderRadius.all(Radius.circular(ConfigSize.borderRadius))
      ),
      child: Stack(
        children: [
          _buildBottomWidget(ctrl),
          _buildSpineWidget(),
        ],
      ),
    ),
                      );
                },
              ),
      ),
    )
    );
  }
}