import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class CourseGuideHeaderTopWidget extends StatefulWidget {

  final PreLessonPreviewHeaderModel? data;

  const CourseGuideHeaderTopWidget({super.key, required this.data});

  @override
  State<StatefulWidget> createState() {
    return CourseGuideHeaderTopWidgetState();
  }
}

class CourseGuideHeaderTopWidgetState extends State<CourseGuideHeaderTopWidget> with SingleTickerProviderStateMixin{

  @override
  void initState() {
    _dealWithViewBuried();
    super.initState();
  }

  // 浏览埋点
  void _dealWithViewBuried() {
    if (widget.data?.activityId != null) {
      int activityId = widget.data?.activityId ?? 0;
      int status = widget.data?.status ?? 0;
      JoJoPromoteFinishBuriedUtils.ActivityCardScreen(activityId, status, widget.data?.lessonInfo);
    }
  }

  // 浏览埋点
  void _dealWithAppClickBuried() {
    if (widget.data?.activityId != null) {
      int activityId = widget.data?.activityId ?? 0;
      int status = widget.data?.status ?? 0;
      JoJoPromoteFinishBuriedUtils.appClickActivityCard(activityId, status, widget.data?.lessonInfo);
    }
  }

  Widget _buildLeftWidget(PlanHomeLessonCtrl ctrl) {
    return Row(
      children: [
        ImageNetworkCached(
                    imageUrl: widget.data?.icon ?? "",
                          fit: BoxFit.cover,
                          width: 48.rdp,
                          height: 48.rdp,
                        ),
                    SizedBox(width: 8.rdp), // 图标与文案之间的间距
                    // Flexible(child: 
                    
                    Text(
                        widget.data?.title ?? '',
                        style: TextStyle(
                          color: ctrl.getColor(context, 6),
                          fontSize: 14.rdp,
                          fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600
                        ),
                        overflow: TextOverflow.ellipsis, // 超出部分显示省略号
                      )
                      
                      // )
                  ],
                );
  }

  Widget _buildRightWidget(PlanHomeLessonCtrl ctrl) {
    return SizedBox(
      height: 48.rdp,
      child: Row(
        mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          S.of(context).knowMore,
          style: TextStyle(
            fontSize: 14.rdp,
            color: ctrl.getColor(context, 6),
          ),
        ),
              Image.asset(
                  AssetsImg.PLAN_IMAGE_PROMOTE_TOP_CARD_ARROW,
                  color: ctrl.getColor(context, 6),
                  width: 14.rdp,
                  height: 14.rdp,
                  fit: BoxFit.cover,
                  package: RunEnv.package,
                ),
      ],
    ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
    return GestureDetector(
      onTap: () {
        // 点击整个区域进行跳转
        if (widget.data?.route != null) {
          // 点击埋点
          _dealWithAppClickBuried();
          RunEnv.jumpLink(widget.data?.route ?? "");
        } else {
          l.e("促完课活动", "预告卡片调整路由为空");
        }
      },
      child: ClipRect(child: Container(
      margin: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
      decoration : BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter, // 渐变开始方向
          end: Alignment.bottomCenter, // 渐变结束方向
            colors: [ 
              ctrl.getColor(context, 3).withOpacity(0.3),
              ctrl.getColor(context, 3).withOpacity(0.8),
            ],
        ),
        borderRadius: BorderRadius.only(topLeft: Radius.circular(24.rdp), topRight: Radius.circular(24.rdp))
      ),
      height: 66.rdp,
      child: Stack(
        children: [
          Container(
        padding: EdgeInsets.only(left: 14.rdp, right: 14.rdp),
        child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 左右对齐
        children: [
          _buildLeftWidget(ctrl),
          _buildRightWidget(ctrl),
        ],
      ),
      ),
        ],
      ))),
    );
  }
}