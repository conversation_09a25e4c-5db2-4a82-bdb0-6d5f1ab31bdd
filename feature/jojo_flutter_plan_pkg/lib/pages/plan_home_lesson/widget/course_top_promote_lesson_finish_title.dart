import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';

class CoursePromoteLessonFinishTopTitleWidget extends StatefulWidget {

  final String? title;
  final int allLessonCount; // 全部课程数目
  final int finishLessonCount;  // 完课数目

  const CoursePromoteLessonFinishTopTitleWidget({
    super.key,
    required this.title,
    required this.allLessonCount,
    required this.finishLessonCount});

  @override
  State<StatefulWidget> createState() {
    return CoursePromoteLessonFinishTopTitleWidgetState();
  }
}

class CoursePromoteLessonFinishTopTitleWidgetState extends State<CoursePromoteLessonFinishTopTitleWidget> {

  @override
  void initState() {
    super.initState();
  }

  Widget _buildContent(PlanHomeLessonCtrl ctrl) {
    return Container(
      height: 27.rdp,
      padding: EdgeInsets.only(right: 80.rdp),
      child: Row(
        children: [
          Flexible(
            child: RichText(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                text: widget.title ?? "",
                style: TextStyle(
                    color: ctrl.getColor(context, 6),
                    fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                    fontSize: 16.rdp),
              ),
            ),
          ),
          SizedBox(width: 4.rdp,),
          RichText(
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            text: TextSpan(
              children: [
                TextSpan(
                  text: "${widget.finishLessonCount}",
                  style: TextStyle(
                      color: ctrl.getColor(context, 5),
                      fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                      fontSize: 16.rdp),
                ),
                TextSpan(
                  text: "/${widget.allLessonCount}",
                  style: TextStyle(
                      color: ctrl.getColor(context, 6),
                      fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                      fontSize: 16.rdp),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    PlanHomeLessonCtrl ctrl = context.read<PlanHomeLessonCtrl>();
    return _buildContent(ctrl);
  }
}