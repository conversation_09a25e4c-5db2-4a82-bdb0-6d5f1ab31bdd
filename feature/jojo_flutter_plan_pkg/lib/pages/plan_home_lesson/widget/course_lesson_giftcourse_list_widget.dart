import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/utils/course_map_utils.dart';

import '../../plan_home_map/model/course_map_home_page_tab_data.dart';
import '../../plan_home_map/model/course_subject_clean_data.dart';

///专项课程列表，这里面
class CourseLessonGiftCourseListWidget extends StatelessWidget {
  final GiftClassData giftClassData;

  const CourseLessonGiftCourseListWidget(
      {super.key, required this.giftClassData});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: _calculateItemCount(),
      itemBuilder: (context, index) {
        final isLastItem = index == _listLength;
        return AdaptiveOrientationLayout(portrait: (BuildContext context) {
          if (isLastItem) {
            return SizedBox(height: 135.rdp);
          }
          return _buildItemWidget(context,
              giftClassData.courseInfoList?.elementAtOrNull(index), 1.0);
        }, landscape: (BuildContext context) {
          if (isLastItem) {
            return const SizedBox(height: 0);
          }
          return _buildItemWidget(context,
              giftClassData.courseInfoList?.elementAtOrNull(index), 1.25);
        });
      },
    );
  }

  // 新增帮助方法
  int _calculateItemCount() => (giftClassData.courseInfoList?.length ?? 0) + 1;

  int get _listLength => giftClassData.courseInfoList?.length ?? 0;


  Widget _buildItemWidget(
      BuildContext context, CourseInfoList? courseItem, double ratio) {
    if (courseItem == null) {
      return const SizedBox();
    }
    return GestureDetector(
      onTap: () {
        RunEnv.jumpLink(courseItem.route ?? "");
        RunEnv.sensorsTrack(
          '\$AppClick',
          {
            '\$screen_name': '2025改版后学习页',
            '\$element_name': '赠课卡片_点击',
            'course_type': courseItem.courseType?.getCourseTypeStr() ?? "",
            'course_stage': courseItem.courseSegment ?? "",
            'material_id': giftClassData.subjectTitle ?? "", //科目类型名称
            'class_id': courseItem.classId ?? "",
            'course_key': courseItem.courseKey ?? ""
          },
        );
      },
      child: Container(
        margin: EdgeInsets.only(
            left: (ratio > 1) ? 0 : 20.rdp,
            right: (ratio > 1) ? 0 : 20.rdp,
            bottom: 20.rdp * ratio),
        padding: EdgeInsets.all(8.rdp * ratio),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.rdp * ratio),
            color: context.appColors.colorVariant1(
              HexColor(courseItem.color ?? "#FFFFFF"),
            )),
        width: double.infinity,
        height: 133.rdp * ratio,
        child: Container(
          padding: EdgeInsets.only(left: 20.rdp * ratio, right: 20.rdp * ratio),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.rdp * ratio),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    ImageNetworkCached(
                        borderRadius: 8.rdp * ratio,
                        width: 56.rdp * ratio,
                        height: 56.rdp * ratio,
                        fit: BoxFit.cover,
                        imageUrl: courseItem.courseIcon ?? ""),
                    SizedBox(width: 16.rdp * ratio),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (courseItem.courseName?.isNotNullOrEmpty() == true)
                              ? Text(
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  courseItem.courseName ?? "",
                                  style: TextStyle(
                                      fontSize: 16.rdp * ratio,
                                      fontWeight: FontWeight.w500,
                                      color: context.appColors.colorVariant6(
                                        HexColor(courseItem.color ?? "#FFFFFF"),
                                      )),
                                )
                              : const SizedBox(),
                          (courseItem.mainCourseName?.isNotNullOrEmpty() ==
                                  true)
                              ? SizedBox(width: 0.rdp, height: 2.rdp * ratio)
                              : const SizedBox(),
                          (courseItem.mainCourseName?.isNotNullOrEmpty() ==
                                  true)
                              ? Text(
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  courseItem.mainCourseName ?? "",
                                  style: TextStyle(
                                      fontSize: 14.rdp * ratio,
                                      fontWeight: FontWeight.w400,
                                      color: context.appColors.colorVariant6(
                                        HexColor(courseItem.color ?? "#FFFFFF"),
                                      )),
                                )
                              : const SizedBox(),
                        ])
                  ]),
              SizedBox(height: 16.rdp * ratio),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                      child: _buildProgressWidget(context, courseItem, ratio)),
                  SizedBox(width: 10.rdp * ratio),
                  Text(
                    "${(courseItem.finishLessonNum ?? 0)}/${(courseItem.totalLessonNum ?? 0)}",
                    style: TextStyle(
                        color: context.appColors.colorVariant4(
                          HexColor(courseItem.color ?? "#FFFFFF"),
                        ),
                        fontWeight: FontWeight.w500,
                        fontSize: 12.rdp * ratio),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressWidget(
      BuildContext context, CourseInfoList courseItem, double ratio) {
    return Stack(
      children: [
        // 下面的背景盒子
        Container(
          height: 10.rdp * ratio,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5.rdp * ratio)),
            color: context.appColors.colorVariant2(
              HexColor(courseItem.color ?? "#FFFFFF"),
            ),
          ),
        ),
        // 上面的进度条盒子
        Container(
          height: 10.rdp * ratio,
          width: (courseItem.finishLessonNum ?? 0) /
              max((courseItem.totalLessonNum ?? 1), 1) *
              160.rdp *
              ratio, // 动态更改进度条盒子的宽度即可
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(5.rdp * ratio)),
            color: context.appColors.colorVariant4(
              HexColor(courseItem.color ?? "#FFFFFF"),
            ),
          ),
        ),
      ],
    );
  }
}
