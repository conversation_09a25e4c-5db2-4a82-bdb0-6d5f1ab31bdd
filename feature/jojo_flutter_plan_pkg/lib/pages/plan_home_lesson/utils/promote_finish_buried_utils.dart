
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_lesson_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';

const String screenName = "2025改版后学习页";

Map JoJoPromoteFinishStatusMap = {
  1: "未开始-行课前",
  2: "未开始-行课后",
  3: "进行中",
};

class JoJoPromoteFinishBuriedUtils {
  // 课程列表中，活动卡片曝光数据
  static void ActivityCardScreen(int activityId, int status, CourseLessonInfo? lessonInfo) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    Map<String, dynamic> properties = {
      'c_element_name': "完课活动_曝光",
      ...buriedMap,
    };
    RunEnv.sensorsTrack(
      'ElementView',
      properties,
    );
  }

  // 整个卡片点击（预告头以及整个卡片的点击时上报）
  static void appClickActivityCard(int activityId, int status,CourseLessonInfo? lessonInfo) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      '\$element_name': "完课活动_点击",
      ...buriedMap,
    };
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  // 勋章曝光
  static void ActivityCardMedalScreen(int isGit, int activityId, int status, CourseLessonInfo? lessonInfo, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      'c_element_name': "完课活动_奖励_曝光",
      'business_type': business_type,
      'user_state': isGit,
      ...buriedMap,
    };
    RunEnv.sensorsTrack(
      'ElementView',
      properties,
    );
  }

  // 勋章点击
  static void appClickActivityCardMedal(int isGit, int activityId, int status,CourseLessonInfo? lessonInfo, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      '\$element_name': "完课活动_奖励_点击",
      'business_type': business_type,
      'user_state': isGit,
      ...buriedMap,
    };
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  // 勋章获得页曝光
  static void ActivityMedalGitScreen(int activityId, int status,CourseLessonInfo? lessonInfo, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      'c_element_name': "完课活动_奖励发放_曝光",
      'business_type': business_type,
      'user_state': '1',
      ...buriedMap,
    };
    RunEnv.sensorsTrack(
      'ElementView',
      properties,
    );
  }

  // 已完成节点曝光
  static void ActivityFinishNodeScreen(int activityId, int status,CourseLessonInfo? lessonInfo, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      'c_element_name': "完课活动_已完成节点_曝光",
      'business_type': business_type,
      'user_state': '1',
      ...buriedMap,
    };
    RunEnv.sensorsTrack(
      'ElementView',
      properties,
    );
  }

  // 已完成节点点击
  static void appClickActivityFinishNode(int activityId, int status,CourseLessonInfo? lessonInfo, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      '\$element_name': "完课活动_已完成节点_点击",
      'business_type': business_type,
      'user_state': '1',
      ...buriedMap,
    };
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  // 勋章获得页按钮点击
  static void appClickActivityMedalGit(int activityId, int status,CourseLessonInfo? lessonInfo, String elementName, int? taskType) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    String business_type = getBusinessType(taskType);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      '\$element_name': "完课活动_奖励发放_$elementName",
      'business_type': business_type,
      'user_state': '1',
      ...buriedMap,
    };
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  // 活动引人弹窗曝光
  static void ActivityPopViewScreen(int activityId, int status,CourseLessonInfo? lessonInfo) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      'c_element_name': "完课活动引入弹窗_曝光",
      ...buriedMap,
    };
    RunEnv.sensorsTrack(
      'ElementView',
      properties,
    );
  }

  // 活动引人弹窗点击
  static void appClickActivityPopView(int activityId, int status,CourseLessonInfo? lessonInfo) {
    Map<String, dynamic> buriedMap = getBuriedParams(activityId, status, lessonInfo);
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      '\$element_name': "完课活动引入弹窗_按钮点击",
      ...buriedMap,
    };
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  // 获取通用埋点数据
  static Map<String, dynamic> getBuriedParams(int activityId, int status,CourseLessonInfo? lessonInfo) {
    Map<String, dynamic> properties = {
      '\$screen_name': screenName,
      'course_type': getCourseTypeStr(lessonInfo?.courseType ?? 0),
      'course_stage': lessonInfo?.courseSegment ?? "",
      'activity_id': activityId,
      'class_id': lessonInfo?.classId ?? 0,
      'material_id': lessonInfo?.subjectName ?? "其他",
      'custom_state': JoJoPromoteFinishStatusMap[status],
    };
    return properties;
  }

  static String getCourseTypeStr(int courseType) {
    if (courseType == 1) {
      return "训练营";
    } else if (courseType == 2) {
      return "单课";
    } else if (courseType == 3) {
      return "年课";
    } else if (courseType == 4) {
      return "体验课";
    } else {
      return "其他";
    }
  }

  static String getBusinessType(int? taskType) {
    String business_type = "勋章";
    if (taskType != null) {
      if (taskType == PromoteLessonRewardsType.virtual) {
        business_type = "商品（SKU）";
      } else if (taskType == PromoteLessonRewardsType.physical) {
        business_type = "商品（ERP）";
      } else if (taskType == PromoteLessonRewardsType.raffle) {
        business_type = "抽奖活动";
      } else if (taskType == PromoteLessonRewardsType.dress) {
        business_type = "装扮";
      }
    }
    return business_type;
  }
}
