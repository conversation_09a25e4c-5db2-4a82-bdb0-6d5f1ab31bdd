import 'dart:async';

import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/utils/file_util.dart';

/// 下载任务信息
class DownloadTask {
  final String url;
  final Completer<String> completer;
  final bool needUnzip;
  bool isCancelled = false;

  DownloadTask({
    required this.url,
    required this.completer,
    this.needUnzip = false,
  });
}

/// 全局下载管理器，避免重复下载，支持取消
class GlobalDownloadManager {
  // 当前正在执行的下载管理器实例
  AbsDownloadManager? _currentDownloadManager;
  static final GlobalDownloadManager _instance = GlobalDownloadManager._internal();
  factory GlobalDownloadManager()=>_instance;
  GlobalDownloadManager._internal(){
    _currentDownloadManager = JoJoResourceManager();
  }

  // 正在下载的任务
  final Map<String, DownloadTask> _downloadingTasks = {};

  // 已下载完成的资源缓存
  final Map<String, String> _downloadedCache = {};



  /// 下载资源（带去重和缓存）
  Future<Map<String, String>> downloadResources(
      List<String> urlList, {
        bool needUnzip = false,
        Function(double)? progressListener,
        String? taskId, // 任务ID，用于取消
      }) async {
    Map<String, String> result = {};
    List<String> needDownloadUrls = [];

    // 检查缓存和正在下载的任务
    for (String url in urlList) {
      if (_downloadedCache.containsKey(url)) {
        // 已经下载完成
        result[url] = _downloadedCache[url]!;
      } else if (_downloadingTasks.containsKey(url)) {
        // 正在下载，等待完成
        try {
          DownloadTask task = _downloadingTasks[url]!;
          if (task.isCancelled) {
            throw Exception("下载已取消: $url");
          }
          String path = await task.completer.future;
          result[url] = path;
          _downloadedCache[url] = path;
        } catch (e) {
          if (e.toString().contains("下载已取消")) {
            rethrow;
          }
          throw Exception("下载失败: $url, 错误: $e");
        }
      } else {
        // 需要下载
        needDownloadUrls.add(url);
      }
    }

    // 下载新的资源
    if (needDownloadUrls.isNotEmpty) {
      Map<String, String> newDownloads = await _downloadNewResources(
        needDownloadUrls,
        needUnzip: needUnzip,
        progressListener: progressListener,
        taskId: taskId,
      );
      result.addAll(newDownloads);
    }

    return result;
  }

  /// 下载新资源
  Future<Map<String, String>> _downloadNewResources(
      List<String> urlList, {
        bool needUnzip = false,
        Function(double)? progressListener,
        String? taskId,
      }) async {
    // 为每个URL创建下载任务
    for (String url in urlList) {
      if (!_downloadingTasks.containsKey(url)) {
        _downloadingTasks[url] = DownloadTask(
          url: url,
          completer: Completer<String>(),
          needUnzip: needUnzip,
        );
      }
    }

    try {
      // 执行实际下载
      Map<String, String> downloadResult = await _performActualDownload(
        urlList,
        needUnzip: needUnzip,
        progressListener: progressListener,
        taskId: taskId,
      );

      // 完成所有下载任务
      for (String url in urlList) {
        DownloadTask? task = _downloadingTasks[url];
        if (task != null) {
          if (task.isCancelled) {
            task.completer.completeError("下载已取消: $url");
          } else if (downloadResult.containsKey(url)) {
            String path = downloadResult[url]!;
            _downloadedCache[url] = path;
            task.completer.complete(path);
          } else {
            task.completer.completeError("下载失败: $url");
          }
          _downloadingTasks.remove(url);
        }
      }

      return downloadResult;
    } catch (e) {
      // 下载失败，完成所有等待的任务
      for (String url in urlList) {
        DownloadTask? task = _downloadingTasks[url];
        if (task != null) {
          if (!task.completer.isCompleted) {
            task.completer.completeError(e);
          }
          _downloadingTasks.remove(url);
        }
      }
      rethrow;
    }
  }

  /// 执行实际下载
  Future<Map<String, String>> _performActualDownload(
      List<String> urlList, {
        bool needUnzip = false,
        Function(double)? progressListener,
        String? taskId,
      }) async {
    // 检查是否已取消
    for (String url in urlList) {
      DownloadTask? task = _downloadingTasks[url];
      if (task != null && task.isCancelled) {
        throw Exception("下载已取消: $url");
      }
    }

    Completer<Map<String, String>> completer = Completer();


    await _currentDownloadManager!.downloadUrl(
      urlList,
      isNeedCancel: true,
      successListener: (urlMap) async {
        // 再次检查是否已取消
        for (String url in urlList) {
          DownloadTask? task = _downloadingTasks[url];
          if (task != null && task.isCancelled) {
            completer.completeError("下载已取消: $url");
            return;
          }
        }

        Map<String, String> result = {};

        for (String url in urlList) {
          String localPath = urlMap[url] ?? "";
          if (localPath.isNotEmpty) {
            if (needUnzip && localPath.contains('.zip')) {
              try {
                String dirPath = await unzip(localPath);
                result[url] = dirPath;
              } catch (e) {
                throw Exception("解压失败: $url, 错误: $e");
              }
            } else {
              result[url] = localPath;
            }
          }
        }

        if (!completer.isCompleted) {
          completer.complete(result);
        }
      },
      failListener: (error) {
        if (!completer.isCompleted) {
          completer.completeError(error);
        }
      },
    );

    return completer.future;
  }

  /// 取消指定URL的下载
  void cancelDownload(String url) {
    DownloadTask? task = _downloadingTasks[url];
    if (task != null) {
      task.isCancelled = true;
      if (!task.completer.isCompleted) {
        task.completer.completeError("下载已取消: $url");
      }
      _downloadingTasks.remove(url);
    }
  }

  /// 取消多个URL的下载
  void cancelDownloads(List<String> urlList) {
    for (String url in urlList) {
      cancelDownload(url);
    }
  }

  /// 取消所有正在进行的下载
  void cancelAllDownloads() {
    List<String> urls = _downloadingTasks.keys.toList();
    for (String url in urls) {
      cancelDownload(url);
    }

    // 取消当前的下载管理器
    _currentDownloadManager?.cancelDownload();
  }

  /// 检查URL是否正在下载
  bool isDownloading(String url) {
    return _downloadingTasks.containsKey(url);
  }

  /// 获取正在下载的URL列表
  List<String> getDownloadingUrls() {
    return _downloadingTasks.keys.toList();
  }

  /// 清除缓存
  void clearCache() {
    _downloadedCache.clear();
  }

  /// 清除所有数据（包括正在下载的任务）
  void clearAll() {
    cancelAllDownloads();
    clearCache();
  }

  /// 检查资源是否已下载
  bool isResourceDownloaded(String url) {
    return _downloadedCache.containsKey(url);
  }

  /// 获取已下载资源的路径
  String? getDownloadedPath(String url) {
    return _downloadedCache[url];
  }

  /// 获取下载状态
  DownloadStatus getDownloadStatus(String url) {
    if (_downloadedCache.containsKey(url)) {
      return DownloadStatus.completed;
    } else if (_downloadingTasks.containsKey(url)) {
      DownloadTask task = _downloadingTasks[url]!;
      if (task.isCancelled) {
        return DownloadStatus.cancelled;
      } else {
        return DownloadStatus.downloading;
      }
    } else {
      return DownloadStatus.notStarted;
    }
  }
}

/// 下载状态枚举
enum DownloadStatus {
  notStarted,  // 未开始
  downloading, // 下载中
  completed,   // 已完成
  cancelled,   // 已取消
  failed,      // 失败
}