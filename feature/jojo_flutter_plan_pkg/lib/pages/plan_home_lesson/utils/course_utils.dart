import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

import '../../../common/host_env/host_env.dart';
import '../../../generated/l10n.dart';

typedef RefreshCallback = Function(Size);

class CourseUtils {
  static String formatRemainingTime(int milliseconds, BuildContext context) {
    int timestamp = milliseconds;
    if (timestamp < 0) {
      timestamp = 0;
    }
    final l10n = S.of(context);
    // 将毫秒转换为秒
    int seconds = timestamp ~/ 1000;
    int totalHours = seconds ~/ 3600;
    int days = totalHours ~/ 24;
    int hours = totalHours % 24;
    int minutes = (seconds % 3600) ~/ 60;
    if (days > 99) {
      //业务上，超过100天的展示99天24小时
      days = 99;
      hours = 24;
    }
    if (days > 0) {
      return l10n.remainingDaysHours(days, hours);
    } else {
      String formattedMinutes = minutes.toString().padLeft(2, '0');
      return l10n.remainingHoursMinutes(hours, formattedMinutes);
    }
  }

  /// 判断一个时间戳是否在24小时之后
  static bool isTwentyFourHoursLater(int timestamp) {
    DateTime targetTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // 获取当前时间
    DateTime now = DateTime.now();
    // 计算当前时间的24小时后的时间
    DateTime twentyFourHoursLater = now.add(const Duration(hours: 24));
    return targetTime.isAfter(twentyFourHoursLater);
  }

  /// 获取输入时间和当前时间的时间差，并返回单位s
  static int getTimeDifferenceInMinutes(int timestamp) {
    final currentTime = DateTime.now().millisecondsSinceEpoch; // 当前时间戳（毫秒）
    var difference = timestamp - currentTime; // 时间差（毫秒）
    if (difference < 0) {
      difference = 0;
    }
    return (difference / 1000).floor(); // 转换为秒
  }

  @visibleForTesting
  static String testFormatRemainingTime(int milliseconds) {
    int timestamp = milliseconds;
    if (timestamp < 0) {
      timestamp = 0;
    }
    int seconds = timestamp ~/ 1000;
    int totalHours = seconds ~/ 3600;
    int days = totalHours ~/ 24;
    int hours = totalHours % 24;
    int minutes = (seconds % 3600) ~/ 60;
    if (days > 99) {
      //业务上，超过100天的展示99天24小时
      days = 99;
      hours = 24;
    }
    if (days > 0) {
      return remainingDaysHours(days, hours);
    } else {
      String formattedMinutes = minutes.toString().padLeft(2, '0');
      return remainingHoursMinutes(hours, formattedMinutes);
    }
  }

  /// `剩余{days}天{hours}小时`
  @visibleForTesting
  static String remainingDaysHours(Object days, Object hours) {
    return Intl.message(
      '剩余$days天$hours小时',
      name: 'remainingDaysHours',
      desc: '',
      args: [days, hours],
    );
  }

  /// `剩余{hours}:{minutes}`
  /// @visibleForTesting
  static String remainingHoursMinutes(Object hours, Object minutes) {
    return Intl.message(
      '剩余$hours:$minutes',
      name: 'remainingHoursMinutes',
      desc: '',
      args: [hours, minutes],
    );
  }

  // 获取图片上的宽高
  static void getSizeByLoadImage(String imageUrl, RefreshCallback refreshCallback) {
    try {
      final imageProvider = NetworkImage(imageUrl);

      final imageStream = imageProvider.resolve(ImageConfiguration.empty);

      imageStream.addListener(
        ImageStreamListener(
              (ImageInfo imageInfo, bool synchronousCall) {
            double imageWidth = imageInfo.image.width.toDouble();
            double imageHeight = imageInfo.image.height.toDouble();
            refreshCallback(Size(imageWidth, imageHeight));
          },
        ),
      );
    } catch (e) {
      refreshCallback(Size.zero);
    }
  }

  static goHome() {
    RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/home/<USER>");
  }
}
