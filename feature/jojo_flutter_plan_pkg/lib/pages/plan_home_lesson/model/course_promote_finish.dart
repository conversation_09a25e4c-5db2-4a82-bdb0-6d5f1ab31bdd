
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_lesson_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';

class PromoteLessonFinishModel {
  int? subjectType;  // 当前科目
  int? activityId; // 活动ID
  int? status; // 状态
  String? topImageIconProtrait;  // 顶部图片资源对象-竖版
  String? topImageIconLandscape;  // 顶部图片资源对象-横版
  String? topPersonalImage;  // 顶部个人形象图片资源对象
  String? classKey;  // 班级信息
  CourseLessonInfo? lessonInfo;  // 课程信息
  PromoteLessonFinishPopVo? activityStartPopupInfo;  // 活动开始弹窗对象
  PreLessonPreviewHeaderModel? preLessonPreviewHeader; // 预览课程头部信息
  LessonInProgressModel? lessonInProgress;  // 进行中的课程对象

  // 数据转化（课程数据转化成我需要的促完课数据）
  static PromoteLessonFinishModel? fromClassTimetableVo(BuildContext? context, String? classKey, int? preCompleteCnt, CourseLessonInfo courseLessonInfo, PlanHomeLessonCtrl ctrl) {

    ClassActivitiesVo? activity = courseLessonInfo.activities;
    if (activity == null) return null;  // 没有活动
    if ((activity.taskVo??[]).isEmpty) return null;
    if (_checkStatus(activity.status ?? 0)) return null;  // 活动状态不对也不返回

    PromoteLessonFinishModel model = PromoteLessonFinishModel();
    // 处理头部信息
    model.topImageIconProtrait = activity.themeRes?.themeHead?.phoneBackgroundImg ?? "";
    model.topImageIconLandscape = activity.themeRes?.themeHead?.padBackgroundImg ?? "";
    model.topPersonalImage = activity.postIcon;
    // 班级信息
    model.subjectType = courseLessonInfo.subjectType;
    model.classKey = classKey;
    model.activityId = activity.activityId;
    model.status = activity.status;
    // 课程信息
    model.lessonInfo = courseLessonInfo;
    // 处理卡片相关数据
    model.preLessonPreviewHeader = _getPreLessonPreviewHeaderModel(activity, courseLessonInfo);
    model.lessonInProgress = _getLessonInProgressModel(preCompleteCnt, courseLessonInfo,activity, ctrl);
    // 处理进行中的首次弹窗数据
    model.activityStartPopupInfo = _getActivityPopupInfo(activity, courseLessonInfo, courseLessonInfo.classId);
    return model;
  }

  // 检查状态是否正确
  static bool _checkStatus(int status) {
    if (status != PromoteLessonFinishStatus.NOT_START_OPENTIME_PRE && 
        status != PromoteLessonFinishStatus.NOT_START_OPENTIME_NEXT &&
        status != PromoteLessonFinishStatus.IN_PROGRESS) {
        return true;
    }
    return false;
  }

  // 预告卡片数据对象
  static PreLessonPreviewHeaderModel? _getPreLessonPreviewHeaderModel(ClassActivitiesVo activity, CourseLessonInfo courseLessonInfo) {
    if (activity.status != PromoteLessonFinishStatus.NOT_START_OPENTIME_PRE) return null;

    ClassActivitiesThemeActivityCardVo? themeActivityCard = activity.themeRes?.themeActivityCard;
    if (themeActivityCard == null) return null;
    PreLessonPreviewHeaderModel model = PreLessonPreviewHeaderModel();
    model.icon = themeActivityCard.icon;
    model.title = themeActivityCard.advanceText;
    model.route = themeActivityCard.advancePage;
    // 埋点数据
    model.activityId = activity.activityId;
    model.status = activity.status;
    model.lessonInfo = courseLessonInfo;
    return model;
  }

  // 进行中的卡片数据对象
  static LessonInProgressModel? _getLessonInProgressModel(int? preCompleteCnt, CourseLessonInfo courseLessonInfo, ClassActivitiesVo activity, PlanHomeLessonCtrl ctrl) {
    if (activity.status == PromoteLessonFinishStatus.NOT_START_OPENTIME_PRE) return null;

    ClassActivitiesThemeActivityCardVo? themeActivityCard = activity.themeRes?.themeActivityCard;
    ClassActivitiesThemeLessonCardVo? themeLessonCard = activity.themeRes?.themeLessonCard;
    if ((activity.taskVo??[]).isEmpty) return null;
    ClassActivitiesTaskVo? taskVo = activity.taskVo?.first;
    if (themeActivityCard == null) return null;
    if (taskVo?.conditions == null || taskVo?.rewards == null) return null;

    LessonInProgressModel model = LessonInProgressModel();
    // 活动名称以及跳转
    if (activity.status != PromoteLessonFinishStatus.IN_PROGRESS) {
      model.message = themeActivityCard.advanceText;
      model.route = themeActivityCard.advancePage;
    } else {
      model.message = themeActivityCard.activityText;
      model.route = themeActivityCard.activityPage;
    }
    // 落地页id
    model.advanceRedirectPageId = themeActivityCard.advanceRedirectPageId;
    model.activityRedirectPageId = themeActivityCard.activityRedirectPageId;
    // 进度条颜色
    model.progressStartColor = themeActivityCard.progressHeadColor;
    model.progressEndColor = themeActivityCard.progressTailColor;
    // 奖励数据
    ClassActivitiesTaskRewardsVo? taskRewardsVo = taskVo?.rewards?.first;
    // 完课数处理
    ClassActivitiesTaskConditionsVo? conditionsVo;
    // 判断是否是多阶段任务
    model.isMultistage = taskVo?.taskType == PromoteLessonTaskType.STEPWISE;
    String taskType = taskVo?.taskType ?? "";
    if (model.isMultistage) {
      // 找到列表中最后一个奖励类型为taskType的任务(后端排序了，目标值根据它来定)
      for (var element in activity.taskVo ?? []) {
        if (element == null) continue;
        if (element?.taskType == taskType) {
          taskVo = element;
        }
      }
      taskRewardsVo = taskVo?.rewards?.first;
    }
    conditionsVo = taskVo?.conditions?.first;
    model.lessonCount = conditionsVo?.targetValue ?? 1;
    model.completeLessonCount = conditionsVo?.currentValue ?? 0;
    if (preCompleteCnt == null) {
      model.preCompleteLessonCount = model.completeLessonCount;
    } else {
      model.preCompleteLessonCount = preCompleteCnt;
    }
    model.audioUrl = activity.nodeRewardVoice;  // 未获得时的语音数据

    // 组装阶段数据
    if (model.isMultistage) {
      for (var element in activity.taskVo ?? []) {
        if (element == null) continue;
        ClassActivitiesTaskRewardsVo? taskRewardsVo = element.rewards?.first;
        // 任务类型不正确，则不能组装对应数据
        if (taskRewardsVo == null) continue;
        LessonInProgressGiftModel item = getGiftModel(activity, model, element);
        model.giftList.add(item);
        if (item.index == model.lessonCount) {
          // 最终阶段奖励
          model.allFinishGiftModel = item;
        } else if (!item.isGet && model.nextGiftModel == null) {
          // 中间阶段奖励
          model.nextGiftModel = item;
        } else if (item.isGet) {
          // 上一个已完成的阶段奖励对象
          model.preFinishGiftModel = item;
        }
      }
      // 计算当前未完成进度的进度值
      if (model.currentGiftModel == null && model.completeLessonCount != model.lessonCount && model.completeLessonCount != 0) {
        int finishCnt = 0;  // 下一个未完成阶段除已完成的课数（减掉之前阶段的目标数）
        int unFinishCnt = 0;  // 下一个未完成阶段除需要完成的课数
        for (var item in model.giftList) {
          if (item.isGet) {
            unFinishCnt = item.index;
            model.finishNodeProgress = item.index * 1.0 / model.lessonCount;
          } else {
            finishCnt = item.currentValue - unFinishCnt;
            unFinishCnt = item.index - unFinishCnt;
            model.targetNodeProgress = item.index * 1.0 / model.lessonCount;
            break;
          }
        }
        if (unFinishCnt > 0) {
          model.progress = finishCnt * 1.0 / unFinishCnt;
        }
      }

    } else {
      model.allFinishGiftModel = getGiftModel(activity, model, taskVo!);
    }

    // 处理spine资源
    model.spineResourceInfo = _dealWithSpineResources(model, themeActivityCard.progressRes, themeActivityCard.backgroundRes, themeLessonCard?.collectRes, model.allFinishGiftModel?.giftSpineUrl, ctrl);
    return model;
  }

  static LessonInProgressGiftModel getGiftModel(ClassActivitiesVo activity, LessonInProgressModel model, ClassActivitiesTaskVo taskVo) {
    ClassActivitiesTaskConditionsVo? conditionsVo = taskVo.conditions?.first;
    ClassActivitiesTaskRewardsVo? taskRewardsVo = taskVo.rewards?.first;
    LessonInProgressGiftModel item = LessonInProgressGiftModel();
    item.currentValue = conditionsVo?.currentValue ?? 0;
    item.index = conditionsVo?.targetValue ?? 0;
    item.lessonCount = model.lessonCount;
    item.completeLessonCount = model.completeLessonCount;
    item.activityId = activity.activityId;
    item.taskId = taskVo.taskId;
    item.isGet = item.completeLessonCount >= item.index;
    item.type = taskRewardsVo?.type;
    int rewardsLength = taskVo.rewards?.length ?? 0;
    bool isMultistageReward = rewardsLength > 1;
    taskVo.rewards?.forEach((element) {
      LessonInProgressGiftRewardModel rewardItem = LessonInProgressGiftRewardModel();
      rewardItem.activityId = activity.activityId;
      rewardItem.taskId = taskVo.taskId;
      rewardItem.medalId = element?.rewardId;
      rewardItem.type = element?.type;
      rewardItem.route = element?.rewardBizUrl;
      rewardItem.title = getGiftRewardNodeModel(taskVo.taskExtendResource?.rewardNodeTexts, rewardItem.type)?.mainText ?? "";
      rewardItem.subTitle = getGiftRewardNodeModel(taskVo.taskExtendResource?.rewardNodeTexts, rewardItem.type)?.subText ?? "";
      rewardItem.isGet = element?.isGet == PromoteLessonMedalGitStatus.GIT;
      if (isMultistageReward) {
        // 多奖励,使用后端返回的类型->动画名称以及语音名称
        rewardItem.animationName = getGiftAnimationName(rewardItem.type);
        rewardItem.audioSoundEffectsName = "${AudioResourceConstants.audioEventSoundEffectsPre}${rewardItem.animationName ?? ""}.mp3";
        rewardItem.audioDubbingName = "${AudioResourceConstants.audioEventDubbingPre}${rewardItem.animationName ?? ""}.mp3";
      } else {
        // 单奖励,使用单奖励的动效以及语音规范
        rewardItem.animationName = SpineAnimationConstants.badgeInfoAnimationFull;
        rewardItem.audioSoundEffectsName = AudioResourceConstants.audioEventSoundEffectsOldFull;
        rewardItem.audioDubbingName = AudioResourceConstants.audioEventDubbingFull;
      }
      item.rewardList.add(rewardItem);
    });
    item.giftSpineUrl = taskVo.taskExtendResource?.rewardDisplayUrl;
    if (model.isMultistage) {
      // 对于勋章累的奖励，动效资源为空时需要从另一处配置中获取
      if (item.type == PromoteLessonRewardsType.normal && (item.giftSpineUrl ?? "").isEmpty) {
        item.giftSpineUrl = taskRewardsVo?.resourceFlutter;
      }
      if (item.completeLessonCount == item.index && model.currentGiftModel == null) {
        model.currentGiftModel = item;
      }
    } else {
      if ((item.giftSpineUrl ?? "").isEmpty && item.type == PromoteLessonRewardsType.normal && !isMultistageReward) {
        item.giftSpineUrl = taskRewardsVo?.resourceFlutter;
      }
    }
    return item;
  }

  static String getGiftBtnName(BuildContext? context, int? type, bool isGit) {
    if (context == null) return "";
    if (type == PromoteLessonRewardsType.normal) {
      return S.of(context).shareMyAchievement;
    } else if (type == PromoteLessonRewardsType.virtual) {
      return S.of(context).iKnow;
    } else if (type == PromoteLessonRewardsType.raffle) {
      return S.of(context).goRaffle;
    } else if (type == PromoteLessonRewardsType.physical) {
      // 已下单展示去查看，未下单则展示立即领取
      return isGit ? S.of(context).goScan : S.of(context).getGift;
    } else if (type == PromoteLessonRewardsType.dress) {
      return S.of(context).takeIt;
    } else {
      return S.of(context).iKnow;
    }
  }

  static String getGiftAnimationName(int? type) {
    if (type == PromoteLessonRewardsType.normal) {
      return "medal";
    } else if (type == PromoteLessonRewardsType.virtual) {
      return "productErp";
    } else if (type == PromoteLessonRewardsType.raffle) {
      return "lottery";
    } else if (type == PromoteLessonRewardsType.physical) {
      return "productSku";
    } else if (type == PromoteLessonRewardsType.dress) {
      return "dress";
    } else {
      return "full";
    }
  }

  static ClassActivitiesTaskRewardNodeTextVo? getGiftRewardNodeModel(List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts, int? type) {
    for (var item in rewardNodeTexts ?? []) {
      if (item?.rewardType == type) {
        return item;
      }
    }
    return null;
  }

  static PromoteLessonFinishSpineResourceInfo _dealWithSpineResources(LessonInProgressModel progressModel, String? progressRes, String? backgroundRes, String? collectRes, String? resourceFlutter, PlanHomeLessonCtrl ctrl) {
    PromoteLessonFinishSpineResourceInfo model = PromoteLessonFinishSpineResourceInfo();
    // 背景动效资源
    model.badgeBgResource.resourceUrl = backgroundRes;
    String? backgroundResLocalPath = ctrl.promoteFinishSpineMap["$backgroundRes"];
    if (backgroundResLocalPath != null) {
      model.badgeBgResource.localPath = backgroundResLocalPath;
      model.badgeBgResource.isDowned = true;
    }
    // 星星动效资源
    model.starResource.resourceUrl = collectRes;
    String? collectResLocalPath = ctrl.promoteFinishSpineMap["$collectRes"];
    if (collectResLocalPath != null) {
      model.starResource.localPath = collectResLocalPath;
      model.starResource.isDowned = true;
    }
    // 飞星动效资源
    model.starFlyResource.resourceUrl = progressRes;
    String? progressResLocalPath = ctrl.promoteFinishSpineMap["$progressRes"];
    if (progressResLocalPath != null) {
      model.starFlyResource.localPath = progressResLocalPath;
      model.starFlyResource.isDowned = true;
    }

    String? targetResourceUrl = resourceFlutter;
    // 阶段奖励动效果资源
    for (var element in progressModel.giftList) {
      PromoteLessonFinishSpineResourceVo resourceVo = PromoteLessonFinishSpineResourceVo();
      resourceVo.resourceUrl = element.giftSpineUrl;
      String? giftSpineUrlLocalPath = ctrl.promoteFinishSpineMap["${element.giftSpineUrl}"];
      if (giftSpineUrlLocalPath != null) {
        resourceVo.localPath = giftSpineUrlLocalPath;
        resourceVo.isDowned = true;
      }
      element.spineResourceVo = resourceVo;
      // 绑定气泡动效资源
      if (progressModel.nextGiftModel != null && progressModel.nextGiftModel?.giftSpineUrl == resourceVo.resourceUrl) {
        progressModel.nextGiftModel?.spineResourceVo = resourceVo;
      }
      // 绑定弹窗奖励动效果资源
      if (progressModel.currentGiftModel != null && progressModel.currentGiftModel?.giftSpineUrl == resourceVo.resourceUrl) {
        progressModel.currentGiftModel?.spineResourceVo = resourceVo;
      }
      if (element == progressModel.giftList.last) {
        targetResourceUrl = element.giftSpineUrl;
      }
    }
    // 最终动效资源
    model.targetResource.resourceUrl = targetResourceUrl;
    if ((targetResourceUrl ?? "").isEmpty && progressModel.isMultistage == false && progressModel.allFinishGiftModel?.type == PromoteLessonRewardsType.normal) {
      model.targetResource.resourceUrl = progressModel.allFinishGiftModel?.giftSpineUrl;
    }
    String? resourceFlutterLocalPath = ctrl.promoteFinishSpineMap["${model.targetResource.resourceUrl}"];
    if (resourceFlutterLocalPath != null) {
      model.targetResource.localPath = resourceFlutterLocalPath;
      model.targetResource.isDowned = true;
    }
    return model;
  }

  // 获取活动弹窗数据对象
  static PromoteLessonFinishPopVo? _getActivityPopupInfo(ClassActivitiesVo activitiesVo, CourseLessonInfo courseLessonInfo,int? classId) {
    if (activitiesVo.themeRes?.themeActivityPopup == null) return null;
    if (activitiesVo.status != PromoteLessonFinishStatus.IN_PROGRESS) return null;

    ClassActivitiesPopupVo? popupVo = activitiesVo.themeRes?.themeActivityPopup;
    PromoteLessonFinishPopVo popup = PromoteLessonFinishPopVo();
    popup.activityId = activitiesVo.activityId;
    popup.classId = classId;
    popup.status = activitiesVo.status;
    popup.lessonInfo = courseLessonInfo;
    popup.videoUrl = popupVo?.guidingVideo;
    popup.audioUrl = popupVo?.guidingAudio;
    popup.icon = popupVo?.headImg;
    popup.title = popupVo?.mainTitle;
    popup.subTitle = popupVo?.subTitle;
    popup.btnText = popupVo?.btnTitle ?? "立即挑战";
    popup.properties = JoJoPromoteFinishBuriedUtils.getBuriedParams(activitiesVo.activityId ?? 0, activitiesVo.status ?? 3, courseLessonInfo);
    popup.type = PromoteLessonFinishPopType.guide;
    return popup;
  }
}

// 弹窗数据
class PromoteLessonFinishPopVo {
  int? activityId;
  int? status;
  int? taskId;  // 任务Id
  int? classId;
  String? videoUrl;  //  视频资源地址
  String? audioUrl;  //  音频资源地址
  String? bizId;  // 上报需要
  String? icon;  // 要显示的图片
  String? title;  // 标题
  String? subTitle;  // 副标题
  String? btnText;  // 按钮文案
  CourseLessonInfo? lessonInfo;
  Map<String, dynamic>? properties;  // 通用埋点对象
  PromoteLessonFinishPopType type = PromoteLessonFinishPopType.guide;  // 弹窗状态
}

// 弹窗状态
enum PromoteLessonFinishPopType {
  guide,  // 首次弹窗
  finish, // 首次完课弹窗
}

class PromoteLessonFinishSpineResourceInfo {
  PromoteLessonFinishSpineResourceVo targetResource = PromoteLessonFinishSpineResourceVo();  // 最终动效资源
  PromoteLessonFinishSpineResourceVo badgeBgResource = PromoteLessonFinishSpineResourceVo();  // 奖章获得页背景动效资源
  PromoteLessonFinishSpineResourceVo starResource = PromoteLessonFinishSpineResourceVo();  // 星星资源
  PromoteLessonFinishSpineResourceVo starFlyResource = PromoteLessonFinishSpineResourceVo();  // 飞星资源

  bool getDownStatus() {
    List<PromoteLessonFinishSpineResourceVo> resources = [
      targetResource, badgeBgResource, starResource, starFlyResource
    ];
    return resources.every((resource) => resource.isDowned);
  }
}

class PromoteLessonFinishSpineResourceVo {
  bool isDowned = false; // 资源是否已经下载好了
  String? resourceUrl;  // 资源下载url
  String? localPath;  // 下载完成后保存的地址

  String getFilePath(String name) {
    String? filePath;
    if (localPath != null) {
      // 已下载好的动效资源
      File? file = findFilesByExtension(localPath ?? "", name);
      filePath = file?.path ?? "";
    }
    return filePath ?? "";
  }
}

// 资源下载队列类
class ResourceDownloadQueue {
  final List<String> firstBatch;  // 最终奖励资源
  final List<String> secondBatch; // 飞星、背景、星星动效
  final List<String> thirdBatch;  // 中间奖励动效资源

  ResourceDownloadQueue({
    required this.firstBatch,
    required this.secondBatch,
    required this.thirdBatch,
  });
}

class SpineAnimationConstants {
  /// 奖章动效资源中具体动画的名称
  static const String badgeInfoAnimationNormal = "normal";  // 轻微晃动
  static const String badgeInfoAnimationFull = "full";  // 进度完成时从无到有的动画
  /// 星星动效资源中具体动画的名称
  static const String starAnimationNormal = "normal";  // 静态不动
  static const String starAnimationFinish = "finish";  // 消失动画
  static const String twStarAnimationPlay = "play";  // 拖尾动画
  static const String twProgressAnimationPlay = "play2";  // 拖尾动画中，第一段动画
  /// 奖章动画名称
  static const String badgeBgAnimation = "full";
  static const String badgeBgAnimationLoop = "loop";
  /// 预告动效果资源中具体动画的名称
  static const String bubbleAnimationLoop = "loop";  // 气泡出现时的动效果
  static const String bubbleAnimationFull = "full";  // 获得后的动效
}

class AudioResourceConstants {
  static const String audioEventDubbingPre = "audio_event_dubbing_";  // 配音前缀
  static const String audioEventSoundEffectsPre = "audio_event_sound_effects_";  // 音效前缀
  static const String audioEventMedalSummerFull = "audio_event_medal_summer_full.mp3";  // 奖章获得音效
  static const String audioEventDubbingFull = "audio_event_dubbing_full.mp3";  // 奖励配音
  static const String audioEventSoundEffectsFull = "audio_event_sound_effects_full.mp3";  // 奖励音效
  static const String audioEventDubbingLoop = "audio_event_dubbing_loop.mp3";  // 气泡配音
  static const String audioEventSoundEffectsLoop = "audio_event_sound_effects_loop.mp3";  // 气泡音效
  static const String audioEventMedalSummerNormal = "audio_event_medal_summer_normal.mp3";
  static const String audioEventSoundEffectsOldFull = "audio_event_soundeffects_full.mp3";  // 奖励音效
  static const String audioEventSoundEffectsOldLoop = "audio_event_soundeffects_loop.mp3";  // 气泡音效
}

class PromoteLessonFinishStatus {
  static const int NOT_START_OPENTIME_PRE = 1;  // 未开始预告-预告时间在开课前
  static const int NOT_START_OPENTIME_NEXT = 2; // 未开始预告-预告时间在开课后
  static const int IN_PROGRESS = 3;  // 进行中
}

class PromoteLessonTaskType {
  static const String DAILY = "DAILY";  // 日常任务
  static const String ONETIME = "ONETIME"; // 单次任务
  static const String STEPWISE = "STEPWISE";  // 阶梯任务
}

class PromoteLessonMedalGitStatus {
  static const int NOT_GIT = 0;  // 未获得
  static const int GIT = 1; // 已获得
}

class PromoteLessonLessonCardCollectStatus {
  static const int NOT_MEET = 0;  // 不在活动范围内
  static const int MEET = 1; // 属于活动范围内的课程
}

class PromoteLessonStatus {
  static const int UNLOCK_ALLFINISH = 1;  // 已解锁，已全部学完
  static const int UNLOCK_HASUNFINISHLESSON = 2; // 已解锁，未全部完成
  static const int LOCK = 3; // 所有课程都未解锁
}

// 促完课任务类型
class PromoteLessonRewardsType {
  static const int normal = 5;  // 奖章类型
  static const int dress = 14; // 装扮奖励
  // 下方全是多阶段任务
  static const int virtual = 13; // 虚拟奖励任务
  static const int physical = 12;  // 实物奖励任务
  static const int raffle = 11; // 抽奖任务
}

class SpineResourceConstants {
  static const String SPINE_ATS = "atlas.txt";
  static const String SPINE_SKL = "skel.bytes";
}

class PreLessonPreviewHeaderModel {
  String? icon;  // 左侧图标
  String? title;  //标题
  String? route;  // 路由地址

  // 埋点所需要的数据
  int? activityId;
  int? status;
  CourseLessonInfo? lessonInfo;
}

class LessonInProgressModel {
  int completeLessonCount = 0;  // 当前完课数
  int preCompleteLessonCount = 0;  // 上一次完课数
  int lessonCount = 0;   // 总课数
  int? medalId;  // 奖章id（已获得时非空）
  int? advanceRedirectPageId;  // 预告期落地页id
  int? activityRedirectPageId;  // 进行期落地页id
  bool isMultistage = false;  // 是否是对阶段活动类型（默认是单一活动）
  double finishNodeProgress = 0.0;  // 已完成阶段的进度值
  double targetNodeProgress = 0.0;  // 目标阶段的的总进度值
  double progress = 0.0;  // 当前未完成阶段的进度值
  String? message;  // 描述内容
  String? route;  // 路由地址
  String? progressStartColor;  // 进度条渐变开始颜色
  String? progressEndColor;  // 进度条渐变结束颜色
  String? audioUrl; // 提示音频Url（未获得奖章时的提示音频信息）
  LessonInProgressGiftModel? currentGiftModel;  // 当前获得阶段奖励对象
  LessonInProgressGiftModel? nextGiftModel;  // 下一个阶段奖励对象
  LessonInProgressGiftModel? preFinishGiftModel;  // 上一个已完成阶段奖励对象（节点数目>4时有用）
  LessonInProgressGiftModel? allFinishGiftModel;  // 最终阶段奖励对象
  List<LessonInProgressGiftModel> giftList = [];  // 阶段奖励列表
  PromoteLessonFinishSpineResourceInfo? spineResourceInfo;  // 动效资源对象
}

class LessonInProgressGiftModel {
  int index = 0;  // 阶段奖励完索引
  int currentValue = 0;  // 当前值
  int lessonCount = 0;   // 总课数
  int completeLessonCount = 0;  // 完课数
  int? type;
  int? activityId; // 活动Id
  int? taskId;  // 任务Id
  int? medalId;  // 奖章id（当类型是奖章时，分享时需要用到）
  bool isGet = false;  // 是否已经获得
  String? giftSpineUrl;  // 阶段奖励的Spine资源地址
  List<LessonInProgressGiftRewardModel> rewardList = [];  // 奖励列表(一个阶段可以有多个奖励)
  PromoteLessonFinishSpineResourceVo? spineResourceVo;  // 阶段奖励动效资源对象
}

class LessonInProgressGiftRewardModel {
  int? type;  // 奖励类型
  String? title;  // 阶段奖励标题
  String? subTitle;  // 阶段奖励副标题
  String? route;  // 阶段奖励路由
  String? animationName;  // 奖励动效名称(如果找不到,则播放full动画)
  String? audioDubbingName;  // 奖励配音资源名称
  String? audioSoundEffectsName;  // 奖励音效资源名称
  int? activityId; // 活动Id
  int? taskId;  // 任务Id
  int? medalId;  // 奖章id（当类型是奖章时，分享时需要用到）
  bool isGet = false; // 是否已经获得
}

class LessonInProgressGiftSpineItemSizeVo {
  double width = 0.0;
  double height = 0.0;
  double offset_x = 0.0;
  double offset_y = 0.0;
}

class ConfigSize {
  static double get cardHeight => 95.rdp; // 活动卡片整体高度
  static double get borderRadius => 24.rdp; // 活动卡片圆角大小
  static double get lrSpace => 20.rdp; // 活动卡片左右间距
  static double get contentPadding => 14.rdp; // 活动卡片子内容左右间距
  static double get madelSpineItemWidth => 80.rdp;  // 奖章动效资源的宽度
  static double get madelSpineItemHeight => 80.rdp; // 奖章动效资源的高度
  static double get nodeItemWidth => 32.rdp; // 节点的宽度
  static double get nodeItemHeight => 32.rdp; // 节点的高度
  static double get nodeItemFinishBgWidth => 27.rdp; // 节点完成背景圆宽度
  static double get nodeItemFinishBgHeight => 27.rdp; // 节点完成背景圆高度
  static double get progressItemHeight => 20.rdp; // 进度条的高度
  static double get madelPreViewCardTopSpace => 25.rdp;  // 奖励预告气泡与卡片顶部的差值
  static double get madelPreViewBubbleWidth => 78.rdp;  // 奖励预告气泡宽度
  static double get madelPreViewBubbleHeight => 87.rdp; // 奖励预告气泡高度
  static double get madelPreViewBubbleSpineWidth => 60.rdp;  // 奖励预告气泡中动效宽度
  static double get madelPreViewBubbleSpineHeight => 60.rdp; // 奖励预告气泡中动效高度
  static double get madelGetPageBtnHeight => 40.rdp;  // 奖励获得页按钮的高度
  static double get landSpaceMadelGetPageBtnWidth => 280.rdp;  // 奖励获得页按钮横版时的宽度
  static double get landSpaceContentWidth => 780.rdp; // 横版时主体内容的宽度
  static double get landSpaceProgressLeftSpace => 80.rdp; // 横版时进度条左侧距离主体内容左边缘的距离
  static double get bgSpineUIWidth => 1920; // UI设计的背景动效宽高是1920X1920，布局时需要对它进行缩放
  static double get medalSpineUIWidth => 500; // UI设计的奖章动效宽高是500X500，布局时需要对它进行缩放
  static double get medalItemSpinePhoneUIHeight => 280.rdp; // UI设计的奖章动效在手机上的宽高
  static double get medalItemSpineIPadUIHeight => 375.rdp; // UI设计的奖章动效在手机上的宽高
  static double get medalItemSpineTitleSpace => 30.rdp; // UI设计的奖章动效在手机上的宽高
  static double get giftGitPageButtonBottomSpace => 30.rdp; // 最终奖励获得按钮组距离底部的距离
  static double get medalGetPageMedalCenterSpace => 70.rdp; // 最终奖励获得奖章以及背景Spine中心点和屏幕中心点点偏移值
  static double get starFlyDySpace => 60.rdp;  // 飞星动画执行后的差值
  static double get progressBorderWidth => 2.rdp; // 进度条的边框宽度
}

// 动画状态
enum AnimationType {
  none,  // 无动画
  starDismiss, // 课时星星消失动画
  starFly,  // 飞星动画
  progress,  // 进度条动画(卡片缩放，进度条变化，奖章摇晃)
  madelPreview,  // 奖章预告气泡
  nodeMadelGet,  // 阶段奖章获得
  badgeShowInCenter,  // 获得奖章
  badgePage,  // 奖章获得页
}

// 动画执行弹窗类型
enum AnimationDialogType {
  none,  // 无弹窗
  hasActivityCard,  // 存在顶部活动卡片
  animationEnd,  // 收集物动画执行完成
  request,  // 询问是否应该显示弹窗
  updateCompleteCountNoAnimation,  // 没有下载好飞星动画时的主动更新卡片数据操作
}

enum LifecycleType {
  resumed,  // 可见
  paused,  // 不可见
}