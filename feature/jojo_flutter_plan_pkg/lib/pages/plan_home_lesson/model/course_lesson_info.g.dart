// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_lesson_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseLessonInfo _$$_CourseLessonInfoFromJson(Map<String, dynamic> json) =>
    _$_CourseLessonInfo(
      classId: json['classId'] as int?,
      courseImg: json['courseImg'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseLabel: json['courseLabel'] as String?,
      userPageCourseLabel: json['userPageCourseLabel'] as String?,
      subjectColor: json['subjectColor'] as String?,
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      courseSegment: json['courseSegment'] as String?,
      startClassDateDesc: json['startClassDateDesc'] as String?,
      courseType: json['courseType'] as int?,
      classStatus: json['classStatus'] as int?,
      startClassTime: json['startClassTime'] as int?,
      activities: json['activities'] == null
          ? null
          : ClassActivitiesVo.fromJson(
              json['activities'] as Map<String, dynamic>),
      timetableNodeList: (json['timetableNodeList'] as List<dynamic>?)
          ?.map((e) => TimetableNodeList.fromJson(e as Map<String, dynamic>))
          .toList(),
      classFunctionList: (json['classFunctionList'] as List<dynamic>?)
          ?.map((e) => ClassFunctionList.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseStartPrepareVo: json['courseStartPrepareVo'] == null
          ? null
          : CourseStartPrepareVo.fromJson(
              json['courseStartPrepareVo'] as Map<String, dynamic>),
      renewalTimetableVo: json['renewalTimetableVo'] == null
          ? null
          : RenewalTimetableVo.fromJson(
              json['renewalTimetableVo'] as Map<String, dynamic>),
      userGiftCourseVoList: (json['userGiftCourseVoList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : UserGifCourse.fromJson(e as Map<String, dynamic>))
          .toList(),
      buyCourseVo: json['buyCourseVo'] == null
          ? null
          : BuyCourseVo.fromJson(json['buyCourseVo'] as Map<String, dynamic>),
      addTeacherVo: json['addTeacherVo'] == null
          ? null
          : AddTeacherVo.fromJson(json['addTeacherVo'] as Map<String, dynamic>),
      friendCardInfo: json['friendCardInfo'] == null
          ? null
          : FriendCardInfo.fromJson(
              json['friendCardInfo'] as Map<String, dynamic>),
      pageCount: json['pageCount'] as int?,
      pageSize: json['pageSize'] as int?,
      allSkuEntranceVo: json['allSkuEntranceVo'] == null
          ? null
          : AllProductsEntranceClass.fromJson(
              json['allSkuEntranceVo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseLessonInfoToJson(_$_CourseLessonInfo instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'courseImg': instance.courseImg,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'courseLabel': instance.courseLabel,
      'userPageCourseLabel': instance.userPageCourseLabel,
      'subjectColor': instance.subjectColor,
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'courseSegment': instance.courseSegment,
      'startClassDateDesc': instance.startClassDateDesc,
      'courseType': instance.courseType,
      'classStatus': instance.classStatus,
      'startClassTime': instance.startClassTime,
      'activities': instance.activities,
      'timetableNodeList': instance.timetableNodeList,
      'classFunctionList': instance.classFunctionList,
      'courseStartPrepareVo': instance.courseStartPrepareVo,
      'renewalTimetableVo': instance.renewalTimetableVo,
      'userGiftCourseVoList': instance.userGiftCourseVoList,
      'buyCourseVo': instance.buyCourseVo,
      'addTeacherVo': instance.addTeacherVo,
      'friendCardInfo': instance.friendCardInfo,
      'pageCount': instance.pageCount,
      'pageSize': instance.pageSize,
      'allSkuEntranceVo': instance.allSkuEntranceVo,
    };

_$_AllProductsEntranceClass _$$_AllProductsEntranceClassFromJson(
        Map<String, dynamic> json) =>
    _$_AllProductsEntranceClass(
      title: json['title'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_AllProductsEntranceClassToJson(
        _$_AllProductsEntranceClass instance) =>
    <String, dynamic>{
      'title': instance.title,
      'route': instance.route,
    };

_$_ClassActivitiesVo _$$_ClassActivitiesVoFromJson(Map<String, dynamic> json) =>
    _$_ClassActivitiesVo(
      activityId: json['activityId'] as int?,
      advanceStartTime: json['advanceStartTime'] as int?,
      advanceEndTime: json['advanceEndTime'] as int?,
      activityStartTime: json['activityStartTime'] as int?,
      activityEndTime: json['activityEndTime'] as int?,
      status: json['status'] as int?,
      hasLesson: json['hasLesson'] as int?,
      nodeRewardVoice: json['nodeRewardVoice'] as String?,
      postIcon: json['postIcon'] as String?,
      themeRes: json['themeRes'] == null
          ? null
          : ClassActivitiesThemeResVo.fromJson(
              json['themeRes'] as Map<String, dynamic>),
      taskVo: (json['taskVo'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      madelGetPopup: json['madelGetPopup'] == null
          ? null
          : ClassActivitiesPopupVo.fromJson(
              json['madelGetPopup'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ClassActivitiesVoToJson(
        _$_ClassActivitiesVo instance) =>
    <String, dynamic>{
      'activityId': instance.activityId,
      'advanceStartTime': instance.advanceStartTime,
      'advanceEndTime': instance.advanceEndTime,
      'activityStartTime': instance.activityStartTime,
      'activityEndTime': instance.activityEndTime,
      'status': instance.status,
      'hasLesson': instance.hasLesson,
      'nodeRewardVoice': instance.nodeRewardVoice,
      'postIcon': instance.postIcon,
      'themeRes': instance.themeRes,
      'taskVo': instance.taskVo,
      'madelGetPopup': instance.madelGetPopup,
    };

_$_ClassActivitiesThemeResVo _$$_ClassActivitiesThemeResVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesThemeResVo(
      themeHead: json['themeHead'] == null
          ? null
          : ClassActivitiesthemeHeadVo.fromJson(
              json['themeHead'] as Map<String, dynamic>),
      themeActivityCard: json['themeActivityCard'] == null
          ? null
          : ClassActivitiesThemeActivityCardVo.fromJson(
              json['themeActivityCard'] as Map<String, dynamic>),
      themeActivityPopup: json['themeActivityPopup'] == null
          ? null
          : ClassActivitiesPopupVo.fromJson(
              json['themeActivityPopup'] as Map<String, dynamic>),
      themeLessonCard: json['themeLessonCard'] == null
          ? null
          : ClassActivitiesThemeLessonCardVo.fromJson(
              json['themeLessonCard'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ClassActivitiesThemeResVoToJson(
        _$_ClassActivitiesThemeResVo instance) =>
    <String, dynamic>{
      'themeHead': instance.themeHead,
      'themeActivityCard': instance.themeActivityCard,
      'themeActivityPopup': instance.themeActivityPopup,
      'themeLessonCard': instance.themeLessonCard,
    };

_$_ClassActivitiesPopupVo _$$_ClassActivitiesPopupVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesPopupVo(
      mainTitle: json['mainTitle'] as String?,
      subTitle: json['subTitle'] as String?,
      headImg: json['headImg'] as String?,
      btnTitle: json['btnTitle'] as String?,
      guidingVideo: json['guidingVideo'] as String?,
      guidingAudio: json['guidingAudio'] as String?,
    );

Map<String, dynamic> _$$_ClassActivitiesPopupVoToJson(
        _$_ClassActivitiesPopupVo instance) =>
    <String, dynamic>{
      'mainTitle': instance.mainTitle,
      'subTitle': instance.subTitle,
      'headImg': instance.headImg,
      'btnTitle': instance.btnTitle,
      'guidingVideo': instance.guidingVideo,
      'guidingAudio': instance.guidingAudio,
    };

_$_ClassActivitiesthemeHeadVo _$$_ClassActivitiesthemeHeadVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesthemeHeadVo(
      phoneBackgroundImg: json['phoneBackgroundImg'] as String?,
      padBackgroundImg: json['padBackgroundImg'] as String?,
    );

Map<String, dynamic> _$$_ClassActivitiesthemeHeadVoToJson(
        _$_ClassActivitiesthemeHeadVo instance) =>
    <String, dynamic>{
      'phoneBackgroundImg': instance.phoneBackgroundImg,
      'padBackgroundImg': instance.padBackgroundImg,
    };

_$_ClassActivitiesThemeLessonCardVo
    _$$_ClassActivitiesThemeLessonCardVoFromJson(Map<String, dynamic> json) =>
        _$_ClassActivitiesThemeLessonCardVo(
          collectRes: json['collectRes'] as String?,
        );

Map<String, dynamic> _$$_ClassActivitiesThemeLessonCardVoToJson(
        _$_ClassActivitiesThemeLessonCardVo instance) =>
    <String, dynamic>{
      'collectRes': instance.collectRes,
    };

_$_ClassActivitiesThemeActivityCardVo
    _$$_ClassActivitiesThemeActivityCardVoFromJson(Map<String, dynamic> json) =>
        _$_ClassActivitiesThemeActivityCardVo(
          icon: json['icon'] as String?,
          progressRes: json['progressRes'] as String?,
          backgroundRes: json['backgroundRes'] as String?,
          progressHeadColor: json['progressHeadColor'] as String?,
          progressTailColor: json['progressTailColor'] as String?,
          advanceText: json['advanceText'] as String?,
          advancePage: json['advancePage'] as String?,
          activityText: json['activityText'] as String?,
          activityPage: json['activityPage'] as String?,
          advanceRedirectPageId: json['advanceRedirectPageId'] as int?,
          activityRedirectPageId: json['activityRedirectPageId'] as int?,
        );

Map<String, dynamic> _$$_ClassActivitiesThemeActivityCardVoToJson(
        _$_ClassActivitiesThemeActivityCardVo instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'progressRes': instance.progressRes,
      'backgroundRes': instance.backgroundRes,
      'progressHeadColor': instance.progressHeadColor,
      'progressTailColor': instance.progressTailColor,
      'advanceText': instance.advanceText,
      'advancePage': instance.advancePage,
      'activityText': instance.activityText,
      'activityPage': instance.activityPage,
      'advanceRedirectPageId': instance.advanceRedirectPageId,
      'activityRedirectPageId': instance.activityRedirectPageId,
    };

_$_ClassActivitiesTaskVo _$$_ClassActivitiesTaskVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskVo(
      taskId: json['taskId'] as int?,
      taskType: json['taskType'] as String?,
      name: json['name'] as String?,
      conditions: (json['conditions'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskConditionsVo.fromJson(
                  e as Map<String, dynamic>))
          .toList(),
      rewards: (json['rewards'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskRewardsVo.fromJson(
                  e as Map<String, dynamic>))
          .toList(),
      taskExtendResource: json['taskExtendResource'] == null
          ? null
          : ClassActivitiesTaskExtendResourceVo.fromJson(
              json['taskExtendResource'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ClassActivitiesTaskVoToJson(
        _$_ClassActivitiesTaskVo instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'taskType': instance.taskType,
      'name': instance.name,
      'conditions': instance.conditions,
      'rewards': instance.rewards,
      'taskExtendResource': instance.taskExtendResource,
    };

_$_ClassActivitiesTaskConditionsVo _$$_ClassActivitiesTaskConditionsVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskConditionsVo(
      currentValue: json['currentValue'] as int?,
      targetValue: json['targetValue'] as int?,
    );

Map<String, dynamic> _$$_ClassActivitiesTaskConditionsVoToJson(
        _$_ClassActivitiesTaskConditionsVo instance) =>
    <String, dynamic>{
      'currentValue': instance.currentValue,
      'targetValue': instance.targetValue,
    };

_$_ClassActivitiesTaskRewardsVo _$$_ClassActivitiesTaskRewardsVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskRewardsVo(
      rewardId: json['rewardId'] as int?,
      isGet: json['isGet'] as int?,
      isPopup: json['isPopup'] as int?,
      type: json['type'] as int?,
      lockImage: json['lockImage'] as String?,
      unlockImage: json['unlockImage'] as String?,
      resourceFlutter: json['resourceFlutter'] as String?,
      bizId: json['bizId'] as String?,
      rewardBizUrl: json['rewardBizUrl'] as String?,
    );

Map<String, dynamic> _$$_ClassActivitiesTaskRewardsVoToJson(
        _$_ClassActivitiesTaskRewardsVo instance) =>
    <String, dynamic>{
      'rewardId': instance.rewardId,
      'isGet': instance.isGet,
      'isPopup': instance.isPopup,
      'type': instance.type,
      'lockImage': instance.lockImage,
      'unlockImage': instance.unlockImage,
      'resourceFlutter': instance.resourceFlutter,
      'bizId': instance.bizId,
      'rewardBizUrl': instance.rewardBizUrl,
    };

_$_ClassActivitiesTaskExtendResourceVo
    _$$_ClassActivitiesTaskExtendResourceVoFromJson(
            Map<String, dynamic> json) =>
        _$_ClassActivitiesTaskExtendResourceVo(
          rewardDisplayUrl: json['rewardDisplayUrl'] as String?,
          mainText: json['mainText'] as String?,
          subText: json['subText'] as String?,
          rewardNodeTexts: (json['rewardNodeTexts'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : ClassActivitiesTaskRewardNodeTextVo.fromJson(
                      e as Map<String, dynamic>))
              .toList(),
        );

Map<String, dynamic> _$$_ClassActivitiesTaskExtendResourceVoToJson(
        _$_ClassActivitiesTaskExtendResourceVo instance) =>
    <String, dynamic>{
      'rewardDisplayUrl': instance.rewardDisplayUrl,
      'mainText': instance.mainText,
      'subText': instance.subText,
      'rewardNodeTexts': instance.rewardNodeTexts,
    };

_$_ClassActivitiesTaskRewardNodeTextVo
    _$$_ClassActivitiesTaskRewardNodeTextVoFromJson(
            Map<String, dynamic> json) =>
        _$_ClassActivitiesTaskRewardNodeTextVo(
          rewardType: json['rewardType'] as int?,
          mainText: json['mainText'] as String?,
          subText: json['subText'] as String?,
        );

Map<String, dynamic> _$$_ClassActivitiesTaskRewardNodeTextVoToJson(
        _$_ClassActivitiesTaskRewardNodeTextVo instance) =>
    <String, dynamic>{
      'rewardType': instance.rewardType,
      'mainText': instance.mainText,
      'subText': instance.subText,
    };

_$_AddTeacherVo _$$_AddTeacherVoFromJson(Map<String, dynamic> json) =>
    _$_AddTeacherVo(
      bannerImage: json['bannerImage'] as String?,
      route: json['route'] as String?,
      customState: json['customState'] as String?,
    );

Map<String, dynamic> _$$_AddTeacherVoToJson(_$_AddTeacherVo instance) =>
    <String, dynamic>{
      'bannerImage': instance.bannerImage,
      'route': instance.route,
      'customState': instance.customState,
    };

_$_BuyCourseVo _$$_BuyCourseVoFromJson(Map<String, dynamic> json) =>
    _$_BuyCourseVo(
      buyCourseSegmentCode: json['buyCourseSegmentCode'] as int?,
      buttonText: json['buttonText'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_BuyCourseVoToJson(_$_BuyCourseVo instance) =>
    <String, dynamic>{
      'buyCourseSegmentCode': instance.buyCourseSegmentCode,
      'buttonText': instance.buttonText,
      'route': instance.route,
    };

_$_CourseStartPrepareVo _$$_CourseStartPrepareVoFromJson(
        Map<String, dynamic> json) =>
    _$_CourseStartPrepareVo(
      courseImg: json['courseImg'] as String?,
      title: json['title'] as String?,
      startClassDateDesc: json['startClassDateDesc'] as String?,
      prepareNodeList: (json['prepareNodeList'] as List<dynamic>?)
          ?.map((e) => TimetableNodeList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseStartPrepareVoToJson(
        _$_CourseStartPrepareVo instance) =>
    <String, dynamic>{
      'courseImg': instance.courseImg,
      'title': instance.title,
      'startClassDateDesc': instance.startClassDateDesc,
      'prepareNodeList': instance.prepareNodeList,
    };

_$_FriendCardInfo _$$_FriendCardInfoFromJson(Map<String, dynamic> json) =>
    _$_FriendCardInfo(
      icon: json['icon'] as String?,
      title: json['title'] as String?,
      validPeriod: json['validPeriod'] as int?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_FriendCardInfoToJson(_$_FriendCardInfo instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'title': instance.title,
      'validPeriod': instance.validPeriod,
      'jumpRoute': instance.jumpRoute,
    };

_$_ClassFunctionList _$$_ClassFunctionListFromJson(Map<String, dynamic> json) =>
    _$_ClassFunctionList(
      functionType: json['functionType'] as int?,
      title: json['title'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      toastList: (json['toastList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$_ClassFunctionListToJson(
        _$_ClassFunctionList instance) =>
    <String, dynamic>{
      'functionType': instance.functionType,
      'title': instance.title,
      'icon': instance.icon,
      'route': instance.route,
      'toastList': instance.toastList,
    };

_$_TimetableNodeList _$$_TimetableNodeListFromJson(Map<String, dynamic> json) =>
    _$_TimetableNodeList(
      showDateTime: json['showDateTime'] as int?,
      showDate: json['showDate'] as String?,
      focus: json['focus'] as bool?,
      today: json['today'] as bool?,
      gif: json['gif'] as bool?,
      position: json['position'] as bool?,
      unfinishLottieIcon: json['unfinishLottieIcon'] as String?,
      nodeStatus: json['nodeStatus'] as int?,
      nodeType: json['nodeType'] as int?,
      title: json['title'] as String?,
      route: json['route'] as String?,
      toast: json['toast'] as String?,
      nodeId: json['nodeId'] as int?,
      lessonId: json['lessonId'] as int?,
      lessonOrder: json['lessonOrder'] as int?,
      segmentName: json['segmentName'] as String?,
      segmentId: json['segmentId'] as int?,
      weekName: json['weekName'] as String?,
      weekId: json['weekId'] as int?,
      collectStatus: json['collectStatus'] as int?,
      introductory: json['introductory'] as String?,
      courseChildType: json['courseChildType'] as int?,
      studyDesc: json['studyDesc'] as String?,
      studyStatus: json['studyStatus'] as int?,
      studyTips: json['studyTips'] as String?,
      studyTipsVoice: json['studyTipsVoice'] as String?,
      lockIcon: json['lockIcon'] as String?,
      unfinishIcon: json['unfinishIcon'] as String?,
      finishIcon: json['finishIcon'] as String?,
      lockVoice: json['lockVoice'] as String?,
      unfinishVoice: json['unfinishVoice'] as String?,
      finishVoice: json['finishVoice'] as String?,
      lessonGrade: json['lessonGrade'] as int?,
      lessonGradeResourceUrl: json['lessonGradeResourceUrl'] as String?,
      unFinishedNum: json['unFinishedNum'] as int?,
      reviewRoute: json['reviewRoute'] as String?,
      makeupUIExpired: json['makeupUIExpired'] as bool?,
      lessonSubjectType: json['lessonSubjectType'] as int?,
      subjectNodeIcon: json['subjectNodeIcon'] as String?,
      rewardVoList: (json['rewardVoList'] as List<dynamic>?)
          ?.map((e) => RewardVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      popupInfo: json['popupInfo'] == null
          ? null
          : PopupInfo.fromJson(json['popupInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TimetableNodeListToJson(
        _$_TimetableNodeList instance) =>
    <String, dynamic>{
      'showDateTime': instance.showDateTime,
      'showDate': instance.showDate,
      'focus': instance.focus,
      'today': instance.today,
      'gif': instance.gif,
      'position': instance.position,
      'unfinishLottieIcon': instance.unfinishLottieIcon,
      'nodeStatus': instance.nodeStatus,
      'nodeType': instance.nodeType,
      'title': instance.title,
      'route': instance.route,
      'toast': instance.toast,
      'nodeId': instance.nodeId,
      'lessonId': instance.lessonId,
      'lessonOrder': instance.lessonOrder,
      'segmentName': instance.segmentName,
      'segmentId': instance.segmentId,
      'weekName': instance.weekName,
      'weekId': instance.weekId,
      'collectStatus': instance.collectStatus,
      'introductory': instance.introductory,
      'courseChildType': instance.courseChildType,
      'studyDesc': instance.studyDesc,
      'studyStatus': instance.studyStatus,
      'studyTips': instance.studyTips,
      'studyTipsVoice': instance.studyTipsVoice,
      'lockIcon': instance.lockIcon,
      'unfinishIcon': instance.unfinishIcon,
      'finishIcon': instance.finishIcon,
      'lockVoice': instance.lockVoice,
      'unfinishVoice': instance.unfinishVoice,
      'finishVoice': instance.finishVoice,
      'lessonGrade': instance.lessonGrade,
      'lessonGradeResourceUrl': instance.lessonGradeResourceUrl,
      'unFinishedNum': instance.unFinishedNum,
      'reviewRoute': instance.reviewRoute,
      'makeupUIExpired': instance.makeupUIExpired,
      'lessonSubjectType': instance.lessonSubjectType,
      'subjectNodeIcon': instance.subjectNodeIcon,
      'rewardVoList': instance.rewardVoList,
      'popupInfo': instance.popupInfo,
    };

_$_RenewalTimetableVo _$$_RenewalTimetableVoFromJson(
        Map<String, dynamic> json) =>
    _$_RenewalTimetableVo(
      userCourseStatus: json['userCourseStatus'] as String?,
      buyCourseDesc: json['buyCourseDesc'] as String?,
      route: json['route'] as String?,
      renewalTimetableList: (json['renewalTimetableList'] as List<dynamic>?)
          ?.map((e) => TimetableNodeList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_RenewalTimetableVoToJson(
        _$_RenewalTimetableVo instance) =>
    <String, dynamic>{
      'userCourseStatus': instance.userCourseStatus,
      'buyCourseDesc': instance.buyCourseDesc,
      'route': instance.route,
      'renewalTimetableList': instance.renewalTimetableList,
    };

_$_PopupInfo _$$_PopupInfoFromJson(Map<String, dynamic> json) => _$_PopupInfo(
      popupTitle: json['popupTitle'] as String?,
      introduceImageList: (json['introduceImageList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      buttonRoute: json['buttonRoute'] as String?,
      buttonText: json['buttonText'] as String?,
      closedText: json['closedText'] as String?,
      introduce: json['introduce'] as String?,
      actionTitle: json['actionTitle'] as String?,
      actionTip: json['actionTip'] as String?,
      hasSetting: json['hasSetting'] as int?,
      options: (json['options'] as List<dynamic>?)
          ?.map((e) => Option.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PopupInfoToJson(_$_PopupInfo instance) =>
    <String, dynamic>{
      'popupTitle': instance.popupTitle,
      'introduceImageList': instance.introduceImageList,
      'buttonRoute': instance.buttonRoute,
      'buttonText': instance.buttonText,
      'closedText': instance.closedText,
      'introduce': instance.introduce,
      'actionTitle': instance.actionTitle,
      'actionTip': instance.actionTip,
      'hasSetting': instance.hasSetting,
      'options': instance.options,
    };

_$_Option _$$_OptionFromJson(Map<String, dynamic> json) => _$_Option(
      navTitle: json['navTitle'] as String?,
      navKey: json['navKey'] as int?,
      hasChoice: json['hasChoice'] as int?,
      contentImg: json['contentImg'] as String?,
    );

Map<String, dynamic> _$$_OptionToJson(_$_Option instance) => <String, dynamic>{
      'navTitle': instance.navTitle,
      'navKey': instance.navKey,
      'hasChoice': instance.hasChoice,
      'contentImg': instance.contentImg,
    };

_$_RewardVo _$$_RewardVoFromJson(Map<String, dynamic> json) => _$_RewardVo(
      rewardCount: json['rewardCount'] as int?,
      rewardType: json['rewardType'] as int?,
      actionType: json['actionType'] as int?,
      actionTargetId: json['actionTargetId'] as String?,
    );

Map<String, dynamic> _$$_RewardVoToJson(_$_RewardVo instance) =>
    <String, dynamic>{
      'rewardCount': instance.rewardCount,
      'rewardType': instance.rewardType,
      'actionType': instance.actionType,
      'actionTargetId': instance.actionTargetId,
    };
