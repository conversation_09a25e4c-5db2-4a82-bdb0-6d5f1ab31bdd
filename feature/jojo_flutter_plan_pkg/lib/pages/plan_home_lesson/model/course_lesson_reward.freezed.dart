// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_lesson_reward.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseLessonReward _$CourseLessonRewardFromJson(Map<String, dynamic> json) {
  return _CourseLessonReward.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonReward {
  List<Result>? get results => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonRewardCopyWith<CourseLessonReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonRewardCopyWith<$Res> {
  factory $CourseLessonRewardCopyWith(
          CourseLessonReward value, $Res Function(CourseLessonReward) then) =
      _$CourseLessonRewardCopyWithImpl<$Res, CourseLessonReward>;
  @useResult
  $Res call({List<Result>? results});
}

/// @nodoc
class _$CourseLessonRewardCopyWithImpl<$Res, $Val extends CourseLessonReward>
    implements $CourseLessonRewardCopyWith<$Res> {
  _$CourseLessonRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = freezed,
  }) {
    return _then(_value.copyWith(
      results: freezed == results
          ? _value.results
          : results // ignore: cast_nullable_to_non_nullable
              as List<Result>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonRewardCopyWith<$Res>
    implements $CourseLessonRewardCopyWith<$Res> {
  factory _$$_CourseLessonRewardCopyWith(_$_CourseLessonReward value,
          $Res Function(_$_CourseLessonReward) then) =
      __$$_CourseLessonRewardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Result>? results});
}

/// @nodoc
class __$$_CourseLessonRewardCopyWithImpl<$Res>
    extends _$CourseLessonRewardCopyWithImpl<$Res, _$_CourseLessonReward>
    implements _$$_CourseLessonRewardCopyWith<$Res> {
  __$$_CourseLessonRewardCopyWithImpl(
      _$_CourseLessonReward _value, $Res Function(_$_CourseLessonReward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = freezed,
  }) {
    return _then(_$_CourseLessonReward(
      results: freezed == results
          ? _value._results
          : results // ignore: cast_nullable_to_non_nullable
              as List<Result>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonReward implements _CourseLessonReward {
  const _$_CourseLessonReward({final List<Result>? results})
      : _results = results;

  factory _$_CourseLessonReward.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonRewardFromJson(json);

  final List<Result>? _results;
  @override
  List<Result>? get results {
    final value = _results;
    if (value == null) return null;
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseLessonReward(results: $results)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseLessonReward &&
            const DeepCollectionEquality().equals(other._results, _results));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_results));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonRewardCopyWith<_$_CourseLessonReward> get copyWith =>
      __$$_CourseLessonRewardCopyWithImpl<_$_CourseLessonReward>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonRewardToJson(
      this,
    );
  }
}

abstract class _CourseLessonReward implements CourseLessonReward {
  const factory _CourseLessonReward({final List<Result>? results}) =
      _$_CourseLessonReward;

  factory _CourseLessonReward.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonReward.fromJson;

  @override
  List<Result>? get results;
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonRewardCopyWith<_$_CourseLessonReward> get copyWith =>
      throw _privateConstructorUsedError;
}

Result _$ResultFromJson(Map<String, dynamic> json) {
  return _Result.fromJson(json);
}

/// @nodoc
mixin _$Result {
  String? get batchId => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ResultCopyWith<Result> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResultCopyWith<$Res> {
  factory $ResultCopyWith(Result value, $Res Function(Result) then) =
      _$ResultCopyWithImpl<$Res, Result>;
  @useResult
  $Res call({String? batchId, int? status});
}

/// @nodoc
class _$ResultCopyWithImpl<$Res, $Val extends Result>
    implements $ResultCopyWith<$Res> {
  _$ResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? batchId = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ResultCopyWith<$Res> implements $ResultCopyWith<$Res> {
  factory _$$_ResultCopyWith(_$_Result value, $Res Function(_$_Result) then) =
      __$$_ResultCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? batchId, int? status});
}

/// @nodoc
class __$$_ResultCopyWithImpl<$Res>
    extends _$ResultCopyWithImpl<$Res, _$_Result>
    implements _$$_ResultCopyWith<$Res> {
  __$$_ResultCopyWithImpl(_$_Result _value, $Res Function(_$_Result) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? batchId = freezed,
    Object? status = freezed,
  }) {
    return _then(_$_Result(
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Result implements _Result {
  const _$_Result({this.batchId, this.status});

  factory _$_Result.fromJson(Map<String, dynamic> json) =>
      _$$_ResultFromJson(json);

  @override
  final String? batchId;
  @override
  final int? status;

  @override
  String toString() {
    return 'Result(batchId: $batchId, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Result &&
            (identical(other.batchId, batchId) || other.batchId == batchId) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, batchId, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ResultCopyWith<_$_Result> get copyWith =>
      __$$_ResultCopyWithImpl<_$_Result>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ResultToJson(
      this,
    );
  }
}

abstract class _Result implements Result {
  const factory _Result({final String? batchId, final int? status}) = _$_Result;

  factory _Result.fromJson(Map<String, dynamic> json) = _$_Result.fromJson;

  @override
  String? get batchId;
  @override
  int? get status;
  @override
  @JsonKey(ignore: true)
  _$$_ResultCopyWith<_$_Result> get copyWith =>
      throw _privateConstructorUsedError;
}
