import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/view_models/my_partners_view_models.dart';

class MyPartnersState {
  PageStatus pageStatus;

  // 发现学伴数据
  DiscoverPartnersViewModel? discoverPartnersData;

  // 学伴数量（用于标题显示）
  int learningPartnersCount;

  // 当前选中的标签索引 (0: 动态, 1: 学伴)
  int selectedTabIndex;

  // 动态列表相关
  List<DynamicViewModel> dynamicsList;
  int nextDynamicsMinId;
  bool hasMoreDynamics;

  // 学伴列表相关
  List<PartnerViewModel> partnersList;
  int nextPartnersMinId;
  bool hasMorePartners;

  // 分页大小
  int pageSize;

  MyPartnersState({
    required this.pageStatus,
    this.discoverPartnersData,
    this.learningPartnersCount = 0,
    this.selectedTabIndex = 0,
    this.dynamicsList = const [],
    this.nextDynamicsMinId = 0,
    this.hasMoreDynamics = true,
    this.partnersList = const [],
    this.nextPartnersMinId = 0,
    this.hasMorePartners = true,
    this.pageSize = 20,
  });

  MyPartnersState copyWith({
    PageStatus? pageStatus,
    DiscoverPartnersViewModel? discoverPartnersData,
    int? learningPartnersCount,
    int? selectedTabIndex,
    List<DynamicViewModel>? dynamicsList,
    int? nextDynamicsMinId,
    bool? hasMoreDynamics,
    List<PartnerViewModel>? partnersList,
    int? nextPartnersMinId,
    bool? hasMorePartners,
    int? pageSize,
  }) {
    return MyPartnersState(
      pageStatus: pageStatus ?? this.pageStatus,
      discoverPartnersData: discoverPartnersData ?? this.discoverPartnersData,
      learningPartnersCount:
          learningPartnersCount ?? this.learningPartnersCount,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      dynamicsList: dynamicsList ?? this.dynamicsList,
      nextDynamicsMinId: nextDynamicsMinId ?? this.nextDynamicsMinId,
      hasMoreDynamics: hasMoreDynamics ?? this.hasMoreDynamics,
      partnersList: partnersList ?? this.partnersList,
      nextPartnersMinId: nextPartnersMinId ?? this.nextPartnersMinId,
      hasMorePartners: hasMorePartners ?? this.hasMorePartners,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}
