/// 动态互动类型枚举
enum DynamicActionType {
  /// 送花花
  flower('flower'),
  /// 戳一戳
  poke('poke');

  const DynamicActionType(this.value);

  final String value;

  static DynamicActionType? fromValue(String value) {
    for (DynamicActionType type in DynamicActionType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  String get displayName {
    switch (this) {
      case DynamicActionType.flower:
        return '送花花';
      case DynamicActionType.poke:
        return '戳一戳';
    }
  }
}
