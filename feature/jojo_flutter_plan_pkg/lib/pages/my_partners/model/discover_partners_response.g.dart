// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_partners_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_DiscoverPartnersData _$$_DiscoverPartnersDataFromJson(
        Map<String, dynamic> json) =>
    _$_DiscoverPartnersData(
      partners: (json['partners'] as List<dynamic>?)
          ?.map((e) => DiscoverPartner.fromJson(e as Map<String, dynamic>))
          .toList(),
      jumpRoute: json['jumpRoute'] as String?,
      title: json['title'] as String?,
      viewMore: json['viewMore'] as String?,
    );

Map<String, dynamic> _$$_DiscoverPartnersDataToJson(
        _$_DiscoverPartnersData instance) =>
    <String, dynamic>{
      'partners': instance.partners,
      'jumpRoute': instance.jumpRoute,
      'title': instance.title,
      'viewMore': instance.viewMore,
    };

_$_DiscoverPartner _$$_DiscoverPartnerFromJson(Map<String, dynamic> json) =>
    _$_DiscoverPartner(
      nickName: json['nickName'] as String?,
      continuousDays: json['continuousDays'] as int?,
      studyDays: json['studyDays'] as int?,
      img: json['img'] as String?,
      url: json['url'] as String?,
      partnerId: json['partnerId'] as int?,
    );

Map<String, dynamic> _$$_DiscoverPartnerToJson(_$_DiscoverPartner instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'continuousDays': instance.continuousDays,
      'studyDays': instance.studyDays,
      'img': instance.img,
      'url': instance.url,
      'partnerId': instance.partnerId,
    };
