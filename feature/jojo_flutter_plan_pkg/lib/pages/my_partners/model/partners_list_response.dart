import 'package:freezed_annotation/freezed_annotation.dart';

part 'partners_list_response.freezed.dart';
part 'partners_list_response.g.dart';

@freezed
class PartnersListData with _$PartnersListData {
  const factory PartnersListData({
    List<PartnerItem>? partners,
    int? minId,
    int? size,
  }) = _PartnersListData;

  factory PartnersListData.fromJson(Map<String, dynamic> json) =>
      _$PartnersListDataFromJson(json);
}

@freezed
class PartnerItem with _$PartnerItem {
  const factory PartnerItem({
    String? nickName,
    String? img,
    String? url,
    int? partnerId,
  }) = _PartnerItem;

  factory PartnerItem.fromJson(Map<String, dynamic> json) =>
      _$PartnerItemFromJson(json);
}
