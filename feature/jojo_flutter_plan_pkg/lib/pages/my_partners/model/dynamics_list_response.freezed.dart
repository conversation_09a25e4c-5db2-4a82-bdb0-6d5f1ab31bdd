// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dynamics_list_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

DynamicsListData _$DynamicsListDataFromJson(Map<String, dynamic> json) {
  return _DynamicsListData.fromJson(json);
}

/// @nodoc
mixin _$DynamicsListData {
  List<DynamicItem>? get dynamics => throw _privateConstructorUsedError;
  int? get minId => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DynamicsListDataCopyWith<DynamicsListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DynamicsListDataCopyWith<$Res> {
  factory $DynamicsListDataCopyWith(
          DynamicsListData value, $Res Function(DynamicsListData) then) =
      _$DynamicsListDataCopyWithImpl<$Res, DynamicsListData>;
  @useResult
  $Res call({List<DynamicItem>? dynamics, int? minId, int? size});
}

/// @nodoc
class _$DynamicsListDataCopyWithImpl<$Res, $Val extends DynamicsListData>
    implements $DynamicsListDataCopyWith<$Res> {
  _$DynamicsListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dynamics = freezed,
    Object? minId = freezed,
    Object? size = freezed,
  }) {
    return _then(_value.copyWith(
      dynamics: freezed == dynamics
          ? _value.dynamics
          : dynamics // ignore: cast_nullable_to_non_nullable
              as List<DynamicItem>?,
      minId: freezed == minId
          ? _value.minId
          : minId // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DynamicsListDataCopyWith<$Res>
    implements $DynamicsListDataCopyWith<$Res> {
  factory _$$_DynamicsListDataCopyWith(
          _$_DynamicsListData value, $Res Function(_$_DynamicsListData) then) =
      __$$_DynamicsListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<DynamicItem>? dynamics, int? minId, int? size});
}

/// @nodoc
class __$$_DynamicsListDataCopyWithImpl<$Res>
    extends _$DynamicsListDataCopyWithImpl<$Res, _$_DynamicsListData>
    implements _$$_DynamicsListDataCopyWith<$Res> {
  __$$_DynamicsListDataCopyWithImpl(
      _$_DynamicsListData _value, $Res Function(_$_DynamicsListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dynamics = freezed,
    Object? minId = freezed,
    Object? size = freezed,
  }) {
    return _then(_$_DynamicsListData(
      dynamics: freezed == dynamics
          ? _value._dynamics
          : dynamics // ignore: cast_nullable_to_non_nullable
              as List<DynamicItem>?,
      minId: freezed == minId
          ? _value.minId
          : minId // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DynamicsListData implements _DynamicsListData {
  const _$_DynamicsListData(
      {final List<DynamicItem>? dynamics, this.minId, this.size})
      : _dynamics = dynamics;

  factory _$_DynamicsListData.fromJson(Map<String, dynamic> json) =>
      _$$_DynamicsListDataFromJson(json);

  final List<DynamicItem>? _dynamics;
  @override
  List<DynamicItem>? get dynamics {
    final value = _dynamics;
    if (value == null) return null;
    if (_dynamics is EqualUnmodifiableListView) return _dynamics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? minId;
  @override
  final int? size;

  @override
  String toString() {
    return 'DynamicsListData(dynamics: $dynamics, minId: $minId, size: $size)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DynamicsListData &&
            const DeepCollectionEquality().equals(other._dynamics, _dynamics) &&
            (identical(other.minId, minId) || other.minId == minId) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_dynamics), minId, size);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DynamicsListDataCopyWith<_$_DynamicsListData> get copyWith =>
      __$$_DynamicsListDataCopyWithImpl<_$_DynamicsListData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DynamicsListDataToJson(
      this,
    );
  }
}

abstract class _DynamicsListData implements DynamicsListData {
  const factory _DynamicsListData(
      {final List<DynamicItem>? dynamics,
      final int? minId,
      final int? size}) = _$_DynamicsListData;

  factory _DynamicsListData.fromJson(Map<String, dynamic> json) =
      _$_DynamicsListData.fromJson;

  @override
  List<DynamicItem>? get dynamics;
  @override
  int? get minId;
  @override
  int? get size;
  @override
  @JsonKey(ignore: true)
  _$$_DynamicsListDataCopyWith<_$_DynamicsListData> get copyWith =>
      throw _privateConstructorUsedError;
}

DynamicItem _$DynamicItemFromJson(Map<String, dynamic> json) {
  return _DynamicItem.fromJson(json);
}

/// @nodoc
mixin _$DynamicItem {
  int? get type => throw _privateConstructorUsedError;
  String? get nickName => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  ActionState? get actionState => throw _privateConstructorUsedError;
  String? get timeDesc => throw _privateConstructorUsedError;
  int? get messageTime => throw _privateConstructorUsedError;
  int? get dynamicRelationId => throw _privateConstructorUsedError;
  int? get partnerId => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DynamicItemCopyWith<DynamicItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DynamicItemCopyWith<$Res> {
  factory $DynamicItemCopyWith(
          DynamicItem value, $Res Function(DynamicItem) then) =
      _$DynamicItemCopyWithImpl<$Res, DynamicItem>;
  @useResult
  $Res call(
      {int? type,
      String? nickName,
      String? img,
      String? url,
      String? content,
      ActionState? actionState,
      String? timeDesc,
      int? messageTime,
      int? dynamicRelationId,
      int? partnerId,
      String? subjectColor});

  $ActionStateCopyWith<$Res>? get actionState;
}

/// @nodoc
class _$DynamicItemCopyWithImpl<$Res, $Val extends DynamicItem>
    implements $DynamicItemCopyWith<$Res> {
  _$DynamicItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? nickName = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? content = freezed,
    Object? actionState = freezed,
    Object? timeDesc = freezed,
    Object? messageTime = freezed,
    Object? dynamicRelationId = freezed,
    Object? partnerId = freezed,
    Object? subjectColor = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      actionState: freezed == actionState
          ? _value.actionState
          : actionState // ignore: cast_nullable_to_non_nullable
              as ActionState?,
      timeDesc: freezed == timeDesc
          ? _value.timeDesc
          : timeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTime: freezed == messageTime
          ? _value.messageTime
          : messageTime // ignore: cast_nullable_to_non_nullable
              as int?,
      dynamicRelationId: freezed == dynamicRelationId
          ? _value.dynamicRelationId
          : dynamicRelationId // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ActionStateCopyWith<$Res>? get actionState {
    if (_value.actionState == null) {
      return null;
    }

    return $ActionStateCopyWith<$Res>(_value.actionState!, (value) {
      return _then(_value.copyWith(actionState: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_DynamicItemCopyWith<$Res>
    implements $DynamicItemCopyWith<$Res> {
  factory _$$_DynamicItemCopyWith(
          _$_DynamicItem value, $Res Function(_$_DynamicItem) then) =
      __$$_DynamicItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      String? nickName,
      String? img,
      String? url,
      String? content,
      ActionState? actionState,
      String? timeDesc,
      int? messageTime,
      int? dynamicRelationId,
      int? partnerId,
      String? subjectColor});

  @override
  $ActionStateCopyWith<$Res>? get actionState;
}

/// @nodoc
class __$$_DynamicItemCopyWithImpl<$Res>
    extends _$DynamicItemCopyWithImpl<$Res, _$_DynamicItem>
    implements _$$_DynamicItemCopyWith<$Res> {
  __$$_DynamicItemCopyWithImpl(
      _$_DynamicItem _value, $Res Function(_$_DynamicItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? nickName = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? content = freezed,
    Object? actionState = freezed,
    Object? timeDesc = freezed,
    Object? messageTime = freezed,
    Object? dynamicRelationId = freezed,
    Object? partnerId = freezed,
    Object? subjectColor = freezed,
  }) {
    return _then(_$_DynamicItem(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      actionState: freezed == actionState
          ? _value.actionState
          : actionState // ignore: cast_nullable_to_non_nullable
              as ActionState?,
      timeDesc: freezed == timeDesc
          ? _value.timeDesc
          : timeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTime: freezed == messageTime
          ? _value.messageTime
          : messageTime // ignore: cast_nullable_to_non_nullable
              as int?,
      dynamicRelationId: freezed == dynamicRelationId
          ? _value.dynamicRelationId
          : dynamicRelationId // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DynamicItem implements _DynamicItem {
  const _$_DynamicItem(
      {this.type,
      this.nickName,
      this.img,
      this.url,
      this.content,
      this.actionState,
      this.timeDesc,
      this.messageTime,
      this.dynamicRelationId,
      this.partnerId,
      this.subjectColor});

  factory _$_DynamicItem.fromJson(Map<String, dynamic> json) =>
      _$$_DynamicItemFromJson(json);

  @override
  final int? type;
  @override
  final String? nickName;
  @override
  final String? img;
  @override
  final String? url;
  @override
  final String? content;
  @override
  final ActionState? actionState;
  @override
  final String? timeDesc;
  @override
  final int? messageTime;
  @override
  final int? dynamicRelationId;
  @override
  final int? partnerId;
  @override
  final String? subjectColor;

  @override
  String toString() {
    return 'DynamicItem(type: $type, nickName: $nickName, img: $img, url: $url, content: $content, actionState: $actionState, timeDesc: $timeDesc, messageTime: $messageTime, dynamicRelationId: $dynamicRelationId, partnerId: $partnerId, subjectColor: $subjectColor)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DynamicItem &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.actionState, actionState) ||
                other.actionState == actionState) &&
            (identical(other.timeDesc, timeDesc) ||
                other.timeDesc == timeDesc) &&
            (identical(other.messageTime, messageTime) ||
                other.messageTime == messageTime) &&
            (identical(other.dynamicRelationId, dynamicRelationId) ||
                other.dynamicRelationId == dynamicRelationId) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      nickName,
      img,
      url,
      content,
      actionState,
      timeDesc,
      messageTime,
      dynamicRelationId,
      partnerId,
      subjectColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DynamicItemCopyWith<_$_DynamicItem> get copyWith =>
      __$$_DynamicItemCopyWithImpl<_$_DynamicItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DynamicItemToJson(
      this,
    );
  }
}

abstract class _DynamicItem implements DynamicItem {
  const factory _DynamicItem(
      {final int? type,
      final String? nickName,
      final String? img,
      final String? url,
      final String? content,
      final ActionState? actionState,
      final String? timeDesc,
      final int? messageTime,
      final int? dynamicRelationId,
      final int? partnerId,
      final String? subjectColor}) = _$_DynamicItem;

  factory _DynamicItem.fromJson(Map<String, dynamic> json) =
      _$_DynamicItem.fromJson;

  @override
  int? get type;
  @override
  String? get nickName;
  @override
  String? get img;
  @override
  String? get url;
  @override
  String? get content;
  @override
  ActionState? get actionState;
  @override
  String? get timeDesc;
  @override
  int? get messageTime;
  @override
  int? get dynamicRelationId;
  @override
  int? get partnerId;
  @override
  String? get subjectColor;
  @override
  @JsonKey(ignore: true)
  _$$_DynamicItemCopyWith<_$_DynamicItem> get copyWith =>
      throw _privateConstructorUsedError;
}

ActionState _$ActionStateFromJson(Map<String, dynamic> json) {
  return _ActionState.fromJson(json);
}

/// @nodoc
mixin _$ActionState {
  int? get poke => throw _privateConstructorUsedError; // 1:已戳一戳 0:未戳一戳
  int? get flower => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActionStateCopyWith<ActionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActionStateCopyWith<$Res> {
  factory $ActionStateCopyWith(
          ActionState value, $Res Function(ActionState) then) =
      _$ActionStateCopyWithImpl<$Res, ActionState>;
  @useResult
  $Res call({int? poke, int? flower});
}

/// @nodoc
class _$ActionStateCopyWithImpl<$Res, $Val extends ActionState>
    implements $ActionStateCopyWith<$Res> {
  _$ActionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? poke = freezed,
    Object? flower = freezed,
  }) {
    return _then(_value.copyWith(
      poke: freezed == poke
          ? _value.poke
          : poke // ignore: cast_nullable_to_non_nullable
              as int?,
      flower: freezed == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ActionStateCopyWith<$Res>
    implements $ActionStateCopyWith<$Res> {
  factory _$$_ActionStateCopyWith(
          _$_ActionState value, $Res Function(_$_ActionState) then) =
      __$$_ActionStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? poke, int? flower});
}

/// @nodoc
class __$$_ActionStateCopyWithImpl<$Res>
    extends _$ActionStateCopyWithImpl<$Res, _$_ActionState>
    implements _$$_ActionStateCopyWith<$Res> {
  __$$_ActionStateCopyWithImpl(
      _$_ActionState _value, $Res Function(_$_ActionState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? poke = freezed,
    Object? flower = freezed,
  }) {
    return _then(_$_ActionState(
      poke: freezed == poke
          ? _value.poke
          : poke // ignore: cast_nullable_to_non_nullable
              as int?,
      flower: freezed == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ActionState implements _ActionState {
  const _$_ActionState({this.poke, this.flower});

  factory _$_ActionState.fromJson(Map<String, dynamic> json) =>
      _$$_ActionStateFromJson(json);

  @override
  final int? poke;
// 1:已戳一戳 0:未戳一戳
  @override
  final int? flower;

  @override
  String toString() {
    return 'ActionState(poke: $poke, flower: $flower)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ActionState &&
            (identical(other.poke, poke) || other.poke == poke) &&
            (identical(other.flower, flower) || other.flower == flower));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, poke, flower);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ActionStateCopyWith<_$_ActionState> get copyWith =>
      __$$_ActionStateCopyWithImpl<_$_ActionState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ActionStateToJson(
      this,
    );
  }
}

abstract class _ActionState implements ActionState {
  const factory _ActionState({final int? poke, final int? flower}) =
      _$_ActionState;

  factory _ActionState.fromJson(Map<String, dynamic> json) =
      _$_ActionState.fromJson;

  @override
  int? get poke;
  @override // 1:已戳一戳 0:未戳一戳
  int? get flower;
  @override
  @JsonKey(ignore: true)
  _$$_ActionStateCopyWith<_$_ActionState> get copyWith =>
      throw _privateConstructorUsedError;
}
