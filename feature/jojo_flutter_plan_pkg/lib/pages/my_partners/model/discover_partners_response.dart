import 'package:freezed_annotation/freezed_annotation.dart';

part 'discover_partners_response.freezed.dart';
part 'discover_partners_response.g.dart';

@freezed
class DiscoverPartnersData with _$DiscoverPartnersData {
  const factory DiscoverPartnersData({
    List<DiscoverPartner>? partners,
    String? jumpRoute,
    String? title,
    String? viewMore,
  }) = _DiscoverPartnersData;

  factory DiscoverPartnersData.fromJson(Map<String, dynamic> json) =>
      _$DiscoverPartnersDataFromJson(json);
}

@freezed
class DiscoverPartner with _$DiscoverPartner {
  const factory DiscoverPartner({
    String? nickName,
    int? continuousDays,
    int? studyDays,
    String? img,
    String? url,
    int? partnerId,
  }) = _DiscoverPartner;

  factory DiscoverPartner.fromJson(Map<String, dynamic> json) =>
      _$DiscoverPartnerFromJson(json);
}
