// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'partners_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PartnersListData _$$_PartnersListDataFromJson(Map<String, dynamic> json) =>
    _$_PartnersListData(
      partners: (json['partners'] as List<dynamic>?)
          ?.map((e) => PartnerItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      minId: json['minId'] as int?,
      size: json['size'] as int?,
    );

Map<String, dynamic> _$$_PartnersListDataToJson(_$_PartnersListData instance) =>
    <String, dynamic>{
      'partners': instance.partners,
      'minId': instance.minId,
      'size': instance.size,
    };

_$_PartnerItem _$$_PartnerItemFromJson(Map<String, dynamic> json) =>
    _$_PartnerItem(
      nickName: json['nickName'] as String?,
      img: json['img'] as String?,
      url: json['url'] as String?,
      partnerId: json['partnerId'] as int?,
    );

Map<String, dynamic> _$$_PartnerItemToJson(_$_PartnerItem instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'img': instance.img,
      'url': instance.url,
      'partnerId': instance.partnerId,
    };
