// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'discover_partners_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

DiscoverPartnersData _$DiscoverPartnersDataFromJson(Map<String, dynamic> json) {
  return _DiscoverPartnersData.fromJson(json);
}

/// @nodoc
mixin _$DiscoverPartnersData {
  List<DiscoverPartner>? get partners => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get viewMore => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DiscoverPartnersDataCopyWith<DiscoverPartnersData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverPartnersDataCopyWith<$Res> {
  factory $DiscoverPartnersDataCopyWith(DiscoverPartnersData value,
          $Res Function(DiscoverPartnersData) then) =
      _$DiscoverPartnersDataCopyWithImpl<$Res, DiscoverPartnersData>;
  @useResult
  $Res call(
      {List<DiscoverPartner>? partners,
      String? jumpRoute,
      String? title,
      String? viewMore});
}

/// @nodoc
class _$DiscoverPartnersDataCopyWithImpl<$Res,
        $Val extends DiscoverPartnersData>
    implements $DiscoverPartnersDataCopyWith<$Res> {
  _$DiscoverPartnersDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? jumpRoute = freezed,
    Object? title = freezed,
    Object? viewMore = freezed,
  }) {
    return _then(_value.copyWith(
      partners: freezed == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartner>?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      viewMore: freezed == viewMore
          ? _value.viewMore
          : viewMore // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DiscoverPartnersDataCopyWith<$Res>
    implements $DiscoverPartnersDataCopyWith<$Res> {
  factory _$$_DiscoverPartnersDataCopyWith(_$_DiscoverPartnersData value,
          $Res Function(_$_DiscoverPartnersData) then) =
      __$$_DiscoverPartnersDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DiscoverPartner>? partners,
      String? jumpRoute,
      String? title,
      String? viewMore});
}

/// @nodoc
class __$$_DiscoverPartnersDataCopyWithImpl<$Res>
    extends _$DiscoverPartnersDataCopyWithImpl<$Res, _$_DiscoverPartnersData>
    implements _$$_DiscoverPartnersDataCopyWith<$Res> {
  __$$_DiscoverPartnersDataCopyWithImpl(_$_DiscoverPartnersData _value,
      $Res Function(_$_DiscoverPartnersData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? jumpRoute = freezed,
    Object? title = freezed,
    Object? viewMore = freezed,
  }) {
    return _then(_$_DiscoverPartnersData(
      partners: freezed == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartner>?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      viewMore: freezed == viewMore
          ? _value.viewMore
          : viewMore // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DiscoverPartnersData implements _DiscoverPartnersData {
  const _$_DiscoverPartnersData(
      {final List<DiscoverPartner>? partners,
      this.jumpRoute,
      this.title,
      this.viewMore})
      : _partners = partners;

  factory _$_DiscoverPartnersData.fromJson(Map<String, dynamic> json) =>
      _$$_DiscoverPartnersDataFromJson(json);

  final List<DiscoverPartner>? _partners;
  @override
  List<DiscoverPartner>? get partners {
    final value = _partners;
    if (value == null) return null;
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? jumpRoute;
  @override
  final String? title;
  @override
  final String? viewMore;

  @override
  String toString() {
    return 'DiscoverPartnersData(partners: $partners, jumpRoute: $jumpRoute, title: $title, viewMore: $viewMore)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DiscoverPartnersData &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.viewMore, viewMore) ||
                other.viewMore == viewMore));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_partners),
      jumpRoute,
      title,
      viewMore);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DiscoverPartnersDataCopyWith<_$_DiscoverPartnersData> get copyWith =>
      __$$_DiscoverPartnersDataCopyWithImpl<_$_DiscoverPartnersData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DiscoverPartnersDataToJson(
      this,
    );
  }
}

abstract class _DiscoverPartnersData implements DiscoverPartnersData {
  const factory _DiscoverPartnersData(
      {final List<DiscoverPartner>? partners,
      final String? jumpRoute,
      final String? title,
      final String? viewMore}) = _$_DiscoverPartnersData;

  factory _DiscoverPartnersData.fromJson(Map<String, dynamic> json) =
      _$_DiscoverPartnersData.fromJson;

  @override
  List<DiscoverPartner>? get partners;
  @override
  String? get jumpRoute;
  @override
  String? get title;
  @override
  String? get viewMore;
  @override
  @JsonKey(ignore: true)
  _$$_DiscoverPartnersDataCopyWith<_$_DiscoverPartnersData> get copyWith =>
      throw _privateConstructorUsedError;
}

DiscoverPartner _$DiscoverPartnerFromJson(Map<String, dynamic> json) {
  return _DiscoverPartner.fromJson(json);
}

/// @nodoc
mixin _$DiscoverPartner {
  String? get nickName => throw _privateConstructorUsedError;
  int? get continuousDays => throw _privateConstructorUsedError;
  int? get studyDays => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  int? get partnerId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DiscoverPartnerCopyWith<DiscoverPartner> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverPartnerCopyWith<$Res> {
  factory $DiscoverPartnerCopyWith(
          DiscoverPartner value, $Res Function(DiscoverPartner) then) =
      _$DiscoverPartnerCopyWithImpl<$Res, DiscoverPartner>;
  @useResult
  $Res call(
      {String? nickName,
      int? continuousDays,
      int? studyDays,
      String? img,
      String? url,
      int? partnerId});
}

/// @nodoc
class _$DiscoverPartnerCopyWithImpl<$Res, $Val extends DiscoverPartner>
    implements $DiscoverPartnerCopyWith<$Res> {
  _$DiscoverPartnerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DiscoverPartnerCopyWith<$Res>
    implements $DiscoverPartnerCopyWith<$Res> {
  factory _$$_DiscoverPartnerCopyWith(
          _$_DiscoverPartner value, $Res Function(_$_DiscoverPartner) then) =
      __$$_DiscoverPartnerCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickName,
      int? continuousDays,
      int? studyDays,
      String? img,
      String? url,
      int? partnerId});
}

/// @nodoc
class __$$_DiscoverPartnerCopyWithImpl<$Res>
    extends _$DiscoverPartnerCopyWithImpl<$Res, _$_DiscoverPartner>
    implements _$$_DiscoverPartnerCopyWith<$Res> {
  __$$_DiscoverPartnerCopyWithImpl(
      _$_DiscoverPartner _value, $Res Function(_$_DiscoverPartner) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
  }) {
    return _then(_$_DiscoverPartner(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DiscoverPartner implements _DiscoverPartner {
  const _$_DiscoverPartner(
      {this.nickName,
      this.continuousDays,
      this.studyDays,
      this.img,
      this.url,
      this.partnerId});

  factory _$_DiscoverPartner.fromJson(Map<String, dynamic> json) =>
      _$$_DiscoverPartnerFromJson(json);

  @override
  final String? nickName;
  @override
  final int? continuousDays;
  @override
  final int? studyDays;
  @override
  final String? img;
  @override
  final String? url;
  @override
  final int? partnerId;

  @override
  String toString() {
    return 'DiscoverPartner(nickName: $nickName, continuousDays: $continuousDays, studyDays: $studyDays, img: $img, url: $url, partnerId: $partnerId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DiscoverPartner &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.continuousDays, continuousDays) ||
                other.continuousDays == continuousDays) &&
            (identical(other.studyDays, studyDays) ||
                other.studyDays == studyDays) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, nickName, continuousDays, studyDays, img, url, partnerId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DiscoverPartnerCopyWith<_$_DiscoverPartner> get copyWith =>
      __$$_DiscoverPartnerCopyWithImpl<_$_DiscoverPartner>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DiscoverPartnerToJson(
      this,
    );
  }
}

abstract class _DiscoverPartner implements DiscoverPartner {
  const factory _DiscoverPartner(
      {final String? nickName,
      final int? continuousDays,
      final int? studyDays,
      final String? img,
      final String? url,
      final int? partnerId}) = _$_DiscoverPartner;

  factory _DiscoverPartner.fromJson(Map<String, dynamic> json) =
      _$_DiscoverPartner.fromJson;

  @override
  String? get nickName;
  @override
  int? get continuousDays;
  @override
  int? get studyDays;
  @override
  String? get img;
  @override
  String? get url;
  @override
  int? get partnerId;
  @override
  @JsonKey(ignore: true)
  _$$_DiscoverPartnerCopyWith<_$_DiscoverPartner> get copyWith =>
      throw _privateConstructorUsedError;
}
