// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'partners_list_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PartnersListData _$PartnersListDataFromJson(Map<String, dynamic> json) {
  return _PartnersListData.fromJson(json);
}

/// @nodoc
mixin _$PartnersListData {
  List<PartnerItem>? get partners => throw _privateConstructorUsedError;
  int? get minId => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnersListDataCopyWith<PartnersListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnersListDataCopyWith<$Res> {
  factory $PartnersListDataCopyWith(
          PartnersListData value, $Res Function(PartnersListData) then) =
      _$PartnersListDataCopyWithImpl<$Res, PartnersListData>;
  @useResult
  $Res call({List<PartnerItem>? partners, int? minId, int? size});
}

/// @nodoc
class _$PartnersListDataCopyWithImpl<$Res, $Val extends PartnersListData>
    implements $PartnersListDataCopyWith<$Res> {
  _$PartnersListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? minId = freezed,
    Object? size = freezed,
  }) {
    return _then(_value.copyWith(
      partners: freezed == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<PartnerItem>?,
      minId: freezed == minId
          ? _value.minId
          : minId // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnersListDataCopyWith<$Res>
    implements $PartnersListDataCopyWith<$Res> {
  factory _$$_PartnersListDataCopyWith(
          _$_PartnersListData value, $Res Function(_$_PartnersListData) then) =
      __$$_PartnersListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PartnerItem>? partners, int? minId, int? size});
}

/// @nodoc
class __$$_PartnersListDataCopyWithImpl<$Res>
    extends _$PartnersListDataCopyWithImpl<$Res, _$_PartnersListData>
    implements _$$_PartnersListDataCopyWith<$Res> {
  __$$_PartnersListDataCopyWithImpl(
      _$_PartnersListData _value, $Res Function(_$_PartnersListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? minId = freezed,
    Object? size = freezed,
  }) {
    return _then(_$_PartnersListData(
      partners: freezed == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<PartnerItem>?,
      minId: freezed == minId
          ? _value.minId
          : minId // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PartnersListData implements _PartnersListData {
  const _$_PartnersListData(
      {final List<PartnerItem>? partners, this.minId, this.size})
      : _partners = partners;

  factory _$_PartnersListData.fromJson(Map<String, dynamic> json) =>
      _$$_PartnersListDataFromJson(json);

  final List<PartnerItem>? _partners;
  @override
  List<PartnerItem>? get partners {
    final value = _partners;
    if (value == null) return null;
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? minId;
  @override
  final int? size;

  @override
  String toString() {
    return 'PartnersListData(partners: $partners, minId: $minId, size: $size)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnersListData &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.minId, minId) || other.minId == minId) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_partners), minId, size);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnersListDataCopyWith<_$_PartnersListData> get copyWith =>
      __$$_PartnersListDataCopyWithImpl<_$_PartnersListData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnersListDataToJson(
      this,
    );
  }
}

abstract class _PartnersListData implements PartnersListData {
  const factory _PartnersListData(
      {final List<PartnerItem>? partners,
      final int? minId,
      final int? size}) = _$_PartnersListData;

  factory _PartnersListData.fromJson(Map<String, dynamic> json) =
      _$_PartnersListData.fromJson;

  @override
  List<PartnerItem>? get partners;
  @override
  int? get minId;
  @override
  int? get size;
  @override
  @JsonKey(ignore: true)
  _$$_PartnersListDataCopyWith<_$_PartnersListData> get copyWith =>
      throw _privateConstructorUsedError;
}

PartnerItem _$PartnerItemFromJson(Map<String, dynamic> json) {
  return _PartnerItem.fromJson(json);
}

/// @nodoc
mixin _$PartnerItem {
  String? get nickName => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  int? get partnerId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnerItemCopyWith<PartnerItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerItemCopyWith<$Res> {
  factory $PartnerItemCopyWith(
          PartnerItem value, $Res Function(PartnerItem) then) =
      _$PartnerItemCopyWithImpl<$Res, PartnerItem>;
  @useResult
  $Res call({String? nickName, String? img, String? url, int? partnerId});
}

/// @nodoc
class _$PartnerItemCopyWithImpl<$Res, $Val extends PartnerItem>
    implements $PartnerItemCopyWith<$Res> {
  _$PartnerItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerItemCopyWith<$Res>
    implements $PartnerItemCopyWith<$Res> {
  factory _$$_PartnerItemCopyWith(
          _$_PartnerItem value, $Res Function(_$_PartnerItem) then) =
      __$$_PartnerItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? nickName, String? img, String? url, int? partnerId});
}

/// @nodoc
class __$$_PartnerItemCopyWithImpl<$Res>
    extends _$PartnerItemCopyWithImpl<$Res, _$_PartnerItem>
    implements _$$_PartnerItemCopyWith<$Res> {
  __$$_PartnerItemCopyWithImpl(
      _$_PartnerItem _value, $Res Function(_$_PartnerItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
  }) {
    return _then(_$_PartnerItem(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PartnerItem implements _PartnerItem {
  const _$_PartnerItem({this.nickName, this.img, this.url, this.partnerId});

  factory _$_PartnerItem.fromJson(Map<String, dynamic> json) =>
      _$$_PartnerItemFromJson(json);

  @override
  final String? nickName;
  @override
  final String? img;
  @override
  final String? url;
  @override
  final int? partnerId;

  @override
  String toString() {
    return 'PartnerItem(nickName: $nickName, img: $img, url: $url, partnerId: $partnerId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnerItem &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickName, img, url, partnerId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerItemCopyWith<_$_PartnerItem> get copyWith =>
      __$$_PartnerItemCopyWithImpl<_$_PartnerItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnerItemToJson(
      this,
    );
  }
}

abstract class _PartnerItem implements PartnerItem {
  const factory _PartnerItem(
      {final String? nickName,
      final String? img,
      final String? url,
      final int? partnerId}) = _$_PartnerItem;

  factory _PartnerItem.fromJson(Map<String, dynamic> json) =
      _$_PartnerItem.fromJson;

  @override
  String? get nickName;
  @override
  String? get img;
  @override
  String? get url;
  @override
  int? get partnerId;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerItemCopyWith<_$_PartnerItem> get copyWith =>
      throw _privateConstructorUsedError;
}
