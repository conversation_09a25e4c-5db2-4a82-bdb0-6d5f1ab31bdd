import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/widgets/discover_partners_section.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/widgets/tab_section.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/widgets/page_view_content.dart';
import '../../common/host_env/host_env.dart';

import '../../generated/l10n.dart';

class MyPartnersView extends HookWidget {
  final MyPartnersState state;

  const MyPartnersView({Key? key, required this.state}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ctrl = context.read<MyPartnersCtrl>();
    final pageController = useMemoized(() => PageController(initialPage: state.selectedTabIndex));

    // 标题显示学伴数量
    String title = S.of(context).myPartner;

    useEffect(() {
      RunEnv.sensorsTrack('\$AppViewScreen', {"\$screen_name": "个人主页_学伴页面曝光"});
      return null;
    }, []);

    // 监听状态变化，同步 PageView
    useEffect(() {
      if (pageController.hasClients && pageController.page?.round() != state.selectedTabIndex) {
        pageController.jumpToPage(state.selectedTabIndex);
      }
      return null;
    }, [state.selectedTabIndex]);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: JoJoAppBar(
        title: title,
        backgroundColor: Colors.transparent,
        centerTitle: true
      ),
      body: JoJoPageLoadingV25(
        status: state.pageStatus,
        child: Column(
          children: [
            // 发现学伴模块
            if (state.learningPartnersCount > 0 && state.discoverPartnersData != null)
              DiscoverPartnersSection(data: state.discoverPartnersData!),

            // 动态/学伴切换标签
            TabSection(
              selectedTabIndex: state.selectedTabIndex,
              controller: ctrl,
            ),

            // PageView 列表内容
            Expanded(
              child: PageViewContent(
                state: state,
                controller: ctrl,
                pageController: pageController,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
