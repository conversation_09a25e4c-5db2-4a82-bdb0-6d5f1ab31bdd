import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';

class DeletePartnerDialog extends StatelessWidget {
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const DeletePartnerDialog({
    super.key,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 271,
      child: Column(
        children: [
          SizedBox(height: 28.rdp),
          _buildTitleLabel(context),
          SizedBox(height: 10.rdp),
          _buildDetailsLabel(context),
          SizedBox(height: 36.rdp),
          _buildCancelButton(context),
          SizedBox(height: 8.rdp),
          _buildConfirmButton(context),
        ],
      ),
    );
  }

  Widget _buildTitleLabel(BuildContext context) {
    return Text(
      S.of(context).deletePartnerTitle,
      style: TextStyle(
        fontSize: 20.rdp,
        fontWeight: FontWeight.w500,
        color: context.appColors.jColorGray6,
        fontFamily: 'PingFang SC',
      ),
    );
  }

  Widget _buildDetailsLabel(BuildContext context) {
    return Text(
      S.of(context).deletePartnerDetails,
      style: TextStyle(
        fontSize: 18.rdp,
        fontWeight: FontWeight.w400,
        color: context.appColors.jColorGray5,
        fontFamily: 'PingFang SC',
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return SizedBox(
      width: 280.rdp,
      height: 44.rdp,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          backgroundColor: context.appColors.jColorYellow4,
          foregroundColor: context.appColors.jColorYellow4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22.rdp),
          ),
          side: BorderSide.none,
        ),
        onPressed: onCancel,
        child: Text(
          S.of(context).deleteCancel,
          style: TextStyle(
            fontSize: 18.rdp,
            color: context.appColors.jColorYellow6,
            fontWeight: FontWeight.w500,
            fontFamily: 'PingFang SC',
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: Colors.white,
      ),
      onPressed: onConfirm,
      child: Text(
        S.of(context).deleteConfirm,
        style: TextStyle(
          fontSize: 18.rdp,
          color: context.appColors.jColorGray5,
          fontWeight: FontWeight.w400,
          fontFamily: 'PingFang SC',
        ),
      ),
    );
  }
}
