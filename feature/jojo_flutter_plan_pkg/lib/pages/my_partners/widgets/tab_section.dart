import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import '../../../generated/l10n.dart';
import '../controller.dart';

/// 动态/学伴切换标签模块
class TabSection extends StatelessWidget {
  final int selectedTabIndex;
  final MyPartnersCtrl controller;

  const TabSection({
    Key? key,
    required this.selectedTabIndex,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Row(
        children: [
          _buildTabItem(context, 0, S.of(context).feed),
          SizedBox(width: 32.rdp),
          _buildTabItem(context, 1, S.of(context).partner),
        ],
      ),
    );
  }

  /// 构建单个标签项
  Widget _buildTabItem(BuildContext context, int index, String title) {
    final isSelected = selectedTabIndex == index;
    final textStyle = isSelected ? context.textstyles.headingEmphasis.pf : context.textstyles.heading.pf ;
    final textColor = isSelected ? context.appColors.jColorGray6 : context.appColors.jColorGray4;

    return GestureDetector(
      onTap: () => controller.changeTab(index),
      child: Column(
        children: [
          Text(
            title,
            style: textStyle.copyWith(color: textColor),
          ),
          SizedBox(height: 2.rdp),
          Container(
            height: 4.rdp,
            width: 20.rdp,
            decoration: BoxDecoration(
              color: isSelected ? context.appColors.mainColor : Colors.transparent,
              borderRadius: BorderRadius.circular(2.rdp),
            ),
          ),
        ],
      ),
    );
  }
}
