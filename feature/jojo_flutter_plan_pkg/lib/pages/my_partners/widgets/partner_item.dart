import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/widgets/delete_partner_dialog.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/new_subject_recm/view.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import '../controller.dart';
import '../view_models/my_partners_view_models.dart';

class PartnerItemWidget extends StatefulWidget {
  final PartnerViewModel partner;
  final VoidCallback? onDelete;
  final VoidCallback? onClick;

  const PartnerItemWidget({
    super.key,
    required this.partner,
    this.onDelete,
    this.onClick,
  });

  @override
  State<PartnerItemWidget> createState() => _PartnerItemWidgetState();
}

class _PartnerItemWidgetState extends State<PartnerItemWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: widget.onClick,
      child: Container(
        height: 82,
        color: Colors.white,
        padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildAvatar(context),
            _buildInfo(context),
            _buildButton(context),
          ],
        ),
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context) {
    return Container(
      width: 58.rdp,
      height: 58.rdp,
      clipBehavior: Clip.none,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(29.rdp),
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(29.rdp),
        child: AlternativeImageWidget(
          imageUrl: widget.partner.partnerAvatar,
          displayWidth: 58.rdp,
          displayHeight: 58.rdp,
          displayConfig: ImageDisplayConfig.head,
          hideDefaultHolder: true,
        ),
      ),
    );
  }

  /// 构建信息
  Widget _buildInfo(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(left: 8.rdp, right: 3.rdp),
        child: Text(
          widget.partner.partnerName,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 16.rdp,
            fontWeight: FontWeight.w400,
            color: context.appColors.jColorGray6,
            fontFamily: 'PingFang SC',
          ),
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context) {
    return SizedBox(
      width: 120.rdp,
      height: 32.rdp,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.rdp),
          ),
          side:
              BorderSide(color: context.appColors.jColorYellow4, width: 1.rdp),
          padding: EdgeInsets.only(right: 4.rdp, top: 1.rdp),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ImageAssetWeb(
              assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_ADD_OK,
              fit: BoxFit.contain,
              package: Config.package,
              height: 20.rdp,
              width: 20.rdp,
            ),
            SizedBox(width: 4.rdp),
            Text(
              S.of(context).isMyPartner,
              style: TextStyle(
                fontSize: 14.rdp,
                color: context.appColors.jColorYellow6,
                fontWeight: FontWeight.w400,
                fontFamily: 'PingFang SC',
              ),
            ),
          ],
        ),
        onPressed: () {
          showDeletePartnerDialog();
        },
      ),
    );
  }

  void showDeletePartnerDialog() {
    JoJoNativeBridge.shared.showHomePageTabs(show: false);
    const dialogTag = "DeletePartnerDialog";
    SmartDialog.show(
        tag: dialogTag,
        alignment: Alignment.bottomCenter,
        clickMaskDismiss: true,
        keepSingle: true,
        animationType: SmartAnimationType.centerFade_otherSlide,
        useAnimation: true,
        maskColor: Colors.black.withOpacity(0.7),
        builder: (context) => Container(
              height: 271.rdp,
              width: MediaQuery.of(context).size.width,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft:
                      Radius.circular(context.dimensions.largeCornerRadius),
                  topRight:
                      Radius.circular(context.dimensions.largeCornerRadius),
                ),
              ),
              child: DeletePartnerDialog(
                onConfirm: () {
                  widget.onDelete?.call();
                  SmartDialog.dismiss();
                },
                onCancel: () {
                  SmartDialog.dismiss();
                },
              ),
            ),
        onDismiss: () {});
  }
}
