import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import '../../learning_incentives/widget/alternative_image_widget.dart';
import '../view_models/my_partners_view_models.dart';
import '../../../static/svg.dart';
import '../../../common/config/config.dart';

/// 发现学伴模块
class DiscoverPartnersSection extends StatelessWidget {
  final DiscoverPartnersViewModel data;

  const DiscoverPartnersSection({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final paddingValue = context.dimensions.mediumSpacing.rdp;
    return GestureDetector(
        onTap: () {
          // 点击后跳转到 jumpRoute
          if (data.jumpRoute != null && data.jumpRoute?.isNotEmpty == true) {
            RunEnv.jumpLink(data.jumpRoute ?? '');
          }

          RunEnv.sensorsTrack('\$AppClick', {
            '\$element_name': '发现学伴_卡片点击',
          });
        },
        child: Container(
          margin: EdgeInsets.all(20.rdp),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: context.appColors.jColorGray3),
            borderRadius:
                BorderRadius.circular(context.dimensions.largeCornerRadius.rdp),
          ),
          child: Stack(
            children: [
              // 背景图
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                      context.dimensions.largeCornerRadius.rdp),
                  child: ImageAssetWeb(
                    assetName: AssetsImg.PLAN_FIND_PARTNER_MY_PARTNERS_FIND_BG,
                    package: Config.package,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              // 内容
              Padding(
                padding: EdgeInsets.fromLTRB(
                    paddingValue, paddingValue, paddingValue, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 30.rdp,
                      child: _buildHeader(context),
                    ),
                    SizedBox(height: context.dimensions.mediumSpacing.rdp),
                    // 学伴头像列表
                    _buildPartnersAvatarRow(context, data.partnerList),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  /// 构建头部标题和查看更多
  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          data.sectionTitle,
          style: context.textstyles.headingLargeEmphasis.pf.copyWith(
            color: context.appColors.jColorGray6,
          ),
        ),
        Text(
          data.moreButtonText,
          style: context.textstyles.remark.pf.copyWith(
            color: context.appColors.jColorGray5,
          ),
        ),
      ],
    );
  }

  /// 学伴头像行布局 - 最多显示3个，平均分配空间
  Widget _buildPartnersAvatarRow(
      BuildContext context, List<DiscoverPartnerViewModel> partners) {
    // 最多显示3个学伴
    final displayPartners = partners.take(3).toList();

    if (displayPartners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        for (int i = 0; i < 3; i++) ...[
          Expanded(
            child: i < displayPartners.length
                ? _buildPartnerAvatar(context, displayPartners[i])
                : const SizedBox.shrink(),
          ),
        ],
      ],
    );
  }

  /// 学伴头像 - 移除边距，居中显示
  Widget _buildPartnerAvatar(
      BuildContext context, DiscoverPartnerViewModel partner) {
    final width = 100.rdp;
    final height = 120.rdp * ImageDisplayConfig.waist.heightRatio;
    final continuousDays = partner.continuousDays ?? 0;
    return Column(
      children: [
        SizedBox(
          height: 18.rdp,
          child: Container(
            color: Colors.white,
            child: Text(
              partner.displayName.length > 8
                  ? '${partner.displayName.substring(0, 8)}...'
                  : partner.displayName,
              style: context.textstyles.smallestText.pf
                  .copyWith(color: context.appColors.jColorGray5),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        SizedBox(height: context.dimensions.minimumSpacing.rdp),
        Stack(
          children: [
            AlternativeImageWidget(
              imageUrl: partner.avatarUrl,
              displayWidth: width,
              displayHeight: height,
              displayConfig: ImageDisplayConfig.waist,
              hideDefaultHolder: true,
            ),
            _buildContinuousDaysWidget(context, continuousDays),
          ],
        ),
      ],
    );
  }

  Widget _buildContinuousDaysWidget(BuildContext context, int continuousDays) {
    var str = getContinuousDaysStr(continuousDays);
    return Positioned(
      left: 14.rdp,
      bottom: 2.rdp,
      height: 24.rdp,
      width: 24.rdp,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_NUM_BG,
            fit: BoxFit.contain,
            package: Config.package,
            height: 24.rdp,
            width: 24.rdp,
          ),
          Padding(
            padding: EdgeInsets.only(top: 1.rdp),
            child: Text(
              str,
              textAlign: TextAlign.center,
              style: context.textstyles.smallestText.mrB.copyWith(
                color: Colors.white,
                fontFamily: 'MohrRounded_Bold',
                package: "jojo_flutter_base",
              ),
            ),
          )
        ],
      ),
    );
  }

  String getContinuousDaysStr(int continuousDays) {
    if (continuousDays < 1000) {
      return continuousDays.toString();
    } else if (continuousDays < 10000) {
      int kValue = continuousDays ~/ 1000;
      return '${kValue}k';
    } else {
      int kValue = continuousDays ~/ 10000;
      return '${kValue}w';
    }
  }
}
