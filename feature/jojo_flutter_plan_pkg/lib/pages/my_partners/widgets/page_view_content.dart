import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/widgets/partner_item.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

import '../controller.dart';
import '../state.dart';
import 'dynamic_item.dart' as widgets;

/// PageView 列表内容模块
class PageViewContent extends StatelessWidget {
  final MyPartnersState state;
  final MyPartnersCtrl controller;
  final PageController pageController;

  const PageViewContent({
    Key? key,
    required this.state,
    required this.controller,
    required this.pageController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageView(
      controller: pageController,
      onPageChanged: (index) {
        controller.changeTab(index);
      },
      children: [
        _buildDynamicsList(context),
        _buildPartnersList(context),
      ],
    );
  }

  bool showDynamicsEmpty() {
    return state.dynamicsList.isEmpty &&
        !controller.refreshDynamicController.isRefresh;
  }

  /// 动态列表
  Widget _buildDynamicsList(BuildContext context) {
    RefreshController refreshController = controller.refreshDynamicController;
    return PullOrRefresh(
      noDataText: '',
      onRefresh: () => controller.onDynamicsRefresh(),
      onLoading: () => controller.onDynamicsLoadMore(),
      enablePullUp: !showDynamicsEmpty(),
      refreshController: refreshController,
      child: showDynamicsEmpty()
          ? getEmptyView(context, S.of(context).dynamicEmpty)
          : ListView.builder(
              padding: EdgeInsets.all(16.rdp),
              itemCount: state.dynamicsList.length,
              itemBuilder: (context, index) {
                final dynamic = state.dynamicsList[index];
                return widgets.DynamicItemWidget(
                  dynamicModel: dynamic,
                  controller: controller,
                );
              },
            ),
    );
  }

  bool showPartnersEmpty() {
    return state.partnersList.isEmpty &&
        !controller.refreshPartnerController.isRefresh;
  }

  /// 学伴列表
  Widget _buildPartnersList(BuildContext context) {
    RefreshController refreshController = controller.refreshPartnerController;
    return PullOrRefresh(
      noDataText: '',
      onRefresh: () => controller.onPartnersRefresh(),
      onLoading: () => controller.onPartnersLoadMore(),
      enablePullUp: !showPartnersEmpty(),
      refreshController: refreshController,
      child: showPartnersEmpty()
          ? getEmptyView(context, S.of(context).partnerEmpty)
          : ListView.builder(
              padding: EdgeInsets.only(top: 4.rdp),
              itemCount: state.partnersList.length,
              itemBuilder: (context, index) {
                final partner = state.partnersList[index];
                return PartnerItemWidget(
                  partner: partner,
                  onDelete: () => deletePartner(partner.partnerId),
                  onClick: () => goPartnerDetails(partner.profileUrl),
                );
              },
            ),
    );
  }

  Widget getEmptyView(BuildContext context, String tip) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ImageAssetWeb(
          assetName: AssetsImg.PLAN_FIND_PARTNER_PARTNER_LIST_EMPTY,
          fit: BoxFit.contain,
          width: 200.rdp,
          height: 200.rdp,
          package: Config.package,
        ),
        SizedBox(height: 4.rdp),
        Text(
          tip,
          style: TextStyle(
            fontSize: 16.rdp,
            fontWeight: FontWeight.w400,
            color: context.appColors.jColorGray4,
            fontFamily: 'PingFang SC',
          ),
        ),
        SizedBox(height: 50.rdp),
      ],
    );
  }

  void deletePartner(int partnerId) {
    controller.deletePartner(partnerId);
  }

  void goPartnerDetails(String? route) {
    if (route != null) {
      RunEnv.jumpLink(route);
    }
  }
}
