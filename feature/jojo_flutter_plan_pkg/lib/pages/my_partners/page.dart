import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/view.dart';

class MyPartnersPage extends BasePage {
  const MyPartnersPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _MyPartnersPageModelState();
}

class _MyPartnersPageModelState extends BaseState<MyPartnersPage> with BasicInitPage {
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => MyPartnersCtrl(),
      child: BlocBuilder<MyPartnersCtrl, MyPartnersState>(builder: (context, state) {
        return MyPartnersView(state: state);
      }),
    );
  }
}
