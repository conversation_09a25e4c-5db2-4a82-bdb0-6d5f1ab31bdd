
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/state.dart';
import 'package:jojo_flutter_plan_pkg/service/review_assistant_api.dart';

class ReviewSearchCtrl extends Cubit<ReviewSearchState> {

  final String? subjectColor;
  final String? courseKey;
  final String? classId;
  final String? buriedString;

  ReviewAssistantApi? pageApi;
  List<String> historyList = [];  // 历史搜索数据
  bool inSearch = false; // 是否处于搜索中
  bool isRequesting = false; // 是否处于搜索请求中
  bool isInputting = false;  // 是否正在输入
  SearchStatus searchStatus = SearchStatus.success; // 搜索状态
  String inputTxt = "";  // 搜索的内容
  String preInputTxt = "";  // 搜索成功的内容
  int currentPageIndex = 1;  // 当前搜索页码
  int totalPageNum = 1;  // 总页数，默认1
  int totalLessonNum = 0;  // 总课程数目
  final int pageSize = 30;  // 默认每页最大显示30条数据
  List<CourseCard?> dataList = [];
  CancelToken? _cancelToken;

  RefreshController refreshController = RefreshController(initialRefresh: false);
  ScrollController scrollController = ScrollController();
  FocusNode focusNode = FocusNode();

  TextEditingController textEditingController = TextEditingController();

  ReviewSearchCtrl(
      {ReviewAssistantApi? api,
        required this.subjectColor,
        required this.courseKey,
        required this.classId,
        required this.buriedString})
      : super(ReviewSearchState(pageStatus: PageStatus.loading)) {
    pageApi = api ?? ReviewAssistantApis;
    // 默认加载成功
    ReviewSearchState newState = state.copyWith();
    newState.pageStatus = PageStatus.success;
    emit(newState);
  }

  // 获取历史数据
  Future<void> getHistoryData() async {
    try {
      String key = "kReviewSearchPage_${BaseConfig.share.userInfo?.uid}";
      NativeValue? mp = await jojoNativeBridge.operationNativeValueGet(key: key).then((value) => value.data);
      String value = mp?.value ?? '';
      if (value.isNotEmpty) {
        historyList.clear();
        historyList.addAll(value.split("_"));
      }
    } catch (e) {
      l.e("补学助手搜索页", "获取历史搜索数据异常");
    } finally {
      safeEmit();
    }
  }

  // 保存历史数据
  Future<void> saveHistoryData() async {
    if (inputTxt.isEmpty) return;
    if (historyList.contains(inputTxt)) {
      // 当前列表中有这条搜索数据，则需要把数据放在最前面
      historyList.remove(inputTxt);
    }
    if (historyList.length >= 10) {
      // 超过了10条，需要删除最早搜索的那条搜索记录
      historyList.removeLast();
    }
    // 插入开头
    historyList.insert(0, inputTxt.trim());
    // 转化成字符串数组，保存在本地
    String result = historyList.join('_');
    String key = "kReviewSearchPage_${BaseConfig.share.userInfo?.uid}";
    await jojoNativeBridge.operationNativeValueSet(key: key, value: result);
  }

  // 点击删除时执行
  Future<void> deleteHandle() async {
    String key = "kReviewSearchPage_${BaseConfig.share.userInfo?.uid}";
    await jojoNativeBridge.operationNativeValueSet(key: key, value: "");
    historyList.clear();
    safeEmit();
  }

  // 点击搜索时执行
  Future<void> searchHandle() async {
    if (isRequesting) return; // 防止用户连续点击
    await searchData();
    saveHistoryData();  // 搜索完成之后再处理历史数据
  }

  Future<CourseLessonsSearchData?> getSearchData(int pageNum, String searchText) async {
    try {
      if (_cancelToken != null) {
        _cancelToken?.cancel();
        _cancelToken = null;
      }
      _cancelToken ??= CancelToken();
      return await pageApi?.searchCourseLessonsInfo(
          courseKey: courseKey ?? "",
          searchWord: Uri.encodeComponent(searchText.trim()),
          classId: int.tryParse(classId ?? "0") ?? 0,
          sceneId: 1,
          pageNum: pageNum,
          pageSize: pageSize,
        cancelToken: RequestOptions(cancelToken: _cancelToken)
      );
    } catch (e) {
      _cancelToken = null;
      return CourseLessonsSearchData();
    }
  }

  // 请求搜索数据
  Future<void> searchData() async {
    try {
      isRequesting = true;
      inSearch = true;
      refresh();  // 页面更新，显示加载状态
      currentPageIndex = 1;
      totalPageNum = 0;
      CourseLessonsSearchData? data = await getSearchData(currentPageIndex, inputTxt);
      dataList.clear();
      if (data?.total == null) {
        // 显示没有查找到内容哦
        searchStatus = SearchStatus.failed;
        preInputTxt = "";
      } else {
        if (data?.total == 0) {
          searchStatus = SearchStatus.emptyResult;
          preInputTxt = "";
        } else {
          // 显示正常页码的内容
          totalPageNum = ((data?.total ?? 0) + pageSize - 1) ~/ pageSize;
          totalLessonNum = data?.total ?? 0;
          searchStatus = SearchStatus.success;
          dataList.addAll(transformList(data?.data));
          refreshController.resetNoData();
          preInputTxt = inputTxt;
          if (totalPageNum <= 1) {
            refreshController.loadNoData();
          }
        }
      }
    } catch (e) {
      print(e);
      // 请求失败，显示失败重试页面
      searchStatus = SearchStatus.failed;
      preInputTxt = "";
    } finally {
      isRequesting = false;
      refresh();
    }
  }

  // 下拉刷新
  Future<void> refreshData() async {
    try {
      CourseLessonsSearchData? data = await getSearchData(1, preInputTxt);
      if (data?.total == null) {} else {
        dataList.clear();
        dataList.addAll(transformList(data?.data));
        currentPageIndex = 1;
        totalPageNum = ((data?.total ?? 0) + pageSize - 1) ~/ pageSize;
        totalLessonNum = data?.total ?? 0;
        refreshController.resetNoData();
        if (totalPageNum <= 1) {
          refreshController.loadNoData();
        }
      }
    } catch (e) {
      print(e);
    } finally {
      refreshController.refreshCompleted();
      refresh();
    }
  }

  // 加载更多
  Future<void> loadMoreData() async {
    try {
      if (currentPageIndex >= totalPageNum) return;
      CourseLessonsSearchData? data = await getSearchData(currentPageIndex+1, preInputTxt);
      if (data?.total == null) {
        // 加载更多失败了
        refreshController.loadComplete();
      } else {
        dataList.addAll(transformList(data?.data));
        currentPageIndex += 1;
        refreshController.loadComplete();
        if (currentPageIndex >= totalPageNum) {
          refreshController.loadNoData();
        }
      }
    } catch (e) {
      print(e);
      refreshController.loadComplete();
    } finally {
      refresh();
    }
  }

  List<CourseCard> transformList(List<CourseLessonItem?>? dataList) {
    List<CourseCard> list = [];
    dataList?.forEach((element) {
      if (element != null) {
        list.add(getLessonCourseCardInfo(element));
      }
    });
    return list;
  }

  CourseCard getLessonCourseCardInfo(CourseLessonItem lessonInfo) {
    CourseCard cardInfo = CourseCard(
        null,
        int.tryParse(classId ?? '0') ?? 0,
        null,
        null,
        lessonInfo.today,
        false,
        null,
        LessonCardType.LESSON.statusCode,
        lessonInfo.lessonName,
        lessonInfo.icon,
        lessonInfo.lessonId,
        lessonInfo.lessonOrder,
        null,
        null,
        null,
        null,
        null,
        null,
        lessonInfo.studyStatus,
        null,
        lessonInfo.studyTipsVoice,
        lessonInfo.icon,
        lessonInfo.icon,
        lessonInfo.icon,
        lessonInfo.lockVoice,
        null,
        null,
        null,
        null,
        null,
        lessonInfo.lessonGrade,
        lessonInfo.lessonGradeResourceUrl,
        null,
        null,
        iconType: null,
        gifIconRes: null);
    cardInfo.router = lessonInfo.route;

    int nodeStatus = lessonInfo.finish == false ? CourseCard.lockedUnFinish : CourseCard.lockedFinish;
    if (lessonInfo.makeup == true && lessonInfo.makeupUIExpired != true) {
      nodeStatus = CourseCard.supplement;
    }
    cardInfo.nodeStatus = nodeStatus;
    cardInfo.mainColor = subjectColor ?? "#FF9045";

    return cardInfo;
  }

  refresh() {
    safeEmit();
  }

  void safeEmit() {
    if (!isClosed) {
      emit(state.copyWith());
    }
  }
}
