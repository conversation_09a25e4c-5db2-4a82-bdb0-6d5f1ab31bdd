import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/conten_history_tagwrap_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/content_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/empty_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/error_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/top_search_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/top_title_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewSearchPageView extends StatefulWidget {

  final String? subjectColor;
  final ReviewSearchState state;

  const ReviewSearchPageView({Key? key, required this.state, required this.subjectColor}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ReviewSearchPageViewState();
  }
}

class _ReviewSearchPageViewState extends State<ReviewSearchPageView> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
        ReviewDetailBuriedUtils.appViewScreenWidgetSearch(_ctrl.buriedString);
      } catch (e) {
        l.e("补学助手搜索页", "首页浏览埋点异常");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLoadingV25(
        scene: PageScene.common,
        hideProgress: true,
        exception: widget.state.exception,
        retry: () {},
        backWidget: Positioned(
            top: MediaQuery.of(context).padding.top,
            child: const AppbarLeft()),
        status: widget.state.pageStatus,
        child: Scaffold(
            primary: !JoJoRouter.isWindow,
            resizeToAvoidBottomInset: false,
            appBar: JoJoAppBar(
                widgetTitle: ReviewTopSearchWidget(subjectColor: widget.subjectColor,),
                backgroundColor: Colors.transparent,
                centerTitle: true),
            body: _buildContentView(context)
        ));
  }

  Widget _buildContentView(BuildContext context) {
    ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
    double tooTitleWidgetHeight = 49.rdp;
    if (_ctrl.inSearch) {
      if (_ctrl.isRequesting) {
        return SizedBox(
          child: Align(
            alignment: Alignment.center, // 保持居中
            child: ImageAssetWeb(
              assetName: AssetsImg.SXZ,
              width: 130.rdp,
              height: 100.rdp,
              package: Config.package,
              fit: BoxFit.contain,
            ),
          ),
        );
      } else if (_ctrl.searchStatus == SearchStatus.failed){
        return const ReviewSearchErrorWidget(showInSearch: true,);
      } else if (_ctrl.searchStatus == SearchStatus.emptyResult){
        return LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              children: [
                ReviewTopTitleWidget(isHistory: false, ctrl: _ctrl, height: tooTitleWidgetHeight,),
                ReviewSearchEmptyWidget(height: constraints.maxHeight - tooTitleWidgetHeight,)
              ]
            );
          },
        );
      } else {
        return ReviewContentWidget(ctrl: _ctrl,);
      }
    }
    if (_ctrl.historyList.isEmpty) {
      return Container();
    }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ReviewTopTitleWidget(isHistory: true, ctrl: _ctrl, height: tooTitleWidgetHeight,),
            ReviewSearchTagWrapWidget(tags: _ctrl.historyList,)
          ],
        ),
      ),
    );
  }
}
