import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewTopSearchWidget extends StatefulWidget {

  final String? subjectColor;

  const ReviewTopSearchWidget({
    super.key,
    required this.subjectColor
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewTopSearchWidgetState();
  }
}

class _ReviewTopSearchWidgetState extends State<ReviewTopSearchWidget> {


  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            FocusScope.of(context).requestFocus(pageController?.focusNode);
          }
        });
      } catch (e) {
        l.i("补学助手搜索页", "软键盘主动拉起异常");
      }
    });
  }

  @override
  void dispose() {
    pageController?.focusNode.dispose();
    super.dispose();
  }

  void searchHandle() {
    if (!mounted) return;
    try {
      if (pageController?.isInputting == true) {
        ReviewDetailBuriedUtils.appClickWidgetSearch("搜索课时执行",pageController?.buriedString);
        pageController?.focusNode.unfocus(); // 键盘失去焦点
        pageController?.searchHandle();
      }
    } catch (e) {
      l.e("补学助手搜索页", "关键字搜索异常");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.only(left: 8.rdp, right: 8.rdp),
            height: 36.rdp,
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              color: context.appColors.jColorGray2,
              borderRadius: BorderRadius.circular(18.rdp), // 设置圆角
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ImageAssetWeb(
                  assetName: AssetsImg.PLAN_IMAGE_PLAN_REVIEW_SEARCH_ICON,
                  width: 18.rdp,
                  height: 18.rdp,
                  fit: BoxFit.contain,
                  package: Config.package,
                ),
                SizedBox(width: 10.rdp), // 图标和输入框之间间距
                Expanded(
                  child: TextField(
                    controller: pageController?.textEditingController,
                    textAlign: TextAlign.left,
                    focusNode: pageController?.focusNode,
                    maxLength: 13,
                    textInputAction: TextInputAction.search, // 设置回车键为“搜索”
                    onSubmitted: (value) {
                      if (value.isNotEmpty) {
                        searchHandle();
                      }
                    },
                    buildCounter: (
                        BuildContext context, {
                          required int currentLength,
                          required bool isFocused,
                          required int? maxLength,
                        }) {
                      return null;
                    },
                    decoration: InputDecoration(
                      hintText: S.of(context).searchHintText,
                      hintStyle: TextStyle(
                        fontSize: 14.rdp,
                        color: context.appColors.jColorGray4,
                      ),
                      border: InputBorder.none,
                      isCollapsed: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    onChanged: (value) {
                      setState(() {
                        pageController?.inputTxt = value;
                        pageController?.isInputting = value.isNotEmpty;
                        if (value.isEmpty) {
                          pageController?.getHistoryData();
                          pageController?.inSearch = false;
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: searchHandle,
          child: Container(
            width: 52.rdp,
            height: 44.rdp,
            alignment: Alignment.center,
            child: Text(
              S.of(context).search,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: pageController?.isInputting == true ? context.appColors.jColorGray6 : context.appColors.jColorGray4,
                  fontSize: 16.rdp,
                  fontWeight: FontWeight.w400),
            ),
          ),
        )
      ],
    );
  }

  ReviewSearchCtrl? get pageController => mounted ? context.read<ReviewSearchCtrl>() : null;
}
