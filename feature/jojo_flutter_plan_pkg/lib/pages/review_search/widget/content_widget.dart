import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/widget/course_card_info_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/widget/top_title_widget.dart';

class ReviewContentWidget extends StatefulWidget {

  final ReviewSearchCtrl ctrl;

  const ReviewContentWidget({
    super.key,
    required this.ctrl
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewContentWidgetState();
  }
}

class _ReviewContentWidgetState extends State<ReviewContentWidget> {

  void pullOrRefreshHandle(bool isRefresh) {
    try {
      ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
      if (isRefresh) {
        _ctrl.refreshData();
      } else {
        _ctrl.loadMoreData();
      }
    } catch (e) {
      l.e("补学助手搜索页", isRefresh ? "下拉刷新异常" : "上拉加载异常");
    }
  }

  @override
  void initState() {
    super.initState();
    widget.ctrl.scrollController.addListener(() {
      ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
      _ctrl.focusNode.unfocus(); // 键盘失去焦点
    });
  }

  @override
  void dispose() {
    widget.ctrl.scrollController.removeListener(() { });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double tooTitleWidgetHeight = 49.rdp;
    return Container(
      color: Colors.white,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
              children: [
                ReviewTopTitleWidget(isHistory: false, ctrl: widget.ctrl, height: tooTitleWidgetHeight,),
                SizedBox(
                  height: constraints.maxHeight - tooTitleWidgetHeight,
                  child: PullOrRefresh(
                      refreshController: widget.ctrl.refreshController,
                      noDataText: "",
                      onLoading: () {
                        pullOrRefreshHandle(false);
                      },
                      onRefresh: () {
                        pullOrRefreshHandle(true);
                      },
                      child: ListView.builder(
                        itemCount: widget.ctrl.dataList.length,
                        controller: widget.ctrl.scrollController,
                        padding: EdgeInsets.zero,
                        physics: const ClampingScrollPhysics(),
                        itemBuilder: (context, index) {
                          CourseCard? cardInfo = widget.ctrl.dataList[index];
                          return CourseCardInfoWidget(
                            coursePreCard: null,
                            courseCardInfo: cardInfo,
                            courseNextCard: null,
                            spineResourceInfo: null,
                            playPositionAnimator: false,
                            playCallback: () {},
                            isHomeList: false,
                            ctrl: null,
                            highLightTxt: widget.ctrl.preInputTxt,
                            onShow: (CourseCard? courseCardInfo) {},
                            onClick: (CourseCard? courseCardInfo, bool isFocus) {
                              try {
                                ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
                                _ctrl.focusNode.unfocus(); // 键盘失去焦点
                              } catch (e) {
                                l.e("补学助手搜索页", "课时点击取消焦点异常");
                              }
                              ReviewDetailBuriedUtils.appClickWidget("课时_点击", widget.ctrl.buriedString);
                            },
                          );
                        },
                      )),)
              ]
          );
        },
      ),
    );
  }
}
