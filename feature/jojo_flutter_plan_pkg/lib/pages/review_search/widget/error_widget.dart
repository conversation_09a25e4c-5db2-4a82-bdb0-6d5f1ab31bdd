import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';

class ReviewSearchErrorWidget extends StatefulWidget {

  final bool showInSearch;

  const ReviewSearchErrorWidget({
    super.key,
    required this.showInSearch
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewSearchErrorWidgetState();
  }
}

class _ReviewSearchErrorWidgetState extends State<ReviewSearchErrorWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: JoJoPageLoading(
        status: PageStatus.error,
        retry: () {
          try {
            if (!mounted) return;
            ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
            if (widget.showInSearch) {
              // 搜索失败页面的点击，需要判断关键字是否为空
              if (_ctrl.inputTxt.isNotEmpty) {
                _ctrl.searchHandle();
              } else {
                JoJoToast.showText(S.of(context).searchKeywordEmpty);
              }
            }
          } catch (e) {
            l.e("补学助手搜索页", "请求重试按钮点击异常");
          }
        },
        placeText: S.of(context).reviewPageRequestError,
        child: Container(),
      ),
    );
  }
}
