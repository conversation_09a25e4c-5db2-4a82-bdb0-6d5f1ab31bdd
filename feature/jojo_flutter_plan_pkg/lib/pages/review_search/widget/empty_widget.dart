import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewSearchEmptyWidget extends StatefulWidget {

  final double height;
  const ReviewSearchEmptyWidget({
    super.key,
    required this.height
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewSearchEmptyWidgetState();
  }
}

class _ReviewSearchEmptyWidgetState extends State<ReviewSearchEmptyWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: widget.height,
      child: SizedBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageAssetWeb(
              assetName:  AssetsImg.AFTER_LESSON_EMPTY,
              width: 200.rdp,
              height: 200.rdp,
              package: Config.package,
            ),
            Text(
              S.of(context).noSearchData,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                fontSize: 16.rdp,
                fontWeight: FontWeight.w400,
                color: context.appColors.jColorGray4,
              ),
            )
          ],
        ),
      ),
    );;
  }
}
