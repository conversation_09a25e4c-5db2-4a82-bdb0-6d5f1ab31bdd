import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';

class ReviewSearchTagWrapWidget extends StatefulWidget {

  final List<String> tags;

  const ReviewSearchTagWrapWidget({
    super.key,
    required this.tags
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewSearchTagWrapWidgetState();
  }
}

class _ReviewSearchTagWrapWidgetState extends State<ReviewSearchTagWrapWidget> {

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.rdp,right: 20.rdp),
      child: Wrap(
        spacing: 8.rdp, // 水平间距
        runSpacing: 8.rdp, // 垂直间距
        children: widget.tags.map((tag) {
          String displayText =
          tag.length > 13 ? "${tag.substring(0, 13)}..." : tag;
          return IntrinsicWidth(
            child: GestureDetector(
              onTap: () {
                try {
                  ReviewSearchCtrl _ctrl = context.read<ReviewSearchCtrl>();
                  _ctrl.focusNode.unfocus(); // 键盘失去焦点
                  _ctrl.inputTxt = displayText;
                  _ctrl.textEditingController.text = displayText;
                  _ctrl.isInputting = true;
                  _ctrl.refresh();
                  _ctrl.searchHandle();
                } catch (e) {
                  l.e("补学助手搜索页", "点击历史记录异常");
                }
              },
              child: Container(
                height: 26.rdp,
                constraints: BoxConstraints(minWidth: 44.rdp),
                padding: EdgeInsets.symmetric(horizontal: 10.rdp, vertical: 4.rdp),
                decoration: BoxDecoration(
                  color: context.appColors.jColorGray2, // 背景色
                  borderRadius: BorderRadius.circular(13.rdp), // 圆角
                ),
                child: Center(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      fontSize: 12.rdp,
                      color: context.appColors.jColorGray5,
                      fontWeight: FontWeight.w400,
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: 1,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
