import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewTopTitleWidget extends StatefulWidget {

  final bool isHistory;
  final double height;
  final ReviewSearchCtrl ctrl;

  const ReviewTopTitleWidget({
    required this.isHistory,
    required this.ctrl,
    required this.height,
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewTopTitleWidgetState();
  }
}

class _ReviewTopTitleWidgetState extends State<ReviewTopTitleWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.rdp, right: 4.rdp),
      height: widget.height,
      child: widget.isHistory ? _buildHistoryTopWidget() : _buildSearchResultWidget(),
    );
  }

  Widget _buildHistoryTopWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          S.of(context).recentSearch,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
              color: context.appColors.jColorGray5,
              fontSize: 14.rdp,
              fontWeight: FontWeight.w400),
        ),
        GestureDetector(
          onTap: () {
            if (!mounted) return;
            try {
              ReviewSearchCtrl ctrl = context.read<ReviewSearchCtrl>();
              ctrl.deleteHandle();
            } catch (e) {
              print(e);
            }
          },
          child: Container(
            height: 49.rdp,
            width: 44.rdp,
            alignment: Alignment.center,
            color: Colors.transparent,
            child: ImageAssetWeb(
              assetName: AssetsImg.PLAN_IMAGE_PLAN_REVIEW_SEARCH_DELETE,
              width: 16.rdp,
              height: 16.rdp,
              fit: BoxFit.contain,
              package: Config.package,
            ),
          ),
        )
      ],

    );
  }

  Widget _buildSearchResultWidget() {
    return Container(
      alignment: Alignment.centerLeft,
      child: Text(
        "搜索结果 ${widget.ctrl.totalLessonNum}",
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
            color: context.appColors.jColorGray5,
            fontSize: 14.rdp,
            fontWeight: FontWeight.w400),
      ),
    );
  }
}
