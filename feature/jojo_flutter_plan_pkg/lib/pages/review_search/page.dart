import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/view.dart';

class ReviewSearchPageModel extends BasePage {

  final String? subjectColor;
  final String? courseKey;
  final String? classId;
  final String? buriedString;

  const ReviewSearchPageModel({
    Key? key,
    required this.classId,
    required this.courseKey,
    required this.buriedString,
    required this.subjectColor}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ReviewSearchPageModelState();
}

class _ReviewSearchPageModelState extends BaseState<ReviewSearchPageModel> with BasicInitPage {
  ReviewSearchCtrl? _ctrl;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ctrl?.getHistoryData();
    });
  }

  @override
  void onResume() {
    super.onResume();
    try {
      ReviewSearchCtrl ctrl = context.read<ReviewSearchCtrl>();
      // 如果列表有数据，那么需要更新列表的数据
      if (ctrl.dataList.isNotEmpty && ctrl.preInputTxt.isNotEmpty) {
        ctrl.refreshData();
      }
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) {
        return _ctrl ??= ReviewSearchCtrl(
            subjectColor: widget.subjectColor,
            courseKey: widget.courseKey,
            classId: widget.classId,
            buriedString: widget.buriedString);
      },
      child: BlocBuilder<ReviewSearchCtrl, ReviewSearchState>(builder: (context, state) {
        return ReviewSearchPageView(state: state, subjectColor: widget.subjectColor,);
      }),
    );
  }
}
