// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'evaluation_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

EvaluationListData _$EvaluationListDataFromJson(Map<String, dynamic> json) {
  return _EvaluationListData.fromJson(json);
}

/// @nodoc
mixin _$EvaluationListData {
  String? get courseSegmentName => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  List<EvaluationData>? get evaluationList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EvaluationListDataCopyWith<EvaluationListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EvaluationListDataCopyWith<$Res> {
  factory $EvaluationListDataCopyWith(
          EvaluationListData value, $Res Function(EvaluationListData) then) =
      _$EvaluationListDataCopyWithImpl<$Res, EvaluationListData>;
  @useResult
  $Res call(
      {String? courseSegmentName,
      int? courseType,
      int? classId,
      List<EvaluationData>? evaluationList});
}

/// @nodoc
class _$EvaluationListDataCopyWithImpl<$Res, $Val extends EvaluationListData>
    implements $EvaluationListDataCopyWith<$Res> {
  _$EvaluationListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentName = freezed,
    Object? courseType = freezed,
    Object? classId = freezed,
    Object? evaluationList = freezed,
  }) {
    return _then(_value.copyWith(
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluationList: freezed == evaluationList
          ? _value.evaluationList
          : evaluationList // ignore: cast_nullable_to_non_nullable
              as List<EvaluationData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EvaluationListDataCopyWith<$Res>
    implements $EvaluationListDataCopyWith<$Res> {
  factory _$$_EvaluationListDataCopyWith(_$_EvaluationListData value,
          $Res Function(_$_EvaluationListData) then) =
      __$$_EvaluationListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseSegmentName,
      int? courseType,
      int? classId,
      List<EvaluationData>? evaluationList});
}

/// @nodoc
class __$$_EvaluationListDataCopyWithImpl<$Res>
    extends _$EvaluationListDataCopyWithImpl<$Res, _$_EvaluationListData>
    implements _$$_EvaluationListDataCopyWith<$Res> {
  __$$_EvaluationListDataCopyWithImpl(
      _$_EvaluationListData _value, $Res Function(_$_EvaluationListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentName = freezed,
    Object? courseType = freezed,
    Object? classId = freezed,
    Object? evaluationList = freezed,
  }) {
    return _then(_$_EvaluationListData(
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluationList: freezed == evaluationList
          ? _value._evaluationList
          : evaluationList // ignore: cast_nullable_to_non_nullable
              as List<EvaluationData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EvaluationListData implements _EvaluationListData {
  const _$_EvaluationListData(
      {this.courseSegmentName,
      this.courseType,
      this.classId,
      final List<EvaluationData>? evaluationList})
      : _evaluationList = evaluationList;

  factory _$_EvaluationListData.fromJson(Map<String, dynamic> json) =>
      _$$_EvaluationListDataFromJson(json);

  @override
  final String? courseSegmentName;
  @override
  final int? courseType;
  @override
  final int? classId;
  final List<EvaluationData>? _evaluationList;
  @override
  List<EvaluationData>? get evaluationList {
    final value = _evaluationList;
    if (value == null) return null;
    if (_evaluationList is EqualUnmodifiableListView) return _evaluationList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'EvaluationListData(courseSegmentName: $courseSegmentName, courseType: $courseType, classId: $classId, evaluationList: $evaluationList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EvaluationListData &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            const DeepCollectionEquality()
                .equals(other._evaluationList, _evaluationList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseSegmentName, courseType,
      classId, const DeepCollectionEquality().hash(_evaluationList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EvaluationListDataCopyWith<_$_EvaluationListData> get copyWith =>
      __$$_EvaluationListDataCopyWithImpl<_$_EvaluationListData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EvaluationListDataToJson(
      this,
    );
  }
}

abstract class _EvaluationListData implements EvaluationListData {
  const factory _EvaluationListData(
      {final String? courseSegmentName,
      final int? courseType,
      final int? classId,
      final List<EvaluationData>? evaluationList}) = _$_EvaluationListData;

  factory _EvaluationListData.fromJson(Map<String, dynamic> json) =
      _$_EvaluationListData.fromJson;

  @override
  String? get courseSegmentName;
  @override
  int? get courseType;
  @override
  int? get classId;
  @override
  List<EvaluationData>? get evaluationList;
  @override
  @JsonKey(ignore: true)
  _$$_EvaluationListDataCopyWith<_$_EvaluationListData> get copyWith =>
      throw _privateConstructorUsedError;
}
