// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'evaluation_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

EvaluationData _$EvaluationDataFromJson(Map<String, dynamic> json) {
  return _EvaluationData.fromJson(json);
}

/// @nodoc
mixin _$EvaluationData {
  String? get evaluateCover => throw _privateConstructorUsedError; // 测评封面
  String? get evaluateUrl => throw _privateConstructorUsedError; // 测评地址
  int? get evaluateType =>
      throw _privateConstructorUsedError; // 测评类型  阅读力测评1，培生2 ，北师大3
  String? get evaluateStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EvaluationDataCopyWith<EvaluationData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EvaluationDataCopyWith<$Res> {
  factory $EvaluationDataCopyWith(
          EvaluationData value, $Res Function(EvaluationData) then) =
      _$EvaluationDataCopyWithImpl<$Res, EvaluationData>;
  @useResult
  $Res call(
      {String? evaluateCover,
      String? evaluateUrl,
      int? evaluateType,
      String? evaluateStatus});
}

/// @nodoc
class _$EvaluationDataCopyWithImpl<$Res, $Val extends EvaluationData>
    implements $EvaluationDataCopyWith<$Res> {
  _$EvaluationDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? evaluateCover = freezed,
    Object? evaluateUrl = freezed,
    Object? evaluateType = freezed,
    Object? evaluateStatus = freezed,
  }) {
    return _then(_value.copyWith(
      evaluateCover: freezed == evaluateCover
          ? _value.evaluateCover
          : evaluateCover // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluateUrl: freezed == evaluateUrl
          ? _value.evaluateUrl
          : evaluateUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluateType: freezed == evaluateType
          ? _value.evaluateType
          : evaluateType // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluateStatus: freezed == evaluateStatus
          ? _value.evaluateStatus
          : evaluateStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EvaluationDataCopyWith<$Res>
    implements $EvaluationDataCopyWith<$Res> {
  factory _$$_EvaluationDataCopyWith(
          _$_EvaluationData value, $Res Function(_$_EvaluationData) then) =
      __$$_EvaluationDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? evaluateCover,
      String? evaluateUrl,
      int? evaluateType,
      String? evaluateStatus});
}

/// @nodoc
class __$$_EvaluationDataCopyWithImpl<$Res>
    extends _$EvaluationDataCopyWithImpl<$Res, _$_EvaluationData>
    implements _$$_EvaluationDataCopyWith<$Res> {
  __$$_EvaluationDataCopyWithImpl(
      _$_EvaluationData _value, $Res Function(_$_EvaluationData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? evaluateCover = freezed,
    Object? evaluateUrl = freezed,
    Object? evaluateType = freezed,
    Object? evaluateStatus = freezed,
  }) {
    return _then(_$_EvaluationData(
      evaluateCover: freezed == evaluateCover
          ? _value.evaluateCover
          : evaluateCover // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluateUrl: freezed == evaluateUrl
          ? _value.evaluateUrl
          : evaluateUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluateType: freezed == evaluateType
          ? _value.evaluateType
          : evaluateType // ignore: cast_nullable_to_non_nullable
              as int?,
      evaluateStatus: freezed == evaluateStatus
          ? _value.evaluateStatus
          : evaluateStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EvaluationData implements _EvaluationData {
  const _$_EvaluationData(
      {this.evaluateCover,
      this.evaluateUrl,
      this.evaluateType,
      this.evaluateStatus});

  factory _$_EvaluationData.fromJson(Map<String, dynamic> json) =>
      _$$_EvaluationDataFromJson(json);

  @override
  final String? evaluateCover;
// 测评封面
  @override
  final String? evaluateUrl;
// 测评地址
  @override
  final int? evaluateType;
// 测评类型  阅读力测评1，培生2 ，北师大3
  @override
  final String? evaluateStatus;

  @override
  String toString() {
    return 'EvaluationData(evaluateCover: $evaluateCover, evaluateUrl: $evaluateUrl, evaluateType: $evaluateType, evaluateStatus: $evaluateStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EvaluationData &&
            (identical(other.evaluateCover, evaluateCover) ||
                other.evaluateCover == evaluateCover) &&
            (identical(other.evaluateUrl, evaluateUrl) ||
                other.evaluateUrl == evaluateUrl) &&
            (identical(other.evaluateType, evaluateType) ||
                other.evaluateType == evaluateType) &&
            (identical(other.evaluateStatus, evaluateStatus) ||
                other.evaluateStatus == evaluateStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, evaluateCover, evaluateUrl, evaluateType, evaluateStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EvaluationDataCopyWith<_$_EvaluationData> get copyWith =>
      __$$_EvaluationDataCopyWithImpl<_$_EvaluationData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EvaluationDataToJson(
      this,
    );
  }
}

abstract class _EvaluationData implements EvaluationData {
  const factory _EvaluationData(
      {final String? evaluateCover,
      final String? evaluateUrl,
      final int? evaluateType,
      final String? evaluateStatus}) = _$_EvaluationData;

  factory _EvaluationData.fromJson(Map<String, dynamic> json) =
      _$_EvaluationData.fromJson;

  @override
  String? get evaluateCover;
  @override // 测评封面
  String? get evaluateUrl;
  @override // 测评地址
  int? get evaluateType;
  @override // 测评类型  阅读力测评1，培生2 ，北师大3
  String? get evaluateStatus;
  @override
  @JsonKey(ignore: true)
  _$$_EvaluationDataCopyWith<_$_EvaluationData> get copyWith =>
      throw _privateConstructorUsedError;
}
