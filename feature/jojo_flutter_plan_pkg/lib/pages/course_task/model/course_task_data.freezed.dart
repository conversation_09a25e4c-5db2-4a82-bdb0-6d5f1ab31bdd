// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_task_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseTaskData _$CourseTaskDataFromJson(Map<String, dynamic> json) {
  return _CourseTaskData.fromJson(json);
}

/// @nodoc
mixin _$CourseTaskData {
  Guide? get guide => throw _privateConstructorUsedError;
  ChallengeBook? get challengeBook => throw _privateConstructorUsedError;
  ChallengeAccept? get challengeAccept => throw _privateConstructorUsedError;
  AcceptGuide? get acceptGuide => throw _privateConstructorUsedError;
  AcceptGuide? get openRemind => throw _privateConstructorUsedError;
  SetOpenRemind? get setOpenRemind => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get pageTitle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseTaskDataCopyWith<CourseTaskData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseTaskDataCopyWith<$Res> {
  factory $CourseTaskDataCopyWith(
          CourseTaskData value, $Res Function(CourseTaskData) then) =
      _$CourseTaskDataCopyWithImpl<$Res, CourseTaskData>;
  @useResult
  $Res call(
      {Guide? guide,
      ChallengeBook? challengeBook,
      ChallengeAccept? challengeAccept,
      AcceptGuide? acceptGuide,
      AcceptGuide? openRemind,
      SetOpenRemind? setOpenRemind,
      String? courseKey,
      String? pageTitle});

  $GuideCopyWith<$Res>? get guide;
  $ChallengeBookCopyWith<$Res>? get challengeBook;
  $ChallengeAcceptCopyWith<$Res>? get challengeAccept;
  $AcceptGuideCopyWith<$Res>? get acceptGuide;
  $AcceptGuideCopyWith<$Res>? get openRemind;
  $SetOpenRemindCopyWith<$Res>? get setOpenRemind;
}

/// @nodoc
class _$CourseTaskDataCopyWithImpl<$Res, $Val extends CourseTaskData>
    implements $CourseTaskDataCopyWith<$Res> {
  _$CourseTaskDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guide = freezed,
    Object? challengeBook = freezed,
    Object? challengeAccept = freezed,
    Object? acceptGuide = freezed,
    Object? openRemind = freezed,
    Object? setOpenRemind = freezed,
    Object? courseKey = freezed,
    Object? pageTitle = freezed,
  }) {
    return _then(_value.copyWith(
      guide: freezed == guide
          ? _value.guide
          : guide // ignore: cast_nullable_to_non_nullable
              as Guide?,
      challengeBook: freezed == challengeBook
          ? _value.challengeBook
          : challengeBook // ignore: cast_nullable_to_non_nullable
              as ChallengeBook?,
      challengeAccept: freezed == challengeAccept
          ? _value.challengeAccept
          : challengeAccept // ignore: cast_nullable_to_non_nullable
              as ChallengeAccept?,
      acceptGuide: freezed == acceptGuide
          ? _value.acceptGuide
          : acceptGuide // ignore: cast_nullable_to_non_nullable
              as AcceptGuide?,
      openRemind: freezed == openRemind
          ? _value.openRemind
          : openRemind // ignore: cast_nullable_to_non_nullable
              as AcceptGuide?,
      setOpenRemind: freezed == setOpenRemind
          ? _value.setOpenRemind
          : setOpenRemind // ignore: cast_nullable_to_non_nullable
              as SetOpenRemind?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      pageTitle: freezed == pageTitle
          ? _value.pageTitle
          : pageTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GuideCopyWith<$Res>? get guide {
    if (_value.guide == null) {
      return null;
    }

    return $GuideCopyWith<$Res>(_value.guide!, (value) {
      return _then(_value.copyWith(guide: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ChallengeBookCopyWith<$Res>? get challengeBook {
    if (_value.challengeBook == null) {
      return null;
    }

    return $ChallengeBookCopyWith<$Res>(_value.challengeBook!, (value) {
      return _then(_value.copyWith(challengeBook: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ChallengeAcceptCopyWith<$Res>? get challengeAccept {
    if (_value.challengeAccept == null) {
      return null;
    }

    return $ChallengeAcceptCopyWith<$Res>(_value.challengeAccept!, (value) {
      return _then(_value.copyWith(challengeAccept: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AcceptGuideCopyWith<$Res>? get acceptGuide {
    if (_value.acceptGuide == null) {
      return null;
    }

    return $AcceptGuideCopyWith<$Res>(_value.acceptGuide!, (value) {
      return _then(_value.copyWith(acceptGuide: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AcceptGuideCopyWith<$Res>? get openRemind {
    if (_value.openRemind == null) {
      return null;
    }

    return $AcceptGuideCopyWith<$Res>(_value.openRemind!, (value) {
      return _then(_value.copyWith(openRemind: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SetOpenRemindCopyWith<$Res>? get setOpenRemind {
    if (_value.setOpenRemind == null) {
      return null;
    }

    return $SetOpenRemindCopyWith<$Res>(_value.setOpenRemind!, (value) {
      return _then(_value.copyWith(setOpenRemind: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseTaskDataCopyWith<$Res>
    implements $CourseTaskDataCopyWith<$Res> {
  factory _$$_CourseTaskDataCopyWith(
          _$_CourseTaskData value, $Res Function(_$_CourseTaskData) then) =
      __$$_CourseTaskDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Guide? guide,
      ChallengeBook? challengeBook,
      ChallengeAccept? challengeAccept,
      AcceptGuide? acceptGuide,
      AcceptGuide? openRemind,
      SetOpenRemind? setOpenRemind,
      String? courseKey,
      String? pageTitle});

  @override
  $GuideCopyWith<$Res>? get guide;
  @override
  $ChallengeBookCopyWith<$Res>? get challengeBook;
  @override
  $ChallengeAcceptCopyWith<$Res>? get challengeAccept;
  @override
  $AcceptGuideCopyWith<$Res>? get acceptGuide;
  @override
  $AcceptGuideCopyWith<$Res>? get openRemind;
  @override
  $SetOpenRemindCopyWith<$Res>? get setOpenRemind;
}

/// @nodoc
class __$$_CourseTaskDataCopyWithImpl<$Res>
    extends _$CourseTaskDataCopyWithImpl<$Res, _$_CourseTaskData>
    implements _$$_CourseTaskDataCopyWith<$Res> {
  __$$_CourseTaskDataCopyWithImpl(
      _$_CourseTaskData _value, $Res Function(_$_CourseTaskData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guide = freezed,
    Object? challengeBook = freezed,
    Object? challengeAccept = freezed,
    Object? acceptGuide = freezed,
    Object? openRemind = freezed,
    Object? setOpenRemind = freezed,
    Object? courseKey = freezed,
    Object? pageTitle = freezed,
  }) {
    return _then(_$_CourseTaskData(
      guide: freezed == guide
          ? _value.guide
          : guide // ignore: cast_nullable_to_non_nullable
              as Guide?,
      challengeBook: freezed == challengeBook
          ? _value.challengeBook
          : challengeBook // ignore: cast_nullable_to_non_nullable
              as ChallengeBook?,
      challengeAccept: freezed == challengeAccept
          ? _value.challengeAccept
          : challengeAccept // ignore: cast_nullable_to_non_nullable
              as ChallengeAccept?,
      acceptGuide: freezed == acceptGuide
          ? _value.acceptGuide
          : acceptGuide // ignore: cast_nullable_to_non_nullable
              as AcceptGuide?,
      openRemind: freezed == openRemind
          ? _value.openRemind
          : openRemind // ignore: cast_nullable_to_non_nullable
              as AcceptGuide?,
      setOpenRemind: freezed == setOpenRemind
          ? _value.setOpenRemind
          : setOpenRemind // ignore: cast_nullable_to_non_nullable
              as SetOpenRemind?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      pageTitle: freezed == pageTitle
          ? _value.pageTitle
          : pageTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseTaskData
    with DiagnosticableTreeMixin
    implements _CourseTaskData {
  const _$_CourseTaskData(
      {this.guide,
      this.challengeBook,
      this.challengeAccept,
      this.acceptGuide,
      this.openRemind,
      this.setOpenRemind,
      this.courseKey,
      this.pageTitle});

  factory _$_CourseTaskData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseTaskDataFromJson(json);

  @override
  final Guide? guide;
  @override
  final ChallengeBook? challengeBook;
  @override
  final ChallengeAccept? challengeAccept;
  @override
  final AcceptGuide? acceptGuide;
  @override
  final AcceptGuide? openRemind;
  @override
  final SetOpenRemind? setOpenRemind;
  @override
  final String? courseKey;
  @override
  final String? pageTitle;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CourseTaskData(guide: $guide, challengeBook: $challengeBook, challengeAccept: $challengeAccept, acceptGuide: $acceptGuide, openRemind: $openRemind, setOpenRemind: $setOpenRemind, courseKey: $courseKey, pageTitle: $pageTitle)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CourseTaskData'))
      ..add(DiagnosticsProperty('guide', guide))
      ..add(DiagnosticsProperty('challengeBook', challengeBook))
      ..add(DiagnosticsProperty('challengeAccept', challengeAccept))
      ..add(DiagnosticsProperty('acceptGuide', acceptGuide))
      ..add(DiagnosticsProperty('openRemind', openRemind))
      ..add(DiagnosticsProperty('setOpenRemind', setOpenRemind))
      ..add(DiagnosticsProperty('courseKey', courseKey))
      ..add(DiagnosticsProperty('pageTitle', pageTitle));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseTaskData &&
            (identical(other.guide, guide) || other.guide == guide) &&
            (identical(other.challengeBook, challengeBook) ||
                other.challengeBook == challengeBook) &&
            (identical(other.challengeAccept, challengeAccept) ||
                other.challengeAccept == challengeAccept) &&
            (identical(other.acceptGuide, acceptGuide) ||
                other.acceptGuide == acceptGuide) &&
            (identical(other.openRemind, openRemind) ||
                other.openRemind == openRemind) &&
            (identical(other.setOpenRemind, setOpenRemind) ||
                other.setOpenRemind == setOpenRemind) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.pageTitle, pageTitle) ||
                other.pageTitle == pageTitle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      guide,
      challengeBook,
      challengeAccept,
      acceptGuide,
      openRemind,
      setOpenRemind,
      courseKey,
      pageTitle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseTaskDataCopyWith<_$_CourseTaskData> get copyWith =>
      __$$_CourseTaskDataCopyWithImpl<_$_CourseTaskData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseTaskDataToJson(
      this,
    );
  }
}

abstract class _CourseTaskData implements CourseTaskData {
  const factory _CourseTaskData(
      {final Guide? guide,
      final ChallengeBook? challengeBook,
      final ChallengeAccept? challengeAccept,
      final AcceptGuide? acceptGuide,
      final AcceptGuide? openRemind,
      final SetOpenRemind? setOpenRemind,
      final String? courseKey,
      final String? pageTitle}) = _$_CourseTaskData;

  factory _CourseTaskData.fromJson(Map<String, dynamic> json) =
      _$_CourseTaskData.fromJson;

  @override
  Guide? get guide;
  @override
  ChallengeBook? get challengeBook;
  @override
  ChallengeAccept? get challengeAccept;
  @override
  AcceptGuide? get acceptGuide;
  @override
  AcceptGuide? get openRemind;
  @override
  SetOpenRemind? get setOpenRemind;
  @override
  String? get courseKey;
  @override
  String? get pageTitle;
  @override
  @JsonKey(ignore: true)
  _$$_CourseTaskDataCopyWith<_$_CourseTaskData> get copyWith =>
      throw _privateConstructorUsedError;
}

Guide _$GuideFromJson(Map<String, dynamic> json) {
  return _Guide.fromJson(json);
}

/// @nodoc
mixin _$Guide {
  String? get animation => throw _privateConstructorUsedError;
  String? get textOne => throw _privateConstructorUsedError;
  String? get voiceOne => throw _privateConstructorUsedError;
  String? get textTwo => throw _privateConstructorUsedError;
  String? get voiceTwo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GuideCopyWith<Guide> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GuideCopyWith<$Res> {
  factory $GuideCopyWith(Guide value, $Res Function(Guide) then) =
      _$GuideCopyWithImpl<$Res, Guide>;
  @useResult
  $Res call(
      {String? animation,
      String? textOne,
      String? voiceOne,
      String? textTwo,
      String? voiceTwo});
}

/// @nodoc
class _$GuideCopyWithImpl<$Res, $Val extends Guide>
    implements $GuideCopyWith<$Res> {
  _$GuideCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? textOne = freezed,
    Object? voiceOne = freezed,
    Object? textTwo = freezed,
    Object? voiceTwo = freezed,
  }) {
    return _then(_value.copyWith(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      textOne: freezed == textOne
          ? _value.textOne
          : textOne // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceOne: freezed == voiceOne
          ? _value.voiceOne
          : voiceOne // ignore: cast_nullable_to_non_nullable
              as String?,
      textTwo: freezed == textTwo
          ? _value.textTwo
          : textTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceTwo: freezed == voiceTwo
          ? _value.voiceTwo
          : voiceTwo // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GuideCopyWith<$Res> implements $GuideCopyWith<$Res> {
  factory _$$_GuideCopyWith(_$_Guide value, $Res Function(_$_Guide) then) =
      __$$_GuideCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? animation,
      String? textOne,
      String? voiceOne,
      String? textTwo,
      String? voiceTwo});
}

/// @nodoc
class __$$_GuideCopyWithImpl<$Res> extends _$GuideCopyWithImpl<$Res, _$_Guide>
    implements _$$_GuideCopyWith<$Res> {
  __$$_GuideCopyWithImpl(_$_Guide _value, $Res Function(_$_Guide) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? textOne = freezed,
    Object? voiceOne = freezed,
    Object? textTwo = freezed,
    Object? voiceTwo = freezed,
  }) {
    return _then(_$_Guide(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      textOne: freezed == textOne
          ? _value.textOne
          : textOne // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceOne: freezed == voiceOne
          ? _value.voiceOne
          : voiceOne // ignore: cast_nullable_to_non_nullable
              as String?,
      textTwo: freezed == textTwo
          ? _value.textTwo
          : textTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceTwo: freezed == voiceTwo
          ? _value.voiceTwo
          : voiceTwo // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Guide with DiagnosticableTreeMixin implements _Guide {
  const _$_Guide(
      {this.animation,
      this.textOne,
      this.voiceOne,
      this.textTwo,
      this.voiceTwo});

  factory _$_Guide.fromJson(Map<String, dynamic> json) =>
      _$$_GuideFromJson(json);

  @override
  final String? animation;
  @override
  final String? textOne;
  @override
  final String? voiceOne;
  @override
  final String? textTwo;
  @override
  final String? voiceTwo;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Guide(animation: $animation, textOne: $textOne, voiceOne: $voiceOne, textTwo: $textTwo, voiceTwo: $voiceTwo)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'Guide'))
      ..add(DiagnosticsProperty('animation', animation))
      ..add(DiagnosticsProperty('textOne', textOne))
      ..add(DiagnosticsProperty('voiceOne', voiceOne))
      ..add(DiagnosticsProperty('textTwo', textTwo))
      ..add(DiagnosticsProperty('voiceTwo', voiceTwo));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Guide &&
            (identical(other.animation, animation) ||
                other.animation == animation) &&
            (identical(other.textOne, textOne) || other.textOne == textOne) &&
            (identical(other.voiceOne, voiceOne) ||
                other.voiceOne == voiceOne) &&
            (identical(other.textTwo, textTwo) || other.textTwo == textTwo) &&
            (identical(other.voiceTwo, voiceTwo) ||
                other.voiceTwo == voiceTwo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, animation, textOne, voiceOne, textTwo, voiceTwo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GuideCopyWith<_$_Guide> get copyWith =>
      __$$_GuideCopyWithImpl<_$_Guide>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GuideToJson(
      this,
    );
  }
}

abstract class _Guide implements Guide {
  const factory _Guide(
      {final String? animation,
      final String? textOne,
      final String? voiceOne,
      final String? textTwo,
      final String? voiceTwo}) = _$_Guide;

  factory _Guide.fromJson(Map<String, dynamic> json) = _$_Guide.fromJson;

  @override
  String? get animation;
  @override
  String? get textOne;
  @override
  String? get voiceOne;
  @override
  String? get textTwo;
  @override
  String? get voiceTwo;
  @override
  @JsonKey(ignore: true)
  _$$_GuideCopyWith<_$_Guide> get copyWith =>
      throw _privateConstructorUsedError;
}

ChallengeBook _$ChallengeBookFromJson(Map<String, dynamic> json) {
  return _ChallengeBook.fromJson(json);
}

/// @nodoc
mixin _$ChallengeBook {
  String? get nickname => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get titleImg => throw _privateConstructorUsedError;
  String? get sustained => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  List<dynamic>? get avatarList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChallengeBookCopyWith<ChallengeBook> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChallengeBookCopyWith<$Res> {
  factory $ChallengeBookCopyWith(
          ChallengeBook value, $Res Function(ChallengeBook) then) =
      _$ChallengeBookCopyWithImpl<$Res, ChallengeBook>;
  @useResult
  $Res call(
      {String? nickname,
      String? avatarUrl,
      String? titleImg,
      String? sustained,
      String? text,
      List<dynamic>? avatarList});
}

/// @nodoc
class _$ChallengeBookCopyWithImpl<$Res, $Val extends ChallengeBook>
    implements $ChallengeBookCopyWith<$Res> {
  _$ChallengeBookCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? titleImg = freezed,
    Object? sustained = freezed,
    Object? text = freezed,
    Object? avatarList = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      titleImg: freezed == titleImg
          ? _value.titleImg
          : titleImg // ignore: cast_nullable_to_non_nullable
              as String?,
      sustained: freezed == sustained
          ? _value.sustained
          : sustained // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarList: freezed == avatarList
          ? _value.avatarList
          : avatarList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ChallengeBookCopyWith<$Res>
    implements $ChallengeBookCopyWith<$Res> {
  factory _$$_ChallengeBookCopyWith(
          _$_ChallengeBook value, $Res Function(_$_ChallengeBook) then) =
      __$$_ChallengeBookCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      String? avatarUrl,
      String? titleImg,
      String? sustained,
      String? text,
      List<dynamic>? avatarList});
}

/// @nodoc
class __$$_ChallengeBookCopyWithImpl<$Res>
    extends _$ChallengeBookCopyWithImpl<$Res, _$_ChallengeBook>
    implements _$$_ChallengeBookCopyWith<$Res> {
  __$$_ChallengeBookCopyWithImpl(
      _$_ChallengeBook _value, $Res Function(_$_ChallengeBook) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? titleImg = freezed,
    Object? sustained = freezed,
    Object? text = freezed,
    Object? avatarList = freezed,
  }) {
    return _then(_$_ChallengeBook(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      titleImg: freezed == titleImg
          ? _value.titleImg
          : titleImg // ignore: cast_nullable_to_non_nullable
              as String?,
      sustained: freezed == sustained
          ? _value.sustained
          : sustained // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarList: freezed == avatarList
          ? _value._avatarList
          : avatarList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ChallengeBook with DiagnosticableTreeMixin implements _ChallengeBook {
  const _$_ChallengeBook(
      {this.nickname,
      this.avatarUrl,
      this.titleImg,
      this.sustained,
      this.text,
      final List<dynamic>? avatarList})
      : _avatarList = avatarList;

  factory _$_ChallengeBook.fromJson(Map<String, dynamic> json) =>
      _$$_ChallengeBookFromJson(json);

  @override
  final String? nickname;
  @override
  final String? avatarUrl;
  @override
  final String? titleImg;
  @override
  final String? sustained;
  @override
  final String? text;
  final List<dynamic>? _avatarList;
  @override
  List<dynamic>? get avatarList {
    final value = _avatarList;
    if (value == null) return null;
    if (_avatarList is EqualUnmodifiableListView) return _avatarList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ChallengeBook(nickname: $nickname, avatarUrl: $avatarUrl, titleImg: $titleImg, sustained: $sustained, text: $text, avatarList: $avatarList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ChallengeBook'))
      ..add(DiagnosticsProperty('nickname', nickname))
      ..add(DiagnosticsProperty('avatarUrl', avatarUrl))
      ..add(DiagnosticsProperty('titleImg', titleImg))
      ..add(DiagnosticsProperty('sustained', sustained))
      ..add(DiagnosticsProperty('text', text))
      ..add(DiagnosticsProperty('avatarList', avatarList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChallengeBook &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.titleImg, titleImg) ||
                other.titleImg == titleImg) &&
            (identical(other.sustained, sustained) ||
                other.sustained == sustained) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality()
                .equals(other._avatarList, _avatarList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickname, avatarUrl, titleImg,
      sustained, text, const DeepCollectionEquality().hash(_avatarList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChallengeBookCopyWith<_$_ChallengeBook> get copyWith =>
      __$$_ChallengeBookCopyWithImpl<_$_ChallengeBook>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ChallengeBookToJson(
      this,
    );
  }
}

abstract class _ChallengeBook implements ChallengeBook {
  const factory _ChallengeBook(
      {final String? nickname,
      final String? avatarUrl,
      final String? titleImg,
      final String? sustained,
      final String? text,
      final List<dynamic>? avatarList}) = _$_ChallengeBook;

  factory _ChallengeBook.fromJson(Map<String, dynamic> json) =
      _$_ChallengeBook.fromJson;

  @override
  String? get nickname;
  @override
  String? get avatarUrl;
  @override
  String? get titleImg;
  @override
  String? get sustained;
  @override
  String? get text;
  @override
  List<dynamic>? get avatarList;
  @override
  @JsonKey(ignore: true)
  _$$_ChallengeBookCopyWith<_$_ChallengeBook> get copyWith =>
      throw _privateConstructorUsedError;
}

ChallengeAccept _$ChallengeAcceptFromJson(Map<String, dynamic> json) {
  return _ChallengeAccept.fromJson(json);
}

/// @nodoc
mixin _$ChallengeAccept {
  String? get beforeText => throw _privateConstructorUsedError;
  String? get afterText => throw _privateConstructorUsedError;
  String? get beforeVoice => throw _privateConstructorUsedError;
  String? get afterVoice => throw _privateConstructorUsedError;
  String? get stamp => throw _privateConstructorUsedError;
  String? get animation => throw _privateConstructorUsedError;
  String? get voice => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChallengeAcceptCopyWith<ChallengeAccept> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChallengeAcceptCopyWith<$Res> {
  factory $ChallengeAcceptCopyWith(
          ChallengeAccept value, $Res Function(ChallengeAccept) then) =
      _$ChallengeAcceptCopyWithImpl<$Res, ChallengeAccept>;
  @useResult
  $Res call(
      {String? beforeText,
      String? afterText,
      String? beforeVoice,
      String? afterVoice,
      String? stamp,
      String? animation,
      String? voice});
}

/// @nodoc
class _$ChallengeAcceptCopyWithImpl<$Res, $Val extends ChallengeAccept>
    implements $ChallengeAcceptCopyWith<$Res> {
  _$ChallengeAcceptCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beforeText = freezed,
    Object? afterText = freezed,
    Object? beforeVoice = freezed,
    Object? afterVoice = freezed,
    Object? stamp = freezed,
    Object? animation = freezed,
    Object? voice = freezed,
  }) {
    return _then(_value.copyWith(
      beforeText: freezed == beforeText
          ? _value.beforeText
          : beforeText // ignore: cast_nullable_to_non_nullable
              as String?,
      afterText: freezed == afterText
          ? _value.afterText
          : afterText // ignore: cast_nullable_to_non_nullable
              as String?,
      beforeVoice: freezed == beforeVoice
          ? _value.beforeVoice
          : beforeVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      afterVoice: freezed == afterVoice
          ? _value.afterVoice
          : afterVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      stamp: freezed == stamp
          ? _value.stamp
          : stamp // ignore: cast_nullable_to_non_nullable
              as String?,
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ChallengeAcceptCopyWith<$Res>
    implements $ChallengeAcceptCopyWith<$Res> {
  factory _$$_ChallengeAcceptCopyWith(
          _$_ChallengeAccept value, $Res Function(_$_ChallengeAccept) then) =
      __$$_ChallengeAcceptCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? beforeText,
      String? afterText,
      String? beforeVoice,
      String? afterVoice,
      String? stamp,
      String? animation,
      String? voice});
}

/// @nodoc
class __$$_ChallengeAcceptCopyWithImpl<$Res>
    extends _$ChallengeAcceptCopyWithImpl<$Res, _$_ChallengeAccept>
    implements _$$_ChallengeAcceptCopyWith<$Res> {
  __$$_ChallengeAcceptCopyWithImpl(
      _$_ChallengeAccept _value, $Res Function(_$_ChallengeAccept) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beforeText = freezed,
    Object? afterText = freezed,
    Object? beforeVoice = freezed,
    Object? afterVoice = freezed,
    Object? stamp = freezed,
    Object? animation = freezed,
    Object? voice = freezed,
  }) {
    return _then(_$_ChallengeAccept(
      beforeText: freezed == beforeText
          ? _value.beforeText
          : beforeText // ignore: cast_nullable_to_non_nullable
              as String?,
      afterText: freezed == afterText
          ? _value.afterText
          : afterText // ignore: cast_nullable_to_non_nullable
              as String?,
      beforeVoice: freezed == beforeVoice
          ? _value.beforeVoice
          : beforeVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      afterVoice: freezed == afterVoice
          ? _value.afterVoice
          : afterVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      stamp: freezed == stamp
          ? _value.stamp
          : stamp // ignore: cast_nullable_to_non_nullable
              as String?,
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ChallengeAccept
    with DiagnosticableTreeMixin
    implements _ChallengeAccept {
  const _$_ChallengeAccept(
      {this.beforeText,
      this.afterText,
      this.beforeVoice,
      this.afterVoice,
      this.stamp,
      this.animation,
      this.voice});

  factory _$_ChallengeAccept.fromJson(Map<String, dynamic> json) =>
      _$$_ChallengeAcceptFromJson(json);

  @override
  final String? beforeText;
  @override
  final String? afterText;
  @override
  final String? beforeVoice;
  @override
  final String? afterVoice;
  @override
  final String? stamp;
  @override
  final String? animation;
  @override
  final String? voice;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ChallengeAccept(beforeText: $beforeText, afterText: $afterText, beforeVoice: $beforeVoice, afterVoice: $afterVoice, stamp: $stamp, animation: $animation, voice: $voice)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ChallengeAccept'))
      ..add(DiagnosticsProperty('beforeText', beforeText))
      ..add(DiagnosticsProperty('afterText', afterText))
      ..add(DiagnosticsProperty('beforeVoice', beforeVoice))
      ..add(DiagnosticsProperty('afterVoice', afterVoice))
      ..add(DiagnosticsProperty('stamp', stamp))
      ..add(DiagnosticsProperty('animation', animation))
      ..add(DiagnosticsProperty('voice', voice));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChallengeAccept &&
            (identical(other.beforeText, beforeText) ||
                other.beforeText == beforeText) &&
            (identical(other.afterText, afterText) ||
                other.afterText == afterText) &&
            (identical(other.beforeVoice, beforeVoice) ||
                other.beforeVoice == beforeVoice) &&
            (identical(other.afterVoice, afterVoice) ||
                other.afterVoice == afterVoice) &&
            (identical(other.stamp, stamp) || other.stamp == stamp) &&
            (identical(other.animation, animation) ||
                other.animation == animation) &&
            (identical(other.voice, voice) || other.voice == voice));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, beforeText, afterText,
      beforeVoice, afterVoice, stamp, animation, voice);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChallengeAcceptCopyWith<_$_ChallengeAccept> get copyWith =>
      __$$_ChallengeAcceptCopyWithImpl<_$_ChallengeAccept>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ChallengeAcceptToJson(
      this,
    );
  }
}

abstract class _ChallengeAccept implements ChallengeAccept {
  const factory _ChallengeAccept(
      {final String? beforeText,
      final String? afterText,
      final String? beforeVoice,
      final String? afterVoice,
      final String? stamp,
      final String? animation,
      final String? voice}) = _$_ChallengeAccept;

  factory _ChallengeAccept.fromJson(Map<String, dynamic> json) =
      _$_ChallengeAccept.fromJson;

  @override
  String? get beforeText;
  @override
  String? get afterText;
  @override
  String? get beforeVoice;
  @override
  String? get afterVoice;
  @override
  String? get stamp;
  @override
  String? get animation;
  @override
  String? get voice;
  @override
  @JsonKey(ignore: true)
  _$$_ChallengeAcceptCopyWith<_$_ChallengeAccept> get copyWith =>
      throw _privateConstructorUsedError;
}

AcceptGuide _$AcceptGuideFromJson(Map<String, dynamic> json) {
  return _AcceptGuide.fromJson(json);
}

/// @nodoc
mixin _$AcceptGuide {
  String? get animation => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get voice => throw _privateConstructorUsedError;
  String? get dateText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AcceptGuideCopyWith<AcceptGuide> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AcceptGuideCopyWith<$Res> {
  factory $AcceptGuideCopyWith(
          AcceptGuide value, $Res Function(AcceptGuide) then) =
      _$AcceptGuideCopyWithImpl<$Res, AcceptGuide>;
  @useResult
  $Res call({String? animation, String? text, String? voice, String? dateText});
}

/// @nodoc
class _$AcceptGuideCopyWithImpl<$Res, $Val extends AcceptGuide>
    implements $AcceptGuideCopyWith<$Res> {
  _$AcceptGuideCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? text = freezed,
    Object? voice = freezed,
    Object? dateText = freezed,
  }) {
    return _then(_value.copyWith(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      dateText: freezed == dateText
          ? _value.dateText
          : dateText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AcceptGuideCopyWith<$Res>
    implements $AcceptGuideCopyWith<$Res> {
  factory _$$_AcceptGuideCopyWith(
          _$_AcceptGuide value, $Res Function(_$_AcceptGuide) then) =
      __$$_AcceptGuideCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? animation, String? text, String? voice, String? dateText});
}

/// @nodoc
class __$$_AcceptGuideCopyWithImpl<$Res>
    extends _$AcceptGuideCopyWithImpl<$Res, _$_AcceptGuide>
    implements _$$_AcceptGuideCopyWith<$Res> {
  __$$_AcceptGuideCopyWithImpl(
      _$_AcceptGuide _value, $Res Function(_$_AcceptGuide) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? text = freezed,
    Object? voice = freezed,
    Object? dateText = freezed,
  }) {
    return _then(_$_AcceptGuide(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      dateText: freezed == dateText
          ? _value.dateText
          : dateText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AcceptGuide with DiagnosticableTreeMixin implements _AcceptGuide {
  const _$_AcceptGuide({this.animation, this.text, this.voice, this.dateText});

  factory _$_AcceptGuide.fromJson(Map<String, dynamic> json) =>
      _$$_AcceptGuideFromJson(json);

  @override
  final String? animation;
  @override
  final String? text;
  @override
  final String? voice;
  @override
  final String? dateText;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AcceptGuide(animation: $animation, text: $text, voice: $voice, dateText: $dateText)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AcceptGuide'))
      ..add(DiagnosticsProperty('animation', animation))
      ..add(DiagnosticsProperty('text', text))
      ..add(DiagnosticsProperty('voice', voice))
      ..add(DiagnosticsProperty('dateText', dateText));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AcceptGuide &&
            (identical(other.animation, animation) ||
                other.animation == animation) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.voice, voice) || other.voice == voice) &&
            (identical(other.dateText, dateText) ||
                other.dateText == dateText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, animation, text, voice, dateText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AcceptGuideCopyWith<_$_AcceptGuide> get copyWith =>
      __$$_AcceptGuideCopyWithImpl<_$_AcceptGuide>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AcceptGuideToJson(
      this,
    );
  }
}

abstract class _AcceptGuide implements AcceptGuide {
  const factory _AcceptGuide(
      {final String? animation,
      final String? text,
      final String? voice,
      final String? dateText}) = _$_AcceptGuide;

  factory _AcceptGuide.fromJson(Map<String, dynamic> json) =
      _$_AcceptGuide.fromJson;

  @override
  String? get animation;
  @override
  String? get text;
  @override
  String? get voice;
  @override
  String? get dateText;
  @override
  @JsonKey(ignore: true)
  _$$_AcceptGuideCopyWith<_$_AcceptGuide> get copyWith =>
      throw _privateConstructorUsedError;
}

SetOpenRemind _$SetOpenRemindFromJson(Map<String, dynamic> json) {
  return _SetOpenRemind.fromJson(json);
}

/// @nodoc
mixin _$SetOpenRemind {
  String? get text => throw _privateConstructorUsedError;
  String? get leftButton => throw _privateConstructorUsedError;
  String? get rightButton => throw _privateConstructorUsedError;
  String? get calendarTitle => throw _privateConstructorUsedError;
  int? get startDate => throw _privateConstructorUsedError;
  int? get reminderDate => throw _privateConstructorUsedError;
  String? get calendarDesc => throw _privateConstructorUsedError;
  bool? get startLessonRemindSwitch => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SetOpenRemindCopyWith<SetOpenRemind> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetOpenRemindCopyWith<$Res> {
  factory $SetOpenRemindCopyWith(
          SetOpenRemind value, $Res Function(SetOpenRemind) then) =
      _$SetOpenRemindCopyWithImpl<$Res, SetOpenRemind>;
  @useResult
  $Res call(
      {String? text,
      String? leftButton,
      String? rightButton,
      String? calendarTitle,
      int? startDate,
      int? reminderDate,
      String? calendarDesc,
      bool? startLessonRemindSwitch});
}

/// @nodoc
class _$SetOpenRemindCopyWithImpl<$Res, $Val extends SetOpenRemind>
    implements $SetOpenRemindCopyWith<$Res> {
  _$SetOpenRemindCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? leftButton = freezed,
    Object? rightButton = freezed,
    Object? calendarTitle = freezed,
    Object? startDate = freezed,
    Object? reminderDate = freezed,
    Object? calendarDesc = freezed,
    Object? startLessonRemindSwitch = freezed,
  }) {
    return _then(_value.copyWith(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      leftButton: freezed == leftButton
          ? _value.leftButton
          : leftButton // ignore: cast_nullable_to_non_nullable
              as String?,
      rightButton: freezed == rightButton
          ? _value.rightButton
          : rightButton // ignore: cast_nullable_to_non_nullable
              as String?,
      calendarTitle: freezed == calendarTitle
          ? _value.calendarTitle
          : calendarTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as int?,
      reminderDate: freezed == reminderDate
          ? _value.reminderDate
          : reminderDate // ignore: cast_nullable_to_non_nullable
              as int?,
      calendarDesc: freezed == calendarDesc
          ? _value.calendarDesc
          : calendarDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      startLessonRemindSwitch: freezed == startLessonRemindSwitch
          ? _value.startLessonRemindSwitch
          : startLessonRemindSwitch // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SetOpenRemindCopyWith<$Res>
    implements $SetOpenRemindCopyWith<$Res> {
  factory _$$_SetOpenRemindCopyWith(
          _$_SetOpenRemind value, $Res Function(_$_SetOpenRemind) then) =
      __$$_SetOpenRemindCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? text,
      String? leftButton,
      String? rightButton,
      String? calendarTitle,
      int? startDate,
      int? reminderDate,
      String? calendarDesc,
      bool? startLessonRemindSwitch});
}

/// @nodoc
class __$$_SetOpenRemindCopyWithImpl<$Res>
    extends _$SetOpenRemindCopyWithImpl<$Res, _$_SetOpenRemind>
    implements _$$_SetOpenRemindCopyWith<$Res> {
  __$$_SetOpenRemindCopyWithImpl(
      _$_SetOpenRemind _value, $Res Function(_$_SetOpenRemind) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? leftButton = freezed,
    Object? rightButton = freezed,
    Object? calendarTitle = freezed,
    Object? startDate = freezed,
    Object? reminderDate = freezed,
    Object? calendarDesc = freezed,
    Object? startLessonRemindSwitch = freezed,
  }) {
    return _then(_$_SetOpenRemind(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      leftButton: freezed == leftButton
          ? _value.leftButton
          : leftButton // ignore: cast_nullable_to_non_nullable
              as String?,
      rightButton: freezed == rightButton
          ? _value.rightButton
          : rightButton // ignore: cast_nullable_to_non_nullable
              as String?,
      calendarTitle: freezed == calendarTitle
          ? _value.calendarTitle
          : calendarTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as int?,
      reminderDate: freezed == reminderDate
          ? _value.reminderDate
          : reminderDate // ignore: cast_nullable_to_non_nullable
              as int?,
      calendarDesc: freezed == calendarDesc
          ? _value.calendarDesc
          : calendarDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      startLessonRemindSwitch: freezed == startLessonRemindSwitch
          ? _value.startLessonRemindSwitch
          : startLessonRemindSwitch // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SetOpenRemind with DiagnosticableTreeMixin implements _SetOpenRemind {
  const _$_SetOpenRemind(
      {this.text,
      this.leftButton,
      this.rightButton,
      this.calendarTitle,
      this.startDate,
      this.reminderDate,
      this.calendarDesc,
      this.startLessonRemindSwitch});

  factory _$_SetOpenRemind.fromJson(Map<String, dynamic> json) =>
      _$$_SetOpenRemindFromJson(json);

  @override
  final String? text;
  @override
  final String? leftButton;
  @override
  final String? rightButton;
  @override
  final String? calendarTitle;
  @override
  final int? startDate;
  @override
  final int? reminderDate;
  @override
  final String? calendarDesc;
  @override
  final bool? startLessonRemindSwitch;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SetOpenRemind(text: $text, leftButton: $leftButton, rightButton: $rightButton, calendarTitle: $calendarTitle, startDate: $startDate, reminderDate: $reminderDate, calendarDesc: $calendarDesc, startLessonRemindSwitch: $startLessonRemindSwitch)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SetOpenRemind'))
      ..add(DiagnosticsProperty('text', text))
      ..add(DiagnosticsProperty('leftButton', leftButton))
      ..add(DiagnosticsProperty('rightButton', rightButton))
      ..add(DiagnosticsProperty('calendarTitle', calendarTitle))
      ..add(DiagnosticsProperty('startDate', startDate))
      ..add(DiagnosticsProperty('reminderDate', reminderDate))
      ..add(DiagnosticsProperty('calendarDesc', calendarDesc))
      ..add(DiagnosticsProperty(
          'startLessonRemindSwitch', startLessonRemindSwitch));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetOpenRemind &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.leftButton, leftButton) ||
                other.leftButton == leftButton) &&
            (identical(other.rightButton, rightButton) ||
                other.rightButton == rightButton) &&
            (identical(other.calendarTitle, calendarTitle) ||
                other.calendarTitle == calendarTitle) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.reminderDate, reminderDate) ||
                other.reminderDate == reminderDate) &&
            (identical(other.calendarDesc, calendarDesc) ||
                other.calendarDesc == calendarDesc) &&
            (identical(
                    other.startLessonRemindSwitch, startLessonRemindSwitch) ||
                other.startLessonRemindSwitch == startLessonRemindSwitch));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      text,
      leftButton,
      rightButton,
      calendarTitle,
      startDate,
      reminderDate,
      calendarDesc,
      startLessonRemindSwitch);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetOpenRemindCopyWith<_$_SetOpenRemind> get copyWith =>
      __$$_SetOpenRemindCopyWithImpl<_$_SetOpenRemind>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SetOpenRemindToJson(
      this,
    );
  }
}

abstract class _SetOpenRemind implements SetOpenRemind {
  const factory _SetOpenRemind(
      {final String? text,
      final String? leftButton,
      final String? rightButton,
      final String? calendarTitle,
      final int? startDate,
      final int? reminderDate,
      final String? calendarDesc,
      final bool? startLessonRemindSwitch}) = _$_SetOpenRemind;

  factory _SetOpenRemind.fromJson(Map<String, dynamic> json) =
      _$_SetOpenRemind.fromJson;

  @override
  String? get text;
  @override
  String? get leftButton;
  @override
  String? get rightButton;
  @override
  String? get calendarTitle;
  @override
  int? get startDate;
  @override
  int? get reminderDate;
  @override
  String? get calendarDesc;
  @override
  bool? get startLessonRemindSwitch;
  @override
  @JsonKey(ignore: true)
  _$$_SetOpenRemindCopyWith<_$_SetOpenRemind> get copyWith =>
      throw _privateConstructorUsedError;
}
