// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_task_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseTaskData _$$_CourseTaskDataFromJson(Map<String, dynamic> json) =>
    _$_CourseTaskData(
      guide: json['guide'] == null
          ? null
          : Guide.fromJson(json['guide'] as Map<String, dynamic>),
      challengeBook: json['challengeBook'] == null
          ? null
          : ChallengeBook.fromJson(
              json['challengeBook'] as Map<String, dynamic>),
      challengeAccept: json['challengeAccept'] == null
          ? null
          : ChallengeAccept.fromJson(
              json['challengeAccept'] as Map<String, dynamic>),
      acceptGuide: json['acceptGuide'] == null
          ? null
          : AcceptGuide.fromJson(json['acceptGuide'] as Map<String, dynamic>),
      openRemind: json['openRemind'] == null
          ? null
          : AcceptGuide.fromJson(json['openRemind'] as Map<String, dynamic>),
      setOpenRemind: json['setOpenRemind'] == null
          ? null
          : SetOpenRemind.fromJson(
              json['setOpenRemind'] as Map<String, dynamic>),
      courseKey: json['courseKey'] as String?,
      pageTitle: json['pageTitle'] as String?,
    );

Map<String, dynamic> _$$_CourseTaskDataToJson(_$_CourseTaskData instance) =>
    <String, dynamic>{
      'guide': instance.guide,
      'challengeBook': instance.challengeBook,
      'challengeAccept': instance.challengeAccept,
      'acceptGuide': instance.acceptGuide,
      'openRemind': instance.openRemind,
      'setOpenRemind': instance.setOpenRemind,
      'courseKey': instance.courseKey,
      'pageTitle': instance.pageTitle,
    };

_$_Guide _$$_GuideFromJson(Map<String, dynamic> json) => _$_Guide(
      animation: json['animation'] as String?,
      textOne: json['textOne'] as String?,
      voiceOne: json['voiceOne'] as String?,
      textTwo: json['textTwo'] as String?,
      voiceTwo: json['voiceTwo'] as String?,
    );

Map<String, dynamic> _$$_GuideToJson(_$_Guide instance) => <String, dynamic>{
      'animation': instance.animation,
      'textOne': instance.textOne,
      'voiceOne': instance.voiceOne,
      'textTwo': instance.textTwo,
      'voiceTwo': instance.voiceTwo,
    };

_$_ChallengeBook _$$_ChallengeBookFromJson(Map<String, dynamic> json) =>
    _$_ChallengeBook(
      nickname: json['nickname'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      titleImg: json['titleImg'] as String?,
      sustained: json['sustained'] as String?,
      text: json['text'] as String?,
      avatarList: json['avatarList'] as List<dynamic>?,
    );

Map<String, dynamic> _$$_ChallengeBookToJson(_$_ChallengeBook instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'avatarUrl': instance.avatarUrl,
      'titleImg': instance.titleImg,
      'sustained': instance.sustained,
      'text': instance.text,
      'avatarList': instance.avatarList,
    };

_$_ChallengeAccept _$$_ChallengeAcceptFromJson(Map<String, dynamic> json) =>
    _$_ChallengeAccept(
      beforeText: json['beforeText'] as String?,
      afterText: json['afterText'] as String?,
      beforeVoice: json['beforeVoice'] as String?,
      afterVoice: json['afterVoice'] as String?,
      stamp: json['stamp'] as String?,
      animation: json['animation'] as String?,
      voice: json['voice'] as String?,
    );

Map<String, dynamic> _$$_ChallengeAcceptToJson(_$_ChallengeAccept instance) =>
    <String, dynamic>{
      'beforeText': instance.beforeText,
      'afterText': instance.afterText,
      'beforeVoice': instance.beforeVoice,
      'afterVoice': instance.afterVoice,
      'stamp': instance.stamp,
      'animation': instance.animation,
      'voice': instance.voice,
    };

_$_AcceptGuide _$$_AcceptGuideFromJson(Map<String, dynamic> json) =>
    _$_AcceptGuide(
      animation: json['animation'] as String?,
      text: json['text'] as String?,
      voice: json['voice'] as String?,
      dateText: json['dateText'] as String?,
    );

Map<String, dynamic> _$$_AcceptGuideToJson(_$_AcceptGuide instance) =>
    <String, dynamic>{
      'animation': instance.animation,
      'text': instance.text,
      'voice': instance.voice,
      'dateText': instance.dateText,
    };

_$_SetOpenRemind _$$_SetOpenRemindFromJson(Map<String, dynamic> json) =>
    _$_SetOpenRemind(
      text: json['text'] as String?,
      leftButton: json['leftButton'] as String?,
      rightButton: json['rightButton'] as String?,
      calendarTitle: json['calendarTitle'] as String?,
      startDate: json['startDate'] as int?,
      reminderDate: json['reminderDate'] as int?,
      calendarDesc: json['calendarDesc'] as String?,
      startLessonRemindSwitch: json['startLessonRemindSwitch'] as bool?,
    );

Map<String, dynamic> _$$_SetOpenRemindToJson(_$_SetOpenRemind instance) =>
    <String, dynamic>{
      'text': instance.text,
      'leftButton': instance.leftButton,
      'rightButton': instance.rightButton,
      'calendarTitle': instance.calendarTitle,
      'startDate': instance.startDate,
      'reminderDate': instance.reminderDate,
      'calendarDesc': instance.calendarDesc,
      'startLessonRemindSwitch': instance.startLessonRemindSwitch,
    };
