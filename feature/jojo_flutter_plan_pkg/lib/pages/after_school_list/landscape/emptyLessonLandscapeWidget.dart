import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

// 无课时显示的内容
class EmptyLessonLandscapeWidget extends StatelessWidget {
  const EmptyLessonLandscapeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 360.rdp,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.AFTER_LESSON_EMPTY,
            width: 250.rdp,
            height: 250.rdp,
            package: Config.package,
          ),
          Text(
            S.of(context).learningLessonIsEmpty,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              fontSize: 17.5.rdp,
              fontWeight: FontWeight.w400,
              color: context.appColors.jColorGray4,
            ),
          )
        ],
      ),
    );
  }
}
