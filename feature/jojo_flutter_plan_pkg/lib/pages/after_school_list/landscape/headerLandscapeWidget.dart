import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart';

import '../../plan_home_lesson/utils/course_helper.dart';
import '../buried_utils.dart';

/// 表头（横屏版）
class AfterSchoolHeaderLandscapeWidget extends StatelessWidget {
  final Color? mainColor;
  final int subjectType;
  final ClassServerList? item;

  const AfterSchoolHeaderLandscapeWidget({
    Key? key,
    required this.mainColor,
    required this.item,
    required this.subjectType,
  }) : super(key: key);

  /// 老师名字和头像的点击事件回调
  void _onTeacherWidgetTap() {
    JoJoAfterClassSeverBuriedUtils.appClickTeacher(item, subjectType);
  }

  @override
  Widget build(BuildContext context) {
    String subTitle = item?.classTeacher?.teacherTitle ?? "";
    String title = item?.classTitle ?? "";
    if (title.length > 9) {
      title = "${title.substring(0, 9)}...";
    }

    return Container(
      width: double.infinity,
      height: 66.rdp,
      color: Colors.white,
      child: Row(
        children: [
          // 标题文本
          Container(
            margin: EdgeInsets.only(right: 8.rdp),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 22.5.rdp,
                fontWeight:
                    RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                color: context.appColors.jColorGray6,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          // 可选的课程标识文本
          if (CourseHelper().showCourseKey())
            Padding(
              padding: EdgeInsets.only(left: 10.rdp, right: 10.rdp),
              child: Text(
                item?.courseKey ?? "",
                style: TextStyle(
                  fontSize: 16,
                  color: context.appColors.jColorGray6,
                ),
              ),
            ),
          // 老师名称和头像
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Container(
                height: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 10.rdp),
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: _onTeacherWidgetTap,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 老师名字
                      Text(
                        subTitle,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                          fontSize: 17.5.rdp,
                          fontWeight: FontWeight.w400,
                          color: context.appColors.jColorGray5,
                        ),
                      ),
                      // 老师头像
                      Container(
                        margin: EdgeInsets.only(left: 4.rdp),
                        child: ImageNetworkCached(
                          height: 30.rdp,
                          width: 30.rdp,
                          borderRadius: 15.rdp,
                          fit: BoxFit.cover,
                          imageUrl: item?.classTeacher?.teacherAvatar ?? "",
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
