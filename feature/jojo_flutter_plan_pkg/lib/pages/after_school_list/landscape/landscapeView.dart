import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/after_class_service_Controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/click_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/landscape/afterSchoolExtendLandscapeWidget.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/landscape/afterSchoolListLandscapeWidget.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/landscape/emptyLessonLandscapeWidget.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/landscape/headerLandscapeWidget.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart';
import 'package:jojo_flutter_plan_pkg/service/teacher_service_api.dart';

import '../../plan_home/dailytask/dialog/course_dialog_helper.dart';
import '../../plan_home_dispatcher/model/train_add_teacher_vent.dart';
import '../../plan_home_map/model/course_map_home_page_tab_data.dart';
import '../util/dialog_after_helper.dart';
import '../util/sp_util.dart';

import '../../plan_home_map/model/course_after_ads_data.dart';
import '../util/type.dart';

class AfterSchoolListLandscapeView extends StatefulWidget {
  final AfterSchoolListState state;
  final int? loadingScene;
  final int? subjectType;

  const AfterSchoolListLandscapeView(
      {Key? key,
      required this.state,
      required this.subjectType,
      required this.loadingScene})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AfterSchoolListLandscapeViewState();
  }
}

class _AfterSchoolListLandscapeViewState
    extends State<AfterSchoolListLandscapeView> {
  Color? _mainColor; // 科目色
  final ScrollController _scrollController = ScrollController();
  List<ClassServerList>? _classNormalServerList;
  List<ClassServerList>? _classEndClassServerList;
  bool _isExtend = false;
  StreamSubscription<TrainAddTeacherVent>? _addTeacherSubscription;
  Function? _addTeacherDismiss;


  @override
  void initState() {
    super.initState();
    setDataList();
    _addTeacherSubscription = jojoEventBus
        .on<TrainAddTeacherVent>().listen((event) async {
      if (event.reason == CourseTrainAddTeacherDialogHelper.readingReportManual &&
          event.closeType == TrainAddTeacherCloseType.cancel) {
        _addTeacherDismiss?.call();
        _addTeacherSubscription?.pause();
      }
    });
    // 触发弹窗后 resume
    _addTeacherSubscription?.pause();
  }

  @override
  void dispose() {
    _addTeacherSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AfterSchoolListLandscapeView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.state.afterSchoolListBean !=
        oldWidget.state.afterSchoolListBean) {
      setDataList();
    }
    // 没有请求到数据的情况下afterSchoolListBean为空，会导致颜色不对，所以这里需要更新科目色以及显示埋点
    if (_mainColor == null && widget.state.afterSchoolListBean != null) {
      _mainColor = hexToColor(
          widget.state.afterSchoolListBean?.subjectColor ?? "#FF9045");
      JoJoAfterClassSeverBuriedUtils.appViewScreen(widget.subjectType ?? 2,
          widget.state.afterSchoolListBean?.subjectName);
    }
  }

  void setDataList() {
    _classNormalServerList = [];
    _classEndClassServerList = [];
    widget.state.afterSchoolListBean?.classServerList?.forEach((element) {
      if (element.endClass == true) {
        _classEndClassServerList?.add(element);
      } else {
        _classNormalServerList?.add(element);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLoadingV25(
        scene:
            PageScene.fromValue(widget.loadingScene ?? 1) ?? PageScene.common,
        hideProgress: true,
        exception: widget.state.exception,
        retry: () {
          if (mounted) {
            context.read<AfterSchoolListCtrl>().refreshDate();
          }
        },
        backWidget: Positioned(
            top: MediaQuery.of(context).padding.top, child: const AppbarLeft()),
        status: widget.state.pageStatus,
        child: Scaffold(
            primary: !JoJoRouter.isWindow,
            appBar: JoJoAppBar(
                title: widget.state.afterSchoolListBean?.tabTitle ?? "",
                backgroundColor: Colors.transparent,
                centerTitle: true),
            body: SafeArea(
              child: _buildListView(),
            )));
  }

  // 服务列表内容点击
  Future<void> _tapHandle(ServerInfoList serverItem, ClassServerList classInfo) async {
    JoJoAfterClassSeverBuriedUtils.appClickSeverItem(
        widget.subjectType ?? 0,
        serverItem.title ?? "",
        classInfo,
        widget.state.afterSchoolListBean?.subjectName);

    final shouldProceed = await _handleReadingReportTap(serverItem, classInfo);
    if (!shouldProceed) return;

    gotoServer(serverItem, classInfo);
  }

  void gotoServer(ServerInfoList serverItem, ClassServerList classInfo) {
    if (serverItem.route != null) {
      // 针对特殊的几个服务，需要调用后端的接口，处理红点信息
      const List<String> serviceKeys = [
        kReadingReport,
        kClassTeacherComments,
        kClassKnowledge,
        kPraiseList,
        kMessageNotify,
        kBeijingNormalUniversity,
      ];
      if ((serverItem.updateNum ?? 0) > 0 &&
          serviceKeys.contains(serverItem.key)) {
        teacherServiceApis.doUnReadTip(
          serviceKey: serverItem.key ?? '',
          classId: (classInfo.classId ?? 0).toString(),
          courseKey: "",
        );
      }
      RunEnv.jumpLink(serverItem.route ?? "");
    } else {
      l.e("课后总结-服务列表", "router is null or empty");
    }
  }

  /// 针对“学习报告”场景的点击处理逻辑
  Future<bool> _handleReadingReportTap(
      ServerInfoList serverItem, ClassServerList classInfo) async {
    if (serverItem.key == kReadingReport) {
      int clickedCount = await AfterSchoolSpUtil.getAfterSchoolClickedCount(
          kReadingReport, classInfo.classId ?? 0);
      // 保存 点击次数+1
      await AfterSchoolSpUtil.saveAfterSchoolClickedCountIncrement(
          kReadingReport, classInfo.classId ?? 0);
      bool isShowed =
          await AfterSchoolSpUtil.hasShowReadingReportAddTeacherDialog(
              classInfo.classId ?? 0);

      clickedCount += 1; // 读取的是历史点击次数，所以需要+1

      // 同一班期只展示一次
      if (mounted && !isShowed) {
        if (widget.state.afterSchoolListBean?.popupList?.isNotEmpty == true) {
          final list = widget.state.afterSchoolListBean?.popupList
              ?.where((popup) => popup.popupType == 4);
          final ClassPagePopupList? popup = (list != null && list.isNotEmpty) ? list.first : null;

          // 判断是否符合展示弹窗的条件
          if (popup != null &&
              popup.popupType == 4 &&
              (popup.occurrenceIndex ?? 0) <= clickedCount) {
            // 保存弹窗已展示
            await AfterSchoolSpUtil.saveHasShowReadingReportAddTeacherDialog(
                classInfo.classId ?? 0);

            if (popup.showMode == ClassPagePopupList.pageModePopup) {
              // 原加老师弹窗逻辑
              DialogAfterHelper().showAddTeacherDialog(popup,
                  CourseTrainAddTeacherDialogHelper.readingReportManual);

              _addTeacherDismiss = () {
                gotoServer(serverItem, classInfo);
              };
              _addTeacherSubscription?.resume();
            } else {
              // 新加老师页面逻辑
              handleOpenAddTeacherPageClick(popup, serverItem.route, serverInfo: serverItem);
            }
            return false;
          }
        }
      }
    }
    // 正常流程继续
    return true;
  }

  // 服务列表内容浏览
  void _severListView(ServerInfoList serverItem, ClassServerList classInfo) {
    if (serverItem.isBuried != true) {
      serverItem.isBuried = true;
      JoJoAfterClassSeverBuriedUtils.appViewSeverItem(
          widget.subjectType ?? 0,
          serverItem.title ?? "",
          classInfo,
          widget.state.afterSchoolListBean?.subjectName);
    }
  }

  Widget _buildSeverView(ClassServerList item, ServerInfoList severItem) {
    return VisibilityDetector(
        key: Key(
            "${item.classId}_${item.courseId}_${item.classTitle}_${severItem.title}"),
        onVisibilityChanged: (VisibilityInfo info) {
          _severListView(severItem, item);
        },
        child: GestureDetector(
          onTap: () async {
            if (severItem.status == 1) {
              await _tapHandle(severItem, item);
            }
          },
          child: AfterSchoolListLandscapeWidget(
            item: severItem,
            mainColor: _mainColor,
          ),
        ));
  }

  // 组装列表视图
  List<Widget> _buildCellList(
      ClassServerList item, bool showSpace, bool isFirst) {
    List<Widget> list = [];
    // 添加表头信息
    list.add(AfterSchoolHeaderLandscapeWidget(
      item: item,
      mainColor: _mainColor,
      subjectType: widget.subjectType ?? 0,
    ));

    // 添加服务列表信息
    int length = item.serverInfoList?.length ?? 0;
    for (int i = 0; i < length; i += 2) {
      ServerInfoList infoLeft = item.serverInfoList?[i] ?? ServerInfoList();
      ServerInfoList? infoRight;
      if (i + 1 < length) {
        infoRight = item.serverInfoList?[i + 1];
      }
      Widget serverLeft = _buildSeverView(item, infoLeft);
      Widget severRight;
      if (infoRight != null) {
        severRight = _buildSeverView(item, infoRight);
      } else {
        severRight = Container(height: 64.rdp, color: Colors.transparent);
      }
      list.add(Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(child: serverLeft),
          SizedBox(width: 16.rdp),
          Expanded(child: severRight),
        ],
      ));
      if (i + 1 < length) {
        list.add(SizedBox(
          height: 16.rdp,
        ));
      }
    }

    // 添加间距
    if (showSpace) {
      list.add(SizedBox(
        height: 7.rdp,
      ));
    }
    if (isFirst) {
      //添加广告信息
      list.insert(0, _buildAdInfoWidget());
    }
    return list;
  }

  // 展开收起按钮
  Widget _buildExtendWidget(bool hasNoEndLesson) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExtend = !_isExtend;
        });
        JoJoAfterClassSeverBuriedUtils.appClickExtend(widget.subjectType ?? 0,
            widget.state.afterSchoolListBean?.subjectName);
      },
      child: AfterSchoolExtendLandscapeWidget(
        mainColor: _mainColor,
        isExtend: _isExtend,
        hasNoEndLesson: hasNoEndLesson,
      ),
    );
  }

  //广告
  Widget _buildAdInfoWidget() {
    // return Container();
    List<AdvertisementList>? list =
        widget.state.adInfo?.advertisementList ?? [];
    String pictureUrl = '';
    AdvertisementList info = const AdvertisementList();
    if (list.isNotEmpty) {
      pictureUrl = list[0].pictureUrl ?? '';
      info = list[0];
    }
    return pictureUrl.isNotEmpty
        ? Visibility(
            visible: list.isNotEmpty,
            child: VisibilityObserve(
                onShow: () {
                  RunEnv.sensorsTrack('ElementView', {
                    '\$screen_name': '2025改版_课后服务列表',
                    'c_element_name': '阅读学后服务_顶部广告位_曝光',
                    'material_id': info.id,
                    'material_name': info.advertisementName,
                  });
                },
                child: AspectRatio(
                  aspectRatio: 860 / 112,
                  child: GestureDetector(
                    onTap: () {
                      RunEnv.sensorsTrack('\$AppClick', {
                        '\$screen_name': '2025改版_课后服务列表',
                        '\$element_name': '阅读学后服务_顶部广告位_点击',
                        'material_id': info.id,
                        'material_name': info.advertisementName,
                      });
                      RunEnv.jumpLink(info.linkUrl ?? "");
                    },
                    child: ImageNetworkCached(
                      borderRadius: 12.rdp,
                      fit: BoxFit.fill,
                      imageUrl: pictureUrl,
                    ),
                  ),
                )),
          )
        : Container();
  }

  Widget _buildListView() {
    bool showExtendBtn = (_classEndClassServerList?.isNotEmpty ?? false);
    int length = _classNormalServerList?.length ?? 0;

    if (length == 0) {
      length += 1; // 没有进行中的课程时，需要展示空视图
    }
    if (showExtendBtn) {
      length += 1; // 展开按钮
      if (_isExtend) {
        length += _classEndClassServerList?.length ?? 0; // 往期课程
      }
    }
    return Container(
      alignment: Alignment.center,
      child: SizedBox(
        width: 780.rdp,
        child: ListView.builder(
            itemCount: length,
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              int lengthNormal = _classNormalServerList?.length ?? 0;
              // 都是已完结的课，
              if (lengthNormal == 0) {
                if (index == 0) {
                  // 缺省内容
                  return Column(
                    children: [
                      _buildAdInfoWidget(),
                      const EmptyLessonLandscapeWidget()
                    ],
                  );
                } else if (index == 1) {
                  // 展开收起
                  return _buildExtendWidget(true);
                } else {
                  // 往期服务
                  ClassServerList item = _classEndClassServerList?[index - 2] ??
                      const ClassServerList();
                  return Column(
                    children: _buildCellList(item, false, false),
                  );
                }
              } else {
                if (index < lengthNormal) {
                  // 正常课程服务
                  ClassServerList item =
                      _classNormalServerList?[index] ?? const ClassServerList();
                  return Column(
                    children: _buildCellList(
                        item, index == lengthNormal, index == 0 ? true : false),
                  );
                } else if (index == lengthNormal) {
                  // 展开收起
                  return _buildExtendWidget(false);
                } else {
                  // 往期服务
                  ClassServerList item =
                      _classEndClassServerList?[index - lengthNormal - 1] ??
                          const ClassServerList();
                  return Column(
                    children: _buildCellList(item, false, false),
                  );
                }
              }
            }),
      ),
    );
  }
}
