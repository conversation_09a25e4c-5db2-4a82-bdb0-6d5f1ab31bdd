import 'package:jojo_flutter_base/base.dart';

class AfterSchoolSpUtil {
  /// 累加课后服务某项的点击次数
  static saveAfterSchoolClickedCountIncrement(String type, int classId) async {
    UserInfo? info =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);

    String key = "AfterSchoolClickedCount_${info?.uid}_${type}_$classId";
    int nowCount = await getAfterSchoolClickedCount(type, classId);
    jojoNativeBridge.operationNativeValueSet(
        key: key, value: "${nowCount + 1}");
  }

  /// 获取课后服务某项的点击次数
  static getAfterSchoolClickedCount(String type, int classId) async {
    UserInfo? info =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);

    String key = "AfterSchoolClickedCount_${info?.uid}_${type}_$classId";
    NativeValue? count = await jojoNativeBridge
        .operationNativeValueGet(key: key)
        .then((value) => value.data);
    return int.tryParse(count?.value ?? "0") ?? 0;
  }

  /// 是否展示过学习日报添加老师弹窗
  static hasShowReadingReportAddTeacherDialog(int classId) async {
    UserInfo? info =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);

    String key = "hasShowReadingReportAddTeacherDialog_${info?.uid}_$classId";
    NativeValue? value = await jojoNativeBridge
        .operationNativeValueGet(key: key)
        .then((value) => value.data);
    return value?.value == "1";
  }

  /// 保存展示过学习日报添加老师弹窗
  static saveHasShowReadingReportAddTeacherDialog(int classId) async {
    UserInfo? info =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);

    String key = "hasShowReadingReportAddTeacherDialog_${info?.uid}_$classId";
    jojoNativeBridge.operationNativeValueSet(key: key, value: "1");
  }
}
