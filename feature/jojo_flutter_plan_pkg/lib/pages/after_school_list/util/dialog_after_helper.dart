import 'package:jojo_flutter_base/widgets/dialog/sort_dialog_helper.dart';

import '../../plan_home/model/course_home_page_data.dart';
import '../../plan_home_map/model/course_map_home_page_tab_data.dart';
import '../../plan_home_map/widget/dialogs/train_add_teacher_2025_dialog.dart';

class DialogAfterHelper {
  DialogAfterHelper._internal();

  static String tag = "UniformDialogSequenceTag";
  static final DialogAfterHelper _instance = DialogAfterHelper._internal();

  factory DialogAfterHelper() => _instance;

  showAddTeacherDialog(ClassPagePopupList popupData, String? reason) async {
    SchedulePopup schedulePopup = SchedulePopup(
      classId: popupData.classId,
      template: popupData.popupType,
      icon: popupData.icon,
      title: popupData.popupTitle,
      subTitle: popupData.subTitle,
      tags: popupData.tags,
      addTeacherUrl: popupData.addTeacherUrl,
      addButton: popupData.buttonText,
      cancelButton: popupData.cancelButtonText,
      popupType: popupData.popupType,
      subjectTypeDesc: popupData.subjectTypeDesc,
      courseSegment: popupData.courseSegment,
      userCourseBusinessStatus: popupData.userCourseBusinessStatus,
        pattern:popupData.pattern,
      courseKey: popupData.courseKey,
      limitFlow: popupData.limitFlow,
        businessTypeDesc:popupData.businessTypeDesc
    );


    SortDialogHelper().dialogShowExecutor?.call(
          true,
          TrainAddTeacher2025Dialog(
            schedulePopup,
            () => true,
            "2025改版_课后服务列表",
            "学习日报加老师弹窗",
            needHideTabBar: false,
            showBigImage: true,
            reason: reason,
          ),
        );
  }
}
