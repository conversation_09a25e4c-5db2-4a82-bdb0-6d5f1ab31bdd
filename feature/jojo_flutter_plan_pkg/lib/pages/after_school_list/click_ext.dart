import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/util/type.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/ext.dart';

const List<String> serviceKeys = [
  kReadingReport,
  kClassTeacherComments,
  kClassKnowledge,
  kPraiseList,
  kMessageNotify,
  kBeijingNormalUniversity,
];

handleOpenAddTeacherPageClick(ClassPagePopupList popup, String? closeRoute,
    {ServerInfoList? serverInfo}) {
  final link = getTrainAddTeacherRoute(
    materialType: popup.subjectTypeDesc ?? '',
    courseStage: popup.courseSegment ?? '',
    customState: popup.userCourseBusinessStatus ?? '',
    classId: _intToString(popup.classId),
    courseKey: popup.courseKey ?? '',
    materialId: _intToString(popup.pattern),
    serviceKey: _getServiceKey(serverInfo),
    addBtnText: popup.buttonText ?? '',
    closeBtnText: popup.cancelButtonText ?? '',
    addRoute: popup.addTeacherUrl ?? '',
    closeRoute: closeRoute ?? '',
    imageUrl: popup.icon ?? '',
    backgroundColor: popup.bgColor ?? '',
  );
  RunEnv.jumpLink(link);
}

String _getServiceKey(ServerInfoList? serverInfo) {
  if (serverInfo == null) {
    return '';
  }
  if ((serverInfo.updateNum ?? 0) > 0 && serviceKeys.contains(serverInfo.key)) {
    return serverInfo.key ?? '';
  }
  return '';
}

String _intToString(int? value) {
  if (value == null) {
    return '';
  }
  return '$value';
}
