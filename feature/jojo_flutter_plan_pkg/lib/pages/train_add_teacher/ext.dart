
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_plan_pkg/common/config/address.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';

const trainAddTeacherTag = "训练营添加老师页面";

String getTrainAddTeacherRoute({
  required String materialType,
  required String courseStage,
  required String customState,
  required String classId,
  required String courseKey,
  required String materialId,
  required String serviceKey,
  required String addBtnText,
  required String closeBtnText,
  required String addRoute,
  required String closeRoute,
  required String imageUrl,
  required String backgroundColor,
}) {
  var uri = Uri.parse('${Address.appFlutter}${AppPage.trainAddTeacherPage.path}');
  final Map<String, String> queryParameters = {};

  if (TextUtil.isNotEmpty(materialType)) {
    queryParameters['materialType'] = materialType;
  }
  if (TextUtil.isNotEmpty(courseStage)) {
    queryParameters['courseStage'] = courseStage;
  }
  if (TextUtil.isNotEmpty(customState)) {
    queryParameters['customState'] = customState;
  }
  if (TextUtil.isNotEmpty(classId)) {
    queryParameters['classId'] = classId;
  }
  if (TextUtil.isNotEmpty(courseKey)) {
    queryParameters['courseKey'] = courseKey;
  }
  if (TextUtil.isNotEmpty(materialId)) {
    queryParameters['materialId'] = materialId;
  }
  if (TextUtil.isNotEmpty(serviceKey)) {
    queryParameters['serviceKey'] = serviceKey;
  }
  if (TextUtil.isNotEmpty(addBtnText)) {
    queryParameters['addBtnText'] = addBtnText;
  }
  if (TextUtil.isNotEmpty(closeBtnText)) {
    queryParameters['closeBtnText'] = closeBtnText;
  }
  if (TextUtil.isNotEmpty(addRoute)) {
    queryParameters['addRoute'] = addRoute;
  }
  if (TextUtil.isNotEmpty(closeRoute)) {
    queryParameters['closeRoute'] = closeRoute;
  }
  if (TextUtil.isNotEmpty(imageUrl)) {
    queryParameters['imageUrl'] = imageUrl;
  }
  if (TextUtil.isNotEmpty(backgroundColor)) {
    queryParameters['backgroundColor'] = backgroundColor;
  }

  queryParameters['windowType'] = 'window';

  if (queryParameters.isNotEmpty) {
    uri = uri.replace(queryParameters: queryParameters);
  }

  return uri.toString();
}

String getValue(Map<String, String>? params, String key, Printer printer) {
  final value = params?[key] ?? "";
  if (TextUtil.isEmpty(value)) {
    printer.e(trainAddTeacherTag, "_getValue 出现空值 key=$key");
    return "";
  }
  return value;
}

const logPrinter = LogPrinter();

abstract class Printer {
  const Printer();
  e(String tag, String message);
}

class LogPrinter extends Printer {
  const LogPrinter();

  @override
  e(String tag, String message) {
    l.e(tag, message);
  }
}

class TestPrinter extends Printer {
  const TestPrinter();
  @override
  e(String tag, String message) {
    print("tag=$tag, message=$message");
  }
}