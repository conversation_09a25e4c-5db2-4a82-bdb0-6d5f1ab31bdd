import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/color_util.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

class TrainAddTeacherState {
  String materialType; // 埋点
  String courseStage; // 埋点
  String customState; // 埋点
  String classId; // 埋点 + 关闭跳转路由 - 针对特殊的几个服务，需要调用后端的接口，处理红点信息
  String courseKey; // 埋点 + 关闭跳转路由 - 针对特殊的几个服务，需要调用后端的接口，处理红点信息
  String materialId;

  String serviceKey; // 关闭跳转路由 - 针对特殊的几个服务，需要调用后端的接口，处理红点信息

  String addRoute; // 添加跳转路由
  String closeRoute; // 关闭跳转路由
  String imageUrl; // 图片链接
  String backgroundColor; // 背景颜色
  String addBtnText; // 添加按钮文案
  String closeBtnText; // 关闭按钮文案

  String localImagePath;

  PageStatus pageStatus;
  Exception? exception;

  TrainAddTeacherState({
    required this.materialType,
    required this.courseStage,
    required this.customState,
    required this.classId,
    required this.courseKey,
    required this.materialId,
    required this.serviceKey,
    required this.addRoute,
    required this.closeRoute,
    required this.addBtnText,
    required this.closeBtnText,
    required this.imageUrl,
    required this.backgroundColor,
    required this.pageStatus,
    required this.exception,
    required this.localImagePath,
  });

  TrainAddTeacherState copyWith() {
    return TrainAddTeacherState(
      materialType: materialType,
      courseStage: courseStage,
      customState: customState,
      classId: classId,
      courseKey: courseKey,
      materialId: materialId,
      serviceKey: serviceKey,
      addRoute: addRoute,
      closeRoute: closeRoute,
      addBtnText: addBtnText,
      closeBtnText: closeBtnText,
      imageUrl: imageUrl,
      backgroundColor: backgroundColor,
      pageStatus: pageStatus,
      exception: exception,
      localImagePath: localImagePath,
    );
  }

  static TrainAddTeacherState fake() {
    return TrainAddTeacherState(
      materialType: 'materialType',
      courseStage: 'courseStage',
      customState: 'customState',
      classId: 'classId',
      courseKey: 'courseKey',
      materialId: 'materialId',
      serviceKey: 'serviceKey',
      addRoute: 'addRoute',
      closeRoute: 'closeRoute',
      addBtnText: 'addBtnText',
      closeBtnText: 'closeBtnText',
      imageUrl: 'https://jojopublicfat.jojoread.com/cms/jaguar-admin/front-resource/807262853443518465.png',
      backgroundColor: "#FFF000",
      pageStatus: PageStatus.success,
      exception: Exception("fjsadklfjasljfg"),
      localImagePath: '',
    );
  }
}
