import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/train_add_teacher_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/train_add_teacher_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/view/train_add_teacher_view.dart';

class TrainAddTeacherPage extends BasePage {
  final int? loadingScene;
  final Map<String, String>? params;

  const TrainAddTeacherPage({
    required this.loadingScene,
    required this.params,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _TrainAddTeacherPageState();
}

class _TrainAddTeacherPageState extends BaseState<TrainAddTeacherPage> {
  late TrainAddTeacherController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TrainAddTeacherController(widget.params);
  }

  @override
  Widget build(BuildContext context) {
    final safeAreaHandleBottom = JoJoRouter.isWindow || !RunEnv.isIOS;
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<TrainAddTeacherController, TrainAddTeacherState>(
          builder: (context, state) {
        return JoJoPageLoadingV25(
          scene:
              PageScene.fromValue(widget.loadingScene ?? 1) ?? PageScene.common,
          hideProgress: true,
          exception: _controller.state.exception,
          retry: _retry,
          backWidget: Positioned(
              top: MediaQuery.of(context).padding.top,
              child: AppbarLeft(
                onBack: (handler) {
                  _backClick();
                  handler?.call();
                },
              )),
          status: _controller.state.pageStatus,
          child: Scaffold(
              primary: !JoJoRouter.isWindow,
              appBar: JoJoAppBar(
                  title: S.of(context).trainAddTeacherPageTitle,
                  backgroundColor: Colors.white,
                  centerTitle: true),
              body: SafeArea(
                bottom: safeAreaHandleBottom,
                child: VisibilityObserve(
                  onShow: () => _show(),
                  child: TrainAddTeacherView(
                    appendBottomPadding: safeAreaHandleBottom ? 0.0 : MediaQuery.of(context).padding.bottom,
                    addBtnText: _controller.state.addBtnText,
                    closeBtnText: _controller.state.closeBtnText,
                    backgroundColor: _controller.state.backgroundColor,
                    localImagePath: _controller.state.localImagePath,
                    addCallback: _addClick,
                    closeCallback: _closeClick,
                  ),
                ),
              )),
        );
      }),
    );
  }

  _retry() {
    _controller.downloadImage();
  }

  _show() {
    _controller.show();
  }

  _backClick() {
    _controller.back();
  }

  _addClick() {
    _controller.jumpToAddTeacher();
  }

  _closeClick() {
    _controller.jumpToWatchReport();
  }
}
