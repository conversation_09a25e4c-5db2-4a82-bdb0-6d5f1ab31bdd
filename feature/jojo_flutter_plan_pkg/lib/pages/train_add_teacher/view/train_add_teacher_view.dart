import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';

class TrainAddTeacherView extends StatefulWidget {
  final String addBtnText;
  final String closeBtnText;
  final String localImagePath;
  final String backgroundColor;
  final VoidCallback addCallback;
  final VoidCallback closeCallback;
  final double appendBottomPadding;

  const TrainAddTeacherView({
    super.key,
    required this.addBtnText,
    required this.closeBtnText,
    required this.localImagePath,
    required this.backgroundColor,
    required this.addCallback,
    required this.closeCallback,
    this.appendBottomPadding = 0.0,
  });

  @override
  State<TrainAddTeacherView> createState() => _TrainAddTeacherViewState();
}

class _TrainAddTeacherViewState extends State<TrainAddTeacherView> {
  Color _getBackgroundColor() {
    final backgroundColor = widget.backgroundColor;
    if (TextUtil.isNotEmpty(backgroundColor)) {
      return HexColor(backgroundColor);
    }
    return Colors.white;
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: _getBackgroundColor(),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Image.file(
                File(widget.localImagePath?? ''),
                errorBuilder: (context, error, stacktrace) => const SizedBox.shrink(),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(height: 14.rdp),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => widget.addCallback(),
                child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(30.rdp)),
                      color: context.appColors.mainColor,
                    ),
                    width: 280.rdp,
                    height: 44.rdp,
                    child: Text(
                      widget.addBtnText,
                      style: TextStyle(
                        fontSize: 18.rdp,
                        color: context.appColors.textColor,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    )),
              ),
              SizedBox(height: 16.rdp),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => widget.closeCallback(),
                child: Container(
                    alignment: Alignment.center,
                    width: 280.rdp,
                    height: 27.rdp,
                    child: Text(
                      widget.closeBtnText,
                      style: TextStyle(
                        fontSize: 18.rdp,
                        color: context.appColors.jColorGray5,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    )),
              ),
              SizedBox(height: 30.rdp + widget.appendBottomPadding),
            ],
          )
        ],
      ),
    );
  }
}
