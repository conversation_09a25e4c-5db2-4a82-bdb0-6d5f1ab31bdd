import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/text_util.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/click_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/train_add_teacher_state.dart';
import 'package:jojo_flutter_plan_pkg/service/teacher_service_api.dart';

class TrainAddTeacherController extends Cubit<TrainAddTeacherState> {
  final Printer printer;
  final HostEnv? hostEnv;
  final TeacherServiceApi? teacherApi;
  final AbsDownloadManager? downloadManager;

  TrainAddTeacherController(Map<String, String>? params,
      {this.printer = logPrinter, this.hostEnv, this.teacherApi, this.downloadManager})
      : super(TrainAddTeacherState(
          materialType: getValue(params, "materialType", printer),
          courseStage: getValue(params, "courseStage", printer),
          customState: getValue(params, "customState", printer),
          classId: getValue(params, "classId", printer),
          courseKey: getValue(params, "courseKey", printer),
          materialId: getValue(params, "materialId", printer),
          serviceKey: getValue(params, "serviceKey", printer),
          addRoute: getValue(params, "addRoute", printer),
          closeRoute: getValue(params, "closeRoute", printer),
          addBtnText: getValue(params, "addBtnText", printer),
          closeBtnText: getValue(params, "closeBtnText", printer),
          imageUrl: getValue(params, "imageUrl", printer),
          backgroundColor: getValue(params, "backgroundColor", printer),
          pageStatus: PageStatus.loading,
          exception: null,
          localImagePath: "",
        )) {
    downloadImage();
  }

  downloadImage() {
    if (state.pageStatus != PageStatus.loading) {
      final newState = state.copyWith();
      newState.pageStatus = PageStatus.loading;
      emit(newState);
    }

    (downloadManager ?? JoJoResourceManager()).downloadUrl([state.imageUrl], successListener: (map) {
      final localImagePath = map[state.imageUrl] ?? "";

      if (TextUtil.isNotEmpty(localImagePath)) {
        final newState = state.copyWith();
        newState.pageStatus = PageStatus.success;
        newState.localImagePath = localImagePath;
        emit(newState);
      } else {
        final newState = state.copyWith();
        newState.pageStatus = PageStatus.error;
        newState.exception = Exception('图片下载失败 url=$state.imageUrl');
        emit(newState);
      }
    }, failListener: (url) {
      final newState = state.copyWith();
      newState.pageStatus = PageStatus.error;
      newState.exception = Exception('图片下载失败 url=$url');
      emit(newState);
    });
  }

  show() {
    _getEnv().sensorsTrack(
      '\$AppViewScreen',
      _generateBuriedPointParams(),
    );
  }

  back() {
    _getEnv().sensorsTrack(
      '\$AppClick',
      _generateBuriedPointParams()..['\$element_name'] = '学习日报加老师页_点击返回按钮',
    );
  }

  jumpToAddTeacher() {
    _getEnv().sensorsTrack(
      '\$AppClick',
      _generateBuriedPointParams()..['\$element_name'] = '学习日报加老师页_点击去添加按钮',
    );
    jojoNativeBridge.closeAndJump(url: state.addRoute);
  }

  jumpToWatchReport() {
    _getEnv().sensorsTrack(
      '\$AppClick',
      _generateBuriedPointParams()..['\$element_name'] = '学习日报加老师页_点击关闭按钮',
    );
    if (TextUtil.isNotEmpty(state.serviceKey)) {
      _consumeReportRedPoint();
    }
    jojoNativeBridge.closeAndJump(url: state.closeRoute);
  }

  HostEnv _getEnv() {
    return hostEnv ?? RunEnv;
  }

  _consumeReportRedPoint() {
    if (serviceKeys.contains(state.serviceKey)) {
      (teacherApi ?? teacherServiceApis).doUnReadTip(
        serviceKey: state.serviceKey,
        classId: state.classId,
        courseKey: "",
      );
    }
  }

  Map<String, dynamic> _generateBuriedPointParams() {
    return {
      '\$screen_name': '学习日报加老师页',
      'material_type': state.materialType,
      'course_stage': state.courseStage,
      'custom_state': state.customState,
      'class_id': state.classId,
      'course_key': state.courseKey,
      'material_id': state.materialId,
    };
  }

  HostEnv test_getEnv() => _getEnv();

  test_consumeReportRedPoint() => _consumeReportRedPoint();

  Map<String, dynamic> test_generateBuriedPointParams() =>
      _generateBuriedPointParams();
}
