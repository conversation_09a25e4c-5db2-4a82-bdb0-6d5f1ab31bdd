// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'msg_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MsgBoxType _$$_MsgBoxTypeFromJson(Map<String, dynamic> json) =>
    _$_MsgBoxType(
      pageNum: json['pageNum'] as int?,
      pageSize: json['pageSize'] as int?,
      pageCount: json['pageCount'] as int?,
      totalCount: json['totalCount'] as int?,
      pageRecords: (json['pageRecords'] as List<dynamic>?)
          ?.map((e) => MsgList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MsgBoxTypeToJson(_$_MsgBoxType instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'pageCount': instance.pageCount,
      'totalCount': instance.totalCount,
      'pageRecords': instance.pageRecords,
    };

_$_MsgList _$$_MsgListFromJson(Map<String, dynamic> json) => _$_MsgList(
      bizId: json['bizId'] as String?,
      messageType: json['messageType'] as String?,
      courseSegment: json['courseSegment'] as String?,
      title: json['title'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      messageTime: json['messageTime'] as int?,
      shareInfo: json['shareInfo'] == null
          ? null
          : ShareInfo.fromJson(json['shareInfo'] as Map<String, dynamic>),
      messageContent: json['messageContent'] as String?,
      linkUrl: json['linkUrl'] as String?,
      classTeacherNotifyId: json['classTeacherNotifyId'] as int?,
      classId: json['classId'] as int?,
      teacherId: json['teacherId'] as int?,
      addTeacher: json['addTeacher'] as bool?,
      extendContent: json['extendContent'] == null
          ? null
          : ExtendContent.fromJson(
              json['extendContent'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_MsgListToJson(_$_MsgList instance) =>
    <String, dynamic>{
      'bizId': instance.bizId,
      'messageType': instance.messageType,
      'courseSegment': instance.courseSegment,
      'title': instance.title,
      'pictureUrl': instance.pictureUrl,
      'messageTime': instance.messageTime,
      'shareInfo': instance.shareInfo,
      'messageContent': instance.messageContent,
      'linkUrl': instance.linkUrl,
      'classTeacherNotifyId': instance.classTeacherNotifyId,
      'classId': instance.classId,
      'teacherId': instance.teacherId,
      'addTeacher': instance.addTeacher,
      'extendContent': instance.extendContent,
    };

_$_ShareInfo _$$_ShareInfoFromJson(Map<String, dynamic> json) => _$_ShareInfo(
      title: json['title'] as String?,
      shareImg: json['shareImg'] as String?,
      subTitle: json['subTitle'] as String?,
    );

Map<String, dynamic> _$$_ShareInfoToJson(_$_ShareInfo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'shareImg': instance.shareImg,
      'subTitle': instance.subTitle,
    };

_$_ExtendContent _$$_ExtendContentFromJson(Map<String, dynamic> json) =>
    _$_ExtendContent(
      showExtentContent: json['showExtentContent'] as int?,
      teacherServiceText: json['teacherServiceText'] as String?,
      teacherServiceUrl: json['teacherServiceUrl'] as String?,
      teacherAvatar: json['teacherAvatar'] as String?,
      teacherName: json['teacherName'] as String?,
      contactTeacherText: json['contactTeacherText'] as String?,
      contactTeacherUrl: json['contactTeacherUrl'] as String?,
    );

Map<String, dynamic> _$$_ExtendContentToJson(_$_ExtendContent instance) =>
    <String, dynamic>{
      'showExtentContent': instance.showExtentContent,
      'teacherServiceText': instance.teacherServiceText,
      'teacherServiceUrl': instance.teacherServiceUrl,
      'teacherAvatar': instance.teacherAvatar,
      'teacherName': instance.teacherName,
      'contactTeacherText': instance.contactTeacherText,
      'contactTeacherUrl': instance.contactTeacherUrl,
    };
