// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'msg_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MsgBoxType _$MsgBoxTypeFromJson(Map<String, dynamic> json) {
  return _MsgBoxType.fromJson(json);
}

/// @nodoc
mixin _$MsgBoxType {
  int? get pageNum => throw _privateConstructorUsedError;
  set pageNum(int? value) => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  set pageSize(int? value) => throw _privateConstructorUsedError;
  int? get pageCount => throw _privateConstructorUsedError;
  set pageCount(int? value) => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;
  set totalCount(int? value) => throw _privateConstructorUsedError;
  List<MsgList>? get pageRecords => throw _privateConstructorUsedError;
  set pageRecords(List<MsgList>? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MsgBoxTypeCopyWith<MsgBoxType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MsgBoxTypeCopyWith<$Res> {
  factory $MsgBoxTypeCopyWith(
          MsgBoxType value, $Res Function(MsgBoxType) then) =
      _$MsgBoxTypeCopyWithImpl<$Res, MsgBoxType>;
  @useResult
  $Res call(
      {int? pageNum,
      int? pageSize,
      int? pageCount,
      int? totalCount,
      List<MsgList>? pageRecords});
}

/// @nodoc
class _$MsgBoxTypeCopyWithImpl<$Res, $Val extends MsgBoxType>
    implements $MsgBoxTypeCopyWith<$Res> {
  _$MsgBoxTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? pageCount = freezed,
    Object? totalCount = freezed,
    Object? pageRecords = freezed,
  }) {
    return _then(_value.copyWith(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pageRecords: freezed == pageRecords
          ? _value.pageRecords
          : pageRecords // ignore: cast_nullable_to_non_nullable
              as List<MsgList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MsgBoxTypeCopyWith<$Res>
    implements $MsgBoxTypeCopyWith<$Res> {
  factory _$$_MsgBoxTypeCopyWith(
          _$_MsgBoxType value, $Res Function(_$_MsgBoxType) then) =
      __$$_MsgBoxTypeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? pageNum,
      int? pageSize,
      int? pageCount,
      int? totalCount,
      List<MsgList>? pageRecords});
}

/// @nodoc
class __$$_MsgBoxTypeCopyWithImpl<$Res>
    extends _$MsgBoxTypeCopyWithImpl<$Res, _$_MsgBoxType>
    implements _$$_MsgBoxTypeCopyWith<$Res> {
  __$$_MsgBoxTypeCopyWithImpl(
      _$_MsgBoxType _value, $Res Function(_$_MsgBoxType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? pageCount = freezed,
    Object? totalCount = freezed,
    Object? pageRecords = freezed,
  }) {
    return _then(_$_MsgBoxType(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pageRecords: freezed == pageRecords
          ? _value.pageRecords
          : pageRecords // ignore: cast_nullable_to_non_nullable
              as List<MsgList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MsgBoxType with DiagnosticableTreeMixin implements _MsgBoxType {
  _$_MsgBoxType(
      {this.pageNum,
      this.pageSize,
      this.pageCount,
      this.totalCount,
      this.pageRecords});

  factory _$_MsgBoxType.fromJson(Map<String, dynamic> json) =>
      _$$_MsgBoxTypeFromJson(json);

  @override
  int? pageNum;
  @override
  int? pageSize;
  @override
  int? pageCount;
  @override
  int? totalCount;
  @override
  List<MsgList>? pageRecords;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MsgBoxType(pageNum: $pageNum, pageSize: $pageSize, pageCount: $pageCount, totalCount: $totalCount, pageRecords: $pageRecords)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MsgBoxType'))
      ..add(DiagnosticsProperty('pageNum', pageNum))
      ..add(DiagnosticsProperty('pageSize', pageSize))
      ..add(DiagnosticsProperty('pageCount', pageCount))
      ..add(DiagnosticsProperty('totalCount', totalCount))
      ..add(DiagnosticsProperty('pageRecords', pageRecords));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MsgBoxTypeCopyWith<_$_MsgBoxType> get copyWith =>
      __$$_MsgBoxTypeCopyWithImpl<_$_MsgBoxType>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MsgBoxTypeToJson(
      this,
    );
  }
}

abstract class _MsgBoxType implements MsgBoxType {
  factory _MsgBoxType(
      {int? pageNum,
      int? pageSize,
      int? pageCount,
      int? totalCount,
      List<MsgList>? pageRecords}) = _$_MsgBoxType;

  factory _MsgBoxType.fromJson(Map<String, dynamic> json) =
      _$_MsgBoxType.fromJson;

  @override
  int? get pageNum;
  set pageNum(int? value);
  @override
  int? get pageSize;
  set pageSize(int? value);
  @override
  int? get pageCount;
  set pageCount(int? value);
  @override
  int? get totalCount;
  set totalCount(int? value);
  @override
  List<MsgList>? get pageRecords;
  set pageRecords(List<MsgList>? value);
  @override
  @JsonKey(ignore: true)
  _$$_MsgBoxTypeCopyWith<_$_MsgBoxType> get copyWith =>
      throw _privateConstructorUsedError;
}

MsgList _$MsgListFromJson(Map<String, dynamic> json) {
  return _MsgList.fromJson(json);
}

/// @nodoc
mixin _$MsgList {
  String? get bizId => throw _privateConstructorUsedError;
  set bizId(String? value) => throw _privateConstructorUsedError;
  String? get messageType => throw _privateConstructorUsedError;
  set messageType(String? value) => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  set courseSegment(String? value) => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  set pictureUrl(String? value) => throw _privateConstructorUsedError;
  int? get messageTime => throw _privateConstructorUsedError;
  set messageTime(int? value) => throw _privateConstructorUsedError;
  ShareInfo? get shareInfo => throw _privateConstructorUsedError;
  set shareInfo(ShareInfo? value) => throw _privateConstructorUsedError;
  String? get messageContent => throw _privateConstructorUsedError;
  set messageContent(String? value) => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  set linkUrl(String? value) => throw _privateConstructorUsedError;
  int? get classTeacherNotifyId => throw _privateConstructorUsedError;
  set classTeacherNotifyId(int? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  set teacherId(int? value) => throw _privateConstructorUsedError;
  bool? get addTeacher => throw _privateConstructorUsedError;
  set addTeacher(bool? value) => throw _privateConstructorUsedError;
  ExtendContent? get extendContent => throw _privateConstructorUsedError;
  set extendContent(ExtendContent? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MsgListCopyWith<MsgList> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MsgListCopyWith<$Res> {
  factory $MsgListCopyWith(MsgList value, $Res Function(MsgList) then) =
      _$MsgListCopyWithImpl<$Res, MsgList>;
  @useResult
  $Res call(
      {String? bizId,
      String? messageType,
      String? courseSegment,
      String? title,
      String? pictureUrl,
      int? messageTime,
      ShareInfo? shareInfo,
      String? messageContent,
      String? linkUrl,
      int? classTeacherNotifyId,
      int? classId,
      int? teacherId,
      bool? addTeacher,
      ExtendContent? extendContent});

  $ShareInfoCopyWith<$Res>? get shareInfo;
  $ExtendContentCopyWith<$Res>? get extendContent;
}

/// @nodoc
class _$MsgListCopyWithImpl<$Res, $Val extends MsgList>
    implements $MsgListCopyWith<$Res> {
  _$MsgListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bizId = freezed,
    Object? messageType = freezed,
    Object? courseSegment = freezed,
    Object? title = freezed,
    Object? pictureUrl = freezed,
    Object? messageTime = freezed,
    Object? shareInfo = freezed,
    Object? messageContent = freezed,
    Object? linkUrl = freezed,
    Object? classTeacherNotifyId = freezed,
    Object? classId = freezed,
    Object? teacherId = freezed,
    Object? addTeacher = freezed,
    Object? extendContent = freezed,
  }) {
    return _then(_value.copyWith(
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTime: freezed == messageTime
          ? _value.messageTime
          : messageTime // ignore: cast_nullable_to_non_nullable
              as int?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacherNotifyId: freezed == classTeacherNotifyId
          ? _value.classTeacherNotifyId
          : classTeacherNotifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacher: freezed == addTeacher
          ? _value.addTeacher
          : addTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      extendContent: freezed == extendContent
          ? _value.extendContent
          : extendContent // ignore: cast_nullable_to_non_nullable
              as ExtendContent?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ShareInfoCopyWith<$Res>? get shareInfo {
    if (_value.shareInfo == null) {
      return null;
    }

    return $ShareInfoCopyWith<$Res>(_value.shareInfo!, (value) {
      return _then(_value.copyWith(shareInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtendContentCopyWith<$Res>? get extendContent {
    if (_value.extendContent == null) {
      return null;
    }

    return $ExtendContentCopyWith<$Res>(_value.extendContent!, (value) {
      return _then(_value.copyWith(extendContent: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MsgListCopyWith<$Res> implements $MsgListCopyWith<$Res> {
  factory _$$_MsgListCopyWith(
          _$_MsgList value, $Res Function(_$_MsgList) then) =
      __$$_MsgListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? bizId,
      String? messageType,
      String? courseSegment,
      String? title,
      String? pictureUrl,
      int? messageTime,
      ShareInfo? shareInfo,
      String? messageContent,
      String? linkUrl,
      int? classTeacherNotifyId,
      int? classId,
      int? teacherId,
      bool? addTeacher,
      ExtendContent? extendContent});

  @override
  $ShareInfoCopyWith<$Res>? get shareInfo;
  @override
  $ExtendContentCopyWith<$Res>? get extendContent;
}

/// @nodoc
class __$$_MsgListCopyWithImpl<$Res>
    extends _$MsgListCopyWithImpl<$Res, _$_MsgList>
    implements _$$_MsgListCopyWith<$Res> {
  __$$_MsgListCopyWithImpl(_$_MsgList _value, $Res Function(_$_MsgList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bizId = freezed,
    Object? messageType = freezed,
    Object? courseSegment = freezed,
    Object? title = freezed,
    Object? pictureUrl = freezed,
    Object? messageTime = freezed,
    Object? shareInfo = freezed,
    Object? messageContent = freezed,
    Object? linkUrl = freezed,
    Object? classTeacherNotifyId = freezed,
    Object? classId = freezed,
    Object? teacherId = freezed,
    Object? addTeacher = freezed,
    Object? extendContent = freezed,
  }) {
    return _then(_$_MsgList(
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTime: freezed == messageTime
          ? _value.messageTime
          : messageTime // ignore: cast_nullable_to_non_nullable
              as int?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacherNotifyId: freezed == classTeacherNotifyId
          ? _value.classTeacherNotifyId
          : classTeacherNotifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacher: freezed == addTeacher
          ? _value.addTeacher
          : addTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      extendContent: freezed == extendContent
          ? _value.extendContent
          : extendContent // ignore: cast_nullable_to_non_nullable
              as ExtendContent?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MsgList with DiagnosticableTreeMixin implements _MsgList {
  _$_MsgList(
      {this.bizId,
      this.messageType,
      this.courseSegment,
      this.title,
      this.pictureUrl,
      this.messageTime,
      this.shareInfo,
      this.messageContent,
      this.linkUrl,
      this.classTeacherNotifyId,
      this.classId,
      this.teacherId,
      this.addTeacher,
      this.extendContent});

  factory _$_MsgList.fromJson(Map<String, dynamic> json) =>
      _$$_MsgListFromJson(json);

  @override
  String? bizId;
  @override
  String? messageType;
  @override
  String? courseSegment;
  @override
  String? title;
  @override
  String? pictureUrl;
  @override
  int? messageTime;
  @override
  ShareInfo? shareInfo;
  @override
  String? messageContent;
  @override
  String? linkUrl;
  @override
  int? classTeacherNotifyId;
  @override
  int? classId;
  @override
  int? teacherId;
  @override
  bool? addTeacher;
  @override
  ExtendContent? extendContent;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MsgList(bizId: $bizId, messageType: $messageType, courseSegment: $courseSegment, title: $title, pictureUrl: $pictureUrl, messageTime: $messageTime, shareInfo: $shareInfo, messageContent: $messageContent, linkUrl: $linkUrl, classTeacherNotifyId: $classTeacherNotifyId, classId: $classId, teacherId: $teacherId, addTeacher: $addTeacher, extendContent: $extendContent)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MsgList'))
      ..add(DiagnosticsProperty('bizId', bizId))
      ..add(DiagnosticsProperty('messageType', messageType))
      ..add(DiagnosticsProperty('courseSegment', courseSegment))
      ..add(DiagnosticsProperty('title', title))
      ..add(DiagnosticsProperty('pictureUrl', pictureUrl))
      ..add(DiagnosticsProperty('messageTime', messageTime))
      ..add(DiagnosticsProperty('shareInfo', shareInfo))
      ..add(DiagnosticsProperty('messageContent', messageContent))
      ..add(DiagnosticsProperty('linkUrl', linkUrl))
      ..add(DiagnosticsProperty('classTeacherNotifyId', classTeacherNotifyId))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('teacherId', teacherId))
      ..add(DiagnosticsProperty('addTeacher', addTeacher))
      ..add(DiagnosticsProperty('extendContent', extendContent));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MsgListCopyWith<_$_MsgList> get copyWith =>
      __$$_MsgListCopyWithImpl<_$_MsgList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MsgListToJson(
      this,
    );
  }
}

abstract class _MsgList implements MsgList {
  factory _MsgList(
      {String? bizId,
      String? messageType,
      String? courseSegment,
      String? title,
      String? pictureUrl,
      int? messageTime,
      ShareInfo? shareInfo,
      String? messageContent,
      String? linkUrl,
      int? classTeacherNotifyId,
      int? classId,
      int? teacherId,
      bool? addTeacher,
      ExtendContent? extendContent}) = _$_MsgList;

  factory _MsgList.fromJson(Map<String, dynamic> json) = _$_MsgList.fromJson;

  @override
  String? get bizId;
  set bizId(String? value);
  @override
  String? get messageType;
  set messageType(String? value);
  @override
  String? get courseSegment;
  set courseSegment(String? value);
  @override
  String? get title;
  set title(String? value);
  @override
  String? get pictureUrl;
  set pictureUrl(String? value);
  @override
  int? get messageTime;
  set messageTime(int? value);
  @override
  ShareInfo? get shareInfo;
  set shareInfo(ShareInfo? value);
  @override
  String? get messageContent;
  set messageContent(String? value);
  @override
  String? get linkUrl;
  set linkUrl(String? value);
  @override
  int? get classTeacherNotifyId;
  set classTeacherNotifyId(int? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  int? get teacherId;
  set teacherId(int? value);
  @override
  bool? get addTeacher;
  set addTeacher(bool? value);
  @override
  ExtendContent? get extendContent;
  set extendContent(ExtendContent? value);
  @override
  @JsonKey(ignore: true)
  _$$_MsgListCopyWith<_$_MsgList> get copyWith =>
      throw _privateConstructorUsedError;
}

ShareInfo _$ShareInfoFromJson(Map<String, dynamic> json) {
  return _ShareInfo.fromJson(json);
}

/// @nodoc
mixin _$ShareInfo {
  String? get title => throw _privateConstructorUsedError;
  String? get shareImg => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShareInfoCopyWith<ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShareInfoCopyWith<$Res> {
  factory $ShareInfoCopyWith(ShareInfo value, $Res Function(ShareInfo) then) =
      _$ShareInfoCopyWithImpl<$Res, ShareInfo>;
  @useResult
  $Res call({String? title, String? shareImg, String? subTitle});
}

/// @nodoc
class _$ShareInfoCopyWithImpl<$Res, $Val extends ShareInfo>
    implements $ShareInfoCopyWith<$Res> {
  _$ShareInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? shareImg = freezed,
    Object? subTitle = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImg: freezed == shareImg
          ? _value.shareImg
          : shareImg // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShareInfoCopyWith<$Res> implements $ShareInfoCopyWith<$Res> {
  factory _$$_ShareInfoCopyWith(
          _$_ShareInfo value, $Res Function(_$_ShareInfo) then) =
      __$$_ShareInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? title, String? shareImg, String? subTitle});
}

/// @nodoc
class __$$_ShareInfoCopyWithImpl<$Res>
    extends _$ShareInfoCopyWithImpl<$Res, _$_ShareInfo>
    implements _$$_ShareInfoCopyWith<$Res> {
  __$$_ShareInfoCopyWithImpl(
      _$_ShareInfo _value, $Res Function(_$_ShareInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? shareImg = freezed,
    Object? subTitle = freezed,
  }) {
    return _then(_$_ShareInfo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImg: freezed == shareImg
          ? _value.shareImg
          : shareImg // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShareInfo with DiagnosticableTreeMixin implements _ShareInfo {
  const _$_ShareInfo({this.title, this.shareImg, this.subTitle});

  factory _$_ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ShareInfoFromJson(json);

  @override
  final String? title;
  @override
  final String? shareImg;
  @override
  final String? subTitle;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ShareInfo(title: $title, shareImg: $shareImg, subTitle: $subTitle)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ShareInfo'))
      ..add(DiagnosticsProperty('title', title))
      ..add(DiagnosticsProperty('shareImg', shareImg))
      ..add(DiagnosticsProperty('subTitle', subTitle));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShareInfo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.shareImg, shareImg) ||
                other.shareImg == shareImg) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, shareImg, subTitle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      __$$_ShareInfoCopyWithImpl<_$_ShareInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShareInfoToJson(
      this,
    );
  }
}

abstract class _ShareInfo implements ShareInfo {
  const factory _ShareInfo(
      {final String? title,
      final String? shareImg,
      final String? subTitle}) = _$_ShareInfo;

  factory _ShareInfo.fromJson(Map<String, dynamic> json) =
      _$_ShareInfo.fromJson;

  @override
  String? get title;
  @override
  String? get shareImg;
  @override
  String? get subTitle;
  @override
  @JsonKey(ignore: true)
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ExtendContent _$ExtendContentFromJson(Map<String, dynamic> json) {
  return _ExtendContent.fromJson(json);
}

/// @nodoc
mixin _$ExtendContent {
  int? get showExtentContent => throw _privateConstructorUsedError;
  String? get teacherServiceText => throw _privateConstructorUsedError;
  String? get teacherServiceUrl => throw _privateConstructorUsedError;
  String? get teacherAvatar => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  String? get contactTeacherText => throw _privateConstructorUsedError;
  String? get contactTeacherUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtendContentCopyWith<ExtendContent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtendContentCopyWith<$Res> {
  factory $ExtendContentCopyWith(
          ExtendContent value, $Res Function(ExtendContent) then) =
      _$ExtendContentCopyWithImpl<$Res, ExtendContent>;
  @useResult
  $Res call(
      {int? showExtentContent,
      String? teacherServiceText,
      String? teacherServiceUrl,
      String? teacherAvatar,
      String? teacherName,
      String? contactTeacherText,
      String? contactTeacherUrl});
}

/// @nodoc
class _$ExtendContentCopyWithImpl<$Res, $Val extends ExtendContent>
    implements $ExtendContentCopyWith<$Res> {
  _$ExtendContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showExtentContent = freezed,
    Object? teacherServiceText = freezed,
    Object? teacherServiceUrl = freezed,
    Object? teacherAvatar = freezed,
    Object? teacherName = freezed,
    Object? contactTeacherText = freezed,
    Object? contactTeacherUrl = freezed,
  }) {
    return _then(_value.copyWith(
      showExtentContent: freezed == showExtentContent
          ? _value.showExtentContent
          : showExtentContent // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherServiceText: freezed == teacherServiceText
          ? _value.teacherServiceText
          : teacherServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceUrl: freezed == teacherServiceUrl
          ? _value.teacherServiceUrl
          : teacherServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherText: freezed == contactTeacherText
          ? _value.contactTeacherText
          : contactTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExtendContentCopyWith<$Res>
    implements $ExtendContentCopyWith<$Res> {
  factory _$$_ExtendContentCopyWith(
          _$_ExtendContent value, $Res Function(_$_ExtendContent) then) =
      __$$_ExtendContentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? showExtentContent,
      String? teacherServiceText,
      String? teacherServiceUrl,
      String? teacherAvatar,
      String? teacherName,
      String? contactTeacherText,
      String? contactTeacherUrl});
}

/// @nodoc
class __$$_ExtendContentCopyWithImpl<$Res>
    extends _$ExtendContentCopyWithImpl<$Res, _$_ExtendContent>
    implements _$$_ExtendContentCopyWith<$Res> {
  __$$_ExtendContentCopyWithImpl(
      _$_ExtendContent _value, $Res Function(_$_ExtendContent) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showExtentContent = freezed,
    Object? teacherServiceText = freezed,
    Object? teacherServiceUrl = freezed,
    Object? teacherAvatar = freezed,
    Object? teacherName = freezed,
    Object? contactTeacherText = freezed,
    Object? contactTeacherUrl = freezed,
  }) {
    return _then(_$_ExtendContent(
      showExtentContent: freezed == showExtentContent
          ? _value.showExtentContent
          : showExtentContent // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherServiceText: freezed == teacherServiceText
          ? _value.teacherServiceText
          : teacherServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceUrl: freezed == teacherServiceUrl
          ? _value.teacherServiceUrl
          : teacherServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherText: freezed == contactTeacherText
          ? _value.contactTeacherText
          : contactTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExtendContent with DiagnosticableTreeMixin implements _ExtendContent {
  const _$_ExtendContent(
      {this.showExtentContent,
      this.teacherServiceText,
      this.teacherServiceUrl,
      this.teacherAvatar,
      this.teacherName,
      this.contactTeacherText,
      this.contactTeacherUrl});

  factory _$_ExtendContent.fromJson(Map<String, dynamic> json) =>
      _$$_ExtendContentFromJson(json);

  @override
  final int? showExtentContent;
  @override
  final String? teacherServiceText;
  @override
  final String? teacherServiceUrl;
  @override
  final String? teacherAvatar;
  @override
  final String? teacherName;
  @override
  final String? contactTeacherText;
  @override
  final String? contactTeacherUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ExtendContent(showExtentContent: $showExtentContent, teacherServiceText: $teacherServiceText, teacherServiceUrl: $teacherServiceUrl, teacherAvatar: $teacherAvatar, teacherName: $teacherName, contactTeacherText: $contactTeacherText, contactTeacherUrl: $contactTeacherUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ExtendContent'))
      ..add(DiagnosticsProperty('showExtentContent', showExtentContent))
      ..add(DiagnosticsProperty('teacherServiceText', teacherServiceText))
      ..add(DiagnosticsProperty('teacherServiceUrl', teacherServiceUrl))
      ..add(DiagnosticsProperty('teacherAvatar', teacherAvatar))
      ..add(DiagnosticsProperty('teacherName', teacherName))
      ..add(DiagnosticsProperty('contactTeacherText', contactTeacherText))
      ..add(DiagnosticsProperty('contactTeacherUrl', contactTeacherUrl));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExtendContent &&
            (identical(other.showExtentContent, showExtentContent) ||
                other.showExtentContent == showExtentContent) &&
            (identical(other.teacherServiceText, teacherServiceText) ||
                other.teacherServiceText == teacherServiceText) &&
            (identical(other.teacherServiceUrl, teacherServiceUrl) ||
                other.teacherServiceUrl == teacherServiceUrl) &&
            (identical(other.teacherAvatar, teacherAvatar) ||
                other.teacherAvatar == teacherAvatar) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.contactTeacherText, contactTeacherText) ||
                other.contactTeacherText == contactTeacherText) &&
            (identical(other.contactTeacherUrl, contactTeacherUrl) ||
                other.contactTeacherUrl == contactTeacherUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showExtentContent,
      teacherServiceText,
      teacherServiceUrl,
      teacherAvatar,
      teacherName,
      contactTeacherText,
      contactTeacherUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtendContentCopyWith<_$_ExtendContent> get copyWith =>
      __$$_ExtendContentCopyWithImpl<_$_ExtendContent>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtendContentToJson(
      this,
    );
  }
}

abstract class _ExtendContent implements ExtendContent {
  const factory _ExtendContent(
      {final int? showExtentContent,
      final String? teacherServiceText,
      final String? teacherServiceUrl,
      final String? teacherAvatar,
      final String? teacherName,
      final String? contactTeacherText,
      final String? contactTeacherUrl}) = _$_ExtendContent;

  factory _ExtendContent.fromJson(Map<String, dynamic> json) =
      _$_ExtendContent.fromJson;

  @override
  int? get showExtentContent;
  @override
  String? get teacherServiceText;
  @override
  String? get teacherServiceUrl;
  @override
  String? get teacherAvatar;
  @override
  String? get teacherName;
  @override
  String? get contactTeacherText;
  @override
  String? get contactTeacherUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ExtendContentCopyWith<_$_ExtendContent> get copyWith =>
      throw _privateConstructorUsedError;
}
