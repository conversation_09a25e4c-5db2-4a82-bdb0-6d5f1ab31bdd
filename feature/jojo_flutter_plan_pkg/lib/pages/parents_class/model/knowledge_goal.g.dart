// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'knowledge_goal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_KnowledgeGoal _$$_KnowledgeGoalFromJson(Map<String, dynamic> json) =>
    _$_KnowledgeGoal(
      courseInfo: json['courseInfo'] == null
          ? null
          : KnowledgeCourseInfo.fromJson(
              json['courseInfo'] as Map<String, dynamic>),
      teacherInfo: json['teacherInfo'] == null
          ? null
          : KnowledgeTeacherInfo.fromJson(
              json['teacherInfo'] as Map<String, dynamic>),
      classInfo: json['classInfo'] == null
          ? null
          : KnowledgeClassInfo.fromJson(
              json['classInfo'] as Map<String, dynamic>),
      title: json['title'] as String?,
      knowledgeGoalResourceList:
          (json['knowledgeGoalResourceList'] as List<dynamic>?)
              ?.map((e) => KnowledgeVideo.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_KnowledgeGoalToJson(_$_KnowledgeGoal instance) =>
    <String, dynamic>{
      'courseInfo': instance.courseInfo,
      'teacherInfo': instance.teacherInfo,
      'classInfo': instance.classInfo,
      'title': instance.title,
      'knowledgeGoalResourceList': instance.knowledgeGoalResourceList,
    };

_$_KnowledgeVideo _$$_KnowledgeVideoFromJson(Map<String, dynamic> json) =>
    _$_KnowledgeVideo(
      title: json['title'] as String?,
      name: json['name'] as String?,
      lessonKey: json['lessonKey'] as String?,
      resourceUrl: json['resourceUrl'] as String?,
    );

Map<String, dynamic> _$$_KnowledgeVideoToJson(_$_KnowledgeVideo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'name': instance.name,
      'lessonKey': instance.lessonKey,
      'resourceUrl': instance.resourceUrl,
    };

_$_KnowledgeCourseInfo _$$_KnowledgeCourseInfoFromJson(
        Map<String, dynamic> json) =>
    _$_KnowledgeCourseInfo(
      courseType: json['courseType'] as int?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
      courseSegment: json['courseSegment'] as String?,
      courseKey: json['courseKey'] as String?,
      courseStatus: json['courseStatus'] as int?,
      courseName: json['courseName'] as String?,
    );

Map<String, dynamic> _$$_KnowledgeCourseInfoToJson(
        _$_KnowledgeCourseInfo instance) =>
    <String, dynamic>{
      'courseType': instance.courseType,
      'courseSegmentCode': instance.courseSegmentCode,
      'courseSegment': instance.courseSegment,
      'courseKey': instance.courseKey,
      'courseStatus': instance.courseStatus,
      'courseName': instance.courseName,
    };

_$_KnowledgeTeacherInfo _$$_KnowledgeTeacherInfoFromJson(
        Map<String, dynamic> json) =>
    _$_KnowledgeTeacherInfo(
      teacherId: json['teacherId'] as int?,
      teacherName: json['teacherName'] as String?,
    );

Map<String, dynamic> _$$_KnowledgeTeacherInfoToJson(
        _$_KnowledgeTeacherInfo instance) =>
    <String, dynamic>{
      'teacherId': instance.teacherId,
      'teacherName': instance.teacherName,
    };

_$_KnowledgeClassInfo _$$_KnowledgeClassInfoFromJson(
        Map<String, dynamic> json) =>
    _$_KnowledgeClassInfo(
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_KnowledgeClassInfoToJson(
        _$_KnowledgeClassInfo instance) =>
    <String, dynamic>{
      'classId': instance.classId,
    };
