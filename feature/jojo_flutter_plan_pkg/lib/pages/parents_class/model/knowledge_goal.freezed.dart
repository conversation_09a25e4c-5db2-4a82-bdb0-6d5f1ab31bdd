// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'knowledge_goal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

KnowledgeGoal _$KnowledgeGoalFromJson(Map<String, dynamic> json) {
  return _KnowledgeGoal.fromJson(json);
}

/// @nodoc
mixin _$KnowledgeGoal {
  KnowledgeCourseInfo? get courseInfo => throw _privateConstructorUsedError;
  set courseInfo(KnowledgeCourseInfo? value) =>
      throw _privateConstructorUsedError;
  KnowledgeTeacherInfo? get teacherInfo => throw _privateConstructorUsedError;
  set teacherInfo(KnowledgeTeacherInfo? value) =>
      throw _privateConstructorUsedError;
  KnowledgeClassInfo? get classInfo => throw _privateConstructorUsedError;
  set classInfo(KnowledgeClassInfo? value) =>
      throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  List<KnowledgeVideo>? get knowledgeGoalResourceList =>
      throw _privateConstructorUsedError;
  set knowledgeGoalResourceList(List<KnowledgeVideo>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KnowledgeGoalCopyWith<KnowledgeGoal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KnowledgeGoalCopyWith<$Res> {
  factory $KnowledgeGoalCopyWith(
          KnowledgeGoal value, $Res Function(KnowledgeGoal) then) =
      _$KnowledgeGoalCopyWithImpl<$Res, KnowledgeGoal>;
  @useResult
  $Res call(
      {KnowledgeCourseInfo? courseInfo,
      KnowledgeTeacherInfo? teacherInfo,
      KnowledgeClassInfo? classInfo,
      String? title,
      List<KnowledgeVideo>? knowledgeGoalResourceList});

  $KnowledgeCourseInfoCopyWith<$Res>? get courseInfo;
  $KnowledgeTeacherInfoCopyWith<$Res>? get teacherInfo;
  $KnowledgeClassInfoCopyWith<$Res>? get classInfo;
}

/// @nodoc
class _$KnowledgeGoalCopyWithImpl<$Res, $Val extends KnowledgeGoal>
    implements $KnowledgeGoalCopyWith<$Res> {
  _$KnowledgeGoalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? teacherInfo = freezed,
    Object? classInfo = freezed,
    Object? title = freezed,
    Object? knowledgeGoalResourceList = freezed,
  }) {
    return _then(_value.copyWith(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeCourseInfo?,
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeTeacherInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeClassInfo?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      knowledgeGoalResourceList: freezed == knowledgeGoalResourceList
          ? _value.knowledgeGoalResourceList
          : knowledgeGoalResourceList // ignore: cast_nullable_to_non_nullable
              as List<KnowledgeVideo>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $KnowledgeCourseInfoCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $KnowledgeCourseInfoCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $KnowledgeTeacherInfoCopyWith<$Res>? get teacherInfo {
    if (_value.teacherInfo == null) {
      return null;
    }

    return $KnowledgeTeacherInfoCopyWith<$Res>(_value.teacherInfo!, (value) {
      return _then(_value.copyWith(teacherInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $KnowledgeClassInfoCopyWith<$Res>? get classInfo {
    if (_value.classInfo == null) {
      return null;
    }

    return $KnowledgeClassInfoCopyWith<$Res>(_value.classInfo!, (value) {
      return _then(_value.copyWith(classInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_KnowledgeGoalCopyWith<$Res>
    implements $KnowledgeGoalCopyWith<$Res> {
  factory _$$_KnowledgeGoalCopyWith(
          _$_KnowledgeGoal value, $Res Function(_$_KnowledgeGoal) then) =
      __$$_KnowledgeGoalCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {KnowledgeCourseInfo? courseInfo,
      KnowledgeTeacherInfo? teacherInfo,
      KnowledgeClassInfo? classInfo,
      String? title,
      List<KnowledgeVideo>? knowledgeGoalResourceList});

  @override
  $KnowledgeCourseInfoCopyWith<$Res>? get courseInfo;
  @override
  $KnowledgeTeacherInfoCopyWith<$Res>? get teacherInfo;
  @override
  $KnowledgeClassInfoCopyWith<$Res>? get classInfo;
}

/// @nodoc
class __$$_KnowledgeGoalCopyWithImpl<$Res>
    extends _$KnowledgeGoalCopyWithImpl<$Res, _$_KnowledgeGoal>
    implements _$$_KnowledgeGoalCopyWith<$Res> {
  __$$_KnowledgeGoalCopyWithImpl(
      _$_KnowledgeGoal _value, $Res Function(_$_KnowledgeGoal) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfo = freezed,
    Object? teacherInfo = freezed,
    Object? classInfo = freezed,
    Object? title = freezed,
    Object? knowledgeGoalResourceList = freezed,
  }) {
    return _then(_$_KnowledgeGoal(
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeCourseInfo?,
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeTeacherInfo?,
      classInfo: freezed == classInfo
          ? _value.classInfo
          : classInfo // ignore: cast_nullable_to_non_nullable
              as KnowledgeClassInfo?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      knowledgeGoalResourceList: freezed == knowledgeGoalResourceList
          ? _value.knowledgeGoalResourceList
          : knowledgeGoalResourceList // ignore: cast_nullable_to_non_nullable
              as List<KnowledgeVideo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_KnowledgeGoal implements _KnowledgeGoal {
  _$_KnowledgeGoal(
      {this.courseInfo,
      this.teacherInfo,
      this.classInfo,
      this.title,
      this.knowledgeGoalResourceList});

  factory _$_KnowledgeGoal.fromJson(Map<String, dynamic> json) =>
      _$$_KnowledgeGoalFromJson(json);

  @override
  KnowledgeCourseInfo? courseInfo;
  @override
  KnowledgeTeacherInfo? teacherInfo;
  @override
  KnowledgeClassInfo? classInfo;
  @override
  String? title;
  @override
  List<KnowledgeVideo>? knowledgeGoalResourceList;

  @override
  String toString() {
    return 'KnowledgeGoal(courseInfo: $courseInfo, teacherInfo: $teacherInfo, classInfo: $classInfo, title: $title, knowledgeGoalResourceList: $knowledgeGoalResourceList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KnowledgeGoalCopyWith<_$_KnowledgeGoal> get copyWith =>
      __$$_KnowledgeGoalCopyWithImpl<_$_KnowledgeGoal>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_KnowledgeGoalToJson(
      this,
    );
  }
}

abstract class _KnowledgeGoal implements KnowledgeGoal {
  factory _KnowledgeGoal(
      {KnowledgeCourseInfo? courseInfo,
      KnowledgeTeacherInfo? teacherInfo,
      KnowledgeClassInfo? classInfo,
      String? title,
      List<KnowledgeVideo>? knowledgeGoalResourceList}) = _$_KnowledgeGoal;

  factory _KnowledgeGoal.fromJson(Map<String, dynamic> json) =
      _$_KnowledgeGoal.fromJson;

  @override
  KnowledgeCourseInfo? get courseInfo;
  set courseInfo(KnowledgeCourseInfo? value);
  @override
  KnowledgeTeacherInfo? get teacherInfo;
  set teacherInfo(KnowledgeTeacherInfo? value);
  @override
  KnowledgeClassInfo? get classInfo;
  set classInfo(KnowledgeClassInfo? value);
  @override
  String? get title;
  set title(String? value);
  @override
  List<KnowledgeVideo>? get knowledgeGoalResourceList;
  set knowledgeGoalResourceList(List<KnowledgeVideo>? value);
  @override
  @JsonKey(ignore: true)
  _$$_KnowledgeGoalCopyWith<_$_KnowledgeGoal> get copyWith =>
      throw _privateConstructorUsedError;
}

KnowledgeVideo _$KnowledgeVideoFromJson(Map<String, dynamic> json) {
  return _KnowledgeVideo.fromJson(json);
}

/// @nodoc
mixin _$KnowledgeVideo {
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  set name(String? value) => throw _privateConstructorUsedError;
  String? get lessonKey => throw _privateConstructorUsedError;
  set lessonKey(String? value) => throw _privateConstructorUsedError;
  String? get resourceUrl => throw _privateConstructorUsedError;
  set resourceUrl(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KnowledgeVideoCopyWith<KnowledgeVideo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KnowledgeVideoCopyWith<$Res> {
  factory $KnowledgeVideoCopyWith(
          KnowledgeVideo value, $Res Function(KnowledgeVideo) then) =
      _$KnowledgeVideoCopyWithImpl<$Res, KnowledgeVideo>;
  @useResult
  $Res call(
      {String? title, String? name, String? lessonKey, String? resourceUrl});
}

/// @nodoc
class _$KnowledgeVideoCopyWithImpl<$Res, $Val extends KnowledgeVideo>
    implements $KnowledgeVideoCopyWith<$Res> {
  _$KnowledgeVideoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? name = freezed,
    Object? lessonKey = freezed,
    Object? resourceUrl = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_KnowledgeVideoCopyWith<$Res>
    implements $KnowledgeVideoCopyWith<$Res> {
  factory _$$_KnowledgeVideoCopyWith(
          _$_KnowledgeVideo value, $Res Function(_$_KnowledgeVideo) then) =
      __$$_KnowledgeVideoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title, String? name, String? lessonKey, String? resourceUrl});
}

/// @nodoc
class __$$_KnowledgeVideoCopyWithImpl<$Res>
    extends _$KnowledgeVideoCopyWithImpl<$Res, _$_KnowledgeVideo>
    implements _$$_KnowledgeVideoCopyWith<$Res> {
  __$$_KnowledgeVideoCopyWithImpl(
      _$_KnowledgeVideo _value, $Res Function(_$_KnowledgeVideo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? name = freezed,
    Object? lessonKey = freezed,
    Object? resourceUrl = freezed,
  }) {
    return _then(_$_KnowledgeVideo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_KnowledgeVideo implements _KnowledgeVideo {
  _$_KnowledgeVideo({this.title, this.name, this.lessonKey, this.resourceUrl});

  factory _$_KnowledgeVideo.fromJson(Map<String, dynamic> json) =>
      _$$_KnowledgeVideoFromJson(json);

  @override
  String? title;
  @override
  String? name;
  @override
  String? lessonKey;
  @override
  String? resourceUrl;

  @override
  String toString() {
    return 'KnowledgeVideo(title: $title, name: $name, lessonKey: $lessonKey, resourceUrl: $resourceUrl)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KnowledgeVideoCopyWith<_$_KnowledgeVideo> get copyWith =>
      __$$_KnowledgeVideoCopyWithImpl<_$_KnowledgeVideo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_KnowledgeVideoToJson(
      this,
    );
  }
}

abstract class _KnowledgeVideo implements KnowledgeVideo {
  factory _KnowledgeVideo(
      {String? title,
      String? name,
      String? lessonKey,
      String? resourceUrl}) = _$_KnowledgeVideo;

  factory _KnowledgeVideo.fromJson(Map<String, dynamic> json) =
      _$_KnowledgeVideo.fromJson;

  @override
  String? get title;
  set title(String? value);
  @override
  String? get name;
  set name(String? value);
  @override
  String? get lessonKey;
  set lessonKey(String? value);
  @override
  String? get resourceUrl;
  set resourceUrl(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_KnowledgeVideoCopyWith<_$_KnowledgeVideo> get copyWith =>
      throw _privateConstructorUsedError;
}

KnowledgeCourseInfo _$KnowledgeCourseInfoFromJson(Map<String, dynamic> json) {
  return _KnowledgeCourseInfo.fromJson(json);
}

/// @nodoc
mixin _$KnowledgeCourseInfo {
  int? get courseType => throw _privateConstructorUsedError;
  set courseType(int? value) => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;
  set courseSegmentCode(int? value) => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  set courseSegment(String? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;
  int? get courseStatus => throw _privateConstructorUsedError;
  set courseStatus(int? value) => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  set courseName(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KnowledgeCourseInfoCopyWith<KnowledgeCourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KnowledgeCourseInfoCopyWith<$Res> {
  factory $KnowledgeCourseInfoCopyWith(
          KnowledgeCourseInfo value, $Res Function(KnowledgeCourseInfo) then) =
      _$KnowledgeCourseInfoCopyWithImpl<$Res, KnowledgeCourseInfo>;
  @useResult
  $Res call(
      {int? courseType,
      int? courseSegmentCode,
      String? courseSegment,
      String? courseKey,
      int? courseStatus,
      String? courseName});
}

/// @nodoc
class _$KnowledgeCourseInfoCopyWithImpl<$Res, $Val extends KnowledgeCourseInfo>
    implements $KnowledgeCourseInfoCopyWith<$Res> {
  _$KnowledgeCourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseType = freezed,
    Object? courseSegmentCode = freezed,
    Object? courseSegment = freezed,
    Object? courseKey = freezed,
    Object? courseStatus = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_value.copyWith(
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_KnowledgeCourseInfoCopyWith<$Res>
    implements $KnowledgeCourseInfoCopyWith<$Res> {
  factory _$$_KnowledgeCourseInfoCopyWith(_$_KnowledgeCourseInfo value,
          $Res Function(_$_KnowledgeCourseInfo) then) =
      __$$_KnowledgeCourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseType,
      int? courseSegmentCode,
      String? courseSegment,
      String? courseKey,
      int? courseStatus,
      String? courseName});
}

/// @nodoc
class __$$_KnowledgeCourseInfoCopyWithImpl<$Res>
    extends _$KnowledgeCourseInfoCopyWithImpl<$Res, _$_KnowledgeCourseInfo>
    implements _$$_KnowledgeCourseInfoCopyWith<$Res> {
  __$$_KnowledgeCourseInfoCopyWithImpl(_$_KnowledgeCourseInfo _value,
      $Res Function(_$_KnowledgeCourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseType = freezed,
    Object? courseSegmentCode = freezed,
    Object? courseSegment = freezed,
    Object? courseKey = freezed,
    Object? courseStatus = freezed,
    Object? courseName = freezed,
  }) {
    return _then(_$_KnowledgeCourseInfo(
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_KnowledgeCourseInfo implements _KnowledgeCourseInfo {
  _$_KnowledgeCourseInfo(
      {this.courseType,
      this.courseSegmentCode,
      this.courseSegment,
      this.courseKey,
      this.courseStatus,
      this.courseName});

  factory _$_KnowledgeCourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_KnowledgeCourseInfoFromJson(json);

  @override
  int? courseType;
  @override
  int? courseSegmentCode;
  @override
  String? courseSegment;
  @override
  String? courseKey;
  @override
  int? courseStatus;
  @override
  String? courseName;

  @override
  String toString() {
    return 'KnowledgeCourseInfo(courseType: $courseType, courseSegmentCode: $courseSegmentCode, courseSegment: $courseSegment, courseKey: $courseKey, courseStatus: $courseStatus, courseName: $courseName)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KnowledgeCourseInfoCopyWith<_$_KnowledgeCourseInfo> get copyWith =>
      __$$_KnowledgeCourseInfoCopyWithImpl<_$_KnowledgeCourseInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_KnowledgeCourseInfoToJson(
      this,
    );
  }
}

abstract class _KnowledgeCourseInfo implements KnowledgeCourseInfo {
  factory _KnowledgeCourseInfo(
      {int? courseType,
      int? courseSegmentCode,
      String? courseSegment,
      String? courseKey,
      int? courseStatus,
      String? courseName}) = _$_KnowledgeCourseInfo;

  factory _KnowledgeCourseInfo.fromJson(Map<String, dynamic> json) =
      _$_KnowledgeCourseInfo.fromJson;

  @override
  int? get courseType;
  set courseType(int? value);
  @override
  int? get courseSegmentCode;
  set courseSegmentCode(int? value);
  @override
  String? get courseSegment;
  set courseSegment(String? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override
  int? get courseStatus;
  set courseStatus(int? value);
  @override
  String? get courseName;
  set courseName(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_KnowledgeCourseInfoCopyWith<_$_KnowledgeCourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

KnowledgeTeacherInfo _$KnowledgeTeacherInfoFromJson(Map<String, dynamic> json) {
  return _KnowledgeTeacherInfo.fromJson(json);
}

/// @nodoc
mixin _$KnowledgeTeacherInfo {
  int? get teacherId => throw _privateConstructorUsedError;
  set teacherId(int? value) => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  set teacherName(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KnowledgeTeacherInfoCopyWith<KnowledgeTeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KnowledgeTeacherInfoCopyWith<$Res> {
  factory $KnowledgeTeacherInfoCopyWith(KnowledgeTeacherInfo value,
          $Res Function(KnowledgeTeacherInfo) then) =
      _$KnowledgeTeacherInfoCopyWithImpl<$Res, KnowledgeTeacherInfo>;
  @useResult
  $Res call({int? teacherId, String? teacherName});
}

/// @nodoc
class _$KnowledgeTeacherInfoCopyWithImpl<$Res,
        $Val extends KnowledgeTeacherInfo>
    implements $KnowledgeTeacherInfoCopyWith<$Res> {
  _$KnowledgeTeacherInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_value.copyWith(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_KnowledgeTeacherInfoCopyWith<$Res>
    implements $KnowledgeTeacherInfoCopyWith<$Res> {
  factory _$$_KnowledgeTeacherInfoCopyWith(_$_KnowledgeTeacherInfo value,
          $Res Function(_$_KnowledgeTeacherInfo) then) =
      __$$_KnowledgeTeacherInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? teacherId, String? teacherName});
}

/// @nodoc
class __$$_KnowledgeTeacherInfoCopyWithImpl<$Res>
    extends _$KnowledgeTeacherInfoCopyWithImpl<$Res, _$_KnowledgeTeacherInfo>
    implements _$$_KnowledgeTeacherInfoCopyWith<$Res> {
  __$$_KnowledgeTeacherInfoCopyWithImpl(_$_KnowledgeTeacherInfo _value,
      $Res Function(_$_KnowledgeTeacherInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_$_KnowledgeTeacherInfo(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_KnowledgeTeacherInfo implements _KnowledgeTeacherInfo {
  _$_KnowledgeTeacherInfo({this.teacherId, this.teacherName});

  factory _$_KnowledgeTeacherInfo.fromJson(Map<String, dynamic> json) =>
      _$$_KnowledgeTeacherInfoFromJson(json);

  @override
  int? teacherId;
  @override
  String? teacherName;

  @override
  String toString() {
    return 'KnowledgeTeacherInfo(teacherId: $teacherId, teacherName: $teacherName)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KnowledgeTeacherInfoCopyWith<_$_KnowledgeTeacherInfo> get copyWith =>
      __$$_KnowledgeTeacherInfoCopyWithImpl<_$_KnowledgeTeacherInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_KnowledgeTeacherInfoToJson(
      this,
    );
  }
}

abstract class _KnowledgeTeacherInfo implements KnowledgeTeacherInfo {
  factory _KnowledgeTeacherInfo({int? teacherId, String? teacherName}) =
      _$_KnowledgeTeacherInfo;

  factory _KnowledgeTeacherInfo.fromJson(Map<String, dynamic> json) =
      _$_KnowledgeTeacherInfo.fromJson;

  @override
  int? get teacherId;
  set teacherId(int? value);
  @override
  String? get teacherName;
  set teacherName(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_KnowledgeTeacherInfoCopyWith<_$_KnowledgeTeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

KnowledgeClassInfo _$KnowledgeClassInfoFromJson(Map<String, dynamic> json) {
  return _KnowledgeClassInfo.fromJson(json);
}

/// @nodoc
mixin _$KnowledgeClassInfo {
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KnowledgeClassInfoCopyWith<KnowledgeClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KnowledgeClassInfoCopyWith<$Res> {
  factory $KnowledgeClassInfoCopyWith(
          KnowledgeClassInfo value, $Res Function(KnowledgeClassInfo) then) =
      _$KnowledgeClassInfoCopyWithImpl<$Res, KnowledgeClassInfo>;
  @useResult
  $Res call({int? classId});
}

/// @nodoc
class _$KnowledgeClassInfoCopyWithImpl<$Res, $Val extends KnowledgeClassInfo>
    implements $KnowledgeClassInfoCopyWith<$Res> {
  _$KnowledgeClassInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_KnowledgeClassInfoCopyWith<$Res>
    implements $KnowledgeClassInfoCopyWith<$Res> {
  factory _$$_KnowledgeClassInfoCopyWith(_$_KnowledgeClassInfo value,
          $Res Function(_$_KnowledgeClassInfo) then) =
      __$$_KnowledgeClassInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? classId});
}

/// @nodoc
class __$$_KnowledgeClassInfoCopyWithImpl<$Res>
    extends _$KnowledgeClassInfoCopyWithImpl<$Res, _$_KnowledgeClassInfo>
    implements _$$_KnowledgeClassInfoCopyWith<$Res> {
  __$$_KnowledgeClassInfoCopyWithImpl(
      _$_KnowledgeClassInfo _value, $Res Function(_$_KnowledgeClassInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
  }) {
    return _then(_$_KnowledgeClassInfo(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_KnowledgeClassInfo implements _KnowledgeClassInfo {
  _$_KnowledgeClassInfo({this.classId});

  factory _$_KnowledgeClassInfo.fromJson(Map<String, dynamic> json) =>
      _$$_KnowledgeClassInfoFromJson(json);

  @override
  int? classId;

  @override
  String toString() {
    return 'KnowledgeClassInfo(classId: $classId)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_KnowledgeClassInfoCopyWith<_$_KnowledgeClassInfo> get copyWith =>
      __$$_KnowledgeClassInfoCopyWithImpl<_$_KnowledgeClassInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_KnowledgeClassInfoToJson(
      this,
    );
  }
}

abstract class _KnowledgeClassInfo implements KnowledgeClassInfo {
  factory _KnowledgeClassInfo({int? classId}) = _$_KnowledgeClassInfo;

  factory _KnowledgeClassInfo.fromJson(Map<String, dynamic> json) =
      _$_KnowledgeClassInfo.fromJson;

  @override
  int? get classId;
  set classId(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_KnowledgeClassInfoCopyWith<_$_KnowledgeClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
