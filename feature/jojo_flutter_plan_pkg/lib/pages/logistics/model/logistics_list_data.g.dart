// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'logistics_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LogisticsListData _$$_LogisticsListDataFromJson(Map<String, dynamic> json) =>
    _$_LogisticsListData(
      customerServiceUrl: json['customerServiceUrl'] as String?,
      logisticInfoList: (json['logisticInfoList'] as List<dynamic>?)
          ?.map((e) => LogisticsItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LogisticsListDataToJson(
        _$_LogisticsListData instance) =>
    <String, dynamic>{
      'customerServiceUrl': instance.customerServiceUrl,
      'logisticInfoList': instance.logisticInfoList,
    };

_$_LogisticsItem _$$_LogisticsItemFromJson(Map<String, dynamic> json) =>
    _$_LogisticsItem(
      deliveredTime: json['deliveredTime'] as int?,
      expressCompany: json['expressCompany'] as String?,
      expressNumber: json['expressNumber'] as String?,
      logisticsDesc: json['logisticsDesc'] as String?,
      logisticsDetailUrl: json['logisticsDetailUrl'] as String?,
      logisticsStatus: json['logisticsStatus'] as String?,
      detailInfoList: (json['detailInfoList'] as List<dynamic>?)
          ?.map((e) => LogisticsDetailItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LogisticsItemToJson(_$_LogisticsItem instance) =>
    <String, dynamic>{
      'deliveredTime': instance.deliveredTime,
      'expressCompany': instance.expressCompany,
      'expressNumber': instance.expressNumber,
      'logisticsDesc': instance.logisticsDesc,
      'logisticsDetailUrl': instance.logisticsDetailUrl,
      'logisticsStatus': instance.logisticsStatus,
      'detailInfoList': instance.detailInfoList,
    };

_$_LogisticsDetailItem _$$_LogisticsDetailItemFromJson(
        Map<String, dynamic> json) =>
    _$_LogisticsDetailItem(
      goodsName: json['goodsName'] as String,
      smallItemNameList: (json['smallItemNameList'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$_LogisticsDetailItemToJson(
        _$_LogisticsDetailItem instance) =>
    <String, dynamic>{
      'goodsName': instance.goodsName,
      'smallItemNameList': instance.smallItemNameList,
    };
