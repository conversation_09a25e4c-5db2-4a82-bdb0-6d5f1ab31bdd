// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'logistics_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LogisticsListData _$LogisticsListDataFromJson(Map<String, dynamic> json) {
  return _LogisticsListData.fromJson(json);
}

/// @nodoc
mixin _$LogisticsListData {
  String? get customerServiceUrl => throw _privateConstructorUsedError;
  List<LogisticsItem>? get logisticInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogisticsListDataCopyWith<LogisticsListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogisticsListDataCopyWith<$Res> {
  factory $LogisticsListDataCopyWith(
          LogisticsListData value, $Res Function(LogisticsListData) then) =
      _$LogisticsListDataCopyWithImpl<$Res, LogisticsListData>;
  @useResult
  $Res call(
      {String? customerServiceUrl, List<LogisticsItem>? logisticInfoList});
}

/// @nodoc
class _$LogisticsListDataCopyWithImpl<$Res, $Val extends LogisticsListData>
    implements $LogisticsListDataCopyWith<$Res> {
  _$LogisticsListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerServiceUrl = freezed,
    Object? logisticInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      customerServiceUrl: freezed == customerServiceUrl
          ? _value.customerServiceUrl
          : customerServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticInfoList: freezed == logisticInfoList
          ? _value.logisticInfoList
          : logisticInfoList // ignore: cast_nullable_to_non_nullable
              as List<LogisticsItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LogisticsListDataCopyWith<$Res>
    implements $LogisticsListDataCopyWith<$Res> {
  factory _$$_LogisticsListDataCopyWith(_$_LogisticsListData value,
          $Res Function(_$_LogisticsListData) then) =
      __$$_LogisticsListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? customerServiceUrl, List<LogisticsItem>? logisticInfoList});
}

/// @nodoc
class __$$_LogisticsListDataCopyWithImpl<$Res>
    extends _$LogisticsListDataCopyWithImpl<$Res, _$_LogisticsListData>
    implements _$$_LogisticsListDataCopyWith<$Res> {
  __$$_LogisticsListDataCopyWithImpl(
      _$_LogisticsListData _value, $Res Function(_$_LogisticsListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerServiceUrl = freezed,
    Object? logisticInfoList = freezed,
  }) {
    return _then(_$_LogisticsListData(
      customerServiceUrl: freezed == customerServiceUrl
          ? _value.customerServiceUrl
          : customerServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticInfoList: freezed == logisticInfoList
          ? _value._logisticInfoList
          : logisticInfoList // ignore: cast_nullable_to_non_nullable
              as List<LogisticsItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LogisticsListData implements _LogisticsListData {
  const _$_LogisticsListData(
      {this.customerServiceUrl, final List<LogisticsItem>? logisticInfoList})
      : _logisticInfoList = logisticInfoList;

  factory _$_LogisticsListData.fromJson(Map<String, dynamic> json) =>
      _$$_LogisticsListDataFromJson(json);

  @override
  final String? customerServiceUrl;
  final List<LogisticsItem>? _logisticInfoList;
  @override
  List<LogisticsItem>? get logisticInfoList {
    final value = _logisticInfoList;
    if (value == null) return null;
    if (_logisticInfoList is EqualUnmodifiableListView)
      return _logisticInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LogisticsListData(customerServiceUrl: $customerServiceUrl, logisticInfoList: $logisticInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LogisticsListData &&
            (identical(other.customerServiceUrl, customerServiceUrl) ||
                other.customerServiceUrl == customerServiceUrl) &&
            const DeepCollectionEquality()
                .equals(other._logisticInfoList, _logisticInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, customerServiceUrl,
      const DeepCollectionEquality().hash(_logisticInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LogisticsListDataCopyWith<_$_LogisticsListData> get copyWith =>
      __$$_LogisticsListDataCopyWithImpl<_$_LogisticsListData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LogisticsListDataToJson(
      this,
    );
  }
}

abstract class _LogisticsListData implements LogisticsListData {
  const factory _LogisticsListData(
      {final String? customerServiceUrl,
      final List<LogisticsItem>? logisticInfoList}) = _$_LogisticsListData;

  factory _LogisticsListData.fromJson(Map<String, dynamic> json) =
      _$_LogisticsListData.fromJson;

  @override
  String? get customerServiceUrl;
  @override
  List<LogisticsItem>? get logisticInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_LogisticsListDataCopyWith<_$_LogisticsListData> get copyWith =>
      throw _privateConstructorUsedError;
}

LogisticsItem _$LogisticsItemFromJson(Map<String, dynamic> json) {
  return _LogisticsItem.fromJson(json);
}

/// @nodoc
mixin _$LogisticsItem {
  /// 发货时间/预计发货时间
  int? get deliveredTime => throw _privateConstructorUsedError;

  /// 物流公司
  String? get expressCompany => throw _privateConstructorUsedError;

  /// 物流单号
  String? get expressNumber => throw _privateConstructorUsedError;

  /// 最新物流状态描述
  String? get logisticsDesc => throw _privateConstructorUsedError;

  /// 物流详情页地址
  String? get logisticsDetailUrl => throw _privateConstructorUsedError;

  /// 物流状态
  String? get logisticsStatus => throw _privateConstructorUsedError;

  /// 包裹明细列表
  List<LogisticsDetailItem>? get detailInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogisticsItemCopyWith<LogisticsItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogisticsItemCopyWith<$Res> {
  factory $LogisticsItemCopyWith(
          LogisticsItem value, $Res Function(LogisticsItem) then) =
      _$LogisticsItemCopyWithImpl<$Res, LogisticsItem>;
  @useResult
  $Res call(
      {int? deliveredTime,
      String? expressCompany,
      String? expressNumber,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      String? logisticsStatus,
      List<LogisticsDetailItem>? detailInfoList});
}

/// @nodoc
class _$LogisticsItemCopyWithImpl<$Res, $Val extends LogisticsItem>
    implements $LogisticsItemCopyWith<$Res> {
  _$LogisticsItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveredTime = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? logisticsStatus = freezed,
    Object? detailInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      deliveredTime: freezed == deliveredTime
          ? _value.deliveredTime
          : deliveredTime // ignore: cast_nullable_to_non_nullable
              as int?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsStatus: freezed == logisticsStatus
          ? _value.logisticsStatus
          : logisticsStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      detailInfoList: freezed == detailInfoList
          ? _value.detailInfoList
          : detailInfoList // ignore: cast_nullable_to_non_nullable
              as List<LogisticsDetailItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LogisticsItemCopyWith<$Res>
    implements $LogisticsItemCopyWith<$Res> {
  factory _$$_LogisticsItemCopyWith(
          _$_LogisticsItem value, $Res Function(_$_LogisticsItem) then) =
      __$$_LogisticsItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? deliveredTime,
      String? expressCompany,
      String? expressNumber,
      String? logisticsDesc,
      String? logisticsDetailUrl,
      String? logisticsStatus,
      List<LogisticsDetailItem>? detailInfoList});
}

/// @nodoc
class __$$_LogisticsItemCopyWithImpl<$Res>
    extends _$LogisticsItemCopyWithImpl<$Res, _$_LogisticsItem>
    implements _$$_LogisticsItemCopyWith<$Res> {
  __$$_LogisticsItemCopyWithImpl(
      _$_LogisticsItem _value, $Res Function(_$_LogisticsItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveredTime = freezed,
    Object? expressCompany = freezed,
    Object? expressNumber = freezed,
    Object? logisticsDesc = freezed,
    Object? logisticsDetailUrl = freezed,
    Object? logisticsStatus = freezed,
    Object? detailInfoList = freezed,
  }) {
    return _then(_$_LogisticsItem(
      deliveredTime: freezed == deliveredTime
          ? _value.deliveredTime
          : deliveredTime // ignore: cast_nullable_to_non_nullable
              as int?,
      expressCompany: freezed == expressCompany
          ? _value.expressCompany
          : expressCompany // ignore: cast_nullable_to_non_nullable
              as String?,
      expressNumber: freezed == expressNumber
          ? _value.expressNumber
          : expressNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDesc: freezed == logisticsDesc
          ? _value.logisticsDesc
          : logisticsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsDetailUrl: freezed == logisticsDetailUrl
          ? _value.logisticsDetailUrl
          : logisticsDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsStatus: freezed == logisticsStatus
          ? _value.logisticsStatus
          : logisticsStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      detailInfoList: freezed == detailInfoList
          ? _value._detailInfoList
          : detailInfoList // ignore: cast_nullable_to_non_nullable
              as List<LogisticsDetailItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LogisticsItem implements _LogisticsItem {
  const _$_LogisticsItem(
      {this.deliveredTime,
      this.expressCompany,
      this.expressNumber,
      this.logisticsDesc,
      this.logisticsDetailUrl,
      this.logisticsStatus,
      final List<LogisticsDetailItem>? detailInfoList})
      : _detailInfoList = detailInfoList;

  factory _$_LogisticsItem.fromJson(Map<String, dynamic> json) =>
      _$$_LogisticsItemFromJson(json);

  /// 发货时间/预计发货时间
  @override
  final int? deliveredTime;

  /// 物流公司
  @override
  final String? expressCompany;

  /// 物流单号
  @override
  final String? expressNumber;

  /// 最新物流状态描述
  @override
  final String? logisticsDesc;

  /// 物流详情页地址
  @override
  final String? logisticsDetailUrl;

  /// 物流状态
  @override
  final String? logisticsStatus;

  /// 包裹明细列表
  final List<LogisticsDetailItem>? _detailInfoList;

  /// 包裹明细列表
  @override
  List<LogisticsDetailItem>? get detailInfoList {
    final value = _detailInfoList;
    if (value == null) return null;
    if (_detailInfoList is EqualUnmodifiableListView) return _detailInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LogisticsItem(deliveredTime: $deliveredTime, expressCompany: $expressCompany, expressNumber: $expressNumber, logisticsDesc: $logisticsDesc, logisticsDetailUrl: $logisticsDetailUrl, logisticsStatus: $logisticsStatus, detailInfoList: $detailInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LogisticsItem &&
            (identical(other.deliveredTime, deliveredTime) ||
                other.deliveredTime == deliveredTime) &&
            (identical(other.expressCompany, expressCompany) ||
                other.expressCompany == expressCompany) &&
            (identical(other.expressNumber, expressNumber) ||
                other.expressNumber == expressNumber) &&
            (identical(other.logisticsDesc, logisticsDesc) ||
                other.logisticsDesc == logisticsDesc) &&
            (identical(other.logisticsDetailUrl, logisticsDetailUrl) ||
                other.logisticsDetailUrl == logisticsDetailUrl) &&
            (identical(other.logisticsStatus, logisticsStatus) ||
                other.logisticsStatus == logisticsStatus) &&
            const DeepCollectionEquality()
                .equals(other._detailInfoList, _detailInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      deliveredTime,
      expressCompany,
      expressNumber,
      logisticsDesc,
      logisticsDetailUrl,
      logisticsStatus,
      const DeepCollectionEquality().hash(_detailInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LogisticsItemCopyWith<_$_LogisticsItem> get copyWith =>
      __$$_LogisticsItemCopyWithImpl<_$_LogisticsItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LogisticsItemToJson(
      this,
    );
  }
}

abstract class _LogisticsItem implements LogisticsItem {
  const factory _LogisticsItem(
      {final int? deliveredTime,
      final String? expressCompany,
      final String? expressNumber,
      final String? logisticsDesc,
      final String? logisticsDetailUrl,
      final String? logisticsStatus,
      final List<LogisticsDetailItem>? detailInfoList}) = _$_LogisticsItem;

  factory _LogisticsItem.fromJson(Map<String, dynamic> json) =
      _$_LogisticsItem.fromJson;

  @override

  /// 发货时间/预计发货时间
  int? get deliveredTime;
  @override

  /// 物流公司
  String? get expressCompany;
  @override

  /// 物流单号
  String? get expressNumber;
  @override

  /// 最新物流状态描述
  String? get logisticsDesc;
  @override

  /// 物流详情页地址
  String? get logisticsDetailUrl;
  @override

  /// 物流状态
  String? get logisticsStatus;
  @override

  /// 包裹明细列表
  List<LogisticsDetailItem>? get detailInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_LogisticsItemCopyWith<_$_LogisticsItem> get copyWith =>
      throw _privateConstructorUsedError;
}

LogisticsDetailItem _$LogisticsDetailItemFromJson(Map<String, dynamic> json) {
  return _LogisticsDetailItem.fromJson(json);
}

/// @nodoc
mixin _$LogisticsDetailItem {
  /// 物流详情页地址
  String get goodsName => throw _privateConstructorUsedError;

  /// 明细名称
  List<String> get smallItemNameList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogisticsDetailItemCopyWith<LogisticsDetailItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogisticsDetailItemCopyWith<$Res> {
  factory $LogisticsDetailItemCopyWith(
          LogisticsDetailItem value, $Res Function(LogisticsDetailItem) then) =
      _$LogisticsDetailItemCopyWithImpl<$Res, LogisticsDetailItem>;
  @useResult
  $Res call({String goodsName, List<String> smallItemNameList});
}

/// @nodoc
class _$LogisticsDetailItemCopyWithImpl<$Res, $Val extends LogisticsDetailItem>
    implements $LogisticsDetailItemCopyWith<$Res> {
  _$LogisticsDetailItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? goodsName = null,
    Object? smallItemNameList = null,
  }) {
    return _then(_value.copyWith(
      goodsName: null == goodsName
          ? _value.goodsName
          : goodsName // ignore: cast_nullable_to_non_nullable
              as String,
      smallItemNameList: null == smallItemNameList
          ? _value.smallItemNameList
          : smallItemNameList // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LogisticsDetailItemCopyWith<$Res>
    implements $LogisticsDetailItemCopyWith<$Res> {
  factory _$$_LogisticsDetailItemCopyWith(_$_LogisticsDetailItem value,
          $Res Function(_$_LogisticsDetailItem) then) =
      __$$_LogisticsDetailItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String goodsName, List<String> smallItemNameList});
}

/// @nodoc
class __$$_LogisticsDetailItemCopyWithImpl<$Res>
    extends _$LogisticsDetailItemCopyWithImpl<$Res, _$_LogisticsDetailItem>
    implements _$$_LogisticsDetailItemCopyWith<$Res> {
  __$$_LogisticsDetailItemCopyWithImpl(_$_LogisticsDetailItem _value,
      $Res Function(_$_LogisticsDetailItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? goodsName = null,
    Object? smallItemNameList = null,
  }) {
    return _then(_$_LogisticsDetailItem(
      goodsName: null == goodsName
          ? _value.goodsName
          : goodsName // ignore: cast_nullable_to_non_nullable
              as String,
      smallItemNameList: null == smallItemNameList
          ? _value._smallItemNameList
          : smallItemNameList // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LogisticsDetailItem implements _LogisticsDetailItem {
  const _$_LogisticsDetailItem(
      {required this.goodsName, required final List<String> smallItemNameList})
      : _smallItemNameList = smallItemNameList;

  factory _$_LogisticsDetailItem.fromJson(Map<String, dynamic> json) =>
      _$$_LogisticsDetailItemFromJson(json);

  /// 物流详情页地址
  @override
  final String goodsName;

  /// 明细名称
  final List<String> _smallItemNameList;

  /// 明细名称
  @override
  List<String> get smallItemNameList {
    if (_smallItemNameList is EqualUnmodifiableListView)
      return _smallItemNameList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_smallItemNameList);
  }

  @override
  String toString() {
    return 'LogisticsDetailItem(goodsName: $goodsName, smallItemNameList: $smallItemNameList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LogisticsDetailItem &&
            (identical(other.goodsName, goodsName) ||
                other.goodsName == goodsName) &&
            const DeepCollectionEquality()
                .equals(other._smallItemNameList, _smallItemNameList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, goodsName,
      const DeepCollectionEquality().hash(_smallItemNameList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LogisticsDetailItemCopyWith<_$_LogisticsDetailItem> get copyWith =>
      __$$_LogisticsDetailItemCopyWithImpl<_$_LogisticsDetailItem>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LogisticsDetailItemToJson(
      this,
    );
  }
}

abstract class _LogisticsDetailItem implements LogisticsDetailItem {
  const factory _LogisticsDetailItem(
      {required final String goodsName,
      required final List<String> smallItemNameList}) = _$_LogisticsDetailItem;

  factory _LogisticsDetailItem.fromJson(Map<String, dynamic> json) =
      _$_LogisticsDetailItem.fromJson;

  @override

  /// 物流详情页地址
  String get goodsName;
  @override

  /// 明细名称
  List<String> get smallItemNameList;
  @override
  @JsonKey(ignore: true)
  _$$_LogisticsDetailItemCopyWith<_$_LogisticsDetailItem> get copyWith =>
      throw _privateConstructorUsedError;
}
