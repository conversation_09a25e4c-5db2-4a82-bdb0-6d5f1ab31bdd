import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import 'model/adjust_record_list_data.dart';

class AdjustInfoListState {
  PageStatus pageStatus;

  AdjustInfoListData listData;
  Map<String, String> queryParams;

  AdjustInfoListState({
    required this.pageStatus,
    required this.listData,
    required this.queryParams,
  });

  AdjustInfoListState copyWith() {
    return AdjustInfoListState(
      pageStatus: pageStatus,
      listData: listData,
      queryParams: queryParams,
    );
  }
}
