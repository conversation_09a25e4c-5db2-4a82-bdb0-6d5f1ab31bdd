// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'adjust_record_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AdjustInfoListData _$AdjustInfoListDataFromJson(Map<String, dynamic> json) {
  return _AdjustInfoList.fromJson(json);
}

/// @nodoc
mixin _$AdjustInfoListData {
  List<RecordItem>? get recordList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdjustInfoListDataCopyWith<AdjustInfoListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdjustInfoListDataCopyWith<$Res> {
  factory $AdjustInfoListDataCopyWith(
          AdjustInfoListData value, $Res Function(AdjustInfoListData) then) =
      _$AdjustInfoListDataCopyWithImpl<$Res, AdjustInfoListData>;
  @useResult
  $Res call({List<RecordItem>? recordList});
}

/// @nodoc
class _$AdjustInfoListDataCopyWithImpl<$Res, $Val extends AdjustInfoListData>
    implements $AdjustInfoListDataCopyWith<$Res> {
  _$AdjustInfoListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recordList = freezed,
  }) {
    return _then(_value.copyWith(
      recordList: freezed == recordList
          ? _value.recordList
          : recordList // ignore: cast_nullable_to_non_nullable
              as List<RecordItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdjustInfoListCopyWith<$Res>
    implements $AdjustInfoListDataCopyWith<$Res> {
  factory _$$_AdjustInfoListCopyWith(
          _$_AdjustInfoList value, $Res Function(_$_AdjustInfoList) then) =
      __$$_AdjustInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<RecordItem>? recordList});
}

/// @nodoc
class __$$_AdjustInfoListCopyWithImpl<$Res>
    extends _$AdjustInfoListDataCopyWithImpl<$Res, _$_AdjustInfoList>
    implements _$$_AdjustInfoListCopyWith<$Res> {
  __$$_AdjustInfoListCopyWithImpl(
      _$_AdjustInfoList _value, $Res Function(_$_AdjustInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recordList = freezed,
  }) {
    return _then(_$_AdjustInfoList(
      recordList: freezed == recordList
          ? _value._recordList
          : recordList // ignore: cast_nullable_to_non_nullable
              as List<RecordItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdjustInfoList implements _AdjustInfoList {
  const _$_AdjustInfoList({final List<RecordItem>? recordList})
      : _recordList = recordList;

  factory _$_AdjustInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_AdjustInfoListFromJson(json);

  final List<RecordItem>? _recordList;
  @override
  List<RecordItem>? get recordList {
    final value = _recordList;
    if (value == null) return null;
    if (_recordList is EqualUnmodifiableListView) return _recordList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AdjustInfoListData(recordList: $recordList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdjustInfoList &&
            const DeepCollectionEquality()
                .equals(other._recordList, _recordList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_recordList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdjustInfoListCopyWith<_$_AdjustInfoList> get copyWith =>
      __$$_AdjustInfoListCopyWithImpl<_$_AdjustInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdjustInfoListToJson(
      this,
    );
  }
}

abstract class _AdjustInfoList implements AdjustInfoListData {
  const factory _AdjustInfoList({final List<RecordItem>? recordList}) =
      _$_AdjustInfoList;

  factory _AdjustInfoList.fromJson(Map<String, dynamic> json) =
      _$_AdjustInfoList.fromJson;

  @override
  List<RecordItem>? get recordList;
  @override
  @JsonKey(ignore: true)
  _$$_AdjustInfoListCopyWith<_$_AdjustInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

RecordItem _$RecordItemFromJson(Map<String, dynamic> json) {
  return _RecordItem.fromJson(json);
}

/// @nodoc
mixin _$RecordItem {
  String? get courseName => throw _privateConstructorUsedError; // 课程名称
  String? get transClassDate => throw _privateConstructorUsedError; // 	转班时间
  String? get sourceClassStartDate =>
      throw _privateConstructorUsedError; // 	老班期开班时间
  String? get targetClassStartDate =>
      throw _privateConstructorUsedError; // 	新班期开班时间
  int? get status => throw _privateConstructorUsedError; //	转班状态（1成功，0失败，2进行中）
  String? get subjectTypeName => throw _privateConstructorUsedError; // 科目名称
  String? get courseTypeName => throw _privateConstructorUsedError; // 课程类型名称
  @JsonKey(
      name: 'subjectType',
      fromJson: SubjectTypeColor.fromString,
      toJson: SubjectTypeColor.toJson)
  SubjectTypeColor? get subjectTypeColor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecordItemCopyWith<RecordItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordItemCopyWith<$Res> {
  factory $RecordItemCopyWith(
          RecordItem value, $Res Function(RecordItem) then) =
      _$RecordItemCopyWithImpl<$Res, RecordItem>;
  @useResult
  $Res call(
      {String? courseName,
      String? transClassDate,
      String? sourceClassStartDate,
      String? targetClassStartDate,
      int? status,
      String? subjectTypeName,
      String? courseTypeName,
      @JsonKey(
          name: 'subjectType',
          fromJson: SubjectTypeColor.fromString,
          toJson: SubjectTypeColor.toJson)
      SubjectTypeColor? subjectTypeColor});
}

/// @nodoc
class _$RecordItemCopyWithImpl<$Res, $Val extends RecordItem>
    implements $RecordItemCopyWith<$Res> {
  _$RecordItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? transClassDate = freezed,
    Object? sourceClassStartDate = freezed,
    Object? targetClassStartDate = freezed,
    Object? status = freezed,
    Object? subjectTypeName = freezed,
    Object? courseTypeName = freezed,
    Object? subjectTypeColor = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      transClassDate: freezed == transClassDate
          ? _value.transClassDate
          : transClassDate // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceClassStartDate: freezed == sourceClassStartDate
          ? _value.sourceClassStartDate
          : sourceClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      targetClassStartDate: freezed == targetClassStartDate
          ? _value.targetClassStartDate
          : targetClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeName: freezed == courseTypeName
          ? _value.courseTypeName
          : courseTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeColor: freezed == subjectTypeColor
          ? _value.subjectTypeColor
          : subjectTypeColor // ignore: cast_nullable_to_non_nullable
              as SubjectTypeColor?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RecordItemCopyWith<$Res>
    implements $RecordItemCopyWith<$Res> {
  factory _$$_RecordItemCopyWith(
          _$_RecordItem value, $Res Function(_$_RecordItem) then) =
      __$$_RecordItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseName,
      String? transClassDate,
      String? sourceClassStartDate,
      String? targetClassStartDate,
      int? status,
      String? subjectTypeName,
      String? courseTypeName,
      @JsonKey(
          name: 'subjectType',
          fromJson: SubjectTypeColor.fromString,
          toJson: SubjectTypeColor.toJson)
      SubjectTypeColor? subjectTypeColor});
}

/// @nodoc
class __$$_RecordItemCopyWithImpl<$Res>
    extends _$RecordItemCopyWithImpl<$Res, _$_RecordItem>
    implements _$$_RecordItemCopyWith<$Res> {
  __$$_RecordItemCopyWithImpl(
      _$_RecordItem _value, $Res Function(_$_RecordItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? transClassDate = freezed,
    Object? sourceClassStartDate = freezed,
    Object? targetClassStartDate = freezed,
    Object? status = freezed,
    Object? subjectTypeName = freezed,
    Object? courseTypeName = freezed,
    Object? subjectTypeColor = freezed,
  }) {
    return _then(_$_RecordItem(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      transClassDate: freezed == transClassDate
          ? _value.transClassDate
          : transClassDate // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceClassStartDate: freezed == sourceClassStartDate
          ? _value.sourceClassStartDate
          : sourceClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      targetClassStartDate: freezed == targetClassStartDate
          ? _value.targetClassStartDate
          : targetClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeName: freezed == courseTypeName
          ? _value.courseTypeName
          : courseTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeColor: freezed == subjectTypeColor
          ? _value.subjectTypeColor
          : subjectTypeColor // ignore: cast_nullable_to_non_nullable
              as SubjectTypeColor?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RecordItem implements _RecordItem {
  const _$_RecordItem(
      {this.courseName,
      this.transClassDate,
      this.sourceClassStartDate,
      this.targetClassStartDate,
      this.status,
      this.subjectTypeName,
      this.courseTypeName,
      @JsonKey(
          name: 'subjectType',
          fromJson: SubjectTypeColor.fromString,
          toJson: SubjectTypeColor.toJson)
      this.subjectTypeColor});

  factory _$_RecordItem.fromJson(Map<String, dynamic> json) =>
      _$$_RecordItemFromJson(json);

  @override
  final String? courseName;
// 课程名称
  @override
  final String? transClassDate;
// 	转班时间
  @override
  final String? sourceClassStartDate;
// 	老班期开班时间
  @override
  final String? targetClassStartDate;
// 	新班期开班时间
  @override
  final int? status;
//	转班状态（1成功，0失败，2进行中）
  @override
  final String? subjectTypeName;
// 科目名称
  @override
  final String? courseTypeName;
// 课程类型名称
  @override
  @JsonKey(
      name: 'subjectType',
      fromJson: SubjectTypeColor.fromString,
      toJson: SubjectTypeColor.toJson)
  final SubjectTypeColor? subjectTypeColor;

  @override
  String toString() {
    return 'RecordItem(courseName: $courseName, transClassDate: $transClassDate, sourceClassStartDate: $sourceClassStartDate, targetClassStartDate: $targetClassStartDate, status: $status, subjectTypeName: $subjectTypeName, courseTypeName: $courseTypeName, subjectTypeColor: $subjectTypeColor)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RecordItem &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.transClassDate, transClassDate) ||
                other.transClassDate == transClassDate) &&
            (identical(other.sourceClassStartDate, sourceClassStartDate) ||
                other.sourceClassStartDate == sourceClassStartDate) &&
            (identical(other.targetClassStartDate, targetClassStartDate) ||
                other.targetClassStartDate == targetClassStartDate) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.subjectTypeName, subjectTypeName) ||
                other.subjectTypeName == subjectTypeName) &&
            (identical(other.courseTypeName, courseTypeName) ||
                other.courseTypeName == courseTypeName) &&
            (identical(other.subjectTypeColor, subjectTypeColor) ||
                other.subjectTypeColor == subjectTypeColor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseName,
      transClassDate,
      sourceClassStartDate,
      targetClassStartDate,
      status,
      subjectTypeName,
      courseTypeName,
      subjectTypeColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RecordItemCopyWith<_$_RecordItem> get copyWith =>
      __$$_RecordItemCopyWithImpl<_$_RecordItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RecordItemToJson(
      this,
    );
  }
}

abstract class _RecordItem implements RecordItem {
  const factory _RecordItem(
      {final String? courseName,
      final String? transClassDate,
      final String? sourceClassStartDate,
      final String? targetClassStartDate,
      final int? status,
      final String? subjectTypeName,
      final String? courseTypeName,
      @JsonKey(
          name: 'subjectType',
          fromJson: SubjectTypeColor.fromString,
          toJson: SubjectTypeColor.toJson)
      final SubjectTypeColor? subjectTypeColor}) = _$_RecordItem;

  factory _RecordItem.fromJson(Map<String, dynamic> json) =
      _$_RecordItem.fromJson;

  @override
  String? get courseName;
  @override // 课程名称
  String? get transClassDate;
  @override // 	转班时间
  String? get sourceClassStartDate;
  @override // 	老班期开班时间
  String? get targetClassStartDate;
  @override // 	新班期开班时间
  int? get status;
  @override //	转班状态（1成功，0失败，2进行中）
  String? get subjectTypeName;
  @override // 科目名称
  String? get courseTypeName;
  @override // 课程类型名称
  @JsonKey(
      name: 'subjectType',
      fromJson: SubjectTypeColor.fromString,
      toJson: SubjectTypeColor.toJson)
  SubjectTypeColor? get subjectTypeColor;
  @override
  @JsonKey(ignore: true)
  _$$_RecordItemCopyWith<_$_RecordItem> get copyWith =>
      throw _privateConstructorUsedError;
}
