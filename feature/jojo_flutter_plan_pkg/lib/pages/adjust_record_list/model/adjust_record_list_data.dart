import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/resources/jojo_colors.dart';

part 'adjust_record_list_data.freezed.dart';
part 'adjust_record_list_data.g.dart';

@freezed
class AdjustInfoListData with _$AdjustInfoListData {
  const factory AdjustInfoListData({List<RecordItem>? recordList}) =
      _AdjustInfoList;
  factory AdjustInfoListData.fromJson(Map<String, dynamic> json) =>
      _$AdjustInfoListDataFromJson(json);
}

class SubjectTypeColor {
  final Color? bgColor;
  final Color? fontColor;
  final int? subjectType;

  SubjectTypeColor({
    required this.bgColor,
    required this.fontColor,
    required this.subjectType,
  });

  static SubjectTypeColor fromString(int? type) {
    switch (type) {
      // 益智
      case 1:
        return SubjectTypeColor(
          bgColor: JoJoColors.blue1,
          fontColor: JoJoColors.blue5,
          subjectType: type,
        );
      // 阅读
      case 2:
        return SubjectTypeColor(
          bgColor: JoJoColors.orange1,
          fontColor: JoJoColors.orange5,
          subjectType: type,
        );
      // 创作
      case 3:
        return SubjectTypeColor(
          bgColor: JoJoColors.orange1,
          fontColor: JoJoColors.orange5,
          subjectType: type,
        );
      // 美育
      case 4:
        return SubjectTypeColor(
          bgColor: JoJoColors.purple1,
          fontColor: JoJoColors.purple5,
          subjectType: type,
        );
      // 识字
      case 5:
        return SubjectTypeColor(
          bgColor: JoJoColors.orange1,
          fontColor: JoJoColors.orange5,
          subjectType: type,
        );
      // 英语
      case 6:
        return SubjectTypeColor(
          bgColor: JoJoColors.pink1,
          fontColor: JoJoColors.pink5,
          subjectType: type,
        );
      // 专题
      case 7:
        return SubjectTypeColor(
          bgColor: JoJoColors.yellow1,
          fontColor: JoJoColors.yellow5,
          subjectType: type,
        );
      // 初中语文
      case 8:
        return SubjectTypeColor(
          bgColor: JoJoColors.brown1,
          fontColor: JoJoColors.brown5,
          subjectType: type,
        );
      // 初中数学
      case 9:
        return SubjectTypeColor(
          bgColor: JoJoColors.brown1,
          fontColor: JoJoColors.brown5,
          subjectType: type,
        );
      // 初中英语
      case 10:
        return SubjectTypeColor(
          bgColor: JoJoColors.brown1,
          fontColor: JoJoColors.brown5,
          subjectType: type,
        );
      // 高中语文
      case 11:
        return SubjectTypeColor(
          bgColor: JoJoColors.brown1,
          fontColor: JoJoColors.brown5,
          subjectType: type,
        );
      default:
        return SubjectTypeColor(
          bgColor: JoJoColors.yellow1,
          fontColor: JoJoColors.yellow5,
          subjectType: type,
        );
    }
  }

  static int? toJson(SubjectTypeColor? instance) => instance?.subjectType;
}

@freezed
class RecordItem with _$RecordItem {
  const factory RecordItem({
    String? courseName, // 课程名称
    String? transClassDate, // 	转班时间
    String? sourceClassStartDate, // 	老班期开班时间
    String? targetClassStartDate, // 	新班期开班时间
    int? status, //	转班状态（1成功，0失败，2进行中）
    String? subjectTypeName, // 科目名称
    String? courseTypeName, // 课程类型名称
    @JsonKey(
      name: 'subjectType',
      fromJson: SubjectTypeColor.fromString,
      toJson: SubjectTypeColor.toJson,
    )
        SubjectTypeColor? subjectTypeColor,
  }) = _RecordItem;

  factory RecordItem.fromJson(Map<String, dynamic> json) =>
      _$RecordItemFromJson(json);
}
