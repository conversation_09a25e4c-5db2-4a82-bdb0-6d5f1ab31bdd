// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_record_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AdjustInfoList _$$_AdjustInfoListFromJson(Map<String, dynamic> json) =>
    _$_AdjustInfoList(
      recordList: (json['recordList'] as List<dynamic>?)
          ?.map((e) => RecordItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AdjustInfoListToJson(_$_AdjustInfoList instance) =>
    <String, dynamic>{
      'recordList': instance.recordList,
    };

_$_RecordItem _$$_RecordItemFromJson(Map<String, dynamic> json) =>
    _$_RecordItem(
      courseName: json['courseName'] as String?,
      transClassDate: json['transClassDate'] as String?,
      sourceClassStartDate: json['sourceClassStartDate'] as String?,
      targetClassStartDate: json['targetClassStartDate'] as String?,
      status: json['status'] as int?,
      subjectTypeName: json['subjectTypeName'] as String?,
      courseTypeName: json['courseTypeName'] as String?,
      subjectTypeColor:
          SubjectTypeColor.fromString(json['subjectType'] as int?),
    );

Map<String, dynamic> _$$_RecordItemToJson(_$_RecordItem instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'transClassDate': instance.transClassDate,
      'sourceClassStartDate': instance.sourceClassStartDate,
      'targetClassStartDate': instance.targetClassStartDate,
      'status': instance.status,
      'subjectTypeName': instance.subjectTypeName,
      'courseTypeName': instance.courseTypeName,
      'subjectType': SubjectTypeColor.toJson(instance.subjectTypeColor),
    };
