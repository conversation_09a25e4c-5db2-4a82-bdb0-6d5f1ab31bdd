import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/widgets/adjust_record_item_new.dart';

///计划调整记录页
class AdjustRecordListView extends HookWidget {
  final AdjustInfoListState state;

  const AdjustRecordListView({Key? key, required this.state}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final listData = state.listData.recordList ?? [];
    return Scaffold(
      primary: !JoJoRouter.isWindow,
      backgroundColor: Colors.white,
      appBar: RunEnv.isWeb && !RunEnv.isApp
          ? null
          : const JoJoAppBar(title: '调整记录'),
      body: SafeArea(
        bottom: false,
        child: JoJoPageLoading(
          status: state.pageStatus,
          placeText: state.pageStatus == PageStatus.empty ? '暂无内容' : null,
          retry: () {
            context.read<AdjustRecordListCtrl>().initState(loading: true);
          },
          child: ListView.builder(
              padding: EdgeInsets.fromLTRB(0, pt(14), 0, pt(24)),
              itemCount: listData.length,
              itemBuilder: (context, index) {
                var preItem = index > 0 ? listData[index - 1] : null;
                var item = listData[index];

                return AdjustRecordItemNew(
                  preData: preItem,
                  data: item,
                );
              }),
        ),
      ),
    );
  }
}
