import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/model/adjust_record_list_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/state.dart';
import 'package:jojo_flutter_plan_pkg/service/adjust_record_api.dart';

import '../../common/host_env/host_env.dart';

class AdjustRecordListCtrl extends Cubit<AdjustInfoListState> {
  AdjustRecordListCtrl({required queryParams})
      : super(
          AdjustInfoListState(
            pageStatus: PageStatus.loading,
            listData: const AdjustInfoListData(),
            queryParams: queryParams,
          ),
        ) {
    initState(loading: true, type: queryParams['type']);
  }

  initState({bool? loading = true, String? type}) async {
    if (loading == true) {
      emit(state.copyWith()..pageStatus = PageStatus.loading);
    }
    try {
      var eventName = '\$AppViewScreen';
      var course_type = state.queryParams['courseType'] == '1' ? '训练营' : '年课';
      // if (RunEnv.isWeb && !RunEnv.isApp) {
      //   eventName = 'autoTrack';
      // } else {
      //   eventName = '\$AppViewScreen';
      // }
      if (type == AdjustType.later) {
        RunEnv.sensorsTrack(eventName, {
          '\$element_name': '延后开启_调整记录',
          "course_type": course_type,
        });
      } else {
        RunEnv.sensorsTrack(eventName, {
          '\$element_name': '提前开启_调整记录',
          "course_type": course_type,
        });
      }
      final adjustListData = await AdjustRecordApiService.getAdjustRecord(type);
      final listData = adjustListData.recordList ?? [];

      if (listData.isEmpty) {
        final newState = state.copyWith()
          ..pageStatus = PageStatus.empty
          ..listData = adjustListData;
        emit(newState);
        return;
      }

      final newState = state.copyWith()
        ..pageStatus = PageStatus.success
        ..listData = adjustListData;
      emit(newState);
    } catch (error) {
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
    }
  }
}
