import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/model/adjust_record_list_data.dart';

class AdjustRecordItemNew extends StatelessWidget {
  final RecordItem? preData;
  final RecordItem data;

  const AdjustRecordItemNew({
    super.key,
    required this.preData,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    var showDate = preData?.transClassDate != data.transClassDate &&
        data.transClassDate?.isNotEmpty == true;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showDate) ...[
          SizedBox(
            height: pt(6),
          ),
          SizedBox(
            width: pt(335),
            child: Text(
              data.transClassDate ?? '',
              style: TextStyle(
                fontSize: pt(18),
                color: HexColor('#404040'),
                fontWeight: FontWeight.bold,
                height: 1.5,
              ),
            ),
          ),
          SizedBox(
            height: pt(8),
          ),
        ],
        Container(
          width: pt(335),
          padding: EdgeInsets.symmetric(horizontal: pt(12), vertical: pt(14)),
          margin: EdgeInsets.only(bottom: pt(8)),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(pt(24)),
              color: data.subjectTypeColor?.bgColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.03), // 阴影颜色及透明度
                  spreadRadius: pt(0), // 扩散半径
                  blurRadius: pt(20), // 模糊半径
                  offset: const Offset(0, 0), // 阴影偏移
                ),
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${data.subjectTypeName ?? ''} • ${data.courseTypeName ?? ''}',
                style: TextStyle(
                  fontSize: pt(14),
                  color: data.subjectTypeColor?.fontColor,
                  height: 1.5,
                ),
              ),
              SizedBox(
                height: pt(10),
              ),
              Container(
                padding: EdgeInsets.all(pt(14)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(pt(16)),
                  color: HexColor('#ffffff'),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            data.courseName ?? '',
                            style: TextStyle(
                              fontSize: pt(18),
                              fontWeight: FontWeight.bold,
                              height: 1.5,
                              color: HexColor('#404040'),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(left: pt(24)),
                          child: Text(
                            getStatusText(data.status ?? 0),
                            style: TextStyle(
                              fontSize: pt(12),
                              height: 1.5,
                              color: getStatusColor(data.status ?? 0),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: pt(8),
                    ),
                    Text(
                      '原班次：${data.sourceClassStartDate}',
                      style: TextStyle(
                        fontSize: pt(14),
                        height: 1.5,
                        color: HexColor('#B2B2B2'),
                      ),
                    ),
                    SizedBox(
                      height: pt(8),
                    ),
                    Text(
                      '新班次：${data.targetClassStartDate}',
                      style: TextStyle(
                        fontSize: pt(14),
                        height: 1.5,
                        color: HexColor('#B2B2B2'),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  ///获取调整记录文本
  String getStatusText(int type) {
    switch (type) {
      case 1:
        //调整成功
        return "调整成功";
      case 2:
        //进行中
        return "进行中";
      default:
        //调整失败
        return "调整失败";
    }
  }

  ///获取调整记录颜色
  Color getStatusColor(int type) {
    switch (type) {
      case 1:
        //调整成功
        return HexColor('#666666');
      case 2:
        //进行中
        return HexColor('#6491D9');
      default:
        //调整失败
        return HexColor('#E35B00');
    }
  }
}
