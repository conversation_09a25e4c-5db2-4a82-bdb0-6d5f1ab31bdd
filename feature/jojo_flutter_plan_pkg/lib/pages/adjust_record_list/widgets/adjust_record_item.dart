import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/model/adjust_record_list_data.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';

enum ItemType { first, normal, last }

Widget adjustRecordItemComponent({
  required RecordItem item,
  required ItemType type,
}) {
  return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: pt(16),
        ),

        /// 左边时间轴
        Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 1,
                height: pt(7),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                      color: HexColor(
                          '#${type == ItemType.first ? '00' : ''}E9ECF0')),
                ),
              ),
              Container(
                width: pt(6),
                height: pt(6),
                decoration: BoxDecoration(
                    color: HexColor('#E9ECF0'),
                    borderRadius: BorderRadius.all(Radius.circular(pt(3)))),
              ),
              SizedBox(
                width: 1,
                height: pt(type == ItemType.last ? 0 : 119),
                child: DecoratedBox(
                  decoration: BoxDecoration(color: HexColor('#E9ECF0')),
                ),
              )
            ]),

        SizedBox(
          width: pt(16),
        ),

        Expanded(
            flex: 1,
            child:

                ///右边文本
                Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        //调整时间
                        Text(
                          '${item.transClassDate}',
                          style: TextStyle(
                              fontSize: pt(14, isFontSize: true),
                              color: HexColor('#B2B2B2'),
                              fontWeight: FontWeight.w400,
                              overflow: TextOverflow.ellipsis),
                        ),
                        SizedBox(
                          width: pt(8),
                        ),
                        //调整状态
                        Text(
                          getStatusText(item.status ?? 0),
                          style: TextStyle(
                              fontSize: pt(14, isFontSize: true),
                              color: getStatusColor(item.status ?? 0),
                              fontWeight: FontWeight.w400,
                              overflow: TextOverflow.ellipsis),
                        ),
                      ]),
                  SizedBox(
                    height: pt(8),
                  ),
                  //课程名称
                  Text(
                    '${item.courseName}',
                    style: TextStyle(
                        fontSize: pt(16, isFontSize: true),
                        color: HexColor('#404040'),
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis),
                  ),
                  SizedBox(
                    height: pt(2),
                  ),
                  //旧班次
                  Text(
                    '旧班次：${item.sourceClassStartDate}',
                    style: TextStyle(
                        fontSize: pt(14, isFontSize: true),
                        color: HexColor('#404040'),
                        fontWeight: FontWeight.w400,
                        overflow: TextOverflow.ellipsis),
                  ),
                  SizedBox(
                    height: pt(2),
                  ),
                  //新班次
                  Text(
                    '新班次：${item.targetClassStartDate}',
                    style: TextStyle(
                        fontSize: pt(14, isFontSize: true),
                        color: HexColor('#404040'),
                        fontWeight: FontWeight.w400,
                        overflow: TextOverflow.ellipsis),
                  ),
                ]))
      ]);
}

///获取调整记录颜色
Color getStatusColor(int type) {
  switch (type) {
    case 1:
      //调整成功
      return HexColor('#B2B2B2');
    case 2:
      //进行中
      return HexColor('##6491D9');
    default:
      //调整失败
      return HexColor('#FF9045');
  }
}

///获取调整记录文本
String getStatusText(int type) {
  switch (type) {
    case 1:
      //调整成功
      return "调整成功";
    case 2:
      //进行中
      return "进行中";
    default:
      //调整失败
      return "调整失败";
  }
}
