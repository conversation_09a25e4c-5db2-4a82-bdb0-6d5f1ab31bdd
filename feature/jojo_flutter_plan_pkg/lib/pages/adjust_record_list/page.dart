import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/view.dart';

import 'controller.dart';

class AdjustRecordListPageModel extends BasePage {
  final Map<String, String>? queryParams;

  const AdjustRecordListPageModel({
    Key? key,
    this.queryParams,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AdjustRecordListPageModel();
}

class _AdjustRecordListPageModel extends BaseState<AdjustRecordListPageModel>
    with BasicInitPage {
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => AdjustRecordListCtrl(
        queryParams: widget.queryParams,
      ),
      child: <PERSON><PERSON><PERSON><PERSON><AdjustRecordListCtrl, AdjustInfoListState>(
          builder: (context, state) {
        return AdjustRecordListView(state: state);
      }),
    );
  }
}
