// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_page_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LessonPageData _$$_LessonPageDataFromJson(Map<String, dynamic> json) =>
    _$_LessonPageData(
      subjectTabList: (json['subjectTabList'] as List<dynamic>?)
          ?.map((e) => lessonTitleBar.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseStatusTabList: (json['courseStatusTabList'] as List<dynamic>?)
          ?.map((e) => lessonStatusData.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseCardList: (json['courseCardList'] as List<dynamic>?)
          ?.map((e) => cardModelData.fromJson(e as Map<String, dynamic>))
          .toList(),
      extendBar: json['extendBar'] == null
          ? null
          : extendData.fromJson(json['extendBar'] as Map<String, dynamic>),
      subjectCourseStatusMap:
          json['subjectCourseStatusMap'] as Map<String, dynamic>?,
      applyCourseCardInfoList: (json['applyCourseCardInfoList']
              as List<dynamic>?)
          ?.map((e) => lessonApplyCardInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      todayPlanData: json['todayPlanData'] == null
          ? null
          : todayPlanInfoData
              .fromJson(json['todayPlanData'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_LessonPageDataToJson(_$_LessonPageData instance) =>
    <String, dynamic>{
      'subjectTabList': instance.subjectTabList,
      'courseStatusTabList': instance.courseStatusTabList,
      'courseCardList': instance.courseCardList,
      'extendBar': instance.extendBar,
      'subjectCourseStatusMap': instance.subjectCourseStatusMap,
      'applyCourseCardInfoList': instance.applyCourseCardInfoList,
      'todayPlanData': instance.todayPlanData,
    };

_$_lessonTitleBar _$$_lessonTitleBarFromJson(Map<String, dynamic> json) =>
    _$_lessonTitleBar(
      type: json['type'] as int?,
      typeDesc: json['typeDesc'] as String?,
      icon: json['icon'] as String?,
      selectedIcon: json['selectedIcon'] as String?,
      selectedBgColor: json['selectedBgColor'] as String?,
      selectedTextColor: json['selectedTextColor'] as String?,
      isPosition: json['isPosition'] as bool?,
      activateIconShow: json['activateIconShow'] as bool?,
      needClassificationInSubject: json['needClassificationInSubject'] as int?,
      cardDataList: (json['cardDataList'] as List<dynamic>?)
          ?.map((e) => cardSectionData.fromJson(e as Map<String, dynamic>))
          .toList(),
      videoCardDataList: (json['videoCardDataList'] as List<dynamic>?)
          ?.map((e) => cardSectionData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_lessonTitleBarToJson(_$_lessonTitleBar instance) =>
    <String, dynamic>{
      'type': instance.type,
      'typeDesc': instance.typeDesc,
      'icon': instance.icon,
      'selectedIcon': instance.selectedIcon,
      'selectedBgColor': instance.selectedBgColor,
      'selectedTextColor': instance.selectedTextColor,
      'isPosition': instance.isPosition,
      'activateIconShow': instance.activateIconShow,
      'needClassificationInSubject': instance.needClassificationInSubject,
      'cardDataList': instance.cardDataList,
      'videoCardDataList': instance.videoCardDataList,
    };

_$_cardSectionData _$$_cardSectionDataFromJson(Map<String, dynamic> json) =>
    _$_cardSectionData(
      headerModel: json['headerModel'] == null
          ? null
          : headerData.fromJson(json['headerModel'] as Map<String, dynamic>),
      lessonCardList: (json['lessonCardList'] as List<dynamic>?)
          ?.map((e) => cardModelData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_cardSectionDataToJson(_$_cardSectionData instance) =>
    <String, dynamic>{
      'headerModel': instance.headerModel,
      'lessonCardList': instance.lessonCardList,
    };

_$_headerData _$$_headerDataFromJson(Map<String, dynamic> json) =>
    _$_headerData(
      courseStatus: json['courseStatus'] as int?,
      isUnfold: json['isUnfold'] as bool?,
      isUnfoldForward: json['isUnfoldForward'] as bool?,
      isHidden: json['isHidden'] as bool?,
      title: json['title'] as String?,
      tipText: json['tipText'] as String?,
    );

Map<String, dynamic> _$$_headerDataToJson(_$_headerData instance) =>
    <String, dynamic>{
      'courseStatus': instance.courseStatus,
      'isUnfold': instance.isUnfold,
      'isUnfoldForward': instance.isUnfoldForward,
      'isHidden': instance.isHidden,
      'title': instance.title,
      'tipText': instance.tipText,
    };

_$_lessonStatusData _$$_lessonStatusDataFromJson(Map<String, dynamic> json) =>
    _$_lessonStatusData(
      status: json['status'] as int?,
      statusDesc: json['statusDesc'] as String?,
      courseCount: json['courseCount'] as int?,
      hasNoCourseTips: json['hasNoCourseTips'] as String?,
      statusDescTag: json['statusDescTag'] as String?,
      hasNoCourseText: json['hasNoCourseText'] as String?,
    );

Map<String, dynamic> _$$_lessonStatusDataToJson(_$_lessonStatusData instance) =>
    <String, dynamic>{
      'status': instance.status,
      'statusDesc': instance.statusDesc,
      'courseCount': instance.courseCount,
      'hasNoCourseTips': instance.hasNoCourseTips,
      'statusDescTag': instance.statusDescTag,
      'hasNoCourseText': instance.hasNoCourseText,
    };

_$_cardModelData _$$_cardModelDataFromJson(Map<String, dynamic> json) =>
    _$_cardModelData(
      cardLabel: json['cardLabel'] as String?,
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      segmentName: json['segmentName'] as String?,
      courseType: json['courseType'] as int?,
      courseTypeIcon: json['courseTypeIcon'] as String?,
      courseLabelUrl: json['courseLabelUrl'] as String?,
      courseId: json['courseId'] as int?,
      courseName: json['courseName'] as String?,
      courseStatus: json['courseStatus'] as int?,
      courseKey: json['courseKey'] as String?,
      teacherId: json['teacherId'] as int?,
      teacherProfileUrl: json['teacherProfileUrl'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      bgColor: json['bgColor'] as String?,
      labelFontColor: json['labelFontColor'] as String?,
      cardMainColor: json['cardMainColor'] as String?,
      topLeftLabelFontColor: json['topLeftLabelFontColor'] as String?,
      courseFinishedIcon: json['courseFinishedIcon'] as String?,
      startNotify: json['startNotify'] as String?,
      rollingCourseNextStartNotify:
          json['rollingCourseNextStartNotify'] as String?,
      isAddTeacher: json['isAddTeacher'] as bool?,
      addTeacherUrl: json['addTeacherUrl'] as String?,
      addTeacherText: json['addTeacherText'] as String?,
      addTeacherEntranceTip: json['addTeacherEntranceTip'] as String?,
      courseStatusDesc: json['courseStatusDesc'] as String?,
      cardStyle: json['cardStyle'] as String?,
      enumCardStyle: json['enumCardStyle'] as int?,
      experienceCardData: json['experienceCardData'] == null
          ? null
          : packageTwoData
              .fromJson(json['experienceCardData'] as Map<String, dynamic>),
      courseTypeDesc: json['courseTypeDesc'] as String?,
      notifySettingRoute: json['notifySettingRoute'] as String?,
      userCourseId: json['userCourseId'] as int?,
      cardHeight: json['cardHeight'] as int?,
      associateCourseNumText: json['associateCourseNumText'] as String?,
      associateCourseGiftsText: json['associateCourseGiftsText'] as String?,
      logisticsMsg: json['logisticsMsg'] as String?,
      cardLabelList: (json['cardLabelList'] as List<dynamic>?)
          ?.map((e) => cardLabelData.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseCardBaseData: json['courseCardBaseData'] == null
          ? null
          : courseCardBase
              .fromJson(json['courseCardBaseData'] as Map<String, dynamic>),
      courseServiceList: (json['courseServiceList'] as List<dynamic>?)
          ?.map((e) => courseServiceData.fromJson(e as Map<String, dynamic>))
          .toList(),
      waitActivateCardInfo: json['waitActivateCardInfo'] == null
          ? null
          : waitActivateCardInfoData
              .fromJson(json['waitActivateCardInfo'] as Map<String, dynamic>),
      viewHeight: json['viewHeight'] as int?,
      newGetFlag: json['newGetFlag'] as int?,
      newGetGuideText: json['newGetGuideText'] as String?,
      hasShip: json['hasShip'] as int?,
      shipGuideText: json['shipGuideText'] as String?,
      newVersionFlag: json['newVersionFlag'] as bool?,
      serviceGuideText: json['serviceGuideText'] as String?,
      isNewLessonGuideIsShowed: json['isNewLessonGuideIsShowed'] as bool?,
      isCampCardStyle1: json['isCampCardStyle1'] as bool?,
      businessTypeDesc: json['businessTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_cardModelDataToJson(_$_cardModelData instance) =>
    <String, dynamic>{
      'cardLabel': instance.cardLabel,
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'segmentName': instance.segmentName,
      'courseType': instance.courseType,
      'courseTypeIcon': instance.courseTypeIcon,
      'courseLabelUrl': instance.courseLabelUrl,
      'courseId': instance.courseId,
      'courseName': instance.courseName,
      'courseStatus': instance.courseStatus,
      'courseKey': instance.courseKey,
      'teacherId': instance.teacherId,
      'teacherProfileUrl': instance.teacherProfileUrl,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'bgColor': instance.bgColor,
      'labelFontColor': instance.labelFontColor,
      'cardMainColor': instance.cardMainColor,
      'topLeftLabelFontColor': instance.topLeftLabelFontColor,
      'courseFinishedIcon': instance.courseFinishedIcon,
      'startNotify': instance.startNotify,
      'rollingCourseNextStartNotify': instance.rollingCourseNextStartNotify,
      'isAddTeacher': instance.isAddTeacher,
      'addTeacherUrl': instance.addTeacherUrl,
      'addTeacherText': instance.addTeacherText,
      'addTeacherEntranceTip': instance.addTeacherEntranceTip,
      'courseStatusDesc': instance.courseStatusDesc,
      'cardStyle': instance.cardStyle,
      'enumCardStyle': instance.enumCardStyle,
      'experienceCardData': instance.experienceCardData,
      'courseTypeDesc': instance.courseTypeDesc,
      'notifySettingRoute': instance.notifySettingRoute,
      'userCourseId': instance.userCourseId,
      'cardHeight': instance.cardHeight,
      'associateCourseNumText': instance.associateCourseNumText,
      'associateCourseGiftsText': instance.associateCourseGiftsText,
      'logisticsMsg': instance.logisticsMsg,
      'cardLabelList': instance.cardLabelList,
      'courseCardBaseData': instance.courseCardBaseData,
      'courseServiceList': instance.courseServiceList,
      'waitActivateCardInfo': instance.waitActivateCardInfo,
      'viewHeight': instance.viewHeight,
      'newGetFlag': instance.newGetFlag,
      'newGetGuideText': instance.newGetGuideText,
      'hasShip': instance.hasShip,
      'shipGuideText': instance.shipGuideText,
      'newVersionFlag': instance.newVersionFlag,
      'serviceGuideText': instance.serviceGuideText,
      'isNewLessonGuideIsShowed': instance.isNewLessonGuideIsShowed,
      'isCampCardStyle1': instance.isCampCardStyle1,
      'businessTypeDesc': instance.businessTypeDesc,
    };

_$_packageTwoData _$$_packageTwoDataFromJson(Map<String, dynamic> json) =>
    _$_packageTwoData(
      forestActivity: json['forestActivity'] == null
          ? null
          : packageTwoItemData
              .fromJson(json['forestActivity'] as Map<String, dynamic>),
      headCardItems: (json['headCardItems'] as List<dynamic>?)
          ?.map((e) => packageTwoItemData.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonCardItems: (json['lessonCardItems'] as List<dynamic>?)
          ?.map((e) => packageTwoItemData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_packageTwoDataToJson(_$_packageTwoData instance) =>
    <String, dynamic>{
      'forestActivity': instance.forestActivity,
      'headCardItems': instance.headCardItems,
      'lessonCardItems': instance.lessonCardItems,
    };

_$_packageTwoItemData _$$_packageTwoItemDataFromJson(
        Map<String, dynamic> json) =>
    _$_packageTwoItemData(
      title: json['title'] as String?,
      icon: json['icon'] as String?,
      content: json['content'] as String?,
      desc: json['desc'] as String?,
      type: json['type'] as String?,
      status: json['status'] as int?,
      startDate: json['startDate'] as String?,
      startWeek: json['startWeek'] as String?,
      redirectUrl: json['redirectUrl'] as String?,
      businessTypeDesc: json['businessTypeDesc'] as String?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      lessonId: json['lessonId'] as int?,
    );

Map<String, dynamic> _$$_packageTwoItemDataToJson(
        _$_packageTwoItemData instance) =>
    <String, dynamic>{
      'title': instance.title,
      'icon': instance.icon,
      'content': instance.content,
      'desc': instance.desc,
      'type': instance.type,
      'status': instance.status,
      'startDate': instance.startDate,
      'startWeek': instance.startWeek,
      'redirectUrl': instance.redirectUrl,
      'businessTypeDesc': instance.businessTypeDesc,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'lessonId': instance.lessonId,
    };

_$_cardLabelData _$$_cardLabelDataFromJson(Map<String, dynamic> json) =>
    _$_cardLabelData(
      labelContent: json['labelContent'] as String?,
      labelEffectType: json['labelEffectType'] as int?,
      labelResourceType: json['labelResourceType'] as int?,
    );

Map<String, dynamic> _$$_cardLabelDataToJson(_$_cardLabelData instance) =>
    <String, dynamic>{
      'labelContent': instance.labelContent,
      'labelEffectType': instance.labelEffectType,
      'labelResourceType': instance.labelResourceType,
    };

_$_courseCardBase _$$_courseCardBaseFromJson(Map<String, dynamic> json) =>
    _$_courseCardBase(
      courseName: json['courseName'] as String?,
      courseImage: json['courseImage'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_courseCardBaseToJson(_$_courseCardBase instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'courseImage': instance.courseImage,
      'route': instance.route,
    };

_$_waitActivateCardInfoData _$$_waitActivateCardInfoDataFromJson(
        Map<String, dynamic> json) =>
    _$_waitActivateCardInfoData(
      attachedCourseData: json['attachedCourseData'] as String?,
      status: json['status'] as int?,
      hasStarted: json['hasStarted'] as int?,
      startedText: json['startedText'] as String?,
      route: json['route'] as String?,
      nonActivePrompt: json['nonActivePrompt'] as String?,
      latestClassTime: (json['latestClassTime'] as num?)?.toDouble(),
      activeNotifyTime: (json['activeNotifyTime'] as num?)?.toDouble(),
      btnText: json['btnText'] as String?,
      activeNotifyModifyTip: json['activeNotifyModifyTip'] as String?,
    );

Map<String, dynamic> _$$_waitActivateCardInfoDataToJson(
        _$_waitActivateCardInfoData instance) =>
    <String, dynamic>{
      'attachedCourseData': instance.attachedCourseData,
      'status': instance.status,
      'hasStarted': instance.hasStarted,
      'startedText': instance.startedText,
      'route': instance.route,
      'nonActivePrompt': instance.nonActivePrompt,
      'latestClassTime': instance.latestClassTime,
      'activeNotifyTime': instance.activeNotifyTime,
      'btnText': instance.btnText,
      'activeNotifyModifyTip': instance.activeNotifyModifyTip,
    };

_$_courseServiceData _$$_courseServiceDataFromJson(Map<String, dynamic> json) =>
    _$_courseServiceData(
      configKey: json['configKey'] as String?,
      configName: json['configName'] as String?,
      icon: json['icon'] as String?,
      redirectUrl: json['redirectUrl'] as String?,
      type: json['type'] as int?,
      title: json['title'] as String?,
      serviceNewMsgTipText: json['serviceNewMsgTipText'] as String?,
      activeIcon: json['activeIcon'] as String?,
      subjectImage: json['subjectImage'] as String?,
      isShowActiveIcon: json['isShowActiveIcon'] as bool?,
      encourageIcon: json['encourageIcon'] as String?,
      serviceUpdateStatus: json['serviceUpdateStatus'] as int?,
      redDot: json['redDot'] as int?,
      hasMakeupLesson: json['hasMakeupLesson'] as int?,
      lessonCoverImageList: (json['lessonCoverImageList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      serviceUpdateBubbleList:
          (json['serviceUpdateBubbleList'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      guideResource: json['guideResource'] == null
          ? null
          : todayPlanResource
              .fromJson(json['guideResource'] as Map<String, dynamic>),
      activityStatus: json['activityStatus'] as int?,
      challengeActivityGuideAudio:
          json['challengeActivityGuideAudio'] as String?,
      padIcon: json['padIcon'] as String?,
      button: json['button'] as String?,
      businessTypeDesc: json['businessTypeDesc'] as String?,
      desc: json['desc'] as String?,
      plantUsedDrips: json['plantUsedDrips'] as int?,
      plantNeedDrips: json['plantNeedDrips'] as int?,
      plantWatered: json['plantWatered'] as int?,
      expandAudio: json['expandAudio'] as String?,
      expandEffect: json['expandEffect'] as String?,
      toastTips: json['toastTips'] as String?,
    );

Map<String, dynamic> _$$_courseServiceDataToJson(
        _$_courseServiceData instance) =>
    <String, dynamic>{
      'configKey': instance.configKey,
      'configName': instance.configName,
      'icon': instance.icon,
      'redirectUrl': instance.redirectUrl,
      'type': instance.type,
      'title': instance.title,
      'serviceNewMsgTipText': instance.serviceNewMsgTipText,
      'activeIcon': instance.activeIcon,
      'subjectImage': instance.subjectImage,
      'isShowActiveIcon': instance.isShowActiveIcon,
      'encourageIcon': instance.encourageIcon,
      'serviceUpdateStatus': instance.serviceUpdateStatus,
      'redDot': instance.redDot,
      'hasMakeupLesson': instance.hasMakeupLesson,
      'lessonCoverImageList': instance.lessonCoverImageList,
      'serviceUpdateBubbleList': instance.serviceUpdateBubbleList,
      'guideResource': instance.guideResource,
      'activityStatus': instance.activityStatus,
      'challengeActivityGuideAudio': instance.challengeActivityGuideAudio,
      'padIcon': instance.padIcon,
      'button': instance.button,
      'businessTypeDesc': instance.businessTypeDesc,
      'desc': instance.desc,
      'plantUsedDrips': instance.plantUsedDrips,
      'plantNeedDrips': instance.plantNeedDrips,
      'plantWatered': instance.plantWatered,
      'expandAudio': instance.expandAudio,
      'expandEffect': instance.expandEffect,
      'toastTips': instance.toastTips,
    };

_$_extendData _$$_extendDataFromJson(Map<String, dynamic> json) =>
    _$_extendData(
      haveCourseStatus: json['haveCourseStatus'] as int?,
      contactCustomerContentList:
          (json['contactCustomerContentList'] as List<dynamic>?)
              ?.map((e) => contactCustomer.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_extendDataToJson(_$_extendData instance) =>
    <String, dynamic>{
      'haveCourseStatus': instance.haveCourseStatus,
      'contactCustomerContentList': instance.contactCustomerContentList,
    };

_$_contactCustomer _$$_contactCustomerFromJson(Map<String, dynamic> json) =>
    _$_contactCustomer(
      color: json['color'] as String?,
      text: json['text'] as String?,
      linkUrl: json['linkUrl'] as String?,
    );

Map<String, dynamic> _$$_contactCustomerToJson(_$_contactCustomer instance) =>
    <String, dynamic>{
      'color': instance.color,
      'text': instance.text,
      'linkUrl': instance.linkUrl,
    };

_$_lessonApplyCardInfo _$$_lessonApplyCardInfoFromJson(
        Map<String, dynamic> json) =>
    _$_lessonApplyCardInfo(
      subjectType: json['subjectType'] as int?,
      imageUrl: json['imageUrl'] as String?,
      applyCourseUrl: json['applyCourseUrl'] as String?,
      videoName: json['videoName'] as String?,
      videoUrl: json['videoUrl'] as String?,
      videoPictureUrl: json['videoPictureUrl'] as String?,
      videoDesc: json['videoDesc'] as String?,
      videoButtonText: json['videoButtonText'] as String?,
      bgColor: json['bgColor'] as String?,
      cardMainColor: json['cardMainColor'] as String?,
      labelFontColor: json['labelFontColor'] as String?,
      materialName: json['materialName'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      topLeftLabelFontColor: json['topLeftLabelFontColor'] as String?,
      materialId: json['materialId'] as int?,
      recommendType: json['recommendType'] as int?,
      skuRecommendInfo: json['skuRecommendInfo'] == null
          ? null
          : SKURecommenInfo.fromJson(
              json['skuRecommendInfo'] as Map<String, dynamic>),
      liveRecommendInfo: json['liveRecommendInfo'] == null
          ? null
          : LiveRecommenInfo.fromJson(
              json['liveRecommendInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_lessonApplyCardInfoToJson(
        _$_lessonApplyCardInfo instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'imageUrl': instance.imageUrl,
      'applyCourseUrl': instance.applyCourseUrl,
      'videoName': instance.videoName,
      'videoUrl': instance.videoUrl,
      'videoPictureUrl': instance.videoPictureUrl,
      'videoDesc': instance.videoDesc,
      'videoButtonText': instance.videoButtonText,
      'bgColor': instance.bgColor,
      'cardMainColor': instance.cardMainColor,
      'labelFontColor': instance.labelFontColor,
      'materialName': instance.materialName,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'topLeftLabelFontColor': instance.topLeftLabelFontColor,
      'materialId': instance.materialId,
      'recommendType': instance.recommendType,
      'skuRecommendInfo': instance.skuRecommendInfo,
      'liveRecommendInfo': instance.liveRecommendInfo,
    };

_$_SKURecommenInfo _$$_SKURecommenInfoFromJson(Map<String, dynamic> json) =>
    _$_SKURecommenInfo(
      materialId: json['materialId'] as int?,
      effectiveTime: (json['effectiveTime'] as num?)?.toDouble(),
      systemRunDurtion: (json['systemRunDurtion'] as num?)?.toDouble(),
      activityId: json['activityId'] as int?,
      linkId: json['linkId'] as String?,
      materialName: json['materialName'] as String?,
      readAbilityIcon: json['readAbilityIcon'] as String?,
      readAbilityName: json['readAbilityName'] as String?,
      readAbilityDesc: json['readAbilityDesc'] as String?,
      lessonOrder: json['lessonOrder'] as String?,
      lessonTitle: json['lessonTitle'] as String?,
      lessonIcon: json['lessonIcon'] as String?,
      lessonContent: json['lessonContent'] as String?,
      lessonButton: json['lessonButton'] as String?,
      recommendOrderButton: json['recommendOrderButton'] as String?,
      skuConfigList: (json['skuConfigList'] as List<dynamic>?)
          ?.map((e) =>
              lessonSKURecommendGrade.fromJson(e as Map<String, dynamic>))
          .toList(),
      assessmentModel: json['assessmentModel'] == null
          ? null
          : courseServiceData
              .fromJson(json['assessmentModel'] as Map<String, dynamic>),
      packageTwoModel: json['packageTwoModel'] == null
          ? null
          : packageTwoItemData
              .fromJson(json['packageTwoModel'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_SKURecommenInfoToJson(_$_SKURecommenInfo instance) =>
    <String, dynamic>{
      'materialId': instance.materialId,
      'effectiveTime': instance.effectiveTime,
      'systemRunDurtion': instance.systemRunDurtion,
      'activityId': instance.activityId,
      'linkId': instance.linkId,
      'materialName': instance.materialName,
      'readAbilityIcon': instance.readAbilityIcon,
      'readAbilityName': instance.readAbilityName,
      'readAbilityDesc': instance.readAbilityDesc,
      'lessonOrder': instance.lessonOrder,
      'lessonTitle': instance.lessonTitle,
      'lessonIcon': instance.lessonIcon,
      'lessonContent': instance.lessonContent,
      'lessonButton': instance.lessonButton,
      'recommendOrderButton': instance.recommendOrderButton,
      'skuConfigList': instance.skuConfigList,
      'assessmentModel': instance.assessmentModel,
      'packageTwoModel': instance.packageTwoModel,
    };

_$_lessonSKURecommendGrade _$$_lessonSKURecommendGradeFromJson(
        Map<String, dynamic> json) =>
    _$_lessonSKURecommendGrade(
      skuId: json['skuId'] as String?,
      skuOption: json['skuOption'] as String?,
      skuChannel: json['skuChannel'] as String?,
    );

Map<String, dynamic> _$$_lessonSKURecommendGradeToJson(
        _$_lessonSKURecommendGrade instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'skuOption': instance.skuOption,
      'skuChannel': instance.skuChannel,
    };

_$_LiveRecommenInfo _$$_LiveRecommenInfoFromJson(Map<String, dynamic> json) =>
    _$_LiveRecommenInfo(
      businessTagId: json['businessTagId'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      liveStreamingCardVideoUrl: json['liveStreamingCardVideoUrl'] as String?,
      liveStreamingDescription: json['liveStreamingDescription'] as String?,
      pushingDecorationUrl: json['pushingDecorationUrl'] as String?,
      liveStreamingVideoUrl: json['liveStreamingVideoUrl'] as String?,
      liveStreamingVideoCoverUrl: json['liveStreamingVideoCoverUrl'] as String?,
      liveStreamingLinkUrl: json['liveStreamingLinkUrl'] as String?,
      productList: (json['productList'] as List<dynamic>?)
          ?.map((e) => liveProductItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      teacherInfo: json['teacherInfo'] == null
          ? null
          : teacherInfoData
              .fromJson(json['teacherInfo'] as Map<String, dynamic>),
      businessTypeDesc: json['businessTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_LiveRecommenInfoToJson(_$_LiveRecommenInfo instance) =>
    <String, dynamic>{
      'businessTagId': instance.businessTagId,
      'backgroundColor': instance.backgroundColor,
      'liveStreamingCardVideoUrl': instance.liveStreamingCardVideoUrl,
      'liveStreamingDescription': instance.liveStreamingDescription,
      'pushingDecorationUrl': instance.pushingDecorationUrl,
      'liveStreamingVideoUrl': instance.liveStreamingVideoUrl,
      'liveStreamingVideoCoverUrl': instance.liveStreamingVideoCoverUrl,
      'liveStreamingLinkUrl': instance.liveStreamingLinkUrl,
      'productList': instance.productList,
      'teacherInfo': instance.teacherInfo,
      'businessTypeDesc': instance.businessTypeDesc,
    };

_$_liveProductItem _$$_liveProductItemFromJson(Map<String, dynamic> json) =>
    _$_liveProductItem(
      productPictureUrl: json['productPictureUrl'] as String?,
      productName: json['productName'] as String?,
      productPrice: json['productPrice'] as String?,
      productTag: json['productTag'] as String?,
      productButtonDescription: json['productButtonDescription'] as String?,
      productLinkUrl: json['productLinkUrl'] as String?,
      productDescription: json['productDescription'] as String?,
      isTeacher: json['isTeacher'] as bool?,
      teacherId: json['teacherId'] as int?,
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_liveProductItemToJson(_$_liveProductItem instance) =>
    <String, dynamic>{
      'productPictureUrl': instance.productPictureUrl,
      'productName': instance.productName,
      'productPrice': instance.productPrice,
      'productTag': instance.productTag,
      'productButtonDescription': instance.productButtonDescription,
      'productLinkUrl': instance.productLinkUrl,
      'productDescription': instance.productDescription,
      'isTeacher': instance.isTeacher,
      'teacherId': instance.teacherId,
      'classId': instance.classId,
    };

_$_teacherInfoData _$$_teacherInfoDataFromJson(Map<String, dynamic> json) =>
    _$_teacherInfoData(
      teacherPictureUrl: json['teacherPictureUrl'] as String?,
      teacherName: json['teacherName'] as String?,
      teacherTag: json['teacherTag'] as String?,
      teacherButtonDescription: json['teacherButtonDescription'] as String?,
      teacherDescription: json['teacherDescription'] as String?,
      teacherLinkUrl: json['teacherLinkUrl'] as String?,
      teacherPrice: json['teacherPrice'] as String?,
      teacherId: json['teacherId'] as int?,
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_teacherInfoDataToJson(_$_teacherInfoData instance) =>
    <String, dynamic>{
      'teacherPictureUrl': instance.teacherPictureUrl,
      'teacherName': instance.teacherName,
      'teacherTag': instance.teacherTag,
      'teacherButtonDescription': instance.teacherButtonDescription,
      'teacherDescription': instance.teacherDescription,
      'teacherLinkUrl': instance.teacherLinkUrl,
      'teacherPrice': instance.teacherPrice,
      'teacherId': instance.teacherId,
      'classId': instance.classId,
    };

_$_todayPlanResource _$$_todayPlanResourceFromJson(Map<String, dynamic> json) =>
    _$_todayPlanResource(
      animation: json['animation'] as String?,
      audio: json['audio'] as String?,
      defaultText: json['defaultText'] as String?,
      defaultIcon: json['defaultIcon'] as String?,
      bgImage: json['bgImage'] as String?,
      image: json['image'] as String?,
      text: json['text'] as String?,
      activityTitle: json['activityTitle'] as String?,
      lottieAnimation: json['lottieAnimation'] as String?,
      foreResource: json['foreResource'] == null
          ? null
          : todayPlanResourceAnimation
              .fromJson(json['foreResource'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_todayPlanResourceToJson(
        _$_todayPlanResource instance) =>
    <String, dynamic>{
      'animation': instance.animation,
      'audio': instance.audio,
      'defaultText': instance.defaultText,
      'defaultIcon': instance.defaultIcon,
      'bgImage': instance.bgImage,
      'image': instance.image,
      'text': instance.text,
      'activityTitle': instance.activityTitle,
      'lottieAnimation': instance.lottieAnimation,
      'foreResource': instance.foreResource,
    };

_$_todayPlanResourceAnimation _$$_todayPlanResourceAnimationFromJson(
        Map<String, dynamic> json) =>
    _$_todayPlanResourceAnimation(
      animationType: json['animationType'] as int?,
      type: json['type'] as String?,
      resourceUrl: json['resourceUrl'] as String?,
    );

Map<String, dynamic> _$$_todayPlanResourceAnimationToJson(
        _$_todayPlanResourceAnimation instance) =>
    <String, dynamic>{
      'animationType': instance.animationType,
      'type': instance.type,
      'resourceUrl': instance.resourceUrl,
    };

_$_advertisementData _$$_advertisementDataFromJson(Map<String, dynamic> json) =>
    _$_advertisementData(
      id: json['id'] as int?,
      advertisementName: json['advertisementName'] as String?,
      advertisementPosition: json['advertisementPosition'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      linkUrl: json['linkUrl'] as String?,
      closeReasonList: (json['closeReasonList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      advertisementId: json['advertisementId'] as int?,
      businessTagId: json['businessTagId'] as String?,
    );

Map<String, dynamic> _$$_advertisementDataToJson(
        _$_advertisementData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'advertisementName': instance.advertisementName,
      'advertisementPosition': instance.advertisementPosition,
      'pictureUrl': instance.pictureUrl,
      'linkUrl': instance.linkUrl,
      'closeReasonList': instance.closeReasonList,
      'advertisementId': instance.advertisementId,
      'businessTagId': instance.businessTagId,
    };

_$_todayPlanInfoData _$$_todayPlanInfoDataFromJson(Map<String, dynamic> json) =>
    _$_todayPlanInfoData(
      allFinishResource: json['allFinishResource'] == null
          ? null
          : todayPlanResourcedata
              .fromJson(json['allFinishResource'] as Map<String, dynamic>),
      latestFinishResource: json['latestFinishResource'] == null
          ? null
          : todayPlanResourcedata
              .fromJson(json['latestFinishResource'] as Map<String, dynamic>),
      nonePlanResource: json['nonePlanResource'] == null
          ? null
          : todayPlanResourcedata
              .fromJson(json['nonePlanResource'] as Map<String, dynamic>),
      processOfAll: json['processOfAll'] as int?,
      processOfFinish: json['processOfFinish'] as int?,
      status: json['status'] as int?,
      shouldPlayFinishOneAnimal: json['shouldPlayFinishOneAnimal'] as bool?,
      shouldPlayFirstFinishAllAnimal:
          json['shouldPlayFirstFinishAllAnimal'] as bool?,
      shouldReloadData: json['shouldReloadData'] as bool?,
      title: json['title'] as String?,
      todayPlanCardList: (json['todayPlanCardList'] as List<dynamic>?)
          ?.map((e) => todayPlanCarddata.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_todayPlanInfoDataToJson(
        _$_todayPlanInfoData instance) =>
    <String, dynamic>{
      'allFinishResource': instance.allFinishResource,
      'latestFinishResource': instance.latestFinishResource,
      'nonePlanResource': instance.nonePlanResource,
      'processOfAll': instance.processOfAll,
      'processOfFinish': instance.processOfFinish,
      'status': instance.status,
      'shouldPlayFinishOneAnimal': instance.shouldPlayFinishOneAnimal,
      'shouldPlayFirstFinishAllAnimal': instance.shouldPlayFirstFinishAllAnimal,
      'shouldReloadData': instance.shouldReloadData,
      'title': instance.title,
      'todayPlanCardList': instance.todayPlanCardList,
    };

_$_todayPlanResourcedata _$$_todayPlanResourcedataFromJson(
        Map<String, dynamic> json) =>
    _$_todayPlanResourcedata(
      animation: json['animation'] as String?,
      audio: json['audio'] as String?,
      defaultText: json['defaultText'] as String?,
      defaultIcon: json['defaultIcon'] as String?,
      bgImage: json['bgImage'] as String?,
      image: json['image'] as String?,
      text: json['text'] as String?,
      activityTitle: json['activityTitle'] as String?,
    );

Map<String, dynamic> _$$_todayPlanResourcedataToJson(
        _$_todayPlanResourcedata instance) =>
    <String, dynamic>{
      'animation': instance.animation,
      'audio': instance.audio,
      'defaultText': instance.defaultText,
      'defaultIcon': instance.defaultIcon,
      'bgImage': instance.bgImage,
      'image': instance.image,
      'text': instance.text,
      'activityTitle': instance.activityTitle,
    };

_$_todayPlanCarddata _$$_todayPlanCarddataFromJson(Map<String, dynamic> json) =>
    _$_todayPlanCarddata(
      userCourseId: json['userCourseId'] as int?,
      classId: json['classId'] as int?,
      mainColor: json['mainColor'] as String?,
      bgColor: json['bgColor'] as String?,
      fontColor: json['fontColor'] as String?,
      btnText: json['btnText'] as String?,
      encourageResource: json['encourageResource'] as String?,
      lessonEntranceImage: json['lessonEntranceImage'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonOrder: json['lessonOrder'] as String?,
      segmentName: json['segmentName'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      route: json['route'] as String?,
      title: json['title'] as String?,
      courseKey: json['courseKey'] as String?,
      isFinishedStudy: json['isFinishedStudy'] as bool?,
      type: json['type'] as int?,
      guideResource: json['guideResource'] == null
          ? null
          : todayPlanResource
              .fromJson(json['guideResource'] as Map<String, dynamic>),
      todayTaskInfo: json['todayTaskInfo'] == null
          ? null
          : todayPlanTaskInfo
              .fromJson(json['todayTaskInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_todayPlanCarddataToJson(
        _$_todayPlanCarddata instance) =>
    <String, dynamic>{
      'userCourseId': instance.userCourseId,
      'classId': instance.classId,
      'mainColor': instance.mainColor,
      'bgColor': instance.bgColor,
      'fontColor': instance.fontColor,
      'btnText': instance.btnText,
      'encourageResource': instance.encourageResource,
      'lessonEntranceImage': instance.lessonEntranceImage,
      'lessonName': instance.lessonName,
      'lessonOrder': instance.lessonOrder,
      'segmentName': instance.segmentName,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'route': instance.route,
      'title': instance.title,
      'courseKey': instance.courseKey,
      'isFinishedStudy': instance.isFinishedStudy,
      'type': instance.type,
      'guideResource': instance.guideResource,
      'todayTaskInfo': instance.todayTaskInfo,
    };

_$_messageBoxInfo _$$_messageBoxInfoFromJson(Map<String, dynamic> json) =>
    _$_messageBoxInfo(
      smallBubbleLinUrl: json['smallBubbleLinUrl'] as String?,
      newMessage: json['newMessage'] as bool?,
      messageList: (json['messageList'] as List<dynamic>?)
          ?.map((e) => messageBoxListInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_messageBoxInfoToJson(_$_messageBoxInfo instance) =>
    <String, dynamic>{
      'smallBubbleLinUrl': instance.smallBubbleLinUrl,
      'newMessage': instance.newMessage,
      'messageList': instance.messageList,
    };

_$_messageBoxListInfo _$$_messageBoxListInfoFromJson(
        Map<String, dynamic> json) =>
    _$_messageBoxListInfo(
      bizId: json['bizId'] as String?,
      messageType: json['messageType'] as String?,
      title: json['title'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      messageContent: json['messageContent'] as String?,
      linkUrl: json['linkUrl'] as String?,
      courseSegment: json['courseSegment'] as String?,
      classTeacherNotifyId: json['classTeacherNotifyId'] as int?,
      notifyId: json['notifyId'] as int?,
      teacherId: json['teacherId'] as int?,
      classId: json['classId'] as int?,
      extendContent: json['extendContent'] == null
          ? null
          : messageBoxItemExtendContent
              .fromJson(json['extendContent'] as Map<String, dynamic>),
      addTeacher: json['addTeacher'] as bool?,
    );

Map<String, dynamic> _$$_messageBoxListInfoToJson(
        _$_messageBoxListInfo instance) =>
    <String, dynamic>{
      'bizId': instance.bizId,
      'messageType': instance.messageType,
      'title': instance.title,
      'pictureUrl': instance.pictureUrl,
      'messageContent': instance.messageContent,
      'linkUrl': instance.linkUrl,
      'courseSegment': instance.courseSegment,
      'classTeacherNotifyId': instance.classTeacherNotifyId,
      'notifyId': instance.notifyId,
      'teacherId': instance.teacherId,
      'classId': instance.classId,
      'extendContent': instance.extendContent,
      'addTeacher': instance.addTeacher,
    };

_$_messageBoxItemExtendContent _$$_messageBoxItemExtendContentFromJson(
        Map<String, dynamic> json) =>
    _$_messageBoxItemExtendContent(
      showExtentContent: json['showExtentContent'] as int?,
      contactTeacherText: json['contactTeacherText'] as String?,
      contactTeacherUrl: json['contactTeacherUrl'] as String?,
      teacherServiceText: json['teacherServiceText'] as String?,
      teacherServiceUrl: json['teacherServiceUrl'] as String?,
      teacherAvatar: json['teacherAvatar'] as String?,
      teacherName: json['teacherName'] as String?,
    );

Map<String, dynamic> _$$_messageBoxItemExtendContentToJson(
        _$_messageBoxItemExtendContent instance) =>
    <String, dynamic>{
      'showExtentContent': instance.showExtentContent,
      'contactTeacherText': instance.contactTeacherText,
      'contactTeacherUrl': instance.contactTeacherUrl,
      'teacherServiceText': instance.teacherServiceText,
      'teacherServiceUrl': instance.teacherServiceUrl,
      'teacherAvatar': instance.teacherAvatar,
      'teacherName': instance.teacherName,
    };

_$_todayPlanTaskInfo _$$_todayPlanTaskInfoFromJson(Map<String, dynamic> json) =>
    _$_todayPlanTaskInfo(
      taskAwardList: (json['taskAwardList'] as List<dynamic>?)
          ?.map((e) =>
              todayPlanActivityTaskAward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_todayPlanTaskInfoToJson(
        _$_todayPlanTaskInfo instance) =>
    <String, dynamic>{
      'taskAwardList': instance.taskAwardList,
    };

_$_todayPlanActivityTaskAward _$$_todayPlanActivityTaskAwardFromJson(
        Map<String, dynamic> json) =>
    _$_todayPlanActivityTaskAward(
      type: json['type'] as int?,
      image: json['image'] as String?,
      name: json['name'] as String?,
      count: json['count'] as int?,
      countText: json['countText'] as String?,
      tips: json['tips'] as String?,
    );

Map<String, dynamic> _$$_todayPlanActivityTaskAwardToJson(
        _$_todayPlanActivityTaskAward instance) =>
    <String, dynamic>{
      'type': instance.type,
      'image': instance.image,
      'name': instance.name,
      'count': instance.count,
      'countText': instance.countText,
      'tips': instance.tips,
    };

_$_exchangePopViewInfo _$$_exchangePopViewInfoFromJson(
        Map<String, dynamic> json) =>
    _$_exchangePopViewInfo(
      showFlag: json['showFlag'] as int?,
      title: json['title'] as String?,
      content: json['content'] as String?,
      notes: json['notes'] as String?,
      popupButtonList: (json['popupButtonList'] as List<dynamic>?)
          ?.map((e) =>
              exchangePopViewButtoinfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_exchangePopViewInfoToJson(
        _$_exchangePopViewInfo instance) =>
    <String, dynamic>{
      'showFlag': instance.showFlag,
      'title': instance.title,
      'content': instance.content,
      'notes': instance.notes,
      'popupButtonList': instance.popupButtonList,
    };

_$_exchangePopViewButtoinfo _$$_exchangePopViewButtoinfoFromJson(
        Map<String, dynamic> json) =>
    _$_exchangePopViewButtoinfo(
      btnText: json['btnText'] as String?,
      btnUrl: json['btnUrl'] as String?,
    );

Map<String, dynamic> _$$_exchangePopViewButtoinfoToJson(
        _$_exchangePopViewButtoinfo instance) =>
    <String, dynamic>{
      'btnText': instance.btnText,
      'btnUrl': instance.btnUrl,
    };

_$_beginnerGuideShowInfo _$$_beginnerGuideShowInfoFromJson(
        Map<String, dynamic> json) =>
    _$_beginnerGuideShowInfo(
      show: json['show'] as bool?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_beginnerGuideShowInfoToJson(
        _$_beginnerGuideShowInfo instance) =>
    <String, dynamic>{
      'show': instance.show,
      'jumpRoute': instance.jumpRoute,
    };

_$_gradeUpdateInfo _$$_gradeUpdateInfoFromJson(Map<String, dynamic> json) =>
    _$_gradeUpdateInfo(
      title: json['title'] as String?,
      notes: json['notes'] as String?,
      content: json['content'] as String?,
      showFlag: json['showFlag'] as int?,
      gradeListSummaryInfo: json['gradeListSummaryInfo'] == null
          ? null
          : gradeSelectGradeList
              .fromJson(json['gradeListSummaryInfo'] as Map<String, dynamic>),
      nextGrade: json['nextGrade'] == null
          ? null
          : gradeSelectGradeListItem
              .fromJson(json['nextGrade'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_gradeUpdateInfoToJson(_$_gradeUpdateInfo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'notes': instance.notes,
      'content': instance.content,
      'showFlag': instance.showFlag,
      'gradeListSummaryInfo': instance.gradeListSummaryInfo,
      'nextGrade': instance.nextGrade,
    };

_$_gradeSelectGradeList _$$_gradeSelectGradeListFromJson(
        Map<String, dynamic> json) =>
    _$_gradeSelectGradeList(
      tip: json['tip'] as String?,
      showFlag: json['showFlag'] as int?,
      gradeDictList: (json['gradeDictList'] as List<dynamic>?)
          ?.map((e) =>
              gradeSelectGradeListItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_gradeSelectGradeListToJson(
        _$_gradeSelectGradeList instance) =>
    <String, dynamic>{
      'tip': instance.tip,
      'showFlag': instance.showFlag,
      'gradeDictList': instance.gradeDictList,
    };

_$_gradeSelectGradeListItem _$$_gradeSelectGradeListItemFromJson(
        Map<String, dynamic> json) =>
    _$_gradeSelectGradeListItem(
      name: json['name'] as String?,
      code: json['code'] as int?,
      subDict: (json['subDict'] as List<dynamic>?)
          ?.map((e) =>
              gradeSelectGradeListItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_gradeSelectGradeListItemToJson(
        _$_gradeSelectGradeListItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      'subDict': instance.subDict,
    };

_$_cmsAdsInfo _$$_cmsAdsInfoFromJson(Map<String, dynamic> json) =>
    _$_cmsAdsInfo(
      businessType: json['businessType'] as int?,
      linkType: json['linkType'] as int?,
      linkUrl: json['linkUrl'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      popupId: json['popupId'] as int?,
      popupName: json['popupName'] as String?,
      configType: json['configType'] as int?,
      contentJson: json['contentJson'] as String?,
      contentDict: json['contentDict'] as String?,
      systemActivateType: json['systemActivateType'] as String?,
      popUpIsShow: json['popUpIsShow'] as bool?,
      controllerPosition: json['controllerPosition'] as int?,
    );

Map<String, dynamic> _$$_cmsAdsInfoToJson(_$_cmsAdsInfo instance) =>
    <String, dynamic>{
      'businessType': instance.businessType,
      'linkType': instance.linkType,
      'linkUrl': instance.linkUrl,
      'pictureUrl': instance.pictureUrl,
      'popupId': instance.popupId,
      'popupName': instance.popupName,
      'configType': instance.configType,
      'contentJson': instance.contentJson,
      'contentDict': instance.contentDict,
      'systemActivateType': instance.systemActivateType,
      'popUpIsShow': instance.popUpIsShow,
      'controllerPosition': instance.controllerPosition,
    };

_$_courseOrderListPopInfo _$$_courseOrderListPopInfoFromJson(
        Map<String, dynamic> json) =>
    _$_courseOrderListPopInfo(
      showPopup: json['showPopup'] as bool?,
      toSettingUrl: json['toSettingUrl'] as String?,
      subjectInfoList: (json['subjectInfoList'] as List<dynamic>?)
          ?.map((e) =>
              courseOrderListItemPopInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_courseOrderListPopInfoToJson(
        _$_courseOrderListPopInfo instance) =>
    <String, dynamic>{
      'showPopup': instance.showPopup,
      'toSettingUrl': instance.toSettingUrl,
      'subjectInfoList': instance.subjectInfoList,
    };

_$_courseOrderListItemPopInfo _$$_courseOrderListItemPopInfoFromJson(
        Map<String, dynamic> json) =>
    _$_courseOrderListItemPopInfo(
      fontColor: json['fontColor'] as String?,
      selected: json['selected'] as bool?,
      subjectName: json['subjectName'] as String?,
    );

Map<String, dynamic> _$$_courseOrderListItemPopInfoToJson(
        _$_courseOrderListItemPopInfo instance) =>
    <String, dynamic>{
      'fontColor': instance.fontColor,
      'selected': instance.selected,
      'subjectName': instance.subjectName,
    };

_$_BirthdayBlessingPosterType _$$_BirthdayBlessingPosterTypeFromJson(
        Map<String, dynamic> json) =>
    _$_BirthdayBlessingPosterType(
      nickname: json['nickname'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      age: json['age'] as int?,
      blessingContent: json['blessingContent'] as String?,
      getCourseUrl: json['getCourseUrl'] as String?,
      isShow: json['isShow'] as int?,
    );

Map<String, dynamic> _$$_BirthdayBlessingPosterTypeToJson(
        _$_BirthdayBlessingPosterType instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'avatarUrl': instance.avatarUrl,
      'age': instance.age,
      'blessingContent': instance.blessingContent,
      'getCourseUrl': instance.getCourseUrl,
      'isShow': instance.isShow,
    };
