// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_page_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonPageData _$LessonPageDataFromJson(Map<String, dynamic> json) {
  return _LessonPageData.fromJson(json);
}

/// @nodoc
mixin _$LessonPageData {
  List<lessonTitleBar>? get subjectTabList =>
      throw _privateConstructorUsedError; // 课程标题tab列表
  List<lessonStatusData>? get courseStatusTabList =>
      throw _privateConstructorUsedError; // 课程状态列表
  List<cardModelData>? get courseCardList =>
      throw _privateConstructorUsedError; // 课程卡片列表
  extendData? get extendBar =>
      throw _privateConstructorUsedError; // 扩展栏： 用于存储部分页面部分小的功能数据，避免外层数据结构凌乱
  Map<dynamic, dynamic>? get subjectCourseStatusMap =>
      throw _privateConstructorUsedError; // 各科下各种状态分类及显示文案映射关系
  List<lessonApplyCardInfo>? get applyCourseCardInfoList =>
      throw _privateConstructorUsedError; //缺省领课卡片信息
  todayPlanInfoData? get todayPlanData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonPageDataCopyWith<LessonPageData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonPageDataCopyWith<$Res> {
  factory $LessonPageDataCopyWith(
          LessonPageData value, $Res Function(LessonPageData) then) =
      _$LessonPageDataCopyWithImpl<$Res, LessonPageData>;
  @useResult
  $Res call(
      {List<lessonTitleBar>? subjectTabList,
      List<lessonStatusData>? courseStatusTabList,
      List<cardModelData>? courseCardList,
      extendData? extendBar,
      Map<dynamic, dynamic>? subjectCourseStatusMap,
      List<lessonApplyCardInfo>? applyCourseCardInfoList,
      todayPlanInfoData? todayPlanData});

  $extendDataCopyWith<$Res>? get extendBar;
  $todayPlanInfoDataCopyWith<$Res>? get todayPlanData;
}

/// @nodoc
class _$LessonPageDataCopyWithImpl<$Res, $Val extends LessonPageData>
    implements $LessonPageDataCopyWith<$Res> {
  _$LessonPageDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? courseStatusTabList = freezed,
    Object? courseCardList = freezed,
    Object? extendBar = freezed,
    Object? subjectCourseStatusMap = freezed,
    Object? applyCourseCardInfoList = freezed,
    Object? todayPlanData = freezed,
  }) {
    return _then(_value.copyWith(
      subjectTabList: freezed == subjectTabList
          ? _value.subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<lessonTitleBar>?,
      courseStatusTabList: freezed == courseStatusTabList
          ? _value.courseStatusTabList
          : courseStatusTabList // ignore: cast_nullable_to_non_nullable
              as List<lessonStatusData>?,
      courseCardList: freezed == courseCardList
          ? _value.courseCardList
          : courseCardList // ignore: cast_nullable_to_non_nullable
              as List<cardModelData>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as extendData?,
      subjectCourseStatusMap: freezed == subjectCourseStatusMap
          ? _value.subjectCourseStatusMap
          : subjectCourseStatusMap // ignore: cast_nullable_to_non_nullable
              as Map<dynamic, dynamic>?,
      applyCourseCardInfoList: freezed == applyCourseCardInfoList
          ? _value.applyCourseCardInfoList
          : applyCourseCardInfoList // ignore: cast_nullable_to_non_nullable
              as List<lessonApplyCardInfo>?,
      todayPlanData: freezed == todayPlanData
          ? _value.todayPlanData
          : todayPlanData // ignore: cast_nullable_to_non_nullable
              as todayPlanInfoData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $extendDataCopyWith<$Res>? get extendBar {
    if (_value.extendBar == null) {
      return null;
    }

    return $extendDataCopyWith<$Res>(_value.extendBar!, (value) {
      return _then(_value.copyWith(extendBar: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanInfoDataCopyWith<$Res>? get todayPlanData {
    if (_value.todayPlanData == null) {
      return null;
    }

    return $todayPlanInfoDataCopyWith<$Res>(_value.todayPlanData!, (value) {
      return _then(_value.copyWith(todayPlanData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LessonPageDataCopyWith<$Res>
    implements $LessonPageDataCopyWith<$Res> {
  factory _$$_LessonPageDataCopyWith(
          _$_LessonPageData value, $Res Function(_$_LessonPageData) then) =
      __$$_LessonPageDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<lessonTitleBar>? subjectTabList,
      List<lessonStatusData>? courseStatusTabList,
      List<cardModelData>? courseCardList,
      extendData? extendBar,
      Map<dynamic, dynamic>? subjectCourseStatusMap,
      List<lessonApplyCardInfo>? applyCourseCardInfoList,
      todayPlanInfoData? todayPlanData});

  @override
  $extendDataCopyWith<$Res>? get extendBar;
  @override
  $todayPlanInfoDataCopyWith<$Res>? get todayPlanData;
}

/// @nodoc
class __$$_LessonPageDataCopyWithImpl<$Res>
    extends _$LessonPageDataCopyWithImpl<$Res, _$_LessonPageData>
    implements _$$_LessonPageDataCopyWith<$Res> {
  __$$_LessonPageDataCopyWithImpl(
      _$_LessonPageData _value, $Res Function(_$_LessonPageData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? courseStatusTabList = freezed,
    Object? courseCardList = freezed,
    Object? extendBar = freezed,
    Object? subjectCourseStatusMap = freezed,
    Object? applyCourseCardInfoList = freezed,
    Object? todayPlanData = freezed,
  }) {
    return _then(_$_LessonPageData(
      subjectTabList: freezed == subjectTabList
          ? _value._subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<lessonTitleBar>?,
      courseStatusTabList: freezed == courseStatusTabList
          ? _value._courseStatusTabList
          : courseStatusTabList // ignore: cast_nullable_to_non_nullable
              as List<lessonStatusData>?,
      courseCardList: freezed == courseCardList
          ? _value._courseCardList
          : courseCardList // ignore: cast_nullable_to_non_nullable
              as List<cardModelData>?,
      extendBar: freezed == extendBar
          ? _value.extendBar
          : extendBar // ignore: cast_nullable_to_non_nullable
              as extendData?,
      subjectCourseStatusMap: freezed == subjectCourseStatusMap
          ? _value._subjectCourseStatusMap
          : subjectCourseStatusMap // ignore: cast_nullable_to_non_nullable
              as Map<dynamic, dynamic>?,
      applyCourseCardInfoList: freezed == applyCourseCardInfoList
          ? _value._applyCourseCardInfoList
          : applyCourseCardInfoList // ignore: cast_nullable_to_non_nullable
              as List<lessonApplyCardInfo>?,
      todayPlanData: freezed == todayPlanData
          ? _value.todayPlanData
          : todayPlanData // ignore: cast_nullable_to_non_nullable
              as todayPlanInfoData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonPageData implements _LessonPageData {
  const _$_LessonPageData(
      {final List<lessonTitleBar>? subjectTabList,
      final List<lessonStatusData>? courseStatusTabList,
      final List<cardModelData>? courseCardList,
      this.extendBar,
      final Map<dynamic, dynamic>? subjectCourseStatusMap,
      final List<lessonApplyCardInfo>? applyCourseCardInfoList,
      this.todayPlanData})
      : _subjectTabList = subjectTabList,
        _courseStatusTabList = courseStatusTabList,
        _courseCardList = courseCardList,
        _subjectCourseStatusMap = subjectCourseStatusMap,
        _applyCourseCardInfoList = applyCourseCardInfoList;

  factory _$_LessonPageData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonPageDataFromJson(json);

  final List<lessonTitleBar>? _subjectTabList;
  @override
  List<lessonTitleBar>? get subjectTabList {
    final value = _subjectTabList;
    if (value == null) return null;
    if (_subjectTabList is EqualUnmodifiableListView) return _subjectTabList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 课程标题tab列表
  final List<lessonStatusData>? _courseStatusTabList;
// 课程标题tab列表
  @override
  List<lessonStatusData>? get courseStatusTabList {
    final value = _courseStatusTabList;
    if (value == null) return null;
    if (_courseStatusTabList is EqualUnmodifiableListView)
      return _courseStatusTabList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 课程状态列表
  final List<cardModelData>? _courseCardList;
// 课程状态列表
  @override
  List<cardModelData>? get courseCardList {
    final value = _courseCardList;
    if (value == null) return null;
    if (_courseCardList is EqualUnmodifiableListView) return _courseCardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 课程卡片列表
  @override
  final extendData? extendBar;
// 扩展栏： 用于存储部分页面部分小的功能数据，避免外层数据结构凌乱
  final Map<dynamic, dynamic>? _subjectCourseStatusMap;
// 扩展栏： 用于存储部分页面部分小的功能数据，避免外层数据结构凌乱
  @override
  Map<dynamic, dynamic>? get subjectCourseStatusMap {
    final value = _subjectCourseStatusMap;
    if (value == null) return null;
    if (_subjectCourseStatusMap is EqualUnmodifiableMapView)
      return _subjectCourseStatusMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

// 各科下各种状态分类及显示文案映射关系
  final List<lessonApplyCardInfo>? _applyCourseCardInfoList;
// 各科下各种状态分类及显示文案映射关系
  @override
  List<lessonApplyCardInfo>? get applyCourseCardInfoList {
    final value = _applyCourseCardInfoList;
    if (value == null) return null;
    if (_applyCourseCardInfoList is EqualUnmodifiableListView)
      return _applyCourseCardInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//缺省领课卡片信息
  @override
  final todayPlanInfoData? todayPlanData;

  @override
  String toString() {
    return 'LessonPageData(subjectTabList: $subjectTabList, courseStatusTabList: $courseStatusTabList, courseCardList: $courseCardList, extendBar: $extendBar, subjectCourseStatusMap: $subjectCourseStatusMap, applyCourseCardInfoList: $applyCourseCardInfoList, todayPlanData: $todayPlanData)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonPageData &&
            const DeepCollectionEquality()
                .equals(other._subjectTabList, _subjectTabList) &&
            const DeepCollectionEquality()
                .equals(other._courseStatusTabList, _courseStatusTabList) &&
            const DeepCollectionEquality()
                .equals(other._courseCardList, _courseCardList) &&
            (identical(other.extendBar, extendBar) ||
                other.extendBar == extendBar) &&
            const DeepCollectionEquality().equals(
                other._subjectCourseStatusMap, _subjectCourseStatusMap) &&
            const DeepCollectionEquality().equals(
                other._applyCourseCardInfoList, _applyCourseCardInfoList) &&
            (identical(other.todayPlanData, todayPlanData) ||
                other.todayPlanData == todayPlanData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectTabList),
      const DeepCollectionEquality().hash(_courseStatusTabList),
      const DeepCollectionEquality().hash(_courseCardList),
      extendBar,
      const DeepCollectionEquality().hash(_subjectCourseStatusMap),
      const DeepCollectionEquality().hash(_applyCourseCardInfoList),
      todayPlanData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonPageDataCopyWith<_$_LessonPageData> get copyWith =>
      __$$_LessonPageDataCopyWithImpl<_$_LessonPageData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonPageDataToJson(
      this,
    );
  }
}

abstract class _LessonPageData implements LessonPageData {
  const factory _LessonPageData(
      {final List<lessonTitleBar>? subjectTabList,
      final List<lessonStatusData>? courseStatusTabList,
      final List<cardModelData>? courseCardList,
      final extendData? extendBar,
      final Map<dynamic, dynamic>? subjectCourseStatusMap,
      final List<lessonApplyCardInfo>? applyCourseCardInfoList,
      final todayPlanInfoData? todayPlanData}) = _$_LessonPageData;

  factory _LessonPageData.fromJson(Map<String, dynamic> json) =
      _$_LessonPageData.fromJson;

  @override
  List<lessonTitleBar>? get subjectTabList;
  @override // 课程标题tab列表
  List<lessonStatusData>? get courseStatusTabList;
  @override // 课程状态列表
  List<cardModelData>? get courseCardList;
  @override // 课程卡片列表
  extendData? get extendBar;
  @override // 扩展栏： 用于存储部分页面部分小的功能数据，避免外层数据结构凌乱
  Map<dynamic, dynamic>? get subjectCourseStatusMap;
  @override // 各科下各种状态分类及显示文案映射关系
  List<lessonApplyCardInfo>? get applyCourseCardInfoList;
  @override //缺省领课卡片信息
  todayPlanInfoData? get todayPlanData;
  @override
  @JsonKey(ignore: true)
  _$$_LessonPageDataCopyWith<_$_LessonPageData> get copyWith =>
      throw _privateConstructorUsedError;
}

lessonTitleBar _$lessonTitleBarFromJson(Map<String, dynamic> json) {
  return _lessonTitleBar.fromJson(json);
}

/// @nodoc
mixin _$lessonTitleBar {
  int? get type =>
      throw _privateConstructorUsedError; // 科目类型：益智-1；阅读-2；创作（表达）-3；美育-4；识字-5; 英语-6；专题-7
  String? get typeDesc => throw _privateConstructorUsedError; // 	科目类型描述
  String? get icon => throw _privateConstructorUsedError; // 	科目图标
  String? get selectedIcon => throw _privateConstructorUsedError; // 	科目选中图标
  String? get selectedBgColor =>
      throw _privateConstructorUsedError; // 	科目选中背景颜色
  String? get selectedTextColor =>
      throw _privateConstructorUsedError; //  科目选中文字颜色
  bool? get isPosition => throw _privateConstructorUsedError; // 	是否定位当前科目
// String? encourageIcon, // 	激励元素图标
  bool? get activateIconShow => throw _privateConstructorUsedError; // 	是否显示激活元素
  int? get needClassificationInSubject =>
      throw _privateConstructorUsedError; //当前科目下是否需要按课程状态分类， 0:否，1: 是
  List<cardSectionData>? get cardDataList =>
      throw _privateConstructorUsedError; // 课程卡片数据
  List<cardSectionData>? get videoCardDataList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $lessonTitleBarCopyWith<lessonTitleBar> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $lessonTitleBarCopyWith<$Res> {
  factory $lessonTitleBarCopyWith(
          lessonTitleBar value, $Res Function(lessonTitleBar) then) =
      _$lessonTitleBarCopyWithImpl<$Res, lessonTitleBar>;
  @useResult
  $Res call(
      {int? type,
      String? typeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedBgColor,
      String? selectedTextColor,
      bool? isPosition,
      bool? activateIconShow,
      int? needClassificationInSubject,
      List<cardSectionData>? cardDataList,
      List<cardSectionData>? videoCardDataList});
}

/// @nodoc
class _$lessonTitleBarCopyWithImpl<$Res, $Val extends lessonTitleBar>
    implements $lessonTitleBarCopyWith<$Res> {
  _$lessonTitleBarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedBgColor = freezed,
    Object? selectedTextColor = freezed,
    Object? isPosition = freezed,
    Object? activateIconShow = freezed,
    Object? needClassificationInSubject = freezed,
    Object? cardDataList = freezed,
    Object? videoCardDataList = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      activateIconShow: freezed == activateIconShow
          ? _value.activateIconShow
          : activateIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      needClassificationInSubject: freezed == needClassificationInSubject
          ? _value.needClassificationInSubject
          : needClassificationInSubject // ignore: cast_nullable_to_non_nullable
              as int?,
      cardDataList: freezed == cardDataList
          ? _value.cardDataList
          : cardDataList // ignore: cast_nullable_to_non_nullable
              as List<cardSectionData>?,
      videoCardDataList: freezed == videoCardDataList
          ? _value.videoCardDataList
          : videoCardDataList // ignore: cast_nullable_to_non_nullable
              as List<cardSectionData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_lessonTitleBarCopyWith<$Res>
    implements $lessonTitleBarCopyWith<$Res> {
  factory _$$_lessonTitleBarCopyWith(
          _$_lessonTitleBar value, $Res Function(_$_lessonTitleBar) then) =
      __$$_lessonTitleBarCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      String? typeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedBgColor,
      String? selectedTextColor,
      bool? isPosition,
      bool? activateIconShow,
      int? needClassificationInSubject,
      List<cardSectionData>? cardDataList,
      List<cardSectionData>? videoCardDataList});
}

/// @nodoc
class __$$_lessonTitleBarCopyWithImpl<$Res>
    extends _$lessonTitleBarCopyWithImpl<$Res, _$_lessonTitleBar>
    implements _$$_lessonTitleBarCopyWith<$Res> {
  __$$_lessonTitleBarCopyWithImpl(
      _$_lessonTitleBar _value, $Res Function(_$_lessonTitleBar) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedBgColor = freezed,
    Object? selectedTextColor = freezed,
    Object? isPosition = freezed,
    Object? activateIconShow = freezed,
    Object? needClassificationInSubject = freezed,
    Object? cardDataList = freezed,
    Object? videoCardDataList = freezed,
  }) {
    return _then(_$_lessonTitleBar(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      activateIconShow: freezed == activateIconShow
          ? _value.activateIconShow
          : activateIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      needClassificationInSubject: freezed == needClassificationInSubject
          ? _value.needClassificationInSubject
          : needClassificationInSubject // ignore: cast_nullable_to_non_nullable
              as int?,
      cardDataList: freezed == cardDataList
          ? _value._cardDataList
          : cardDataList // ignore: cast_nullable_to_non_nullable
              as List<cardSectionData>?,
      videoCardDataList: freezed == videoCardDataList
          ? _value._videoCardDataList
          : videoCardDataList // ignore: cast_nullable_to_non_nullable
              as List<cardSectionData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_lessonTitleBar implements _lessonTitleBar {
  const _$_lessonTitleBar(
      {this.type,
      this.typeDesc,
      this.icon,
      this.selectedIcon,
      this.selectedBgColor,
      this.selectedTextColor,
      this.isPosition,
      this.activateIconShow,
      this.needClassificationInSubject,
      final List<cardSectionData>? cardDataList,
      final List<cardSectionData>? videoCardDataList})
      : _cardDataList = cardDataList,
        _videoCardDataList = videoCardDataList;

  factory _$_lessonTitleBar.fromJson(Map<String, dynamic> json) =>
      _$$_lessonTitleBarFromJson(json);

  @override
  final int? type;
// 科目类型：益智-1；阅读-2；创作（表达）-3；美育-4；识字-5; 英语-6；专题-7
  @override
  final String? typeDesc;
// 	科目类型描述
  @override
  final String? icon;
// 	科目图标
  @override
  final String? selectedIcon;
// 	科目选中图标
  @override
  final String? selectedBgColor;
// 	科目选中背景颜色
  @override
  final String? selectedTextColor;
//  科目选中文字颜色
  @override
  final bool? isPosition;
// 	是否定位当前科目
// String? encourageIcon, // 	激励元素图标
  @override
  final bool? activateIconShow;
// 	是否显示激活元素
  @override
  final int? needClassificationInSubject;
//当前科目下是否需要按课程状态分类， 0:否，1: 是
  final List<cardSectionData>? _cardDataList;
//当前科目下是否需要按课程状态分类， 0:否，1: 是
  @override
  List<cardSectionData>? get cardDataList {
    final value = _cardDataList;
    if (value == null) return null;
    if (_cardDataList is EqualUnmodifiableListView) return _cardDataList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 课程卡片数据
  final List<cardSectionData>? _videoCardDataList;
// 课程卡片数据
  @override
  List<cardSectionData>? get videoCardDataList {
    final value = _videoCardDataList;
    if (value == null) return null;
    if (_videoCardDataList is EqualUnmodifiableListView)
      return _videoCardDataList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'lessonTitleBar(type: $type, typeDesc: $typeDesc, icon: $icon, selectedIcon: $selectedIcon, selectedBgColor: $selectedBgColor, selectedTextColor: $selectedTextColor, isPosition: $isPosition, activateIconShow: $activateIconShow, needClassificationInSubject: $needClassificationInSubject, cardDataList: $cardDataList, videoCardDataList: $videoCardDataList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_lessonTitleBar &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.typeDesc, typeDesc) ||
                other.typeDesc == typeDesc) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.selectedIcon, selectedIcon) ||
                other.selectedIcon == selectedIcon) &&
            (identical(other.selectedBgColor, selectedBgColor) ||
                other.selectedBgColor == selectedBgColor) &&
            (identical(other.selectedTextColor, selectedTextColor) ||
                other.selectedTextColor == selectedTextColor) &&
            (identical(other.isPosition, isPosition) ||
                other.isPosition == isPosition) &&
            (identical(other.activateIconShow, activateIconShow) ||
                other.activateIconShow == activateIconShow) &&
            (identical(other.needClassificationInSubject,
                    needClassificationInSubject) ||
                other.needClassificationInSubject ==
                    needClassificationInSubject) &&
            const DeepCollectionEquality()
                .equals(other._cardDataList, _cardDataList) &&
            const DeepCollectionEquality()
                .equals(other._videoCardDataList, _videoCardDataList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      typeDesc,
      icon,
      selectedIcon,
      selectedBgColor,
      selectedTextColor,
      isPosition,
      activateIconShow,
      needClassificationInSubject,
      const DeepCollectionEquality().hash(_cardDataList),
      const DeepCollectionEquality().hash(_videoCardDataList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_lessonTitleBarCopyWith<_$_lessonTitleBar> get copyWith =>
      __$$_lessonTitleBarCopyWithImpl<_$_lessonTitleBar>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_lessonTitleBarToJson(
      this,
    );
  }
}

abstract class _lessonTitleBar implements lessonTitleBar {
  const factory _lessonTitleBar(
      {final int? type,
      final String? typeDesc,
      final String? icon,
      final String? selectedIcon,
      final String? selectedBgColor,
      final String? selectedTextColor,
      final bool? isPosition,
      final bool? activateIconShow,
      final int? needClassificationInSubject,
      final List<cardSectionData>? cardDataList,
      final List<cardSectionData>? videoCardDataList}) = _$_lessonTitleBar;

  factory _lessonTitleBar.fromJson(Map<String, dynamic> json) =
      _$_lessonTitleBar.fromJson;

  @override
  int? get type;
  @override // 科目类型：益智-1；阅读-2；创作（表达）-3；美育-4；识字-5; 英语-6；专题-7
  String? get typeDesc;
  @override // 	科目类型描述
  String? get icon;
  @override // 	科目图标
  String? get selectedIcon;
  @override // 	科目选中图标
  String? get selectedBgColor;
  @override // 	科目选中背景颜色
  String? get selectedTextColor;
  @override //  科目选中文字颜色
  bool? get isPosition;
  @override // 	是否定位当前科目
// String? encourageIcon, // 	激励元素图标
  bool? get activateIconShow;
  @override // 	是否显示激活元素
  int? get needClassificationInSubject;
  @override //当前科目下是否需要按课程状态分类， 0:否，1: 是
  List<cardSectionData>? get cardDataList;
  @override // 课程卡片数据
  List<cardSectionData>? get videoCardDataList;
  @override
  @JsonKey(ignore: true)
  _$$_lessonTitleBarCopyWith<_$_lessonTitleBar> get copyWith =>
      throw _privateConstructorUsedError;
}

cardSectionData _$cardSectionDataFromJson(Map<String, dynamic> json) {
  return _cardSectionData.fromJson(json);
}

/// @nodoc
mixin _$cardSectionData {
  headerData? get headerModel => throw _privateConstructorUsedError;
  List<cardModelData>? get lessonCardList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $cardSectionDataCopyWith<cardSectionData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $cardSectionDataCopyWith<$Res> {
  factory $cardSectionDataCopyWith(
          cardSectionData value, $Res Function(cardSectionData) then) =
      _$cardSectionDataCopyWithImpl<$Res, cardSectionData>;
  @useResult
  $Res call({headerData? headerModel, List<cardModelData>? lessonCardList});

  $headerDataCopyWith<$Res>? get headerModel;
}

/// @nodoc
class _$cardSectionDataCopyWithImpl<$Res, $Val extends cardSectionData>
    implements $cardSectionDataCopyWith<$Res> {
  _$cardSectionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? headerModel = freezed,
    Object? lessonCardList = freezed,
  }) {
    return _then(_value.copyWith(
      headerModel: freezed == headerModel
          ? _value.headerModel
          : headerModel // ignore: cast_nullable_to_non_nullable
              as headerData?,
      lessonCardList: freezed == lessonCardList
          ? _value.lessonCardList
          : lessonCardList // ignore: cast_nullable_to_non_nullable
              as List<cardModelData>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $headerDataCopyWith<$Res>? get headerModel {
    if (_value.headerModel == null) {
      return null;
    }

    return $headerDataCopyWith<$Res>(_value.headerModel!, (value) {
      return _then(_value.copyWith(headerModel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_cardSectionDataCopyWith<$Res>
    implements $cardSectionDataCopyWith<$Res> {
  factory _$$_cardSectionDataCopyWith(
          _$_cardSectionData value, $Res Function(_$_cardSectionData) then) =
      __$$_cardSectionDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({headerData? headerModel, List<cardModelData>? lessonCardList});

  @override
  $headerDataCopyWith<$Res>? get headerModel;
}

/// @nodoc
class __$$_cardSectionDataCopyWithImpl<$Res>
    extends _$cardSectionDataCopyWithImpl<$Res, _$_cardSectionData>
    implements _$$_cardSectionDataCopyWith<$Res> {
  __$$_cardSectionDataCopyWithImpl(
      _$_cardSectionData _value, $Res Function(_$_cardSectionData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? headerModel = freezed,
    Object? lessonCardList = freezed,
  }) {
    return _then(_$_cardSectionData(
      headerModel: freezed == headerModel
          ? _value.headerModel
          : headerModel // ignore: cast_nullable_to_non_nullable
              as headerData?,
      lessonCardList: freezed == lessonCardList
          ? _value._lessonCardList
          : lessonCardList // ignore: cast_nullable_to_non_nullable
              as List<cardModelData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_cardSectionData implements _cardSectionData {
  const _$_cardSectionData(
      {this.headerModel, final List<cardModelData>? lessonCardList})
      : _lessonCardList = lessonCardList;

  factory _$_cardSectionData.fromJson(Map<String, dynamic> json) =>
      _$$_cardSectionDataFromJson(json);

  @override
  final headerData? headerModel;
  final List<cardModelData>? _lessonCardList;
  @override
  List<cardModelData>? get lessonCardList {
    final value = _lessonCardList;
    if (value == null) return null;
    if (_lessonCardList is EqualUnmodifiableListView) return _lessonCardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'cardSectionData(headerModel: $headerModel, lessonCardList: $lessonCardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_cardSectionData &&
            (identical(other.headerModel, headerModel) ||
                other.headerModel == headerModel) &&
            const DeepCollectionEquality()
                .equals(other._lessonCardList, _lessonCardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, headerModel,
      const DeepCollectionEquality().hash(_lessonCardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_cardSectionDataCopyWith<_$_cardSectionData> get copyWith =>
      __$$_cardSectionDataCopyWithImpl<_$_cardSectionData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_cardSectionDataToJson(
      this,
    );
  }
}

abstract class _cardSectionData implements cardSectionData {
  const factory _cardSectionData(
      {final headerData? headerModel,
      final List<cardModelData>? lessonCardList}) = _$_cardSectionData;

  factory _cardSectionData.fromJson(Map<String, dynamic> json) =
      _$_cardSectionData.fromJson;

  @override
  headerData? get headerModel;
  @override
  List<cardModelData>? get lessonCardList;
  @override
  @JsonKey(ignore: true)
  _$$_cardSectionDataCopyWith<_$_cardSectionData> get copyWith =>
      throw _privateConstructorUsedError;
}

headerData _$headerDataFromJson(Map<String, dynamic> json) {
  return _headerData.fromJson(json);
}

/// @nodoc
mixin _$headerData {
  int? get courseStatus =>
      throw _privateConstructorUsedError; //课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4; 未完成-5
  bool? get isUnfold => throw _privateConstructorUsedError; // 	是否展开
  bool? get isUnfoldForward => throw _privateConstructorUsedError; // 	上一次的展开状态
  bool? get isHidden => throw _privateConstructorUsedError; // 	是否显示
  String? get title => throw _privateConstructorUsedError;
  String? get tipText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $headerDataCopyWith<headerData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $headerDataCopyWith<$Res> {
  factory $headerDataCopyWith(
          headerData value, $Res Function(headerData) then) =
      _$headerDataCopyWithImpl<$Res, headerData>;
  @useResult
  $Res call(
      {int? courseStatus,
      bool? isUnfold,
      bool? isUnfoldForward,
      bool? isHidden,
      String? title,
      String? tipText});
}

/// @nodoc
class _$headerDataCopyWithImpl<$Res, $Val extends headerData>
    implements $headerDataCopyWith<$Res> {
  _$headerDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseStatus = freezed,
    Object? isUnfold = freezed,
    Object? isUnfoldForward = freezed,
    Object? isHidden = freezed,
    Object? title = freezed,
    Object? tipText = freezed,
  }) {
    return _then(_value.copyWith(
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnfold: freezed == isUnfold
          ? _value.isUnfold
          : isUnfold // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUnfoldForward: freezed == isUnfoldForward
          ? _value.isUnfoldForward
          : isUnfoldForward // ignore: cast_nullable_to_non_nullable
              as bool?,
      isHidden: freezed == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_headerDataCopyWith<$Res>
    implements $headerDataCopyWith<$Res> {
  factory _$$_headerDataCopyWith(
          _$_headerData value, $Res Function(_$_headerData) then) =
      __$$_headerDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseStatus,
      bool? isUnfold,
      bool? isUnfoldForward,
      bool? isHidden,
      String? title,
      String? tipText});
}

/// @nodoc
class __$$_headerDataCopyWithImpl<$Res>
    extends _$headerDataCopyWithImpl<$Res, _$_headerData>
    implements _$$_headerDataCopyWith<$Res> {
  __$$_headerDataCopyWithImpl(
      _$_headerData _value, $Res Function(_$_headerData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseStatus = freezed,
    Object? isUnfold = freezed,
    Object? isUnfoldForward = freezed,
    Object? isHidden = freezed,
    Object? title = freezed,
    Object? tipText = freezed,
  }) {
    return _then(_$_headerData(
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnfold: freezed == isUnfold
          ? _value.isUnfold
          : isUnfold // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUnfoldForward: freezed == isUnfoldForward
          ? _value.isUnfoldForward
          : isUnfoldForward // ignore: cast_nullable_to_non_nullable
              as bool?,
      isHidden: freezed == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_headerData implements _headerData {
  const _$_headerData(
      {this.courseStatus,
      this.isUnfold,
      this.isUnfoldForward,
      this.isHidden,
      this.title,
      this.tipText});

  factory _$_headerData.fromJson(Map<String, dynamic> json) =>
      _$$_headerDataFromJson(json);

  @override
  final int? courseStatus;
//课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4; 未完成-5
  @override
  final bool? isUnfold;
// 	是否展开
  @override
  final bool? isUnfoldForward;
// 	上一次的展开状态
  @override
  final bool? isHidden;
// 	是否显示
  @override
  final String? title;
  @override
  final String? tipText;

  @override
  String toString() {
    return 'headerData(courseStatus: $courseStatus, isUnfold: $isUnfold, isUnfoldForward: $isUnfoldForward, isHidden: $isHidden, title: $title, tipText: $tipText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_headerData &&
            (identical(other.courseStatus, courseStatus) ||
                other.courseStatus == courseStatus) &&
            (identical(other.isUnfold, isUnfold) ||
                other.isUnfold == isUnfold) &&
            (identical(other.isUnfoldForward, isUnfoldForward) ||
                other.isUnfoldForward == isUnfoldForward) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.tipText, tipText) || other.tipText == tipText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseStatus, isUnfold,
      isUnfoldForward, isHidden, title, tipText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_headerDataCopyWith<_$_headerData> get copyWith =>
      __$$_headerDataCopyWithImpl<_$_headerData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_headerDataToJson(
      this,
    );
  }
}

abstract class _headerData implements headerData {
  const factory _headerData(
      {final int? courseStatus,
      final bool? isUnfold,
      final bool? isUnfoldForward,
      final bool? isHidden,
      final String? title,
      final String? tipText}) = _$_headerData;

  factory _headerData.fromJson(Map<String, dynamic> json) =
      _$_headerData.fromJson;

  @override
  int? get courseStatus;
  @override //课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4; 未完成-5
  bool? get isUnfold;
  @override // 	是否展开
  bool? get isUnfoldForward;
  @override // 	上一次的展开状态
  bool? get isHidden;
  @override // 	是否显示
  String? get title;
  @override
  String? get tipText;
  @override
  @JsonKey(ignore: true)
  _$$_headerDataCopyWith<_$_headerData> get copyWith =>
      throw _privateConstructorUsedError;
}

lessonStatusData _$lessonStatusDataFromJson(Map<String, dynamic> json) {
  return _lessonStatusData.fromJson(json);
}

/// @nodoc
mixin _$lessonStatusData {
  int? get status =>
      throw _privateConstructorUsedError; //课程状态：全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  String? get statusDesc => throw _privateConstructorUsedError; //课程状态描述
  int? get courseCount => throw _privateConstructorUsedError; //对应状态课程总数
  String? get hasNoCourseTips =>
      throw _privateConstructorUsedError; //无对应状态课程的提示文案
  String? get statusDescTag => throw _privateConstructorUsedError; //状态选中文本
  String? get hasNoCourseText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $lessonStatusDataCopyWith<lessonStatusData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $lessonStatusDataCopyWith<$Res> {
  factory $lessonStatusDataCopyWith(
          lessonStatusData value, $Res Function(lessonStatusData) then) =
      _$lessonStatusDataCopyWithImpl<$Res, lessonStatusData>;
  @useResult
  $Res call(
      {int? status,
      String? statusDesc,
      int? courseCount,
      String? hasNoCourseTips,
      String? statusDescTag,
      String? hasNoCourseText});
}

/// @nodoc
class _$lessonStatusDataCopyWithImpl<$Res, $Val extends lessonStatusData>
    implements $lessonStatusDataCopyWith<$Res> {
  _$lessonStatusDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? statusDesc = freezed,
    Object? courseCount = freezed,
    Object? hasNoCourseTips = freezed,
    Object? statusDescTag = freezed,
    Object? hasNoCourseText = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      statusDesc: freezed == statusDesc
          ? _value.statusDesc
          : statusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCount: freezed == courseCount
          ? _value.courseCount
          : courseCount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNoCourseTips: freezed == hasNoCourseTips
          ? _value.hasNoCourseTips
          : hasNoCourseTips // ignore: cast_nullable_to_non_nullable
              as String?,
      statusDescTag: freezed == statusDescTag
          ? _value.statusDescTag
          : statusDescTag // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNoCourseText: freezed == hasNoCourseText
          ? _value.hasNoCourseText
          : hasNoCourseText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_lessonStatusDataCopyWith<$Res>
    implements $lessonStatusDataCopyWith<$Res> {
  factory _$$_lessonStatusDataCopyWith(
          _$_lessonStatusData value, $Res Function(_$_lessonStatusData) then) =
      __$$_lessonStatusDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? status,
      String? statusDesc,
      int? courseCount,
      String? hasNoCourseTips,
      String? statusDescTag,
      String? hasNoCourseText});
}

/// @nodoc
class __$$_lessonStatusDataCopyWithImpl<$Res>
    extends _$lessonStatusDataCopyWithImpl<$Res, _$_lessonStatusData>
    implements _$$_lessonStatusDataCopyWith<$Res> {
  __$$_lessonStatusDataCopyWithImpl(
      _$_lessonStatusData _value, $Res Function(_$_lessonStatusData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? statusDesc = freezed,
    Object? courseCount = freezed,
    Object? hasNoCourseTips = freezed,
    Object? statusDescTag = freezed,
    Object? hasNoCourseText = freezed,
  }) {
    return _then(_$_lessonStatusData(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      statusDesc: freezed == statusDesc
          ? _value.statusDesc
          : statusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCount: freezed == courseCount
          ? _value.courseCount
          : courseCount // ignore: cast_nullable_to_non_nullable
              as int?,
      hasNoCourseTips: freezed == hasNoCourseTips
          ? _value.hasNoCourseTips
          : hasNoCourseTips // ignore: cast_nullable_to_non_nullable
              as String?,
      statusDescTag: freezed == statusDescTag
          ? _value.statusDescTag
          : statusDescTag // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNoCourseText: freezed == hasNoCourseText
          ? _value.hasNoCourseText
          : hasNoCourseText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_lessonStatusData implements _lessonStatusData {
  const _$_lessonStatusData(
      {this.status,
      this.statusDesc,
      this.courseCount,
      this.hasNoCourseTips,
      this.statusDescTag,
      this.hasNoCourseText});

  factory _$_lessonStatusData.fromJson(Map<String, dynamic> json) =>
      _$$_lessonStatusDataFromJson(json);

  @override
  final int? status;
//课程状态：全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  @override
  final String? statusDesc;
//课程状态描述
  @override
  final int? courseCount;
//对应状态课程总数
  @override
  final String? hasNoCourseTips;
//无对应状态课程的提示文案
  @override
  final String? statusDescTag;
//状态选中文本
  @override
  final String? hasNoCourseText;

  @override
  String toString() {
    return 'lessonStatusData(status: $status, statusDesc: $statusDesc, courseCount: $courseCount, hasNoCourseTips: $hasNoCourseTips, statusDescTag: $statusDescTag, hasNoCourseText: $hasNoCourseText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_lessonStatusData &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.statusDesc, statusDesc) ||
                other.statusDesc == statusDesc) &&
            (identical(other.courseCount, courseCount) ||
                other.courseCount == courseCount) &&
            (identical(other.hasNoCourseTips, hasNoCourseTips) ||
                other.hasNoCourseTips == hasNoCourseTips) &&
            (identical(other.statusDescTag, statusDescTag) ||
                other.statusDescTag == statusDescTag) &&
            (identical(other.hasNoCourseText, hasNoCourseText) ||
                other.hasNoCourseText == hasNoCourseText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, status, statusDesc, courseCount,
      hasNoCourseTips, statusDescTag, hasNoCourseText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_lessonStatusDataCopyWith<_$_lessonStatusData> get copyWith =>
      __$$_lessonStatusDataCopyWithImpl<_$_lessonStatusData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_lessonStatusDataToJson(
      this,
    );
  }
}

abstract class _lessonStatusData implements lessonStatusData {
  const factory _lessonStatusData(
      {final int? status,
      final String? statusDesc,
      final int? courseCount,
      final String? hasNoCourseTips,
      final String? statusDescTag,
      final String? hasNoCourseText}) = _$_lessonStatusData;

  factory _lessonStatusData.fromJson(Map<String, dynamic> json) =
      _$_lessonStatusData.fromJson;

  @override
  int? get status;
  @override //课程状态：全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  String? get statusDesc;
  @override //课程状态描述
  int? get courseCount;
  @override //对应状态课程总数
  String? get hasNoCourseTips;
  @override //无对应状态课程的提示文案
  String? get statusDescTag;
  @override //状态选中文本
  String? get hasNoCourseText;
  @override
  @JsonKey(ignore: true)
  _$$_lessonStatusDataCopyWith<_$_lessonStatusData> get copyWith =>
      throw _privateConstructorUsedError;
}

cardModelData _$cardModelDataFromJson(Map<String, dynamic> json) {
  return _cardModelData.fromJson(json);
}

/// @nodoc
mixin _$cardModelData {
  String? get cardLabel => throw _privateConstructorUsedError; //课程标题：卡片标签
  int? get subjectType => throw _privateConstructorUsedError; //课程类型
  String? get subjectTypeDesc => throw _privateConstructorUsedError; //课程标题：科目
  String? get segmentName => throw _privateConstructorUsedError; //课程标题：阶段
  int? get courseType =>
      throw _privateConstructorUsedError; //课程标题：类型1,体验包（训练营）2,主题包4 ,试用包3,  系统包（年课）
  String? get courseTypeIcon =>
      throw _privateConstructorUsedError; //课程标题：类型对应卡片
  String? get courseLabelUrl => throw _privateConstructorUsedError; //课程标签跳转url
  int? get courseId => throw _privateConstructorUsedError; //课程ID
  String? get courseName => throw _privateConstructorUsedError; //课程名称
  int? get courseStatus =>
      throw _privateConstructorUsedError; //课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  String? get courseKey => throw _privateConstructorUsedError; //课程key
  int? get teacherId => throw _privateConstructorUsedError; //老师ID
  String? get teacherProfileUrl => throw _privateConstructorUsedError; //对应老师头像
  int? get classId => throw _privateConstructorUsedError; //班级id
  String? get classKey => throw _privateConstructorUsedError; //班期key
  String? get bgColor =>
      throw _privateConstructorUsedError; //背景色-Apollo配置：根据科目区分
  String? get labelFontColor =>
      throw _privateConstructorUsedError; //文字色-Apollo配置：根据科目区分
  String? get cardMainColor =>
      throw _privateConstructorUsedError; //主色-Apollo配置：根据科目区分
  String? get topLeftLabelFontColor =>
      throw _privateConstructorUsedError; //标题色-Apollo配置：根据科目区分
  String? get courseFinishedIcon =>
      throw _privateConstructorUsedError; //课程已完成图标
  String? get startNotify => throw _privateConstructorUsedError; //开课提醒
  String? get rollingCourseNextStartNotify =>
      throw _privateConstructorUsedError; //提示信息
  bool? get isAddTeacher => throw _privateConstructorUsedError; //是否已加老师
  String? get addTeacherUrl => throw _privateConstructorUsedError; //加老师跳转url
  String? get addTeacherText => throw _privateConstructorUsedError; //加老师提示文案
  String? get addTeacherEntranceTip =>
      throw _privateConstructorUsedError; //加老师入口提示文案
  String? get courseStatusDesc =>
      throw _privateConstructorUsedError; //卡片状态中文 埋点用
  String? get cardStyle => throw _privateConstructorUsedError; //卡片样式，v2:课包2卡片
  int? get enumCardStyle =>
      throw _privateConstructorUsedError; //卡片样式 0课包1 默认,1课包2,2领课栏目,3空卡片,4视频领课卡片
  packageTwoData? get experienceCardData =>
      throw _privateConstructorUsedError; //课包2卡片数据
  String? get courseTypeDesc => throw _privateConstructorUsedError; //卡片类型中文 埋点用
  String? get notifySettingRoute => throw _privateConstructorUsedError; //设置提醒路由
  int? get userCourseId => throw _privateConstructorUsedError; //卡片唯一id
  int? get cardHeight => throw _privateConstructorUsedError; //卡片高度
  String? get associateCourseNumText =>
      throw _privateConstructorUsedError; //课程卡片新增关联课程数量提示
  String? get associateCourseGiftsText =>
      throw _privateConstructorUsedError; //附加信息
  String? get logisticsMsg => throw _privateConstructorUsedError; //物流信息
  List<cardLabelData>? get cardLabelList =>
      throw _privateConstructorUsedError; //标题
  courseCardBase? get courseCardBaseData =>
      throw _privateConstructorUsedError; //加老师入口提示文案
  List<courseServiceData>? get courseServiceList =>
      throw _privateConstructorUsedError; //服务列表
  waitActivateCardInfoData? get waitActivateCardInfo =>
      throw _privateConstructorUsedError; //未激活卡片信息
  int? get viewHeight => throw _privateConstructorUsedError; //列表高度]
  int? get newGetFlag =>
      throw _privateConstructorUsedError; //课程是否为15天内新获得 1是 0否
  String? get newGetGuideText => throw _privateConstructorUsedError; //新获得引导文案
  int? get hasShip => throw _privateConstructorUsedError; //是否有物流气泡   1是0否
  String? get shipGuideText => throw _privateConstructorUsedError; //物流气泡文案
  bool? get newVersionFlag => throw _privateConstructorUsedError; //新版训练营
  String? get serviceGuideText => throw _privateConstructorUsedError; //服务引导文案
  bool? get isNewLessonGuideIsShowed =>
      throw _privateConstructorUsedError; //新课引导是否刚显示过
  bool? get isCampCardStyle1 =>
      throw _privateConstructorUsedError; //新版训练营 - 是否是卡包1元素样式
// TMLessonSKURecommendModel? recommendModel, // 栏目位数据
  String? get businessTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $cardModelDataCopyWith<cardModelData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $cardModelDataCopyWith<$Res> {
  factory $cardModelDataCopyWith(
          cardModelData value, $Res Function(cardModelData) then) =
      _$cardModelDataCopyWithImpl<$Res, cardModelData>;
  @useResult
  $Res call(
      {String? cardLabel,
      int? subjectType,
      String? subjectTypeDesc,
      String? segmentName,
      int? courseType,
      String? courseTypeIcon,
      String? courseLabelUrl,
      int? courseId,
      String? courseName,
      int? courseStatus,
      String? courseKey,
      int? teacherId,
      String? teacherProfileUrl,
      int? classId,
      String? classKey,
      String? bgColor,
      String? labelFontColor,
      String? cardMainColor,
      String? topLeftLabelFontColor,
      String? courseFinishedIcon,
      String? startNotify,
      String? rollingCourseNextStartNotify,
      bool? isAddTeacher,
      String? addTeacherUrl,
      String? addTeacherText,
      String? addTeacherEntranceTip,
      String? courseStatusDesc,
      String? cardStyle,
      int? enumCardStyle,
      packageTwoData? experienceCardData,
      String? courseTypeDesc,
      String? notifySettingRoute,
      int? userCourseId,
      int? cardHeight,
      String? associateCourseNumText,
      String? associateCourseGiftsText,
      String? logisticsMsg,
      List<cardLabelData>? cardLabelList,
      courseCardBase? courseCardBaseData,
      List<courseServiceData>? courseServiceList,
      waitActivateCardInfoData? waitActivateCardInfo,
      int? viewHeight,
      int? newGetFlag,
      String? newGetGuideText,
      int? hasShip,
      String? shipGuideText,
      bool? newVersionFlag,
      String? serviceGuideText,
      bool? isNewLessonGuideIsShowed,
      bool? isCampCardStyle1,
      String? businessTypeDesc});

  $packageTwoDataCopyWith<$Res>? get experienceCardData;
  $courseCardBaseCopyWith<$Res>? get courseCardBaseData;
  $waitActivateCardInfoDataCopyWith<$Res>? get waitActivateCardInfo;
}

/// @nodoc
class _$cardModelDataCopyWithImpl<$Res, $Val extends cardModelData>
    implements $cardModelDataCopyWith<$Res> {
  _$cardModelDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardLabel = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentName = freezed,
    Object? courseType = freezed,
    Object? courseTypeIcon = freezed,
    Object? courseLabelUrl = freezed,
    Object? courseId = freezed,
    Object? courseName = freezed,
    Object? courseStatus = freezed,
    Object? courseKey = freezed,
    Object? teacherId = freezed,
    Object? teacherProfileUrl = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? bgColor = freezed,
    Object? labelFontColor = freezed,
    Object? cardMainColor = freezed,
    Object? topLeftLabelFontColor = freezed,
    Object? courseFinishedIcon = freezed,
    Object? startNotify = freezed,
    Object? rollingCourseNextStartNotify = freezed,
    Object? isAddTeacher = freezed,
    Object? addTeacherUrl = freezed,
    Object? addTeacherText = freezed,
    Object? addTeacherEntranceTip = freezed,
    Object? courseStatusDesc = freezed,
    Object? cardStyle = freezed,
    Object? enumCardStyle = freezed,
    Object? experienceCardData = freezed,
    Object? courseTypeDesc = freezed,
    Object? notifySettingRoute = freezed,
    Object? userCourseId = freezed,
    Object? cardHeight = freezed,
    Object? associateCourseNumText = freezed,
    Object? associateCourseGiftsText = freezed,
    Object? logisticsMsg = freezed,
    Object? cardLabelList = freezed,
    Object? courseCardBaseData = freezed,
    Object? courseServiceList = freezed,
    Object? waitActivateCardInfo = freezed,
    Object? viewHeight = freezed,
    Object? newGetFlag = freezed,
    Object? newGetGuideText = freezed,
    Object? hasShip = freezed,
    Object? shipGuideText = freezed,
    Object? newVersionFlag = freezed,
    Object? serviceGuideText = freezed,
    Object? isNewLessonGuideIsShowed = freezed,
    Object? isCampCardStyle1 = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      cardLabel: freezed == cardLabel
          ? _value.cardLabel
          : cardLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeIcon: freezed == courseTypeIcon
          ? _value.courseTypeIcon
          : courseTypeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabelUrl: freezed == courseLabelUrl
          ? _value.courseLabelUrl
          : courseLabelUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      labelFontColor: freezed == labelFontColor
          ? _value.labelFontColor
          : labelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      cardMainColor: freezed == cardMainColor
          ? _value.cardMainColor
          : cardMainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      topLeftLabelFontColor: freezed == topLeftLabelFontColor
          ? _value.topLeftLabelFontColor
          : topLeftLabelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      courseFinishedIcon: freezed == courseFinishedIcon
          ? _value.courseFinishedIcon
          : courseFinishedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      startNotify: freezed == startNotify
          ? _value.startNotify
          : startNotify // ignore: cast_nullable_to_non_nullable
              as String?,
      rollingCourseNextStartNotify: freezed == rollingCourseNextStartNotify
          ? _value.rollingCourseNextStartNotify
          : rollingCourseNextStartNotify // ignore: cast_nullable_to_non_nullable
              as String?,
      isAddTeacher: freezed == isAddTeacher
          ? _value.isAddTeacher
          : isAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherText: freezed == addTeacherText
          ? _value.addTeacherText
          : addTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherEntranceTip: freezed == addTeacherEntranceTip
          ? _value.addTeacherEntranceTip
          : addTeacherEntranceTip // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatusDesc: freezed == courseStatusDesc
          ? _value.courseStatusDesc
          : courseStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      cardStyle: freezed == cardStyle
          ? _value.cardStyle
          : cardStyle // ignore: cast_nullable_to_non_nullable
              as String?,
      enumCardStyle: freezed == enumCardStyle
          ? _value.enumCardStyle
          : enumCardStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      experienceCardData: freezed == experienceCardData
          ? _value.experienceCardData
          : experienceCardData // ignore: cast_nullable_to_non_nullable
              as packageTwoData?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      notifySettingRoute: freezed == notifySettingRoute
          ? _value.notifySettingRoute
          : notifySettingRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      cardHeight: freezed == cardHeight
          ? _value.cardHeight
          : cardHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      associateCourseNumText: freezed == associateCourseNumText
          ? _value.associateCourseNumText
          : associateCourseNumText // ignore: cast_nullable_to_non_nullable
              as String?,
      associateCourseGiftsText: freezed == associateCourseGiftsText
          ? _value.associateCourseGiftsText
          : associateCourseGiftsText // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsMsg: freezed == logisticsMsg
          ? _value.logisticsMsg
          : logisticsMsg // ignore: cast_nullable_to_non_nullable
              as String?,
      cardLabelList: freezed == cardLabelList
          ? _value.cardLabelList
          : cardLabelList // ignore: cast_nullable_to_non_nullable
              as List<cardLabelData>?,
      courseCardBaseData: freezed == courseCardBaseData
          ? _value.courseCardBaseData
          : courseCardBaseData // ignore: cast_nullable_to_non_nullable
              as courseCardBase?,
      courseServiceList: freezed == courseServiceList
          ? _value.courseServiceList
          : courseServiceList // ignore: cast_nullable_to_non_nullable
              as List<courseServiceData>?,
      waitActivateCardInfo: freezed == waitActivateCardInfo
          ? _value.waitActivateCardInfo
          : waitActivateCardInfo // ignore: cast_nullable_to_non_nullable
              as waitActivateCardInfoData?,
      viewHeight: freezed == viewHeight
          ? _value.viewHeight
          : viewHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetGuideText: freezed == newGetGuideText
          ? _value.newGetGuideText
          : newGetGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      hasShip: freezed == hasShip
          ? _value.hasShip
          : hasShip // ignore: cast_nullable_to_non_nullable
              as int?,
      shipGuideText: freezed == shipGuideText
          ? _value.shipGuideText
          : shipGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      newVersionFlag: freezed == newVersionFlag
          ? _value.newVersionFlag
          : newVersionFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      serviceGuideText: freezed == serviceGuideText
          ? _value.serviceGuideText
          : serviceGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      isNewLessonGuideIsShowed: freezed == isNewLessonGuideIsShowed
          ? _value.isNewLessonGuideIsShowed
          : isNewLessonGuideIsShowed // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCampCardStyle1: freezed == isCampCardStyle1
          ? _value.isCampCardStyle1
          : isCampCardStyle1 // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $packageTwoDataCopyWith<$Res>? get experienceCardData {
    if (_value.experienceCardData == null) {
      return null;
    }

    return $packageTwoDataCopyWith<$Res>(_value.experienceCardData!, (value) {
      return _then(_value.copyWith(experienceCardData: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $courseCardBaseCopyWith<$Res>? get courseCardBaseData {
    if (_value.courseCardBaseData == null) {
      return null;
    }

    return $courseCardBaseCopyWith<$Res>(_value.courseCardBaseData!, (value) {
      return _then(_value.copyWith(courseCardBaseData: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $waitActivateCardInfoDataCopyWith<$Res>? get waitActivateCardInfo {
    if (_value.waitActivateCardInfo == null) {
      return null;
    }

    return $waitActivateCardInfoDataCopyWith<$Res>(_value.waitActivateCardInfo!,
        (value) {
      return _then(_value.copyWith(waitActivateCardInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_cardModelDataCopyWith<$Res>
    implements $cardModelDataCopyWith<$Res> {
  factory _$$_cardModelDataCopyWith(
          _$_cardModelData value, $Res Function(_$_cardModelData) then) =
      __$$_cardModelDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? cardLabel,
      int? subjectType,
      String? subjectTypeDesc,
      String? segmentName,
      int? courseType,
      String? courseTypeIcon,
      String? courseLabelUrl,
      int? courseId,
      String? courseName,
      int? courseStatus,
      String? courseKey,
      int? teacherId,
      String? teacherProfileUrl,
      int? classId,
      String? classKey,
      String? bgColor,
      String? labelFontColor,
      String? cardMainColor,
      String? topLeftLabelFontColor,
      String? courseFinishedIcon,
      String? startNotify,
      String? rollingCourseNextStartNotify,
      bool? isAddTeacher,
      String? addTeacherUrl,
      String? addTeacherText,
      String? addTeacherEntranceTip,
      String? courseStatusDesc,
      String? cardStyle,
      int? enumCardStyle,
      packageTwoData? experienceCardData,
      String? courseTypeDesc,
      String? notifySettingRoute,
      int? userCourseId,
      int? cardHeight,
      String? associateCourseNumText,
      String? associateCourseGiftsText,
      String? logisticsMsg,
      List<cardLabelData>? cardLabelList,
      courseCardBase? courseCardBaseData,
      List<courseServiceData>? courseServiceList,
      waitActivateCardInfoData? waitActivateCardInfo,
      int? viewHeight,
      int? newGetFlag,
      String? newGetGuideText,
      int? hasShip,
      String? shipGuideText,
      bool? newVersionFlag,
      String? serviceGuideText,
      bool? isNewLessonGuideIsShowed,
      bool? isCampCardStyle1,
      String? businessTypeDesc});

  @override
  $packageTwoDataCopyWith<$Res>? get experienceCardData;
  @override
  $courseCardBaseCopyWith<$Res>? get courseCardBaseData;
  @override
  $waitActivateCardInfoDataCopyWith<$Res>? get waitActivateCardInfo;
}

/// @nodoc
class __$$_cardModelDataCopyWithImpl<$Res>
    extends _$cardModelDataCopyWithImpl<$Res, _$_cardModelData>
    implements _$$_cardModelDataCopyWith<$Res> {
  __$$_cardModelDataCopyWithImpl(
      _$_cardModelData _value, $Res Function(_$_cardModelData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardLabel = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentName = freezed,
    Object? courseType = freezed,
    Object? courseTypeIcon = freezed,
    Object? courseLabelUrl = freezed,
    Object? courseId = freezed,
    Object? courseName = freezed,
    Object? courseStatus = freezed,
    Object? courseKey = freezed,
    Object? teacherId = freezed,
    Object? teacherProfileUrl = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? bgColor = freezed,
    Object? labelFontColor = freezed,
    Object? cardMainColor = freezed,
    Object? topLeftLabelFontColor = freezed,
    Object? courseFinishedIcon = freezed,
    Object? startNotify = freezed,
    Object? rollingCourseNextStartNotify = freezed,
    Object? isAddTeacher = freezed,
    Object? addTeacherUrl = freezed,
    Object? addTeacherText = freezed,
    Object? addTeacherEntranceTip = freezed,
    Object? courseStatusDesc = freezed,
    Object? cardStyle = freezed,
    Object? enumCardStyle = freezed,
    Object? experienceCardData = freezed,
    Object? courseTypeDesc = freezed,
    Object? notifySettingRoute = freezed,
    Object? userCourseId = freezed,
    Object? cardHeight = freezed,
    Object? associateCourseNumText = freezed,
    Object? associateCourseGiftsText = freezed,
    Object? logisticsMsg = freezed,
    Object? cardLabelList = freezed,
    Object? courseCardBaseData = freezed,
    Object? courseServiceList = freezed,
    Object? waitActivateCardInfo = freezed,
    Object? viewHeight = freezed,
    Object? newGetFlag = freezed,
    Object? newGetGuideText = freezed,
    Object? hasShip = freezed,
    Object? shipGuideText = freezed,
    Object? newVersionFlag = freezed,
    Object? serviceGuideText = freezed,
    Object? isNewLessonGuideIsShowed = freezed,
    Object? isCampCardStyle1 = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_$_cardModelData(
      cardLabel: freezed == cardLabel
          ? _value.cardLabel
          : cardLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeIcon: freezed == courseTypeIcon
          ? _value.courseTypeIcon
          : courseTypeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabelUrl: freezed == courseLabelUrl
          ? _value.courseLabelUrl
          : courseLabelUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      labelFontColor: freezed == labelFontColor
          ? _value.labelFontColor
          : labelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      cardMainColor: freezed == cardMainColor
          ? _value.cardMainColor
          : cardMainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      topLeftLabelFontColor: freezed == topLeftLabelFontColor
          ? _value.topLeftLabelFontColor
          : topLeftLabelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      courseFinishedIcon: freezed == courseFinishedIcon
          ? _value.courseFinishedIcon
          : courseFinishedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      startNotify: freezed == startNotify
          ? _value.startNotify
          : startNotify // ignore: cast_nullable_to_non_nullable
              as String?,
      rollingCourseNextStartNotify: freezed == rollingCourseNextStartNotify
          ? _value.rollingCourseNextStartNotify
          : rollingCourseNextStartNotify // ignore: cast_nullable_to_non_nullable
              as String?,
      isAddTeacher: freezed == isAddTeacher
          ? _value.isAddTeacher
          : isAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherText: freezed == addTeacherText
          ? _value.addTeacherText
          : addTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherEntranceTip: freezed == addTeacherEntranceTip
          ? _value.addTeacherEntranceTip
          : addTeacherEntranceTip // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatusDesc: freezed == courseStatusDesc
          ? _value.courseStatusDesc
          : courseStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      cardStyle: freezed == cardStyle
          ? _value.cardStyle
          : cardStyle // ignore: cast_nullable_to_non_nullable
              as String?,
      enumCardStyle: freezed == enumCardStyle
          ? _value.enumCardStyle
          : enumCardStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      experienceCardData: freezed == experienceCardData
          ? _value.experienceCardData
          : experienceCardData // ignore: cast_nullable_to_non_nullable
              as packageTwoData?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      notifySettingRoute: freezed == notifySettingRoute
          ? _value.notifySettingRoute
          : notifySettingRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      cardHeight: freezed == cardHeight
          ? _value.cardHeight
          : cardHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      associateCourseNumText: freezed == associateCourseNumText
          ? _value.associateCourseNumText
          : associateCourseNumText // ignore: cast_nullable_to_non_nullable
              as String?,
      associateCourseGiftsText: freezed == associateCourseGiftsText
          ? _value.associateCourseGiftsText
          : associateCourseGiftsText // ignore: cast_nullable_to_non_nullable
              as String?,
      logisticsMsg: freezed == logisticsMsg
          ? _value.logisticsMsg
          : logisticsMsg // ignore: cast_nullable_to_non_nullable
              as String?,
      cardLabelList: freezed == cardLabelList
          ? _value._cardLabelList
          : cardLabelList // ignore: cast_nullable_to_non_nullable
              as List<cardLabelData>?,
      courseCardBaseData: freezed == courseCardBaseData
          ? _value.courseCardBaseData
          : courseCardBaseData // ignore: cast_nullable_to_non_nullable
              as courseCardBase?,
      courseServiceList: freezed == courseServiceList
          ? _value._courseServiceList
          : courseServiceList // ignore: cast_nullable_to_non_nullable
              as List<courseServiceData>?,
      waitActivateCardInfo: freezed == waitActivateCardInfo
          ? _value.waitActivateCardInfo
          : waitActivateCardInfo // ignore: cast_nullable_to_non_nullable
              as waitActivateCardInfoData?,
      viewHeight: freezed == viewHeight
          ? _value.viewHeight
          : viewHeight // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetGuideText: freezed == newGetGuideText
          ? _value.newGetGuideText
          : newGetGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      hasShip: freezed == hasShip
          ? _value.hasShip
          : hasShip // ignore: cast_nullable_to_non_nullable
              as int?,
      shipGuideText: freezed == shipGuideText
          ? _value.shipGuideText
          : shipGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      newVersionFlag: freezed == newVersionFlag
          ? _value.newVersionFlag
          : newVersionFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      serviceGuideText: freezed == serviceGuideText
          ? _value.serviceGuideText
          : serviceGuideText // ignore: cast_nullable_to_non_nullable
              as String?,
      isNewLessonGuideIsShowed: freezed == isNewLessonGuideIsShowed
          ? _value.isNewLessonGuideIsShowed
          : isNewLessonGuideIsShowed // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCampCardStyle1: freezed == isCampCardStyle1
          ? _value.isCampCardStyle1
          : isCampCardStyle1 // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_cardModelData implements _cardModelData {
  const _$_cardModelData(
      {this.cardLabel,
      this.subjectType,
      this.subjectTypeDesc,
      this.segmentName,
      this.courseType,
      this.courseTypeIcon,
      this.courseLabelUrl,
      this.courseId,
      this.courseName,
      this.courseStatus,
      this.courseKey,
      this.teacherId,
      this.teacherProfileUrl,
      this.classId,
      this.classKey,
      this.bgColor,
      this.labelFontColor,
      this.cardMainColor,
      this.topLeftLabelFontColor,
      this.courseFinishedIcon,
      this.startNotify,
      this.rollingCourseNextStartNotify,
      this.isAddTeacher,
      this.addTeacherUrl,
      this.addTeacherText,
      this.addTeacherEntranceTip,
      this.courseStatusDesc,
      this.cardStyle,
      this.enumCardStyle,
      this.experienceCardData,
      this.courseTypeDesc,
      this.notifySettingRoute,
      this.userCourseId,
      this.cardHeight,
      this.associateCourseNumText,
      this.associateCourseGiftsText,
      this.logisticsMsg,
      final List<cardLabelData>? cardLabelList,
      this.courseCardBaseData,
      final List<courseServiceData>? courseServiceList,
      this.waitActivateCardInfo,
      this.viewHeight,
      this.newGetFlag,
      this.newGetGuideText,
      this.hasShip,
      this.shipGuideText,
      this.newVersionFlag,
      this.serviceGuideText,
      this.isNewLessonGuideIsShowed,
      this.isCampCardStyle1,
      this.businessTypeDesc})
      : _cardLabelList = cardLabelList,
        _courseServiceList = courseServiceList;

  factory _$_cardModelData.fromJson(Map<String, dynamic> json) =>
      _$$_cardModelDataFromJson(json);

  @override
  final String? cardLabel;
//课程标题：卡片标签
  @override
  final int? subjectType;
//课程类型
  @override
  final String? subjectTypeDesc;
//课程标题：科目
  @override
  final String? segmentName;
//课程标题：阶段
  @override
  final int? courseType;
//课程标题：类型1,体验包（训练营）2,主题包4 ,试用包3,  系统包（年课）
  @override
  final String? courseTypeIcon;
//课程标题：类型对应卡片
  @override
  final String? courseLabelUrl;
//课程标签跳转url
  @override
  final int? courseId;
//课程ID
  @override
  final String? courseName;
//课程名称
  @override
  final int? courseStatus;
//课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  @override
  final String? courseKey;
//课程key
  @override
  final int? teacherId;
//老师ID
  @override
  final String? teacherProfileUrl;
//对应老师头像
  @override
  final int? classId;
//班级id
  @override
  final String? classKey;
//班期key
  @override
  final String? bgColor;
//背景色-Apollo配置：根据科目区分
  @override
  final String? labelFontColor;
//文字色-Apollo配置：根据科目区分
  @override
  final String? cardMainColor;
//主色-Apollo配置：根据科目区分
  @override
  final String? topLeftLabelFontColor;
//标题色-Apollo配置：根据科目区分
  @override
  final String? courseFinishedIcon;
//课程已完成图标
  @override
  final String? startNotify;
//开课提醒
  @override
  final String? rollingCourseNextStartNotify;
//提示信息
  @override
  final bool? isAddTeacher;
//是否已加老师
  @override
  final String? addTeacherUrl;
//加老师跳转url
  @override
  final String? addTeacherText;
//加老师提示文案
  @override
  final String? addTeacherEntranceTip;
//加老师入口提示文案
  @override
  final String? courseStatusDesc;
//卡片状态中文 埋点用
  @override
  final String? cardStyle;
//卡片样式，v2:课包2卡片
  @override
  final int? enumCardStyle;
//卡片样式 0课包1 默认,1课包2,2领课栏目,3空卡片,4视频领课卡片
  @override
  final packageTwoData? experienceCardData;
//课包2卡片数据
  @override
  final String? courseTypeDesc;
//卡片类型中文 埋点用
  @override
  final String? notifySettingRoute;
//设置提醒路由
  @override
  final int? userCourseId;
//卡片唯一id
  @override
  final int? cardHeight;
//卡片高度
  @override
  final String? associateCourseNumText;
//课程卡片新增关联课程数量提示
  @override
  final String? associateCourseGiftsText;
//附加信息
  @override
  final String? logisticsMsg;
//物流信息
  final List<cardLabelData>? _cardLabelList;
//物流信息
  @override
  List<cardLabelData>? get cardLabelList {
    final value = _cardLabelList;
    if (value == null) return null;
    if (_cardLabelList is EqualUnmodifiableListView) return _cardLabelList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//标题
  @override
  final courseCardBase? courseCardBaseData;
//加老师入口提示文案
  final List<courseServiceData>? _courseServiceList;
//加老师入口提示文案
  @override
  List<courseServiceData>? get courseServiceList {
    final value = _courseServiceList;
    if (value == null) return null;
    if (_courseServiceList is EqualUnmodifiableListView)
      return _courseServiceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//服务列表
  @override
  final waitActivateCardInfoData? waitActivateCardInfo;
//未激活卡片信息
  @override
  final int? viewHeight;
//列表高度]
  @override
  final int? newGetFlag;
//课程是否为15天内新获得 1是 0否
  @override
  final String? newGetGuideText;
//新获得引导文案
  @override
  final int? hasShip;
//是否有物流气泡   1是0否
  @override
  final String? shipGuideText;
//物流气泡文案
  @override
  final bool? newVersionFlag;
//新版训练营
  @override
  final String? serviceGuideText;
//服务引导文案
  @override
  final bool? isNewLessonGuideIsShowed;
//新课引导是否刚显示过
  @override
  final bool? isCampCardStyle1;
//新版训练营 - 是否是卡包1元素样式
// TMLessonSKURecommendModel? recommendModel, // 栏目位数据
  @override
  final String? businessTypeDesc;

  @override
  String toString() {
    return 'cardModelData(cardLabel: $cardLabel, subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, segmentName: $segmentName, courseType: $courseType, courseTypeIcon: $courseTypeIcon, courseLabelUrl: $courseLabelUrl, courseId: $courseId, courseName: $courseName, courseStatus: $courseStatus, courseKey: $courseKey, teacherId: $teacherId, teacherProfileUrl: $teacherProfileUrl, classId: $classId, classKey: $classKey, bgColor: $bgColor, labelFontColor: $labelFontColor, cardMainColor: $cardMainColor, topLeftLabelFontColor: $topLeftLabelFontColor, courseFinishedIcon: $courseFinishedIcon, startNotify: $startNotify, rollingCourseNextStartNotify: $rollingCourseNextStartNotify, isAddTeacher: $isAddTeacher, addTeacherUrl: $addTeacherUrl, addTeacherText: $addTeacherText, addTeacherEntranceTip: $addTeacherEntranceTip, courseStatusDesc: $courseStatusDesc, cardStyle: $cardStyle, enumCardStyle: $enumCardStyle, experienceCardData: $experienceCardData, courseTypeDesc: $courseTypeDesc, notifySettingRoute: $notifySettingRoute, userCourseId: $userCourseId, cardHeight: $cardHeight, associateCourseNumText: $associateCourseNumText, associateCourseGiftsText: $associateCourseGiftsText, logisticsMsg: $logisticsMsg, cardLabelList: $cardLabelList, courseCardBaseData: $courseCardBaseData, courseServiceList: $courseServiceList, waitActivateCardInfo: $waitActivateCardInfo, viewHeight: $viewHeight, newGetFlag: $newGetFlag, newGetGuideText: $newGetGuideText, hasShip: $hasShip, shipGuideText: $shipGuideText, newVersionFlag: $newVersionFlag, serviceGuideText: $serviceGuideText, isNewLessonGuideIsShowed: $isNewLessonGuideIsShowed, isCampCardStyle1: $isCampCardStyle1, businessTypeDesc: $businessTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_cardModelData &&
            (identical(other.cardLabel, cardLabel) ||
                other.cardLabel == cardLabel) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseTypeIcon, courseTypeIcon) ||
                other.courseTypeIcon == courseTypeIcon) &&
            (identical(other.courseLabelUrl, courseLabelUrl) ||
                other.courseLabelUrl == courseLabelUrl) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseStatus, courseStatus) ||
                other.courseStatus == courseStatus) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherProfileUrl, teacherProfileUrl) ||
                other.teacherProfileUrl == teacherProfileUrl) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor) &&
            (identical(other.labelFontColor, labelFontColor) ||
                other.labelFontColor == labelFontColor) &&
            (identical(other.cardMainColor, cardMainColor) ||
                other.cardMainColor == cardMainColor) &&
            (identical(other.topLeftLabelFontColor, topLeftLabelFontColor) ||
                other.topLeftLabelFontColor == topLeftLabelFontColor) &&
            (identical(other.courseFinishedIcon, courseFinishedIcon) ||
                other.courseFinishedIcon == courseFinishedIcon) &&
            (identical(other.startNotify, startNotify) ||
                other.startNotify == startNotify) &&
            (identical(other.rollingCourseNextStartNotify, rollingCourseNextStartNotify) ||
                other.rollingCourseNextStartNotify ==
                    rollingCourseNextStartNotify) &&
            (identical(other.isAddTeacher, isAddTeacher) ||
                other.isAddTeacher == isAddTeacher) &&
            (identical(other.addTeacherUrl, addTeacherUrl) ||
                other.addTeacherUrl == addTeacherUrl) &&
            (identical(other.addTeacherText, addTeacherText) ||
                other.addTeacherText == addTeacherText) &&
            (identical(other.addTeacherEntranceTip, addTeacherEntranceTip) ||
                other.addTeacherEntranceTip == addTeacherEntranceTip) &&
            (identical(other.courseStatusDesc, courseStatusDesc) ||
                other.courseStatusDesc == courseStatusDesc) &&
            (identical(other.cardStyle, cardStyle) ||
                other.cardStyle == cardStyle) &&
            (identical(other.enumCardStyle, enumCardStyle) ||
                other.enumCardStyle == enumCardStyle) &&
            (identical(other.experienceCardData, experienceCardData) ||
                other.experienceCardData == experienceCardData) &&
            (identical(other.courseTypeDesc, courseTypeDesc) ||
                other.courseTypeDesc == courseTypeDesc) &&
            (identical(other.notifySettingRoute, notifySettingRoute) ||
                other.notifySettingRoute == notifySettingRoute) &&
            (identical(other.userCourseId, userCourseId) ||
                other.userCourseId == userCourseId) &&
            (identical(other.cardHeight, cardHeight) ||
                other.cardHeight == cardHeight) &&
            (identical(other.associateCourseNumText, associateCourseNumText) ||
                other.associateCourseNumText == associateCourseNumText) &&
            (identical(other.associateCourseGiftsText, associateCourseGiftsText) ||
                other.associateCourseGiftsText == associateCourseGiftsText) &&
            (identical(other.logisticsMsg, logisticsMsg) ||
                other.logisticsMsg == logisticsMsg) &&
            const DeepCollectionEquality()
                .equals(other._cardLabelList, _cardLabelList) &&
            (identical(other.courseCardBaseData, courseCardBaseData) ||
                other.courseCardBaseData == courseCardBaseData) &&
            const DeepCollectionEquality()
                .equals(other._courseServiceList, _courseServiceList) &&
            (identical(other.waitActivateCardInfo, waitActivateCardInfo) ||
                other.waitActivateCardInfo == waitActivateCardInfo) &&
            (identical(other.viewHeight, viewHeight) || other.viewHeight == viewHeight) &&
            (identical(other.newGetFlag, newGetFlag) || other.newGetFlag == newGetFlag) &&
            (identical(other.newGetGuideText, newGetGuideText) || other.newGetGuideText == newGetGuideText) &&
            (identical(other.hasShip, hasShip) || other.hasShip == hasShip) &&
            (identical(other.shipGuideText, shipGuideText) || other.shipGuideText == shipGuideText) &&
            (identical(other.newVersionFlag, newVersionFlag) || other.newVersionFlag == newVersionFlag) &&
            (identical(other.serviceGuideText, serviceGuideText) || other.serviceGuideText == serviceGuideText) &&
            (identical(other.isNewLessonGuideIsShowed, isNewLessonGuideIsShowed) || other.isNewLessonGuideIsShowed == isNewLessonGuideIsShowed) &&
            (identical(other.isCampCardStyle1, isCampCardStyle1) || other.isCampCardStyle1 == isCampCardStyle1) &&
            (identical(other.businessTypeDesc, businessTypeDesc) || other.businessTypeDesc == businessTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        cardLabel,
        subjectType,
        subjectTypeDesc,
        segmentName,
        courseType,
        courseTypeIcon,
        courseLabelUrl,
        courseId,
        courseName,
        courseStatus,
        courseKey,
        teacherId,
        teacherProfileUrl,
        classId,
        classKey,
        bgColor,
        labelFontColor,
        cardMainColor,
        topLeftLabelFontColor,
        courseFinishedIcon,
        startNotify,
        rollingCourseNextStartNotify,
        isAddTeacher,
        addTeacherUrl,
        addTeacherText,
        addTeacherEntranceTip,
        courseStatusDesc,
        cardStyle,
        enumCardStyle,
        experienceCardData,
        courseTypeDesc,
        notifySettingRoute,
        userCourseId,
        cardHeight,
        associateCourseNumText,
        associateCourseGiftsText,
        logisticsMsg,
        const DeepCollectionEquality().hash(_cardLabelList),
        courseCardBaseData,
        const DeepCollectionEquality().hash(_courseServiceList),
        waitActivateCardInfo,
        viewHeight,
        newGetFlag,
        newGetGuideText,
        hasShip,
        shipGuideText,
        newVersionFlag,
        serviceGuideText,
        isNewLessonGuideIsShowed,
        isCampCardStyle1,
        businessTypeDesc
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_cardModelDataCopyWith<_$_cardModelData> get copyWith =>
      __$$_cardModelDataCopyWithImpl<_$_cardModelData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_cardModelDataToJson(
      this,
    );
  }
}

abstract class _cardModelData implements cardModelData {
  const factory _cardModelData(
      {final String? cardLabel,
      final int? subjectType,
      final String? subjectTypeDesc,
      final String? segmentName,
      final int? courseType,
      final String? courseTypeIcon,
      final String? courseLabelUrl,
      final int? courseId,
      final String? courseName,
      final int? courseStatus,
      final String? courseKey,
      final int? teacherId,
      final String? teacherProfileUrl,
      final int? classId,
      final String? classKey,
      final String? bgColor,
      final String? labelFontColor,
      final String? cardMainColor,
      final String? topLeftLabelFontColor,
      final String? courseFinishedIcon,
      final String? startNotify,
      final String? rollingCourseNextStartNotify,
      final bool? isAddTeacher,
      final String? addTeacherUrl,
      final String? addTeacherText,
      final String? addTeacherEntranceTip,
      final String? courseStatusDesc,
      final String? cardStyle,
      final int? enumCardStyle,
      final packageTwoData? experienceCardData,
      final String? courseTypeDesc,
      final String? notifySettingRoute,
      final int? userCourseId,
      final int? cardHeight,
      final String? associateCourseNumText,
      final String? associateCourseGiftsText,
      final String? logisticsMsg,
      final List<cardLabelData>? cardLabelList,
      final courseCardBase? courseCardBaseData,
      final List<courseServiceData>? courseServiceList,
      final waitActivateCardInfoData? waitActivateCardInfo,
      final int? viewHeight,
      final int? newGetFlag,
      final String? newGetGuideText,
      final int? hasShip,
      final String? shipGuideText,
      final bool? newVersionFlag,
      final String? serviceGuideText,
      final bool? isNewLessonGuideIsShowed,
      final bool? isCampCardStyle1,
      final String? businessTypeDesc}) = _$_cardModelData;

  factory _cardModelData.fromJson(Map<String, dynamic> json) =
      _$_cardModelData.fromJson;

  @override
  String? get cardLabel;
  @override //课程标题：卡片标签
  int? get subjectType;
  @override //课程类型
  String? get subjectTypeDesc;
  @override //课程标题：科目
  String? get segmentName;
  @override //课程标题：阶段
  int? get courseType;
  @override //课程标题：类型1,体验包（训练营）2,主题包4 ,试用包3,  系统包（年课）
  String? get courseTypeIcon;
  @override //课程标题：类型对应卡片
  String? get courseLabelUrl;
  @override //课程标签跳转url
  int? get courseId;
  @override //课程ID
  String? get courseName;
  @override //课程名称
  int? get courseStatus;
  @override //课程状态 全部-0；待激活-1；待开始-2；进行中-3；已完结-4
  String? get courseKey;
  @override //课程key
  int? get teacherId;
  @override //老师ID
  String? get teacherProfileUrl;
  @override //对应老师头像
  int? get classId;
  @override //班级id
  String? get classKey;
  @override //班期key
  String? get bgColor;
  @override //背景色-Apollo配置：根据科目区分
  String? get labelFontColor;
  @override //文字色-Apollo配置：根据科目区分
  String? get cardMainColor;
  @override //主色-Apollo配置：根据科目区分
  String? get topLeftLabelFontColor;
  @override //标题色-Apollo配置：根据科目区分
  String? get courseFinishedIcon;
  @override //课程已完成图标
  String? get startNotify;
  @override //开课提醒
  String? get rollingCourseNextStartNotify;
  @override //提示信息
  bool? get isAddTeacher;
  @override //是否已加老师
  String? get addTeacherUrl;
  @override //加老师跳转url
  String? get addTeacherText;
  @override //加老师提示文案
  String? get addTeacherEntranceTip;
  @override //加老师入口提示文案
  String? get courseStatusDesc;
  @override //卡片状态中文 埋点用
  String? get cardStyle;
  @override //卡片样式，v2:课包2卡片
  int? get enumCardStyle;
  @override //卡片样式 0课包1 默认,1课包2,2领课栏目,3空卡片,4视频领课卡片
  packageTwoData? get experienceCardData;
  @override //课包2卡片数据
  String? get courseTypeDesc;
  @override //卡片类型中文 埋点用
  String? get notifySettingRoute;
  @override //设置提醒路由
  int? get userCourseId;
  @override //卡片唯一id
  int? get cardHeight;
  @override //卡片高度
  String? get associateCourseNumText;
  @override //课程卡片新增关联课程数量提示
  String? get associateCourseGiftsText;
  @override //附加信息
  String? get logisticsMsg;
  @override //物流信息
  List<cardLabelData>? get cardLabelList;
  @override //标题
  courseCardBase? get courseCardBaseData;
  @override //加老师入口提示文案
  List<courseServiceData>? get courseServiceList;
  @override //服务列表
  waitActivateCardInfoData? get waitActivateCardInfo;
  @override //未激活卡片信息
  int? get viewHeight;
  @override //列表高度]
  int? get newGetFlag;
  @override //课程是否为15天内新获得 1是 0否
  String? get newGetGuideText;
  @override //新获得引导文案
  int? get hasShip;
  @override //是否有物流气泡   1是0否
  String? get shipGuideText;
  @override //物流气泡文案
  bool? get newVersionFlag;
  @override //新版训练营
  String? get serviceGuideText;
  @override //服务引导文案
  bool? get isNewLessonGuideIsShowed;
  @override //新课引导是否刚显示过
  bool? get isCampCardStyle1;
  @override //新版训练营 - 是否是卡包1元素样式
// TMLessonSKURecommendModel? recommendModel, // 栏目位数据
  String? get businessTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_cardModelDataCopyWith<_$_cardModelData> get copyWith =>
      throw _privateConstructorUsedError;
}

packageTwoData _$packageTwoDataFromJson(Map<String, dynamic> json) {
  return _packageTwoData.fromJson(json);
}

/// @nodoc
mixin _$packageTwoData {
  packageTwoItemData? get forestActivity =>
      throw _privateConstructorUsedError; // 萌萌森林入口
  List<packageTwoItemData>? get headCardItems =>
      throw _privateConstructorUsedError; // 课包2头部入口，可能存在两个入口都没有的情况，返回会是空数组
  List<packageTwoItemData>? get lessonCardItems =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $packageTwoDataCopyWith<packageTwoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $packageTwoDataCopyWith<$Res> {
  factory $packageTwoDataCopyWith(
          packageTwoData value, $Res Function(packageTwoData) then) =
      _$packageTwoDataCopyWithImpl<$Res, packageTwoData>;
  @useResult
  $Res call(
      {packageTwoItemData? forestActivity,
      List<packageTwoItemData>? headCardItems,
      List<packageTwoItemData>? lessonCardItems});

  $packageTwoItemDataCopyWith<$Res>? get forestActivity;
}

/// @nodoc
class _$packageTwoDataCopyWithImpl<$Res, $Val extends packageTwoData>
    implements $packageTwoDataCopyWith<$Res> {
  _$packageTwoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forestActivity = freezed,
    Object? headCardItems = freezed,
    Object? lessonCardItems = freezed,
  }) {
    return _then(_value.copyWith(
      forestActivity: freezed == forestActivity
          ? _value.forestActivity
          : forestActivity // ignore: cast_nullable_to_non_nullable
              as packageTwoItemData?,
      headCardItems: freezed == headCardItems
          ? _value.headCardItems
          : headCardItems // ignore: cast_nullable_to_non_nullable
              as List<packageTwoItemData>?,
      lessonCardItems: freezed == lessonCardItems
          ? _value.lessonCardItems
          : lessonCardItems // ignore: cast_nullable_to_non_nullable
              as List<packageTwoItemData>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $packageTwoItemDataCopyWith<$Res>? get forestActivity {
    if (_value.forestActivity == null) {
      return null;
    }

    return $packageTwoItemDataCopyWith<$Res>(_value.forestActivity!, (value) {
      return _then(_value.copyWith(forestActivity: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_packageTwoDataCopyWith<$Res>
    implements $packageTwoDataCopyWith<$Res> {
  factory _$$_packageTwoDataCopyWith(
          _$_packageTwoData value, $Res Function(_$_packageTwoData) then) =
      __$$_packageTwoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {packageTwoItemData? forestActivity,
      List<packageTwoItemData>? headCardItems,
      List<packageTwoItemData>? lessonCardItems});

  @override
  $packageTwoItemDataCopyWith<$Res>? get forestActivity;
}

/// @nodoc
class __$$_packageTwoDataCopyWithImpl<$Res>
    extends _$packageTwoDataCopyWithImpl<$Res, _$_packageTwoData>
    implements _$$_packageTwoDataCopyWith<$Res> {
  __$$_packageTwoDataCopyWithImpl(
      _$_packageTwoData _value, $Res Function(_$_packageTwoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forestActivity = freezed,
    Object? headCardItems = freezed,
    Object? lessonCardItems = freezed,
  }) {
    return _then(_$_packageTwoData(
      forestActivity: freezed == forestActivity
          ? _value.forestActivity
          : forestActivity // ignore: cast_nullable_to_non_nullable
              as packageTwoItemData?,
      headCardItems: freezed == headCardItems
          ? _value._headCardItems
          : headCardItems // ignore: cast_nullable_to_non_nullable
              as List<packageTwoItemData>?,
      lessonCardItems: freezed == lessonCardItems
          ? _value._lessonCardItems
          : lessonCardItems // ignore: cast_nullable_to_non_nullable
              as List<packageTwoItemData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_packageTwoData implements _packageTwoData {
  const _$_packageTwoData(
      {this.forestActivity,
      final List<packageTwoItemData>? headCardItems,
      final List<packageTwoItemData>? lessonCardItems})
      : _headCardItems = headCardItems,
        _lessonCardItems = lessonCardItems;

  factory _$_packageTwoData.fromJson(Map<String, dynamic> json) =>
      _$$_packageTwoDataFromJson(json);

  @override
  final packageTwoItemData? forestActivity;
// 萌萌森林入口
  final List<packageTwoItemData>? _headCardItems;
// 萌萌森林入口
  @override
  List<packageTwoItemData>? get headCardItems {
    final value = _headCardItems;
    if (value == null) return null;
    if (_headCardItems is EqualUnmodifiableListView) return _headCardItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 课包2头部入口，可能存在两个入口都没有的情况，返回会是空数组
  final List<packageTwoItemData>? _lessonCardItems;
// 课包2头部入口，可能存在两个入口都没有的情况，返回会是空数组
  @override
  List<packageTwoItemData>? get lessonCardItems {
    final value = _lessonCardItems;
    if (value == null) return null;
    if (_lessonCardItems is EqualUnmodifiableListView) return _lessonCardItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'packageTwoData(forestActivity: $forestActivity, headCardItems: $headCardItems, lessonCardItems: $lessonCardItems)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_packageTwoData &&
            (identical(other.forestActivity, forestActivity) ||
                other.forestActivity == forestActivity) &&
            const DeepCollectionEquality()
                .equals(other._headCardItems, _headCardItems) &&
            const DeepCollectionEquality()
                .equals(other._lessonCardItems, _lessonCardItems));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      forestActivity,
      const DeepCollectionEquality().hash(_headCardItems),
      const DeepCollectionEquality().hash(_lessonCardItems));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_packageTwoDataCopyWith<_$_packageTwoData> get copyWith =>
      __$$_packageTwoDataCopyWithImpl<_$_packageTwoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_packageTwoDataToJson(
      this,
    );
  }
}

abstract class _packageTwoData implements packageTwoData {
  const factory _packageTwoData(
      {final packageTwoItemData? forestActivity,
      final List<packageTwoItemData>? headCardItems,
      final List<packageTwoItemData>? lessonCardItems}) = _$_packageTwoData;

  factory _packageTwoData.fromJson(Map<String, dynamic> json) =
      _$_packageTwoData.fromJson;

  @override
  packageTwoItemData? get forestActivity;
  @override // 萌萌森林入口
  List<packageTwoItemData>? get headCardItems;
  @override // 课包2头部入口，可能存在两个入口都没有的情况，返回会是空数组
  List<packageTwoItemData>? get lessonCardItems;
  @override
  @JsonKey(ignore: true)
  _$$_packageTwoDataCopyWith<_$_packageTwoData> get copyWith =>
      throw _privateConstructorUsedError;
}

packageTwoItemData _$packageTwoItemDataFromJson(Map<String, dynamic> json) {
  return _packageTwoItemData.fromJson(json);
}

/// @nodoc
mixin _$packageTwoItemData {
  String? get title => throw _privateConstructorUsedError; //标题
  String? get icon => throw _privateConstructorUsedError; //图标
  String? get content => throw _privateConstructorUsedError; //内容
  String? get desc => throw _privateConstructorUsedError; //描述
  String? get type =>
      throw _privateConstructorUsedError; //类型，forest_activity萌萌森林，read_ability阅读力测评，add_teacher指导师
  int? get status => throw _privateConstructorUsedError; //1.待学习 2.已学习 3.未解锁
  String? get startDate => throw _privateConstructorUsedError; //课程开始日期
  String? get startWeek => throw _privateConstructorUsedError; //课程开始周几
  String? get redirectUrl => throw _privateConstructorUsedError; //跳转链接
  String? get businessTypeDesc => throw _privateConstructorUsedError; //业务类型描述
/*************获取跳转路由参数*************/
  int? get segmentId => throw _privateConstructorUsedError; //主题月id
  int? get weekId => throw _privateConstructorUsedError; //周id
  int? get lessonId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $packageTwoItemDataCopyWith<packageTwoItemData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $packageTwoItemDataCopyWith<$Res> {
  factory $packageTwoItemDataCopyWith(
          packageTwoItemData value, $Res Function(packageTwoItemData) then) =
      _$packageTwoItemDataCopyWithImpl<$Res, packageTwoItemData>;
  @useResult
  $Res call(
      {String? title,
      String? icon,
      String? content,
      String? desc,
      String? type,
      int? status,
      String? startDate,
      String? startWeek,
      String? redirectUrl,
      String? businessTypeDesc,
      int? segmentId,
      int? weekId,
      int? lessonId});
}

/// @nodoc
class _$packageTwoItemDataCopyWithImpl<$Res, $Val extends packageTwoItemData>
    implements $packageTwoItemDataCopyWith<$Res> {
  _$packageTwoItemDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? icon = freezed,
    Object? content = freezed,
    Object? desc = freezed,
    Object? type = freezed,
    Object? status = freezed,
    Object? startDate = freezed,
    Object? startWeek = freezed,
    Object? redirectUrl = freezed,
    Object? businessTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? lessonId = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      startWeek: freezed == startWeek
          ? _value.startWeek
          : startWeek // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_packageTwoItemDataCopyWith<$Res>
    implements $packageTwoItemDataCopyWith<$Res> {
  factory _$$_packageTwoItemDataCopyWith(_$_packageTwoItemData value,
          $Res Function(_$_packageTwoItemData) then) =
      __$$_packageTwoItemDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? icon,
      String? content,
      String? desc,
      String? type,
      int? status,
      String? startDate,
      String? startWeek,
      String? redirectUrl,
      String? businessTypeDesc,
      int? segmentId,
      int? weekId,
      int? lessonId});
}

/// @nodoc
class __$$_packageTwoItemDataCopyWithImpl<$Res>
    extends _$packageTwoItemDataCopyWithImpl<$Res, _$_packageTwoItemData>
    implements _$$_packageTwoItemDataCopyWith<$Res> {
  __$$_packageTwoItemDataCopyWithImpl(
      _$_packageTwoItemData _value, $Res Function(_$_packageTwoItemData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? icon = freezed,
    Object? content = freezed,
    Object? desc = freezed,
    Object? type = freezed,
    Object? status = freezed,
    Object? startDate = freezed,
    Object? startWeek = freezed,
    Object? redirectUrl = freezed,
    Object? businessTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? lessonId = freezed,
  }) {
    return _then(_$_packageTwoItemData(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      startWeek: freezed == startWeek
          ? _value.startWeek
          : startWeek // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_packageTwoItemData implements _packageTwoItemData {
  const _$_packageTwoItemData(
      {this.title,
      this.icon,
      this.content,
      this.desc,
      this.type,
      this.status,
      this.startDate,
      this.startWeek,
      this.redirectUrl,
      this.businessTypeDesc,
      this.segmentId,
      this.weekId,
      this.lessonId});

  factory _$_packageTwoItemData.fromJson(Map<String, dynamic> json) =>
      _$$_packageTwoItemDataFromJson(json);

  @override
  final String? title;
//标题
  @override
  final String? icon;
//图标
  @override
  final String? content;
//内容
  @override
  final String? desc;
//描述
  @override
  final String? type;
//类型，forest_activity萌萌森林，read_ability阅读力测评，add_teacher指导师
  @override
  final int? status;
//1.待学习 2.已学习 3.未解锁
  @override
  final String? startDate;
//课程开始日期
  @override
  final String? startWeek;
//课程开始周几
  @override
  final String? redirectUrl;
//跳转链接
  @override
  final String? businessTypeDesc;
//业务类型描述
/*************获取跳转路由参数*************/
  @override
  final int? segmentId;
//主题月id
  @override
  final int? weekId;
//周id
  @override
  final int? lessonId;

  @override
  String toString() {
    return 'packageTwoItemData(title: $title, icon: $icon, content: $content, desc: $desc, type: $type, status: $status, startDate: $startDate, startWeek: $startWeek, redirectUrl: $redirectUrl, businessTypeDesc: $businessTypeDesc, segmentId: $segmentId, weekId: $weekId, lessonId: $lessonId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_packageTwoItemData &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.startWeek, startWeek) ||
                other.startWeek == startWeek) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.businessTypeDesc, businessTypeDesc) ||
                other.businessTypeDesc == businessTypeDesc) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      icon,
      content,
      desc,
      type,
      status,
      startDate,
      startWeek,
      redirectUrl,
      businessTypeDesc,
      segmentId,
      weekId,
      lessonId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_packageTwoItemDataCopyWith<_$_packageTwoItemData> get copyWith =>
      __$$_packageTwoItemDataCopyWithImpl<_$_packageTwoItemData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_packageTwoItemDataToJson(
      this,
    );
  }
}

abstract class _packageTwoItemData implements packageTwoItemData {
  const factory _packageTwoItemData(
      {final String? title,
      final String? icon,
      final String? content,
      final String? desc,
      final String? type,
      final int? status,
      final String? startDate,
      final String? startWeek,
      final String? redirectUrl,
      final String? businessTypeDesc,
      final int? segmentId,
      final int? weekId,
      final int? lessonId}) = _$_packageTwoItemData;

  factory _packageTwoItemData.fromJson(Map<String, dynamic> json) =
      _$_packageTwoItemData.fromJson;

  @override
  String? get title;
  @override //标题
  String? get icon;
  @override //图标
  String? get content;
  @override //内容
  String? get desc;
  @override //描述
  String? get type;
  @override //类型，forest_activity萌萌森林，read_ability阅读力测评，add_teacher指导师
  int? get status;
  @override //1.待学习 2.已学习 3.未解锁
  String? get startDate;
  @override //课程开始日期
  String? get startWeek;
  @override //课程开始周几
  String? get redirectUrl;
  @override //跳转链接
  String? get businessTypeDesc;
  @override //业务类型描述
/*************获取跳转路由参数*************/
  int? get segmentId;
  @override //主题月id
  int? get weekId;
  @override //周id
  int? get lessonId;
  @override
  @JsonKey(ignore: true)
  _$$_packageTwoItemDataCopyWith<_$_packageTwoItemData> get copyWith =>
      throw _privateConstructorUsedError;
}

cardLabelData _$cardLabelDataFromJson(Map<String, dynamic> json) {
  return _cardLabelData.fromJson(json);
}

/// @nodoc
mixin _$cardLabelData {
  String? get labelContent => throw _privateConstructorUsedError; //标签内容
  int? get labelEffectType =>
      throw _privateConstructorUsedError; //标签效果(0: 无默认效果， 1:有默认效果)
  int? get labelResourceType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $cardLabelDataCopyWith<cardLabelData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $cardLabelDataCopyWith<$Res> {
  factory $cardLabelDataCopyWith(
          cardLabelData value, $Res Function(cardLabelData) then) =
      _$cardLabelDataCopyWithImpl<$Res, cardLabelData>;
  @useResult
  $Res call(
      {String? labelContent, int? labelEffectType, int? labelResourceType});
}

/// @nodoc
class _$cardLabelDataCopyWithImpl<$Res, $Val extends cardLabelData>
    implements $cardLabelDataCopyWith<$Res> {
  _$cardLabelDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? labelContent = freezed,
    Object? labelEffectType = freezed,
    Object? labelResourceType = freezed,
  }) {
    return _then(_value.copyWith(
      labelContent: freezed == labelContent
          ? _value.labelContent
          : labelContent // ignore: cast_nullable_to_non_nullable
              as String?,
      labelEffectType: freezed == labelEffectType
          ? _value.labelEffectType
          : labelEffectType // ignore: cast_nullable_to_non_nullable
              as int?,
      labelResourceType: freezed == labelResourceType
          ? _value.labelResourceType
          : labelResourceType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_cardLabelDataCopyWith<$Res>
    implements $cardLabelDataCopyWith<$Res> {
  factory _$$_cardLabelDataCopyWith(
          _$_cardLabelData value, $Res Function(_$_cardLabelData) then) =
      __$$_cardLabelDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? labelContent, int? labelEffectType, int? labelResourceType});
}

/// @nodoc
class __$$_cardLabelDataCopyWithImpl<$Res>
    extends _$cardLabelDataCopyWithImpl<$Res, _$_cardLabelData>
    implements _$$_cardLabelDataCopyWith<$Res> {
  __$$_cardLabelDataCopyWithImpl(
      _$_cardLabelData _value, $Res Function(_$_cardLabelData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? labelContent = freezed,
    Object? labelEffectType = freezed,
    Object? labelResourceType = freezed,
  }) {
    return _then(_$_cardLabelData(
      labelContent: freezed == labelContent
          ? _value.labelContent
          : labelContent // ignore: cast_nullable_to_non_nullable
              as String?,
      labelEffectType: freezed == labelEffectType
          ? _value.labelEffectType
          : labelEffectType // ignore: cast_nullable_to_non_nullable
              as int?,
      labelResourceType: freezed == labelResourceType
          ? _value.labelResourceType
          : labelResourceType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_cardLabelData implements _cardLabelData {
  const _$_cardLabelData(
      {this.labelContent, this.labelEffectType, this.labelResourceType});

  factory _$_cardLabelData.fromJson(Map<String, dynamic> json) =>
      _$$_cardLabelDataFromJson(json);

  @override
  final String? labelContent;
//标签内容
  @override
  final int? labelEffectType;
//标签效果(0: 无默认效果， 1:有默认效果)
  @override
  final int? labelResourceType;

  @override
  String toString() {
    return 'cardLabelData(labelContent: $labelContent, labelEffectType: $labelEffectType, labelResourceType: $labelResourceType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_cardLabelData &&
            (identical(other.labelContent, labelContent) ||
                other.labelContent == labelContent) &&
            (identical(other.labelEffectType, labelEffectType) ||
                other.labelEffectType == labelEffectType) &&
            (identical(other.labelResourceType, labelResourceType) ||
                other.labelResourceType == labelResourceType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, labelContent, labelEffectType, labelResourceType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_cardLabelDataCopyWith<_$_cardLabelData> get copyWith =>
      __$$_cardLabelDataCopyWithImpl<_$_cardLabelData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_cardLabelDataToJson(
      this,
    );
  }
}

abstract class _cardLabelData implements cardLabelData {
  const factory _cardLabelData(
      {final String? labelContent,
      final int? labelEffectType,
      final int? labelResourceType}) = _$_cardLabelData;

  factory _cardLabelData.fromJson(Map<String, dynamic> json) =
      _$_cardLabelData.fromJson;

  @override
  String? get labelContent;
  @override //标签内容
  int? get labelEffectType;
  @override //标签效果(0: 无默认效果， 1:有默认效果)
  int? get labelResourceType;
  @override
  @JsonKey(ignore: true)
  _$$_cardLabelDataCopyWith<_$_cardLabelData> get copyWith =>
      throw _privateConstructorUsedError;
}

courseCardBase _$courseCardBaseFromJson(Map<String, dynamic> json) {
  return _courseCardBase.fromJson(json);
}

/// @nodoc
mixin _$courseCardBase {
  String? get courseName => throw _privateConstructorUsedError; //课程卡片
  String? get courseImage => throw _privateConstructorUsedError; //课程图标
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $courseCardBaseCopyWith<courseCardBase> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $courseCardBaseCopyWith<$Res> {
  factory $courseCardBaseCopyWith(
          courseCardBase value, $Res Function(courseCardBase) then) =
      _$courseCardBaseCopyWithImpl<$Res, courseCardBase>;
  @useResult
  $Res call({String? courseName, String? courseImage, String? route});
}

/// @nodoc
class _$courseCardBaseCopyWithImpl<$Res, $Val extends courseCardBase>
    implements $courseCardBaseCopyWith<$Res> {
  _$courseCardBaseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseImage = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseImage: freezed == courseImage
          ? _value.courseImage
          : courseImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_courseCardBaseCopyWith<$Res>
    implements $courseCardBaseCopyWith<$Res> {
  factory _$$_courseCardBaseCopyWith(
          _$_courseCardBase value, $Res Function(_$_courseCardBase) then) =
      __$$_courseCardBaseCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? courseName, String? courseImage, String? route});
}

/// @nodoc
class __$$_courseCardBaseCopyWithImpl<$Res>
    extends _$courseCardBaseCopyWithImpl<$Res, _$_courseCardBase>
    implements _$$_courseCardBaseCopyWith<$Res> {
  __$$_courseCardBaseCopyWithImpl(
      _$_courseCardBase _value, $Res Function(_$_courseCardBase) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseImage = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_courseCardBase(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseImage: freezed == courseImage
          ? _value.courseImage
          : courseImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_courseCardBase implements _courseCardBase {
  const _$_courseCardBase({this.courseName, this.courseImage, this.route});

  factory _$_courseCardBase.fromJson(Map<String, dynamic> json) =>
      _$$_courseCardBaseFromJson(json);

  @override
  final String? courseName;
//课程卡片
  @override
  final String? courseImage;
//课程图标
  @override
  final String? route;

  @override
  String toString() {
    return 'courseCardBase(courseName: $courseName, courseImage: $courseImage, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_courseCardBase &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseImage, courseImage) ||
                other.courseImage == courseImage) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseName, courseImage, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_courseCardBaseCopyWith<_$_courseCardBase> get copyWith =>
      __$$_courseCardBaseCopyWithImpl<_$_courseCardBase>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_courseCardBaseToJson(
      this,
    );
  }
}

abstract class _courseCardBase implements courseCardBase {
  const factory _courseCardBase(
      {final String? courseName,
      final String? courseImage,
      final String? route}) = _$_courseCardBase;

  factory _courseCardBase.fromJson(Map<String, dynamic> json) =
      _$_courseCardBase.fromJson;

  @override
  String? get courseName;
  @override //课程卡片
  String? get courseImage;
  @override //课程图标
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_courseCardBaseCopyWith<_$_courseCardBase> get copyWith =>
      throw _privateConstructorUsedError;
}

waitActivateCardInfoData _$waitActivateCardInfoDataFromJson(
    Map<String, dynamic> json) {
  return _waitActivateCardInfoData.fromJson(json);
}

/// @nodoc
mixin _$waitActivateCardInfoData {
  String? get attachedCourseData => throw _privateConstructorUsedError; //附属课程信息
  int? get status => throw _privateConstructorUsedError; //0:不可激活，1:可激活
  int? get hasStarted =>
      throw _privateConstructorUsedError; //最近可加入班期是否已经开始， 0：未开始，1：已开始
  String? get startedText =>
      throw _privateConstructorUsedError; //最近可加入班期已开始时的提示文案
  String? get route => throw _privateConstructorUsedError; //激活路由
  String? get nonActivePrompt => throw _privateConstructorUsedError; //不可激活状态提示语
  double? get latestClassTime => throw _privateConstructorUsedError; //最新班次时间
  double? get activeNotifyTime => throw _privateConstructorUsedError; //提醒激活时间
// TMLessonNotifiTimeInfo *> *notifyTimeInfo
  String? get btnText => throw _privateConstructorUsedError; //按钮文案（去激活）
  String? get activeNotifyModifyTip => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $waitActivateCardInfoDataCopyWith<waitActivateCardInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $waitActivateCardInfoDataCopyWith<$Res> {
  factory $waitActivateCardInfoDataCopyWith(waitActivateCardInfoData value,
          $Res Function(waitActivateCardInfoData) then) =
      _$waitActivateCardInfoDataCopyWithImpl<$Res, waitActivateCardInfoData>;
  @useResult
  $Res call(
      {String? attachedCourseData,
      int? status,
      int? hasStarted,
      String? startedText,
      String? route,
      String? nonActivePrompt,
      double? latestClassTime,
      double? activeNotifyTime,
      String? btnText,
      String? activeNotifyModifyTip});
}

/// @nodoc
class _$waitActivateCardInfoDataCopyWithImpl<$Res,
        $Val extends waitActivateCardInfoData>
    implements $waitActivateCardInfoDataCopyWith<$Res> {
  _$waitActivateCardInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attachedCourseData = freezed,
    Object? status = freezed,
    Object? hasStarted = freezed,
    Object? startedText = freezed,
    Object? route = freezed,
    Object? nonActivePrompt = freezed,
    Object? latestClassTime = freezed,
    Object? activeNotifyTime = freezed,
    Object? btnText = freezed,
    Object? activeNotifyModifyTip = freezed,
  }) {
    return _then(_value.copyWith(
      attachedCourseData: freezed == attachedCourseData
          ? _value.attachedCourseData
          : attachedCourseData // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      hasStarted: freezed == hasStarted
          ? _value.hasStarted
          : hasStarted // ignore: cast_nullable_to_non_nullable
              as int?,
      startedText: freezed == startedText
          ? _value.startedText
          : startedText // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      nonActivePrompt: freezed == nonActivePrompt
          ? _value.nonActivePrompt
          : nonActivePrompt // ignore: cast_nullable_to_non_nullable
              as String?,
      latestClassTime: freezed == latestClassTime
          ? _value.latestClassTime
          : latestClassTime // ignore: cast_nullable_to_non_nullable
              as double?,
      activeNotifyTime: freezed == activeNotifyTime
          ? _value.activeNotifyTime
          : activeNotifyTime // ignore: cast_nullable_to_non_nullable
              as double?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      activeNotifyModifyTip: freezed == activeNotifyModifyTip
          ? _value.activeNotifyModifyTip
          : activeNotifyModifyTip // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_waitActivateCardInfoDataCopyWith<$Res>
    implements $waitActivateCardInfoDataCopyWith<$Res> {
  factory _$$_waitActivateCardInfoDataCopyWith(
          _$_waitActivateCardInfoData value,
          $Res Function(_$_waitActivateCardInfoData) then) =
      __$$_waitActivateCardInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? attachedCourseData,
      int? status,
      int? hasStarted,
      String? startedText,
      String? route,
      String? nonActivePrompt,
      double? latestClassTime,
      double? activeNotifyTime,
      String? btnText,
      String? activeNotifyModifyTip});
}

/// @nodoc
class __$$_waitActivateCardInfoDataCopyWithImpl<$Res>
    extends _$waitActivateCardInfoDataCopyWithImpl<$Res,
        _$_waitActivateCardInfoData>
    implements _$$_waitActivateCardInfoDataCopyWith<$Res> {
  __$$_waitActivateCardInfoDataCopyWithImpl(_$_waitActivateCardInfoData _value,
      $Res Function(_$_waitActivateCardInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attachedCourseData = freezed,
    Object? status = freezed,
    Object? hasStarted = freezed,
    Object? startedText = freezed,
    Object? route = freezed,
    Object? nonActivePrompt = freezed,
    Object? latestClassTime = freezed,
    Object? activeNotifyTime = freezed,
    Object? btnText = freezed,
    Object? activeNotifyModifyTip = freezed,
  }) {
    return _then(_$_waitActivateCardInfoData(
      attachedCourseData: freezed == attachedCourseData
          ? _value.attachedCourseData
          : attachedCourseData // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      hasStarted: freezed == hasStarted
          ? _value.hasStarted
          : hasStarted // ignore: cast_nullable_to_non_nullable
              as int?,
      startedText: freezed == startedText
          ? _value.startedText
          : startedText // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      nonActivePrompt: freezed == nonActivePrompt
          ? _value.nonActivePrompt
          : nonActivePrompt // ignore: cast_nullable_to_non_nullable
              as String?,
      latestClassTime: freezed == latestClassTime
          ? _value.latestClassTime
          : latestClassTime // ignore: cast_nullable_to_non_nullable
              as double?,
      activeNotifyTime: freezed == activeNotifyTime
          ? _value.activeNotifyTime
          : activeNotifyTime // ignore: cast_nullable_to_non_nullable
              as double?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      activeNotifyModifyTip: freezed == activeNotifyModifyTip
          ? _value.activeNotifyModifyTip
          : activeNotifyModifyTip // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_waitActivateCardInfoData implements _waitActivateCardInfoData {
  const _$_waitActivateCardInfoData(
      {this.attachedCourseData,
      this.status,
      this.hasStarted,
      this.startedText,
      this.route,
      this.nonActivePrompt,
      this.latestClassTime,
      this.activeNotifyTime,
      this.btnText,
      this.activeNotifyModifyTip});

  factory _$_waitActivateCardInfoData.fromJson(Map<String, dynamic> json) =>
      _$$_waitActivateCardInfoDataFromJson(json);

  @override
  final String? attachedCourseData;
//附属课程信息
  @override
  final int? status;
//0:不可激活，1:可激活
  @override
  final int? hasStarted;
//最近可加入班期是否已经开始， 0：未开始，1：已开始
  @override
  final String? startedText;
//最近可加入班期已开始时的提示文案
  @override
  final String? route;
//激活路由
  @override
  final String? nonActivePrompt;
//不可激活状态提示语
  @override
  final double? latestClassTime;
//最新班次时间
  @override
  final double? activeNotifyTime;
//提醒激活时间
// TMLessonNotifiTimeInfo *> *notifyTimeInfo
  @override
  final String? btnText;
//按钮文案（去激活）
  @override
  final String? activeNotifyModifyTip;

  @override
  String toString() {
    return 'waitActivateCardInfoData(attachedCourseData: $attachedCourseData, status: $status, hasStarted: $hasStarted, startedText: $startedText, route: $route, nonActivePrompt: $nonActivePrompt, latestClassTime: $latestClassTime, activeNotifyTime: $activeNotifyTime, btnText: $btnText, activeNotifyModifyTip: $activeNotifyModifyTip)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_waitActivateCardInfoData &&
            (identical(other.attachedCourseData, attachedCourseData) ||
                other.attachedCourseData == attachedCourseData) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.hasStarted, hasStarted) ||
                other.hasStarted == hasStarted) &&
            (identical(other.startedText, startedText) ||
                other.startedText == startedText) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.nonActivePrompt, nonActivePrompt) ||
                other.nonActivePrompt == nonActivePrompt) &&
            (identical(other.latestClassTime, latestClassTime) ||
                other.latestClassTime == latestClassTime) &&
            (identical(other.activeNotifyTime, activeNotifyTime) ||
                other.activeNotifyTime == activeNotifyTime) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.activeNotifyModifyTip, activeNotifyModifyTip) ||
                other.activeNotifyModifyTip == activeNotifyModifyTip));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      attachedCourseData,
      status,
      hasStarted,
      startedText,
      route,
      nonActivePrompt,
      latestClassTime,
      activeNotifyTime,
      btnText,
      activeNotifyModifyTip);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_waitActivateCardInfoDataCopyWith<_$_waitActivateCardInfoData>
      get copyWith => __$$_waitActivateCardInfoDataCopyWithImpl<
          _$_waitActivateCardInfoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_waitActivateCardInfoDataToJson(
      this,
    );
  }
}

abstract class _waitActivateCardInfoData implements waitActivateCardInfoData {
  const factory _waitActivateCardInfoData(
      {final String? attachedCourseData,
      final int? status,
      final int? hasStarted,
      final String? startedText,
      final String? route,
      final String? nonActivePrompt,
      final double? latestClassTime,
      final double? activeNotifyTime,
      final String? btnText,
      final String? activeNotifyModifyTip}) = _$_waitActivateCardInfoData;

  factory _waitActivateCardInfoData.fromJson(Map<String, dynamic> json) =
      _$_waitActivateCardInfoData.fromJson;

  @override
  String? get attachedCourseData;
  @override //附属课程信息
  int? get status;
  @override //0:不可激活，1:可激活
  int? get hasStarted;
  @override //最近可加入班期是否已经开始， 0：未开始，1：已开始
  String? get startedText;
  @override //最近可加入班期已开始时的提示文案
  String? get route;
  @override //激活路由
  String? get nonActivePrompt;
  @override //不可激活状态提示语
  double? get latestClassTime;
  @override //最新班次时间
  double? get activeNotifyTime;
  @override //提醒激活时间
// TMLessonNotifiTimeInfo *> *notifyTimeInfo
  String? get btnText;
  @override //按钮文案（去激活）
  String? get activeNotifyModifyTip;
  @override
  @JsonKey(ignore: true)
  _$$_waitActivateCardInfoDataCopyWith<_$_waitActivateCardInfoData>
      get copyWith => throw _privateConstructorUsedError;
}

courseServiceData _$courseServiceDataFromJson(Map<String, dynamic> json) {
  return _courseServiceData.fromJson(json);
}

/// @nodoc
mixin _$courseServiceData {
  String? get configKey => throw _privateConstructorUsedError; //服务配置key
  String? get configName => throw _privateConstructorUsedError; //服务配置名称
  String? get icon => throw _privateConstructorUsedError; //服务对应icon
  String? get redirectUrl => throw _privateConstructorUsedError; //服务对应跳转链接
  int? get type =>
      throw _privateConstructorUsedError; //4//添加指导师，10//萌萌森林//11学前必做，评测 //12预激活信息
  String? get title => throw _privateConstructorUsedError; //服务标题
  String? get serviceNewMsgTipText =>
      throw _privateConstructorUsedError; //服务补充说明
  String? get activeIcon => throw _privateConstructorUsedError; //服务对应动态icon
  String? get subjectImage => throw _privateConstructorUsedError; //服务补充说明
  bool? get isShowActiveIcon => throw _privateConstructorUsedError; //是否展示动态icon
  String? get encourageIcon => throw _privateConstructorUsedError; //服务补充说明
  int? get serviceUpdateStatus =>
      throw _privateConstructorUsedError; //服务更新状态(0: 无更新，1: 有更新)
  int? get redDot => throw _privateConstructorUsedError; //是否需要显示红点
  int? get hasMakeupLesson =>
      throw _privateConstructorUsedError; //有无补学课时，0：无补学课时 1：有补学课时
// TMLessonServiceSuffixContentModel? serviceSuffixContent, //服务后置内容
  List<String>? get lessonCoverImageList =>
      throw _privateConstructorUsedError; //补读，回看的封面图
  List<String>? get serviceUpdateBubbleList =>
      throw _privateConstructorUsedError; //服务更新气泡列表
// NSMutableDictionary? serviceUpdateBubbleStatusDic, //服务更新气泡状态列表（用于记录是否埋点过）
  todayPlanResource? get guideResource =>
      throw _privateConstructorUsedError; //动画资源
  int? get activityStatus =>
      throw _privateConstructorUsedError; //活动完成状态 0 未接受 1已接受
  String? get challengeActivityGuideAudio =>
      throw _privateConstructorUsedError; //5日活动引导语音
  ///**************** 新版训练营 ****************///
  String? get padIcon => throw _privateConstructorUsedError; //服务对应icon - Pad
  String? get button => throw _privateConstructorUsedError; //按钮文字
  String? get businessTypeDesc =>
      throw _privateConstructorUsedError; //业务类型，埋点使用
  String? get desc => throw _privateConstructorUsedError; //描述文案
// bool? needUpdateData, //需要刷新数据 - 本地使用
  int? get plantUsedDrips => throw _privateConstructorUsedError; //植物使用水滴数
  int? get plantNeedDrips => throw _privateConstructorUsedError; //植物需要水滴数
  int? get plantWatered =>
      throw _privateConstructorUsedError; //是否应该浇水 0:不应该浇水 1:应该浇水
  String? get expandAudio => throw _privateConstructorUsedError; //扩展音效 - 高亮音效
  String? get expandEffect => throw _privateConstructorUsedError; //扩展动效 - 去浇水动效
  String? get toastTips => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $courseServiceDataCopyWith<courseServiceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $courseServiceDataCopyWith<$Res> {
  factory $courseServiceDataCopyWith(
          courseServiceData value, $Res Function(courseServiceData) then) =
      _$courseServiceDataCopyWithImpl<$Res, courseServiceData>;
  @useResult
  $Res call(
      {String? configKey,
      String? configName,
      String? icon,
      String? redirectUrl,
      int? type,
      String? title,
      String? serviceNewMsgTipText,
      String? activeIcon,
      String? subjectImage,
      bool? isShowActiveIcon,
      String? encourageIcon,
      int? serviceUpdateStatus,
      int? redDot,
      int? hasMakeupLesson,
      List<String>? lessonCoverImageList,
      List<String>? serviceUpdateBubbleList,
      todayPlanResource? guideResource,
      int? activityStatus,
      String? challengeActivityGuideAudio,
      String? padIcon,
      String? button,
      String? businessTypeDesc,
      String? desc,
      int? plantUsedDrips,
      int? plantNeedDrips,
      int? plantWatered,
      String? expandAudio,
      String? expandEffect,
      String? toastTips});

  $todayPlanResourceCopyWith<$Res>? get guideResource;
}

/// @nodoc
class _$courseServiceDataCopyWithImpl<$Res, $Val extends courseServiceData>
    implements $courseServiceDataCopyWith<$Res> {
  _$courseServiceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? configKey = freezed,
    Object? configName = freezed,
    Object? icon = freezed,
    Object? redirectUrl = freezed,
    Object? type = freezed,
    Object? title = freezed,
    Object? serviceNewMsgTipText = freezed,
    Object? activeIcon = freezed,
    Object? subjectImage = freezed,
    Object? isShowActiveIcon = freezed,
    Object? encourageIcon = freezed,
    Object? serviceUpdateStatus = freezed,
    Object? redDot = freezed,
    Object? hasMakeupLesson = freezed,
    Object? lessonCoverImageList = freezed,
    Object? serviceUpdateBubbleList = freezed,
    Object? guideResource = freezed,
    Object? activityStatus = freezed,
    Object? challengeActivityGuideAudio = freezed,
    Object? padIcon = freezed,
    Object? button = freezed,
    Object? businessTypeDesc = freezed,
    Object? desc = freezed,
    Object? plantUsedDrips = freezed,
    Object? plantNeedDrips = freezed,
    Object? plantWatered = freezed,
    Object? expandAudio = freezed,
    Object? expandEffect = freezed,
    Object? toastTips = freezed,
  }) {
    return _then(_value.copyWith(
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceNewMsgTipText: freezed == serviceNewMsgTipText
          ? _value.serviceNewMsgTipText
          : serviceNewMsgTipText // ignore: cast_nullable_to_non_nullable
              as String?,
      activeIcon: freezed == activeIcon
          ? _value.activeIcon
          : activeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectImage: freezed == subjectImage
          ? _value.subjectImage
          : subjectImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isShowActiveIcon: freezed == isShowActiveIcon
          ? _value.isShowActiveIcon
          : isShowActiveIcon // ignore: cast_nullable_to_non_nullable
              as bool?,
      encourageIcon: freezed == encourageIcon
          ? _value.encourageIcon
          : encourageIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceUpdateStatus: freezed == serviceUpdateStatus
          ? _value.serviceUpdateStatus
          : serviceUpdateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      redDot: freezed == redDot
          ? _value.redDot
          : redDot // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMakeupLesson: freezed == hasMakeupLesson
          ? _value.hasMakeupLesson
          : hasMakeupLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonCoverImageList: freezed == lessonCoverImageList
          ? _value.lessonCoverImageList
          : lessonCoverImageList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      serviceUpdateBubbleList: freezed == serviceUpdateBubbleList
          ? _value.serviceUpdateBubbleList
          : serviceUpdateBubbleList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      guideResource: freezed == guideResource
          ? _value.guideResource
          : guideResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResource?,
      activityStatus: freezed == activityStatus
          ? _value.activityStatus
          : activityStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      challengeActivityGuideAudio: freezed == challengeActivityGuideAudio
          ? _value.challengeActivityGuideAudio
          : challengeActivityGuideAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      padIcon: freezed == padIcon
          ? _value.padIcon
          : padIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      plantUsedDrips: freezed == plantUsedDrips
          ? _value.plantUsedDrips
          : plantUsedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantNeedDrips: freezed == plantNeedDrips
          ? _value.plantNeedDrips
          : plantNeedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantWatered: freezed == plantWatered
          ? _value.plantWatered
          : plantWatered // ignore: cast_nullable_to_non_nullable
              as int?,
      expandAudio: freezed == expandAudio
          ? _value.expandAudio
          : expandAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      expandEffect: freezed == expandEffect
          ? _value.expandEffect
          : expandEffect // ignore: cast_nullable_to_non_nullable
              as String?,
      toastTips: freezed == toastTips
          ? _value.toastTips
          : toastTips // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourceCopyWith<$Res>? get guideResource {
    if (_value.guideResource == null) {
      return null;
    }

    return $todayPlanResourceCopyWith<$Res>(_value.guideResource!, (value) {
      return _then(_value.copyWith(guideResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_courseServiceDataCopyWith<$Res>
    implements $courseServiceDataCopyWith<$Res> {
  factory _$$_courseServiceDataCopyWith(_$_courseServiceData value,
          $Res Function(_$_courseServiceData) then) =
      __$$_courseServiceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? configKey,
      String? configName,
      String? icon,
      String? redirectUrl,
      int? type,
      String? title,
      String? serviceNewMsgTipText,
      String? activeIcon,
      String? subjectImage,
      bool? isShowActiveIcon,
      String? encourageIcon,
      int? serviceUpdateStatus,
      int? redDot,
      int? hasMakeupLesson,
      List<String>? lessonCoverImageList,
      List<String>? serviceUpdateBubbleList,
      todayPlanResource? guideResource,
      int? activityStatus,
      String? challengeActivityGuideAudio,
      String? padIcon,
      String? button,
      String? businessTypeDesc,
      String? desc,
      int? plantUsedDrips,
      int? plantNeedDrips,
      int? plantWatered,
      String? expandAudio,
      String? expandEffect,
      String? toastTips});

  @override
  $todayPlanResourceCopyWith<$Res>? get guideResource;
}

/// @nodoc
class __$$_courseServiceDataCopyWithImpl<$Res>
    extends _$courseServiceDataCopyWithImpl<$Res, _$_courseServiceData>
    implements _$$_courseServiceDataCopyWith<$Res> {
  __$$_courseServiceDataCopyWithImpl(
      _$_courseServiceData _value, $Res Function(_$_courseServiceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? configKey = freezed,
    Object? configName = freezed,
    Object? icon = freezed,
    Object? redirectUrl = freezed,
    Object? type = freezed,
    Object? title = freezed,
    Object? serviceNewMsgTipText = freezed,
    Object? activeIcon = freezed,
    Object? subjectImage = freezed,
    Object? isShowActiveIcon = freezed,
    Object? encourageIcon = freezed,
    Object? serviceUpdateStatus = freezed,
    Object? redDot = freezed,
    Object? hasMakeupLesson = freezed,
    Object? lessonCoverImageList = freezed,
    Object? serviceUpdateBubbleList = freezed,
    Object? guideResource = freezed,
    Object? activityStatus = freezed,
    Object? challengeActivityGuideAudio = freezed,
    Object? padIcon = freezed,
    Object? button = freezed,
    Object? businessTypeDesc = freezed,
    Object? desc = freezed,
    Object? plantUsedDrips = freezed,
    Object? plantNeedDrips = freezed,
    Object? plantWatered = freezed,
    Object? expandAudio = freezed,
    Object? expandEffect = freezed,
    Object? toastTips = freezed,
  }) {
    return _then(_$_courseServiceData(
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceNewMsgTipText: freezed == serviceNewMsgTipText
          ? _value.serviceNewMsgTipText
          : serviceNewMsgTipText // ignore: cast_nullable_to_non_nullable
              as String?,
      activeIcon: freezed == activeIcon
          ? _value.activeIcon
          : activeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectImage: freezed == subjectImage
          ? _value.subjectImage
          : subjectImage // ignore: cast_nullable_to_non_nullable
              as String?,
      isShowActiveIcon: freezed == isShowActiveIcon
          ? _value.isShowActiveIcon
          : isShowActiveIcon // ignore: cast_nullable_to_non_nullable
              as bool?,
      encourageIcon: freezed == encourageIcon
          ? _value.encourageIcon
          : encourageIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceUpdateStatus: freezed == serviceUpdateStatus
          ? _value.serviceUpdateStatus
          : serviceUpdateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      redDot: freezed == redDot
          ? _value.redDot
          : redDot // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMakeupLesson: freezed == hasMakeupLesson
          ? _value.hasMakeupLesson
          : hasMakeupLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonCoverImageList: freezed == lessonCoverImageList
          ? _value._lessonCoverImageList
          : lessonCoverImageList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      serviceUpdateBubbleList: freezed == serviceUpdateBubbleList
          ? _value._serviceUpdateBubbleList
          : serviceUpdateBubbleList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      guideResource: freezed == guideResource
          ? _value.guideResource
          : guideResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResource?,
      activityStatus: freezed == activityStatus
          ? _value.activityStatus
          : activityStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      challengeActivityGuideAudio: freezed == challengeActivityGuideAudio
          ? _value.challengeActivityGuideAudio
          : challengeActivityGuideAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      padIcon: freezed == padIcon
          ? _value.padIcon
          : padIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      plantUsedDrips: freezed == plantUsedDrips
          ? _value.plantUsedDrips
          : plantUsedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantNeedDrips: freezed == plantNeedDrips
          ? _value.plantNeedDrips
          : plantNeedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantWatered: freezed == plantWatered
          ? _value.plantWatered
          : plantWatered // ignore: cast_nullable_to_non_nullable
              as int?,
      expandAudio: freezed == expandAudio
          ? _value.expandAudio
          : expandAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      expandEffect: freezed == expandEffect
          ? _value.expandEffect
          : expandEffect // ignore: cast_nullable_to_non_nullable
              as String?,
      toastTips: freezed == toastTips
          ? _value.toastTips
          : toastTips // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_courseServiceData implements _courseServiceData {
  const _$_courseServiceData(
      {this.configKey,
      this.configName,
      this.icon,
      this.redirectUrl,
      this.type,
      this.title,
      this.serviceNewMsgTipText,
      this.activeIcon,
      this.subjectImage,
      this.isShowActiveIcon,
      this.encourageIcon,
      this.serviceUpdateStatus,
      this.redDot,
      this.hasMakeupLesson,
      final List<String>? lessonCoverImageList,
      final List<String>? serviceUpdateBubbleList,
      this.guideResource,
      this.activityStatus,
      this.challengeActivityGuideAudio,
      this.padIcon,
      this.button,
      this.businessTypeDesc,
      this.desc,
      this.plantUsedDrips,
      this.plantNeedDrips,
      this.plantWatered,
      this.expandAudio,
      this.expandEffect,
      this.toastTips})
      : _lessonCoverImageList = lessonCoverImageList,
        _serviceUpdateBubbleList = serviceUpdateBubbleList;

  factory _$_courseServiceData.fromJson(Map<String, dynamic> json) =>
      _$$_courseServiceDataFromJson(json);

  @override
  final String? configKey;
//服务配置key
  @override
  final String? configName;
//服务配置名称
  @override
  final String? icon;
//服务对应icon
  @override
  final String? redirectUrl;
//服务对应跳转链接
  @override
  final int? type;
//4//添加指导师，10//萌萌森林//11学前必做，评测 //12预激活信息
  @override
  final String? title;
//服务标题
  @override
  final String? serviceNewMsgTipText;
//服务补充说明
  @override
  final String? activeIcon;
//服务对应动态icon
  @override
  final String? subjectImage;
//服务补充说明
  @override
  final bool? isShowActiveIcon;
//是否展示动态icon
  @override
  final String? encourageIcon;
//服务补充说明
  @override
  final int? serviceUpdateStatus;
//服务更新状态(0: 无更新，1: 有更新)
  @override
  final int? redDot;
//是否需要显示红点
  @override
  final int? hasMakeupLesson;
//有无补学课时，0：无补学课时 1：有补学课时
// TMLessonServiceSuffixContentModel? serviceSuffixContent, //服务后置内容
  final List<String>? _lessonCoverImageList;
//有无补学课时，0：无补学课时 1：有补学课时
// TMLessonServiceSuffixContentModel? serviceSuffixContent, //服务后置内容
  @override
  List<String>? get lessonCoverImageList {
    final value = _lessonCoverImageList;
    if (value == null) return null;
    if (_lessonCoverImageList is EqualUnmodifiableListView)
      return _lessonCoverImageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//补读，回看的封面图
  final List<String>? _serviceUpdateBubbleList;
//补读，回看的封面图
  @override
  List<String>? get serviceUpdateBubbleList {
    final value = _serviceUpdateBubbleList;
    if (value == null) return null;
    if (_serviceUpdateBubbleList is EqualUnmodifiableListView)
      return _serviceUpdateBubbleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//服务更新气泡列表
// NSMutableDictionary? serviceUpdateBubbleStatusDic, //服务更新气泡状态列表（用于记录是否埋点过）
  @override
  final todayPlanResource? guideResource;
//动画资源
  @override
  final int? activityStatus;
//活动完成状态 0 未接受 1已接受
  @override
  final String? challengeActivityGuideAudio;
//5日活动引导语音
  ///**************** 新版训练营 ****************///
  @override
  final String? padIcon;
//服务对应icon - Pad
  @override
  final String? button;
//按钮文字
  @override
  final String? businessTypeDesc;
//业务类型，埋点使用
  @override
  final String? desc;
//描述文案
// bool? needUpdateData, //需要刷新数据 - 本地使用
  @override
  final int? plantUsedDrips;
//植物使用水滴数
  @override
  final int? plantNeedDrips;
//植物需要水滴数
  @override
  final int? plantWatered;
//是否应该浇水 0:不应该浇水 1:应该浇水
  @override
  final String? expandAudio;
//扩展音效 - 高亮音效
  @override
  final String? expandEffect;
//扩展动效 - 去浇水动效
  @override
  final String? toastTips;

  @override
  String toString() {
    return 'courseServiceData(configKey: $configKey, configName: $configName, icon: $icon, redirectUrl: $redirectUrl, type: $type, title: $title, serviceNewMsgTipText: $serviceNewMsgTipText, activeIcon: $activeIcon, subjectImage: $subjectImage, isShowActiveIcon: $isShowActiveIcon, encourageIcon: $encourageIcon, serviceUpdateStatus: $serviceUpdateStatus, redDot: $redDot, hasMakeupLesson: $hasMakeupLesson, lessonCoverImageList: $lessonCoverImageList, serviceUpdateBubbleList: $serviceUpdateBubbleList, guideResource: $guideResource, activityStatus: $activityStatus, challengeActivityGuideAudio: $challengeActivityGuideAudio, padIcon: $padIcon, button: $button, businessTypeDesc: $businessTypeDesc, desc: $desc, plantUsedDrips: $plantUsedDrips, plantNeedDrips: $plantNeedDrips, plantWatered: $plantWatered, expandAudio: $expandAudio, expandEffect: $expandEffect, toastTips: $toastTips)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_courseServiceData &&
            (identical(other.configKey, configKey) ||
                other.configKey == configKey) &&
            (identical(other.configName, configName) ||
                other.configName == configName) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.serviceNewMsgTipText, serviceNewMsgTipText) ||
                other.serviceNewMsgTipText == serviceNewMsgTipText) &&
            (identical(other.activeIcon, activeIcon) ||
                other.activeIcon == activeIcon) &&
            (identical(other.subjectImage, subjectImage) ||
                other.subjectImage == subjectImage) &&
            (identical(other.isShowActiveIcon, isShowActiveIcon) ||
                other.isShowActiveIcon == isShowActiveIcon) &&
            (identical(other.encourageIcon, encourageIcon) ||
                other.encourageIcon == encourageIcon) &&
            (identical(other.serviceUpdateStatus, serviceUpdateStatus) ||
                other.serviceUpdateStatus == serviceUpdateStatus) &&
            (identical(other.redDot, redDot) || other.redDot == redDot) &&
            (identical(other.hasMakeupLesson, hasMakeupLesson) ||
                other.hasMakeupLesson == hasMakeupLesson) &&
            const DeepCollectionEquality()
                .equals(other._lessonCoverImageList, _lessonCoverImageList) &&
            const DeepCollectionEquality().equals(
                other._serviceUpdateBubbleList, _serviceUpdateBubbleList) &&
            (identical(other.guideResource, guideResource) ||
                other.guideResource == guideResource) &&
            (identical(other.activityStatus, activityStatus) ||
                other.activityStatus == activityStatus) &&
            (identical(other.challengeActivityGuideAudio,
                    challengeActivityGuideAudio) ||
                other.challengeActivityGuideAudio ==
                    challengeActivityGuideAudio) &&
            (identical(other.padIcon, padIcon) || other.padIcon == padIcon) &&
            (identical(other.button, button) || other.button == button) &&
            (identical(other.businessTypeDesc, businessTypeDesc) ||
                other.businessTypeDesc == businessTypeDesc) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.plantUsedDrips, plantUsedDrips) ||
                other.plantUsedDrips == plantUsedDrips) &&
            (identical(other.plantNeedDrips, plantNeedDrips) ||
                other.plantNeedDrips == plantNeedDrips) &&
            (identical(other.plantWatered, plantWatered) ||
                other.plantWatered == plantWatered) &&
            (identical(other.expandAudio, expandAudio) ||
                other.expandAudio == expandAudio) &&
            (identical(other.expandEffect, expandEffect) ||
                other.expandEffect == expandEffect) &&
            (identical(other.toastTips, toastTips) ||
                other.toastTips == toastTips));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        configKey,
        configName,
        icon,
        redirectUrl,
        type,
        title,
        serviceNewMsgTipText,
        activeIcon,
        subjectImage,
        isShowActiveIcon,
        encourageIcon,
        serviceUpdateStatus,
        redDot,
        hasMakeupLesson,
        const DeepCollectionEquality().hash(_lessonCoverImageList),
        const DeepCollectionEquality().hash(_serviceUpdateBubbleList),
        guideResource,
        activityStatus,
        challengeActivityGuideAudio,
        padIcon,
        button,
        businessTypeDesc,
        desc,
        plantUsedDrips,
        plantNeedDrips,
        plantWatered,
        expandAudio,
        expandEffect,
        toastTips
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_courseServiceDataCopyWith<_$_courseServiceData> get copyWith =>
      __$$_courseServiceDataCopyWithImpl<_$_courseServiceData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_courseServiceDataToJson(
      this,
    );
  }
}

abstract class _courseServiceData implements courseServiceData {
  const factory _courseServiceData(
      {final String? configKey,
      final String? configName,
      final String? icon,
      final String? redirectUrl,
      final int? type,
      final String? title,
      final String? serviceNewMsgTipText,
      final String? activeIcon,
      final String? subjectImage,
      final bool? isShowActiveIcon,
      final String? encourageIcon,
      final int? serviceUpdateStatus,
      final int? redDot,
      final int? hasMakeupLesson,
      final List<String>? lessonCoverImageList,
      final List<String>? serviceUpdateBubbleList,
      final todayPlanResource? guideResource,
      final int? activityStatus,
      final String? challengeActivityGuideAudio,
      final String? padIcon,
      final String? button,
      final String? businessTypeDesc,
      final String? desc,
      final int? plantUsedDrips,
      final int? plantNeedDrips,
      final int? plantWatered,
      final String? expandAudio,
      final String? expandEffect,
      final String? toastTips}) = _$_courseServiceData;

  factory _courseServiceData.fromJson(Map<String, dynamic> json) =
      _$_courseServiceData.fromJson;

  @override
  String? get configKey;
  @override //服务配置key
  String? get configName;
  @override //服务配置名称
  String? get icon;
  @override //服务对应icon
  String? get redirectUrl;
  @override //服务对应跳转链接
  int? get type;
  @override //4//添加指导师，10//萌萌森林//11学前必做，评测 //12预激活信息
  String? get title;
  @override //服务标题
  String? get serviceNewMsgTipText;
  @override //服务补充说明
  String? get activeIcon;
  @override //服务对应动态icon
  String? get subjectImage;
  @override //服务补充说明
  bool? get isShowActiveIcon;
  @override //是否展示动态icon
  String? get encourageIcon;
  @override //服务补充说明
  int? get serviceUpdateStatus;
  @override //服务更新状态(0: 无更新，1: 有更新)
  int? get redDot;
  @override //是否需要显示红点
  int? get hasMakeupLesson;
  @override //有无补学课时，0：无补学课时 1：有补学课时
// TMLessonServiceSuffixContentModel? serviceSuffixContent, //服务后置内容
  List<String>? get lessonCoverImageList;
  @override //补读，回看的封面图
  List<String>? get serviceUpdateBubbleList;
  @override //服务更新气泡列表
// NSMutableDictionary? serviceUpdateBubbleStatusDic, //服务更新气泡状态列表（用于记录是否埋点过）
  todayPlanResource? get guideResource;
  @override //动画资源
  int? get activityStatus;
  @override //活动完成状态 0 未接受 1已接受
  String? get challengeActivityGuideAudio;
  @override //5日活动引导语音
  ///**************** 新版训练营 ****************///
  String? get padIcon;
  @override //服务对应icon - Pad
  String? get button;
  @override //按钮文字
  String? get businessTypeDesc;
  @override //业务类型，埋点使用
  String? get desc;
  @override //描述文案
// bool? needUpdateData, //需要刷新数据 - 本地使用
  int? get plantUsedDrips;
  @override //植物使用水滴数
  int? get plantNeedDrips;
  @override //植物需要水滴数
  int? get plantWatered;
  @override //是否应该浇水 0:不应该浇水 1:应该浇水
  String? get expandAudio;
  @override //扩展音效 - 高亮音效
  String? get expandEffect;
  @override //扩展动效 - 去浇水动效
  String? get toastTips;
  @override
  @JsonKey(ignore: true)
  _$$_courseServiceDataCopyWith<_$_courseServiceData> get copyWith =>
      throw _privateConstructorUsedError;
}

extendData _$extendDataFromJson(Map<String, dynamic> json) {
  return _extendData.fromJson(json);
}

/// @nodoc
mixin _$extendData {
  int? get haveCourseStatus =>
      throw _privateConstructorUsedError; //是否有课程 1: 有年课，训练营， 2：无年课，训练营
  List<contactCustomer>? get contactCustomerContentList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $extendDataCopyWith<extendData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $extendDataCopyWith<$Res> {
  factory $extendDataCopyWith(
          extendData value, $Res Function(extendData) then) =
      _$extendDataCopyWithImpl<$Res, extendData>;
  @useResult
  $Res call(
      {int? haveCourseStatus,
      List<contactCustomer>? contactCustomerContentList});
}

/// @nodoc
class _$extendDataCopyWithImpl<$Res, $Val extends extendData>
    implements $extendDataCopyWith<$Res> {
  _$extendDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? haveCourseStatus = freezed,
    Object? contactCustomerContentList = freezed,
  }) {
    return _then(_value.copyWith(
      haveCourseStatus: freezed == haveCourseStatus
          ? _value.haveCourseStatus
          : haveCourseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value.contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<contactCustomer>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_extendDataCopyWith<$Res>
    implements $extendDataCopyWith<$Res> {
  factory _$$_extendDataCopyWith(
          _$_extendData value, $Res Function(_$_extendData) then) =
      __$$_extendDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? haveCourseStatus,
      List<contactCustomer>? contactCustomerContentList});
}

/// @nodoc
class __$$_extendDataCopyWithImpl<$Res>
    extends _$extendDataCopyWithImpl<$Res, _$_extendData>
    implements _$$_extendDataCopyWith<$Res> {
  __$$_extendDataCopyWithImpl(
      _$_extendData _value, $Res Function(_$_extendData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? haveCourseStatus = freezed,
    Object? contactCustomerContentList = freezed,
  }) {
    return _then(_$_extendData(
      haveCourseStatus: freezed == haveCourseStatus
          ? _value.haveCourseStatus
          : haveCourseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value._contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<contactCustomer>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_extendData implements _extendData {
  const _$_extendData(
      {this.haveCourseStatus,
      final List<contactCustomer>? contactCustomerContentList})
      : _contactCustomerContentList = contactCustomerContentList;

  factory _$_extendData.fromJson(Map<String, dynamic> json) =>
      _$$_extendDataFromJson(json);

  @override
  final int? haveCourseStatus;
//是否有课程 1: 有年课，训练营， 2：无年课，训练营
  final List<contactCustomer>? _contactCustomerContentList;
//是否有课程 1: 有年课，训练营， 2：无年课，训练营
  @override
  List<contactCustomer>? get contactCustomerContentList {
    final value = _contactCustomerContentList;
    if (value == null) return null;
    if (_contactCustomerContentList is EqualUnmodifiableListView)
      return _contactCustomerContentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'extendData(haveCourseStatus: $haveCourseStatus, contactCustomerContentList: $contactCustomerContentList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_extendData &&
            (identical(other.haveCourseStatus, haveCourseStatus) ||
                other.haveCourseStatus == haveCourseStatus) &&
            const DeepCollectionEquality().equals(
                other._contactCustomerContentList,
                _contactCustomerContentList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, haveCourseStatus,
      const DeepCollectionEquality().hash(_contactCustomerContentList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_extendDataCopyWith<_$_extendData> get copyWith =>
      __$$_extendDataCopyWithImpl<_$_extendData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_extendDataToJson(
      this,
    );
  }
}

abstract class _extendData implements extendData {
  const factory _extendData(
      {final int? haveCourseStatus,
      final List<contactCustomer>? contactCustomerContentList}) = _$_extendData;

  factory _extendData.fromJson(Map<String, dynamic> json) =
      _$_extendData.fromJson;

  @override
  int? get haveCourseStatus;
  @override //是否有课程 1: 有年课，训练营， 2：无年课，训练营
  List<contactCustomer>? get contactCustomerContentList;
  @override
  @JsonKey(ignore: true)
  _$$_extendDataCopyWith<_$_extendData> get copyWith =>
      throw _privateConstructorUsedError;
}

contactCustomer _$contactCustomerFromJson(Map<String, dynamic> json) {
  return _contactCustomer.fromJson(json);
}

/// @nodoc
mixin _$contactCustomer {
  String? get color => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $contactCustomerCopyWith<contactCustomer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $contactCustomerCopyWith<$Res> {
  factory $contactCustomerCopyWith(
          contactCustomer value, $Res Function(contactCustomer) then) =
      _$contactCustomerCopyWithImpl<$Res, contactCustomer>;
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class _$contactCustomerCopyWithImpl<$Res, $Val extends contactCustomer>
    implements $contactCustomerCopyWith<$Res> {
  _$contactCustomerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_value.copyWith(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_contactCustomerCopyWith<$Res>
    implements $contactCustomerCopyWith<$Res> {
  factory _$$_contactCustomerCopyWith(
          _$_contactCustomer value, $Res Function(_$_contactCustomer) then) =
      __$$_contactCustomerCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class __$$_contactCustomerCopyWithImpl<$Res>
    extends _$contactCustomerCopyWithImpl<$Res, _$_contactCustomer>
    implements _$$_contactCustomerCopyWith<$Res> {
  __$$_contactCustomerCopyWithImpl(
      _$_contactCustomer _value, $Res Function(_$_contactCustomer) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_$_contactCustomer(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_contactCustomer implements _contactCustomer {
  const _$_contactCustomer({this.color, this.text, this.linkUrl});

  factory _$_contactCustomer.fromJson(Map<String, dynamic> json) =>
      _$$_contactCustomerFromJson(json);

  @override
  final String? color;
  @override
  final String? text;
  @override
  final String? linkUrl;

  @override
  String toString() {
    return 'contactCustomer(color: $color, text: $text, linkUrl: $linkUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_contactCustomer &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, color, text, linkUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_contactCustomerCopyWith<_$_contactCustomer> get copyWith =>
      __$$_contactCustomerCopyWithImpl<_$_contactCustomer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_contactCustomerToJson(
      this,
    );
  }
}

abstract class _contactCustomer implements contactCustomer {
  const factory _contactCustomer(
      {final String? color,
      final String? text,
      final String? linkUrl}) = _$_contactCustomer;

  factory _contactCustomer.fromJson(Map<String, dynamic> json) =
      _$_contactCustomer.fromJson;

  @override
  String? get color;
  @override
  String? get text;
  @override
  String? get linkUrl;
  @override
  @JsonKey(ignore: true)
  _$$_contactCustomerCopyWith<_$_contactCustomer> get copyWith =>
      throw _privateConstructorUsedError;
}

lessonApplyCardInfo _$lessonApplyCardInfoFromJson(Map<String, dynamic> json) {
  return _lessonApplyCardInfo.fromJson(json);
}

/// @nodoc
mixin _$lessonApplyCardInfo {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError; //领课卡片图
  String? get applyCourseUrl => throw _privateConstructorUsedError; //领课链接
  String? get videoName => throw _privateConstructorUsedError; //视频名称
  String? get videoUrl => throw _privateConstructorUsedError; //视频链接url
  String? get videoPictureUrl => throw _privateConstructorUsedError; //视频封面
  String? get videoDesc => throw _privateConstructorUsedError; // 视频描述
  String? get videoButtonText => throw _privateConstructorUsedError; //视频按钮文案
  String? get bgColor => throw _privateConstructorUsedError; //背景颜色
  String? get cardMainColor => throw _privateConstructorUsedError; //主色调
  String? get labelFontColor => throw _privateConstructorUsedError; //文本颜色
  String? get materialName => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get topLeftLabelFontColor => throw _privateConstructorUsedError;
  int? get materialId => throw _privateConstructorUsedError; //素材ID
  int? get recommendType =>
      throw _privateConstructorUsedError; //缺省推荐类型，0-无课推荐，2-全部课结束推荐
  SKURecommenInfo? get skuRecommendInfo =>
      throw _privateConstructorUsedError; //栏目信息
  LiveRecommenInfo? get liveRecommendInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $lessonApplyCardInfoCopyWith<lessonApplyCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $lessonApplyCardInfoCopyWith<$Res> {
  factory $lessonApplyCardInfoCopyWith(
          lessonApplyCardInfo value, $Res Function(lessonApplyCardInfo) then) =
      _$lessonApplyCardInfoCopyWithImpl<$Res, lessonApplyCardInfo>;
  @useResult
  $Res call(
      {int? subjectType,
      String? imageUrl,
      String? applyCourseUrl,
      String? videoName,
      String? videoUrl,
      String? videoPictureUrl,
      String? videoDesc,
      String? videoButtonText,
      String? bgColor,
      String? cardMainColor,
      String? labelFontColor,
      String? materialName,
      String? subjectTypeDesc,
      String? topLeftLabelFontColor,
      int? materialId,
      int? recommendType,
      SKURecommenInfo? skuRecommendInfo,
      LiveRecommenInfo? liveRecommendInfo});

  $SKURecommenInfoCopyWith<$Res>? get skuRecommendInfo;
  $LiveRecommenInfoCopyWith<$Res>? get liveRecommendInfo;
}

/// @nodoc
class _$lessonApplyCardInfoCopyWithImpl<$Res, $Val extends lessonApplyCardInfo>
    implements $lessonApplyCardInfoCopyWith<$Res> {
  _$lessonApplyCardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? imageUrl = freezed,
    Object? applyCourseUrl = freezed,
    Object? videoName = freezed,
    Object? videoUrl = freezed,
    Object? videoPictureUrl = freezed,
    Object? videoDesc = freezed,
    Object? videoButtonText = freezed,
    Object? bgColor = freezed,
    Object? cardMainColor = freezed,
    Object? labelFontColor = freezed,
    Object? materialName = freezed,
    Object? subjectTypeDesc = freezed,
    Object? topLeftLabelFontColor = freezed,
    Object? materialId = freezed,
    Object? recommendType = freezed,
    Object? skuRecommendInfo = freezed,
    Object? liveRecommendInfo = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      applyCourseUrl: freezed == applyCourseUrl
          ? _value.applyCourseUrl
          : applyCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoName: freezed == videoName
          ? _value.videoName
          : videoName // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoPictureUrl: freezed == videoPictureUrl
          ? _value.videoPictureUrl
          : videoPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoDesc: freezed == videoDesc
          ? _value.videoDesc
          : videoDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      videoButtonText: freezed == videoButtonText
          ? _value.videoButtonText
          : videoButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      cardMainColor: freezed == cardMainColor
          ? _value.cardMainColor
          : cardMainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      labelFontColor: freezed == labelFontColor
          ? _value.labelFontColor
          : labelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      topLeftLabelFontColor: freezed == topLeftLabelFontColor
          ? _value.topLeftLabelFontColor
          : topLeftLabelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      recommendType: freezed == recommendType
          ? _value.recommendType
          : recommendType // ignore: cast_nullable_to_non_nullable
              as int?,
      skuRecommendInfo: freezed == skuRecommendInfo
          ? _value.skuRecommendInfo
          : skuRecommendInfo // ignore: cast_nullable_to_non_nullable
              as SKURecommenInfo?,
      liveRecommendInfo: freezed == liveRecommendInfo
          ? _value.liveRecommendInfo
          : liveRecommendInfo // ignore: cast_nullable_to_non_nullable
              as LiveRecommenInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SKURecommenInfoCopyWith<$Res>? get skuRecommendInfo {
    if (_value.skuRecommendInfo == null) {
      return null;
    }

    return $SKURecommenInfoCopyWith<$Res>(_value.skuRecommendInfo!, (value) {
      return _then(_value.copyWith(skuRecommendInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LiveRecommenInfoCopyWith<$Res>? get liveRecommendInfo {
    if (_value.liveRecommendInfo == null) {
      return null;
    }

    return $LiveRecommenInfoCopyWith<$Res>(_value.liveRecommendInfo!, (value) {
      return _then(_value.copyWith(liveRecommendInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_lessonApplyCardInfoCopyWith<$Res>
    implements $lessonApplyCardInfoCopyWith<$Res> {
  factory _$$_lessonApplyCardInfoCopyWith(_$_lessonApplyCardInfo value,
          $Res Function(_$_lessonApplyCardInfo) then) =
      __$$_lessonApplyCardInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? imageUrl,
      String? applyCourseUrl,
      String? videoName,
      String? videoUrl,
      String? videoPictureUrl,
      String? videoDesc,
      String? videoButtonText,
      String? bgColor,
      String? cardMainColor,
      String? labelFontColor,
      String? materialName,
      String? subjectTypeDesc,
      String? topLeftLabelFontColor,
      int? materialId,
      int? recommendType,
      SKURecommenInfo? skuRecommendInfo,
      LiveRecommenInfo? liveRecommendInfo});

  @override
  $SKURecommenInfoCopyWith<$Res>? get skuRecommendInfo;
  @override
  $LiveRecommenInfoCopyWith<$Res>? get liveRecommendInfo;
}

/// @nodoc
class __$$_lessonApplyCardInfoCopyWithImpl<$Res>
    extends _$lessonApplyCardInfoCopyWithImpl<$Res, _$_lessonApplyCardInfo>
    implements _$$_lessonApplyCardInfoCopyWith<$Res> {
  __$$_lessonApplyCardInfoCopyWithImpl(_$_lessonApplyCardInfo _value,
      $Res Function(_$_lessonApplyCardInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? imageUrl = freezed,
    Object? applyCourseUrl = freezed,
    Object? videoName = freezed,
    Object? videoUrl = freezed,
    Object? videoPictureUrl = freezed,
    Object? videoDesc = freezed,
    Object? videoButtonText = freezed,
    Object? bgColor = freezed,
    Object? cardMainColor = freezed,
    Object? labelFontColor = freezed,
    Object? materialName = freezed,
    Object? subjectTypeDesc = freezed,
    Object? topLeftLabelFontColor = freezed,
    Object? materialId = freezed,
    Object? recommendType = freezed,
    Object? skuRecommendInfo = freezed,
    Object? liveRecommendInfo = freezed,
  }) {
    return _then(_$_lessonApplyCardInfo(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      applyCourseUrl: freezed == applyCourseUrl
          ? _value.applyCourseUrl
          : applyCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoName: freezed == videoName
          ? _value.videoName
          : videoName // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoPictureUrl: freezed == videoPictureUrl
          ? _value.videoPictureUrl
          : videoPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoDesc: freezed == videoDesc
          ? _value.videoDesc
          : videoDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      videoButtonText: freezed == videoButtonText
          ? _value.videoButtonText
          : videoButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      cardMainColor: freezed == cardMainColor
          ? _value.cardMainColor
          : cardMainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      labelFontColor: freezed == labelFontColor
          ? _value.labelFontColor
          : labelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      topLeftLabelFontColor: freezed == topLeftLabelFontColor
          ? _value.topLeftLabelFontColor
          : topLeftLabelFontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      recommendType: freezed == recommendType
          ? _value.recommendType
          : recommendType // ignore: cast_nullable_to_non_nullable
              as int?,
      skuRecommendInfo: freezed == skuRecommendInfo
          ? _value.skuRecommendInfo
          : skuRecommendInfo // ignore: cast_nullable_to_non_nullable
              as SKURecommenInfo?,
      liveRecommendInfo: freezed == liveRecommendInfo
          ? _value.liveRecommendInfo
          : liveRecommendInfo // ignore: cast_nullable_to_non_nullable
              as LiveRecommenInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_lessonApplyCardInfo implements _lessonApplyCardInfo {
  const _$_lessonApplyCardInfo(
      {this.subjectType,
      this.imageUrl,
      this.applyCourseUrl,
      this.videoName,
      this.videoUrl,
      this.videoPictureUrl,
      this.videoDesc,
      this.videoButtonText,
      this.bgColor,
      this.cardMainColor,
      this.labelFontColor,
      this.materialName,
      this.subjectTypeDesc,
      this.topLeftLabelFontColor,
      this.materialId,
      this.recommendType,
      this.skuRecommendInfo,
      this.liveRecommendInfo});

  factory _$_lessonApplyCardInfo.fromJson(Map<String, dynamic> json) =>
      _$$_lessonApplyCardInfoFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? imageUrl;
//领课卡片图
  @override
  final String? applyCourseUrl;
//领课链接
  @override
  final String? videoName;
//视频名称
  @override
  final String? videoUrl;
//视频链接url
  @override
  final String? videoPictureUrl;
//视频封面
  @override
  final String? videoDesc;
// 视频描述
  @override
  final String? videoButtonText;
//视频按钮文案
  @override
  final String? bgColor;
//背景颜色
  @override
  final String? cardMainColor;
//主色调
  @override
  final String? labelFontColor;
//文本颜色
  @override
  final String? materialName;
  @override
  final String? subjectTypeDesc;
  @override
  final String? topLeftLabelFontColor;
  @override
  final int? materialId;
//素材ID
  @override
  final int? recommendType;
//缺省推荐类型，0-无课推荐，2-全部课结束推荐
  @override
  final SKURecommenInfo? skuRecommendInfo;
//栏目信息
  @override
  final LiveRecommenInfo? liveRecommendInfo;

  @override
  String toString() {
    return 'lessonApplyCardInfo(subjectType: $subjectType, imageUrl: $imageUrl, applyCourseUrl: $applyCourseUrl, videoName: $videoName, videoUrl: $videoUrl, videoPictureUrl: $videoPictureUrl, videoDesc: $videoDesc, videoButtonText: $videoButtonText, bgColor: $bgColor, cardMainColor: $cardMainColor, labelFontColor: $labelFontColor, materialName: $materialName, subjectTypeDesc: $subjectTypeDesc, topLeftLabelFontColor: $topLeftLabelFontColor, materialId: $materialId, recommendType: $recommendType, skuRecommendInfo: $skuRecommendInfo, liveRecommendInfo: $liveRecommendInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_lessonApplyCardInfo &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.applyCourseUrl, applyCourseUrl) ||
                other.applyCourseUrl == applyCourseUrl) &&
            (identical(other.videoName, videoName) ||
                other.videoName == videoName) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.videoPictureUrl, videoPictureUrl) ||
                other.videoPictureUrl == videoPictureUrl) &&
            (identical(other.videoDesc, videoDesc) ||
                other.videoDesc == videoDesc) &&
            (identical(other.videoButtonText, videoButtonText) ||
                other.videoButtonText == videoButtonText) &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor) &&
            (identical(other.cardMainColor, cardMainColor) ||
                other.cardMainColor == cardMainColor) &&
            (identical(other.labelFontColor, labelFontColor) ||
                other.labelFontColor == labelFontColor) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.topLeftLabelFontColor, topLeftLabelFontColor) ||
                other.topLeftLabelFontColor == topLeftLabelFontColor) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.recommendType, recommendType) ||
                other.recommendType == recommendType) &&
            (identical(other.skuRecommendInfo, skuRecommendInfo) ||
                other.skuRecommendInfo == skuRecommendInfo) &&
            (identical(other.liveRecommendInfo, liveRecommendInfo) ||
                other.liveRecommendInfo == liveRecommendInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectType,
      imageUrl,
      applyCourseUrl,
      videoName,
      videoUrl,
      videoPictureUrl,
      videoDesc,
      videoButtonText,
      bgColor,
      cardMainColor,
      labelFontColor,
      materialName,
      subjectTypeDesc,
      topLeftLabelFontColor,
      materialId,
      recommendType,
      skuRecommendInfo,
      liveRecommendInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_lessonApplyCardInfoCopyWith<_$_lessonApplyCardInfo> get copyWith =>
      __$$_lessonApplyCardInfoCopyWithImpl<_$_lessonApplyCardInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_lessonApplyCardInfoToJson(
      this,
    );
  }
}

abstract class _lessonApplyCardInfo implements lessonApplyCardInfo {
  const factory _lessonApplyCardInfo(
      {final int? subjectType,
      final String? imageUrl,
      final String? applyCourseUrl,
      final String? videoName,
      final String? videoUrl,
      final String? videoPictureUrl,
      final String? videoDesc,
      final String? videoButtonText,
      final String? bgColor,
      final String? cardMainColor,
      final String? labelFontColor,
      final String? materialName,
      final String? subjectTypeDesc,
      final String? topLeftLabelFontColor,
      final int? materialId,
      final int? recommendType,
      final SKURecommenInfo? skuRecommendInfo,
      final LiveRecommenInfo? liveRecommendInfo}) = _$_lessonApplyCardInfo;

  factory _lessonApplyCardInfo.fromJson(Map<String, dynamic> json) =
      _$_lessonApplyCardInfo.fromJson;

  @override
  int? get subjectType;
  @override
  String? get imageUrl;
  @override //领课卡片图
  String? get applyCourseUrl;
  @override //领课链接
  String? get videoName;
  @override //视频名称
  String? get videoUrl;
  @override //视频链接url
  String? get videoPictureUrl;
  @override //视频封面
  String? get videoDesc;
  @override // 视频描述
  String? get videoButtonText;
  @override //视频按钮文案
  String? get bgColor;
  @override //背景颜色
  String? get cardMainColor;
  @override //主色调
  String? get labelFontColor;
  @override //文本颜色
  String? get materialName;
  @override
  String? get subjectTypeDesc;
  @override
  String? get topLeftLabelFontColor;
  @override
  int? get materialId;
  @override //素材ID
  int? get recommendType;
  @override //缺省推荐类型，0-无课推荐，2-全部课结束推荐
  SKURecommenInfo? get skuRecommendInfo;
  @override //栏目信息
  LiveRecommenInfo? get liveRecommendInfo;
  @override
  @JsonKey(ignore: true)
  _$$_lessonApplyCardInfoCopyWith<_$_lessonApplyCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

SKURecommenInfo _$SKURecommenInfoFromJson(Map<String, dynamic> json) {
  return _SKURecommenInfo.fromJson(json);
}

/// @nodoc
mixin _$SKURecommenInfo {
  int? get materialId => throw _privateConstructorUsedError; //素材ID
  double? get effectiveTime => throw _privateConstructorUsedError; //有效倒计时时长 ms
  double? get systemRunDurtion => throw _privateConstructorUsedError; //系统启动时间
  int? get activityId => throw _privateConstructorUsedError; //活动ID
  String? get linkId => throw _privateConstructorUsedError; //link id
  String? get materialName => throw _privateConstructorUsedError; //素材名称
  String? get readAbilityIcon => throw _privateConstructorUsedError; //阅读力测评图标
  String? get readAbilityName => throw _privateConstructorUsedError; //阅读力测评名称
  String? get readAbilityDesc => throw _privateConstructorUsedError; //阅读力测评入口描述
  String? get lessonOrder => throw _privateConstructorUsedError; //课时顺序表述
  String? get lessonTitle => throw _privateConstructorUsedError; //课时标题
  String? get lessonIcon => throw _privateConstructorUsedError; //课时图标
  String? get lessonContent => throw _privateConstructorUsedError; //课时内容
  String? get lessonButton => throw _privateConstructorUsedError; //课时按钮
  String? get recommendOrderButton => throw _privateConstructorUsedError; //领课按钮
  List<lessonSKURecommendGrade>? get skuConfigList =>
      throw _privateConstructorUsedError; // 年级弹窗数据
  courseServiceData? get assessmentModel =>
      throw _privateConstructorUsedError; // 测评
  packageTwoItemData? get packageTwoModel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SKURecommenInfoCopyWith<SKURecommenInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SKURecommenInfoCopyWith<$Res> {
  factory $SKURecommenInfoCopyWith(
          SKURecommenInfo value, $Res Function(SKURecommenInfo) then) =
      _$SKURecommenInfoCopyWithImpl<$Res, SKURecommenInfo>;
  @useResult
  $Res call(
      {int? materialId,
      double? effectiveTime,
      double? systemRunDurtion,
      int? activityId,
      String? linkId,
      String? materialName,
      String? readAbilityIcon,
      String? readAbilityName,
      String? readAbilityDesc,
      String? lessonOrder,
      String? lessonTitle,
      String? lessonIcon,
      String? lessonContent,
      String? lessonButton,
      String? recommendOrderButton,
      List<lessonSKURecommendGrade>? skuConfigList,
      courseServiceData? assessmentModel,
      packageTwoItemData? packageTwoModel});

  $courseServiceDataCopyWith<$Res>? get assessmentModel;
  $packageTwoItemDataCopyWith<$Res>? get packageTwoModel;
}

/// @nodoc
class _$SKURecommenInfoCopyWithImpl<$Res, $Val extends SKURecommenInfo>
    implements $SKURecommenInfoCopyWith<$Res> {
  _$SKURecommenInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? effectiveTime = freezed,
    Object? systemRunDurtion = freezed,
    Object? activityId = freezed,
    Object? linkId = freezed,
    Object? materialName = freezed,
    Object? readAbilityIcon = freezed,
    Object? readAbilityName = freezed,
    Object? readAbilityDesc = freezed,
    Object? lessonOrder = freezed,
    Object? lessonTitle = freezed,
    Object? lessonIcon = freezed,
    Object? lessonContent = freezed,
    Object? lessonButton = freezed,
    Object? recommendOrderButton = freezed,
    Object? skuConfigList = freezed,
    Object? assessmentModel = freezed,
    Object? packageTwoModel = freezed,
  }) {
    return _then(_value.copyWith(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      effectiveTime: freezed == effectiveTime
          ? _value.effectiveTime
          : effectiveTime // ignore: cast_nullable_to_non_nullable
              as double?,
      systemRunDurtion: freezed == systemRunDurtion
          ? _value.systemRunDurtion
          : systemRunDurtion // ignore: cast_nullable_to_non_nullable
              as double?,
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityIcon: freezed == readAbilityIcon
          ? _value.readAbilityIcon
          : readAbilityIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityName: freezed == readAbilityName
          ? _value.readAbilityName
          : readAbilityName // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityDesc: freezed == readAbilityDesc
          ? _value.readAbilityDesc
          : readAbilityDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonIcon: freezed == lessonIcon
          ? _value.lessonIcon
          : lessonIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonContent: freezed == lessonContent
          ? _value.lessonContent
          : lessonContent // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonButton: freezed == lessonButton
          ? _value.lessonButton
          : lessonButton // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendOrderButton: freezed == recommendOrderButton
          ? _value.recommendOrderButton
          : recommendOrderButton // ignore: cast_nullable_to_non_nullable
              as String?,
      skuConfigList: freezed == skuConfigList
          ? _value.skuConfigList
          : skuConfigList // ignore: cast_nullable_to_non_nullable
              as List<lessonSKURecommendGrade>?,
      assessmentModel: freezed == assessmentModel
          ? _value.assessmentModel
          : assessmentModel // ignore: cast_nullable_to_non_nullable
              as courseServiceData?,
      packageTwoModel: freezed == packageTwoModel
          ? _value.packageTwoModel
          : packageTwoModel // ignore: cast_nullable_to_non_nullable
              as packageTwoItemData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $courseServiceDataCopyWith<$Res>? get assessmentModel {
    if (_value.assessmentModel == null) {
      return null;
    }

    return $courseServiceDataCopyWith<$Res>(_value.assessmentModel!, (value) {
      return _then(_value.copyWith(assessmentModel: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $packageTwoItemDataCopyWith<$Res>? get packageTwoModel {
    if (_value.packageTwoModel == null) {
      return null;
    }

    return $packageTwoItemDataCopyWith<$Res>(_value.packageTwoModel!, (value) {
      return _then(_value.copyWith(packageTwoModel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SKURecommenInfoCopyWith<$Res>
    implements $SKURecommenInfoCopyWith<$Res> {
  factory _$$_SKURecommenInfoCopyWith(
          _$_SKURecommenInfo value, $Res Function(_$_SKURecommenInfo) then) =
      __$$_SKURecommenInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? materialId,
      double? effectiveTime,
      double? systemRunDurtion,
      int? activityId,
      String? linkId,
      String? materialName,
      String? readAbilityIcon,
      String? readAbilityName,
      String? readAbilityDesc,
      String? lessonOrder,
      String? lessonTitle,
      String? lessonIcon,
      String? lessonContent,
      String? lessonButton,
      String? recommendOrderButton,
      List<lessonSKURecommendGrade>? skuConfigList,
      courseServiceData? assessmentModel,
      packageTwoItemData? packageTwoModel});

  @override
  $courseServiceDataCopyWith<$Res>? get assessmentModel;
  @override
  $packageTwoItemDataCopyWith<$Res>? get packageTwoModel;
}

/// @nodoc
class __$$_SKURecommenInfoCopyWithImpl<$Res>
    extends _$SKURecommenInfoCopyWithImpl<$Res, _$_SKURecommenInfo>
    implements _$$_SKURecommenInfoCopyWith<$Res> {
  __$$_SKURecommenInfoCopyWithImpl(
      _$_SKURecommenInfo _value, $Res Function(_$_SKURecommenInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? effectiveTime = freezed,
    Object? systemRunDurtion = freezed,
    Object? activityId = freezed,
    Object? linkId = freezed,
    Object? materialName = freezed,
    Object? readAbilityIcon = freezed,
    Object? readAbilityName = freezed,
    Object? readAbilityDesc = freezed,
    Object? lessonOrder = freezed,
    Object? lessonTitle = freezed,
    Object? lessonIcon = freezed,
    Object? lessonContent = freezed,
    Object? lessonButton = freezed,
    Object? recommendOrderButton = freezed,
    Object? skuConfigList = freezed,
    Object? assessmentModel = freezed,
    Object? packageTwoModel = freezed,
  }) {
    return _then(_$_SKURecommenInfo(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      effectiveTime: freezed == effectiveTime
          ? _value.effectiveTime
          : effectiveTime // ignore: cast_nullable_to_non_nullable
              as double?,
      systemRunDurtion: freezed == systemRunDurtion
          ? _value.systemRunDurtion
          : systemRunDurtion // ignore: cast_nullable_to_non_nullable
              as double?,
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityIcon: freezed == readAbilityIcon
          ? _value.readAbilityIcon
          : readAbilityIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityName: freezed == readAbilityName
          ? _value.readAbilityName
          : readAbilityName // ignore: cast_nullable_to_non_nullable
              as String?,
      readAbilityDesc: freezed == readAbilityDesc
          ? _value.readAbilityDesc
          : readAbilityDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonTitle: freezed == lessonTitle
          ? _value.lessonTitle
          : lessonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonIcon: freezed == lessonIcon
          ? _value.lessonIcon
          : lessonIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonContent: freezed == lessonContent
          ? _value.lessonContent
          : lessonContent // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonButton: freezed == lessonButton
          ? _value.lessonButton
          : lessonButton // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendOrderButton: freezed == recommendOrderButton
          ? _value.recommendOrderButton
          : recommendOrderButton // ignore: cast_nullable_to_non_nullable
              as String?,
      skuConfigList: freezed == skuConfigList
          ? _value._skuConfigList
          : skuConfigList // ignore: cast_nullable_to_non_nullable
              as List<lessonSKURecommendGrade>?,
      assessmentModel: freezed == assessmentModel
          ? _value.assessmentModel
          : assessmentModel // ignore: cast_nullable_to_non_nullable
              as courseServiceData?,
      packageTwoModel: freezed == packageTwoModel
          ? _value.packageTwoModel
          : packageTwoModel // ignore: cast_nullable_to_non_nullable
              as packageTwoItemData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SKURecommenInfo implements _SKURecommenInfo {
  const _$_SKURecommenInfo(
      {this.materialId,
      this.effectiveTime,
      this.systemRunDurtion,
      this.activityId,
      this.linkId,
      this.materialName,
      this.readAbilityIcon,
      this.readAbilityName,
      this.readAbilityDesc,
      this.lessonOrder,
      this.lessonTitle,
      this.lessonIcon,
      this.lessonContent,
      this.lessonButton,
      this.recommendOrderButton,
      final List<lessonSKURecommendGrade>? skuConfigList,
      this.assessmentModel,
      this.packageTwoModel})
      : _skuConfigList = skuConfigList;

  factory _$_SKURecommenInfo.fromJson(Map<String, dynamic> json) =>
      _$$_SKURecommenInfoFromJson(json);

  @override
  final int? materialId;
//素材ID
  @override
  final double? effectiveTime;
//有效倒计时时长 ms
  @override
  final double? systemRunDurtion;
//系统启动时间
  @override
  final int? activityId;
//活动ID
  @override
  final String? linkId;
//link id
  @override
  final String? materialName;
//素材名称
  @override
  final String? readAbilityIcon;
//阅读力测评图标
  @override
  final String? readAbilityName;
//阅读力测评名称
  @override
  final String? readAbilityDesc;
//阅读力测评入口描述
  @override
  final String? lessonOrder;
//课时顺序表述
  @override
  final String? lessonTitle;
//课时标题
  @override
  final String? lessonIcon;
//课时图标
  @override
  final String? lessonContent;
//课时内容
  @override
  final String? lessonButton;
//课时按钮
  @override
  final String? recommendOrderButton;
//领课按钮
  final List<lessonSKURecommendGrade>? _skuConfigList;
//领课按钮
  @override
  List<lessonSKURecommendGrade>? get skuConfigList {
    final value = _skuConfigList;
    if (value == null) return null;
    if (_skuConfigList is EqualUnmodifiableListView) return _skuConfigList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 年级弹窗数据
  @override
  final courseServiceData? assessmentModel;
// 测评
  @override
  final packageTwoItemData? packageTwoModel;

  @override
  String toString() {
    return 'SKURecommenInfo(materialId: $materialId, effectiveTime: $effectiveTime, systemRunDurtion: $systemRunDurtion, activityId: $activityId, linkId: $linkId, materialName: $materialName, readAbilityIcon: $readAbilityIcon, readAbilityName: $readAbilityName, readAbilityDesc: $readAbilityDesc, lessonOrder: $lessonOrder, lessonTitle: $lessonTitle, lessonIcon: $lessonIcon, lessonContent: $lessonContent, lessonButton: $lessonButton, recommendOrderButton: $recommendOrderButton, skuConfigList: $skuConfigList, assessmentModel: $assessmentModel, packageTwoModel: $packageTwoModel)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SKURecommenInfo &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.effectiveTime, effectiveTime) ||
                other.effectiveTime == effectiveTime) &&
            (identical(other.systemRunDurtion, systemRunDurtion) ||
                other.systemRunDurtion == systemRunDurtion) &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.linkId, linkId) || other.linkId == linkId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.readAbilityIcon, readAbilityIcon) ||
                other.readAbilityIcon == readAbilityIcon) &&
            (identical(other.readAbilityName, readAbilityName) ||
                other.readAbilityName == readAbilityName) &&
            (identical(other.readAbilityDesc, readAbilityDesc) ||
                other.readAbilityDesc == readAbilityDesc) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.lessonTitle, lessonTitle) ||
                other.lessonTitle == lessonTitle) &&
            (identical(other.lessonIcon, lessonIcon) ||
                other.lessonIcon == lessonIcon) &&
            (identical(other.lessonContent, lessonContent) ||
                other.lessonContent == lessonContent) &&
            (identical(other.lessonButton, lessonButton) ||
                other.lessonButton == lessonButton) &&
            (identical(other.recommendOrderButton, recommendOrderButton) ||
                other.recommendOrderButton == recommendOrderButton) &&
            const DeepCollectionEquality()
                .equals(other._skuConfigList, _skuConfigList) &&
            (identical(other.assessmentModel, assessmentModel) ||
                other.assessmentModel == assessmentModel) &&
            (identical(other.packageTwoModel, packageTwoModel) ||
                other.packageTwoModel == packageTwoModel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      materialId,
      effectiveTime,
      systemRunDurtion,
      activityId,
      linkId,
      materialName,
      readAbilityIcon,
      readAbilityName,
      readAbilityDesc,
      lessonOrder,
      lessonTitle,
      lessonIcon,
      lessonContent,
      lessonButton,
      recommendOrderButton,
      const DeepCollectionEquality().hash(_skuConfigList),
      assessmentModel,
      packageTwoModel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SKURecommenInfoCopyWith<_$_SKURecommenInfo> get copyWith =>
      __$$_SKURecommenInfoCopyWithImpl<_$_SKURecommenInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SKURecommenInfoToJson(
      this,
    );
  }
}

abstract class _SKURecommenInfo implements SKURecommenInfo {
  const factory _SKURecommenInfo(
      {final int? materialId,
      final double? effectiveTime,
      final double? systemRunDurtion,
      final int? activityId,
      final String? linkId,
      final String? materialName,
      final String? readAbilityIcon,
      final String? readAbilityName,
      final String? readAbilityDesc,
      final String? lessonOrder,
      final String? lessonTitle,
      final String? lessonIcon,
      final String? lessonContent,
      final String? lessonButton,
      final String? recommendOrderButton,
      final List<lessonSKURecommendGrade>? skuConfigList,
      final courseServiceData? assessmentModel,
      final packageTwoItemData? packageTwoModel}) = _$_SKURecommenInfo;

  factory _SKURecommenInfo.fromJson(Map<String, dynamic> json) =
      _$_SKURecommenInfo.fromJson;

  @override
  int? get materialId;
  @override //素材ID
  double? get effectiveTime;
  @override //有效倒计时时长 ms
  double? get systemRunDurtion;
  @override //系统启动时间
  int? get activityId;
  @override //活动ID
  String? get linkId;
  @override //link id
  String? get materialName;
  @override //素材名称
  String? get readAbilityIcon;
  @override //阅读力测评图标
  String? get readAbilityName;
  @override //阅读力测评名称
  String? get readAbilityDesc;
  @override //阅读力测评入口描述
  String? get lessonOrder;
  @override //课时顺序表述
  String? get lessonTitle;
  @override //课时标题
  String? get lessonIcon;
  @override //课时图标
  String? get lessonContent;
  @override //课时内容
  String? get lessonButton;
  @override //课时按钮
  String? get recommendOrderButton;
  @override //领课按钮
  List<lessonSKURecommendGrade>? get skuConfigList;
  @override // 年级弹窗数据
  courseServiceData? get assessmentModel;
  @override // 测评
  packageTwoItemData? get packageTwoModel;
  @override
  @JsonKey(ignore: true)
  _$$_SKURecommenInfoCopyWith<_$_SKURecommenInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

lessonSKURecommendGrade _$lessonSKURecommendGradeFromJson(
    Map<String, dynamic> json) {
  return _lessonSKURecommendGrade.fromJson(json);
}

/// @nodoc
mixin _$lessonSKURecommendGrade {
  String? get skuId => throw _privateConstructorUsedError; // ID
  String? get skuOption => throw _privateConstructorUsedError; // 选项值
  String? get skuChannel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $lessonSKURecommendGradeCopyWith<lessonSKURecommendGrade> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $lessonSKURecommendGradeCopyWith<$Res> {
  factory $lessonSKURecommendGradeCopyWith(lessonSKURecommendGrade value,
          $Res Function(lessonSKURecommendGrade) then) =
      _$lessonSKURecommendGradeCopyWithImpl<$Res, lessonSKURecommendGrade>;
  @useResult
  $Res call({String? skuId, String? skuOption, String? skuChannel});
}

/// @nodoc
class _$lessonSKURecommendGradeCopyWithImpl<$Res,
        $Val extends lessonSKURecommendGrade>
    implements $lessonSKURecommendGradeCopyWith<$Res> {
  _$lessonSKURecommendGradeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuOption = freezed,
    Object? skuChannel = freezed,
  }) {
    return _then(_value.copyWith(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuOption: freezed == skuOption
          ? _value.skuOption
          : skuOption // ignore: cast_nullable_to_non_nullable
              as String?,
      skuChannel: freezed == skuChannel
          ? _value.skuChannel
          : skuChannel // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_lessonSKURecommendGradeCopyWith<$Res>
    implements $lessonSKURecommendGradeCopyWith<$Res> {
  factory _$$_lessonSKURecommendGradeCopyWith(_$_lessonSKURecommendGrade value,
          $Res Function(_$_lessonSKURecommendGrade) then) =
      __$$_lessonSKURecommendGradeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? skuId, String? skuOption, String? skuChannel});
}

/// @nodoc
class __$$_lessonSKURecommendGradeCopyWithImpl<$Res>
    extends _$lessonSKURecommendGradeCopyWithImpl<$Res,
        _$_lessonSKURecommendGrade>
    implements _$$_lessonSKURecommendGradeCopyWith<$Res> {
  __$$_lessonSKURecommendGradeCopyWithImpl(_$_lessonSKURecommendGrade _value,
      $Res Function(_$_lessonSKURecommendGrade) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuOption = freezed,
    Object? skuChannel = freezed,
  }) {
    return _then(_$_lessonSKURecommendGrade(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuOption: freezed == skuOption
          ? _value.skuOption
          : skuOption // ignore: cast_nullable_to_non_nullable
              as String?,
      skuChannel: freezed == skuChannel
          ? _value.skuChannel
          : skuChannel // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_lessonSKURecommendGrade implements _lessonSKURecommendGrade {
  const _$_lessonSKURecommendGrade(
      {this.skuId, this.skuOption, this.skuChannel});

  factory _$_lessonSKURecommendGrade.fromJson(Map<String, dynamic> json) =>
      _$$_lessonSKURecommendGradeFromJson(json);

  @override
  final String? skuId;
// ID
  @override
  final String? skuOption;
// 选项值
  @override
  final String? skuChannel;

  @override
  String toString() {
    return 'lessonSKURecommendGrade(skuId: $skuId, skuOption: $skuOption, skuChannel: $skuChannel)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_lessonSKURecommendGrade &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuOption, skuOption) ||
                other.skuOption == skuOption) &&
            (identical(other.skuChannel, skuChannel) ||
                other.skuChannel == skuChannel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skuId, skuOption, skuChannel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_lessonSKURecommendGradeCopyWith<_$_lessonSKURecommendGrade>
      get copyWith =>
          __$$_lessonSKURecommendGradeCopyWithImpl<_$_lessonSKURecommendGrade>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_lessonSKURecommendGradeToJson(
      this,
    );
  }
}

abstract class _lessonSKURecommendGrade implements lessonSKURecommendGrade {
  const factory _lessonSKURecommendGrade(
      {final String? skuId,
      final String? skuOption,
      final String? skuChannel}) = _$_lessonSKURecommendGrade;

  factory _lessonSKURecommendGrade.fromJson(Map<String, dynamic> json) =
      _$_lessonSKURecommendGrade.fromJson;

  @override
  String? get skuId;
  @override // ID
  String? get skuOption;
  @override // 选项值
  String? get skuChannel;
  @override
  @JsonKey(ignore: true)
  _$$_lessonSKURecommendGradeCopyWith<_$_lessonSKURecommendGrade>
      get copyWith => throw _privateConstructorUsedError;
}

LiveRecommenInfo _$LiveRecommenInfoFromJson(Map<String, dynamic> json) {
  return _LiveRecommenInfo.fromJson(json);
}

/// @nodoc
mixin _$LiveRecommenInfo {
  String? get businessTagId => throw _privateConstructorUsedError; //商业标签ID
  String? get backgroundColor => throw _privateConstructorUsedError; //背景色
  String? get liveStreamingCardVideoUrl =>
      throw _privateConstructorUsedError; //直播卡片视频URL
  String? get liveStreamingDescription =>
      throw _privateConstructorUsedError; //直播描述
  String? get pushingDecorationUrl =>
      throw _privateConstructorUsedError; //主推装饰URL
  String? get liveStreamingVideoUrl => throw _privateConstructorUsedError; //
  String? get liveStreamingVideoCoverUrl =>
      throw _privateConstructorUsedError; //视频封面图URL
  String? get liveStreamingLinkUrl => throw _privateConstructorUsedError; //跳转链接
  List<liveProductItem>? get productList =>
      throw _privateConstructorUsedError; //商品列表
  teacherInfoData? get teacherInfo =>
      throw _privateConstructorUsedError; // 指导老师信息
  String? get businessTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LiveRecommenInfoCopyWith<LiveRecommenInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LiveRecommenInfoCopyWith<$Res> {
  factory $LiveRecommenInfoCopyWith(
          LiveRecommenInfo value, $Res Function(LiveRecommenInfo) then) =
      _$LiveRecommenInfoCopyWithImpl<$Res, LiveRecommenInfo>;
  @useResult
  $Res call(
      {String? businessTagId,
      String? backgroundColor,
      String? liveStreamingCardVideoUrl,
      String? liveStreamingDescription,
      String? pushingDecorationUrl,
      String? liveStreamingVideoUrl,
      String? liveStreamingVideoCoverUrl,
      String? liveStreamingLinkUrl,
      List<liveProductItem>? productList,
      teacherInfoData? teacherInfo,
      String? businessTypeDesc});

  $teacherInfoDataCopyWith<$Res>? get teacherInfo;
}

/// @nodoc
class _$LiveRecommenInfoCopyWithImpl<$Res, $Val extends LiveRecommenInfo>
    implements $LiveRecommenInfoCopyWith<$Res> {
  _$LiveRecommenInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessTagId = freezed,
    Object? backgroundColor = freezed,
    Object? liveStreamingCardVideoUrl = freezed,
    Object? liveStreamingDescription = freezed,
    Object? pushingDecorationUrl = freezed,
    Object? liveStreamingVideoUrl = freezed,
    Object? liveStreamingVideoCoverUrl = freezed,
    Object? liveStreamingLinkUrl = freezed,
    Object? productList = freezed,
    Object? teacherInfo = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingCardVideoUrl: freezed == liveStreamingCardVideoUrl
          ? _value.liveStreamingCardVideoUrl
          : liveStreamingCardVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingDescription: freezed == liveStreamingDescription
          ? _value.liveStreamingDescription
          : liveStreamingDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      pushingDecorationUrl: freezed == pushingDecorationUrl
          ? _value.pushingDecorationUrl
          : pushingDecorationUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingVideoUrl: freezed == liveStreamingVideoUrl
          ? _value.liveStreamingVideoUrl
          : liveStreamingVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingVideoCoverUrl: freezed == liveStreamingVideoCoverUrl
          ? _value.liveStreamingVideoCoverUrl
          : liveStreamingVideoCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingLinkUrl: freezed == liveStreamingLinkUrl
          ? _value.liveStreamingLinkUrl
          : liveStreamingLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productList: freezed == productList
          ? _value.productList
          : productList // ignore: cast_nullable_to_non_nullable
              as List<liveProductItem>?,
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as teacherInfoData?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $teacherInfoDataCopyWith<$Res>? get teacherInfo {
    if (_value.teacherInfo == null) {
      return null;
    }

    return $teacherInfoDataCopyWith<$Res>(_value.teacherInfo!, (value) {
      return _then(_value.copyWith(teacherInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LiveRecommenInfoCopyWith<$Res>
    implements $LiveRecommenInfoCopyWith<$Res> {
  factory _$$_LiveRecommenInfoCopyWith(
          _$_LiveRecommenInfo value, $Res Function(_$_LiveRecommenInfo) then) =
      __$$_LiveRecommenInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? businessTagId,
      String? backgroundColor,
      String? liveStreamingCardVideoUrl,
      String? liveStreamingDescription,
      String? pushingDecorationUrl,
      String? liveStreamingVideoUrl,
      String? liveStreamingVideoCoverUrl,
      String? liveStreamingLinkUrl,
      List<liveProductItem>? productList,
      teacherInfoData? teacherInfo,
      String? businessTypeDesc});

  @override
  $teacherInfoDataCopyWith<$Res>? get teacherInfo;
}

/// @nodoc
class __$$_LiveRecommenInfoCopyWithImpl<$Res>
    extends _$LiveRecommenInfoCopyWithImpl<$Res, _$_LiveRecommenInfo>
    implements _$$_LiveRecommenInfoCopyWith<$Res> {
  __$$_LiveRecommenInfoCopyWithImpl(
      _$_LiveRecommenInfo _value, $Res Function(_$_LiveRecommenInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessTagId = freezed,
    Object? backgroundColor = freezed,
    Object? liveStreamingCardVideoUrl = freezed,
    Object? liveStreamingDescription = freezed,
    Object? pushingDecorationUrl = freezed,
    Object? liveStreamingVideoUrl = freezed,
    Object? liveStreamingVideoCoverUrl = freezed,
    Object? liveStreamingLinkUrl = freezed,
    Object? productList = freezed,
    Object? teacherInfo = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_$_LiveRecommenInfo(
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingCardVideoUrl: freezed == liveStreamingCardVideoUrl
          ? _value.liveStreamingCardVideoUrl
          : liveStreamingCardVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingDescription: freezed == liveStreamingDescription
          ? _value.liveStreamingDescription
          : liveStreamingDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      pushingDecorationUrl: freezed == pushingDecorationUrl
          ? _value.pushingDecorationUrl
          : pushingDecorationUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingVideoUrl: freezed == liveStreamingVideoUrl
          ? _value.liveStreamingVideoUrl
          : liveStreamingVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingVideoCoverUrl: freezed == liveStreamingVideoCoverUrl
          ? _value.liveStreamingVideoCoverUrl
          : liveStreamingVideoCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      liveStreamingLinkUrl: freezed == liveStreamingLinkUrl
          ? _value.liveStreamingLinkUrl
          : liveStreamingLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productList: freezed == productList
          ? _value._productList
          : productList // ignore: cast_nullable_to_non_nullable
              as List<liveProductItem>?,
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as teacherInfoData?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LiveRecommenInfo implements _LiveRecommenInfo {
  const _$_LiveRecommenInfo(
      {this.businessTagId,
      this.backgroundColor,
      this.liveStreamingCardVideoUrl,
      this.liveStreamingDescription,
      this.pushingDecorationUrl,
      this.liveStreamingVideoUrl,
      this.liveStreamingVideoCoverUrl,
      this.liveStreamingLinkUrl,
      final List<liveProductItem>? productList,
      this.teacherInfo,
      this.businessTypeDesc})
      : _productList = productList;

  factory _$_LiveRecommenInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LiveRecommenInfoFromJson(json);

  @override
  final String? businessTagId;
//商业标签ID
  @override
  final String? backgroundColor;
//背景色
  @override
  final String? liveStreamingCardVideoUrl;
//直播卡片视频URL
  @override
  final String? liveStreamingDescription;
//直播描述
  @override
  final String? pushingDecorationUrl;
//主推装饰URL
  @override
  final String? liveStreamingVideoUrl;
//
  @override
  final String? liveStreamingVideoCoverUrl;
//视频封面图URL
  @override
  final String? liveStreamingLinkUrl;
//跳转链接
  final List<liveProductItem>? _productList;
//跳转链接
  @override
  List<liveProductItem>? get productList {
    final value = _productList;
    if (value == null) return null;
    if (_productList is EqualUnmodifiableListView) return _productList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//商品列表
  @override
  final teacherInfoData? teacherInfo;
// 指导老师信息
  @override
  final String? businessTypeDesc;

  @override
  String toString() {
    return 'LiveRecommenInfo(businessTagId: $businessTagId, backgroundColor: $backgroundColor, liveStreamingCardVideoUrl: $liveStreamingCardVideoUrl, liveStreamingDescription: $liveStreamingDescription, pushingDecorationUrl: $pushingDecorationUrl, liveStreamingVideoUrl: $liveStreamingVideoUrl, liveStreamingVideoCoverUrl: $liveStreamingVideoCoverUrl, liveStreamingLinkUrl: $liveStreamingLinkUrl, productList: $productList, teacherInfo: $teacherInfo, businessTypeDesc: $businessTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LiveRecommenInfo &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.liveStreamingCardVideoUrl,
                    liveStreamingCardVideoUrl) ||
                other.liveStreamingCardVideoUrl == liveStreamingCardVideoUrl) &&
            (identical(
                    other.liveStreamingDescription, liveStreamingDescription) ||
                other.liveStreamingDescription == liveStreamingDescription) &&
            (identical(other.pushingDecorationUrl, pushingDecorationUrl) ||
                other.pushingDecorationUrl == pushingDecorationUrl) &&
            (identical(other.liveStreamingVideoUrl, liveStreamingVideoUrl) ||
                other.liveStreamingVideoUrl == liveStreamingVideoUrl) &&
            (identical(other.liveStreamingVideoCoverUrl,
                    liveStreamingVideoCoverUrl) ||
                other.liveStreamingVideoCoverUrl ==
                    liveStreamingVideoCoverUrl) &&
            (identical(other.liveStreamingLinkUrl, liveStreamingLinkUrl) ||
                other.liveStreamingLinkUrl == liveStreamingLinkUrl) &&
            const DeepCollectionEquality()
                .equals(other._productList, _productList) &&
            (identical(other.teacherInfo, teacherInfo) ||
                other.teacherInfo == teacherInfo) &&
            (identical(other.businessTypeDesc, businessTypeDesc) ||
                other.businessTypeDesc == businessTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      businessTagId,
      backgroundColor,
      liveStreamingCardVideoUrl,
      liveStreamingDescription,
      pushingDecorationUrl,
      liveStreamingVideoUrl,
      liveStreamingVideoCoverUrl,
      liveStreamingLinkUrl,
      const DeepCollectionEquality().hash(_productList),
      teacherInfo,
      businessTypeDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LiveRecommenInfoCopyWith<_$_LiveRecommenInfo> get copyWith =>
      __$$_LiveRecommenInfoCopyWithImpl<_$_LiveRecommenInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LiveRecommenInfoToJson(
      this,
    );
  }
}

abstract class _LiveRecommenInfo implements LiveRecommenInfo {
  const factory _LiveRecommenInfo(
      {final String? businessTagId,
      final String? backgroundColor,
      final String? liveStreamingCardVideoUrl,
      final String? liveStreamingDescription,
      final String? pushingDecorationUrl,
      final String? liveStreamingVideoUrl,
      final String? liveStreamingVideoCoverUrl,
      final String? liveStreamingLinkUrl,
      final List<liveProductItem>? productList,
      final teacherInfoData? teacherInfo,
      final String? businessTypeDesc}) = _$_LiveRecommenInfo;

  factory _LiveRecommenInfo.fromJson(Map<String, dynamic> json) =
      _$_LiveRecommenInfo.fromJson;

  @override
  String? get businessTagId;
  @override //商业标签ID
  String? get backgroundColor;
  @override //背景色
  String? get liveStreamingCardVideoUrl;
  @override //直播卡片视频URL
  String? get liveStreamingDescription;
  @override //直播描述
  String? get pushingDecorationUrl;
  @override //主推装饰URL
  String? get liveStreamingVideoUrl;
  @override //
  String? get liveStreamingVideoCoverUrl;
  @override //视频封面图URL
  String? get liveStreamingLinkUrl;
  @override //跳转链接
  List<liveProductItem>? get productList;
  @override //商品列表
  teacherInfoData? get teacherInfo;
  @override // 指导老师信息
  String? get businessTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_LiveRecommenInfoCopyWith<_$_LiveRecommenInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

liveProductItem _$liveProductItemFromJson(Map<String, dynamic> json) {
  return _liveProductItem.fromJson(json);
}

/// @nodoc
mixin _$liveProductItem {
  String? get productPictureUrl => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  String? get productPrice => throw _privateConstructorUsedError;
  String? get productTag => throw _privateConstructorUsedError;
  String? get productButtonDescription => throw _privateConstructorUsedError;
  String? get productLinkUrl => throw _privateConstructorUsedError;
  String? get productDescription => throw _privateConstructorUsedError;
  bool? get isTeacher => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $liveProductItemCopyWith<liveProductItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $liveProductItemCopyWith<$Res> {
  factory $liveProductItemCopyWith(
          liveProductItem value, $Res Function(liveProductItem) then) =
      _$liveProductItemCopyWithImpl<$Res, liveProductItem>;
  @useResult
  $Res call(
      {String? productPictureUrl,
      String? productName,
      String? productPrice,
      String? productTag,
      String? productButtonDescription,
      String? productLinkUrl,
      String? productDescription,
      bool? isTeacher,
      int? teacherId,
      int? classId});
}

/// @nodoc
class _$liveProductItemCopyWithImpl<$Res, $Val extends liveProductItem>
    implements $liveProductItemCopyWith<$Res> {
  _$liveProductItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productPictureUrl = freezed,
    Object? productName = freezed,
    Object? productPrice = freezed,
    Object? productTag = freezed,
    Object? productButtonDescription = freezed,
    Object? productLinkUrl = freezed,
    Object? productDescription = freezed,
    Object? isTeacher = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      productPictureUrl: freezed == productPictureUrl
          ? _value.productPictureUrl
          : productPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      productPrice: freezed == productPrice
          ? _value.productPrice
          : productPrice // ignore: cast_nullable_to_non_nullable
              as String?,
      productTag: freezed == productTag
          ? _value.productTag
          : productTag // ignore: cast_nullable_to_non_nullable
              as String?,
      productButtonDescription: freezed == productButtonDescription
          ? _value.productButtonDescription
          : productButtonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      productLinkUrl: freezed == productLinkUrl
          ? _value.productLinkUrl
          : productLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productDescription: freezed == productDescription
          ? _value.productDescription
          : productDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      isTeacher: freezed == isTeacher
          ? _value.isTeacher
          : isTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_liveProductItemCopyWith<$Res>
    implements $liveProductItemCopyWith<$Res> {
  factory _$$_liveProductItemCopyWith(
          _$_liveProductItem value, $Res Function(_$_liveProductItem) then) =
      __$$_liveProductItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productPictureUrl,
      String? productName,
      String? productPrice,
      String? productTag,
      String? productButtonDescription,
      String? productLinkUrl,
      String? productDescription,
      bool? isTeacher,
      int? teacherId,
      int? classId});
}

/// @nodoc
class __$$_liveProductItemCopyWithImpl<$Res>
    extends _$liveProductItemCopyWithImpl<$Res, _$_liveProductItem>
    implements _$$_liveProductItemCopyWith<$Res> {
  __$$_liveProductItemCopyWithImpl(
      _$_liveProductItem _value, $Res Function(_$_liveProductItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productPictureUrl = freezed,
    Object? productName = freezed,
    Object? productPrice = freezed,
    Object? productTag = freezed,
    Object? productButtonDescription = freezed,
    Object? productLinkUrl = freezed,
    Object? productDescription = freezed,
    Object? isTeacher = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_$_liveProductItem(
      productPictureUrl: freezed == productPictureUrl
          ? _value.productPictureUrl
          : productPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      productPrice: freezed == productPrice
          ? _value.productPrice
          : productPrice // ignore: cast_nullable_to_non_nullable
              as String?,
      productTag: freezed == productTag
          ? _value.productTag
          : productTag // ignore: cast_nullable_to_non_nullable
              as String?,
      productButtonDescription: freezed == productButtonDescription
          ? _value.productButtonDescription
          : productButtonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      productLinkUrl: freezed == productLinkUrl
          ? _value.productLinkUrl
          : productLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      productDescription: freezed == productDescription
          ? _value.productDescription
          : productDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      isTeacher: freezed == isTeacher
          ? _value.isTeacher
          : isTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_liveProductItem implements _liveProductItem {
  const _$_liveProductItem(
      {this.productPictureUrl,
      this.productName,
      this.productPrice,
      this.productTag,
      this.productButtonDescription,
      this.productLinkUrl,
      this.productDescription,
      this.isTeacher,
      this.teacherId,
      this.classId});

  factory _$_liveProductItem.fromJson(Map<String, dynamic> json) =>
      _$$_liveProductItemFromJson(json);

  @override
  final String? productPictureUrl;
  @override
  final String? productName;
  @override
  final String? productPrice;
  @override
  final String? productTag;
  @override
  final String? productButtonDescription;
  @override
  final String? productLinkUrl;
  @override
  final String? productDescription;
  @override
  final bool? isTeacher;
  @override
  final int? teacherId;
  @override
  final int? classId;

  @override
  String toString() {
    return 'liveProductItem(productPictureUrl: $productPictureUrl, productName: $productName, productPrice: $productPrice, productTag: $productTag, productButtonDescription: $productButtonDescription, productLinkUrl: $productLinkUrl, productDescription: $productDescription, isTeacher: $isTeacher, teacherId: $teacherId, classId: $classId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_liveProductItem &&
            (identical(other.productPictureUrl, productPictureUrl) ||
                other.productPictureUrl == productPictureUrl) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productPrice, productPrice) ||
                other.productPrice == productPrice) &&
            (identical(other.productTag, productTag) ||
                other.productTag == productTag) &&
            (identical(
                    other.productButtonDescription, productButtonDescription) ||
                other.productButtonDescription == productButtonDescription) &&
            (identical(other.productLinkUrl, productLinkUrl) ||
                other.productLinkUrl == productLinkUrl) &&
            (identical(other.productDescription, productDescription) ||
                other.productDescription == productDescription) &&
            (identical(other.isTeacher, isTeacher) ||
                other.isTeacher == isTeacher) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.classId, classId) || other.classId == classId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productPictureUrl,
      productName,
      productPrice,
      productTag,
      productButtonDescription,
      productLinkUrl,
      productDescription,
      isTeacher,
      teacherId,
      classId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_liveProductItemCopyWith<_$_liveProductItem> get copyWith =>
      __$$_liveProductItemCopyWithImpl<_$_liveProductItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_liveProductItemToJson(
      this,
    );
  }
}

abstract class _liveProductItem implements liveProductItem {
  const factory _liveProductItem(
      {final String? productPictureUrl,
      final String? productName,
      final String? productPrice,
      final String? productTag,
      final String? productButtonDescription,
      final String? productLinkUrl,
      final String? productDescription,
      final bool? isTeacher,
      final int? teacherId,
      final int? classId}) = _$_liveProductItem;

  factory _liveProductItem.fromJson(Map<String, dynamic> json) =
      _$_liveProductItem.fromJson;

  @override
  String? get productPictureUrl;
  @override
  String? get productName;
  @override
  String? get productPrice;
  @override
  String? get productTag;
  @override
  String? get productButtonDescription;
  @override
  String? get productLinkUrl;
  @override
  String? get productDescription;
  @override
  bool? get isTeacher;
  @override
  int? get teacherId;
  @override
  int? get classId;
  @override
  @JsonKey(ignore: true)
  _$$_liveProductItemCopyWith<_$_liveProductItem> get copyWith =>
      throw _privateConstructorUsedError;
}

teacherInfoData _$teacherInfoDataFromJson(Map<String, dynamic> json) {
  return _teacherInfoData.fromJson(json);
}

/// @nodoc
mixin _$teacherInfoData {
  String? get teacherPictureUrl => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  String? get teacherTag => throw _privateConstructorUsedError;
  String? get teacherButtonDescription => throw _privateConstructorUsedError;
  String? get teacherDescription => throw _privateConstructorUsedError;
  String? get teacherLinkUrl => throw _privateConstructorUsedError;
  String? get teacherPrice => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $teacherInfoDataCopyWith<teacherInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $teacherInfoDataCopyWith<$Res> {
  factory $teacherInfoDataCopyWith(
          teacherInfoData value, $Res Function(teacherInfoData) then) =
      _$teacherInfoDataCopyWithImpl<$Res, teacherInfoData>;
  @useResult
  $Res call(
      {String? teacherPictureUrl,
      String? teacherName,
      String? teacherTag,
      String? teacherButtonDescription,
      String? teacherDescription,
      String? teacherLinkUrl,
      String? teacherPrice,
      int? teacherId,
      int? classId});
}

/// @nodoc
class _$teacherInfoDataCopyWithImpl<$Res, $Val extends teacherInfoData>
    implements $teacherInfoDataCopyWith<$Res> {
  _$teacherInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherPictureUrl = freezed,
    Object? teacherName = freezed,
    Object? teacherTag = freezed,
    Object? teacherButtonDescription = freezed,
    Object? teacherDescription = freezed,
    Object? teacherLinkUrl = freezed,
    Object? teacherPrice = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      teacherPictureUrl: freezed == teacherPictureUrl
          ? _value.teacherPictureUrl
          : teacherPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherTag: freezed == teacherTag
          ? _value.teacherTag
          : teacherTag // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherButtonDescription: freezed == teacherButtonDescription
          ? _value.teacherButtonDescription
          : teacherButtonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherDescription: freezed == teacherDescription
          ? _value.teacherDescription
          : teacherDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherLinkUrl: freezed == teacherLinkUrl
          ? _value.teacherLinkUrl
          : teacherLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherPrice: freezed == teacherPrice
          ? _value.teacherPrice
          : teacherPrice // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_teacherInfoDataCopyWith<$Res>
    implements $teacherInfoDataCopyWith<$Res> {
  factory _$$_teacherInfoDataCopyWith(
          _$_teacherInfoData value, $Res Function(_$_teacherInfoData) then) =
      __$$_teacherInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? teacherPictureUrl,
      String? teacherName,
      String? teacherTag,
      String? teacherButtonDescription,
      String? teacherDescription,
      String? teacherLinkUrl,
      String? teacherPrice,
      int? teacherId,
      int? classId});
}

/// @nodoc
class __$$_teacherInfoDataCopyWithImpl<$Res>
    extends _$teacherInfoDataCopyWithImpl<$Res, _$_teacherInfoData>
    implements _$$_teacherInfoDataCopyWith<$Res> {
  __$$_teacherInfoDataCopyWithImpl(
      _$_teacherInfoData _value, $Res Function(_$_teacherInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherPictureUrl = freezed,
    Object? teacherName = freezed,
    Object? teacherTag = freezed,
    Object? teacherButtonDescription = freezed,
    Object? teacherDescription = freezed,
    Object? teacherLinkUrl = freezed,
    Object? teacherPrice = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_$_teacherInfoData(
      teacherPictureUrl: freezed == teacherPictureUrl
          ? _value.teacherPictureUrl
          : teacherPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherTag: freezed == teacherTag
          ? _value.teacherTag
          : teacherTag // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherButtonDescription: freezed == teacherButtonDescription
          ? _value.teacherButtonDescription
          : teacherButtonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherDescription: freezed == teacherDescription
          ? _value.teacherDescription
          : teacherDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherLinkUrl: freezed == teacherLinkUrl
          ? _value.teacherLinkUrl
          : teacherLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherPrice: freezed == teacherPrice
          ? _value.teacherPrice
          : teacherPrice // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_teacherInfoData implements _teacherInfoData {
  const _$_teacherInfoData(
      {this.teacherPictureUrl,
      this.teacherName,
      this.teacherTag,
      this.teacherButtonDescription,
      this.teacherDescription,
      this.teacherLinkUrl,
      this.teacherPrice,
      this.teacherId,
      this.classId});

  factory _$_teacherInfoData.fromJson(Map<String, dynamic> json) =>
      _$$_teacherInfoDataFromJson(json);

  @override
  final String? teacherPictureUrl;
  @override
  final String? teacherName;
  @override
  final String? teacherTag;
  @override
  final String? teacherButtonDescription;
  @override
  final String? teacherDescription;
  @override
  final String? teacherLinkUrl;
  @override
  final String? teacherPrice;
  @override
  final int? teacherId;
  @override
  final int? classId;

  @override
  String toString() {
    return 'teacherInfoData(teacherPictureUrl: $teacherPictureUrl, teacherName: $teacherName, teacherTag: $teacherTag, teacherButtonDescription: $teacherButtonDescription, teacherDescription: $teacherDescription, teacherLinkUrl: $teacherLinkUrl, teacherPrice: $teacherPrice, teacherId: $teacherId, classId: $classId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_teacherInfoData &&
            (identical(other.teacherPictureUrl, teacherPictureUrl) ||
                other.teacherPictureUrl == teacherPictureUrl) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.teacherTag, teacherTag) ||
                other.teacherTag == teacherTag) &&
            (identical(
                    other.teacherButtonDescription, teacherButtonDescription) ||
                other.teacherButtonDescription == teacherButtonDescription) &&
            (identical(other.teacherDescription, teacherDescription) ||
                other.teacherDescription == teacherDescription) &&
            (identical(other.teacherLinkUrl, teacherLinkUrl) ||
                other.teacherLinkUrl == teacherLinkUrl) &&
            (identical(other.teacherPrice, teacherPrice) ||
                other.teacherPrice == teacherPrice) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.classId, classId) || other.classId == classId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teacherPictureUrl,
      teacherName,
      teacherTag,
      teacherButtonDescription,
      teacherDescription,
      teacherLinkUrl,
      teacherPrice,
      teacherId,
      classId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_teacherInfoDataCopyWith<_$_teacherInfoData> get copyWith =>
      __$$_teacherInfoDataCopyWithImpl<_$_teacherInfoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_teacherInfoDataToJson(
      this,
    );
  }
}

abstract class _teacherInfoData implements teacherInfoData {
  const factory _teacherInfoData(
      {final String? teacherPictureUrl,
      final String? teacherName,
      final String? teacherTag,
      final String? teacherButtonDescription,
      final String? teacherDescription,
      final String? teacherLinkUrl,
      final String? teacherPrice,
      final int? teacherId,
      final int? classId}) = _$_teacherInfoData;

  factory _teacherInfoData.fromJson(Map<String, dynamic> json) =
      _$_teacherInfoData.fromJson;

  @override
  String? get teacherPictureUrl;
  @override
  String? get teacherName;
  @override
  String? get teacherTag;
  @override
  String? get teacherButtonDescription;
  @override
  String? get teacherDescription;
  @override
  String? get teacherLinkUrl;
  @override
  String? get teacherPrice;
  @override
  int? get teacherId;
  @override
  int? get classId;
  @override
  @JsonKey(ignore: true)
  _$$_teacherInfoDataCopyWith<_$_teacherInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanResource _$todayPlanResourceFromJson(Map<String, dynamic> json) {
  return _todayPlanResource.fromJson(json);
}

/// @nodoc
mixin _$todayPlanResource {
  String? get animation => throw _privateConstructorUsedError;
  String? get audio => throw _privateConstructorUsedError;
  String? get defaultText => throw _privateConstructorUsedError;
  String? get defaultIcon => throw _privateConstructorUsedError;
  String? get bgImage => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get activityTitle => throw _privateConstructorUsedError;
  String? get lottieAnimation => throw _privateConstructorUsedError;
  todayPlanResourceAnimation? get foreResource =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanResourceCopyWith<todayPlanResource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanResourceCopyWith<$Res> {
  factory $todayPlanResourceCopyWith(
          todayPlanResource value, $Res Function(todayPlanResource) then) =
      _$todayPlanResourceCopyWithImpl<$Res, todayPlanResource>;
  @useResult
  $Res call(
      {String? animation,
      String? audio,
      String? defaultText,
      String? defaultIcon,
      String? bgImage,
      String? image,
      String? text,
      String? activityTitle,
      String? lottieAnimation,
      todayPlanResourceAnimation? foreResource});

  $todayPlanResourceAnimationCopyWith<$Res>? get foreResource;
}

/// @nodoc
class _$todayPlanResourceCopyWithImpl<$Res, $Val extends todayPlanResource>
    implements $todayPlanResourceCopyWith<$Res> {
  _$todayPlanResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? audio = freezed,
    Object? defaultText = freezed,
    Object? defaultIcon = freezed,
    Object? bgImage = freezed,
    Object? image = freezed,
    Object? text = freezed,
    Object? activityTitle = freezed,
    Object? lottieAnimation = freezed,
    Object? foreResource = freezed,
  }) {
    return _then(_value.copyWith(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultText: freezed == defaultText
          ? _value.defaultText
          : defaultText // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultIcon: freezed == defaultIcon
          ? _value.defaultIcon
          : defaultIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      activityTitle: freezed == activityTitle
          ? _value.activityTitle
          : activityTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lottieAnimation: freezed == lottieAnimation
          ? _value.lottieAnimation
          : lottieAnimation // ignore: cast_nullable_to_non_nullable
              as String?,
      foreResource: freezed == foreResource
          ? _value.foreResource
          : foreResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourceAnimation?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourceAnimationCopyWith<$Res>? get foreResource {
    if (_value.foreResource == null) {
      return null;
    }

    return $todayPlanResourceAnimationCopyWith<$Res>(_value.foreResource!,
        (value) {
      return _then(_value.copyWith(foreResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_todayPlanResourceCopyWith<$Res>
    implements $todayPlanResourceCopyWith<$Res> {
  factory _$$_todayPlanResourceCopyWith(_$_todayPlanResource value,
          $Res Function(_$_todayPlanResource) then) =
      __$$_todayPlanResourceCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? animation,
      String? audio,
      String? defaultText,
      String? defaultIcon,
      String? bgImage,
      String? image,
      String? text,
      String? activityTitle,
      String? lottieAnimation,
      todayPlanResourceAnimation? foreResource});

  @override
  $todayPlanResourceAnimationCopyWith<$Res>? get foreResource;
}

/// @nodoc
class __$$_todayPlanResourceCopyWithImpl<$Res>
    extends _$todayPlanResourceCopyWithImpl<$Res, _$_todayPlanResource>
    implements _$$_todayPlanResourceCopyWith<$Res> {
  __$$_todayPlanResourceCopyWithImpl(
      _$_todayPlanResource _value, $Res Function(_$_todayPlanResource) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? audio = freezed,
    Object? defaultText = freezed,
    Object? defaultIcon = freezed,
    Object? bgImage = freezed,
    Object? image = freezed,
    Object? text = freezed,
    Object? activityTitle = freezed,
    Object? lottieAnimation = freezed,
    Object? foreResource = freezed,
  }) {
    return _then(_$_todayPlanResource(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultText: freezed == defaultText
          ? _value.defaultText
          : defaultText // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultIcon: freezed == defaultIcon
          ? _value.defaultIcon
          : defaultIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      activityTitle: freezed == activityTitle
          ? _value.activityTitle
          : activityTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lottieAnimation: freezed == lottieAnimation
          ? _value.lottieAnimation
          : lottieAnimation // ignore: cast_nullable_to_non_nullable
              as String?,
      foreResource: freezed == foreResource
          ? _value.foreResource
          : foreResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourceAnimation?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanResource implements _todayPlanResource {
  const _$_todayPlanResource(
      {this.animation,
      this.audio,
      this.defaultText,
      this.defaultIcon,
      this.bgImage,
      this.image,
      this.text,
      this.activityTitle,
      this.lottieAnimation,
      this.foreResource});

  factory _$_todayPlanResource.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanResourceFromJson(json);

  @override
  final String? animation;
  @override
  final String? audio;
  @override
  final String? defaultText;
  @override
  final String? defaultIcon;
  @override
  final String? bgImage;
  @override
  final String? image;
  @override
  final String? text;
  @override
  final String? activityTitle;
  @override
  final String? lottieAnimation;
  @override
  final todayPlanResourceAnimation? foreResource;

  @override
  String toString() {
    return 'todayPlanResource(animation: $animation, audio: $audio, defaultText: $defaultText, defaultIcon: $defaultIcon, bgImage: $bgImage, image: $image, text: $text, activityTitle: $activityTitle, lottieAnimation: $lottieAnimation, foreResource: $foreResource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanResource &&
            (identical(other.animation, animation) ||
                other.animation == animation) &&
            (identical(other.audio, audio) || other.audio == audio) &&
            (identical(other.defaultText, defaultText) ||
                other.defaultText == defaultText) &&
            (identical(other.defaultIcon, defaultIcon) ||
                other.defaultIcon == defaultIcon) &&
            (identical(other.bgImage, bgImage) || other.bgImage == bgImage) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.activityTitle, activityTitle) ||
                other.activityTitle == activityTitle) &&
            (identical(other.lottieAnimation, lottieAnimation) ||
                other.lottieAnimation == lottieAnimation) &&
            (identical(other.foreResource, foreResource) ||
                other.foreResource == foreResource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      animation,
      audio,
      defaultText,
      defaultIcon,
      bgImage,
      image,
      text,
      activityTitle,
      lottieAnimation,
      foreResource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanResourceCopyWith<_$_todayPlanResource> get copyWith =>
      __$$_todayPlanResourceCopyWithImpl<_$_todayPlanResource>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanResourceToJson(
      this,
    );
  }
}

abstract class _todayPlanResource implements todayPlanResource {
  const factory _todayPlanResource(
      {final String? animation,
      final String? audio,
      final String? defaultText,
      final String? defaultIcon,
      final String? bgImage,
      final String? image,
      final String? text,
      final String? activityTitle,
      final String? lottieAnimation,
      final todayPlanResourceAnimation? foreResource}) = _$_todayPlanResource;

  factory _todayPlanResource.fromJson(Map<String, dynamic> json) =
      _$_todayPlanResource.fromJson;

  @override
  String? get animation;
  @override
  String? get audio;
  @override
  String? get defaultText;
  @override
  String? get defaultIcon;
  @override
  String? get bgImage;
  @override
  String? get image;
  @override
  String? get text;
  @override
  String? get activityTitle;
  @override
  String? get lottieAnimation;
  @override
  todayPlanResourceAnimation? get foreResource;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanResourceCopyWith<_$_todayPlanResource> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanResourceAnimation _$todayPlanResourceAnimationFromJson(
    Map<String, dynamic> json) {
  return _todayPlanResourceAnimation.fromJson(json);
}

/// @nodoc
mixin _$todayPlanResourceAnimation {
  int? get animationType => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get resourceUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanResourceAnimationCopyWith<todayPlanResourceAnimation>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanResourceAnimationCopyWith<$Res> {
  factory $todayPlanResourceAnimationCopyWith(todayPlanResourceAnimation value,
          $Res Function(todayPlanResourceAnimation) then) =
      _$todayPlanResourceAnimationCopyWithImpl<$Res,
          todayPlanResourceAnimation>;
  @useResult
  $Res call({int? animationType, String? type, String? resourceUrl});
}

/// @nodoc
class _$todayPlanResourceAnimationCopyWithImpl<$Res,
        $Val extends todayPlanResourceAnimation>
    implements $todayPlanResourceAnimationCopyWith<$Res> {
  _$todayPlanResourceAnimationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animationType = freezed,
    Object? type = freezed,
    Object? resourceUrl = freezed,
  }) {
    return _then(_value.copyWith(
      animationType: freezed == animationType
          ? _value.animationType
          : animationType // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_todayPlanResourceAnimationCopyWith<$Res>
    implements $todayPlanResourceAnimationCopyWith<$Res> {
  factory _$$_todayPlanResourceAnimationCopyWith(
          _$_todayPlanResourceAnimation value,
          $Res Function(_$_todayPlanResourceAnimation) then) =
      __$$_todayPlanResourceAnimationCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? animationType, String? type, String? resourceUrl});
}

/// @nodoc
class __$$_todayPlanResourceAnimationCopyWithImpl<$Res>
    extends _$todayPlanResourceAnimationCopyWithImpl<$Res,
        _$_todayPlanResourceAnimation>
    implements _$$_todayPlanResourceAnimationCopyWith<$Res> {
  __$$_todayPlanResourceAnimationCopyWithImpl(
      _$_todayPlanResourceAnimation _value,
      $Res Function(_$_todayPlanResourceAnimation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animationType = freezed,
    Object? type = freezed,
    Object? resourceUrl = freezed,
  }) {
    return _then(_$_todayPlanResourceAnimation(
      animationType: freezed == animationType
          ? _value.animationType
          : animationType // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanResourceAnimation implements _todayPlanResourceAnimation {
  const _$_todayPlanResourceAnimation(
      {this.animationType, this.type, this.resourceUrl});

  factory _$_todayPlanResourceAnimation.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanResourceAnimationFromJson(json);

  @override
  final int? animationType;
  @override
  final String? type;
  @override
  final String? resourceUrl;

  @override
  String toString() {
    return 'todayPlanResourceAnimation(animationType: $animationType, type: $type, resourceUrl: $resourceUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanResourceAnimation &&
            (identical(other.animationType, animationType) ||
                other.animationType == animationType) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.resourceUrl, resourceUrl) ||
                other.resourceUrl == resourceUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, animationType, type, resourceUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanResourceAnimationCopyWith<_$_todayPlanResourceAnimation>
      get copyWith => __$$_todayPlanResourceAnimationCopyWithImpl<
          _$_todayPlanResourceAnimation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanResourceAnimationToJson(
      this,
    );
  }
}

abstract class _todayPlanResourceAnimation
    implements todayPlanResourceAnimation {
  const factory _todayPlanResourceAnimation(
      {final int? animationType,
      final String? type,
      final String? resourceUrl}) = _$_todayPlanResourceAnimation;

  factory _todayPlanResourceAnimation.fromJson(Map<String, dynamic> json) =
      _$_todayPlanResourceAnimation.fromJson;

  @override
  int? get animationType;
  @override
  String? get type;
  @override
  String? get resourceUrl;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanResourceAnimationCopyWith<_$_todayPlanResourceAnimation>
      get copyWith => throw _privateConstructorUsedError;
}

advertisementData _$advertisementDataFromJson(Map<String, dynamic> json) {
  return _advertisementData.fromJson(json);
}

/// @nodoc
mixin _$advertisementData {
  int? get id => throw _privateConstructorUsedError;
  String? get advertisementName => throw _privateConstructorUsedError;
  String? get advertisementPosition => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  List<String>? get closeReasonList => throw _privateConstructorUsedError;
  int? get advertisementId => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $advertisementDataCopyWith<advertisementData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $advertisementDataCopyWith<$Res> {
  factory $advertisementDataCopyWith(
          advertisementData value, $Res Function(advertisementData) then) =
      _$advertisementDataCopyWithImpl<$Res, advertisementData>;
  @useResult
  $Res call(
      {int? id,
      String? advertisementName,
      String? advertisementPosition,
      String? pictureUrl,
      String? linkUrl,
      List<String>? closeReasonList,
      int? advertisementId,
      String? businessTagId});
}

/// @nodoc
class _$advertisementDataCopyWithImpl<$Res, $Val extends advertisementData>
    implements $advertisementDataCopyWith<$Res> {
  _$advertisementDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? advertisementName = freezed,
    Object? advertisementPosition = freezed,
    Object? pictureUrl = freezed,
    Object? linkUrl = freezed,
    Object? closeReasonList = freezed,
    Object? advertisementId = freezed,
    Object? businessTagId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      advertisementName: freezed == advertisementName
          ? _value.advertisementName
          : advertisementName // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementPosition: freezed == advertisementPosition
          ? _value.advertisementPosition
          : advertisementPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      closeReasonList: freezed == closeReasonList
          ? _value.closeReasonList
          : closeReasonList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      advertisementId: freezed == advertisementId
          ? _value.advertisementId
          : advertisementId // ignore: cast_nullable_to_non_nullable
              as int?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_advertisementDataCopyWith<$Res>
    implements $advertisementDataCopyWith<$Res> {
  factory _$$_advertisementDataCopyWith(_$_advertisementData value,
          $Res Function(_$_advertisementData) then) =
      __$$_advertisementDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? advertisementName,
      String? advertisementPosition,
      String? pictureUrl,
      String? linkUrl,
      List<String>? closeReasonList,
      int? advertisementId,
      String? businessTagId});
}

/// @nodoc
class __$$_advertisementDataCopyWithImpl<$Res>
    extends _$advertisementDataCopyWithImpl<$Res, _$_advertisementData>
    implements _$$_advertisementDataCopyWith<$Res> {
  __$$_advertisementDataCopyWithImpl(
      _$_advertisementData _value, $Res Function(_$_advertisementData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? advertisementName = freezed,
    Object? advertisementPosition = freezed,
    Object? pictureUrl = freezed,
    Object? linkUrl = freezed,
    Object? closeReasonList = freezed,
    Object? advertisementId = freezed,
    Object? businessTagId = freezed,
  }) {
    return _then(_$_advertisementData(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      advertisementName: freezed == advertisementName
          ? _value.advertisementName
          : advertisementName // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementPosition: freezed == advertisementPosition
          ? _value.advertisementPosition
          : advertisementPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      closeReasonList: freezed == closeReasonList
          ? _value._closeReasonList
          : closeReasonList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      advertisementId: freezed == advertisementId
          ? _value.advertisementId
          : advertisementId // ignore: cast_nullable_to_non_nullable
              as int?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_advertisementData implements _advertisementData {
  const _$_advertisementData(
      {this.id,
      this.advertisementName,
      this.advertisementPosition,
      this.pictureUrl,
      this.linkUrl,
      final List<String>? closeReasonList,
      this.advertisementId,
      this.businessTagId})
      : _closeReasonList = closeReasonList;

  factory _$_advertisementData.fromJson(Map<String, dynamic> json) =>
      _$$_advertisementDataFromJson(json);

  @override
  final int? id;
  @override
  final String? advertisementName;
  @override
  final String? advertisementPosition;
  @override
  final String? pictureUrl;
  @override
  final String? linkUrl;
  final List<String>? _closeReasonList;
  @override
  List<String>? get closeReasonList {
    final value = _closeReasonList;
    if (value == null) return null;
    if (_closeReasonList is EqualUnmodifiableListView) return _closeReasonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? advertisementId;
  @override
  final String? businessTagId;

  @override
  String toString() {
    return 'advertisementData(id: $id, advertisementName: $advertisementName, advertisementPosition: $advertisementPosition, pictureUrl: $pictureUrl, linkUrl: $linkUrl, closeReasonList: $closeReasonList, advertisementId: $advertisementId, businessTagId: $businessTagId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_advertisementData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.advertisementName, advertisementName) ||
                other.advertisementName == advertisementName) &&
            (identical(other.advertisementPosition, advertisementPosition) ||
                other.advertisementPosition == advertisementPosition) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            const DeepCollectionEquality()
                .equals(other._closeReasonList, _closeReasonList) &&
            (identical(other.advertisementId, advertisementId) ||
                other.advertisementId == advertisementId) &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      advertisementName,
      advertisementPosition,
      pictureUrl,
      linkUrl,
      const DeepCollectionEquality().hash(_closeReasonList),
      advertisementId,
      businessTagId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_advertisementDataCopyWith<_$_advertisementData> get copyWith =>
      __$$_advertisementDataCopyWithImpl<_$_advertisementData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_advertisementDataToJson(
      this,
    );
  }
}

abstract class _advertisementData implements advertisementData {
  const factory _advertisementData(
      {final int? id,
      final String? advertisementName,
      final String? advertisementPosition,
      final String? pictureUrl,
      final String? linkUrl,
      final List<String>? closeReasonList,
      final int? advertisementId,
      final String? businessTagId}) = _$_advertisementData;

  factory _advertisementData.fromJson(Map<String, dynamic> json) =
      _$_advertisementData.fromJson;

  @override
  int? get id;
  @override
  String? get advertisementName;
  @override
  String? get advertisementPosition;
  @override
  String? get pictureUrl;
  @override
  String? get linkUrl;
  @override
  List<String>? get closeReasonList;
  @override
  int? get advertisementId;
  @override
  String? get businessTagId;
  @override
  @JsonKey(ignore: true)
  _$$_advertisementDataCopyWith<_$_advertisementData> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanInfoData _$todayPlanInfoDataFromJson(Map<String, dynamic> json) {
  return _todayPlanInfoData.fromJson(json);
}

/// @nodoc
mixin _$todayPlanInfoData {
  todayPlanResourcedata? get allFinishResource =>
      throw _privateConstructorUsedError;
  todayPlanResourcedata? get latestFinishResource =>
      throw _privateConstructorUsedError;
  todayPlanResourcedata? get nonePlanResource =>
      throw _privateConstructorUsedError;
  int? get processOfAll => throw _privateConstructorUsedError;
  int? get processOfFinish => throw _privateConstructorUsedError;
  int? get status =>
      throw _privateConstructorUsedError; //0不展示，1今日无计划，2今日计划未完成，3今日计划已完成
  bool? get shouldPlayFinishOneAnimal => throw _privateConstructorUsedError;
  bool? get shouldPlayFirstFinishAllAnimal =>
      throw _privateConstructorUsedError;
  bool? get shouldReloadData => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  List<todayPlanCarddata>? get todayPlanCardList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanInfoDataCopyWith<todayPlanInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanInfoDataCopyWith<$Res> {
  factory $todayPlanInfoDataCopyWith(
          todayPlanInfoData value, $Res Function(todayPlanInfoData) then) =
      _$todayPlanInfoDataCopyWithImpl<$Res, todayPlanInfoData>;
  @useResult
  $Res call(
      {todayPlanResourcedata? allFinishResource,
      todayPlanResourcedata? latestFinishResource,
      todayPlanResourcedata? nonePlanResource,
      int? processOfAll,
      int? processOfFinish,
      int? status,
      bool? shouldPlayFinishOneAnimal,
      bool? shouldPlayFirstFinishAllAnimal,
      bool? shouldReloadData,
      String? title,
      List<todayPlanCarddata>? todayPlanCardList});

  $todayPlanResourcedataCopyWith<$Res>? get allFinishResource;
  $todayPlanResourcedataCopyWith<$Res>? get latestFinishResource;
  $todayPlanResourcedataCopyWith<$Res>? get nonePlanResource;
}

/// @nodoc
class _$todayPlanInfoDataCopyWithImpl<$Res, $Val extends todayPlanInfoData>
    implements $todayPlanInfoDataCopyWith<$Res> {
  _$todayPlanInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allFinishResource = freezed,
    Object? latestFinishResource = freezed,
    Object? nonePlanResource = freezed,
    Object? processOfAll = freezed,
    Object? processOfFinish = freezed,
    Object? status = freezed,
    Object? shouldPlayFinishOneAnimal = freezed,
    Object? shouldPlayFirstFinishAllAnimal = freezed,
    Object? shouldReloadData = freezed,
    Object? title = freezed,
    Object? todayPlanCardList = freezed,
  }) {
    return _then(_value.copyWith(
      allFinishResource: freezed == allFinishResource
          ? _value.allFinishResource
          : allFinishResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      latestFinishResource: freezed == latestFinishResource
          ? _value.latestFinishResource
          : latestFinishResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      nonePlanResource: freezed == nonePlanResource
          ? _value.nonePlanResource
          : nonePlanResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      processOfAll: freezed == processOfAll
          ? _value.processOfAll
          : processOfAll // ignore: cast_nullable_to_non_nullable
              as int?,
      processOfFinish: freezed == processOfFinish
          ? _value.processOfFinish
          : processOfFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      shouldPlayFinishOneAnimal: freezed == shouldPlayFinishOneAnimal
          ? _value.shouldPlayFinishOneAnimal
          : shouldPlayFinishOneAnimal // ignore: cast_nullable_to_non_nullable
              as bool?,
      shouldPlayFirstFinishAllAnimal: freezed == shouldPlayFirstFinishAllAnimal
          ? _value.shouldPlayFirstFinishAllAnimal
          : shouldPlayFirstFinishAllAnimal // ignore: cast_nullable_to_non_nullable
              as bool?,
      shouldReloadData: freezed == shouldReloadData
          ? _value.shouldReloadData
          : shouldReloadData // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      todayPlanCardList: freezed == todayPlanCardList
          ? _value.todayPlanCardList
          : todayPlanCardList // ignore: cast_nullable_to_non_nullable
              as List<todayPlanCarddata>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourcedataCopyWith<$Res>? get allFinishResource {
    if (_value.allFinishResource == null) {
      return null;
    }

    return $todayPlanResourcedataCopyWith<$Res>(_value.allFinishResource!,
        (value) {
      return _then(_value.copyWith(allFinishResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourcedataCopyWith<$Res>? get latestFinishResource {
    if (_value.latestFinishResource == null) {
      return null;
    }

    return $todayPlanResourcedataCopyWith<$Res>(_value.latestFinishResource!,
        (value) {
      return _then(_value.copyWith(latestFinishResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourcedataCopyWith<$Res>? get nonePlanResource {
    if (_value.nonePlanResource == null) {
      return null;
    }

    return $todayPlanResourcedataCopyWith<$Res>(_value.nonePlanResource!,
        (value) {
      return _then(_value.copyWith(nonePlanResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_todayPlanInfoDataCopyWith<$Res>
    implements $todayPlanInfoDataCopyWith<$Res> {
  factory _$$_todayPlanInfoDataCopyWith(_$_todayPlanInfoData value,
          $Res Function(_$_todayPlanInfoData) then) =
      __$$_todayPlanInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {todayPlanResourcedata? allFinishResource,
      todayPlanResourcedata? latestFinishResource,
      todayPlanResourcedata? nonePlanResource,
      int? processOfAll,
      int? processOfFinish,
      int? status,
      bool? shouldPlayFinishOneAnimal,
      bool? shouldPlayFirstFinishAllAnimal,
      bool? shouldReloadData,
      String? title,
      List<todayPlanCarddata>? todayPlanCardList});

  @override
  $todayPlanResourcedataCopyWith<$Res>? get allFinishResource;
  @override
  $todayPlanResourcedataCopyWith<$Res>? get latestFinishResource;
  @override
  $todayPlanResourcedataCopyWith<$Res>? get nonePlanResource;
}

/// @nodoc
class __$$_todayPlanInfoDataCopyWithImpl<$Res>
    extends _$todayPlanInfoDataCopyWithImpl<$Res, _$_todayPlanInfoData>
    implements _$$_todayPlanInfoDataCopyWith<$Res> {
  __$$_todayPlanInfoDataCopyWithImpl(
      _$_todayPlanInfoData _value, $Res Function(_$_todayPlanInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allFinishResource = freezed,
    Object? latestFinishResource = freezed,
    Object? nonePlanResource = freezed,
    Object? processOfAll = freezed,
    Object? processOfFinish = freezed,
    Object? status = freezed,
    Object? shouldPlayFinishOneAnimal = freezed,
    Object? shouldPlayFirstFinishAllAnimal = freezed,
    Object? shouldReloadData = freezed,
    Object? title = freezed,
    Object? todayPlanCardList = freezed,
  }) {
    return _then(_$_todayPlanInfoData(
      allFinishResource: freezed == allFinishResource
          ? _value.allFinishResource
          : allFinishResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      latestFinishResource: freezed == latestFinishResource
          ? _value.latestFinishResource
          : latestFinishResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      nonePlanResource: freezed == nonePlanResource
          ? _value.nonePlanResource
          : nonePlanResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResourcedata?,
      processOfAll: freezed == processOfAll
          ? _value.processOfAll
          : processOfAll // ignore: cast_nullable_to_non_nullable
              as int?,
      processOfFinish: freezed == processOfFinish
          ? _value.processOfFinish
          : processOfFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      shouldPlayFinishOneAnimal: freezed == shouldPlayFinishOneAnimal
          ? _value.shouldPlayFinishOneAnimal
          : shouldPlayFinishOneAnimal // ignore: cast_nullable_to_non_nullable
              as bool?,
      shouldPlayFirstFinishAllAnimal: freezed == shouldPlayFirstFinishAllAnimal
          ? _value.shouldPlayFirstFinishAllAnimal
          : shouldPlayFirstFinishAllAnimal // ignore: cast_nullable_to_non_nullable
              as bool?,
      shouldReloadData: freezed == shouldReloadData
          ? _value.shouldReloadData
          : shouldReloadData // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      todayPlanCardList: freezed == todayPlanCardList
          ? _value._todayPlanCardList
          : todayPlanCardList // ignore: cast_nullable_to_non_nullable
              as List<todayPlanCarddata>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanInfoData implements _todayPlanInfoData {
  const _$_todayPlanInfoData(
      {this.allFinishResource,
      this.latestFinishResource,
      this.nonePlanResource,
      this.processOfAll,
      this.processOfFinish,
      this.status,
      this.shouldPlayFinishOneAnimal,
      this.shouldPlayFirstFinishAllAnimal,
      this.shouldReloadData,
      this.title,
      final List<todayPlanCarddata>? todayPlanCardList})
      : _todayPlanCardList = todayPlanCardList;

  factory _$_todayPlanInfoData.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanInfoDataFromJson(json);

  @override
  final todayPlanResourcedata? allFinishResource;
  @override
  final todayPlanResourcedata? latestFinishResource;
  @override
  final todayPlanResourcedata? nonePlanResource;
  @override
  final int? processOfAll;
  @override
  final int? processOfFinish;
  @override
  final int? status;
//0不展示，1今日无计划，2今日计划未完成，3今日计划已完成
  @override
  final bool? shouldPlayFinishOneAnimal;
  @override
  final bool? shouldPlayFirstFinishAllAnimal;
  @override
  final bool? shouldReloadData;
  @override
  final String? title;
  final List<todayPlanCarddata>? _todayPlanCardList;
  @override
  List<todayPlanCarddata>? get todayPlanCardList {
    final value = _todayPlanCardList;
    if (value == null) return null;
    if (_todayPlanCardList is EqualUnmodifiableListView)
      return _todayPlanCardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'todayPlanInfoData(allFinishResource: $allFinishResource, latestFinishResource: $latestFinishResource, nonePlanResource: $nonePlanResource, processOfAll: $processOfAll, processOfFinish: $processOfFinish, status: $status, shouldPlayFinishOneAnimal: $shouldPlayFinishOneAnimal, shouldPlayFirstFinishAllAnimal: $shouldPlayFirstFinishAllAnimal, shouldReloadData: $shouldReloadData, title: $title, todayPlanCardList: $todayPlanCardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanInfoData &&
            (identical(other.allFinishResource, allFinishResource) ||
                other.allFinishResource == allFinishResource) &&
            (identical(other.latestFinishResource, latestFinishResource) ||
                other.latestFinishResource == latestFinishResource) &&
            (identical(other.nonePlanResource, nonePlanResource) ||
                other.nonePlanResource == nonePlanResource) &&
            (identical(other.processOfAll, processOfAll) ||
                other.processOfAll == processOfAll) &&
            (identical(other.processOfFinish, processOfFinish) ||
                other.processOfFinish == processOfFinish) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.shouldPlayFinishOneAnimal,
                    shouldPlayFinishOneAnimal) ||
                other.shouldPlayFinishOneAnimal == shouldPlayFinishOneAnimal) &&
            (identical(other.shouldPlayFirstFinishAllAnimal,
                    shouldPlayFirstFinishAllAnimal) ||
                other.shouldPlayFirstFinishAllAnimal ==
                    shouldPlayFirstFinishAllAnimal) &&
            (identical(other.shouldReloadData, shouldReloadData) ||
                other.shouldReloadData == shouldReloadData) &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality()
                .equals(other._todayPlanCardList, _todayPlanCardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      allFinishResource,
      latestFinishResource,
      nonePlanResource,
      processOfAll,
      processOfFinish,
      status,
      shouldPlayFinishOneAnimal,
      shouldPlayFirstFinishAllAnimal,
      shouldReloadData,
      title,
      const DeepCollectionEquality().hash(_todayPlanCardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanInfoDataCopyWith<_$_todayPlanInfoData> get copyWith =>
      __$$_todayPlanInfoDataCopyWithImpl<_$_todayPlanInfoData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanInfoDataToJson(
      this,
    );
  }
}

abstract class _todayPlanInfoData implements todayPlanInfoData {
  const factory _todayPlanInfoData(
      {final todayPlanResourcedata? allFinishResource,
      final todayPlanResourcedata? latestFinishResource,
      final todayPlanResourcedata? nonePlanResource,
      final int? processOfAll,
      final int? processOfFinish,
      final int? status,
      final bool? shouldPlayFinishOneAnimal,
      final bool? shouldPlayFirstFinishAllAnimal,
      final bool? shouldReloadData,
      final String? title,
      final List<todayPlanCarddata>? todayPlanCardList}) = _$_todayPlanInfoData;

  factory _todayPlanInfoData.fromJson(Map<String, dynamic> json) =
      _$_todayPlanInfoData.fromJson;

  @override
  todayPlanResourcedata? get allFinishResource;
  @override
  todayPlanResourcedata? get latestFinishResource;
  @override
  todayPlanResourcedata? get nonePlanResource;
  @override
  int? get processOfAll;
  @override
  int? get processOfFinish;
  @override
  int? get status;
  @override //0不展示，1今日无计划，2今日计划未完成，3今日计划已完成
  bool? get shouldPlayFinishOneAnimal;
  @override
  bool? get shouldPlayFirstFinishAllAnimal;
  @override
  bool? get shouldReloadData;
  @override
  String? get title;
  @override
  List<todayPlanCarddata>? get todayPlanCardList;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanInfoDataCopyWith<_$_todayPlanInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanResourcedata _$todayPlanResourcedataFromJson(
    Map<String, dynamic> json) {
  return _todayPlanResourcedata.fromJson(json);
}

/// @nodoc
mixin _$todayPlanResourcedata {
  String? get animation => throw _privateConstructorUsedError;
  String? get audio => throw _privateConstructorUsedError;
  String? get defaultText => throw _privateConstructorUsedError;
  String? get defaultIcon => throw _privateConstructorUsedError;
  String? get bgImage => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get activityTitle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanResourcedataCopyWith<todayPlanResourcedata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanResourcedataCopyWith<$Res> {
  factory $todayPlanResourcedataCopyWith(todayPlanResourcedata value,
          $Res Function(todayPlanResourcedata) then) =
      _$todayPlanResourcedataCopyWithImpl<$Res, todayPlanResourcedata>;
  @useResult
  $Res call(
      {String? animation,
      String? audio,
      String? defaultText,
      String? defaultIcon,
      String? bgImage,
      String? image,
      String? text,
      String? activityTitle});
}

/// @nodoc
class _$todayPlanResourcedataCopyWithImpl<$Res,
        $Val extends todayPlanResourcedata>
    implements $todayPlanResourcedataCopyWith<$Res> {
  _$todayPlanResourcedataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? audio = freezed,
    Object? defaultText = freezed,
    Object? defaultIcon = freezed,
    Object? bgImage = freezed,
    Object? image = freezed,
    Object? text = freezed,
    Object? activityTitle = freezed,
  }) {
    return _then(_value.copyWith(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultText: freezed == defaultText
          ? _value.defaultText
          : defaultText // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultIcon: freezed == defaultIcon
          ? _value.defaultIcon
          : defaultIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      activityTitle: freezed == activityTitle
          ? _value.activityTitle
          : activityTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_todayPlanResourcedataCopyWith<$Res>
    implements $todayPlanResourcedataCopyWith<$Res> {
  factory _$$_todayPlanResourcedataCopyWith(_$_todayPlanResourcedata value,
          $Res Function(_$_todayPlanResourcedata) then) =
      __$$_todayPlanResourcedataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? animation,
      String? audio,
      String? defaultText,
      String? defaultIcon,
      String? bgImage,
      String? image,
      String? text,
      String? activityTitle});
}

/// @nodoc
class __$$_todayPlanResourcedataCopyWithImpl<$Res>
    extends _$todayPlanResourcedataCopyWithImpl<$Res, _$_todayPlanResourcedata>
    implements _$$_todayPlanResourcedataCopyWith<$Res> {
  __$$_todayPlanResourcedataCopyWithImpl(_$_todayPlanResourcedata _value,
      $Res Function(_$_todayPlanResourcedata) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? animation = freezed,
    Object? audio = freezed,
    Object? defaultText = freezed,
    Object? defaultIcon = freezed,
    Object? bgImage = freezed,
    Object? image = freezed,
    Object? text = freezed,
    Object? activityTitle = freezed,
  }) {
    return _then(_$_todayPlanResourcedata(
      animation: freezed == animation
          ? _value.animation
          : animation // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultText: freezed == defaultText
          ? _value.defaultText
          : defaultText // ignore: cast_nullable_to_non_nullable
              as String?,
      defaultIcon: freezed == defaultIcon
          ? _value.defaultIcon
          : defaultIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      activityTitle: freezed == activityTitle
          ? _value.activityTitle
          : activityTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanResourcedata implements _todayPlanResourcedata {
  const _$_todayPlanResourcedata(
      {this.animation,
      this.audio,
      this.defaultText,
      this.defaultIcon,
      this.bgImage,
      this.image,
      this.text,
      this.activityTitle});

  factory _$_todayPlanResourcedata.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanResourcedataFromJson(json);

  @override
  final String? animation;
  @override
  final String? audio;
  @override
  final String? defaultText;
  @override
  final String? defaultIcon;
  @override
  final String? bgImage;
  @override
  final String? image;
  @override
  final String? text;
  @override
  final String? activityTitle;

  @override
  String toString() {
    return 'todayPlanResourcedata(animation: $animation, audio: $audio, defaultText: $defaultText, defaultIcon: $defaultIcon, bgImage: $bgImage, image: $image, text: $text, activityTitle: $activityTitle)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanResourcedata &&
            (identical(other.animation, animation) ||
                other.animation == animation) &&
            (identical(other.audio, audio) || other.audio == audio) &&
            (identical(other.defaultText, defaultText) ||
                other.defaultText == defaultText) &&
            (identical(other.defaultIcon, defaultIcon) ||
                other.defaultIcon == defaultIcon) &&
            (identical(other.bgImage, bgImage) || other.bgImage == bgImage) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.activityTitle, activityTitle) ||
                other.activityTitle == activityTitle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, animation, audio, defaultText,
      defaultIcon, bgImage, image, text, activityTitle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanResourcedataCopyWith<_$_todayPlanResourcedata> get copyWith =>
      __$$_todayPlanResourcedataCopyWithImpl<_$_todayPlanResourcedata>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanResourcedataToJson(
      this,
    );
  }
}

abstract class _todayPlanResourcedata implements todayPlanResourcedata {
  const factory _todayPlanResourcedata(
      {final String? animation,
      final String? audio,
      final String? defaultText,
      final String? defaultIcon,
      final String? bgImage,
      final String? image,
      final String? text,
      final String? activityTitle}) = _$_todayPlanResourcedata;

  factory _todayPlanResourcedata.fromJson(Map<String, dynamic> json) =
      _$_todayPlanResourcedata.fromJson;

  @override
  String? get animation;
  @override
  String? get audio;
  @override
  String? get defaultText;
  @override
  String? get defaultIcon;
  @override
  String? get bgImage;
  @override
  String? get image;
  @override
  String? get text;
  @override
  String? get activityTitle;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanResourcedataCopyWith<_$_todayPlanResourcedata> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanCarddata _$todayPlanCarddataFromJson(Map<String, dynamic> json) {
  return _todayPlanCarddata.fromJson(json);
}

/// @nodoc
mixin _$todayPlanCarddata {
  int? get userCourseId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get mainColor => throw _privateConstructorUsedError;
  String? get bgColor => throw _privateConstructorUsedError;
  String? get fontColor => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get encourageResource => throw _privateConstructorUsedError;
  String? get lessonEntranceImage => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String? get lessonOrder => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  bool? get isFinishedStudy => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError; // 1. 学习 2. 活动
  todayPlanResource? get guideResource => throw _privateConstructorUsedError;
  todayPlanTaskInfo? get todayTaskInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanCarddataCopyWith<todayPlanCarddata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanCarddataCopyWith<$Res> {
  factory $todayPlanCarddataCopyWith(
          todayPlanCarddata value, $Res Function(todayPlanCarddata) then) =
      _$todayPlanCarddataCopyWithImpl<$Res, todayPlanCarddata>;
  @useResult
  $Res call(
      {int? userCourseId,
      int? classId,
      String? mainColor,
      String? bgColor,
      String? fontColor,
      String? btnText,
      String? encourageResource,
      String? lessonEntranceImage,
      String? lessonName,
      String? lessonOrder,
      String? segmentName,
      String? subjectTypeDesc,
      String? route,
      String? title,
      String? courseKey,
      bool? isFinishedStudy,
      int? type,
      todayPlanResource? guideResource,
      todayPlanTaskInfo? todayTaskInfo});

  $todayPlanResourceCopyWith<$Res>? get guideResource;
  $todayPlanTaskInfoCopyWith<$Res>? get todayTaskInfo;
}

/// @nodoc
class _$todayPlanCarddataCopyWithImpl<$Res, $Val extends todayPlanCarddata>
    implements $todayPlanCarddataCopyWith<$Res> {
  _$todayPlanCarddataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCourseId = freezed,
    Object? classId = freezed,
    Object? mainColor = freezed,
    Object? bgColor = freezed,
    Object? fontColor = freezed,
    Object? btnText = freezed,
    Object? encourageResource = freezed,
    Object? lessonEntranceImage = freezed,
    Object? lessonName = freezed,
    Object? lessonOrder = freezed,
    Object? segmentName = freezed,
    Object? subjectTypeDesc = freezed,
    Object? route = freezed,
    Object? title = freezed,
    Object? courseKey = freezed,
    Object? isFinishedStudy = freezed,
    Object? type = freezed,
    Object? guideResource = freezed,
    Object? todayTaskInfo = freezed,
  }) {
    return _then(_value.copyWith(
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      fontColor: freezed == fontColor
          ? _value.fontColor
          : fontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      encourageResource: freezed == encourageResource
          ? _value.encourageResource
          : encourageResource // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonEntranceImage: freezed == lessonEntranceImage
          ? _value.lessonEntranceImage
          : lessonEntranceImage // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinishedStudy: freezed == isFinishedStudy
          ? _value.isFinishedStudy
          : isFinishedStudy // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      guideResource: freezed == guideResource
          ? _value.guideResource
          : guideResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResource?,
      todayTaskInfo: freezed == todayTaskInfo
          ? _value.todayTaskInfo
          : todayTaskInfo // ignore: cast_nullable_to_non_nullable
              as todayPlanTaskInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanResourceCopyWith<$Res>? get guideResource {
    if (_value.guideResource == null) {
      return null;
    }

    return $todayPlanResourceCopyWith<$Res>(_value.guideResource!, (value) {
      return _then(_value.copyWith(guideResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $todayPlanTaskInfoCopyWith<$Res>? get todayTaskInfo {
    if (_value.todayTaskInfo == null) {
      return null;
    }

    return $todayPlanTaskInfoCopyWith<$Res>(_value.todayTaskInfo!, (value) {
      return _then(_value.copyWith(todayTaskInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_todayPlanCarddataCopyWith<$Res>
    implements $todayPlanCarddataCopyWith<$Res> {
  factory _$$_todayPlanCarddataCopyWith(_$_todayPlanCarddata value,
          $Res Function(_$_todayPlanCarddata) then) =
      __$$_todayPlanCarddataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userCourseId,
      int? classId,
      String? mainColor,
      String? bgColor,
      String? fontColor,
      String? btnText,
      String? encourageResource,
      String? lessonEntranceImage,
      String? lessonName,
      String? lessonOrder,
      String? segmentName,
      String? subjectTypeDesc,
      String? route,
      String? title,
      String? courseKey,
      bool? isFinishedStudy,
      int? type,
      todayPlanResource? guideResource,
      todayPlanTaskInfo? todayTaskInfo});

  @override
  $todayPlanResourceCopyWith<$Res>? get guideResource;
  @override
  $todayPlanTaskInfoCopyWith<$Res>? get todayTaskInfo;
}

/// @nodoc
class __$$_todayPlanCarddataCopyWithImpl<$Res>
    extends _$todayPlanCarddataCopyWithImpl<$Res, _$_todayPlanCarddata>
    implements _$$_todayPlanCarddataCopyWith<$Res> {
  __$$_todayPlanCarddataCopyWithImpl(
      _$_todayPlanCarddata _value, $Res Function(_$_todayPlanCarddata) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCourseId = freezed,
    Object? classId = freezed,
    Object? mainColor = freezed,
    Object? bgColor = freezed,
    Object? fontColor = freezed,
    Object? btnText = freezed,
    Object? encourageResource = freezed,
    Object? lessonEntranceImage = freezed,
    Object? lessonName = freezed,
    Object? lessonOrder = freezed,
    Object? segmentName = freezed,
    Object? subjectTypeDesc = freezed,
    Object? route = freezed,
    Object? title = freezed,
    Object? courseKey = freezed,
    Object? isFinishedStudy = freezed,
    Object? type = freezed,
    Object? guideResource = freezed,
    Object? todayTaskInfo = freezed,
  }) {
    return _then(_$_todayPlanCarddata(
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      fontColor: freezed == fontColor
          ? _value.fontColor
          : fontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      encourageResource: freezed == encourageResource
          ? _value.encourageResource
          : encourageResource // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonEntranceImage: freezed == lessonEntranceImage
          ? _value.lessonEntranceImage
          : lessonEntranceImage // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinishedStudy: freezed == isFinishedStudy
          ? _value.isFinishedStudy
          : isFinishedStudy // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      guideResource: freezed == guideResource
          ? _value.guideResource
          : guideResource // ignore: cast_nullable_to_non_nullable
              as todayPlanResource?,
      todayTaskInfo: freezed == todayTaskInfo
          ? _value.todayTaskInfo
          : todayTaskInfo // ignore: cast_nullable_to_non_nullable
              as todayPlanTaskInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanCarddata implements _todayPlanCarddata {
  const _$_todayPlanCarddata(
      {this.userCourseId,
      this.classId,
      this.mainColor,
      this.bgColor,
      this.fontColor,
      this.btnText,
      this.encourageResource,
      this.lessonEntranceImage,
      this.lessonName,
      this.lessonOrder,
      this.segmentName,
      this.subjectTypeDesc,
      this.route,
      this.title,
      this.courseKey,
      this.isFinishedStudy,
      this.type,
      this.guideResource,
      this.todayTaskInfo});

  factory _$_todayPlanCarddata.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanCarddataFromJson(json);

  @override
  final int? userCourseId;
  @override
  final int? classId;
  @override
  final String? mainColor;
  @override
  final String? bgColor;
  @override
  final String? fontColor;
  @override
  final String? btnText;
  @override
  final String? encourageResource;
  @override
  final String? lessonEntranceImage;
  @override
  final String? lessonName;
  @override
  final String? lessonOrder;
  @override
  final String? segmentName;
  @override
  final String? subjectTypeDesc;
  @override
  final String? route;
  @override
  final String? title;
  @override
  final String? courseKey;
  @override
  final bool? isFinishedStudy;
  @override
  final int? type;
// 1. 学习 2. 活动
  @override
  final todayPlanResource? guideResource;
  @override
  final todayPlanTaskInfo? todayTaskInfo;

  @override
  String toString() {
    return 'todayPlanCarddata(userCourseId: $userCourseId, classId: $classId, mainColor: $mainColor, bgColor: $bgColor, fontColor: $fontColor, btnText: $btnText, encourageResource: $encourageResource, lessonEntranceImage: $lessonEntranceImage, lessonName: $lessonName, lessonOrder: $lessonOrder, segmentName: $segmentName, subjectTypeDesc: $subjectTypeDesc, route: $route, title: $title, courseKey: $courseKey, isFinishedStudy: $isFinishedStudy, type: $type, guideResource: $guideResource, todayTaskInfo: $todayTaskInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanCarddata &&
            (identical(other.userCourseId, userCourseId) ||
                other.userCourseId == userCourseId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.mainColor, mainColor) ||
                other.mainColor == mainColor) &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor) &&
            (identical(other.fontColor, fontColor) ||
                other.fontColor == fontColor) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.encourageResource, encourageResource) ||
                other.encourageResource == encourageResource) &&
            (identical(other.lessonEntranceImage, lessonEntranceImage) ||
                other.lessonEntranceImage == lessonEntranceImage) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.isFinishedStudy, isFinishedStudy) ||
                other.isFinishedStudy == isFinishedStudy) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.guideResource, guideResource) ||
                other.guideResource == guideResource) &&
            (identical(other.todayTaskInfo, todayTaskInfo) ||
                other.todayTaskInfo == todayTaskInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        userCourseId,
        classId,
        mainColor,
        bgColor,
        fontColor,
        btnText,
        encourageResource,
        lessonEntranceImage,
        lessonName,
        lessonOrder,
        segmentName,
        subjectTypeDesc,
        route,
        title,
        courseKey,
        isFinishedStudy,
        type,
        guideResource,
        todayTaskInfo
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanCarddataCopyWith<_$_todayPlanCarddata> get copyWith =>
      __$$_todayPlanCarddataCopyWithImpl<_$_todayPlanCarddata>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanCarddataToJson(
      this,
    );
  }
}

abstract class _todayPlanCarddata implements todayPlanCarddata {
  const factory _todayPlanCarddata(
      {final int? userCourseId,
      final int? classId,
      final String? mainColor,
      final String? bgColor,
      final String? fontColor,
      final String? btnText,
      final String? encourageResource,
      final String? lessonEntranceImage,
      final String? lessonName,
      final String? lessonOrder,
      final String? segmentName,
      final String? subjectTypeDesc,
      final String? route,
      final String? title,
      final String? courseKey,
      final bool? isFinishedStudy,
      final int? type,
      final todayPlanResource? guideResource,
      final todayPlanTaskInfo? todayTaskInfo}) = _$_todayPlanCarddata;

  factory _todayPlanCarddata.fromJson(Map<String, dynamic> json) =
      _$_todayPlanCarddata.fromJson;

  @override
  int? get userCourseId;
  @override
  int? get classId;
  @override
  String? get mainColor;
  @override
  String? get bgColor;
  @override
  String? get fontColor;
  @override
  String? get btnText;
  @override
  String? get encourageResource;
  @override
  String? get lessonEntranceImage;
  @override
  String? get lessonName;
  @override
  String? get lessonOrder;
  @override
  String? get segmentName;
  @override
  String? get subjectTypeDesc;
  @override
  String? get route;
  @override
  String? get title;
  @override
  String? get courseKey;
  @override
  bool? get isFinishedStudy;
  @override
  int? get type;
  @override // 1. 学习 2. 活动
  todayPlanResource? get guideResource;
  @override
  todayPlanTaskInfo? get todayTaskInfo;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanCarddataCopyWith<_$_todayPlanCarddata> get copyWith =>
      throw _privateConstructorUsedError;
}

messageBoxInfo _$messageBoxInfoFromJson(Map<String, dynamic> json) {
  return _messageBoxInfo.fromJson(json);
}

/// @nodoc
mixin _$messageBoxInfo {
  String? get smallBubbleLinUrl => throw _privateConstructorUsedError;
  bool? get newMessage => throw _privateConstructorUsedError;
  List<messageBoxListInfo>? get messageList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $messageBoxInfoCopyWith<messageBoxInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $messageBoxInfoCopyWith<$Res> {
  factory $messageBoxInfoCopyWith(
          messageBoxInfo value, $Res Function(messageBoxInfo) then) =
      _$messageBoxInfoCopyWithImpl<$Res, messageBoxInfo>;
  @useResult
  $Res call(
      {String? smallBubbleLinUrl,
      bool? newMessage,
      List<messageBoxListInfo>? messageList});
}

/// @nodoc
class _$messageBoxInfoCopyWithImpl<$Res, $Val extends messageBoxInfo>
    implements $messageBoxInfoCopyWith<$Res> {
  _$messageBoxInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? smallBubbleLinUrl = freezed,
    Object? newMessage = freezed,
    Object? messageList = freezed,
  }) {
    return _then(_value.copyWith(
      smallBubbleLinUrl: freezed == smallBubbleLinUrl
          ? _value.smallBubbleLinUrl
          : smallBubbleLinUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      newMessage: freezed == newMessage
          ? _value.newMessage
          : newMessage // ignore: cast_nullable_to_non_nullable
              as bool?,
      messageList: freezed == messageList
          ? _value.messageList
          : messageList // ignore: cast_nullable_to_non_nullable
              as List<messageBoxListInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_messageBoxInfoCopyWith<$Res>
    implements $messageBoxInfoCopyWith<$Res> {
  factory _$$_messageBoxInfoCopyWith(
          _$_messageBoxInfo value, $Res Function(_$_messageBoxInfo) then) =
      __$$_messageBoxInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? smallBubbleLinUrl,
      bool? newMessage,
      List<messageBoxListInfo>? messageList});
}

/// @nodoc
class __$$_messageBoxInfoCopyWithImpl<$Res>
    extends _$messageBoxInfoCopyWithImpl<$Res, _$_messageBoxInfo>
    implements _$$_messageBoxInfoCopyWith<$Res> {
  __$$_messageBoxInfoCopyWithImpl(
      _$_messageBoxInfo _value, $Res Function(_$_messageBoxInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? smallBubbleLinUrl = freezed,
    Object? newMessage = freezed,
    Object? messageList = freezed,
  }) {
    return _then(_$_messageBoxInfo(
      smallBubbleLinUrl: freezed == smallBubbleLinUrl
          ? _value.smallBubbleLinUrl
          : smallBubbleLinUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      newMessage: freezed == newMessage
          ? _value.newMessage
          : newMessage // ignore: cast_nullable_to_non_nullable
              as bool?,
      messageList: freezed == messageList
          ? _value._messageList
          : messageList // ignore: cast_nullable_to_non_nullable
              as List<messageBoxListInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_messageBoxInfo implements _messageBoxInfo {
  const _$_messageBoxInfo(
      {this.smallBubbleLinUrl,
      this.newMessage,
      final List<messageBoxListInfo>? messageList})
      : _messageList = messageList;

  factory _$_messageBoxInfo.fromJson(Map<String, dynamic> json) =>
      _$$_messageBoxInfoFromJson(json);

  @override
  final String? smallBubbleLinUrl;
  @override
  final bool? newMessage;
  final List<messageBoxListInfo>? _messageList;
  @override
  List<messageBoxListInfo>? get messageList {
    final value = _messageList;
    if (value == null) return null;
    if (_messageList is EqualUnmodifiableListView) return _messageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'messageBoxInfo(smallBubbleLinUrl: $smallBubbleLinUrl, newMessage: $newMessage, messageList: $messageList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_messageBoxInfo &&
            (identical(other.smallBubbleLinUrl, smallBubbleLinUrl) ||
                other.smallBubbleLinUrl == smallBubbleLinUrl) &&
            (identical(other.newMessage, newMessage) ||
                other.newMessage == newMessage) &&
            const DeepCollectionEquality()
                .equals(other._messageList, _messageList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, smallBubbleLinUrl, newMessage,
      const DeepCollectionEquality().hash(_messageList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_messageBoxInfoCopyWith<_$_messageBoxInfo> get copyWith =>
      __$$_messageBoxInfoCopyWithImpl<_$_messageBoxInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_messageBoxInfoToJson(
      this,
    );
  }
}

abstract class _messageBoxInfo implements messageBoxInfo {
  const factory _messageBoxInfo(
      {final String? smallBubbleLinUrl,
      final bool? newMessage,
      final List<messageBoxListInfo>? messageList}) = _$_messageBoxInfo;

  factory _messageBoxInfo.fromJson(Map<String, dynamic> json) =
      _$_messageBoxInfo.fromJson;

  @override
  String? get smallBubbleLinUrl;
  @override
  bool? get newMessage;
  @override
  List<messageBoxListInfo>? get messageList;
  @override
  @JsonKey(ignore: true)
  _$$_messageBoxInfoCopyWith<_$_messageBoxInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

messageBoxListInfo _$messageBoxListInfoFromJson(Map<String, dynamic> json) {
  return _messageBoxListInfo.fromJson(json);
}

/// @nodoc
mixin _$messageBoxListInfo {
  String? get bizId => throw _privateConstructorUsedError;
  String? get messageType => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get messageContent => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  int? get classTeacherNotifyId => throw _privateConstructorUsedError;
  int? get notifyId => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  messageBoxItemExtendContent? get extendContent =>
      throw _privateConstructorUsedError;
  bool? get addTeacher => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $messageBoxListInfoCopyWith<messageBoxListInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $messageBoxListInfoCopyWith<$Res> {
  factory $messageBoxListInfoCopyWith(
          messageBoxListInfo value, $Res Function(messageBoxListInfo) then) =
      _$messageBoxListInfoCopyWithImpl<$Res, messageBoxListInfo>;
  @useResult
  $Res call(
      {String? bizId,
      String? messageType,
      String? title,
      String? pictureUrl,
      String? messageContent,
      String? linkUrl,
      String? courseSegment,
      int? classTeacherNotifyId,
      int? notifyId,
      int? teacherId,
      int? classId,
      messageBoxItemExtendContent? extendContent,
      bool? addTeacher});

  $messageBoxItemExtendContentCopyWith<$Res>? get extendContent;
}

/// @nodoc
class _$messageBoxListInfoCopyWithImpl<$Res, $Val extends messageBoxListInfo>
    implements $messageBoxListInfoCopyWith<$Res> {
  _$messageBoxListInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bizId = freezed,
    Object? messageType = freezed,
    Object? title = freezed,
    Object? pictureUrl = freezed,
    Object? messageContent = freezed,
    Object? linkUrl = freezed,
    Object? courseSegment = freezed,
    Object? classTeacherNotifyId = freezed,
    Object? notifyId = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
    Object? extendContent = freezed,
    Object? addTeacher = freezed,
  }) {
    return _then(_value.copyWith(
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacherNotifyId: freezed == classTeacherNotifyId
          ? _value.classTeacherNotifyId
          : classTeacherNotifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      notifyId: freezed == notifyId
          ? _value.notifyId
          : notifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      extendContent: freezed == extendContent
          ? _value.extendContent
          : extendContent // ignore: cast_nullable_to_non_nullable
              as messageBoxItemExtendContent?,
      addTeacher: freezed == addTeacher
          ? _value.addTeacher
          : addTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $messageBoxItemExtendContentCopyWith<$Res>? get extendContent {
    if (_value.extendContent == null) {
      return null;
    }

    return $messageBoxItemExtendContentCopyWith<$Res>(_value.extendContent!,
        (value) {
      return _then(_value.copyWith(extendContent: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_messageBoxListInfoCopyWith<$Res>
    implements $messageBoxListInfoCopyWith<$Res> {
  factory _$$_messageBoxListInfoCopyWith(_$_messageBoxListInfo value,
          $Res Function(_$_messageBoxListInfo) then) =
      __$$_messageBoxListInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? bizId,
      String? messageType,
      String? title,
      String? pictureUrl,
      String? messageContent,
      String? linkUrl,
      String? courseSegment,
      int? classTeacherNotifyId,
      int? notifyId,
      int? teacherId,
      int? classId,
      messageBoxItemExtendContent? extendContent,
      bool? addTeacher});

  @override
  $messageBoxItemExtendContentCopyWith<$Res>? get extendContent;
}

/// @nodoc
class __$$_messageBoxListInfoCopyWithImpl<$Res>
    extends _$messageBoxListInfoCopyWithImpl<$Res, _$_messageBoxListInfo>
    implements _$$_messageBoxListInfoCopyWith<$Res> {
  __$$_messageBoxListInfoCopyWithImpl(
      _$_messageBoxListInfo _value, $Res Function(_$_messageBoxListInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bizId = freezed,
    Object? messageType = freezed,
    Object? title = freezed,
    Object? pictureUrl = freezed,
    Object? messageContent = freezed,
    Object? linkUrl = freezed,
    Object? courseSegment = freezed,
    Object? classTeacherNotifyId = freezed,
    Object? notifyId = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
    Object? extendContent = freezed,
    Object? addTeacher = freezed,
  }) {
    return _then(_$_messageBoxListInfo(
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacherNotifyId: freezed == classTeacherNotifyId
          ? _value.classTeacherNotifyId
          : classTeacherNotifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      notifyId: freezed == notifyId
          ? _value.notifyId
          : notifyId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      extendContent: freezed == extendContent
          ? _value.extendContent
          : extendContent // ignore: cast_nullable_to_non_nullable
              as messageBoxItemExtendContent?,
      addTeacher: freezed == addTeacher
          ? _value.addTeacher
          : addTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_messageBoxListInfo implements _messageBoxListInfo {
  const _$_messageBoxListInfo(
      {this.bizId,
      this.messageType,
      this.title,
      this.pictureUrl,
      this.messageContent,
      this.linkUrl,
      this.courseSegment,
      this.classTeacherNotifyId,
      this.notifyId,
      this.teacherId,
      this.classId,
      this.extendContent,
      this.addTeacher});

  factory _$_messageBoxListInfo.fromJson(Map<String, dynamic> json) =>
      _$$_messageBoxListInfoFromJson(json);

  @override
  final String? bizId;
  @override
  final String? messageType;
  @override
  final String? title;
  @override
  final String? pictureUrl;
  @override
  final String? messageContent;
  @override
  final String? linkUrl;
  @override
  final String? courseSegment;
  @override
  final int? classTeacherNotifyId;
  @override
  final int? notifyId;
  @override
  final int? teacherId;
  @override
  final int? classId;
  @override
  final messageBoxItemExtendContent? extendContent;
  @override
  final bool? addTeacher;

  @override
  String toString() {
    return 'messageBoxListInfo(bizId: $bizId, messageType: $messageType, title: $title, pictureUrl: $pictureUrl, messageContent: $messageContent, linkUrl: $linkUrl, courseSegment: $courseSegment, classTeacherNotifyId: $classTeacherNotifyId, notifyId: $notifyId, teacherId: $teacherId, classId: $classId, extendContent: $extendContent, addTeacher: $addTeacher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_messageBoxListInfo &&
            (identical(other.bizId, bizId) || other.bizId == bizId) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.classTeacherNotifyId, classTeacherNotifyId) ||
                other.classTeacherNotifyId == classTeacherNotifyId) &&
            (identical(other.notifyId, notifyId) ||
                other.notifyId == notifyId) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.extendContent, extendContent) ||
                other.extendContent == extendContent) &&
            (identical(other.addTeacher, addTeacher) ||
                other.addTeacher == addTeacher));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      bizId,
      messageType,
      title,
      pictureUrl,
      messageContent,
      linkUrl,
      courseSegment,
      classTeacherNotifyId,
      notifyId,
      teacherId,
      classId,
      extendContent,
      addTeacher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_messageBoxListInfoCopyWith<_$_messageBoxListInfo> get copyWith =>
      __$$_messageBoxListInfoCopyWithImpl<_$_messageBoxListInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_messageBoxListInfoToJson(
      this,
    );
  }
}

abstract class _messageBoxListInfo implements messageBoxListInfo {
  const factory _messageBoxListInfo(
      {final String? bizId,
      final String? messageType,
      final String? title,
      final String? pictureUrl,
      final String? messageContent,
      final String? linkUrl,
      final String? courseSegment,
      final int? classTeacherNotifyId,
      final int? notifyId,
      final int? teacherId,
      final int? classId,
      final messageBoxItemExtendContent? extendContent,
      final bool? addTeacher}) = _$_messageBoxListInfo;

  factory _messageBoxListInfo.fromJson(Map<String, dynamic> json) =
      _$_messageBoxListInfo.fromJson;

  @override
  String? get bizId;
  @override
  String? get messageType;
  @override
  String? get title;
  @override
  String? get pictureUrl;
  @override
  String? get messageContent;
  @override
  String? get linkUrl;
  @override
  String? get courseSegment;
  @override
  int? get classTeacherNotifyId;
  @override
  int? get notifyId;
  @override
  int? get teacherId;
  @override
  int? get classId;
  @override
  messageBoxItemExtendContent? get extendContent;
  @override
  bool? get addTeacher;
  @override
  @JsonKey(ignore: true)
  _$$_messageBoxListInfoCopyWith<_$_messageBoxListInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

messageBoxItemExtendContent _$messageBoxItemExtendContentFromJson(
    Map<String, dynamic> json) {
  return _messageBoxItemExtendContent.fromJson(json);
}

/// @nodoc
mixin _$messageBoxItemExtendContent {
  int? get showExtentContent => throw _privateConstructorUsedError;
  String? get contactTeacherText => throw _privateConstructorUsedError;
  String? get contactTeacherUrl => throw _privateConstructorUsedError;
  String? get teacherServiceText => throw _privateConstructorUsedError;
  String? get teacherServiceUrl => throw _privateConstructorUsedError;
  String? get teacherAvatar => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $messageBoxItemExtendContentCopyWith<messageBoxItemExtendContent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $messageBoxItemExtendContentCopyWith<$Res> {
  factory $messageBoxItemExtendContentCopyWith(
          messageBoxItemExtendContent value,
          $Res Function(messageBoxItemExtendContent) then) =
      _$messageBoxItemExtendContentCopyWithImpl<$Res,
          messageBoxItemExtendContent>;
  @useResult
  $Res call(
      {int? showExtentContent,
      String? contactTeacherText,
      String? contactTeacherUrl,
      String? teacherServiceText,
      String? teacherServiceUrl,
      String? teacherAvatar,
      String? teacherName});
}

/// @nodoc
class _$messageBoxItemExtendContentCopyWithImpl<$Res,
        $Val extends messageBoxItemExtendContent>
    implements $messageBoxItemExtendContentCopyWith<$Res> {
  _$messageBoxItemExtendContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showExtentContent = freezed,
    Object? contactTeacherText = freezed,
    Object? contactTeacherUrl = freezed,
    Object? teacherServiceText = freezed,
    Object? teacherServiceUrl = freezed,
    Object? teacherAvatar = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_value.copyWith(
      showExtentContent: freezed == showExtentContent
          ? _value.showExtentContent
          : showExtentContent // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherText: freezed == contactTeacherText
          ? _value.contactTeacherText
          : contactTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceText: freezed == teacherServiceText
          ? _value.teacherServiceText
          : teacherServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceUrl: freezed == teacherServiceUrl
          ? _value.teacherServiceUrl
          : teacherServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_messageBoxItemExtendContentCopyWith<$Res>
    implements $messageBoxItemExtendContentCopyWith<$Res> {
  factory _$$_messageBoxItemExtendContentCopyWith(
          _$_messageBoxItemExtendContent value,
          $Res Function(_$_messageBoxItemExtendContent) then) =
      __$$_messageBoxItemExtendContentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? showExtentContent,
      String? contactTeacherText,
      String? contactTeacherUrl,
      String? teacherServiceText,
      String? teacherServiceUrl,
      String? teacherAvatar,
      String? teacherName});
}

/// @nodoc
class __$$_messageBoxItemExtendContentCopyWithImpl<$Res>
    extends _$messageBoxItemExtendContentCopyWithImpl<$Res,
        _$_messageBoxItemExtendContent>
    implements _$$_messageBoxItemExtendContentCopyWith<$Res> {
  __$$_messageBoxItemExtendContentCopyWithImpl(
      _$_messageBoxItemExtendContent _value,
      $Res Function(_$_messageBoxItemExtendContent) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showExtentContent = freezed,
    Object? contactTeacherText = freezed,
    Object? contactTeacherUrl = freezed,
    Object? teacherServiceText = freezed,
    Object? teacherServiceUrl = freezed,
    Object? teacherAvatar = freezed,
    Object? teacherName = freezed,
  }) {
    return _then(_$_messageBoxItemExtendContent(
      showExtentContent: freezed == showExtentContent
          ? _value.showExtentContent
          : showExtentContent // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherText: freezed == contactTeacherText
          ? _value.contactTeacherText
          : contactTeacherText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceText: freezed == teacherServiceText
          ? _value.teacherServiceText
          : teacherServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherServiceUrl: freezed == teacherServiceUrl
          ? _value.teacherServiceUrl
          : teacherServiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_messageBoxItemExtendContent implements _messageBoxItemExtendContent {
  const _$_messageBoxItemExtendContent(
      {this.showExtentContent,
      this.contactTeacherText,
      this.contactTeacherUrl,
      this.teacherServiceText,
      this.teacherServiceUrl,
      this.teacherAvatar,
      this.teacherName});

  factory _$_messageBoxItemExtendContent.fromJson(Map<String, dynamic> json) =>
      _$$_messageBoxItemExtendContentFromJson(json);

  @override
  final int? showExtentContent;
  @override
  final String? contactTeacherText;
  @override
  final String? contactTeacherUrl;
  @override
  final String? teacherServiceText;
  @override
  final String? teacherServiceUrl;
  @override
  final String? teacherAvatar;
  @override
  final String? teacherName;

  @override
  String toString() {
    return 'messageBoxItemExtendContent(showExtentContent: $showExtentContent, contactTeacherText: $contactTeacherText, contactTeacherUrl: $contactTeacherUrl, teacherServiceText: $teacherServiceText, teacherServiceUrl: $teacherServiceUrl, teacherAvatar: $teacherAvatar, teacherName: $teacherName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_messageBoxItemExtendContent &&
            (identical(other.showExtentContent, showExtentContent) ||
                other.showExtentContent == showExtentContent) &&
            (identical(other.contactTeacherText, contactTeacherText) ||
                other.contactTeacherText == contactTeacherText) &&
            (identical(other.contactTeacherUrl, contactTeacherUrl) ||
                other.contactTeacherUrl == contactTeacherUrl) &&
            (identical(other.teacherServiceText, teacherServiceText) ||
                other.teacherServiceText == teacherServiceText) &&
            (identical(other.teacherServiceUrl, teacherServiceUrl) ||
                other.teacherServiceUrl == teacherServiceUrl) &&
            (identical(other.teacherAvatar, teacherAvatar) ||
                other.teacherAvatar == teacherAvatar) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showExtentContent,
      contactTeacherText,
      contactTeacherUrl,
      teacherServiceText,
      teacherServiceUrl,
      teacherAvatar,
      teacherName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_messageBoxItemExtendContentCopyWith<_$_messageBoxItemExtendContent>
      get copyWith => __$$_messageBoxItemExtendContentCopyWithImpl<
          _$_messageBoxItemExtendContent>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_messageBoxItemExtendContentToJson(
      this,
    );
  }
}

abstract class _messageBoxItemExtendContent
    implements messageBoxItemExtendContent {
  const factory _messageBoxItemExtendContent(
      {final int? showExtentContent,
      final String? contactTeacherText,
      final String? contactTeacherUrl,
      final String? teacherServiceText,
      final String? teacherServiceUrl,
      final String? teacherAvatar,
      final String? teacherName}) = _$_messageBoxItemExtendContent;

  factory _messageBoxItemExtendContent.fromJson(Map<String, dynamic> json) =
      _$_messageBoxItemExtendContent.fromJson;

  @override
  int? get showExtentContent;
  @override
  String? get contactTeacherText;
  @override
  String? get contactTeacherUrl;
  @override
  String? get teacherServiceText;
  @override
  String? get teacherServiceUrl;
  @override
  String? get teacherAvatar;
  @override
  String? get teacherName;
  @override
  @JsonKey(ignore: true)
  _$$_messageBoxItemExtendContentCopyWith<_$_messageBoxItemExtendContent>
      get copyWith => throw _privateConstructorUsedError;
}

todayPlanTaskInfo _$todayPlanTaskInfoFromJson(Map<String, dynamic> json) {
  return _todayPlanTaskInfo.fromJson(json);
}

/// @nodoc
mixin _$todayPlanTaskInfo {
  List<todayPlanActivityTaskAward>? get taskAwardList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanTaskInfoCopyWith<todayPlanTaskInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanTaskInfoCopyWith<$Res> {
  factory $todayPlanTaskInfoCopyWith(
          todayPlanTaskInfo value, $Res Function(todayPlanTaskInfo) then) =
      _$todayPlanTaskInfoCopyWithImpl<$Res, todayPlanTaskInfo>;
  @useResult
  $Res call({List<todayPlanActivityTaskAward>? taskAwardList});
}

/// @nodoc
class _$todayPlanTaskInfoCopyWithImpl<$Res, $Val extends todayPlanTaskInfo>
    implements $todayPlanTaskInfoCopyWith<$Res> {
  _$todayPlanTaskInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskAwardList = freezed,
  }) {
    return _then(_value.copyWith(
      taskAwardList: freezed == taskAwardList
          ? _value.taskAwardList
          : taskAwardList // ignore: cast_nullable_to_non_nullable
              as List<todayPlanActivityTaskAward>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_todayPlanTaskInfoCopyWith<$Res>
    implements $todayPlanTaskInfoCopyWith<$Res> {
  factory _$$_todayPlanTaskInfoCopyWith(_$_todayPlanTaskInfo value,
          $Res Function(_$_todayPlanTaskInfo) then) =
      __$$_todayPlanTaskInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<todayPlanActivityTaskAward>? taskAwardList});
}

/// @nodoc
class __$$_todayPlanTaskInfoCopyWithImpl<$Res>
    extends _$todayPlanTaskInfoCopyWithImpl<$Res, _$_todayPlanTaskInfo>
    implements _$$_todayPlanTaskInfoCopyWith<$Res> {
  __$$_todayPlanTaskInfoCopyWithImpl(
      _$_todayPlanTaskInfo _value, $Res Function(_$_todayPlanTaskInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskAwardList = freezed,
  }) {
    return _then(_$_todayPlanTaskInfo(
      taskAwardList: freezed == taskAwardList
          ? _value._taskAwardList
          : taskAwardList // ignore: cast_nullable_to_non_nullable
              as List<todayPlanActivityTaskAward>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanTaskInfo implements _todayPlanTaskInfo {
  const _$_todayPlanTaskInfo(
      {final List<todayPlanActivityTaskAward>? taskAwardList})
      : _taskAwardList = taskAwardList;

  factory _$_todayPlanTaskInfo.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanTaskInfoFromJson(json);

  final List<todayPlanActivityTaskAward>? _taskAwardList;
  @override
  List<todayPlanActivityTaskAward>? get taskAwardList {
    final value = _taskAwardList;
    if (value == null) return null;
    if (_taskAwardList is EqualUnmodifiableListView) return _taskAwardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'todayPlanTaskInfo(taskAwardList: $taskAwardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanTaskInfo &&
            const DeepCollectionEquality()
                .equals(other._taskAwardList, _taskAwardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_taskAwardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanTaskInfoCopyWith<_$_todayPlanTaskInfo> get copyWith =>
      __$$_todayPlanTaskInfoCopyWithImpl<_$_todayPlanTaskInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanTaskInfoToJson(
      this,
    );
  }
}

abstract class _todayPlanTaskInfo implements todayPlanTaskInfo {
  const factory _todayPlanTaskInfo(
          {final List<todayPlanActivityTaskAward>? taskAwardList}) =
      _$_todayPlanTaskInfo;

  factory _todayPlanTaskInfo.fromJson(Map<String, dynamic> json) =
      _$_todayPlanTaskInfo.fromJson;

  @override
  List<todayPlanActivityTaskAward>? get taskAwardList;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanTaskInfoCopyWith<_$_todayPlanTaskInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

todayPlanActivityTaskAward _$todayPlanActivityTaskAwardFromJson(
    Map<String, dynamic> json) {
  return _todayPlanActivityTaskAward.fromJson(json);
}

/// @nodoc
mixin _$todayPlanActivityTaskAward {
  int? get type =>
      throw _privateConstructorUsedError; //1-学豆、2-奖章（不存在不考虑）、3-学豆+奖章、4-心愿礼物
  String? get image => throw _privateConstructorUsedError; // 图片
  String? get name => throw _privateConstructorUsedError; // 奖励名称
  int? get count => throw _privateConstructorUsedError; //奖励数量
  String? get countText => throw _privateConstructorUsedError; //数量文案
  String? get tips => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $todayPlanActivityTaskAwardCopyWith<todayPlanActivityTaskAward>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $todayPlanActivityTaskAwardCopyWith<$Res> {
  factory $todayPlanActivityTaskAwardCopyWith(todayPlanActivityTaskAward value,
          $Res Function(todayPlanActivityTaskAward) then) =
      _$todayPlanActivityTaskAwardCopyWithImpl<$Res,
          todayPlanActivityTaskAward>;
  @useResult
  $Res call(
      {int? type,
      String? image,
      String? name,
      int? count,
      String? countText,
      String? tips});
}

/// @nodoc
class _$todayPlanActivityTaskAwardCopyWithImpl<$Res,
        $Val extends todayPlanActivityTaskAward>
    implements $todayPlanActivityTaskAwardCopyWith<$Res> {
  _$todayPlanActivityTaskAwardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? image = freezed,
    Object? name = freezed,
    Object? count = freezed,
    Object? countText = freezed,
    Object? tips = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      countText: freezed == countText
          ? _value.countText
          : countText // ignore: cast_nullable_to_non_nullable
              as String?,
      tips: freezed == tips
          ? _value.tips
          : tips // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_todayPlanActivityTaskAwardCopyWith<$Res>
    implements $todayPlanActivityTaskAwardCopyWith<$Res> {
  factory _$$_todayPlanActivityTaskAwardCopyWith(
          _$_todayPlanActivityTaskAward value,
          $Res Function(_$_todayPlanActivityTaskAward) then) =
      __$$_todayPlanActivityTaskAwardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      String? image,
      String? name,
      int? count,
      String? countText,
      String? tips});
}

/// @nodoc
class __$$_todayPlanActivityTaskAwardCopyWithImpl<$Res>
    extends _$todayPlanActivityTaskAwardCopyWithImpl<$Res,
        _$_todayPlanActivityTaskAward>
    implements _$$_todayPlanActivityTaskAwardCopyWith<$Res> {
  __$$_todayPlanActivityTaskAwardCopyWithImpl(
      _$_todayPlanActivityTaskAward _value,
      $Res Function(_$_todayPlanActivityTaskAward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? image = freezed,
    Object? name = freezed,
    Object? count = freezed,
    Object? countText = freezed,
    Object? tips = freezed,
  }) {
    return _then(_$_todayPlanActivityTaskAward(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      countText: freezed == countText
          ? _value.countText
          : countText // ignore: cast_nullable_to_non_nullable
              as String?,
      tips: freezed == tips
          ? _value.tips
          : tips // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_todayPlanActivityTaskAward implements _todayPlanActivityTaskAward {
  const _$_todayPlanActivityTaskAward(
      {this.type,
      this.image,
      this.name,
      this.count,
      this.countText,
      this.tips});

  factory _$_todayPlanActivityTaskAward.fromJson(Map<String, dynamic> json) =>
      _$$_todayPlanActivityTaskAwardFromJson(json);

  @override
  final int? type;
//1-学豆、2-奖章（不存在不考虑）、3-学豆+奖章、4-心愿礼物
  @override
  final String? image;
// 图片
  @override
  final String? name;
// 奖励名称
  @override
  final int? count;
//奖励数量
  @override
  final String? countText;
//数量文案
  @override
  final String? tips;

  @override
  String toString() {
    return 'todayPlanActivityTaskAward(type: $type, image: $image, name: $name, count: $count, countText: $countText, tips: $tips)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_todayPlanActivityTaskAward &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.countText, countText) ||
                other.countText == countText) &&
            (identical(other.tips, tips) || other.tips == tips));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, image, name, count, countText, tips);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_todayPlanActivityTaskAwardCopyWith<_$_todayPlanActivityTaskAward>
      get copyWith => __$$_todayPlanActivityTaskAwardCopyWithImpl<
          _$_todayPlanActivityTaskAward>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_todayPlanActivityTaskAwardToJson(
      this,
    );
  }
}

abstract class _todayPlanActivityTaskAward
    implements todayPlanActivityTaskAward {
  const factory _todayPlanActivityTaskAward(
      {final int? type,
      final String? image,
      final String? name,
      final int? count,
      final String? countText,
      final String? tips}) = _$_todayPlanActivityTaskAward;

  factory _todayPlanActivityTaskAward.fromJson(Map<String, dynamic> json) =
      _$_todayPlanActivityTaskAward.fromJson;

  @override
  int? get type;
  @override //1-学豆、2-奖章（不存在不考虑）、3-学豆+奖章、4-心愿礼物
  String? get image;
  @override // 图片
  String? get name;
  @override // 奖励名称
  int? get count;
  @override //奖励数量
  String? get countText;
  @override //数量文案
  String? get tips;
  @override
  @JsonKey(ignore: true)
  _$$_todayPlanActivityTaskAwardCopyWith<_$_todayPlanActivityTaskAward>
      get copyWith => throw _privateConstructorUsedError;
}

exchangePopViewInfo _$exchangePopViewInfoFromJson(Map<String, dynamic> json) {
  return _exchangePopViewInfo.fromJson(json);
}

/// @nodoc
mixin _$exchangePopViewInfo {
  int? get showFlag => throw _privateConstructorUsedError; //是否显示
  String? get title => throw _privateConstructorUsedError; // 标题
  String? get content => throw _privateConstructorUsedError; // 内容
  String? get notes => throw _privateConstructorUsedError; // 详细内容
  List<exchangePopViewButtoinfo>? get popupButtonList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $exchangePopViewInfoCopyWith<exchangePopViewInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $exchangePopViewInfoCopyWith<$Res> {
  factory $exchangePopViewInfoCopyWith(
          exchangePopViewInfo value, $Res Function(exchangePopViewInfo) then) =
      _$exchangePopViewInfoCopyWithImpl<$Res, exchangePopViewInfo>;
  @useResult
  $Res call(
      {int? showFlag,
      String? title,
      String? content,
      String? notes,
      List<exchangePopViewButtoinfo>? popupButtonList});
}

/// @nodoc
class _$exchangePopViewInfoCopyWithImpl<$Res, $Val extends exchangePopViewInfo>
    implements $exchangePopViewInfoCopyWith<$Res> {
  _$exchangePopViewInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showFlag = freezed,
    Object? title = freezed,
    Object? content = freezed,
    Object? notes = freezed,
    Object? popupButtonList = freezed,
  }) {
    return _then(_value.copyWith(
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      popupButtonList: freezed == popupButtonList
          ? _value.popupButtonList
          : popupButtonList // ignore: cast_nullable_to_non_nullable
              as List<exchangePopViewButtoinfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_exchangePopViewInfoCopyWith<$Res>
    implements $exchangePopViewInfoCopyWith<$Res> {
  factory _$$_exchangePopViewInfoCopyWith(_$_exchangePopViewInfo value,
          $Res Function(_$_exchangePopViewInfo) then) =
      __$$_exchangePopViewInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? showFlag,
      String? title,
      String? content,
      String? notes,
      List<exchangePopViewButtoinfo>? popupButtonList});
}

/// @nodoc
class __$$_exchangePopViewInfoCopyWithImpl<$Res>
    extends _$exchangePopViewInfoCopyWithImpl<$Res, _$_exchangePopViewInfo>
    implements _$$_exchangePopViewInfoCopyWith<$Res> {
  __$$_exchangePopViewInfoCopyWithImpl(_$_exchangePopViewInfo _value,
      $Res Function(_$_exchangePopViewInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showFlag = freezed,
    Object? title = freezed,
    Object? content = freezed,
    Object? notes = freezed,
    Object? popupButtonList = freezed,
  }) {
    return _then(_$_exchangePopViewInfo(
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      popupButtonList: freezed == popupButtonList
          ? _value._popupButtonList
          : popupButtonList // ignore: cast_nullable_to_non_nullable
              as List<exchangePopViewButtoinfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_exchangePopViewInfo implements _exchangePopViewInfo {
  const _$_exchangePopViewInfo(
      {this.showFlag,
      this.title,
      this.content,
      this.notes,
      final List<exchangePopViewButtoinfo>? popupButtonList})
      : _popupButtonList = popupButtonList;

  factory _$_exchangePopViewInfo.fromJson(Map<String, dynamic> json) =>
      _$$_exchangePopViewInfoFromJson(json);

  @override
  final int? showFlag;
//是否显示
  @override
  final String? title;
// 标题
  @override
  final String? content;
// 内容
  @override
  final String? notes;
// 详细内容
  final List<exchangePopViewButtoinfo>? _popupButtonList;
// 详细内容
  @override
  List<exchangePopViewButtoinfo>? get popupButtonList {
    final value = _popupButtonList;
    if (value == null) return null;
    if (_popupButtonList is EqualUnmodifiableListView) return _popupButtonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'exchangePopViewInfo(showFlag: $showFlag, title: $title, content: $content, notes: $notes, popupButtonList: $popupButtonList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_exchangePopViewInfo &&
            (identical(other.showFlag, showFlag) ||
                other.showFlag == showFlag) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality()
                .equals(other._popupButtonList, _popupButtonList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, showFlag, title, content, notes,
      const DeepCollectionEquality().hash(_popupButtonList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_exchangePopViewInfoCopyWith<_$_exchangePopViewInfo> get copyWith =>
      __$$_exchangePopViewInfoCopyWithImpl<_$_exchangePopViewInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_exchangePopViewInfoToJson(
      this,
    );
  }
}

abstract class _exchangePopViewInfo implements exchangePopViewInfo {
  const factory _exchangePopViewInfo(
          {final int? showFlag,
          final String? title,
          final String? content,
          final String? notes,
          final List<exchangePopViewButtoinfo>? popupButtonList}) =
      _$_exchangePopViewInfo;

  factory _exchangePopViewInfo.fromJson(Map<String, dynamic> json) =
      _$_exchangePopViewInfo.fromJson;

  @override
  int? get showFlag;
  @override //是否显示
  String? get title;
  @override // 标题
  String? get content;
  @override // 内容
  String? get notes;
  @override // 详细内容
  List<exchangePopViewButtoinfo>? get popupButtonList;
  @override
  @JsonKey(ignore: true)
  _$$_exchangePopViewInfoCopyWith<_$_exchangePopViewInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

exchangePopViewButtoinfo _$exchangePopViewButtoinfoFromJson(
    Map<String, dynamic> json) {
  return _exchangePopViewButtoinfo.fromJson(json);
}

/// @nodoc
mixin _$exchangePopViewButtoinfo {
  String? get btnText => throw _privateConstructorUsedError; // 按钮文案
  String? get btnUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $exchangePopViewButtoinfoCopyWith<exchangePopViewButtoinfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $exchangePopViewButtoinfoCopyWith<$Res> {
  factory $exchangePopViewButtoinfoCopyWith(exchangePopViewButtoinfo value,
          $Res Function(exchangePopViewButtoinfo) then) =
      _$exchangePopViewButtoinfoCopyWithImpl<$Res, exchangePopViewButtoinfo>;
  @useResult
  $Res call({String? btnText, String? btnUrl});
}

/// @nodoc
class _$exchangePopViewButtoinfoCopyWithImpl<$Res,
        $Val extends exchangePopViewButtoinfo>
    implements $exchangePopViewButtoinfoCopyWith<$Res> {
  _$exchangePopViewButtoinfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnText = freezed,
    Object? btnUrl = freezed,
  }) {
    return _then(_value.copyWith(
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnUrl: freezed == btnUrl
          ? _value.btnUrl
          : btnUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_exchangePopViewButtoinfoCopyWith<$Res>
    implements $exchangePopViewButtoinfoCopyWith<$Res> {
  factory _$$_exchangePopViewButtoinfoCopyWith(
          _$_exchangePopViewButtoinfo value,
          $Res Function(_$_exchangePopViewButtoinfo) then) =
      __$$_exchangePopViewButtoinfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? btnText, String? btnUrl});
}

/// @nodoc
class __$$_exchangePopViewButtoinfoCopyWithImpl<$Res>
    extends _$exchangePopViewButtoinfoCopyWithImpl<$Res,
        _$_exchangePopViewButtoinfo>
    implements _$$_exchangePopViewButtoinfoCopyWith<$Res> {
  __$$_exchangePopViewButtoinfoCopyWithImpl(_$_exchangePopViewButtoinfo _value,
      $Res Function(_$_exchangePopViewButtoinfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnText = freezed,
    Object? btnUrl = freezed,
  }) {
    return _then(_$_exchangePopViewButtoinfo(
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnUrl: freezed == btnUrl
          ? _value.btnUrl
          : btnUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_exchangePopViewButtoinfo implements _exchangePopViewButtoinfo {
  const _$_exchangePopViewButtoinfo({this.btnText, this.btnUrl});

  factory _$_exchangePopViewButtoinfo.fromJson(Map<String, dynamic> json) =>
      _$$_exchangePopViewButtoinfoFromJson(json);

  @override
  final String? btnText;
// 按钮文案
  @override
  final String? btnUrl;

  @override
  String toString() {
    return 'exchangePopViewButtoinfo(btnText: $btnText, btnUrl: $btnUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_exchangePopViewButtoinfo &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.btnUrl, btnUrl) || other.btnUrl == btnUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, btnText, btnUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_exchangePopViewButtoinfoCopyWith<_$_exchangePopViewButtoinfo>
      get copyWith => __$$_exchangePopViewButtoinfoCopyWithImpl<
          _$_exchangePopViewButtoinfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_exchangePopViewButtoinfoToJson(
      this,
    );
  }
}

abstract class _exchangePopViewButtoinfo implements exchangePopViewButtoinfo {
  const factory _exchangePopViewButtoinfo(
      {final String? btnText,
      final String? btnUrl}) = _$_exchangePopViewButtoinfo;

  factory _exchangePopViewButtoinfo.fromJson(Map<String, dynamic> json) =
      _$_exchangePopViewButtoinfo.fromJson;

  @override
  String? get btnText;
  @override // 按钮文案
  String? get btnUrl;
  @override
  @JsonKey(ignore: true)
  _$$_exchangePopViewButtoinfoCopyWith<_$_exchangePopViewButtoinfo>
      get copyWith => throw _privateConstructorUsedError;
}

beginnerGuideShowInfo _$beginnerGuideShowInfoFromJson(
    Map<String, dynamic> json) {
  return _beginnerGuideShowInfo.fromJson(json);
}

/// @nodoc
mixin _$beginnerGuideShowInfo {
  bool? get show => throw _privateConstructorUsedError; // 是否显示
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $beginnerGuideShowInfoCopyWith<beginnerGuideShowInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $beginnerGuideShowInfoCopyWith<$Res> {
  factory $beginnerGuideShowInfoCopyWith(beginnerGuideShowInfo value,
          $Res Function(beginnerGuideShowInfo) then) =
      _$beginnerGuideShowInfoCopyWithImpl<$Res, beginnerGuideShowInfo>;
  @useResult
  $Res call({bool? show, String? jumpRoute});
}

/// @nodoc
class _$beginnerGuideShowInfoCopyWithImpl<$Res,
        $Val extends beginnerGuideShowInfo>
    implements $beginnerGuideShowInfoCopyWith<$Res> {
  _$beginnerGuideShowInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? show = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      show: freezed == show
          ? _value.show
          : show // ignore: cast_nullable_to_non_nullable
              as bool?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_beginnerGuideShowInfoCopyWith<$Res>
    implements $beginnerGuideShowInfoCopyWith<$Res> {
  factory _$$_beginnerGuideShowInfoCopyWith(_$_beginnerGuideShowInfo value,
          $Res Function(_$_beginnerGuideShowInfo) then) =
      __$$_beginnerGuideShowInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? show, String? jumpRoute});
}

/// @nodoc
class __$$_beginnerGuideShowInfoCopyWithImpl<$Res>
    extends _$beginnerGuideShowInfoCopyWithImpl<$Res, _$_beginnerGuideShowInfo>
    implements _$$_beginnerGuideShowInfoCopyWith<$Res> {
  __$$_beginnerGuideShowInfoCopyWithImpl(_$_beginnerGuideShowInfo _value,
      $Res Function(_$_beginnerGuideShowInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? show = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_beginnerGuideShowInfo(
      show: freezed == show
          ? _value.show
          : show // ignore: cast_nullable_to_non_nullable
              as bool?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_beginnerGuideShowInfo implements _beginnerGuideShowInfo {
  const _$_beginnerGuideShowInfo({this.show, this.jumpRoute});

  factory _$_beginnerGuideShowInfo.fromJson(Map<String, dynamic> json) =>
      _$$_beginnerGuideShowInfoFromJson(json);

  @override
  final bool? show;
// 是否显示
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'beginnerGuideShowInfo(show: $show, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_beginnerGuideShowInfo &&
            (identical(other.show, show) || other.show == show) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, show, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_beginnerGuideShowInfoCopyWith<_$_beginnerGuideShowInfo> get copyWith =>
      __$$_beginnerGuideShowInfoCopyWithImpl<_$_beginnerGuideShowInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_beginnerGuideShowInfoToJson(
      this,
    );
  }
}

abstract class _beginnerGuideShowInfo implements beginnerGuideShowInfo {
  const factory _beginnerGuideShowInfo(
      {final bool? show, final String? jumpRoute}) = _$_beginnerGuideShowInfo;

  factory _beginnerGuideShowInfo.fromJson(Map<String, dynamic> json) =
      _$_beginnerGuideShowInfo.fromJson;

  @override
  bool? get show;
  @override // 是否显示
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_beginnerGuideShowInfoCopyWith<_$_beginnerGuideShowInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

gradeUpdateInfo _$gradeUpdateInfoFromJson(Map<String, dynamic> json) {
  return _gradeUpdateInfo.fromJson(json);
}

/// @nodoc
mixin _$gradeUpdateInfo {
  String? get title => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  int? get showFlag => throw _privateConstructorUsedError;
  gradeSelectGradeList? get gradeListSummaryInfo =>
      throw _privateConstructorUsedError;
  gradeSelectGradeListItem? get nextGrade => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $gradeUpdateInfoCopyWith<gradeUpdateInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $gradeUpdateInfoCopyWith<$Res> {
  factory $gradeUpdateInfoCopyWith(
          gradeUpdateInfo value, $Res Function(gradeUpdateInfo) then) =
      _$gradeUpdateInfoCopyWithImpl<$Res, gradeUpdateInfo>;
  @useResult
  $Res call(
      {String? title,
      String? notes,
      String? content,
      int? showFlag,
      gradeSelectGradeList? gradeListSummaryInfo,
      gradeSelectGradeListItem? nextGrade});

  $gradeSelectGradeListCopyWith<$Res>? get gradeListSummaryInfo;
  $gradeSelectGradeListItemCopyWith<$Res>? get nextGrade;
}

/// @nodoc
class _$gradeUpdateInfoCopyWithImpl<$Res, $Val extends gradeUpdateInfo>
    implements $gradeUpdateInfoCopyWith<$Res> {
  _$gradeUpdateInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? notes = freezed,
    Object? content = freezed,
    Object? showFlag = freezed,
    Object? gradeListSummaryInfo = freezed,
    Object? nextGrade = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeListSummaryInfo: freezed == gradeListSummaryInfo
          ? _value.gradeListSummaryInfo
          : gradeListSummaryInfo // ignore: cast_nullable_to_non_nullable
              as gradeSelectGradeList?,
      nextGrade: freezed == nextGrade
          ? _value.nextGrade
          : nextGrade // ignore: cast_nullable_to_non_nullable
              as gradeSelectGradeListItem?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $gradeSelectGradeListCopyWith<$Res>? get gradeListSummaryInfo {
    if (_value.gradeListSummaryInfo == null) {
      return null;
    }

    return $gradeSelectGradeListCopyWith<$Res>(_value.gradeListSummaryInfo!,
        (value) {
      return _then(_value.copyWith(gradeListSummaryInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $gradeSelectGradeListItemCopyWith<$Res>? get nextGrade {
    if (_value.nextGrade == null) {
      return null;
    }

    return $gradeSelectGradeListItemCopyWith<$Res>(_value.nextGrade!, (value) {
      return _then(_value.copyWith(nextGrade: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_gradeUpdateInfoCopyWith<$Res>
    implements $gradeUpdateInfoCopyWith<$Res> {
  factory _$$_gradeUpdateInfoCopyWith(
          _$_gradeUpdateInfo value, $Res Function(_$_gradeUpdateInfo) then) =
      __$$_gradeUpdateInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? notes,
      String? content,
      int? showFlag,
      gradeSelectGradeList? gradeListSummaryInfo,
      gradeSelectGradeListItem? nextGrade});

  @override
  $gradeSelectGradeListCopyWith<$Res>? get gradeListSummaryInfo;
  @override
  $gradeSelectGradeListItemCopyWith<$Res>? get nextGrade;
}

/// @nodoc
class __$$_gradeUpdateInfoCopyWithImpl<$Res>
    extends _$gradeUpdateInfoCopyWithImpl<$Res, _$_gradeUpdateInfo>
    implements _$$_gradeUpdateInfoCopyWith<$Res> {
  __$$_gradeUpdateInfoCopyWithImpl(
      _$_gradeUpdateInfo _value, $Res Function(_$_gradeUpdateInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? notes = freezed,
    Object? content = freezed,
    Object? showFlag = freezed,
    Object? gradeListSummaryInfo = freezed,
    Object? nextGrade = freezed,
  }) {
    return _then(_$_gradeUpdateInfo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeListSummaryInfo: freezed == gradeListSummaryInfo
          ? _value.gradeListSummaryInfo
          : gradeListSummaryInfo // ignore: cast_nullable_to_non_nullable
              as gradeSelectGradeList?,
      nextGrade: freezed == nextGrade
          ? _value.nextGrade
          : nextGrade // ignore: cast_nullable_to_non_nullable
              as gradeSelectGradeListItem?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_gradeUpdateInfo implements _gradeUpdateInfo {
  const _$_gradeUpdateInfo(
      {this.title,
      this.notes,
      this.content,
      this.showFlag,
      this.gradeListSummaryInfo,
      this.nextGrade});

  factory _$_gradeUpdateInfo.fromJson(Map<String, dynamic> json) =>
      _$$_gradeUpdateInfoFromJson(json);

  @override
  final String? title;
  @override
  final String? notes;
  @override
  final String? content;
  @override
  final int? showFlag;
  @override
  final gradeSelectGradeList? gradeListSummaryInfo;
  @override
  final gradeSelectGradeListItem? nextGrade;

  @override
  String toString() {
    return 'gradeUpdateInfo(title: $title, notes: $notes, content: $content, showFlag: $showFlag, gradeListSummaryInfo: $gradeListSummaryInfo, nextGrade: $nextGrade)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_gradeUpdateInfo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.showFlag, showFlag) ||
                other.showFlag == showFlag) &&
            (identical(other.gradeListSummaryInfo, gradeListSummaryInfo) ||
                other.gradeListSummaryInfo == gradeListSummaryInfo) &&
            (identical(other.nextGrade, nextGrade) ||
                other.nextGrade == nextGrade));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, notes, content, showFlag,
      gradeListSummaryInfo, nextGrade);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_gradeUpdateInfoCopyWith<_$_gradeUpdateInfo> get copyWith =>
      __$$_gradeUpdateInfoCopyWithImpl<_$_gradeUpdateInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_gradeUpdateInfoToJson(
      this,
    );
  }
}

abstract class _gradeUpdateInfo implements gradeUpdateInfo {
  const factory _gradeUpdateInfo(
      {final String? title,
      final String? notes,
      final String? content,
      final int? showFlag,
      final gradeSelectGradeList? gradeListSummaryInfo,
      final gradeSelectGradeListItem? nextGrade}) = _$_gradeUpdateInfo;

  factory _gradeUpdateInfo.fromJson(Map<String, dynamic> json) =
      _$_gradeUpdateInfo.fromJson;

  @override
  String? get title;
  @override
  String? get notes;
  @override
  String? get content;
  @override
  int? get showFlag;
  @override
  gradeSelectGradeList? get gradeListSummaryInfo;
  @override
  gradeSelectGradeListItem? get nextGrade;
  @override
  @JsonKey(ignore: true)
  _$$_gradeUpdateInfoCopyWith<_$_gradeUpdateInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

gradeSelectGradeList _$gradeSelectGradeListFromJson(Map<String, dynamic> json) {
  return _gradeSelectGradeList.fromJson(json);
}

/// @nodoc
mixin _$gradeSelectGradeList {
  String? get tip => throw _privateConstructorUsedError;
  int? get showFlag => throw _privateConstructorUsedError;
  List<gradeSelectGradeListItem>? get gradeDictList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $gradeSelectGradeListCopyWith<gradeSelectGradeList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $gradeSelectGradeListCopyWith<$Res> {
  factory $gradeSelectGradeListCopyWith(gradeSelectGradeList value,
          $Res Function(gradeSelectGradeList) then) =
      _$gradeSelectGradeListCopyWithImpl<$Res, gradeSelectGradeList>;
  @useResult
  $Res call(
      {String? tip,
      int? showFlag,
      List<gradeSelectGradeListItem>? gradeDictList});
}

/// @nodoc
class _$gradeSelectGradeListCopyWithImpl<$Res,
        $Val extends gradeSelectGradeList>
    implements $gradeSelectGradeListCopyWith<$Res> {
  _$gradeSelectGradeListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tip = freezed,
    Object? showFlag = freezed,
    Object? gradeDictList = freezed,
  }) {
    return _then(_value.copyWith(
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeDictList: freezed == gradeDictList
          ? _value.gradeDictList
          : gradeDictList // ignore: cast_nullable_to_non_nullable
              as List<gradeSelectGradeListItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_gradeSelectGradeListCopyWith<$Res>
    implements $gradeSelectGradeListCopyWith<$Res> {
  factory _$$_gradeSelectGradeListCopyWith(_$_gradeSelectGradeList value,
          $Res Function(_$_gradeSelectGradeList) then) =
      __$$_gradeSelectGradeListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tip,
      int? showFlag,
      List<gradeSelectGradeListItem>? gradeDictList});
}

/// @nodoc
class __$$_gradeSelectGradeListCopyWithImpl<$Res>
    extends _$gradeSelectGradeListCopyWithImpl<$Res, _$_gradeSelectGradeList>
    implements _$$_gradeSelectGradeListCopyWith<$Res> {
  __$$_gradeSelectGradeListCopyWithImpl(_$_gradeSelectGradeList _value,
      $Res Function(_$_gradeSelectGradeList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tip = freezed,
    Object? showFlag = freezed,
    Object? gradeDictList = freezed,
  }) {
    return _then(_$_gradeSelectGradeList(
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      showFlag: freezed == showFlag
          ? _value.showFlag
          : showFlag // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeDictList: freezed == gradeDictList
          ? _value._gradeDictList
          : gradeDictList // ignore: cast_nullable_to_non_nullable
              as List<gradeSelectGradeListItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_gradeSelectGradeList implements _gradeSelectGradeList {
  const _$_gradeSelectGradeList(
      {this.tip,
      this.showFlag,
      final List<gradeSelectGradeListItem>? gradeDictList})
      : _gradeDictList = gradeDictList;

  factory _$_gradeSelectGradeList.fromJson(Map<String, dynamic> json) =>
      _$$_gradeSelectGradeListFromJson(json);

  @override
  final String? tip;
  @override
  final int? showFlag;
  final List<gradeSelectGradeListItem>? _gradeDictList;
  @override
  List<gradeSelectGradeListItem>? get gradeDictList {
    final value = _gradeDictList;
    if (value == null) return null;
    if (_gradeDictList is EqualUnmodifiableListView) return _gradeDictList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'gradeSelectGradeList(tip: $tip, showFlag: $showFlag, gradeDictList: $gradeDictList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_gradeSelectGradeList &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.showFlag, showFlag) ||
                other.showFlag == showFlag) &&
            const DeepCollectionEquality()
                .equals(other._gradeDictList, _gradeDictList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tip, showFlag,
      const DeepCollectionEquality().hash(_gradeDictList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_gradeSelectGradeListCopyWith<_$_gradeSelectGradeList> get copyWith =>
      __$$_gradeSelectGradeListCopyWithImpl<_$_gradeSelectGradeList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_gradeSelectGradeListToJson(
      this,
    );
  }
}

abstract class _gradeSelectGradeList implements gradeSelectGradeList {
  const factory _gradeSelectGradeList(
          {final String? tip,
          final int? showFlag,
          final List<gradeSelectGradeListItem>? gradeDictList}) =
      _$_gradeSelectGradeList;

  factory _gradeSelectGradeList.fromJson(Map<String, dynamic> json) =
      _$_gradeSelectGradeList.fromJson;

  @override
  String? get tip;
  @override
  int? get showFlag;
  @override
  List<gradeSelectGradeListItem>? get gradeDictList;
  @override
  @JsonKey(ignore: true)
  _$$_gradeSelectGradeListCopyWith<_$_gradeSelectGradeList> get copyWith =>
      throw _privateConstructorUsedError;
}

gradeSelectGradeListItem _$gradeSelectGradeListItemFromJson(
    Map<String, dynamic> json) {
  return _gradeSelectGradeListItem.fromJson(json);
}

/// @nodoc
mixin _$gradeSelectGradeListItem {
  String? get name => throw _privateConstructorUsedError;
  int? get code => throw _privateConstructorUsedError;
  List<gradeSelectGradeListItem>? get subDict =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $gradeSelectGradeListItemCopyWith<gradeSelectGradeListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $gradeSelectGradeListItemCopyWith<$Res> {
  factory $gradeSelectGradeListItemCopyWith(gradeSelectGradeListItem value,
          $Res Function(gradeSelectGradeListItem) then) =
      _$gradeSelectGradeListItemCopyWithImpl<$Res, gradeSelectGradeListItem>;
  @useResult
  $Res call({String? name, int? code, List<gradeSelectGradeListItem>? subDict});
}

/// @nodoc
class _$gradeSelectGradeListItemCopyWithImpl<$Res,
        $Val extends gradeSelectGradeListItem>
    implements $gradeSelectGradeListItemCopyWith<$Res> {
  _$gradeSelectGradeListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
    Object? subDict = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      subDict: freezed == subDict
          ? _value.subDict
          : subDict // ignore: cast_nullable_to_non_nullable
              as List<gradeSelectGradeListItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_gradeSelectGradeListItemCopyWith<$Res>
    implements $gradeSelectGradeListItemCopyWith<$Res> {
  factory _$$_gradeSelectGradeListItemCopyWith(
          _$_gradeSelectGradeListItem value,
          $Res Function(_$_gradeSelectGradeListItem) then) =
      __$$_gradeSelectGradeListItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, int? code, List<gradeSelectGradeListItem>? subDict});
}

/// @nodoc
class __$$_gradeSelectGradeListItemCopyWithImpl<$Res>
    extends _$gradeSelectGradeListItemCopyWithImpl<$Res,
        _$_gradeSelectGradeListItem>
    implements _$$_gradeSelectGradeListItemCopyWith<$Res> {
  __$$_gradeSelectGradeListItemCopyWithImpl(_$_gradeSelectGradeListItem _value,
      $Res Function(_$_gradeSelectGradeListItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
    Object? subDict = freezed,
  }) {
    return _then(_$_gradeSelectGradeListItem(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      subDict: freezed == subDict
          ? _value._subDict
          : subDict // ignore: cast_nullable_to_non_nullable
              as List<gradeSelectGradeListItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_gradeSelectGradeListItem implements _gradeSelectGradeListItem {
  const _$_gradeSelectGradeListItem(
      {this.name, this.code, final List<gradeSelectGradeListItem>? subDict})
      : _subDict = subDict;

  factory _$_gradeSelectGradeListItem.fromJson(Map<String, dynamic> json) =>
      _$$_gradeSelectGradeListItemFromJson(json);

  @override
  final String? name;
  @override
  final int? code;
  final List<gradeSelectGradeListItem>? _subDict;
  @override
  List<gradeSelectGradeListItem>? get subDict {
    final value = _subDict;
    if (value == null) return null;
    if (_subDict is EqualUnmodifiableListView) return _subDict;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'gradeSelectGradeListItem(name: $name, code: $code, subDict: $subDict)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_gradeSelectGradeListItem &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality().equals(other._subDict, _subDict));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, code, const DeepCollectionEquality().hash(_subDict));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_gradeSelectGradeListItemCopyWith<_$_gradeSelectGradeListItem>
      get copyWith => __$$_gradeSelectGradeListItemCopyWithImpl<
          _$_gradeSelectGradeListItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_gradeSelectGradeListItemToJson(
      this,
    );
  }
}

abstract class _gradeSelectGradeListItem implements gradeSelectGradeListItem {
  const factory _gradeSelectGradeListItem(
          {final String? name,
          final int? code,
          final List<gradeSelectGradeListItem>? subDict}) =
      _$_gradeSelectGradeListItem;

  factory _gradeSelectGradeListItem.fromJson(Map<String, dynamic> json) =
      _$_gradeSelectGradeListItem.fromJson;

  @override
  String? get name;
  @override
  int? get code;
  @override
  List<gradeSelectGradeListItem>? get subDict;
  @override
  @JsonKey(ignore: true)
  _$$_gradeSelectGradeListItemCopyWith<_$_gradeSelectGradeListItem>
      get copyWith => throw _privateConstructorUsedError;
}

cmsAdsInfo _$cmsAdsInfoFromJson(Map<String, dynamic> json) {
  return _cmsAdsInfo.fromJson(json);
}

/// @nodoc
mixin _$cmsAdsInfo {
  int? get businessType =>
      throw _privateConstructorUsedError; //业务配置类型，弹窗固定返回值为3
  int? get linkType =>
      throw _privateConstructorUsedError; //弹窗链接类型 1- 需要识别二维码 2-不需要识别二维码
  String? get linkUrl => throw _privateConstructorUsedError; //跳转地址
  String? get pictureUrl => throw _privateConstructorUsedError; //弹窗图片地址
  int? get popupId => throw _privateConstructorUsedError; //弹窗主键id
  String? get popupName => throw _privateConstructorUsedError; //主键名字
  int? get configType =>
      throw _privateConstructorUsedError; //弹窗配置类型  1-默认配置  2-人工培植 3-系统调用
  String? get contentJson =>
      throw _privateConstructorUsedError; //系统调用弹窗所展示的json
  String? get contentDict => throw _privateConstructorUsedError;
  String? get systemActivateType =>
      throw _privateConstructorUsedError; //系统调用活动类型(后期可能会根据不同的运营需求变化不同的展示样式): MALL_SYSTEM_COUPON(商城优惠券活动)
  bool? get popUpIsShow => throw _privateConstructorUsedError;
  int? get controllerPosition => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $cmsAdsInfoCopyWith<cmsAdsInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $cmsAdsInfoCopyWith<$Res> {
  factory $cmsAdsInfoCopyWith(
          cmsAdsInfo value, $Res Function(cmsAdsInfo) then) =
      _$cmsAdsInfoCopyWithImpl<$Res, cmsAdsInfo>;
  @useResult
  $Res call(
      {int? businessType,
      int? linkType,
      String? linkUrl,
      String? pictureUrl,
      int? popupId,
      String? popupName,
      int? configType,
      String? contentJson,
      String? contentDict,
      String? systemActivateType,
      bool? popUpIsShow,
      int? controllerPosition});
}

/// @nodoc
class _$cmsAdsInfoCopyWithImpl<$Res, $Val extends cmsAdsInfo>
    implements $cmsAdsInfoCopyWith<$Res> {
  _$cmsAdsInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessType = freezed,
    Object? linkType = freezed,
    Object? linkUrl = freezed,
    Object? pictureUrl = freezed,
    Object? popupId = freezed,
    Object? popupName = freezed,
    Object? configType = freezed,
    Object? contentJson = freezed,
    Object? contentDict = freezed,
    Object? systemActivateType = freezed,
    Object? popUpIsShow = freezed,
    Object? controllerPosition = freezed,
  }) {
    return _then(_value.copyWith(
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as int?,
      linkType: freezed == linkType
          ? _value.linkType
          : linkType // ignore: cast_nullable_to_non_nullable
              as int?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      popupId: freezed == popupId
          ? _value.popupId
          : popupId // ignore: cast_nullable_to_non_nullable
              as int?,
      popupName: freezed == popupName
          ? _value.popupName
          : popupName // ignore: cast_nullable_to_non_nullable
              as String?,
      configType: freezed == configType
          ? _value.configType
          : configType // ignore: cast_nullable_to_non_nullable
              as int?,
      contentJson: freezed == contentJson
          ? _value.contentJson
          : contentJson // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDict: freezed == contentDict
          ? _value.contentDict
          : contentDict // ignore: cast_nullable_to_non_nullable
              as String?,
      systemActivateType: freezed == systemActivateType
          ? _value.systemActivateType
          : systemActivateType // ignore: cast_nullable_to_non_nullable
              as String?,
      popUpIsShow: freezed == popUpIsShow
          ? _value.popUpIsShow
          : popUpIsShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      controllerPosition: freezed == controllerPosition
          ? _value.controllerPosition
          : controllerPosition // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_cmsAdsInfoCopyWith<$Res>
    implements $cmsAdsInfoCopyWith<$Res> {
  factory _$$_cmsAdsInfoCopyWith(
          _$_cmsAdsInfo value, $Res Function(_$_cmsAdsInfo) then) =
      __$$_cmsAdsInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? businessType,
      int? linkType,
      String? linkUrl,
      String? pictureUrl,
      int? popupId,
      String? popupName,
      int? configType,
      String? contentJson,
      String? contentDict,
      String? systemActivateType,
      bool? popUpIsShow,
      int? controllerPosition});
}

/// @nodoc
class __$$_cmsAdsInfoCopyWithImpl<$Res>
    extends _$cmsAdsInfoCopyWithImpl<$Res, _$_cmsAdsInfo>
    implements _$$_cmsAdsInfoCopyWith<$Res> {
  __$$_cmsAdsInfoCopyWithImpl(
      _$_cmsAdsInfo _value, $Res Function(_$_cmsAdsInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessType = freezed,
    Object? linkType = freezed,
    Object? linkUrl = freezed,
    Object? pictureUrl = freezed,
    Object? popupId = freezed,
    Object? popupName = freezed,
    Object? configType = freezed,
    Object? contentJson = freezed,
    Object? contentDict = freezed,
    Object? systemActivateType = freezed,
    Object? popUpIsShow = freezed,
    Object? controllerPosition = freezed,
  }) {
    return _then(_$_cmsAdsInfo(
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as int?,
      linkType: freezed == linkType
          ? _value.linkType
          : linkType // ignore: cast_nullable_to_non_nullable
              as int?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      popupId: freezed == popupId
          ? _value.popupId
          : popupId // ignore: cast_nullable_to_non_nullable
              as int?,
      popupName: freezed == popupName
          ? _value.popupName
          : popupName // ignore: cast_nullable_to_non_nullable
              as String?,
      configType: freezed == configType
          ? _value.configType
          : configType // ignore: cast_nullable_to_non_nullable
              as int?,
      contentJson: freezed == contentJson
          ? _value.contentJson
          : contentJson // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDict: freezed == contentDict
          ? _value.contentDict
          : contentDict // ignore: cast_nullable_to_non_nullable
              as String?,
      systemActivateType: freezed == systemActivateType
          ? _value.systemActivateType
          : systemActivateType // ignore: cast_nullable_to_non_nullable
              as String?,
      popUpIsShow: freezed == popUpIsShow
          ? _value.popUpIsShow
          : popUpIsShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      controllerPosition: freezed == controllerPosition
          ? _value.controllerPosition
          : controllerPosition // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_cmsAdsInfo implements _cmsAdsInfo {
  const _$_cmsAdsInfo(
      {this.businessType,
      this.linkType,
      this.linkUrl,
      this.pictureUrl,
      this.popupId,
      this.popupName,
      this.configType,
      this.contentJson,
      this.contentDict,
      this.systemActivateType,
      this.popUpIsShow,
      this.controllerPosition});

  factory _$_cmsAdsInfo.fromJson(Map<String, dynamic> json) =>
      _$$_cmsAdsInfoFromJson(json);

  @override
  final int? businessType;
//业务配置类型，弹窗固定返回值为3
  @override
  final int? linkType;
//弹窗链接类型 1- 需要识别二维码 2-不需要识别二维码
  @override
  final String? linkUrl;
//跳转地址
  @override
  final String? pictureUrl;
//弹窗图片地址
  @override
  final int? popupId;
//弹窗主键id
  @override
  final String? popupName;
//主键名字
  @override
  final int? configType;
//弹窗配置类型  1-默认配置  2-人工培植 3-系统调用
  @override
  final String? contentJson;
//系统调用弹窗所展示的json
  @override
  final String? contentDict;
  @override
  final String? systemActivateType;
//系统调用活动类型(后期可能会根据不同的运营需求变化不同的展示样式): MALL_SYSTEM_COUPON(商城优惠券活动)
  @override
  final bool? popUpIsShow;
  @override
  final int? controllerPosition;

  @override
  String toString() {
    return 'cmsAdsInfo(businessType: $businessType, linkType: $linkType, linkUrl: $linkUrl, pictureUrl: $pictureUrl, popupId: $popupId, popupName: $popupName, configType: $configType, contentJson: $contentJson, contentDict: $contentDict, systemActivateType: $systemActivateType, popUpIsShow: $popUpIsShow, controllerPosition: $controllerPosition)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_cmsAdsInfo &&
            (identical(other.businessType, businessType) ||
                other.businessType == businessType) &&
            (identical(other.linkType, linkType) ||
                other.linkType == linkType) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.popupId, popupId) || other.popupId == popupId) &&
            (identical(other.popupName, popupName) ||
                other.popupName == popupName) &&
            (identical(other.configType, configType) ||
                other.configType == configType) &&
            (identical(other.contentJson, contentJson) ||
                other.contentJson == contentJson) &&
            (identical(other.contentDict, contentDict) ||
                other.contentDict == contentDict) &&
            (identical(other.systemActivateType, systemActivateType) ||
                other.systemActivateType == systemActivateType) &&
            (identical(other.popUpIsShow, popUpIsShow) ||
                other.popUpIsShow == popUpIsShow) &&
            (identical(other.controllerPosition, controllerPosition) ||
                other.controllerPosition == controllerPosition));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      businessType,
      linkType,
      linkUrl,
      pictureUrl,
      popupId,
      popupName,
      configType,
      contentJson,
      contentDict,
      systemActivateType,
      popUpIsShow,
      controllerPosition);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_cmsAdsInfoCopyWith<_$_cmsAdsInfo> get copyWith =>
      __$$_cmsAdsInfoCopyWithImpl<_$_cmsAdsInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_cmsAdsInfoToJson(
      this,
    );
  }
}

abstract class _cmsAdsInfo implements cmsAdsInfo {
  const factory _cmsAdsInfo(
      {final int? businessType,
      final int? linkType,
      final String? linkUrl,
      final String? pictureUrl,
      final int? popupId,
      final String? popupName,
      final int? configType,
      final String? contentJson,
      final String? contentDict,
      final String? systemActivateType,
      final bool? popUpIsShow,
      final int? controllerPosition}) = _$_cmsAdsInfo;

  factory _cmsAdsInfo.fromJson(Map<String, dynamic> json) =
      _$_cmsAdsInfo.fromJson;

  @override
  int? get businessType;
  @override //业务配置类型，弹窗固定返回值为3
  int? get linkType;
  @override //弹窗链接类型 1- 需要识别二维码 2-不需要识别二维码
  String? get linkUrl;
  @override //跳转地址
  String? get pictureUrl;
  @override //弹窗图片地址
  int? get popupId;
  @override //弹窗主键id
  String? get popupName;
  @override //主键名字
  int? get configType;
  @override //弹窗配置类型  1-默认配置  2-人工培植 3-系统调用
  String? get contentJson;
  @override //系统调用弹窗所展示的json
  String? get contentDict;
  @override
  String? get systemActivateType;
  @override //系统调用活动类型(后期可能会根据不同的运营需求变化不同的展示样式): MALL_SYSTEM_COUPON(商城优惠券活动)
  bool? get popUpIsShow;
  @override
  int? get controllerPosition;
  @override
  @JsonKey(ignore: true)
  _$$_cmsAdsInfoCopyWith<_$_cmsAdsInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

courseOrderListPopInfo _$courseOrderListPopInfoFromJson(
    Map<String, dynamic> json) {
  return _courseOrderListPopInfo.fromJson(json);
}

/// @nodoc
mixin _$courseOrderListPopInfo {
  bool? get showPopup => throw _privateConstructorUsedError; //是否显示
  String? get toSettingUrl => throw _privateConstructorUsedError; //跳转路由
  List<courseOrderListItemPopInfo>? get subjectInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $courseOrderListPopInfoCopyWith<courseOrderListPopInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $courseOrderListPopInfoCopyWith<$Res> {
  factory $courseOrderListPopInfoCopyWith(courseOrderListPopInfo value,
          $Res Function(courseOrderListPopInfo) then) =
      _$courseOrderListPopInfoCopyWithImpl<$Res, courseOrderListPopInfo>;
  @useResult
  $Res call(
      {bool? showPopup,
      String? toSettingUrl,
      List<courseOrderListItemPopInfo>? subjectInfoList});
}

/// @nodoc
class _$courseOrderListPopInfoCopyWithImpl<$Res,
        $Val extends courseOrderListPopInfo>
    implements $courseOrderListPopInfoCopyWith<$Res> {
  _$courseOrderListPopInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showPopup = freezed,
    Object? toSettingUrl = freezed,
    Object? subjectInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      showPopup: freezed == showPopup
          ? _value.showPopup
          : showPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      toSettingUrl: freezed == toSettingUrl
          ? _value.toSettingUrl
          : toSettingUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value.subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<courseOrderListItemPopInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_courseOrderListPopInfoCopyWith<$Res>
    implements $courseOrderListPopInfoCopyWith<$Res> {
  factory _$$_courseOrderListPopInfoCopyWith(_$_courseOrderListPopInfo value,
          $Res Function(_$_courseOrderListPopInfo) then) =
      __$$_courseOrderListPopInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? showPopup,
      String? toSettingUrl,
      List<courseOrderListItemPopInfo>? subjectInfoList});
}

/// @nodoc
class __$$_courseOrderListPopInfoCopyWithImpl<$Res>
    extends _$courseOrderListPopInfoCopyWithImpl<$Res,
        _$_courseOrderListPopInfo>
    implements _$$_courseOrderListPopInfoCopyWith<$Res> {
  __$$_courseOrderListPopInfoCopyWithImpl(_$_courseOrderListPopInfo _value,
      $Res Function(_$_courseOrderListPopInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showPopup = freezed,
    Object? toSettingUrl = freezed,
    Object? subjectInfoList = freezed,
  }) {
    return _then(_$_courseOrderListPopInfo(
      showPopup: freezed == showPopup
          ? _value.showPopup
          : showPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      toSettingUrl: freezed == toSettingUrl
          ? _value.toSettingUrl
          : toSettingUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value._subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<courseOrderListItemPopInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_courseOrderListPopInfo implements _courseOrderListPopInfo {
  const _$_courseOrderListPopInfo(
      {this.showPopup,
      this.toSettingUrl,
      final List<courseOrderListItemPopInfo>? subjectInfoList})
      : _subjectInfoList = subjectInfoList;

  factory _$_courseOrderListPopInfo.fromJson(Map<String, dynamic> json) =>
      _$$_courseOrderListPopInfoFromJson(json);

  @override
  final bool? showPopup;
//是否显示
  @override
  final String? toSettingUrl;
//跳转路由
  final List<courseOrderListItemPopInfo>? _subjectInfoList;
//跳转路由
  @override
  List<courseOrderListItemPopInfo>? get subjectInfoList {
    final value = _subjectInfoList;
    if (value == null) return null;
    if (_subjectInfoList is EqualUnmodifiableListView) return _subjectInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'courseOrderListPopInfo(showPopup: $showPopup, toSettingUrl: $toSettingUrl, subjectInfoList: $subjectInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_courseOrderListPopInfo &&
            (identical(other.showPopup, showPopup) ||
                other.showPopup == showPopup) &&
            (identical(other.toSettingUrl, toSettingUrl) ||
                other.toSettingUrl == toSettingUrl) &&
            const DeepCollectionEquality()
                .equals(other._subjectInfoList, _subjectInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, showPopup, toSettingUrl,
      const DeepCollectionEquality().hash(_subjectInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_courseOrderListPopInfoCopyWith<_$_courseOrderListPopInfo> get copyWith =>
      __$$_courseOrderListPopInfoCopyWithImpl<_$_courseOrderListPopInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_courseOrderListPopInfoToJson(
      this,
    );
  }
}

abstract class _courseOrderListPopInfo implements courseOrderListPopInfo {
  const factory _courseOrderListPopInfo(
          {final bool? showPopup,
          final String? toSettingUrl,
          final List<courseOrderListItemPopInfo>? subjectInfoList}) =
      _$_courseOrderListPopInfo;

  factory _courseOrderListPopInfo.fromJson(Map<String, dynamic> json) =
      _$_courseOrderListPopInfo.fromJson;

  @override
  bool? get showPopup;
  @override //是否显示
  String? get toSettingUrl;
  @override //跳转路由
  List<courseOrderListItemPopInfo>? get subjectInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_courseOrderListPopInfoCopyWith<_$_courseOrderListPopInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

courseOrderListItemPopInfo _$courseOrderListItemPopInfoFromJson(
    Map<String, dynamic> json) {
  return _courseOrderListItemPopInfo.fromJson(json);
}

/// @nodoc
mixin _$courseOrderListItemPopInfo {
  String? get fontColor => throw _privateConstructorUsedError; //字体颜色
  bool? get selected => throw _privateConstructorUsedError; //是否选中
  String? get subjectName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $courseOrderListItemPopInfoCopyWith<courseOrderListItemPopInfo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $courseOrderListItemPopInfoCopyWith<$Res> {
  factory $courseOrderListItemPopInfoCopyWith(courseOrderListItemPopInfo value,
          $Res Function(courseOrderListItemPopInfo) then) =
      _$courseOrderListItemPopInfoCopyWithImpl<$Res,
          courseOrderListItemPopInfo>;
  @useResult
  $Res call({String? fontColor, bool? selected, String? subjectName});
}

/// @nodoc
class _$courseOrderListItemPopInfoCopyWithImpl<$Res,
        $Val extends courseOrderListItemPopInfo>
    implements $courseOrderListItemPopInfoCopyWith<$Res> {
  _$courseOrderListItemPopInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontColor = freezed,
    Object? selected = freezed,
    Object? subjectName = freezed,
  }) {
    return _then(_value.copyWith(
      fontColor: freezed == fontColor
          ? _value.fontColor
          : fontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_courseOrderListItemPopInfoCopyWith<$Res>
    implements $courseOrderListItemPopInfoCopyWith<$Res> {
  factory _$$_courseOrderListItemPopInfoCopyWith(
          _$_courseOrderListItemPopInfo value,
          $Res Function(_$_courseOrderListItemPopInfo) then) =
      __$$_courseOrderListItemPopInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? fontColor, bool? selected, String? subjectName});
}

/// @nodoc
class __$$_courseOrderListItemPopInfoCopyWithImpl<$Res>
    extends _$courseOrderListItemPopInfoCopyWithImpl<$Res,
        _$_courseOrderListItemPopInfo>
    implements _$$_courseOrderListItemPopInfoCopyWith<$Res> {
  __$$_courseOrderListItemPopInfoCopyWithImpl(
      _$_courseOrderListItemPopInfo _value,
      $Res Function(_$_courseOrderListItemPopInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fontColor = freezed,
    Object? selected = freezed,
    Object? subjectName = freezed,
  }) {
    return _then(_$_courseOrderListItemPopInfo(
      fontColor: freezed == fontColor
          ? _value.fontColor
          : fontColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_courseOrderListItemPopInfo implements _courseOrderListItemPopInfo {
  const _$_courseOrderListItemPopInfo(
      {this.fontColor, this.selected, this.subjectName});

  factory _$_courseOrderListItemPopInfo.fromJson(Map<String, dynamic> json) =>
      _$$_courseOrderListItemPopInfoFromJson(json);

  @override
  final String? fontColor;
//字体颜色
  @override
  final bool? selected;
//是否选中
  @override
  final String? subjectName;

  @override
  String toString() {
    return 'courseOrderListItemPopInfo(fontColor: $fontColor, selected: $selected, subjectName: $subjectName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_courseOrderListItemPopInfo &&
            (identical(other.fontColor, fontColor) ||
                other.fontColor == fontColor) &&
            (identical(other.selected, selected) ||
                other.selected == selected) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, fontColor, selected, subjectName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_courseOrderListItemPopInfoCopyWith<_$_courseOrderListItemPopInfo>
      get copyWith => __$$_courseOrderListItemPopInfoCopyWithImpl<
          _$_courseOrderListItemPopInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_courseOrderListItemPopInfoToJson(
      this,
    );
  }
}

abstract class _courseOrderListItemPopInfo
    implements courseOrderListItemPopInfo {
  const factory _courseOrderListItemPopInfo(
      {final String? fontColor,
      final bool? selected,
      final String? subjectName}) = _$_courseOrderListItemPopInfo;

  factory _courseOrderListItemPopInfo.fromJson(Map<String, dynamic> json) =
      _$_courseOrderListItemPopInfo.fromJson;

  @override
  String? get fontColor;
  @override //字体颜色
  bool? get selected;
  @override //是否选中
  String? get subjectName;
  @override
  @JsonKey(ignore: true)
  _$$_courseOrderListItemPopInfoCopyWith<_$_courseOrderListItemPopInfo>
      get copyWith => throw _privateConstructorUsedError;
}

BirthdayBlessingPosterType _$BirthdayBlessingPosterTypeFromJson(
    Map<String, dynamic> json) {
  return _BirthdayBlessingPosterType.fromJson(json);
}

/// @nodoc
mixin _$BirthdayBlessingPosterType {
  String? get nickname => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  int? get age => throw _privateConstructorUsedError;
  String? get blessingContent => throw _privateConstructorUsedError;
  String? get getCourseUrl => throw _privateConstructorUsedError;
  int? get isShow => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BirthdayBlessingPosterTypeCopyWith<BirthdayBlessingPosterType>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BirthdayBlessingPosterTypeCopyWith<$Res> {
  factory $BirthdayBlessingPosterTypeCopyWith(BirthdayBlessingPosterType value,
          $Res Function(BirthdayBlessingPosterType) then) =
      _$BirthdayBlessingPosterTypeCopyWithImpl<$Res,
          BirthdayBlessingPosterType>;
  @useResult
  $Res call(
      {String? nickname,
      String? avatarUrl,
      int? age,
      String? blessingContent,
      String? getCourseUrl,
      int? isShow});
}

/// @nodoc
class _$BirthdayBlessingPosterTypeCopyWithImpl<$Res,
        $Val extends BirthdayBlessingPosterType>
    implements $BirthdayBlessingPosterTypeCopyWith<$Res> {
  _$BirthdayBlessingPosterTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? age = freezed,
    Object? blessingContent = freezed,
    Object? getCourseUrl = freezed,
    Object? isShow = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      blessingContent: freezed == blessingContent
          ? _value.blessingContent
          : blessingContent // ignore: cast_nullable_to_non_nullable
              as String?,
      getCourseUrl: freezed == getCourseUrl
          ? _value.getCourseUrl
          : getCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isShow: freezed == isShow
          ? _value.isShow
          : isShow // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BirthdayBlessingPosterTypeCopyWith<$Res>
    implements $BirthdayBlessingPosterTypeCopyWith<$Res> {
  factory _$$_BirthdayBlessingPosterTypeCopyWith(
          _$_BirthdayBlessingPosterType value,
          $Res Function(_$_BirthdayBlessingPosterType) then) =
      __$$_BirthdayBlessingPosterTypeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      String? avatarUrl,
      int? age,
      String? blessingContent,
      String? getCourseUrl,
      int? isShow});
}

/// @nodoc
class __$$_BirthdayBlessingPosterTypeCopyWithImpl<$Res>
    extends _$BirthdayBlessingPosterTypeCopyWithImpl<$Res,
        _$_BirthdayBlessingPosterType>
    implements _$$_BirthdayBlessingPosterTypeCopyWith<$Res> {
  __$$_BirthdayBlessingPosterTypeCopyWithImpl(
      _$_BirthdayBlessingPosterType _value,
      $Res Function(_$_BirthdayBlessingPosterType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? age = freezed,
    Object? blessingContent = freezed,
    Object? getCourseUrl = freezed,
    Object? isShow = freezed,
  }) {
    return _then(_$_BirthdayBlessingPosterType(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      blessingContent: freezed == blessingContent
          ? _value.blessingContent
          : blessingContent // ignore: cast_nullable_to_non_nullable
              as String?,
      getCourseUrl: freezed == getCourseUrl
          ? _value.getCourseUrl
          : getCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isShow: freezed == isShow
          ? _value.isShow
          : isShow // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BirthdayBlessingPosterType implements _BirthdayBlessingPosterType {
  const _$_BirthdayBlessingPosterType(
      {this.nickname,
      this.avatarUrl,
      this.age,
      this.blessingContent,
      this.getCourseUrl,
      this.isShow});

  factory _$_BirthdayBlessingPosterType.fromJson(Map<String, dynamic> json) =>
      _$$_BirthdayBlessingPosterTypeFromJson(json);

  @override
  final String? nickname;
  @override
  final String? avatarUrl;
  @override
  final int? age;
  @override
  final String? blessingContent;
  @override
  final String? getCourseUrl;
  @override
  final int? isShow;

  @override
  String toString() {
    return 'BirthdayBlessingPosterType(nickname: $nickname, avatarUrl: $avatarUrl, age: $age, blessingContent: $blessingContent, getCourseUrl: $getCourseUrl, isShow: $isShow)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BirthdayBlessingPosterType &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.blessingContent, blessingContent) ||
                other.blessingContent == blessingContent) &&
            (identical(other.getCourseUrl, getCourseUrl) ||
                other.getCourseUrl == getCourseUrl) &&
            (identical(other.isShow, isShow) || other.isShow == isShow));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickname, avatarUrl, age,
      blessingContent, getCourseUrl, isShow);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BirthdayBlessingPosterTypeCopyWith<_$_BirthdayBlessingPosterType>
      get copyWith => __$$_BirthdayBlessingPosterTypeCopyWithImpl<
          _$_BirthdayBlessingPosterType>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BirthdayBlessingPosterTypeToJson(
      this,
    );
  }
}

abstract class _BirthdayBlessingPosterType
    implements BirthdayBlessingPosterType {
  const factory _BirthdayBlessingPosterType(
      {final String? nickname,
      final String? avatarUrl,
      final int? age,
      final String? blessingContent,
      final String? getCourseUrl,
      final int? isShow}) = _$_BirthdayBlessingPosterType;

  factory _BirthdayBlessingPosterType.fromJson(Map<String, dynamic> json) =
      _$_BirthdayBlessingPosterType.fromJson;

  @override
  String? get nickname;
  @override
  String? get avatarUrl;
  @override
  int? get age;
  @override
  String? get blessingContent;
  @override
  String? get getCourseUrl;
  @override
  int? get isShow;
  @override
  @JsonKey(ignore: true)
  _$$_BirthdayBlessingPosterTypeCopyWith<_$_BirthdayBlessingPosterType>
      get copyWith => throw _privateConstructorUsedError;
}
