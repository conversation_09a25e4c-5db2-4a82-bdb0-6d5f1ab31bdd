// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_courses_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_UserCoursesModel _$$_UserCoursesModelFromJson(Map<String, dynamic> json) =>
    _$_UserCoursesModel(
      subjectTabList: (json['subjectTabList'] as List<dynamic>?)
          ?.map((e) => SubjectTabModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      simpleCourseCardList: (json['simpleCourseCardList'] as List<dynamic>?)
          ?.map(
              (e) => SimpleCourseCardModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_UserCoursesModelToJson(_$_UserCoursesModel instance) =>
    <String, dynamic>{
      'subjectTabList': instance.subjectTabList,
      'simpleCourseCardList': instance.simpleCourseCardList,
    };

_$_SubjectTabModel _$$_SubjectTabModelFromJson(Map<String, dynamic> json) =>
    _$_SubjectTabModel(
      type: json['type'] as int?,
      typeDesc: json['typeDesc'] as String?,
      icon: json['icon'] as String?,
      subjectColor: json['subjectColor'] as String?,
      selectedIcon: json['selectedIcon'] as String?,
      selectedTextColor: json['selectedTextColor'] as String?,
      isPosition: json['isPosition'] as bool?,
      activateIconShow: json['activateIconShow'] as bool?,
      addTeacherTipIconShow: json['addTeacherTipIconShow'] as bool?,
      showScene: json['showScene'] as int?,
      needClassificationInSubject: json['needClassificationInSubject'] as int?,
    );

Map<String, dynamic> _$$_SubjectTabModelToJson(_$_SubjectTabModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'typeDesc': instance.typeDesc,
      'icon': instance.icon,
      'subjectColor': instance.subjectColor,
      'selectedIcon': instance.selectedIcon,
      'selectedTextColor': instance.selectedTextColor,
      'isPosition': instance.isPosition,
      'activateIconShow': instance.activateIconShow,
      'addTeacherTipIconShow': instance.addTeacherTipIconShow,
      'showScene': instance.showScene,
      'needClassificationInSubject': instance.needClassificationInSubject,
    };

_$_SimpleCourseCardModel _$$_SimpleCourseCardModelFromJson(
        Map<String, dynamic> json) =>
    _$_SimpleCourseCardModel(
      courseName: json['courseName'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      courseLabel: json['courseLabel'] as String?,
      courseCover: json['courseCover'] as String?,
      subjectType: json['subjectType'] as int?,
      courseStatus: json['courseStatus'] as int?,
      courseSegment: json['courseSegment'] as String?,
      courseProgressPercent:
          (json['courseProgressPercent'] as num?)?.toDouble(),
      totalLessonCount: json['totalLessonCount'] as int?,
      finishedLessonCount: json['finishedLessonCount'] as int?,
      activateStatus: json['activateStatus'] as int?,
      route: json['route'] as String?,
      bgImage: json['bgImage'] as String?,
      subjectColor: json['subjectColor'] as String?,
      buttonText: json['buttonText'] as String?,
      unactivatedReason: json['unactivatedReason'] as String?,
      waitActivateCardInfo: json['waitActivateCardInfo'] == null
          ? null
          : WaitActivateCardInfoModel.fromJson(
              json['waitActivateCardInfo'] as Map<String, dynamic>),
      mainCourseName: json['mainCourseName'] as String?,
    );

Map<String, dynamic> _$$_SimpleCourseCardModelToJson(
        _$_SimpleCourseCardModel instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'courseLabel': instance.courseLabel,
      'courseCover': instance.courseCover,
      'subjectType': instance.subjectType,
      'courseStatus': instance.courseStatus,
      'courseSegment': instance.courseSegment,
      'courseProgressPercent': instance.courseProgressPercent,
      'totalLessonCount': instance.totalLessonCount,
      'finishedLessonCount': instance.finishedLessonCount,
      'activateStatus': instance.activateStatus,
      'route': instance.route,
      'bgImage': instance.bgImage,
      'subjectColor': instance.subjectColor,
      'buttonText': instance.buttonText,
      'unactivatedReason': instance.unactivatedReason,
      'waitActivateCardInfo': instance.waitActivateCardInfo,
      'mainCourseName': instance.mainCourseName,
    };

_$_WaitActivateCardInfoModel _$$_WaitActivateCardInfoModelFromJson(
        Map<String, dynamic> json) =>
    _$_WaitActivateCardInfoModel(
      status: json['status'] as int?,
      route: json['route'] as String?,
      canActiveButtonText: json['canActiveButtonText'] as String?,
      nonActivePrompt: json['nonActivePrompt'] as String?,
    );

Map<String, dynamic> _$$_WaitActivateCardInfoModelToJson(
        _$_WaitActivateCardInfoModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'route': instance.route,
      'canActiveButtonText': instance.canActiveButtonText,
      'nonActivePrompt': instance.nonActivePrompt,
    };
