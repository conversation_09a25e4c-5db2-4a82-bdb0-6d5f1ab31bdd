// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_courses_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

UserCoursesModel _$UserCoursesModelFromJson(Map<String, dynamic> json) {
  return _UserCoursesModel.fromJson(json);
}

/// @nodoc
mixin _$UserCoursesModel {
  List<SubjectTabModel>? get subjectTabList =>
      throw _privateConstructorUsedError;
  List<SimpleCourseCardModel>? get simpleCourseCardList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCoursesModelCopyWith<UserCoursesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCoursesModelCopyWith<$Res> {
  factory $UserCoursesModelCopyWith(
          UserCoursesModel value, $Res Function(UserCoursesModel) then) =
      _$UserCoursesModelCopyWithImpl<$Res, UserCoursesModel>;
  @useResult
  $Res call(
      {List<SubjectTabModel>? subjectTabList,
      List<SimpleCourseCardModel>? simpleCourseCardList});
}

/// @nodoc
class _$UserCoursesModelCopyWithImpl<$Res, $Val extends UserCoursesModel>
    implements $UserCoursesModelCopyWith<$Res> {
  _$UserCoursesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? simpleCourseCardList = freezed,
  }) {
    return _then(_value.copyWith(
      subjectTabList: freezed == subjectTabList
          ? _value.subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTabModel>?,
      simpleCourseCardList: freezed == simpleCourseCardList
          ? _value.simpleCourseCardList
          : simpleCourseCardList // ignore: cast_nullable_to_non_nullable
              as List<SimpleCourseCardModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserCoursesModelCopyWith<$Res>
    implements $UserCoursesModelCopyWith<$Res> {
  factory _$$_UserCoursesModelCopyWith(
          _$_UserCoursesModel value, $Res Function(_$_UserCoursesModel) then) =
      __$$_UserCoursesModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectTabModel>? subjectTabList,
      List<SimpleCourseCardModel>? simpleCourseCardList});
}

/// @nodoc
class __$$_UserCoursesModelCopyWithImpl<$Res>
    extends _$UserCoursesModelCopyWithImpl<$Res, _$_UserCoursesModel>
    implements _$$_UserCoursesModelCopyWith<$Res> {
  __$$_UserCoursesModelCopyWithImpl(
      _$_UserCoursesModel _value, $Res Function(_$_UserCoursesModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? simpleCourseCardList = freezed,
  }) {
    return _then(_$_UserCoursesModel(
      subjectTabList: freezed == subjectTabList
          ? _value._subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTabModel>?,
      simpleCourseCardList: freezed == simpleCourseCardList
          ? _value._simpleCourseCardList
          : simpleCourseCardList // ignore: cast_nullable_to_non_nullable
              as List<SimpleCourseCardModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserCoursesModel implements _UserCoursesModel {
  _$_UserCoursesModel(
      {required final List<SubjectTabModel>? subjectTabList,
      required final List<SimpleCourseCardModel>? simpleCourseCardList})
      : _subjectTabList = subjectTabList,
        _simpleCourseCardList = simpleCourseCardList;

  factory _$_UserCoursesModel.fromJson(Map<String, dynamic> json) =>
      _$$_UserCoursesModelFromJson(json);

  final List<SubjectTabModel>? _subjectTabList;
  @override
  List<SubjectTabModel>? get subjectTabList {
    final value = _subjectTabList;
    if (value == null) return null;
    if (_subjectTabList is EqualUnmodifiableListView) return _subjectTabList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SimpleCourseCardModel>? _simpleCourseCardList;
  @override
  List<SimpleCourseCardModel>? get simpleCourseCardList {
    final value = _simpleCourseCardList;
    if (value == null) return null;
    if (_simpleCourseCardList is EqualUnmodifiableListView)
      return _simpleCourseCardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'UserCoursesModel(subjectTabList: $subjectTabList, simpleCourseCardList: $simpleCourseCardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserCoursesModel &&
            const DeepCollectionEquality()
                .equals(other._subjectTabList, _subjectTabList) &&
            const DeepCollectionEquality()
                .equals(other._simpleCourseCardList, _simpleCourseCardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectTabList),
      const DeepCollectionEquality().hash(_simpleCourseCardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserCoursesModelCopyWith<_$_UserCoursesModel> get copyWith =>
      __$$_UserCoursesModelCopyWithImpl<_$_UserCoursesModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserCoursesModelToJson(
      this,
    );
  }
}

abstract class _UserCoursesModel implements UserCoursesModel {
  factory _UserCoursesModel(
          {required final List<SubjectTabModel>? subjectTabList,
          required final List<SimpleCourseCardModel>? simpleCourseCardList}) =
      _$_UserCoursesModel;

  factory _UserCoursesModel.fromJson(Map<String, dynamic> json) =
      _$_UserCoursesModel.fromJson;

  @override
  List<SubjectTabModel>? get subjectTabList;
  @override
  List<SimpleCourseCardModel>? get simpleCourseCardList;
  @override
  @JsonKey(ignore: true)
  _$$_UserCoursesModelCopyWith<_$_UserCoursesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectTabModel _$SubjectTabModelFromJson(Map<String, dynamic> json) {
  return _SubjectTabModel.fromJson(json);
}

/// @nodoc
mixin _$SubjectTabModel {
  int? get type => throw _privateConstructorUsedError;
  String? get typeDesc => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;
  String? get selectedIcon => throw _privateConstructorUsedError;
  String? get selectedTextColor => throw _privateConstructorUsedError;
  bool? get isPosition => throw _privateConstructorUsedError;
  bool? get activateIconShow => throw _privateConstructorUsedError;
  bool? get addTeacherTipIconShow => throw _privateConstructorUsedError;
  int? get showScene => throw _privateConstructorUsedError;
  int? get needClassificationInSubject => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectTabModelCopyWith<SubjectTabModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectTabModelCopyWith<$Res> {
  factory $SubjectTabModelCopyWith(
          SubjectTabModel value, $Res Function(SubjectTabModel) then) =
      _$SubjectTabModelCopyWithImpl<$Res, SubjectTabModel>;
  @useResult
  $Res call(
      {int? type,
      String? typeDesc,
      String? icon,
      String? subjectColor,
      String? selectedIcon,
      String? selectedTextColor,
      bool? isPosition,
      bool? activateIconShow,
      bool? addTeacherTipIconShow,
      int? showScene,
      int? needClassificationInSubject});
}

/// @nodoc
class _$SubjectTabModelCopyWithImpl<$Res, $Val extends SubjectTabModel>
    implements $SubjectTabModelCopyWith<$Res> {
  _$SubjectTabModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? icon = freezed,
    Object? subjectColor = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? isPosition = freezed,
    Object? activateIconShow = freezed,
    Object? addTeacherTipIconShow = freezed,
    Object? showScene = freezed,
    Object? needClassificationInSubject = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      activateIconShow: freezed == activateIconShow
          ? _value.activateIconShow
          : activateIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherTipIconShow: freezed == addTeacherTipIconShow
          ? _value.addTeacherTipIconShow
          : addTeacherTipIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      showScene: freezed == showScene
          ? _value.showScene
          : showScene // ignore: cast_nullable_to_non_nullable
              as int?,
      needClassificationInSubject: freezed == needClassificationInSubject
          ? _value.needClassificationInSubject
          : needClassificationInSubject // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectTabModelCopyWith<$Res>
    implements $SubjectTabModelCopyWith<$Res> {
  factory _$$_SubjectTabModelCopyWith(
          _$_SubjectTabModel value, $Res Function(_$_SubjectTabModel) then) =
      __$$_SubjectTabModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      String? typeDesc,
      String? icon,
      String? subjectColor,
      String? selectedIcon,
      String? selectedTextColor,
      bool? isPosition,
      bool? activateIconShow,
      bool? addTeacherTipIconShow,
      int? showScene,
      int? needClassificationInSubject});
}

/// @nodoc
class __$$_SubjectTabModelCopyWithImpl<$Res>
    extends _$SubjectTabModelCopyWithImpl<$Res, _$_SubjectTabModel>
    implements _$$_SubjectTabModelCopyWith<$Res> {
  __$$_SubjectTabModelCopyWithImpl(
      _$_SubjectTabModel _value, $Res Function(_$_SubjectTabModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? icon = freezed,
    Object? subjectColor = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? isPosition = freezed,
    Object? activateIconShow = freezed,
    Object? addTeacherTipIconShow = freezed,
    Object? showScene = freezed,
    Object? needClassificationInSubject = freezed,
  }) {
    return _then(_$_SubjectTabModel(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      activateIconShow: freezed == activateIconShow
          ? _value.activateIconShow
          : activateIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherTipIconShow: freezed == addTeacherTipIconShow
          ? _value.addTeacherTipIconShow
          : addTeacherTipIconShow // ignore: cast_nullable_to_non_nullable
              as bool?,
      showScene: freezed == showScene
          ? _value.showScene
          : showScene // ignore: cast_nullable_to_non_nullable
              as int?,
      needClassificationInSubject: freezed == needClassificationInSubject
          ? _value.needClassificationInSubject
          : needClassificationInSubject // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectTabModel implements _SubjectTabModel {
  const _$_SubjectTabModel(
      {this.type,
      this.typeDesc,
      this.icon,
      this.subjectColor,
      this.selectedIcon,
      this.selectedTextColor,
      this.isPosition,
      this.activateIconShow,
      this.addTeacherTipIconShow,
      this.showScene,
      this.needClassificationInSubject});

  factory _$_SubjectTabModel.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectTabModelFromJson(json);

  @override
  final int? type;
  @override
  final String? typeDesc;
  @override
  final String? icon;
  @override
  final String? subjectColor;
  @override
  final String? selectedIcon;
  @override
  final String? selectedTextColor;
  @override
  final bool? isPosition;
  @override
  final bool? activateIconShow;
  @override
  final bool? addTeacherTipIconShow;
  @override
  final int? showScene;
  @override
  final int? needClassificationInSubject;

  @override
  String toString() {
    return 'SubjectTabModel(type: $type, typeDesc: $typeDesc, icon: $icon, subjectColor: $subjectColor, selectedIcon: $selectedIcon, selectedTextColor: $selectedTextColor, isPosition: $isPosition, activateIconShow: $activateIconShow, addTeacherTipIconShow: $addTeacherTipIconShow, showScene: $showScene, needClassificationInSubject: $needClassificationInSubject)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectTabModel &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.typeDesc, typeDesc) ||
                other.typeDesc == typeDesc) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            (identical(other.selectedIcon, selectedIcon) ||
                other.selectedIcon == selectedIcon) &&
            (identical(other.selectedTextColor, selectedTextColor) ||
                other.selectedTextColor == selectedTextColor) &&
            (identical(other.isPosition, isPosition) ||
                other.isPosition == isPosition) &&
            (identical(other.activateIconShow, activateIconShow) ||
                other.activateIconShow == activateIconShow) &&
            (identical(other.addTeacherTipIconShow, addTeacherTipIconShow) ||
                other.addTeacherTipIconShow == addTeacherTipIconShow) &&
            (identical(other.showScene, showScene) ||
                other.showScene == showScene) &&
            (identical(other.needClassificationInSubject,
                    needClassificationInSubject) ||
                other.needClassificationInSubject ==
                    needClassificationInSubject));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      typeDesc,
      icon,
      subjectColor,
      selectedIcon,
      selectedTextColor,
      isPosition,
      activateIconShow,
      addTeacherTipIconShow,
      showScene,
      needClassificationInSubject);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectTabModelCopyWith<_$_SubjectTabModel> get copyWith =>
      __$$_SubjectTabModelCopyWithImpl<_$_SubjectTabModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectTabModelToJson(
      this,
    );
  }
}

abstract class _SubjectTabModel implements SubjectTabModel {
  const factory _SubjectTabModel(
      {final int? type,
      final String? typeDesc,
      final String? icon,
      final String? subjectColor,
      final String? selectedIcon,
      final String? selectedTextColor,
      final bool? isPosition,
      final bool? activateIconShow,
      final bool? addTeacherTipIconShow,
      final int? showScene,
      final int? needClassificationInSubject}) = _$_SubjectTabModel;

  factory _SubjectTabModel.fromJson(Map<String, dynamic> json) =
      _$_SubjectTabModel.fromJson;

  @override
  int? get type;
  @override
  String? get typeDesc;
  @override
  String? get icon;
  @override
  String? get subjectColor;
  @override
  String? get selectedIcon;
  @override
  String? get selectedTextColor;
  @override
  bool? get isPosition;
  @override
  bool? get activateIconShow;
  @override
  bool? get addTeacherTipIconShow;
  @override
  int? get showScene;
  @override
  int? get needClassificationInSubject;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectTabModelCopyWith<_$_SubjectTabModel> get copyWith =>
      throw _privateConstructorUsedError;
}

SimpleCourseCardModel _$SimpleCourseCardModelFromJson(
    Map<String, dynamic> json) {
  return _SimpleCourseCardModel.fromJson(json);
}

/// @nodoc
mixin _$SimpleCourseCardModel {
  String? get courseName => throw _privateConstructorUsedError;
  set courseName(String? value) => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  set courseId(int? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  set classKey(String? value) => throw _privateConstructorUsedError;
  String? get courseLabel => throw _privateConstructorUsedError;
  set courseLabel(String? value) => throw _privateConstructorUsedError;
  String? get courseCover => throw _privateConstructorUsedError;
  set courseCover(String? value) => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  set subjectType(int? value) => throw _privateConstructorUsedError;

  /// 课程状态(1:待激活，2:待开始，3:进行中，4:已完成)
  int? get courseStatus => throw _privateConstructorUsedError;

  /// 课程状态(1:待激活，2:待开始，3:进行中，4:已完成)
  set courseStatus(int? value) => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  set courseSegment(String? value) => throw _privateConstructorUsedError;
  double? get courseProgressPercent => throw _privateConstructorUsedError;
  set courseProgressPercent(double? value) =>
      throw _privateConstructorUsedError;
  int? get totalLessonCount => throw _privateConstructorUsedError;
  set totalLessonCount(int? value) => throw _privateConstructorUsedError;
  int? get finishedLessonCount => throw _privateConstructorUsedError;
  set finishedLessonCount(int? value) => throw _privateConstructorUsedError;
  int? get activateStatus => throw _privateConstructorUsedError;
  set activateStatus(int? value) => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;
  String? get bgImage => throw _privateConstructorUsedError;
  set bgImage(String? value) => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;
  set subjectColor(String? value) => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  set buttonText(String? value) => throw _privateConstructorUsedError;
  String? get unactivatedReason => throw _privateConstructorUsedError;
  set unactivatedReason(String? value) => throw _privateConstructorUsedError;
  WaitActivateCardInfoModel? get waitActivateCardInfo =>
      throw _privateConstructorUsedError;
  set waitActivateCardInfo(WaitActivateCardInfoModel? value) =>
      throw _privateConstructorUsedError;
  String? get mainCourseName => throw _privateConstructorUsedError;
  set mainCourseName(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SimpleCourseCardModelCopyWith<SimpleCourseCardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SimpleCourseCardModelCopyWith<$Res> {
  factory $SimpleCourseCardModelCopyWith(SimpleCourseCardModel value,
          $Res Function(SimpleCourseCardModel) then) =
      _$SimpleCourseCardModelCopyWithImpl<$Res, SimpleCourseCardModel>;
  @useResult
  $Res call(
      {String? courseName,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      String? courseLabel,
      String? courseCover,
      int? subjectType,
      int? courseStatus,
      String? courseSegment,
      double? courseProgressPercent,
      int? totalLessonCount,
      int? finishedLessonCount,
      int? activateStatus,
      String? route,
      String? bgImage,
      String? subjectColor,
      String? buttonText,
      String? unactivatedReason,
      WaitActivateCardInfoModel? waitActivateCardInfo,
      String? mainCourseName});

  $WaitActivateCardInfoModelCopyWith<$Res>? get waitActivateCardInfo;
}

/// @nodoc
class _$SimpleCourseCardModelCopyWithImpl<$Res,
        $Val extends SimpleCourseCardModel>
    implements $SimpleCourseCardModelCopyWith<$Res> {
  _$SimpleCourseCardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseLabel = freezed,
    Object? courseCover = freezed,
    Object? subjectType = freezed,
    Object? courseStatus = freezed,
    Object? courseSegment = freezed,
    Object? courseProgressPercent = freezed,
    Object? totalLessonCount = freezed,
    Object? finishedLessonCount = freezed,
    Object? activateStatus = freezed,
    Object? route = freezed,
    Object? bgImage = freezed,
    Object? subjectColor = freezed,
    Object? buttonText = freezed,
    Object? unactivatedReason = freezed,
    Object? waitActivateCardInfo = freezed,
    Object? mainCourseName = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCover: freezed == courseCover
          ? _value.courseCover
          : courseCover // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseProgressPercent: freezed == courseProgressPercent
          ? _value.courseProgressPercent
          : courseProgressPercent // ignore: cast_nullable_to_non_nullable
              as double?,
      totalLessonCount: freezed == totalLessonCount
          ? _value.totalLessonCount
          : totalLessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedLessonCount: freezed == finishedLessonCount
          ? _value.finishedLessonCount
          : finishedLessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      waitActivateCardInfo: freezed == waitActivateCardInfo
          ? _value.waitActivateCardInfo
          : waitActivateCardInfo // ignore: cast_nullable_to_non_nullable
              as WaitActivateCardInfoModel?,
      mainCourseName: freezed == mainCourseName
          ? _value.mainCourseName
          : mainCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WaitActivateCardInfoModelCopyWith<$Res>? get waitActivateCardInfo {
    if (_value.waitActivateCardInfo == null) {
      return null;
    }

    return $WaitActivateCardInfoModelCopyWith<$Res>(
        _value.waitActivateCardInfo!, (value) {
      return _then(_value.copyWith(waitActivateCardInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SimpleCourseCardModelCopyWith<$Res>
    implements $SimpleCourseCardModelCopyWith<$Res> {
  factory _$$_SimpleCourseCardModelCopyWith(_$_SimpleCourseCardModel value,
          $Res Function(_$_SimpleCourseCardModel) then) =
      __$$_SimpleCourseCardModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseName,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      String? courseLabel,
      String? courseCover,
      int? subjectType,
      int? courseStatus,
      String? courseSegment,
      double? courseProgressPercent,
      int? totalLessonCount,
      int? finishedLessonCount,
      int? activateStatus,
      String? route,
      String? bgImage,
      String? subjectColor,
      String? buttonText,
      String? unactivatedReason,
      WaitActivateCardInfoModel? waitActivateCardInfo,
      String? mainCourseName});

  @override
  $WaitActivateCardInfoModelCopyWith<$Res>? get waitActivateCardInfo;
}

/// @nodoc
class __$$_SimpleCourseCardModelCopyWithImpl<$Res>
    extends _$SimpleCourseCardModelCopyWithImpl<$Res, _$_SimpleCourseCardModel>
    implements _$$_SimpleCourseCardModelCopyWith<$Res> {
  __$$_SimpleCourseCardModelCopyWithImpl(_$_SimpleCourseCardModel _value,
      $Res Function(_$_SimpleCourseCardModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseLabel = freezed,
    Object? courseCover = freezed,
    Object? subjectType = freezed,
    Object? courseStatus = freezed,
    Object? courseSegment = freezed,
    Object? courseProgressPercent = freezed,
    Object? totalLessonCount = freezed,
    Object? finishedLessonCount = freezed,
    Object? activateStatus = freezed,
    Object? route = freezed,
    Object? bgImage = freezed,
    Object? subjectColor = freezed,
    Object? buttonText = freezed,
    Object? unactivatedReason = freezed,
    Object? waitActivateCardInfo = freezed,
    Object? mainCourseName = freezed,
  }) {
    return _then(_$_SimpleCourseCardModel(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCover: freezed == courseCover
          ? _value.courseCover
          : courseCover // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseProgressPercent: freezed == courseProgressPercent
          ? _value.courseProgressPercent
          : courseProgressPercent // ignore: cast_nullable_to_non_nullable
              as double?,
      totalLessonCount: freezed == totalLessonCount
          ? _value.totalLessonCount
          : totalLessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedLessonCount: freezed == finishedLessonCount
          ? _value.finishedLessonCount
          : finishedLessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      waitActivateCardInfo: freezed == waitActivateCardInfo
          ? _value.waitActivateCardInfo
          : waitActivateCardInfo // ignore: cast_nullable_to_non_nullable
              as WaitActivateCardInfoModel?,
      mainCourseName: freezed == mainCourseName
          ? _value.mainCourseName
          : mainCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SimpleCourseCardModel implements _SimpleCourseCardModel {
  _$_SimpleCourseCardModel(
      {this.courseName,
      this.courseId,
      this.courseKey,
      this.classId,
      this.classKey,
      this.courseLabel,
      this.courseCover,
      this.subjectType,
      this.courseStatus,
      this.courseSegment,
      this.courseProgressPercent,
      this.totalLessonCount,
      this.finishedLessonCount,
      this.activateStatus,
      this.route,
      this.bgImage,
      this.subjectColor,
      this.buttonText,
      this.unactivatedReason,
      this.waitActivateCardInfo,
      this.mainCourseName});

  factory _$_SimpleCourseCardModel.fromJson(Map<String, dynamic> json) =>
      _$$_SimpleCourseCardModelFromJson(json);

  @override
  String? courseName;
  @override
  int? courseId;
  @override
  String? courseKey;
  @override
  int? classId;
  @override
  String? classKey;
  @override
  String? courseLabel;
  @override
  String? courseCover;
  @override
  int? subjectType;

  /// 课程状态(1:待激活，2:待开始，3:进行中，4:已完成)
  @override
  int? courseStatus;
  @override
  String? courseSegment;
  @override
  double? courseProgressPercent;
  @override
  int? totalLessonCount;
  @override
  int? finishedLessonCount;
  @override
  int? activateStatus;
  @override
  String? route;
  @override
  String? bgImage;
  @override
  String? subjectColor;
  @override
  String? buttonText;
  @override
  String? unactivatedReason;
  @override
  WaitActivateCardInfoModel? waitActivateCardInfo;
  @override
  String? mainCourseName;

  @override
  String toString() {
    return 'SimpleCourseCardModel(courseName: $courseName, courseId: $courseId, courseKey: $courseKey, classId: $classId, classKey: $classKey, courseLabel: $courseLabel, courseCover: $courseCover, subjectType: $subjectType, courseStatus: $courseStatus, courseSegment: $courseSegment, courseProgressPercent: $courseProgressPercent, totalLessonCount: $totalLessonCount, finishedLessonCount: $finishedLessonCount, activateStatus: $activateStatus, route: $route, bgImage: $bgImage, subjectColor: $subjectColor, buttonText: $buttonText, unactivatedReason: $unactivatedReason, waitActivateCardInfo: $waitActivateCardInfo, mainCourseName: $mainCourseName)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SimpleCourseCardModelCopyWith<_$_SimpleCourseCardModel> get copyWith =>
      __$$_SimpleCourseCardModelCopyWithImpl<_$_SimpleCourseCardModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SimpleCourseCardModelToJson(
      this,
    );
  }
}

abstract class _SimpleCourseCardModel implements SimpleCourseCardModel {
  factory _SimpleCourseCardModel(
      {String? courseName,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      String? courseLabel,
      String? courseCover,
      int? subjectType,
      int? courseStatus,
      String? courseSegment,
      double? courseProgressPercent,
      int? totalLessonCount,
      int? finishedLessonCount,
      int? activateStatus,
      String? route,
      String? bgImage,
      String? subjectColor,
      String? buttonText,
      String? unactivatedReason,
      WaitActivateCardInfoModel? waitActivateCardInfo,
      String? mainCourseName}) = _$_SimpleCourseCardModel;

  factory _SimpleCourseCardModel.fromJson(Map<String, dynamic> json) =
      _$_SimpleCourseCardModel.fromJson;

  @override
  String? get courseName;
  set courseName(String? value);
  @override
  int? get courseId;
  set courseId(int? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  String? get classKey;
  set classKey(String? value);
  @override
  String? get courseLabel;
  set courseLabel(String? value);
  @override
  String? get courseCover;
  set courseCover(String? value);
  @override
  int? get subjectType;
  set subjectType(int? value);
  @override

  /// 课程状态(1:待激活，2:待开始，3:进行中，4:已完成)
  int? get courseStatus;

  /// 课程状态(1:待激活，2:待开始，3:进行中，4:已完成)
  set courseStatus(int? value);
  @override
  String? get courseSegment;
  set courseSegment(String? value);
  @override
  double? get courseProgressPercent;
  set courseProgressPercent(double? value);
  @override
  int? get totalLessonCount;
  set totalLessonCount(int? value);
  @override
  int? get finishedLessonCount;
  set finishedLessonCount(int? value);
  @override
  int? get activateStatus;
  set activateStatus(int? value);
  @override
  String? get route;
  set route(String? value);
  @override
  String? get bgImage;
  set bgImage(String? value);
  @override
  String? get subjectColor;
  set subjectColor(String? value);
  @override
  String? get buttonText;
  set buttonText(String? value);
  @override
  String? get unactivatedReason;
  set unactivatedReason(String? value);
  @override
  WaitActivateCardInfoModel? get waitActivateCardInfo;
  set waitActivateCardInfo(WaitActivateCardInfoModel? value);
  @override
  String? get mainCourseName;
  set mainCourseName(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_SimpleCourseCardModelCopyWith<_$_SimpleCourseCardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

WaitActivateCardInfoModel _$WaitActivateCardInfoModelFromJson(
    Map<String, dynamic> json) {
  return _WaitActivateCardInfoModel.fromJson(json);
}

/// @nodoc
mixin _$WaitActivateCardInfoModel {
  /// 课程是否可激活状态， 0：不可激活，1：可激活
  int? get status => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get canActiveButtonText => throw _privateConstructorUsedError;
  String? get nonActivePrompt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WaitActivateCardInfoModelCopyWith<WaitActivateCardInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WaitActivateCardInfoModelCopyWith<$Res> {
  factory $WaitActivateCardInfoModelCopyWith(WaitActivateCardInfoModel value,
          $Res Function(WaitActivateCardInfoModel) then) =
      _$WaitActivateCardInfoModelCopyWithImpl<$Res, WaitActivateCardInfoModel>;
  @useResult
  $Res call(
      {int? status,
      String? route,
      String? canActiveButtonText,
      String? nonActivePrompt});
}

/// @nodoc
class _$WaitActivateCardInfoModelCopyWithImpl<$Res,
        $Val extends WaitActivateCardInfoModel>
    implements $WaitActivateCardInfoModelCopyWith<$Res> {
  _$WaitActivateCardInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? route = freezed,
    Object? canActiveButtonText = freezed,
    Object? nonActivePrompt = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      canActiveButtonText: freezed == canActiveButtonText
          ? _value.canActiveButtonText
          : canActiveButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      nonActivePrompt: freezed == nonActivePrompt
          ? _value.nonActivePrompt
          : nonActivePrompt // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WaitActivateCardInfoModelCopyWith<$Res>
    implements $WaitActivateCardInfoModelCopyWith<$Res> {
  factory _$$_WaitActivateCardInfoModelCopyWith(
          _$_WaitActivateCardInfoModel value,
          $Res Function(_$_WaitActivateCardInfoModel) then) =
      __$$_WaitActivateCardInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? status,
      String? route,
      String? canActiveButtonText,
      String? nonActivePrompt});
}

/// @nodoc
class __$$_WaitActivateCardInfoModelCopyWithImpl<$Res>
    extends _$WaitActivateCardInfoModelCopyWithImpl<$Res,
        _$_WaitActivateCardInfoModel>
    implements _$$_WaitActivateCardInfoModelCopyWith<$Res> {
  __$$_WaitActivateCardInfoModelCopyWithImpl(
      _$_WaitActivateCardInfoModel _value,
      $Res Function(_$_WaitActivateCardInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? route = freezed,
    Object? canActiveButtonText = freezed,
    Object? nonActivePrompt = freezed,
  }) {
    return _then(_$_WaitActivateCardInfoModel(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      canActiveButtonText: freezed == canActiveButtonText
          ? _value.canActiveButtonText
          : canActiveButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      nonActivePrompt: freezed == nonActivePrompt
          ? _value.nonActivePrompt
          : nonActivePrompt // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_WaitActivateCardInfoModel implements _WaitActivateCardInfoModel {
  _$_WaitActivateCardInfoModel(
      {this.status,
      this.route,
      this.canActiveButtonText,
      this.nonActivePrompt});

  factory _$_WaitActivateCardInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_WaitActivateCardInfoModelFromJson(json);

  /// 课程是否可激活状态， 0：不可激活，1：可激活
  @override
  final int? status;
  @override
  final String? route;
  @override
  final String? canActiveButtonText;
  @override
  final String? nonActivePrompt;

  @override
  String toString() {
    return 'WaitActivateCardInfoModel(status: $status, route: $route, canActiveButtonText: $canActiveButtonText, nonActivePrompt: $nonActivePrompt)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WaitActivateCardInfoModel &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.canActiveButtonText, canActiveButtonText) ||
                other.canActiveButtonText == canActiveButtonText) &&
            (identical(other.nonActivePrompt, nonActivePrompt) ||
                other.nonActivePrompt == nonActivePrompt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, status, route, canActiveButtonText, nonActivePrompt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WaitActivateCardInfoModelCopyWith<_$_WaitActivateCardInfoModel>
      get copyWith => __$$_WaitActivateCardInfoModelCopyWithImpl<
          _$_WaitActivateCardInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_WaitActivateCardInfoModelToJson(
      this,
    );
  }
}

abstract class _WaitActivateCardInfoModel implements WaitActivateCardInfoModel {
  factory _WaitActivateCardInfoModel(
      {final int? status,
      final String? route,
      final String? canActiveButtonText,
      final String? nonActivePrompt}) = _$_WaitActivateCardInfoModel;

  factory _WaitActivateCardInfoModel.fromJson(Map<String, dynamic> json) =
      _$_WaitActivateCardInfoModel.fromJson;

  @override

  /// 课程是否可激活状态， 0：不可激活，1：可激活
  int? get status;
  @override
  String? get route;
  @override
  String? get canActiveButtonText;
  @override
  String? get nonActivePrompt;
  @override
  @JsonKey(ignore: true)
  _$$_WaitActivateCardInfoModelCopyWith<_$_WaitActivateCardInfoModel>
      get copyWith => throw _privateConstructorUsedError;
}
