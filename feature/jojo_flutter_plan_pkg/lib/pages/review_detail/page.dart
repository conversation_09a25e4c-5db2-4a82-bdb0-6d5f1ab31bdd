import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/view.dart';

class ReviewDetailPageModel extends BasePage {

  final int? loadingScene;
  final int? segmentId;
  final int? weekId;
  final String? subjectColor;
  final String? courseKey;
  final String? classKey;
  final String? classId;
  final String? buriedString;

  const ReviewDetailPageModel({
    Key? key, 
    this.loadingScene, 
    required this.classKey, 
    required this.classId, 
    required this.courseKey,
    required this.weekId, 
    required this.segmentId, 
    required this.buriedString,
    required this.subjectColor}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ReviewDetailPageModelState();
}

class _ReviewDetailPageModelState extends BaseState<ReviewDetailPageModel> with BasicInitPage {
  ReviewDetailCtrl? _ctrl;

  @override
  void onResume() {
    super.onResume();
    _ctrl?.courseCardAnimation = true;
    _ctrl?.getSegmentsInfo();
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) {
        return _ctrl ??= ReviewDetailCtrl(
          classKey: widget.classKey, 
          subjectColor: widget.subjectColor, 
          courseKey: widget.courseKey,
          segmentId: widget.segmentId,
          weekId: widget.weekId,
          classId: widget.classId,
          buriedString: widget.buriedString,);
      },
      child: BlocBuilder<ReviewDetailCtrl, ReviewDetailState>(builder: (context, state) {
         return ReviewDetailPageView(state: state, loadingScene: widget.loadingScene, subjectColor: widget.subjectColor,);
      }),
    );
  }
}
