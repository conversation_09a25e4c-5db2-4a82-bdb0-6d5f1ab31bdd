// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'review_assistant_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseSegmentsData _$CourseSegmentsDataFromJson(Map<String, dynamic> json) {
  return _CourseSegmentsData.fromJson(json);
}

/// @nodoc
mixin _$CourseSegmentsData {
  List<CourseSegmentData?>? get segments => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSegmentsDataCopyWith<CourseSegmentsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSegmentsDataCopyWith<$Res> {
  factory $CourseSegmentsDataCopyWith(
          CourseSegmentsData value, $Res Function(CourseSegmentsData) then) =
      _$CourseSegmentsDataCopyWithImpl<$Res, CourseSegmentsData>;
  @useResult
  $Res call({List<CourseSegmentData?>? segments});
}

/// @nodoc
class _$CourseSegmentsDataCopyWithImpl<$Res, $Val extends CourseSegmentsData>
    implements $CourseSegmentsDataCopyWith<$Res> {
  _$CourseSegmentsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segments = freezed,
  }) {
    return _then(_value.copyWith(
      segments: freezed == segments
          ? _value.segments
          : segments // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentData?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseSegmentsDataCopyWith<$Res>
    implements $CourseSegmentsDataCopyWith<$Res> {
  factory _$$_CourseSegmentsDataCopyWith(_$_CourseSegmentsData value,
          $Res Function(_$_CourseSegmentsData) then) =
      __$$_CourseSegmentsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CourseSegmentData?>? segments});
}

/// @nodoc
class __$$_CourseSegmentsDataCopyWithImpl<$Res>
    extends _$CourseSegmentsDataCopyWithImpl<$Res, _$_CourseSegmentsData>
    implements _$$_CourseSegmentsDataCopyWith<$Res> {
  __$$_CourseSegmentsDataCopyWithImpl(
      _$_CourseSegmentsData _value, $Res Function(_$_CourseSegmentsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segments = freezed,
  }) {
    return _then(_$_CourseSegmentsData(
      segments: freezed == segments
          ? _value._segments
          : segments // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentData?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSegmentsData implements _CourseSegmentsData {
  const _$_CourseSegmentsData({final List<CourseSegmentData?>? segments})
      : _segments = segments;

  factory _$_CourseSegmentsData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSegmentsDataFromJson(json);

  final List<CourseSegmentData?>? _segments;
  @override
  List<CourseSegmentData?>? get segments {
    final value = _segments;
    if (value == null) return null;
    if (_segments is EqualUnmodifiableListView) return _segments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseSegmentsData(segments: $segments)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseSegmentsData &&
            const DeepCollectionEquality().equals(other._segments, _segments));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_segments));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSegmentsDataCopyWith<_$_CourseSegmentsData> get copyWith =>
      __$$_CourseSegmentsDataCopyWithImpl<_$_CourseSegmentsData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSegmentsDataToJson(
      this,
    );
  }
}

abstract class _CourseSegmentsData implements CourseSegmentsData {
  const factory _CourseSegmentsData(
      {final List<CourseSegmentData?>? segments}) = _$_CourseSegmentsData;

  factory _CourseSegmentsData.fromJson(Map<String, dynamic> json) =
      _$_CourseSegmentsData.fromJson;

  @override
  List<CourseSegmentData?>? get segments;
  @override
  @JsonKey(ignore: true)
  _$$_CourseSegmentsDataCopyWith<_$_CourseSegmentsData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseSegmentData _$CourseSegmentDataFromJson(Map<String, dynamic> json) {
  return _CourseSegmentData.fromJson(json);
}

/// @nodoc
mixin _$CourseSegmentData {
  int? get segmentId => throw _privateConstructorUsedError;
  set segmentId(int? value) => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  set weekId(int? value) => throw _privateConstructorUsedError;
  int? get courseChildType => throw _privateConstructorUsedError;
  set courseChildType(int? value) => throw _privateConstructorUsedError;
  int? get unFinishLessonNum => throw _privateConstructorUsedError;
  set unFinishLessonNum(int? value) => throw _privateConstructorUsedError;
  bool? get lock => throw _privateConstructorUsedError;
  set lock(bool? value) => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  set segmentName(String? value) => throw _privateConstructorUsedError;
  List<CourseSegmentWeekData?>? get weekList =>
      throw _privateConstructorUsedError;
  set weekList(List<CourseSegmentWeekData?>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSegmentDataCopyWith<CourseSegmentData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSegmentDataCopyWith<$Res> {
  factory $CourseSegmentDataCopyWith(
          CourseSegmentData value, $Res Function(CourseSegmentData) then) =
      _$CourseSegmentDataCopyWithImpl<$Res, CourseSegmentData>;
  @useResult
  $Res call(
      {int? segmentId,
      int? weekId,
      int? courseChildType,
      int? unFinishLessonNum,
      bool? lock,
      String? segmentName,
      List<CourseSegmentWeekData?>? weekList});
}

/// @nodoc
class _$CourseSegmentDataCopyWithImpl<$Res, $Val extends CourseSegmentData>
    implements $CourseSegmentDataCopyWith<$Res> {
  _$CourseSegmentDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? courseChildType = freezed,
    Object? unFinishLessonNum = freezed,
    Object? lock = freezed,
    Object? segmentName = freezed,
    Object? weekList = freezed,
  }) {
    return _then(_value.copyWith(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishLessonNum: freezed == unFinishLessonNum
          ? _value.unFinishLessonNum
          : unFinishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      weekList: freezed == weekList
          ? _value.weekList
          : weekList // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentWeekData?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseSegmentDataCopyWith<$Res>
    implements $CourseSegmentDataCopyWith<$Res> {
  factory _$$_CourseSegmentDataCopyWith(_$_CourseSegmentData value,
          $Res Function(_$_CourseSegmentData) then) =
      __$$_CourseSegmentDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? segmentId,
      int? weekId,
      int? courseChildType,
      int? unFinishLessonNum,
      bool? lock,
      String? segmentName,
      List<CourseSegmentWeekData?>? weekList});
}

/// @nodoc
class __$$_CourseSegmentDataCopyWithImpl<$Res>
    extends _$CourseSegmentDataCopyWithImpl<$Res, _$_CourseSegmentData>
    implements _$$_CourseSegmentDataCopyWith<$Res> {
  __$$_CourseSegmentDataCopyWithImpl(
      _$_CourseSegmentData _value, $Res Function(_$_CourseSegmentData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? courseChildType = freezed,
    Object? unFinishLessonNum = freezed,
    Object? lock = freezed,
    Object? segmentName = freezed,
    Object? weekList = freezed,
  }) {
    return _then(_$_CourseSegmentData(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishLessonNum: freezed == unFinishLessonNum
          ? _value.unFinishLessonNum
          : unFinishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      weekList: freezed == weekList
          ? _value.weekList
          : weekList // ignore: cast_nullable_to_non_nullable
              as List<CourseSegmentWeekData?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSegmentData implements _CourseSegmentData {
  _$_CourseSegmentData(
      {this.segmentId,
      this.weekId,
      this.courseChildType,
      this.unFinishLessonNum,
      this.lock,
      this.segmentName,
      this.weekList});

  factory _$_CourseSegmentData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSegmentDataFromJson(json);

  @override
  int? segmentId;
  @override
  int? weekId;
  @override
  int? courseChildType;
  @override
  int? unFinishLessonNum;
  @override
  bool? lock;
  @override
  String? segmentName;
  @override
  List<CourseSegmentWeekData?>? weekList;

  @override
  String toString() {
    return 'CourseSegmentData(segmentId: $segmentId, weekId: $weekId, courseChildType: $courseChildType, unFinishLessonNum: $unFinishLessonNum, lock: $lock, segmentName: $segmentName, weekList: $weekList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSegmentDataCopyWith<_$_CourseSegmentData> get copyWith =>
      __$$_CourseSegmentDataCopyWithImpl<_$_CourseSegmentData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSegmentDataToJson(
      this,
    );
  }
}

abstract class _CourseSegmentData implements CourseSegmentData {
  factory _CourseSegmentData(
      {int? segmentId,
      int? weekId,
      int? courseChildType,
      int? unFinishLessonNum,
      bool? lock,
      String? segmentName,
      List<CourseSegmentWeekData?>? weekList}) = _$_CourseSegmentData;

  factory _CourseSegmentData.fromJson(Map<String, dynamic> json) =
      _$_CourseSegmentData.fromJson;

  @override
  int? get segmentId;
  set segmentId(int? value);
  @override
  int? get weekId;
  set weekId(int? value);
  @override
  int? get courseChildType;
  set courseChildType(int? value);
  @override
  int? get unFinishLessonNum;
  set unFinishLessonNum(int? value);
  @override
  bool? get lock;
  set lock(bool? value);
  @override
  String? get segmentName;
  set segmentName(String? value);
  @override
  List<CourseSegmentWeekData?>? get weekList;
  set weekList(List<CourseSegmentWeekData?>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseSegmentDataCopyWith<_$_CourseSegmentData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseSegmentWeekData _$CourseSegmentWeekDataFromJson(
    Map<String, dynamic> json) {
  return _CourseSegmentWeekData.fromJson(json);
}

/// @nodoc
mixin _$CourseSegmentWeekData {
  int? get weekId => throw _privateConstructorUsedError;
  set weekId(int? value) => throw _privateConstructorUsedError;
  int? get unFinishLessonNum => throw _privateConstructorUsedError;
  set unFinishLessonNum(int? value) => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;
  set weekName(String? value) => throw _privateConstructorUsedError;
  bool? get lock => throw _privateConstructorUsedError;
  set lock(bool? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSegmentWeekDataCopyWith<CourseSegmentWeekData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSegmentWeekDataCopyWith<$Res> {
  factory $CourseSegmentWeekDataCopyWith(CourseSegmentWeekData value,
          $Res Function(CourseSegmentWeekData) then) =
      _$CourseSegmentWeekDataCopyWithImpl<$Res, CourseSegmentWeekData>;
  @useResult
  $Res call(
      {int? weekId, int? unFinishLessonNum, String? weekName, bool? lock});
}

/// @nodoc
class _$CourseSegmentWeekDataCopyWithImpl<$Res,
        $Val extends CourseSegmentWeekData>
    implements $CourseSegmentWeekDataCopyWith<$Res> {
  _$CourseSegmentWeekDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? unFinishLessonNum = freezed,
    Object? weekName = freezed,
    Object? lock = freezed,
  }) {
    return _then(_value.copyWith(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishLessonNum: freezed == unFinishLessonNum
          ? _value.unFinishLessonNum
          : unFinishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseSegmentWeekDataCopyWith<$Res>
    implements $CourseSegmentWeekDataCopyWith<$Res> {
  factory _$$_CourseSegmentWeekDataCopyWith(_$_CourseSegmentWeekData value,
          $Res Function(_$_CourseSegmentWeekData) then) =
      __$$_CourseSegmentWeekDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? weekId, int? unFinishLessonNum, String? weekName, bool? lock});
}

/// @nodoc
class __$$_CourseSegmentWeekDataCopyWithImpl<$Res>
    extends _$CourseSegmentWeekDataCopyWithImpl<$Res, _$_CourseSegmentWeekData>
    implements _$$_CourseSegmentWeekDataCopyWith<$Res> {
  __$$_CourseSegmentWeekDataCopyWithImpl(_$_CourseSegmentWeekData _value,
      $Res Function(_$_CourseSegmentWeekData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? unFinishLessonNum = freezed,
    Object? weekName = freezed,
    Object? lock = freezed,
  }) {
    return _then(_$_CourseSegmentWeekData(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishLessonNum: freezed == unFinishLessonNum
          ? _value.unFinishLessonNum
          : unFinishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSegmentWeekData implements _CourseSegmentWeekData {
  _$_CourseSegmentWeekData(
      {this.weekId, this.unFinishLessonNum, this.weekName, this.lock});

  factory _$_CourseSegmentWeekData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSegmentWeekDataFromJson(json);

  @override
  int? weekId;
  @override
  int? unFinishLessonNum;
  @override
  String? weekName;
  @override
  bool? lock;

  @override
  String toString() {
    return 'CourseSegmentWeekData(weekId: $weekId, unFinishLessonNum: $unFinishLessonNum, weekName: $weekName, lock: $lock)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSegmentWeekDataCopyWith<_$_CourseSegmentWeekData> get copyWith =>
      __$$_CourseSegmentWeekDataCopyWithImpl<_$_CourseSegmentWeekData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSegmentWeekDataToJson(
      this,
    );
  }
}

abstract class _CourseSegmentWeekData implements CourseSegmentWeekData {
  factory _CourseSegmentWeekData(
      {int? weekId,
      int? unFinishLessonNum,
      String? weekName,
      bool? lock}) = _$_CourseSegmentWeekData;

  factory _CourseSegmentWeekData.fromJson(Map<String, dynamic> json) =
      _$_CourseSegmentWeekData.fromJson;

  @override
  int? get weekId;
  set weekId(int? value);
  @override
  int? get unFinishLessonNum;
  set unFinishLessonNum(int? value);
  @override
  String? get weekName;
  set weekName(String? value);
  @override
  bool? get lock;
  set lock(bool? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseSegmentWeekDataCopyWith<_$_CourseSegmentWeekData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseLessonsSearchData _$CourseLessonsSearchDataFromJson(
    Map<String, dynamic> json) {
  return _CourseLessonsSearchData.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonsSearchData {
  int? get pageNum => throw _privateConstructorUsedError;
  set pageNum(int? value) => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  set pageSize(int? value) => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  set total(int? value) => throw _privateConstructorUsedError;
  List<CourseLessonItem?>? get data => throw _privateConstructorUsedError;
  set data(List<CourseLessonItem?>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonsSearchDataCopyWith<CourseLessonsSearchData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonsSearchDataCopyWith<$Res> {
  factory $CourseLessonsSearchDataCopyWith(CourseLessonsSearchData value,
          $Res Function(CourseLessonsSearchData) then) =
      _$CourseLessonsSearchDataCopyWithImpl<$Res, CourseLessonsSearchData>;
  @useResult
  $Res call(
      {int? pageNum, int? pageSize, int? total, List<CourseLessonItem?>? data});
}

/// @nodoc
class _$CourseLessonsSearchDataCopyWithImpl<$Res,
        $Val extends CourseLessonsSearchData>
    implements $CourseLessonsSearchDataCopyWith<$Res> {
  _$CourseLessonsSearchDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? total = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonItem?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonsSearchDataCopyWith<$Res>
    implements $CourseLessonsSearchDataCopyWith<$Res> {
  factory _$$_CourseLessonsSearchDataCopyWith(_$_CourseLessonsSearchData value,
          $Res Function(_$_CourseLessonsSearchData) then) =
      __$$_CourseLessonsSearchDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? pageNum, int? pageSize, int? total, List<CourseLessonItem?>? data});
}

/// @nodoc
class __$$_CourseLessonsSearchDataCopyWithImpl<$Res>
    extends _$CourseLessonsSearchDataCopyWithImpl<$Res,
        _$_CourseLessonsSearchData>
    implements _$$_CourseLessonsSearchDataCopyWith<$Res> {
  __$$_CourseLessonsSearchDataCopyWithImpl(_$_CourseLessonsSearchData _value,
      $Res Function(_$_CourseLessonsSearchData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? total = freezed,
    Object? data = freezed,
  }) {
    return _then(_$_CourseLessonsSearchData(
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonItem?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonsSearchData implements _CourseLessonsSearchData {
  _$_CourseLessonsSearchData(
      {this.pageNum, this.pageSize, this.total, this.data});

  factory _$_CourseLessonsSearchData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonsSearchDataFromJson(json);

  @override
  int? pageNum;
  @override
  int? pageSize;
  @override
  int? total;
  @override
  List<CourseLessonItem?>? data;

  @override
  String toString() {
    return 'CourseLessonsSearchData(pageNum: $pageNum, pageSize: $pageSize, total: $total, data: $data)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonsSearchDataCopyWith<_$_CourseLessonsSearchData>
      get copyWith =>
          __$$_CourseLessonsSearchDataCopyWithImpl<_$_CourseLessonsSearchData>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonsSearchDataToJson(
      this,
    );
  }
}

abstract class _CourseLessonsSearchData implements CourseLessonsSearchData {
  factory _CourseLessonsSearchData(
      {int? pageNum,
      int? pageSize,
      int? total,
      List<CourseLessonItem?>? data}) = _$_CourseLessonsSearchData;

  factory _CourseLessonsSearchData.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonsSearchData.fromJson;

  @override
  int? get pageNum;
  set pageNum(int? value);
  @override
  int? get pageSize;
  set pageSize(int? value);
  @override
  int? get total;
  set total(int? value);
  @override
  List<CourseLessonItem?>? get data;
  set data(List<CourseLessonItem?>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonsSearchDataCopyWith<_$_CourseLessonsSearchData>
      get copyWith => throw _privateConstructorUsedError;
}

CourseLessonsData _$CourseLessonsDataFromJson(Map<String, dynamic> json) {
  return _CourseLessonsData.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonsData {
  List<CourseLessonData?>? get list => throw _privateConstructorUsedError;
  set list(List<CourseLessonData?>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonsDataCopyWith<CourseLessonsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonsDataCopyWith<$Res> {
  factory $CourseLessonsDataCopyWith(
          CourseLessonsData value, $Res Function(CourseLessonsData) then) =
      _$CourseLessonsDataCopyWithImpl<$Res, CourseLessonsData>;
  @useResult
  $Res call({List<CourseLessonData?>? list});
}

/// @nodoc
class _$CourseLessonsDataCopyWithImpl<$Res, $Val extends CourseLessonsData>
    implements $CourseLessonsDataCopyWith<$Res> {
  _$CourseLessonsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
  }) {
    return _then(_value.copyWith(
      list: freezed == list
          ? _value.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonData?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonsDataCopyWith<$Res>
    implements $CourseLessonsDataCopyWith<$Res> {
  factory _$$_CourseLessonsDataCopyWith(_$_CourseLessonsData value,
          $Res Function(_$_CourseLessonsData) then) =
      __$$_CourseLessonsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CourseLessonData?>? list});
}

/// @nodoc
class __$$_CourseLessonsDataCopyWithImpl<$Res>
    extends _$CourseLessonsDataCopyWithImpl<$Res, _$_CourseLessonsData>
    implements _$$_CourseLessonsDataCopyWith<$Res> {
  __$$_CourseLessonsDataCopyWithImpl(
      _$_CourseLessonsData _value, $Res Function(_$_CourseLessonsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
  }) {
    return _then(_$_CourseLessonsData(
      list: freezed == list
          ? _value.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonData?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonsData implements _CourseLessonsData {
  _$_CourseLessonsData({this.list});

  factory _$_CourseLessonsData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonsDataFromJson(json);

  @override
  List<CourseLessonData?>? list;

  @override
  String toString() {
    return 'CourseLessonsData(list: $list)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonsDataCopyWith<_$_CourseLessonsData> get copyWith =>
      __$$_CourseLessonsDataCopyWithImpl<_$_CourseLessonsData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonsDataToJson(
      this,
    );
  }
}

abstract class _CourseLessonsData implements CourseLessonsData {
  factory _CourseLessonsData({List<CourseLessonData?>? list}) =
      _$_CourseLessonsData;

  factory _CourseLessonsData.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonsData.fromJson;

  @override
  List<CourseLessonData?>? get list;
  set list(List<CourseLessonData?>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonsDataCopyWith<_$_CourseLessonsData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseLessonData _$CourseLessonDataFromJson(Map<String, dynamic> json) {
  return _CourseLessonData.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonData {
  int? get segmentId => throw _privateConstructorUsedError;
  set segmentId(int? value) => throw _privateConstructorUsedError;
  List<CourseLessonItem?>? get lessonInfos =>
      throw _privateConstructorUsedError;
  set lessonInfos(List<CourseLessonItem?>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonDataCopyWith<CourseLessonData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonDataCopyWith<$Res> {
  factory $CourseLessonDataCopyWith(
          CourseLessonData value, $Res Function(CourseLessonData) then) =
      _$CourseLessonDataCopyWithImpl<$Res, CourseLessonData>;
  @useResult
  $Res call({int? segmentId, List<CourseLessonItem?>? lessonInfos});
}

/// @nodoc
class _$CourseLessonDataCopyWithImpl<$Res, $Val extends CourseLessonData>
    implements $CourseLessonDataCopyWith<$Res> {
  _$CourseLessonDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? lessonInfos = freezed,
  }) {
    return _then(_value.copyWith(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfos: freezed == lessonInfos
          ? _value.lessonInfos
          : lessonInfos // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonItem?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonDataCopyWith<$Res>
    implements $CourseLessonDataCopyWith<$Res> {
  factory _$$_CourseLessonDataCopyWith(
          _$_CourseLessonData value, $Res Function(_$_CourseLessonData) then) =
      __$$_CourseLessonDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? segmentId, List<CourseLessonItem?>? lessonInfos});
}

/// @nodoc
class __$$_CourseLessonDataCopyWithImpl<$Res>
    extends _$CourseLessonDataCopyWithImpl<$Res, _$_CourseLessonData>
    implements _$$_CourseLessonDataCopyWith<$Res> {
  __$$_CourseLessonDataCopyWithImpl(
      _$_CourseLessonData _value, $Res Function(_$_CourseLessonData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentId = freezed,
    Object? lessonInfos = freezed,
  }) {
    return _then(_$_CourseLessonData(
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfos: freezed == lessonInfos
          ? _value.lessonInfos
          : lessonInfos // ignore: cast_nullable_to_non_nullable
              as List<CourseLessonItem?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonData implements _CourseLessonData {
  _$_CourseLessonData({this.segmentId, this.lessonInfos});

  factory _$_CourseLessonData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonDataFromJson(json);

  @override
  int? segmentId;
  @override
  List<CourseLessonItem?>? lessonInfos;

  @override
  String toString() {
    return 'CourseLessonData(segmentId: $segmentId, lessonInfos: $lessonInfos)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonDataCopyWith<_$_CourseLessonData> get copyWith =>
      __$$_CourseLessonDataCopyWithImpl<_$_CourseLessonData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonDataToJson(
      this,
    );
  }
}

abstract class _CourseLessonData implements CourseLessonData {
  factory _CourseLessonData(
      {int? segmentId,
      List<CourseLessonItem?>? lessonInfos}) = _$_CourseLessonData;

  factory _CourseLessonData.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonData.fromJson;

  @override
  int? get segmentId;
  set segmentId(int? value);
  @override
  List<CourseLessonItem?>? get lessonInfos;
  set lessonInfos(List<CourseLessonItem?>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonDataCopyWith<_$_CourseLessonData> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseLessonItem _$CourseLessonItemFromJson(Map<String, dynamic> json) {
  return _CourseLessonItem.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonItem {
  int? get lessonId => throw _privateConstructorUsedError;
  set lessonId(int? value) => throw _privateConstructorUsedError;
  int? get lessonOrder => throw _privateConstructorUsedError;
  set lessonOrder(int? value) => throw _privateConstructorUsedError;
  int? get studyStatus => throw _privateConstructorUsedError;
  set studyStatus(int? value) => throw _privateConstructorUsedError;
  int? get lessonGrade => throw _privateConstructorUsedError;
  set lessonGrade(int? value) => throw _privateConstructorUsedError;
  bool? get unlock => throw _privateConstructorUsedError;
  set unlock(bool? value) => throw _privateConstructorUsedError;
  bool? get finish => throw _privateConstructorUsedError;
  set finish(bool? value) => throw _privateConstructorUsedError;
  bool? get makeup => throw _privateConstructorUsedError;
  set makeup(bool? value) => throw _privateConstructorUsedError;
  bool? get makeupUIExpired => throw _privateConstructorUsedError;
  set makeupUIExpired(bool? value) => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  set today(bool? value) => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  set lessonName(String? value) => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get studyTipsVoice => throw _privateConstructorUsedError;
  set studyTipsVoice(String? value) => throw _privateConstructorUsedError;
  String? get lockVoice => throw _privateConstructorUsedError;
  set lockVoice(String? value) => throw _privateConstructorUsedError;
  String? get lessonGradeResourceUrl => throw _privateConstructorUsedError;
  set lessonGradeResourceUrl(String? value) =>
      throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonItemCopyWith<CourseLessonItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonItemCopyWith<$Res> {
  factory $CourseLessonItemCopyWith(
          CourseLessonItem value, $Res Function(CourseLessonItem) then) =
      _$CourseLessonItemCopyWithImpl<$Res, CourseLessonItem>;
  @useResult
  $Res call(
      {int? lessonId,
      int? lessonOrder,
      int? studyStatus,
      int? lessonGrade,
      bool? unlock,
      bool? finish,
      bool? makeup,
      bool? makeupUIExpired,
      bool? today,
      String? lessonName,
      String? icon,
      String? studyTipsVoice,
      String? lockVoice,
      String? lessonGradeResourceUrl,
      String? route});
}

/// @nodoc
class _$CourseLessonItemCopyWithImpl<$Res, $Val extends CourseLessonItem>
    implements $CourseLessonItemCopyWith<$Res> {
  _$CourseLessonItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonOrder = freezed,
    Object? studyStatus = freezed,
    Object? lessonGrade = freezed,
    Object? unlock = freezed,
    Object? finish = freezed,
    Object? makeup = freezed,
    Object? makeupUIExpired = freezed,
    Object? today = freezed,
    Object? lessonName = freezed,
    Object? icon = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockVoice = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      unlock: freezed == unlock
          ? _value.unlock
          : unlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      finish: freezed == finish
          ? _value.finish
          : finish // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeup: freezed == makeup
          ? _value.makeup
          : makeup // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeupUIExpired: freezed == makeupUIExpired
          ? _value.makeupUIExpired
          : makeupUIExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseLessonItemCopyWith<$Res>
    implements $CourseLessonItemCopyWith<$Res> {
  factory _$$_CourseLessonItemCopyWith(
          _$_CourseLessonItem value, $Res Function(_$_CourseLessonItem) then) =
      __$$_CourseLessonItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? lessonId,
      int? lessonOrder,
      int? studyStatus,
      int? lessonGrade,
      bool? unlock,
      bool? finish,
      bool? makeup,
      bool? makeupUIExpired,
      bool? today,
      String? lessonName,
      String? icon,
      String? studyTipsVoice,
      String? lockVoice,
      String? lessonGradeResourceUrl,
      String? route});
}

/// @nodoc
class __$$_CourseLessonItemCopyWithImpl<$Res>
    extends _$CourseLessonItemCopyWithImpl<$Res, _$_CourseLessonItem>
    implements _$$_CourseLessonItemCopyWith<$Res> {
  __$$_CourseLessonItemCopyWithImpl(
      _$_CourseLessonItem _value, $Res Function(_$_CourseLessonItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonOrder = freezed,
    Object? studyStatus = freezed,
    Object? lessonGrade = freezed,
    Object? unlock = freezed,
    Object? finish = freezed,
    Object? makeup = freezed,
    Object? makeupUIExpired = freezed,
    Object? today = freezed,
    Object? lessonName = freezed,
    Object? icon = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockVoice = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_CourseLessonItem(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      unlock: freezed == unlock
          ? _value.unlock
          : unlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      finish: freezed == finish
          ? _value.finish
          : finish // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeup: freezed == makeup
          ? _value.makeup
          : makeup // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeupUIExpired: freezed == makeupUIExpired
          ? _value.makeupUIExpired
          : makeupUIExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonItem implements _CourseLessonItem {
  _$_CourseLessonItem(
      {this.lessonId,
      this.lessonOrder,
      this.studyStatus,
      this.lessonGrade,
      this.unlock,
      this.finish,
      this.makeup,
      this.makeupUIExpired,
      this.today,
      this.lessonName,
      this.icon,
      this.studyTipsVoice,
      this.lockVoice,
      this.lessonGradeResourceUrl,
      this.route});

  factory _$_CourseLessonItem.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonItemFromJson(json);

  @override
  int? lessonId;
  @override
  int? lessonOrder;
  @override
  int? studyStatus;
  @override
  int? lessonGrade;
  @override
  bool? unlock;
  @override
  bool? finish;
  @override
  bool? makeup;
  @override
  bool? makeupUIExpired;
  @override
  bool? today;
  @override
  String? lessonName;
  @override
  String? icon;
  @override
  String? studyTipsVoice;
  @override
  String? lockVoice;
  @override
  String? lessonGradeResourceUrl;
  @override
  String? route;

  @override
  String toString() {
    return 'CourseLessonItem(lessonId: $lessonId, lessonOrder: $lessonOrder, studyStatus: $studyStatus, lessonGrade: $lessonGrade, unlock: $unlock, finish: $finish, makeup: $makeup, makeupUIExpired: $makeupUIExpired, today: $today, lessonName: $lessonName, icon: $icon, studyTipsVoice: $studyTipsVoice, lockVoice: $lockVoice, lessonGradeResourceUrl: $lessonGradeResourceUrl, route: $route)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonItemCopyWith<_$_CourseLessonItem> get copyWith =>
      __$$_CourseLessonItemCopyWithImpl<_$_CourseLessonItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonItemToJson(
      this,
    );
  }
}

abstract class _CourseLessonItem implements CourseLessonItem {
  factory _CourseLessonItem(
      {int? lessonId,
      int? lessonOrder,
      int? studyStatus,
      int? lessonGrade,
      bool? unlock,
      bool? finish,
      bool? makeup,
      bool? makeupUIExpired,
      bool? today,
      String? lessonName,
      String? icon,
      String? studyTipsVoice,
      String? lockVoice,
      String? lessonGradeResourceUrl,
      String? route}) = _$_CourseLessonItem;

  factory _CourseLessonItem.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonItem.fromJson;

  @override
  int? get lessonId;
  set lessonId(int? value);
  @override
  int? get lessonOrder;
  set lessonOrder(int? value);
  @override
  int? get studyStatus;
  set studyStatus(int? value);
  @override
  int? get lessonGrade;
  set lessonGrade(int? value);
  @override
  bool? get unlock;
  set unlock(bool? value);
  @override
  bool? get finish;
  set finish(bool? value);
  @override
  bool? get makeup;
  set makeup(bool? value);
  @override
  bool? get makeupUIExpired;
  set makeupUIExpired(bool? value);
  @override
  bool? get today;
  set today(bool? value);
  @override
  String? get lessonName;
  set lessonName(String? value);
  @override
  String? get icon;
  set icon(String? value);
  @override
  String? get studyTipsVoice;
  set studyTipsVoice(String? value);
  @override
  String? get lockVoice;
  set lockVoice(String? value);
  @override
  String? get lessonGradeResourceUrl;
  set lessonGradeResourceUrl(String? value);
  @override
  String? get route;
  set route(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonItemCopyWith<_$_CourseLessonItem> get copyWith =>
      throw _privateConstructorUsedError;
}
