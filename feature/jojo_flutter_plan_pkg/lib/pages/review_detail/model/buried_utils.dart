import 'dart:convert';

import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

class ReviewDetailBuriedUtils {
  // 补学助手详情页浏览
  static void appViewScreenWidget(String? dataString) {
    if (dataString == null) return;
    try {
      Map<String, dynamic> buriedMap = jsonDecode(dataString);
      Map<String, dynamic> properties = {
        '\$screen_name': "补学助手详情页",
        ...buriedMap,
      };
      RunEnv.sensorsTrack('\$AppViewScreen', properties);
    } catch (e) {
      l.e("补学助手详情页", "埋点参数解析失败: $e");
    }
  }

  // 补学助手搜索页浏览
  static void appViewScreenWidgetSearch(String? dataString) {
    if (dataString == null) return;
    try {
      Map<String, dynamic> buriedMap = jsonDecode(dataString);
      Map<String, dynamic> properties = {
        '\$screen_name': "搜索课时详情页",
        ...buriedMap,
      };
      RunEnv.sensorsTrack('\$AppViewScreen', properties);
    } catch (e) {
      l.e("搜索课时详情页", "埋点参数解析失败: $e");
    }
  }

  // 点击埋点
  static void appClickWidgetSearch(String elementName, String? dataString) {
    if (dataString == null) return;
    try {
      Map<String, dynamic> buriedMap = jsonDecode(dataString);
      Map<String, dynamic> properties = {
        '\$screen_name': "搜索课时详情页",
        '\$element_name': elementName,
        ...buriedMap,
      };
      RunEnv.sensorsTrack('\$AppClick', properties);
    } catch (e) {
      l.e("搜索课时详情页", "埋点参数解析失败: $e");
    }
  }

  // 点击埋点
  static void appClickWidget(String elementName, String? dataString) {
    if (dataString == null) return;
    try {
      Map<String, dynamic> buriedMap = jsonDecode(dataString);
      Map<String, dynamic> properties = {
        '\$screen_name': "补学助手详情页",
        '\$element_name': elementName,
        ...buriedMap,
      };
      RunEnv.sensorsTrack('\$AppClick', properties);
    } catch (e) {
      l.e("补学助手详情页", "埋点参数解析失败: $e");
    }
  }

  // 浏览埋点
  static void appViewWidget(String elementName, String? dataString) {
    if (dataString == null) return;
    try {
      Map<String, dynamic> buriedMap = jsonDecode(dataString);
      Map<String, dynamic> properties = {
        '\$screen_name': "补学助手详情页",
        'c_element_name': elementName,
        ...buriedMap,
      };
      RunEnv.sensorsTrack(
        'ElementView',
        properties,
      );
    } catch (e) {
      l.e("补学助手详情页", "埋点参数解析失败: $e");
    }
  }
}
