// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_assistant_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseSegmentsData _$$_CourseSegmentsDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseSegmentsData(
      segments: (json['segments'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseSegmentData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseSegmentsDataToJson(
        _$_CourseSegmentsData instance) =>
    <String, dynamic>{
      'segments': instance.segments,
    };

_$_CourseSegmentData _$$_CourseSegmentDataFromJson(Map<String, dynamic> json) =>
    _$_CourseSegmentData(
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      courseChildType: json['courseChildType'] as int?,
      unFinishLessonNum: json['unFinishLessonNum'] as int?,
      lock: json['lock'] as bool?,
      segmentName: json['segmentName'] as String?,
      weekList: (json['weekList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseSegmentWeekData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseSegmentDataToJson(
        _$_CourseSegmentData instance) =>
    <String, dynamic>{
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'courseChildType': instance.courseChildType,
      'unFinishLessonNum': instance.unFinishLessonNum,
      'lock': instance.lock,
      'segmentName': instance.segmentName,
      'weekList': instance.weekList,
    };

_$_CourseSegmentWeekData _$$_CourseSegmentWeekDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseSegmentWeekData(
      weekId: json['weekId'] as int?,
      unFinishLessonNum: json['unFinishLessonNum'] as int?,
      weekName: json['weekName'] as String?,
      lock: json['lock'] as bool?,
    );

Map<String, dynamic> _$$_CourseSegmentWeekDataToJson(
        _$_CourseSegmentWeekData instance) =>
    <String, dynamic>{
      'weekId': instance.weekId,
      'unFinishLessonNum': instance.unFinishLessonNum,
      'weekName': instance.weekName,
      'lock': instance.lock,
    };

_$_CourseLessonsSearchData _$$_CourseLessonsSearchDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseLessonsSearchData(
      pageNum: json['pageNum'] as int?,
      pageSize: json['pageSize'] as int?,
      total: json['total'] as int?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseLessonItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseLessonsSearchDataToJson(
        _$_CourseLessonsSearchData instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
      'data': instance.data,
    };

_$_CourseLessonsData _$$_CourseLessonsDataFromJson(Map<String, dynamic> json) =>
    _$_CourseLessonsData(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseLessonData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseLessonsDataToJson(
        _$_CourseLessonsData instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

_$_CourseLessonData _$$_CourseLessonDataFromJson(Map<String, dynamic> json) =>
    _$_CourseLessonData(
      segmentId: json['segmentId'] as int?,
      lessonInfos: (json['lessonInfos'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseLessonItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseLessonDataToJson(_$_CourseLessonData instance) =>
    <String, dynamic>{
      'segmentId': instance.segmentId,
      'lessonInfos': instance.lessonInfos,
    };

_$_CourseLessonItem _$$_CourseLessonItemFromJson(Map<String, dynamic> json) =>
    _$_CourseLessonItem(
      lessonId: json['lessonId'] as int?,
      lessonOrder: json['lessonOrder'] as int?,
      studyStatus: json['studyStatus'] as int?,
      lessonGrade: json['lessonGrade'] as int?,
      unlock: json['unlock'] as bool?,
      finish: json['finish'] as bool?,
      makeup: json['makeup'] as bool?,
      makeupUIExpired: json['makeupUIExpired'] as bool?,
      today: json['today'] as bool?,
      lessonName: json['lessonName'] as String?,
      icon: json['icon'] as String?,
      studyTipsVoice: json['studyTipsVoice'] as String?,
      lockVoice: json['lockVoice'] as String?,
      lessonGradeResourceUrl: json['lessonGradeResourceUrl'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_CourseLessonItemToJson(_$_CourseLessonItem instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'lessonOrder': instance.lessonOrder,
      'studyStatus': instance.studyStatus,
      'lessonGrade': instance.lessonGrade,
      'unlock': instance.unlock,
      'finish': instance.finish,
      'makeup': instance.makeup,
      'makeupUIExpired': instance.makeupUIExpired,
      'today': instance.today,
      'lessonName': instance.lessonName,
      'icon': instance.icon,
      'studyTipsVoice': instance.studyTipsVoice,
      'lockVoice': instance.lockVoice,
      'lessonGradeResourceUrl': instance.lessonGradeResourceUrl,
      'route': instance.route,
    };
