import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
part 'review_assistant_data.freezed.dart';
part 'review_assistant_data.g.dart';

@freezed
class CourseSegmentsData with _$CourseSegmentsData {
  const factory CourseSegmentsData({
    List<CourseSegmentData?>? segments,
  }) = _CourseSegmentsData;

  factory CourseSegmentsData.fromJson(Map<String, dynamic> json) =>
      _$CourseSegmentsDataFromJson(json);
}

@unfreezed
class CourseSegmentData
    with _$CourseSegmentData{
  factory CourseSegmentData({
    int? segmentId,
    int? weekId,
    int? courseChildType,
    int? unFinishLessonNum,
    bool? lock,
    String? segmentName,
    List<CourseSegmentWeekData?>? weekList,
  }) = _CourseSegmentData;

  factory CourseSegmentData.fromJson(
          Map<String, dynamic> json) =>
      _$CourseSegmentDataFromJson(json);
}

@unfreezed
class CourseSegmentWeekData
    with _$CourseSegmentWeekData {
  factory CourseSegmentWeekData({
    int? weekId,
    int? unFinishLessonNum,
    String? weekName,
    bool? lock,
  }) = _CourseSegmentWeekData;

  factory CourseSegmentWeekData.fromJson(
          Map<String, dynamic> json) =>
      _$CourseSegmentWeekDataFromJson(json);
}

@unfreezed
class CourseLessonsSearchData with _$CourseLessonsSearchData {
  factory CourseLessonsSearchData({
    int? pageNum,
    int? pageSize,
    int? total,
    List<CourseLessonItem?>? data,
  }) = _CourseLessonsSearchData;

  factory CourseLessonsSearchData.fromJson(Map<String, dynamic> json) =>
      _$CourseLessonsSearchDataFromJson(json);
}

@unfreezed
class CourseLessonsData with _$CourseLessonsData {
  factory CourseLessonsData({
    List<CourseLessonData?>? list,
  }) = _CourseLessonsData;

  factory CourseLessonsData.fromJson(Map<String, dynamic> json) =>
      _$CourseLessonsDataFromJson(json);
}

@unfreezed
class CourseLessonData with _$CourseLessonData {
  factory CourseLessonData({
    int? segmentId,
    List<CourseLessonItem?>? lessonInfos,
  }) = _CourseLessonData;

  factory CourseLessonData.fromJson(Map<String, dynamic> json) =>
      _$CourseLessonDataFromJson(json);
}

@unfreezed
class CourseLessonItem with _$CourseLessonItem {
  factory CourseLessonItem({
    int? lessonId,
    int? lessonOrder,
    int? studyStatus,
    int? lessonGrade,
    bool? unlock,
    bool? finish,
    bool? makeup,
    bool? makeupUIExpired,
    bool? today,
    String? lessonName,
    String? icon,
    String? studyTipsVoice,
    String? lockVoice,
    String? lessonGradeResourceUrl,
    String? route,
  }) = _CourseLessonItem;

  factory CourseLessonItem.fromJson(Map<String, dynamic> json) =>
      _$CourseLessonItemFromJson(json);
}

class ReviewPagePopSwitchContentSize {
  static double cancelBtnHeight = 60.rdp; // 取消按钮高度
  static double titleHeight = 52.rdp; // title高度
  static double cellHeight = 56.rdp; // 单元格高度
  static double lrSpace = 20.rdp; // 左右间距
  static double maxHeightRatio = 0.88; // 弹窗宽高比
}

class ReviewcourseChildType {
  static int segment = 0;
  static int week = 1;
}

enum RequestStatus { success, failed, loading }
enum SearchStatus { success, failed, emptyResult }

class ReviewLessonDataModel {
  bool needRefresh = true;
  RequestStatus requestType = RequestStatus.loading;  // 默认请求中
  int? segmentId;
  int? weekId;
  bool lock = false;
  List<CourseCard?>? reviewList;  // 待补课程
  List<CourseCard?>? list;  // 全部课程
}

// 补学详情入口数据
class ReviewEnterDataModel {
  int? segmentId;
  int? weekId;
  int? unFinishedNum;
  int? classId;
  int? loadingScene;
  String? reviewRoute;
  String? classKey;
}