import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/top_title_widget.dart';

class ReviewSwitchPopWidget extends StatefulWidget {

  final ReviewDetailCtrl ctrl;

  const ReviewSwitchPopWidget({
    super.key,
    required this.ctrl,
  
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewSwitchPopWidgetState();
  }
}

class _ReviewSwitchPopWidgetState extends State<ReviewSwitchPopWidget> {

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ReviewDetailBuriedUtils.appViewWidget("切换主题半弹窗", widget.ctrl.buriedString);
      // 滚动到当前选中项（滚动到屏幕中间位置）
      if (!mounted) return;
      if (_scrollController.hasClients) {
        double height = MediaQuery.of(context).size.height;
        double bottom = MediaQuery.of(context).viewPadding.bottom;
        double maxHeight = height * ReviewPagePopSwitchContentSize.maxHeightRatio;
        double itemOffset = widget.ctrl.currentIndex * ReviewPagePopSwitchContentSize.cellHeight;
        double listViewHeight = maxHeight - ReviewPagePopSwitchContentSize.titleHeight - ReviewPagePopSwitchContentSize.cancelBtnHeight - bottom;
        double centerOffset = itemOffset - (listViewHeight / 2) + (ReviewPagePopSwitchContentSize.cellHeight / 2);
        double targetOffset = centerOffset.clamp(0.0, _scrollController.position.maxScrollExtent);
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double bottom = MediaQuery.of(context).viewPadding.bottom;
    double safeBottom = RunEnv.isAndroid ? 0.rdp : (bottom > 0 ? 20.rdp : 0.rdp);
    double maxHeight = height * 0.88;
    double minHeight = ReviewPagePopSwitchContentSize.titleHeight + 2 * ReviewPagePopSwitchContentSize.cellHeight + ReviewPagePopSwitchContentSize.cancelBtnHeight + bottom;
    double contentHeight = ReviewPagePopSwitchContentSize.titleHeight + ReviewPagePopSwitchContentSize.cancelBtnHeight + bottom;
    contentHeight += (widget.ctrl.state.segmentList ?? []).length * ReviewPagePopSwitchContentSize.cellHeight;
    contentHeight = max(minHeight, contentHeight);
    contentHeight = min(maxHeight, contentHeight);

    return AdaptiveOrientationLayout(portrait: (BuildContext context) {
        return _buildContentPOPWidget(contentHeight, safeBottom);
    }, landscape: (BuildContext context) {
        return _buildContentPOPWidget(contentHeight, 0);
    });
  }

  Widget _buildContentPOPWidget(double contentHeight , double bottom) {
    return Container(
      height: contentHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(ReviewPagePopSwitchContentSize.lrSpace),
          topRight: Radius.circular(ReviewPagePopSwitchContentSize.lrSpace),
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 0.rdp,
            left: 0.rdp,
            right: 0.rdp,
            bottom: ReviewPagePopSwitchContentSize.cancelBtnHeight + bottom,
            child: Column(
        children: [
          _buildTitleWidget(),
          _buildLineWidget(),
          Expanded(child: _buildContentWidget())
        ],
      )
          ),
          Positioned(
            left: 0.rdp,
            right: 0.rdp,
            bottom: 0.rdp,
            child: _buildBottomWidget(bottom),
          )
        ],
      ),
    );
  }

  Widget _buildTitleWidget() {
    return Container(
      height: ReviewPagePopSwitchContentSize.titleHeight,
      alignment: Alignment.center,
      child: Text(
                            S.of(context).pleaseChooseThemeMonth,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: context.appColors.jColorGray5,
                              fontSize: 16.rdp,
                              fontWeight: FontWeight.w400),
                            ),
    );
  }

  Widget _buildLineWidget() {
    return Container(
            height: 1.rdp,
            decoration: BoxDecoration(
              color: HexColor("#F4F4F4"),
            ));
  }


  Widget _buildContentWidget() {
    return Container(
      color: Colors.white,
      child: ListView.builder(
        itemCount: (widget.ctrl.state.segmentList ?? []).length,
        controller: _scrollController,
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (context, index) {
          CourseSegmentData? data = widget.ctrl.state.segmentList?[index];
          if(data == null) return Container();
          bool isSelect = widget.ctrl.currentSegment == data;
          return GestureDetector(
            onTap: () {
              if (!mounted) return;
              try {
                if (widget.ctrl.currentSegment != data) {
                  ReviewDetailBuriedUtils.appClickWidget("点击切换主题", widget.ctrl.buriedString);
                  widget.ctrl.currentSegment = data; 
                  widget.ctrl.currentSegmentId = data.segmentId ?? 0;
                  widget.ctrl.currentWeekId = data.weekId ?? 0;
                  widget.ctrl.currentIndex = index;
                  widget.ctrl.pageController?.jumpToPage(index);
                  widget.ctrl.refresh();
                }
              } catch (e) {
                l.e("补学助手详情页", "点击切换主题异常");
              }
              SmartDialog.dismiss();
            },
            child: ReviewTopTitleInfoWidget(subjectColor: widget.ctrl.subjectColor,selected: isSelect, lock: data.lock, titleTxt: data.segmentName, unFinishedNum: data.unFinishLessonNum, showArrow: true, cellHeight: ReviewPagePopSwitchContentSize.cellHeight,),
          );
        }),
    );
  }

  Widget _buildBottomWidget(double bottom) {
    return Container(
      height: ReviewPagePopSwitchContentSize.cancelBtnHeight + bottom,
      color: Colors.white,
      child: Stack(
        children: [
          Positioned(
            top: 0.rdp,
            left: 0.rdp,
            right: 0.rdp,
            height: ReviewPagePopSwitchContentSize.cancelBtnHeight,
            child: 
            GestureDetector(
              onTap: () {
                SmartDialog.dismiss();
              },
              child: Container(
              alignment: Alignment.center,
              color: Colors.white,
              child: Text(
                            S.of(context).cancel,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: context.appColors.jColorGray5,
                              fontSize: 16.rdp,
                              fontWeight: FontWeight.w400),
                            ),
            ),
            )
          )
        ],
      ),
    );
  }
}
