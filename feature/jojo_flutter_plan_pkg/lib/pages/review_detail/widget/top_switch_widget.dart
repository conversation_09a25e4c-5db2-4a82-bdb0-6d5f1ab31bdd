
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/switch_pop_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/truncated_text_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewTopSwitchWidget extends StatefulWidget {

  final String? subjectColor;

  const ReviewTopSwitchWidget({
    super.key,
    required this.subjectColor
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewTopSwitchWidgetState();
  }
}

class _ReviewTopSwitchWidgetState extends State<ReviewTopSwitchWidget> {

  Color? _mainColor;

  @override
  Widget build(BuildContext context) {
    _mainColor ??= context.appColors.colorVariant4(HexColor(widget.subjectColor ?? "#FF9045"));
    return Container(
      color: Colors.transparent,
      alignment: Alignment.centerLeft,
      height: 70.rdp,
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
      child: Row(
        children: [
          Expanded(child: _buildThemeMonthSwitchWidget()),
          SizedBox(width: 10.rdp,),
          _buildReviewSwitchWidget()
        ],
      ),
    );
  }

  Widget _buildThemeMonthSwitchWidget() {
    ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
    return GestureDetector(
      onTap: () {
        if (!mounted) return;
        try {
          ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
          if (_ctrl.isRequestPageInfo) {
            return;
          }
          ReviewDetailBuriedUtils.appClickWidget("主题切换入口_点击", _ctrl.buriedString);
          SmartDialog.show(
            animationTime: const Duration(milliseconds: 300),
            clickMaskDismiss: true,
            onDismiss: () {},
            alignment: Alignment.bottomCenter,
            maskColor: HexColor('#000000', 0.4),
            builder: (_) => ReviewSwitchPopWidget(ctrl: _ctrl));
        } catch(e) {
          l.e("补学助手详情页", "主题切换入口点击异常");
        }
      },
      child: Container(
        height: 43.rdp,
        padding: EdgeInsets.only(left: 14.rdp, right: 15.rdp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.rdp),
          border: Border.all(color: context.appColors.colorVariant2(_mainColor ?? HexColor("#DBF3FF")), width: 1.rdp),
        ),
        child: Row(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  String segmentName = _ctrl.currentSegment?.segmentName ?? "";
                  return buildTruncatedText(segmentName, constraints.maxWidth, context.appColors.jColorGray6, 18.rdp);
                },
              ),
            ),
                            SizedBox(width: 4.rdp,),
                            Image.asset(
            AssetsImg.REVIEW_PAGE_SWITCH_ICON,
            color: context.appColors.jColorGray5,
            width: 12.rdp,
            height: 12.rdp,
            fit: BoxFit.cover,
            package: RunEnv.package,
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewSwitchWidget() {
    ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
    double itemWidth = 130.rdp;
    double itemHeight = 40.rdp;
    return Container(
      width: itemWidth,
      height: itemHeight,
      decoration: BoxDecoration(
        color: context.appColors.colorVariant2(_mainColor ?? HexColor("#DBF3FF")),
        borderRadius: BorderRadius.circular(itemHeight/2.0),
      ),
      child: Stack(
        children: [
          Positioned(
            left: _ctrl.selectAll ? null : 4.rdp,
            right: _ctrl.selectAll ? 4.rdp : null,
            top: 4.rdp,
            bottom: 4.rdp,
            child: Container(
              width: 59.rdp,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular((itemHeight - 8.rdp)/2.0),
              )
            ),
          ),
          Positioned.fill(
            child: SizedBox(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      if (!mounted) return;
                      try {
                        ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
                        ReviewDetailBuriedUtils.appClickWidget("待补_点击", _ctrl.buriedString);
                        _ctrl.selectAll = false;
                        _ctrl.refresh();
                      } catch (e) {
                        l.e("补学助手详情页", "待补按钮点击异常");
                      }
                    },
                    child: Container(
                    alignment: Alignment.center,
                    width: itemWidth/2.0,
                    color: Colors.transparent,
                    child: Text(
                            S.of(context).pendingMakeupCourses_abbr,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: context.appColors.colorVariant6(_mainColor ?? HexColor("#004161")).withOpacity(_ctrl.selectAll == false ? 1 : 0.7),
                              fontSize: 16.rdp,
                              fontWeight: _ctrl.selectAll == false ? FontWeight.w600 : FontWeight.normal),
                            ),
                  ),
                  ),
                  GestureDetector(
                    onTap: () {
                      if (!mounted) return;
                      try {
                        ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
                        ReviewDetailBuriedUtils.appClickWidget("全部_点击", _ctrl.buriedString);
                        _ctrl.selectAll = true;
                        _ctrl.courseCardAnimation = false;
                        _ctrl.refresh();
                      } catch (e) {
                        l.e("补学助手详情页", "全部按钮点击异常");
                      }
                    },
                    child: Container(
                    alignment: Alignment.center,
                    color: Colors.transparent,
                    width: itemWidth/2.0,
                    child: Text(
                            S.of(context).all,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: context.appColors.colorVariant6(_mainColor ?? HexColor("#004161")).withOpacity(_ctrl.selectAll == true ? 1 : 0.7),
                              fontSize: 16.rdp,
                              fontWeight: _ctrl.selectAll == true ? FontWeight.w600 : FontWeight.normal),
                            ),
                  ),
                  ),
                ],
              ),
            )
          )
        ],
      ),
    );
  }
}
