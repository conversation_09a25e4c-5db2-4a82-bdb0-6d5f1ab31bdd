import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewTopTitleInfoWidget extends StatefulWidget {

  final int? unFinishedNum;
  final String? titleTxt;
  final bool? showArrow;
  final bool? lock;
  final bool? selected;
  final String? subjectColor;
  final double? cellHeight;

  const ReviewTopTitleInfoWidget({
    super.key,
    this.unFinishedNum,
    required this.lock,
    required this.selected,
    required this.showArrow,
    required this.titleTxt,
    required this.cellHeight,
    required this.subjectColor,
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewTopTitleInfoWidgetState();
  }
}

class _ReviewTopTitleInfoWidgetState extends State<ReviewTopTitleInfoWidget> {
  @override
  Widget build(BuildContext context) {
    Color selectTxtColor = context.appColors.colorVariant5(HexColor(widget.subjectColor ?? "#FF9045"));
    return Container(
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
        height: widget.cellHeight ?? 46.rdp,
                      child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.titleTxt ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: widget.selected == true ? selectTxtColor : context.appColors.jColorGray6,
                              fontSize: 16.rdp,
                              fontWeight: FontWeight.w600),
                            ),
                       ),
                       _buildHeaderReviewWidget(selectTxtColor),
                    ],
                  ),
                 );
  }

  Widget _buildHeaderReviewWidget(Color selectTxtColor) {
    if (widget.unFinishedNum != null) {
      int unFinishedNum = widget.unFinishedNum ?? 0;
      String unFinishedNumTxt = unFinishedNum > 0 ? "${S.of(context).pendingMakeupCourses_abbr} ${widget.unFinishedNum}" : "";
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              widget.lock == true ? S.of(context).lock : unFinishedNumTxt,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.right,
              style: TextStyle(
                          color: widget.selected == true ? selectTxtColor : context.appColors.jColorGray5,
                          fontSize: 14.rdp,
                          fontWeight: FontWeight.w400),
            ),
          ),
          if (widget.showArrow == true) SizedBox(width: 4.rdp), // 图标和文字之间的间距
          if (widget.showArrow == true) Image.asset(
            AssetsImg.PLAN_IMAGE_PROMOTE_TOP_CARD_ARROW,
            color: widget.selected == true ? selectTxtColor : context.appColors.jColorGray5,
            width: 16.rdp,
            height: 16.rdp,
            fit: BoxFit.cover,
            package: RunEnv.package,
          ),
        ],
      );
    }
    return Container();
  }
}
