import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewEmptyWidget extends StatefulWidget {

  final String? title;
  final double height;

  const ReviewEmptyWidget({
    super.key,
    required this.title,
    required this.height,
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewEmptyWidgetState();
  }
}

class _ReviewEmptyWidgetState extends State<ReviewEmptyWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: SizedBox(
        width: double.infinity,
        height: widget.height,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageAssetWeb(
              assetName:  AssetsImg.AFTER_LESSON_EMPTY,
              width: 200.rdp,
              height: 200.rdp,
              package: Config.package,
            ),
            Text(
              widget.title ?? "",
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(
                fontSize: 16.rdp,
                fontWeight: FontWeight.w400,
                color: context.appColors.jColorGray4,
              ),
            )
          ],
        ),
      ),
    );
  }
}
