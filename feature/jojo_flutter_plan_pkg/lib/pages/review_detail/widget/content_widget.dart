
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/after_layout.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/widget/course_card_info_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/content_empty.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/review_refresh_footer.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/review_refresh_header.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewContentWidget extends StatefulWidget {

  final ReviewDetailCtrl ctrl;

  const ReviewContentWidget({
    super.key,
    required this.ctrl,
  });

  @override
  State<StatefulWidget> createState() {
    return _ReviewContentWidgetState();
  }
}

class _ReviewContentWidgetState extends State<ReviewContentWidget> {

  final ScrollController _scrollController = ScrollController();
  double _pageViewHeight = 240.rdp;

  Widget _buildContentView(Widget child) {
    EasyRefreshController _refreshController = EasyRefreshController();
    int currentIndex = widget.ctrl.currentIndex;
    List segmentList = widget.ctrl.state.segmentList ?? [];
    return EasyRefresh(
      controller: _refreshController,
      header: currentIndex == 0 ? JOJOReviewRefreshEmptyHeader() : JOJOReviewRefreshHeader(),
      footer: currentIndex == segmentList.length - 1 ? JOJOReviewRefreshEmptyFooter() : JOJOReviewRefreshFooter(),
      onRefresh: () async {
        if (!mounted) return;
        try {
          ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
          _ctrl.changePageToPre();
        } catch (e) {
          l.e("补学助手详情页", "下滑切换主题异常");
        }
      },
      onLoad: () async {
        if (!mounted) return;
        try {
          ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
          _ctrl.changePageToNext();
        } catch (e) {
          l.e("补学助手详情页", "上滑切换主题异常");
        }
      },
      child: child,
    );
  }

  Widget _buildListView(List<CourseCard?>? list) {
    int length = list?.length ?? 0;
    double contentHeight = length * 70.rdp;
    bool needTCCell = contentHeight < _pageViewHeight;
    return _buildContentView(ListView.builder(
      itemCount: needTCCell ? length + 1 : length,
      controller: _scrollController,
      padding: EdgeInsets.zero,
      physics: const ClampingScrollPhysics(),
      itemBuilder: (context, index) {
        if(list == null) return const SizedBox();
        if (needTCCell && index == length) {
          return Container(
            color: Colors.white,
            height: _pageViewHeight - contentHeight,
          );
        }
        CourseCard? cardInfo = list[index];
        return Container(
          key: Key("${cardInfo?.classId}-${cardInfo?.lessonId}-${cardInfo?.lessonOrder}"),
          child: CourseCardInfoWidget(
            coursePreCard: null,
            courseCardInfo: cardInfo,
            courseNextCard: null,
            spineResourceInfo: null,
            playPositionAnimator: false,
            playCallback: () {},
            isHomeList: false,
            ctrl: null,
            onShow: (CourseCard? courseCardInfo) {},
            onClick: (CourseCard? courseCardInfo, bool isFocus) {
              ReviewDetailBuriedUtils.appClickWidget("课时_点击", widget.ctrl.buriedString);
            },
          ),
        );
      },
    ));
  }

  Widget _buildLoadingWidget() {
    return SizedBox(
      child: Align(
        alignment: Alignment.center, // 保持居中
        child: ImageAssetWeb(
          assetName: AssetsImg.SXZ,
          width: 130.rdp,
          height: 100.rdp,
          package: Config.package,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(double height) {
    return SizedBox(
      height: height,
      child: JoJoPageLoading(
      status: PageStatus.error,
      retry: () {
        try {
          if (!mounted) return;
          ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
          CourseSegmentData? data = _ctrl.state.segmentList?[_ctrl.currentIndex];
          if (data == null) return;
          String key = data.weekId == 0 ? "${data.segmentId}" : "${data.segmentId}_${data.weekId}";
          ReviewLessonDataModel? dataModel = _ctrl.state.lessonsMap?[key];
          if (dataModel != null) {
            dataModel.requestType = RequestStatus.loading;
            dataModel.needRefresh = true;
            _ctrl.state.lessonsMap?[key] = dataModel;
            _ctrl.refresh();
          }
          _ctrl.getLessonsInfo(_ctrl.currentSegment?.segmentId ?? 0, _ctrl.currentSegment?.weekId ?? 0);
        } catch (e) {
          l.e("补学助手详情页", "请求重试按钮点击异常");
        }
      },
      placeText: S.of(context).reviewPageRequestError,
      child: Container(),
    ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
    return Expanded(child: AfterLayout(
      callback: (RenderAfterLayout value) {
        _pageViewHeight = value.size.height;
      },
      child: PageView.builder(
        controller: _ctrl.pageController,
        scrollDirection: Axis.vertical,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: (_ctrl.state.segmentList ?? []).length,
        onPageChanged: (value) {
          if (!mounted || _ctrl.state.segmentList == null) return;
          try {
            CourseSegmentData data = _ctrl.state.segmentList![value];
            _ctrl = context.read<ReviewDetailCtrl>();
            _ctrl.currentIndex = value;
            _ctrl.currentSegment = data;
            _ctrl.currentSegmentId = data.segmentId ?? 0;
            _ctrl.currentWeekId = data.weekId ?? 0;
            _ctrl.refresh();
            _ctrl.getLessonsInfo(data.segmentId ?? 0, data.weekId ?? 0);
          } catch (e) {
            l.e("补学助手详情页", "主题Page切换异常");
          }
        },
        itemBuilder: (context, index) {
          CourseSegmentData? data = _ctrl.state.segmentList?[index];
          if (data == null) return Container();
          String key = (data.weekId ?? 0) == 0 ? "${data.segmentId}" : "${data.segmentId}_${data.weekId}";
          ReviewLessonDataModel? dataModel = _ctrl.state.lessonsMap?[key];
          if (dataModel == null) {
            // 显示加载中
            return _buildLoadingWidget();
          } else {
            if (dataModel.requestType == RequestStatus.failed) {
              return _buildContentView(_buildErrorWidget(_pageViewHeight));
            } else if (dataModel.requestType == RequestStatus.loading) {
              return _buildLoadingWidget();
            } else {
              if (dataModel.lock && !_ctrl.selectAll) {
                return _buildContentView(ReviewEmptyWidget(title: S.of(context).currentStageUnlock, height: _pageViewHeight,));
              }
              if (_ctrl.selectAll) {
                List<CourseCard?> list = dataModel.list ?? [];
                if (list.isEmpty) {
                  // 显示全部课程为空的页面
                  return _buildContentView(ReviewEmptyWidget(title: S.of(context).emptyAllLessonData, height: _pageViewHeight,),);
                } else {
                  // 显示全部课程
                  return _buildListView(list);
                }
              } else {
                List<CourseCard?> list = dataModel.reviewList ?? [];
                if (list.isEmpty) {
                  // 显示补学课程为空的页面
                  return _buildContentView(ReviewEmptyWidget(title: S.of(context).emptyReviewLessonData, height: _pageViewHeight,));
                } else {
                  // 显示补学课程
                  return _buildListView(list);
                }
              }
            }
          }
        }
      ),
    ),);
  }
}
