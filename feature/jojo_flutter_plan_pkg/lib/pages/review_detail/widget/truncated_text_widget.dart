

import 'package:flutter/material.dart';

Widget buildTruncatedText(String text, double maxWidth, Color color, double fontSize) {
  if (text.isEmpty) return const SizedBox.shrink();

  final TextStyle textStyle = TextStyle(
    color: color,
    fontSize: fontSize,
    fontWeight: FontWeight.w600,
  );

  // 测量完整文本宽度
  final TextPainter fullTextPainter = TextPainter(
    text: TextSpan(text: text, style: textStyle),
    maxLines: 1,
    textDirection: TextDirection.ltr,
  );
  fullTextPainter.layout();

  // 如果没有超出，直接返回
  if (fullTextPainter.width <= maxWidth) {
    return Text(text, style: textStyle);
  }

  // 超出了，逐字符测试找到最大可显示长度
  const String ellipsis = '...';
  final double ellipsisWidth = _getTextWidth(ellipsis, textStyle);
  final double availableWidth = maxWidth - ellipsisWidth;

  String displayText = '';
  for (int i = 1; i <= text.length; i++) {
    String testText = text.substring(0, i);
    if (_getTextWidth(testText, textStyle) <= availableWidth) {
      displayText = testText;
    } else {
      break;
    }
  }

  return Text(
    displayText + ellipsis,
    style: textStyle,
  );
}

/// 获取文本宽度的辅助方法
double _getTextWidth(String text, TextStyle style) {
  final TextPainter painter = TextPainter(
    text: TextSpan(text: text, style: style),
    textDirection: TextDirection.ltr,
    maxLines: 1,
  );
  painter.layout();
  return painter.width;
}