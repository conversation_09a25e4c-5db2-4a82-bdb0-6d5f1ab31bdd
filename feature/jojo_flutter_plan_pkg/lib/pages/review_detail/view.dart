import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/content_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/top_switch_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/widget/top_title_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ReviewDetailPageView extends StatefulWidget {

  final int? loadingScene;
  final String? subjectColor;
  final ReviewDetailState state;

  const ReviewDetailPageView({Key? key, required this.state, required this.subjectColor, this.loadingScene}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ReviewDetailPageViewState();
  }
}

class _ReviewDetailPageViewState extends State<ReviewDetailPageView> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
        ReviewDetailBuriedUtils.appViewScreenWidget(_ctrl.buriedString);
      } catch (e) {
        l.e("补学助手详情页", "首页浏览埋点异常");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLoadingV25(
      scene: PageScene.fromValue(widget.loadingScene ?? 1) ?? PageScene.common,
      hideProgress: true,
      exception: widget.state.exception,
      retry: () {
        if (!mounted) return;
        try {
          ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
          _ctrl.getSegmentsInfo();
        } catch (e) {
          l.e("补学助手详情页", "重新请求异常");
        } 
      },
      backWidget: Positioned(
        top: MediaQuery.of(context).padding.top,
        child: const AppbarLeft()),
        status: widget.state.pageStatus,
        child: Scaffold(
        primary: !JoJoRouter.isWindow,
        appBar: JoJoAppBar(
            title: S.of(context).reviewAssistant,
            backgroundColor: Colors.transparent,
            centerTitle: true,
            actions: [
              GestureDetector(
                onTap: () {
                  if (!mounted) return;
                  try {
                    ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
                    String buriedString = Uri.encodeComponent(_ctrl.buriedString ?? "");
                    String subjectColor = Uri.encodeComponent(_ctrl.subjectColor ?? "#FF9045");
                    String courseKey = _ctrl.courseKey ?? "";
                    String classId = _ctrl.classId ?? "";
                    ReviewDetailBuriedUtils.appClickWidget("搜索课时入口_点击", _ctrl.buriedString);
                    RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/reviewAssistantSearchPage?windowType=window&subjectColor=$subjectColor&courseKey=$courseKey&classId=$classId&buriedString=$buriedString");
                  } catch (e) {
                    l.e("补学助手详情页", "搜索按钮点击异常");
                  }
                },
                child: ImageAssetWeb(
                  assetName: AssetsImg.PLAN_IMAGE_PLAN_REVIEW_DETAIL_SEARCH_ICON,
                  width: 60,
                  height: 44,
                  fit: BoxFit.contain,
                  package: Config.package,
                ),
              )
            ],),
        body: AdaptiveOrientationLayout(portrait: (BuildContext context) {
          return SafeArea(child: _buildContentView(),);
        }, landscape: (BuildContext context) {
          return _buildContentView();
        })
        ));
  }

  Widget _buildContentView() {
    ReviewDetailCtrl _ctrl = context.read<ReviewDetailCtrl>();
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          ReviewTopSwitchWidget(subjectColor: widget.subjectColor,),
          ReviewTopTitleInfoWidget(subjectColor: widget.subjectColor, selected: false, lock: false, titleTxt: _ctrl.currentSegment?.segmentName, unFinishedNum: _ctrl.currentSegment?.unFinishLessonNum,showArrow: false, cellHeight: null,),
          ReviewContentWidget(ctrl: _ctrl,),
        ],
      ),
    );
  }
}
