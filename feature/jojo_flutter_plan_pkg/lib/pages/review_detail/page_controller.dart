
import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/state.dart';
import 'package:jojo_flutter_plan_pkg/service/review_assistant_api.dart';

class ReviewDetailCtrl extends Cubit<ReviewDetailState> {

  final int? segmentId;
  final int? weekId;
  final String? subjectColor;
  final String? courseKey;
  final String? classKey;
  final String? classId;
  final String? buriedString;
  
  ReviewAssistantApi? pageApi;
  ReviewAssistantApi? pageApiMock;
  bool selectAll = false;
  bool isRequestPageInfo = false;  // 是否正在请求页面接口数据（如果是，则选择按钮无法点击）
  bool courseCardAnimation = false;
  int currentIndex = 0;
  int? currentSegmentId;
  int? currentWeekId;
  CourseSegmentData? currentSegment;
  PageController? pageController;

  ReviewDetailCtrl(
    {ReviewAssistantApi? api,
    required this.segmentId, 
    required this.weekId, 
    required this.subjectColor, 
    required this.courseKey, 
    required this.classKey,
    required this.classId,
    required this.buriedString})
      : super(ReviewDetailState(pageStatus: PageStatus.loading)) {
    pageApi = api ?? ReviewAssistantApis;
  }

  getSegmentsInfo() async {
    ReviewDetailState newState = state.copyWith();
    currentSegmentId ??= (segmentId ?? 0);
    currentWeekId ??= (weekId ?? 0);
    try {
      if ((courseKey ?? "").isEmpty || (classKey ?? "").isEmpty || (classId ?? "").isEmpty) {
        var exception = Exception("请求参数为空");
        newState.pageStatus = PageStatus.error;
        newState.exception = exception;
        isRequestPageInfo = false;
        emit(newState);
        return;
      }
      isRequestPageInfo = true;
      // 获取当前课程下的主题信息
      CourseSegmentsData? data = await pageApi?.getCourseSegmentsInfo(courseKey: courseKey ?? "", classKey: classKey ?? "", review: 1);
      if (data != null) {
        if ((data.segments ?? []).isEmpty) {
          newState.pageStatus = PageStatus.error;
        } else { 
          // 先整理数据
          dealWithData(data, newState);
          // 查找当前主题
          findSegment(newState);
          // 处理页面定位到问题
          if (pageController == null) {
            pageController = PageController(initialPage: currentIndex);
          } else {
            pageController!.jumpToPage(currentIndex);
          }
          newState.pageStatus = PageStatus.success;
        }
      } else {
        newState.pageStatus = PageStatus.error;
      }
    } catch (e) {
      if (newState.segmentList == null) {
        var exception = e is Exception ? e : Exception(e.toString());
        newState.pageStatus = PageStatus.error;
        newState.exception = exception;
      } else {
        newState.pageStatus = PageStatus.success;
      }
    } finally {
      isRequestPageInfo = false;
    }
    emit(newState);
    if (newState.pageStatus == PageStatus.success) getLessonsInfo(currentSegment?.segmentId ?? 0, currentSegment?.weekId ?? 0);
  }

  void dealWithData(CourseSegmentsData data, ReviewDetailState newState) {
    newState.segmentList ??= [];
    newState.segmentList?.clear();

    List<CourseSegmentData?>  list = data.segments ?? [];
    for (var element in list) {
      if (element != null) {
        List weekList = element.weekList ?? [];
        if (weekList.isNotEmpty && element.courseChildType == ReviewcourseChildType.week) {
          for (var weekElement in weekList) {
            CourseSegmentData item = CourseSegmentData();
            item.segmentId = element.segmentId;
            item.weekId = weekElement.weekId;
            item.lock = weekElement.lock;
            item.unFinishLessonNum = weekElement.unFinishLessonNum;
            item.courseChildType = element.courseChildType;
            item.segmentName = "${element.segmentName}·${weekElement.weekName}";
            newState.segmentList!.add(item);
          }
        } else {
          newState.segmentList!.add(element);
        }
      }
    }
    newState.lessonsMap ??= {};
    newState.lessonsMap?.forEach((key, value) {
      value.needRefresh = true;
    });
  }

  void findSegment(ReviewDetailState newState) {
    List<CourseSegmentData?> list = newState.segmentList ?? [];
    if (list.isEmpty) return;
    // 是否需要更新当前主题
    bool needUpdateCurrentSegment = true;
    // 如果有选中的主题，则寻找主题
    for (var element in list) {
      if (currentSegmentId != 0 && currentWeekId != 0 && currentSegmentId == element?.segmentId && currentWeekId == element?.weekId) {
        setCurrentSegment(list.indexOf(element), element);
        needUpdateCurrentSegment = false;
        break;
      }
    }
    if (currentSegment == null) {
      // 没有找到有currentSegmentId以及currentWeekId都存在的主题，则只找currentSegmentId
      for (var element in list) {
        if (currentSegmentId != 0 && currentSegmentId == element?.segmentId) {
          setCurrentSegment(list.indexOf(element), element);
          needUpdateCurrentSegment = false;
          break;
        }
      }
    }
    if (currentSegment == null) {
      // 如果没有选择主题，则默认选择第一个
      setCurrentSegment(0, list.first);
      needUpdateCurrentSegment = false;
    }
    // 根据数据，更新当前选中的主题
    if (needUpdateCurrentSegment && currentSegment != null) {
      final segmentId = currentSegment?.segmentId ?? 0;
      final weekId = currentSegment?.weekId ?? 0;

      if (segmentId != 0) {
        final match = (weekId != 0)
           ? list.firstWhereOrNull((element) => element?.segmentId == segmentId && element?.weekId == weekId,)
           : list.firstWhereOrNull((element) => element?.segmentId == segmentId);

      if (match != null) {
        setCurrentSegment(list.indexOf(match), match);
      }
     }
    }
  }

  void setCurrentSegment(int index, CourseSegmentData? data) {
    currentIndex = index;
    currentSegment = data;
    currentSegmentId = currentSegment?.segmentId ?? 0;
    currentWeekId = currentSegment?.weekId ?? 0;
  }

  getLessonsInfo(int selectSegmentId, int selectWeekId) async {
    if (selectSegmentId == 0) return;
    ReviewDetailState newState = state.copyWith();
    newState.lessonsMap ??= {};
    String key = selectWeekId == 0 ? "$selectSegmentId" : "${selectSegmentId}_$selectWeekId";
    ReviewLessonDataModel? dataModel = newState.lessonsMap![key];
    dataModel ??= ReviewLessonDataModel();
    dataModel.segmentId = selectSegmentId;
    dataModel.weekId = selectWeekId;
    dataModel.lock = currentSegment?.lock ?? false;
    if (dataModel.needRefresh == false) {
      return;
    }
    try {
      CourseLessonsData? lessonsData = await pageApi?.getCourseLessonsInfo(
            courseKey: courseKey ?? "", 
            classKey: classKey ?? "", 
            classId: classId ?? "", 
            segmentId: "$selectSegmentId", 
            weekId: selectWeekId == 0 ? "" : "$selectWeekId");
      _processLessons(dataModel, lessonsData, selectSegmentId);
      dataModel.needRefresh = false;
      dataModel.requestType = RequestStatus.success;
    } catch (e) {
      dataModel.needRefresh = true;
      if (dataModel.list == null) dataModel.requestType = RequestStatus.failed;
    }
    newState.lessonsMap![key] = dataModel;
    emit(newState);
  }

  void _processLessons(ReviewLessonDataModel dataModel, CourseLessonsData? lessonsData, int selectSegmentId) {
    dataModel.list = [];
    dataModel.reviewList = [];
    for (var element in lessonsData?.list ?? []) {
      if (element?.segmentId != selectSegmentId) continue;
      for (CourseLessonItem lessonInfo in element?.lessonInfos ?? []) {
        var lessonCard = getLessonCourseCardInfo(lessonInfo);
        dataModel.list?.add(lessonCard);
        if (lessonInfo.makeup == true && !dataModel.lock) {
          dataModel.reviewList?.add(lessonCard);
        }
    }
  }
}

  CourseCard getLessonCourseCardInfo(CourseLessonItem lessonInfo) {
    CourseCard cardInfo = CourseCard(
        classKey,
        int.tryParse(classId ?? '0') ?? 0,
        null,
        null,
        lessonInfo.today,
        false,
        null,
        LessonCardType.LESSON.statusCode,
        lessonInfo.lessonName,
        lessonInfo.icon,
        lessonInfo.lessonId,
        lessonInfo.lessonOrder,
        null,
        segmentId,
        null,
        weekId,
        null,
        null,
        lessonInfo.studyStatus,
        null,
        lessonInfo.studyTipsVoice,
        lessonInfo.icon,
        lessonInfo.icon,
        lessonInfo.icon,
        lessonInfo.lockVoice,
        null,
        null,
        null,
        null,
        null,
        lessonInfo.lessonGrade,
        lessonInfo.lessonGradeResourceUrl,
        null,
        null,
        iconType: null,
        gifIconRes: null);
    cardInfo.router = lessonInfo.route;

    int nodeStatus = CourseCard.supplement;
    if (lessonInfo.unlock == false) {
      nodeStatus = CourseCard.unLocked;
      cardInfo.studyStatus = 0;  // 锁住状态不可学
      cardInfo.studyTipsVoice = lessonInfo.lockVoice;  // 带锁的课时需要播放带锁的语音
    } else {
      nodeStatus = lessonInfo.finish == false ? CourseCard.lockedUnFinish : CourseCard.lockedFinish;
    }
    if (lessonInfo.makeup == true && lessonInfo.makeupUIExpired != true) {
      nodeStatus = CourseCard.supplement;
    }
    cardInfo.nodeStatus = nodeStatus;
    cardInfo.mainColor = subjectColor ?? "#FF9045";

    return cardInfo;
  }

  void changePageToPre() {
    if (state.segmentList == null) {  
      return;
    }
    if(currentIndex > 0) {
      currentIndex = currentIndex - 1;
      currentSegment = state.segmentList?[currentIndex];
      currentSegmentId = currentSegment?.segmentId ?? 0;
      currentWeekId = currentSegment?.weekId ?? 0;
      pageController?.animateToPage(currentIndex, duration: const Duration(seconds: 1), curve: Curves.ease);
    }
    ReviewDetailBuriedUtils.appClickWidget("滑动切换主题", buriedString);
  }

  void changePageToNext() {
    if (state.segmentList == null) {
      return;
    }
    if(currentIndex < (state.segmentList ?? []).length - 1) {
      currentIndex = currentIndex + 1;
      currentSegment = state.segmentList?[currentIndex];
      currentSegmentId = currentSegment?.segmentId ?? 0;
      currentWeekId = currentSegment?.weekId ?? 0;
      pageController?.animateToPage(currentIndex, duration: const Duration(seconds: 1), curve: Curves.ease);
    }
    ReviewDetailBuriedUtils.appClickWidget("滑动切换主题", buriedString);
  }


  refresh() {
    if (!isClosed) {
      emit(state.copyWith());
    }
  }
}
