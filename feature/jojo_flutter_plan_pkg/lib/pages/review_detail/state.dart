import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';

class ReviewDetailState {
  PageStatus pageStatus;
  Exception? exception;
  List<CourseSegmentData>? segmentList;
  Map<String, ReviewLessonDataModel>? lessonsMap;

  ReviewDetailState({required this.pageStatus, this.segmentList, this.lessonsMap});

  ReviewDetailState copyWith() {
    return ReviewDetailState(pageStatus: pageStatus, segmentList: segmentList, lessonsMap: lessonsMap);
  }
}