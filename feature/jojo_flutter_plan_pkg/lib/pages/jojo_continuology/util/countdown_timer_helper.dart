import 'dart:async';

/// 倒计时定时器辅助工具
class CountdownTimerHelper {
  Timer? _timer;
  int _remainingSeconds = 0;
  int _totalSeconds = 0;
  bool _isRunning = false;
  bool _isPaused = false; // 新增暂停状态标记

  /// 倒计时回调函数
  final Function(int remainingSeconds)? onTick;

  /// 倒计时完成回调函数
  final Function? onComplete;

  /// 倒计时暂停回调函数
  final Function? onPause;

  /// 倒计时重置回调函数
  final Function? onReset;

  CountdownTimerHelper({
    this.onTick,
    this.onComplete,
    this.onPause,
    this.onReset,
  });

  /// 开始倒计时
  /// [seconds] 倒计时总秒数
  void startCountdown(int seconds) {
    if (seconds <= 0) {
      throw ArgumentError('倒计时秒数必须大于0');
    }

    // 强制停止任何现有定时器
    _forceStop();

    _totalSeconds = seconds;
    _remainingSeconds = seconds;
    _isRunning = true;
    _isPaused = false;

    // 立即触发第一次回调
    onTick?.call(_remainingSeconds);

    _startTimer();
  }

  /// 内部方法：启动定时器
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isRunning) {
        // 如果状态已经不是运行中，立即停止
        timer.cancel();
        return;
      }

      _remainingSeconds--;

      if (_remainingSeconds <= 0) {
        // 倒计时完成
        _isRunning = false;
        _isPaused = false;
        timer.cancel();
        _timer = null;
        onComplete?.call();
      } else {
        // 继续倒计时
        onTick?.call(_remainingSeconds);
      }
    });
  }

  /// 暂停倒计时
  void pause() {
    if (_isRunning && _timer != null) {
      _timer!.cancel();
      _timer = null;
      _isRunning = false;
      _isPaused = true; // 标记为暂停状态
      onPause?.call();
    }
  }

  /// 恢复倒计时
  void resume() {
    if (_isPaused && _remainingSeconds > 0) {
      _isRunning = true;
      _isPaused = false;
      _startTimer(); // 直接启动定时器，不重新设置时间
    }
  }

  /// 强制停止（内部方法）
  void _forceStop() {
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
    }
    _isRunning = false;
    _isPaused = false;
  }

  /// 停止倒计时
  void stop() {
    _forceStop();
  }

  /// 重置倒计时进度
  /// [newSeconds] 新的倒计时秒数，如果不传则重置为初始值
  void reset([int? newSeconds]) {
    _forceStop();

    if (newSeconds != null) {
      _totalSeconds = newSeconds;
    }

    _remainingSeconds = _totalSeconds;
    onReset?.call();

    // 重置后立即触发回调显示新的时间
    onTick?.call(_remainingSeconds);
  }

  /// 重新开始倒计时（重置并立即开始）
  void restart([int? newSeconds]) {
    if (newSeconds != null) {
      _totalSeconds = newSeconds;
    }

    reset();
    startCountdown(_totalSeconds);
  }

  /// 释放资源
  void dispose() {
    _forceStop();
    _remainingSeconds = 0;
    _totalSeconds = 0;
  }

  /// 获取剩余秒数
  int get remainingSeconds => _remainingSeconds;

  /// 获取总秒数
  int get totalSeconds => _totalSeconds;

  /// 是否正在运行
  bool get isRunning => _isRunning;

  /// 是否已暂停
  bool get isPaused => _isPaused;

  /// 是否已完成
  bool get isCompleted => _remainingSeconds <= 0 && _totalSeconds > 0;
}
