import 'package:collection/collection.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';

import '../model/jojo_continuology_api_data.dart';

class StudyGuideAudioImpl {
  final AudioPlayer audioPlayer;

  StudyGuideAudioImpl(this.audioPlayer);

  void playAudioWithUrl(String url) {
    audioPlayer.play(UrlSource(url)).catchError((e) {
      l.e('audioPlayer.play', e);
    });
  }

  void pauseAudio() {
    if (audioPlayer.state == PlayerState.playing) {
      audioPlayer.pause();
    }
  }

  void stopAudio() {
    if (audioPlayer.state == PlayerState.playing) {
      audioPlayer.stop();
    }
  }

  void resume() {
    if (audioPlayer.state == PlayerState.paused) {
      audioPlayer.resume();
    }
  }

  void dispose() {
    audioPlayer.dispose();
  }

  String findPlayAudioUrl(
      {required int scene, required List<GuideResource> guideResourceList}) {
    // 保持原有的 scene == -1 逻辑
    if (scene == -1 || guideResourceList.isEmpty) {
      return '';
    }
    final resource =
        guideResourceList.firstWhereOrNull((element) => element.scene == scene);
    if (resource == null) {
      return '';
    }
    final audioUrl = resource.audio;
    if (audioUrl == null || audioUrl.isEmpty) {
      return '';
    }

    return audioUrl;
  }
}
