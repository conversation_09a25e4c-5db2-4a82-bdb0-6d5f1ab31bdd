import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_api_data.dart';

import '../../../static/img.dart';

class JojoContinuologyMilestoneData {
  Milestone? milestone;
  int? bestInHistory;
  String? subjectName;

  JojoContinuologyMilestoneData({
    this.milestone,
    this.bestInHistory,
    this.subjectName,
  });

  factory JojoContinuologyMilestoneData.fromJson(Map<String, dynamic> json) {
    return JojoContinuologyMilestoneData(
        milestone: json['milestone'] == null
            ? null
            : Milestone.fromJson(json['milestone'] as Map<String, dynamic>),
        bestInHistory: json['bestInHistory'] as int?,
        subjectName: json['subjectName'] as String?);
  }

  Map<String, dynamic> toJson() {
    return {
      'milestone': milestone?.toJson(),
      'bestInHistory': bestInHistory,
      'subjectName': subjectName,
    };
  }
}

class JojoContinuologyMilestoneProgressBoxData {
  final int days;
  final bool isOpen;
  final int index;

  JojoContinuologyMilestoneProgressBoxData({
    required this.days,
    required this.isOpen,
    required this.index, 
  });

  String imageAsset() {
    return isOpen ? AssetsImg.CONTINUE_STUDY_MILESTONE_BOX_OPENED : AssetsImg.CONTINUE_STUDY_MILESTONE_BOX_LOCK;
  }
}