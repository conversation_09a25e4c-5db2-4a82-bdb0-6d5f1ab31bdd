// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jojo_continuology_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_JoJoContinuologyHeaderDetailDate
    _$$_JoJoContinuologyHeaderDetailDateFromJson(Map<String, dynamic> json) =>
        _$_JoJoContinuologyHeaderDetailDate(
          status: json['status'] as int?,
          clientDynamic: json['clientDynamic'] as String?,
          spineResource: json['spineResource'] as String?,
          consecutiveDays: json['consecutiveDays'] as int?,
          consecutiveUnit: json['consecutiveUnit'] as String?,
          consecutiveTitle: json['consecutiveTitle'] as String?,
          consecutiveBest: json['consecutiveBest'] as String?,
          consecutiveBestDay: json['consecutiveBestDay'] as String?,
          initialTitle: json['initialTitle'] as String?,
        );

Map<String, dynamic> _$$_JoJoContinuologyHeaderDetailDateToJson(
        _$_JoJoContinuologyHeaderDetailDate instance) =>
    <String, dynamic>{
      'status': instance.status,
      'clientDynamic': instance.clientDynamic,
      'spineResource': instance.spineResource,
      'consecutiveDays': instance.consecutiveDays,
      'consecutiveUnit': instance.consecutiveUnit,
      'consecutiveTitle': instance.consecutiveTitle,
      'consecutiveBest': instance.consecutiveBest,
      'consecutiveBestDay': instance.consecutiveBestDay,
      'initialTitle': instance.initialTitle,
    };

_$_JoJoContinuologyHeaderSpineDate _$$_JoJoContinuologyHeaderSpineDateFromJson(
        Map<String, dynamic> json) =>
    _$_JoJoContinuologyHeaderSpineDate(
      atlasFile: json['atlasFile'] as String?,
      skelFile: json['skelFile'] as String?,
    );

Map<String, dynamic> _$$_JoJoContinuologyHeaderSpineDateToJson(
        _$_JoJoContinuologyHeaderSpineDate instance) =>
    <String, dynamic>{
      'atlasFile': instance.atlasFile,
      'skelFile': instance.skelFile,
    };

_$_JoJoContinuologyTargetData _$$_JoJoContinuologyTargetDataFromJson(
        Map<String, dynamic> json) =>
    _$_JoJoContinuologyTargetData(
      scene: json['scene'] as int?,
      propId: json['propId'] as String?,
      target: json['target'] == null
          ? null
          : JoJoContinuologyTargetDetail.fromJson(
              json['target'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_JoJoContinuologyTargetDataToJson(
        _$_JoJoContinuologyTargetData instance) =>
    <String, dynamic>{
      'scene': instance.scene,
      'propId': instance.propId,
      'target': instance.target,
    };

_$_JoJoContinuologyTargetDetail _$$_JoJoContinuologyTargetDetailFromJson(
        Map<String, dynamic> json) =>
    _$_JoJoContinuologyTargetDetail(
      classKey: json['classKey'] as String?,
      targetId: json['targetId'] as String?,
      courseKey: json['courseKey'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$_JoJoContinuologyTargetDetailToJson(
        _$_JoJoContinuologyTargetDetail instance) =>
    <String, dynamic>{
      'classKey': instance.classKey,
      'targetId': instance.targetId,
      'courseKey': instance.courseKey,
      'type': instance.type,
    };
