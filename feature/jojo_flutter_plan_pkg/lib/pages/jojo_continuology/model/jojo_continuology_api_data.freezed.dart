// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jojo_continuology_api_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ContinuologyDataModel _$ContinuologyDataModelFromJson(
    Map<String, dynamic> json) {
  return _ContinuologyDataModel.fromJson(json);
}

/// @nodoc
mixin _$ContinuologyDataModel {
  String? get subjectColor => throw _privateConstructorUsedError; // 主题色
  String? get subjectName => throw _privateConstructorUsedError; // 科目名称
  String? get ruleDescRoute => throw _privateConstructorUsedError; // 规则说明路由
  int? get onlyTrainingCamp => throw _privateConstructorUsedError; // 是否为训练营
  ContinuologyInfo? get continuous =>
      throw _privateConstructorUsedError; // 连续学习信息
  Milestone? get milestone => throw _privateConstructorUsedError;
  int? get guideStatus => throw _privateConstructorUsedError;
  List<GuideResource>? get guideResourceList =>
      throw _privateConstructorUsedError; // 引导资源
  PropInfo? get prop => throw _privateConstructorUsedError; // 道具信息
  List<CalendarInfo>? get calendar => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContinuologyDataModelCopyWith<ContinuologyDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContinuologyDataModelCopyWith<$Res> {
  factory $ContinuologyDataModelCopyWith(ContinuologyDataModel value,
          $Res Function(ContinuologyDataModel) then) =
      _$ContinuologyDataModelCopyWithImpl<$Res, ContinuologyDataModel>;
  @useResult
  $Res call(
      {String? subjectColor,
      String? subjectName,
      String? ruleDescRoute,
      int? onlyTrainingCamp,
      ContinuologyInfo? continuous,
      Milestone? milestone,
      int? guideStatus,
      List<GuideResource>? guideResourceList,
      PropInfo? prop,
      List<CalendarInfo>? calendar});

  $ContinuologyInfoCopyWith<$Res>? get continuous;
  $MilestoneCopyWith<$Res>? get milestone;
  $PropInfoCopyWith<$Res>? get prop;
}

/// @nodoc
class _$ContinuologyDataModelCopyWithImpl<$Res,
        $Val extends ContinuologyDataModel>
    implements $ContinuologyDataModelCopyWith<$Res> {
  _$ContinuologyDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectColor = freezed,
    Object? subjectName = freezed,
    Object? ruleDescRoute = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? continuous = freezed,
    Object? milestone = freezed,
    Object? guideStatus = freezed,
    Object? guideResourceList = freezed,
    Object? prop = freezed,
    Object? calendar = freezed,
  }) {
    return _then(_value.copyWith(
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleDescRoute: freezed == ruleDescRoute
          ? _value.ruleDescRoute
          : ruleDescRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      continuous: freezed == continuous
          ? _value.continuous
          : continuous // ignore: cast_nullable_to_non_nullable
              as ContinuologyInfo?,
      milestone: freezed == milestone
          ? _value.milestone
          : milestone // ignore: cast_nullable_to_non_nullable
              as Milestone?,
      guideStatus: freezed == guideStatus
          ? _value.guideStatus
          : guideStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      guideResourceList: freezed == guideResourceList
          ? _value.guideResourceList
          : guideResourceList // ignore: cast_nullable_to_non_nullable
              as List<GuideResource>?,
      prop: freezed == prop
          ? _value.prop
          : prop // ignore: cast_nullable_to_non_nullable
              as PropInfo?,
      calendar: freezed == calendar
          ? _value.calendar
          : calendar // ignore: cast_nullable_to_non_nullable
              as List<CalendarInfo>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ContinuologyInfoCopyWith<$Res>? get continuous {
    if (_value.continuous == null) {
      return null;
    }

    return $ContinuologyInfoCopyWith<$Res>(_value.continuous!, (value) {
      return _then(_value.copyWith(continuous: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MilestoneCopyWith<$Res>? get milestone {
    if (_value.milestone == null) {
      return null;
    }

    return $MilestoneCopyWith<$Res>(_value.milestone!, (value) {
      return _then(_value.copyWith(milestone: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PropInfoCopyWith<$Res>? get prop {
    if (_value.prop == null) {
      return null;
    }

    return $PropInfoCopyWith<$Res>(_value.prop!, (value) {
      return _then(_value.copyWith(prop: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ContinuologyDataModelCopyWith<$Res>
    implements $ContinuologyDataModelCopyWith<$Res> {
  factory _$$_ContinuologyDataModelCopyWith(_$_ContinuologyDataModel value,
          $Res Function(_$_ContinuologyDataModel) then) =
      __$$_ContinuologyDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? subjectColor,
      String? subjectName,
      String? ruleDescRoute,
      int? onlyTrainingCamp,
      ContinuologyInfo? continuous,
      Milestone? milestone,
      int? guideStatus,
      List<GuideResource>? guideResourceList,
      PropInfo? prop,
      List<CalendarInfo>? calendar});

  @override
  $ContinuologyInfoCopyWith<$Res>? get continuous;
  @override
  $MilestoneCopyWith<$Res>? get milestone;
  @override
  $PropInfoCopyWith<$Res>? get prop;
}

/// @nodoc
class __$$_ContinuologyDataModelCopyWithImpl<$Res>
    extends _$ContinuologyDataModelCopyWithImpl<$Res, _$_ContinuologyDataModel>
    implements _$$_ContinuologyDataModelCopyWith<$Res> {
  __$$_ContinuologyDataModelCopyWithImpl(_$_ContinuologyDataModel _value,
      $Res Function(_$_ContinuologyDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectColor = freezed,
    Object? subjectName = freezed,
    Object? ruleDescRoute = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? continuous = freezed,
    Object? milestone = freezed,
    Object? guideStatus = freezed,
    Object? guideResourceList = freezed,
    Object? prop = freezed,
    Object? calendar = freezed,
  }) {
    return _then(_$_ContinuologyDataModel(
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleDescRoute: freezed == ruleDescRoute
          ? _value.ruleDescRoute
          : ruleDescRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      continuous: freezed == continuous
          ? _value.continuous
          : continuous // ignore: cast_nullable_to_non_nullable
              as ContinuologyInfo?,
      milestone: freezed == milestone
          ? _value.milestone
          : milestone // ignore: cast_nullable_to_non_nullable
              as Milestone?,
      guideStatus: freezed == guideStatus
          ? _value.guideStatus
          : guideStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      guideResourceList: freezed == guideResourceList
          ? _value._guideResourceList
          : guideResourceList // ignore: cast_nullable_to_non_nullable
              as List<GuideResource>?,
      prop: freezed == prop
          ? _value.prop
          : prop // ignore: cast_nullable_to_non_nullable
              as PropInfo?,
      calendar: freezed == calendar
          ? _value._calendar
          : calendar // ignore: cast_nullable_to_non_nullable
              as List<CalendarInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContinuologyDataModel implements _ContinuologyDataModel {
  _$_ContinuologyDataModel(
      {this.subjectColor,
      this.subjectName,
      this.ruleDescRoute,
      this.onlyTrainingCamp,
      this.continuous,
      this.milestone,
      this.guideStatus = -1,
      final List<GuideResource>? guideResourceList,
      this.prop,
      final List<CalendarInfo>? calendar})
      : _guideResourceList = guideResourceList,
        _calendar = calendar;

  factory _$_ContinuologyDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_ContinuologyDataModelFromJson(json);

  @override
  final String? subjectColor;
// 主题色
  @override
  final String? subjectName;
// 科目名称
  @override
  final String? ruleDescRoute;
// 规则说明路由
  @override
  final int? onlyTrainingCamp;
// 是否为训练营
  @override
  final ContinuologyInfo? continuous;
// 连续学习信息
  @override
  final Milestone? milestone;
  @override
  @JsonKey()
  final int? guideStatus;
  final List<GuideResource>? _guideResourceList;
  @override
  List<GuideResource>? get guideResourceList {
    final value = _guideResourceList;
    if (value == null) return null;
    if (_guideResourceList is EqualUnmodifiableListView)
      return _guideResourceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 引导资源
  @override
  final PropInfo? prop;
// 道具信息
  final List<CalendarInfo>? _calendar;
// 道具信息
  @override
  List<CalendarInfo>? get calendar {
    final value = _calendar;
    if (value == null) return null;
    if (_calendar is EqualUnmodifiableListView) return _calendar;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ContinuologyDataModel(subjectColor: $subjectColor, subjectName: $subjectName, ruleDescRoute: $ruleDescRoute, onlyTrainingCamp: $onlyTrainingCamp, continuous: $continuous, milestone: $milestone, guideStatus: $guideStatus, guideResourceList: $guideResourceList, prop: $prop, calendar: $calendar)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContinuologyDataModel &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.ruleDescRoute, ruleDescRoute) ||
                other.ruleDescRoute == ruleDescRoute) &&
            (identical(other.onlyTrainingCamp, onlyTrainingCamp) ||
                other.onlyTrainingCamp == onlyTrainingCamp) &&
            (identical(other.continuous, continuous) ||
                other.continuous == continuous) &&
            (identical(other.milestone, milestone) ||
                other.milestone == milestone) &&
            (identical(other.guideStatus, guideStatus) ||
                other.guideStatus == guideStatus) &&
            const DeepCollectionEquality()
                .equals(other._guideResourceList, _guideResourceList) &&
            (identical(other.prop, prop) || other.prop == prop) &&
            const DeepCollectionEquality().equals(other._calendar, _calendar));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectColor,
      subjectName,
      ruleDescRoute,
      onlyTrainingCamp,
      continuous,
      milestone,
      guideStatus,
      const DeepCollectionEquality().hash(_guideResourceList),
      prop,
      const DeepCollectionEquality().hash(_calendar));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContinuologyDataModelCopyWith<_$_ContinuologyDataModel> get copyWith =>
      __$$_ContinuologyDataModelCopyWithImpl<_$_ContinuologyDataModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContinuologyDataModelToJson(
      this,
    );
  }
}

abstract class _ContinuologyDataModel implements ContinuologyDataModel {
  factory _ContinuologyDataModel(
      {final String? subjectColor,
      final String? subjectName,
      final String? ruleDescRoute,
      final int? onlyTrainingCamp,
      final ContinuologyInfo? continuous,
      final Milestone? milestone,
      final int? guideStatus,
      final List<GuideResource>? guideResourceList,
      final PropInfo? prop,
      final List<CalendarInfo>? calendar}) = _$_ContinuologyDataModel;

  factory _ContinuologyDataModel.fromJson(Map<String, dynamic> json) =
      _$_ContinuologyDataModel.fromJson;

  @override
  String? get subjectColor;
  @override // 主题色
  String? get subjectName;
  @override // 科目名称
  String? get ruleDescRoute;
  @override // 规则说明路由
  int? get onlyTrainingCamp;
  @override // 是否为训练营
  ContinuologyInfo? get continuous;
  @override // 连续学习信息
  Milestone? get milestone;
  @override
  int? get guideStatus;
  @override
  List<GuideResource>? get guideResourceList;
  @override // 引导资源
  PropInfo? get prop;
  @override // 道具信息
  List<CalendarInfo>? get calendar;
  @override
  @JsonKey(ignore: true)
  _$$_ContinuologyDataModelCopyWith<_$_ContinuologyDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

ContinuologyInfo _$ContinuologyInfoFromJson(Map<String, dynamic> json) {
  return _ContinuologyInfo.fromJson(json);
}

/// @nodoc
mixin _$ContinuologyInfo {
  int? get days => throw _privateConstructorUsedError; // 连续学天数
  int? get status =>
      throw _privateConstructorUsedError; // 连续学状态(1保持连胜, 2中断连胜, 3初始化的期待)
  String? get resource => throw _privateConstructorUsedError; // 连续学资源
  String? get clientDynamic => throw _privateConstructorUsedError; // 连续学资源
  int? get bestInHistory => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContinuologyInfoCopyWith<ContinuologyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContinuologyInfoCopyWith<$Res> {
  factory $ContinuologyInfoCopyWith(
          ContinuologyInfo value, $Res Function(ContinuologyInfo) then) =
      _$ContinuologyInfoCopyWithImpl<$Res, ContinuologyInfo>;
  @useResult
  $Res call(
      {int? days,
      int? status,
      String? resource,
      String? clientDynamic,
      int? bestInHistory});
}

/// @nodoc
class _$ContinuologyInfoCopyWithImpl<$Res, $Val extends ContinuologyInfo>
    implements $ContinuologyInfoCopyWith<$Res> {
  _$ContinuologyInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? days = freezed,
    Object? status = freezed,
    Object? resource = freezed,
    Object? clientDynamic = freezed,
    Object? bestInHistory = freezed,
  }) {
    return _then(_value.copyWith(
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      bestInHistory: freezed == bestInHistory
          ? _value.bestInHistory
          : bestInHistory // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContinuologyInfoCopyWith<$Res>
    implements $ContinuologyInfoCopyWith<$Res> {
  factory _$$_ContinuologyInfoCopyWith(
          _$_ContinuologyInfo value, $Res Function(_$_ContinuologyInfo) then) =
      __$$_ContinuologyInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? days,
      int? status,
      String? resource,
      String? clientDynamic,
      int? bestInHistory});
}

/// @nodoc
class __$$_ContinuologyInfoCopyWithImpl<$Res>
    extends _$ContinuologyInfoCopyWithImpl<$Res, _$_ContinuologyInfo>
    implements _$$_ContinuologyInfoCopyWith<$Res> {
  __$$_ContinuologyInfoCopyWithImpl(
      _$_ContinuologyInfo _value, $Res Function(_$_ContinuologyInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? days = freezed,
    Object? status = freezed,
    Object? resource = freezed,
    Object? clientDynamic = freezed,
    Object? bestInHistory = freezed,
  }) {
    return _then(_$_ContinuologyInfo(
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      bestInHistory: freezed == bestInHistory
          ? _value.bestInHistory
          : bestInHistory // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContinuologyInfo implements _ContinuologyInfo {
  _$_ContinuologyInfo(
      {this.days,
      this.status,
      this.resource,
      this.clientDynamic,
      this.bestInHistory});

  factory _$_ContinuologyInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ContinuologyInfoFromJson(json);

  @override
  final int? days;
// 连续学天数
  @override
  final int? status;
// 连续学状态(1保持连胜, 2中断连胜, 3初始化的期待)
  @override
  final String? resource;
// 连续学资源
  @override
  final String? clientDynamic;
// 连续学资源
  @override
  final int? bestInHistory;

  @override
  String toString() {
    return 'ContinuologyInfo(days: $days, status: $status, resource: $resource, clientDynamic: $clientDynamic, bestInHistory: $bestInHistory)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContinuologyInfo &&
            (identical(other.days, days) || other.days == days) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.resource, resource) ||
                other.resource == resource) &&
            (identical(other.clientDynamic, clientDynamic) ||
                other.clientDynamic == clientDynamic) &&
            (identical(other.bestInHistory, bestInHistory) ||
                other.bestInHistory == bestInHistory));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, days, status, resource, clientDynamic, bestInHistory);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContinuologyInfoCopyWith<_$_ContinuologyInfo> get copyWith =>
      __$$_ContinuologyInfoCopyWithImpl<_$_ContinuologyInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContinuologyInfoToJson(
      this,
    );
  }
}

abstract class _ContinuologyInfo implements ContinuologyInfo {
  factory _ContinuologyInfo(
      {final int? days,
      final int? status,
      final String? resource,
      final String? clientDynamic,
      final int? bestInHistory}) = _$_ContinuologyInfo;

  factory _ContinuologyInfo.fromJson(Map<String, dynamic> json) =
      _$_ContinuologyInfo.fromJson;

  @override
  int? get days;
  @override // 连续学天数
  int? get status;
  @override // 连续学状态(1保持连胜, 2中断连胜, 3初始化的期待)
  String? get resource;
  @override // 连续学资源
  String? get clientDynamic;
  @override // 连续学资源
  int? get bestInHistory;
  @override
  @JsonKey(ignore: true)
  _$$_ContinuologyInfoCopyWith<_$_ContinuologyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Milestone _$MilestoneFromJson(Map<String, dynamic> json) {
  return _Milestone.fromJson(json);
}

/// @nodoc
mixin _$Milestone {
  String? get title => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get buttonRoute => throw _privateConstructorUsedError;
  List<TargetList>? get targetList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneCopyWith<Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneCopyWith<$Res> {
  factory $MilestoneCopyWith(Milestone value, $Res Function(Milestone) then) =
      _$MilestoneCopyWithImpl<$Res, Milestone>;
  @useResult
  $Res call(
      {String? title,
      String? buttonText,
      String? buttonRoute,
      List<TargetList>? targetList});
}

/// @nodoc
class _$MilestoneCopyWithImpl<$Res, $Val extends Milestone>
    implements $MilestoneCopyWith<$Res> {
  _$MilestoneCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? buttonText = freezed,
    Object? buttonRoute = freezed,
    Object? targetList = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      targetList: freezed == targetList
          ? _value.targetList
          : targetList // ignore: cast_nullable_to_non_nullable
              as List<TargetList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneCopyWith<$Res> implements $MilestoneCopyWith<$Res> {
  factory _$$_MilestoneCopyWith(
          _$_Milestone value, $Res Function(_$_Milestone) then) =
      __$$_MilestoneCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? buttonText,
      String? buttonRoute,
      List<TargetList>? targetList});
}

/// @nodoc
class __$$_MilestoneCopyWithImpl<$Res>
    extends _$MilestoneCopyWithImpl<$Res, _$_Milestone>
    implements _$$_MilestoneCopyWith<$Res> {
  __$$_MilestoneCopyWithImpl(
      _$_Milestone _value, $Res Function(_$_Milestone) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? buttonText = freezed,
    Object? buttonRoute = freezed,
    Object? targetList = freezed,
  }) {
    return _then(_$_Milestone(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      targetList: freezed == targetList
          ? _value._targetList
          : targetList // ignore: cast_nullable_to_non_nullable
              as List<TargetList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Milestone implements _Milestone {
  const _$_Milestone(
      {this.title,
      this.buttonText,
      this.buttonRoute,
      final List<TargetList>? targetList})
      : _targetList = targetList;

  factory _$_Milestone.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneFromJson(json);

  @override
  final String? title;
  @override
  final String? buttonText;
  @override
  final String? buttonRoute;
  final List<TargetList>? _targetList;
  @override
  List<TargetList>? get targetList {
    final value = _targetList;
    if (value == null) return null;
    if (_targetList is EqualUnmodifiableListView) return _targetList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Milestone(title: $title, buttonText: $buttonText, buttonRoute: $buttonRoute, targetList: $targetList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Milestone &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.buttonRoute, buttonRoute) ||
                other.buttonRoute == buttonRoute) &&
            const DeepCollectionEquality()
                .equals(other._targetList, _targetList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, buttonText, buttonRoute,
      const DeepCollectionEquality().hash(_targetList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      __$$_MilestoneCopyWithImpl<_$_Milestone>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneToJson(
      this,
    );
  }
}

abstract class _Milestone implements Milestone {
  const factory _Milestone(
      {final String? title,
      final String? buttonText,
      final String? buttonRoute,
      final List<TargetList>? targetList}) = _$_Milestone;

  factory _Milestone.fromJson(Map<String, dynamic> json) =
      _$_Milestone.fromJson;

  @override
  String? get title;
  @override
  String? get buttonText;
  @override
  String? get buttonRoute;
  @override
  List<TargetList>? get targetList;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

TargetList _$TargetListFromJson(Map<String, dynamic> json) {
  return _TargetList.fromJson(json);
}

/// @nodoc
mixin _$TargetList {
  int? get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TargetListCopyWith<TargetList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TargetListCopyWith<$Res> {
  factory $TargetListCopyWith(
          TargetList value, $Res Function(TargetList) then) =
      _$TargetListCopyWithImpl<$Res, TargetList>;
  @useResult
  $Res call({int? value});
}

/// @nodoc
class _$TargetListCopyWithImpl<$Res, $Val extends TargetList>
    implements $TargetListCopyWith<$Res> {
  _$TargetListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TargetListCopyWith<$Res>
    implements $TargetListCopyWith<$Res> {
  factory _$$_TargetListCopyWith(
          _$_TargetList value, $Res Function(_$_TargetList) then) =
      __$$_TargetListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? value});
}

/// @nodoc
class __$$_TargetListCopyWithImpl<$Res>
    extends _$TargetListCopyWithImpl<$Res, _$_TargetList>
    implements _$$_TargetListCopyWith<$Res> {
  __$$_TargetListCopyWithImpl(
      _$_TargetList _value, $Res Function(_$_TargetList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$_TargetList(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TargetList implements _TargetList {
  const _$_TargetList({this.value});

  factory _$_TargetList.fromJson(Map<String, dynamic> json) =>
      _$$_TargetListFromJson(json);

  @override
  final int? value;

  @override
  String toString() {
    return 'TargetList(value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TargetList &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TargetListCopyWith<_$_TargetList> get copyWith =>
      __$$_TargetListCopyWithImpl<_$_TargetList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TargetListToJson(
      this,
    );
  }
}

abstract class _TargetList implements TargetList {
  const factory _TargetList({final int? value}) = _$_TargetList;

  factory _TargetList.fromJson(Map<String, dynamic> json) =
      _$_TargetList.fromJson;

  @override
  int? get value;
  @override
  @JsonKey(ignore: true)
  _$$_TargetListCopyWith<_$_TargetList> get copyWith =>
      throw _privateConstructorUsedError;
}

PropInfo _$PropInfoFromJson(Map<String, dynamic> json) {
  return _PropInfo.fromJson(json);
}

/// @nodoc
mixin _$PropInfo {
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError; // 道具信息
  String? get jumpRoute => throw _privateConstructorUsedError; // 道具信息
  set jumpRoute(String? value) => throw _privateConstructorUsedError; // 跳转路由
  List<ItemInfo>? get items => throw _privateConstructorUsedError; // 跳转路由
  set items(List<ItemInfo>? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PropInfoCopyWith<PropInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PropInfoCopyWith<$Res> {
  factory $PropInfoCopyWith(PropInfo value, $Res Function(PropInfo) then) =
      _$PropInfoCopyWithImpl<$Res, PropInfo>;
  @useResult
  $Res call({String? title, String? jumpRoute, List<ItemInfo>? items});
}

/// @nodoc
class _$PropInfoCopyWithImpl<$Res, $Val extends PropInfo>
    implements $PropInfoCopyWith<$Res> {
  _$PropInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? jumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ItemInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PropInfoCopyWith<$Res> implements $PropInfoCopyWith<$Res> {
  factory _$$_PropInfoCopyWith(
          _$_PropInfo value, $Res Function(_$_PropInfo) then) =
      __$$_PropInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? title, String? jumpRoute, List<ItemInfo>? items});
}

/// @nodoc
class __$$_PropInfoCopyWithImpl<$Res>
    extends _$PropInfoCopyWithImpl<$Res, _$_PropInfo>
    implements _$$_PropInfoCopyWith<$Res> {
  __$$_PropInfoCopyWithImpl(
      _$_PropInfo _value, $Res Function(_$_PropInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? jumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_$_PropInfo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ItemInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PropInfo implements _PropInfo {
  _$_PropInfo({this.title, this.jumpRoute, this.items});

  factory _$_PropInfo.fromJson(Map<String, dynamic> json) =>
      _$$_PropInfoFromJson(json);

  @override
  String? title;
// 道具信息
  @override
  String? jumpRoute;
// 跳转路由
  @override
  List<ItemInfo>? items;

  @override
  String toString() {
    return 'PropInfo(title: $title, jumpRoute: $jumpRoute, items: $items)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PropInfoCopyWith<_$_PropInfo> get copyWith =>
      __$$_PropInfoCopyWithImpl<_$_PropInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PropInfoToJson(
      this,
    );
  }
}

abstract class _PropInfo implements PropInfo {
  factory _PropInfo({String? title, String? jumpRoute, List<ItemInfo>? items}) =
      _$_PropInfo;

  factory _PropInfo.fromJson(Map<String, dynamic> json) = _$_PropInfo.fromJson;

  @override
  String? get title;
  set title(String? value);
  @override // 道具信息
  String? get jumpRoute; // 道具信息
  set jumpRoute(String? value);
  @override // 跳转路由
  List<ItemInfo>? get items; // 跳转路由
  set items(List<ItemInfo>? value);
  @override
  @JsonKey(ignore: true)
  _$$_PropInfoCopyWith<_$_PropInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ItemInfo _$ItemInfoFromJson(Map<String, dynamic> json) {
  return _ItemInfo.fromJson(json);
}

/// @nodoc
mixin _$ItemInfo {
  int? get id => throw _privateConstructorUsedError; // 道具id
  int? get type => throw _privateConstructorUsedError; // 道具类型(2001复活, 2002连胜)
  int? get number => throw _privateConstructorUsedError; // 道具数
  int? get isFree => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ItemInfoCopyWith<ItemInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ItemInfoCopyWith<$Res> {
  factory $ItemInfoCopyWith(ItemInfo value, $Res Function(ItemInfo) then) =
      _$ItemInfoCopyWithImpl<$Res, ItemInfo>;
  @useResult
  $Res call({int? id, int? type, int? number, int? isFree});
}

/// @nodoc
class _$ItemInfoCopyWithImpl<$Res, $Val extends ItemInfo>
    implements $ItemInfoCopyWith<$Res> {
  _$ItemInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? number = freezed,
    Object? isFree = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ItemInfoCopyWith<$Res> implements $ItemInfoCopyWith<$Res> {
  factory _$$_ItemInfoCopyWith(
          _$_ItemInfo value, $Res Function(_$_ItemInfo) then) =
      __$$_ItemInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, int? type, int? number, int? isFree});
}

/// @nodoc
class __$$_ItemInfoCopyWithImpl<$Res>
    extends _$ItemInfoCopyWithImpl<$Res, _$_ItemInfo>
    implements _$$_ItemInfoCopyWith<$Res> {
  __$$_ItemInfoCopyWithImpl(
      _$_ItemInfo _value, $Res Function(_$_ItemInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? number = freezed,
    Object? isFree = freezed,
  }) {
    return _then(_$_ItemInfo(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ItemInfo implements _ItemInfo {
  _$_ItemInfo({this.id, this.type, this.number, this.isFree});

  factory _$_ItemInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ItemInfoFromJson(json);

  @override
  final int? id;
// 道具id
  @override
  final int? type;
// 道具类型(2001复活, 2002连胜)
  @override
  final int? number;
// 道具数
  @override
  final int? isFree;

  @override
  String toString() {
    return 'ItemInfo(id: $id, type: $type, number: $number, isFree: $isFree)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ItemInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.isFree, isFree) || other.isFree == isFree));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, number, isFree);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ItemInfoCopyWith<_$_ItemInfo> get copyWith =>
      __$$_ItemInfoCopyWithImpl<_$_ItemInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ItemInfoToJson(
      this,
    );
  }
}

abstract class _ItemInfo implements ItemInfo {
  factory _ItemInfo(
      {final int? id,
      final int? type,
      final int? number,
      final int? isFree}) = _$_ItemInfo;

  factory _ItemInfo.fromJson(Map<String, dynamic> json) = _$_ItemInfo.fromJson;

  @override
  int? get id;
  @override // 道具id
  int? get type;
  @override // 道具类型(2001复活, 2002连胜)
  int? get number;
  @override // 道具数
  int? get isFree;
  @override
  @JsonKey(ignore: true)
  _$$_ItemInfoCopyWith<_$_ItemInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

CalendarInfo _$CalendarInfoFromJson(Map<String, dynamic> json) {
  return _CalendarInfo.fromJson(json);
}

/// @nodoc
mixin _$CalendarInfo {
  int? get time => throw _privateConstructorUsedError;
  set time(int? value) => throw _privateConstructorUsedError; // 排课时间戳(当天0点)
  int? get status => throw _privateConstructorUsedError; // 排课时间戳(当天0点)
  set status(int? value) =>
      throw _privateConstructorUsedError; // 状态(0未解锁, 1待点亮, 2火鸡叫, 3冰冻叫, 4嗝屁叫)
  int? get frostEndTime =>
      throw _privateConstructorUsedError; // 状态(0未解锁, 1待点亮, 2火鸡叫, 3冰冻叫, 4嗝屁叫)
  set frostEndTime(int? value) => throw _privateConstructorUsedError; // 冰冻结束时间
  int? get isUseConcatProp => throw _privateConstructorUsedError; // 冰冻结束时间
  set isUseConcatProp(int? value) =>
      throw _privateConstructorUsedError; // 是否使用了连胜道具
  bool? get isAdd => throw _privateConstructorUsedError; // 是否使用了连胜道具
  set isAdd(bool? value) =>
      throw _privateConstructorUsedError; // 是否已经添加过（用于组装数据，减少循环）
  List<LessonInfo>? get lessonList =>
      throw _privateConstructorUsedError; // 是否已经添加过（用于组装数据，减少循环）
  set lessonList(List<LessonInfo>? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CalendarInfoCopyWith<CalendarInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarInfoCopyWith<$Res> {
  factory $CalendarInfoCopyWith(
          CalendarInfo value, $Res Function(CalendarInfo) then) =
      _$CalendarInfoCopyWithImpl<$Res, CalendarInfo>;
  @useResult
  $Res call(
      {int? time,
      int? status,
      int? frostEndTime,
      int? isUseConcatProp,
      bool? isAdd,
      List<LessonInfo>? lessonList});
}

/// @nodoc
class _$CalendarInfoCopyWithImpl<$Res, $Val extends CalendarInfo>
    implements $CalendarInfoCopyWith<$Res> {
  _$CalendarInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = freezed,
    Object? status = freezed,
    Object? frostEndTime = freezed,
    Object? isUseConcatProp = freezed,
    Object? isAdd = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_value.copyWith(
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      frostEndTime: freezed == frostEndTime
          ? _value.frostEndTime
          : frostEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isUseConcatProp: freezed == isUseConcatProp
          ? _value.isUseConcatProp
          : isUseConcatProp // ignore: cast_nullable_to_non_nullable
              as int?,
      isAdd: freezed == isAdd
          ? _value.isAdd
          : isAdd // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonList: freezed == lessonList
          ? _value.lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CalendarInfoCopyWith<$Res>
    implements $CalendarInfoCopyWith<$Res> {
  factory _$$_CalendarInfoCopyWith(
          _$_CalendarInfo value, $Res Function(_$_CalendarInfo) then) =
      __$$_CalendarInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? time,
      int? status,
      int? frostEndTime,
      int? isUseConcatProp,
      bool? isAdd,
      List<LessonInfo>? lessonList});
}

/// @nodoc
class __$$_CalendarInfoCopyWithImpl<$Res>
    extends _$CalendarInfoCopyWithImpl<$Res, _$_CalendarInfo>
    implements _$$_CalendarInfoCopyWith<$Res> {
  __$$_CalendarInfoCopyWithImpl(
      _$_CalendarInfo _value, $Res Function(_$_CalendarInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = freezed,
    Object? status = freezed,
    Object? frostEndTime = freezed,
    Object? isUseConcatProp = freezed,
    Object? isAdd = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_$_CalendarInfo(
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      frostEndTime: freezed == frostEndTime
          ? _value.frostEndTime
          : frostEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isUseConcatProp: freezed == isUseConcatProp
          ? _value.isUseConcatProp
          : isUseConcatProp // ignore: cast_nullable_to_non_nullable
              as int?,
      isAdd: freezed == isAdd
          ? _value.isAdd
          : isAdd // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonList: freezed == lessonList
          ? _value.lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CalendarInfo implements _CalendarInfo {
  _$_CalendarInfo(
      {this.time,
      this.status,
      this.frostEndTime,
      this.isUseConcatProp,
      this.isAdd,
      this.lessonList});

  factory _$_CalendarInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CalendarInfoFromJson(json);

  @override
  int? time;
// 排课时间戳(当天0点)
  @override
  int? status;
// 状态(0未解锁, 1待点亮, 2火鸡叫, 3冰冻叫, 4嗝屁叫)
  @override
  int? frostEndTime;
// 冰冻结束时间
  @override
  int? isUseConcatProp;
// 是否使用了连胜道具
  @override
  bool? isAdd;
// 是否已经添加过（用于组装数据，减少循环）
  @override
  List<LessonInfo>? lessonList;

  @override
  String toString() {
    return 'CalendarInfo(time: $time, status: $status, frostEndTime: $frostEndTime, isUseConcatProp: $isUseConcatProp, isAdd: $isAdd, lessonList: $lessonList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CalendarInfoCopyWith<_$_CalendarInfo> get copyWith =>
      __$$_CalendarInfoCopyWithImpl<_$_CalendarInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CalendarInfoToJson(
      this,
    );
  }
}

abstract class _CalendarInfo implements CalendarInfo {
  factory _CalendarInfo(
      {int? time,
      int? status,
      int? frostEndTime,
      int? isUseConcatProp,
      bool? isAdd,
      List<LessonInfo>? lessonList}) = _$_CalendarInfo;

  factory _CalendarInfo.fromJson(Map<String, dynamic> json) =
      _$_CalendarInfo.fromJson;

  @override
  int? get time;
  set time(int? value);
  @override // 排课时间戳(当天0点)
  int? get status; // 排课时间戳(当天0点)
  set status(int? value);
  @override // 状态(0未解锁, 1待点亮, 2火鸡叫, 3冰冻叫, 4嗝屁叫)
  int? get frostEndTime; // 状态(0未解锁, 1待点亮, 2火鸡叫, 3冰冻叫, 4嗝屁叫)
  set frostEndTime(int? value);
  @override // 冰冻结束时间
  int? get isUseConcatProp; // 冰冻结束时间
  set isUseConcatProp(int? value);
  @override // 是否使用了连胜道具
  bool? get isAdd; // 是否使用了连胜道具
  set isAdd(bool? value);
  @override // 是否已经添加过（用于组装数据，减少循环）
  List<LessonInfo>? get lessonList; // 是否已经添加过（用于组装数据，减少循环）
  set lessonList(List<LessonInfo>? value);
  @override
  @JsonKey(ignore: true)
  _$$_CalendarInfoCopyWith<_$_CalendarInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfo _$LessonInfoFromJson(Map<String, dynamic> json) {
  return _LessonInfo.fromJson(json);
}

/// @nodoc
mixin _$LessonInfo {
  List<int>? get usePropIds => throw _privateConstructorUsedError; // 使用道具id
  LessonInfo? get relationLesson =>
      throw _privateConstructorUsedError; // 关联课时信息
  int? get lessonId => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get lessonKey => throw _privateConstructorUsedError; // 课时key
  String? get lessonName => throw _privateConstructorUsedError; // 课时名称
  int? get lessonSort => throw _privateConstructorUsedError; // 课时序号
  String? get classKey => throw _privateConstructorUsedError; // 班期key
  String? get courseKey => throw _privateConstructorUsedError; // 班期key
  String? get courseSegmentName => throw _privateConstructorUsedError; // 阶段名称
  String? get courseTypeName => throw _privateConstructorUsedError; // 课程类型
  int? get classId => throw _privateConstructorUsedError; // 班级ID
  int? get unlockTime => throw _privateConstructorUsedError; // 课程解锁时间
  int? get isFinish => throw _privateConstructorUsedError; // 是否完成(1是0否)
  String? get router => throw _privateConstructorUsedError; // 课时页路由
  int? get segmentId => throw _privateConstructorUsedError; //主题id
  int? get weekId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoCopyWith<LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoCopyWith<$Res> {
  factory $LessonInfoCopyWith(
          LessonInfo value, $Res Function(LessonInfo) then) =
      _$LessonInfoCopyWithImpl<$Res, LessonInfo>;
  @useResult
  $Res call(
      {List<int>? usePropIds,
      LessonInfo? relationLesson,
      int? lessonId,
      int? status,
      String? lessonKey,
      String? lessonName,
      int? lessonSort,
      String? classKey,
      String? courseKey,
      String? courseSegmentName,
      String? courseTypeName,
      int? classId,
      int? unlockTime,
      int? isFinish,
      String? router,
      int? segmentId,
      int? weekId});

  $LessonInfoCopyWith<$Res>? get relationLesson;
}

/// @nodoc
class _$LessonInfoCopyWithImpl<$Res, $Val extends LessonInfo>
    implements $LessonInfoCopyWith<$Res> {
  _$LessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? usePropIds = freezed,
    Object? relationLesson = freezed,
    Object? lessonId = freezed,
    Object? status = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
    Object? lessonSort = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? courseSegmentName = freezed,
    Object? courseTypeName = freezed,
    Object? classId = freezed,
    Object? unlockTime = freezed,
    Object? isFinish = freezed,
    Object? router = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
  }) {
    return _then(_value.copyWith(
      usePropIds: freezed == usePropIds
          ? _value.usePropIds
          : usePropIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      relationLesson: freezed == relationLesson
          ? _value.relationLesson
          : relationLesson // ignore: cast_nullable_to_non_nullable
              as LessonInfo?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSort: freezed == lessonSort
          ? _value.lessonSort
          : lessonSort // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeName: freezed == courseTypeName
          ? _value.courseTypeName
          : courseTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockTime: freezed == unlockTime
          ? _value.unlockTime
          : unlockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LessonInfoCopyWith<$Res>? get relationLesson {
    if (_value.relationLesson == null) {
      return null;
    }

    return $LessonInfoCopyWith<$Res>(_value.relationLesson!, (value) {
      return _then(_value.copyWith(relationLesson: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LessonInfoCopyWith<$Res>
    implements $LessonInfoCopyWith<$Res> {
  factory _$$_LessonInfoCopyWith(
          _$_LessonInfo value, $Res Function(_$_LessonInfo) then) =
      __$$_LessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<int>? usePropIds,
      LessonInfo? relationLesson,
      int? lessonId,
      int? status,
      String? lessonKey,
      String? lessonName,
      int? lessonSort,
      String? classKey,
      String? courseKey,
      String? courseSegmentName,
      String? courseTypeName,
      int? classId,
      int? unlockTime,
      int? isFinish,
      String? router,
      int? segmentId,
      int? weekId});

  @override
  $LessonInfoCopyWith<$Res>? get relationLesson;
}

/// @nodoc
class __$$_LessonInfoCopyWithImpl<$Res>
    extends _$LessonInfoCopyWithImpl<$Res, _$_LessonInfo>
    implements _$$_LessonInfoCopyWith<$Res> {
  __$$_LessonInfoCopyWithImpl(
      _$_LessonInfo _value, $Res Function(_$_LessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? usePropIds = freezed,
    Object? relationLesson = freezed,
    Object? lessonId = freezed,
    Object? status = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
    Object? lessonSort = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? courseSegmentName = freezed,
    Object? courseTypeName = freezed,
    Object? classId = freezed,
    Object? unlockTime = freezed,
    Object? isFinish = freezed,
    Object? router = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
  }) {
    return _then(_$_LessonInfo(
      usePropIds: freezed == usePropIds
          ? _value._usePropIds
          : usePropIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      relationLesson: freezed == relationLesson
          ? _value.relationLesson
          : relationLesson // ignore: cast_nullable_to_non_nullable
              as LessonInfo?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSort: freezed == lessonSort
          ? _value.lessonSort
          : lessonSort // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeName: freezed == courseTypeName
          ? _value.courseTypeName
          : courseTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockTime: freezed == unlockTime
          ? _value.unlockTime
          : unlockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfo implements _LessonInfo {
  _$_LessonInfo(
      {final List<int>? usePropIds,
      this.relationLesson,
      this.lessonId,
      this.status,
      this.lessonKey,
      this.lessonName,
      this.lessonSort,
      this.classKey,
      this.courseKey,
      this.courseSegmentName,
      this.courseTypeName,
      this.classId,
      this.unlockTime,
      this.isFinish,
      this.router,
      this.segmentId,
      this.weekId})
      : _usePropIds = usePropIds;

  factory _$_LessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoFromJson(json);

  final List<int>? _usePropIds;
  @override
  List<int>? get usePropIds {
    final value = _usePropIds;
    if (value == null) return null;
    if (_usePropIds is EqualUnmodifiableListView) return _usePropIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 使用道具id
  @override
  final LessonInfo? relationLesson;
// 关联课时信息
  @override
  final int? lessonId;
  @override
  final int? status;
  @override
  final String? lessonKey;
// 课时key
  @override
  final String? lessonName;
// 课时名称
  @override
  final int? lessonSort;
// 课时序号
  @override
  final String? classKey;
// 班期key
  @override
  final String? courseKey;
// 班期key
  @override
  final String? courseSegmentName;
// 阶段名称
  @override
  final String? courseTypeName;
// 课程类型
  @override
  final int? classId;
// 班级ID
  @override
  final int? unlockTime;
// 课程解锁时间
  @override
  final int? isFinish;
// 是否完成(1是0否)
  @override
  final String? router;
// 课时页路由
  @override
  final int? segmentId;
//主题id
  @override
  final int? weekId;

  @override
  String toString() {
    return 'LessonInfo(usePropIds: $usePropIds, relationLesson: $relationLesson, lessonId: $lessonId, status: $status, lessonKey: $lessonKey, lessonName: $lessonName, lessonSort: $lessonSort, classKey: $classKey, courseKey: $courseKey, courseSegmentName: $courseSegmentName, courseTypeName: $courseTypeName, classId: $classId, unlockTime: $unlockTime, isFinish: $isFinish, router: $router, segmentId: $segmentId, weekId: $weekId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonInfo &&
            const DeepCollectionEquality()
                .equals(other._usePropIds, _usePropIds) &&
            (identical(other.relationLesson, relationLesson) ||
                other.relationLesson == relationLesson) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lessonKey, lessonKey) ||
                other.lessonKey == lessonKey) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonSort, lessonSort) ||
                other.lessonSort == lessonSort) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.courseTypeName, courseTypeName) ||
                other.courseTypeName == courseTypeName) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.unlockTime, unlockTime) ||
                other.unlockTime == unlockTime) &&
            (identical(other.isFinish, isFinish) ||
                other.isFinish == isFinish) &&
            (identical(other.router, router) || other.router == router) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_usePropIds),
      relationLesson,
      lessonId,
      status,
      lessonKey,
      lessonName,
      lessonSort,
      classKey,
      courseKey,
      courseSegmentName,
      courseTypeName,
      classId,
      unlockTime,
      isFinish,
      router,
      segmentId,
      weekId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      __$$_LessonInfoCopyWithImpl<_$_LessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoToJson(
      this,
    );
  }
}

abstract class _LessonInfo implements LessonInfo {
  factory _LessonInfo(
      {final List<int>? usePropIds,
      final LessonInfo? relationLesson,
      final int? lessonId,
      final int? status,
      final String? lessonKey,
      final String? lessonName,
      final int? lessonSort,
      final String? classKey,
      final String? courseKey,
      final String? courseSegmentName,
      final String? courseTypeName,
      final int? classId,
      final int? unlockTime,
      final int? isFinish,
      final String? router,
      final int? segmentId,
      final int? weekId}) = _$_LessonInfo;

  factory _LessonInfo.fromJson(Map<String, dynamic> json) =
      _$_LessonInfo.fromJson;

  @override
  List<int>? get usePropIds;
  @override // 使用道具id
  LessonInfo? get relationLesson;
  @override // 关联课时信息
  int? get lessonId;
  @override
  int? get status;
  @override
  String? get lessonKey;
  @override // 课时key
  String? get lessonName;
  @override // 课时名称
  int? get lessonSort;
  @override // 课时序号
  String? get classKey;
  @override // 班期key
  String? get courseKey;
  @override // 班期key
  String? get courseSegmentName;
  @override // 阶段名称
  String? get courseTypeName;
  @override // 课程类型
  int? get classId;
  @override // 班级ID
  int? get unlockTime;
  @override // 课程解锁时间
  int? get isFinish;
  @override // 是否完成(1是0否)
  String? get router;
  @override // 课时页路由
  int? get segmentId;
  @override //主题id
  int? get weekId;
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

GuideResource _$GuideResourceFromJson(Map<String, dynamic> json) {
  return _GuideResource.fromJson(json);
}

/// @nodoc
mixin _$GuideResource {
  int? get scene => throw _privateConstructorUsedError; // 场景值
  String? get audio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GuideResourceCopyWith<GuideResource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GuideResourceCopyWith<$Res> {
  factory $GuideResourceCopyWith(
          GuideResource value, $Res Function(GuideResource) then) =
      _$GuideResourceCopyWithImpl<$Res, GuideResource>;
  @useResult
  $Res call({int? scene, String? audio});
}

/// @nodoc
class _$GuideResourceCopyWithImpl<$Res, $Val extends GuideResource>
    implements $GuideResourceCopyWith<$Res> {
  _$GuideResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? audio = freezed,
  }) {
    return _then(_value.copyWith(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as int?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GuideResourceCopyWith<$Res>
    implements $GuideResourceCopyWith<$Res> {
  factory _$$_GuideResourceCopyWith(
          _$_GuideResource value, $Res Function(_$_GuideResource) then) =
      __$$_GuideResourceCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? scene, String? audio});
}

/// @nodoc
class __$$_GuideResourceCopyWithImpl<$Res>
    extends _$GuideResourceCopyWithImpl<$Res, _$_GuideResource>
    implements _$$_GuideResourceCopyWith<$Res> {
  __$$_GuideResourceCopyWithImpl(
      _$_GuideResource _value, $Res Function(_$_GuideResource) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? audio = freezed,
  }) {
    return _then(_$_GuideResource(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as int?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GuideResource implements _GuideResource {
  _$_GuideResource({this.scene, this.audio});

  factory _$_GuideResource.fromJson(Map<String, dynamic> json) =>
      _$$_GuideResourceFromJson(json);

  @override
  final int? scene;
// 场景值
  @override
  final String? audio;

  @override
  String toString() {
    return 'GuideResource(scene: $scene, audio: $audio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GuideResource &&
            (identical(other.scene, scene) || other.scene == scene) &&
            (identical(other.audio, audio) || other.audio == audio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, scene, audio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GuideResourceCopyWith<_$_GuideResource> get copyWith =>
      __$$_GuideResourceCopyWithImpl<_$_GuideResource>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GuideResourceToJson(
      this,
    );
  }
}

abstract class _GuideResource implements GuideResource {
  factory _GuideResource({final int? scene, final String? audio}) =
      _$_GuideResource;

  factory _GuideResource.fromJson(Map<String, dynamic> json) =
      _$_GuideResource.fromJson;

  @override
  int? get scene;
  @override // 场景值
  String? get audio;
  @override
  @JsonKey(ignore: true)
  _$$_GuideResourceCopyWith<_$_GuideResource> get copyWith =>
      throw _privateConstructorUsedError;
}
