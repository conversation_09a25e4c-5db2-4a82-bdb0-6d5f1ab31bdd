// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jojo_continuology_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

JoJoContinuologyHeaderDetailDate _$JoJoContinuologyHeaderDetailDateFromJson(
    Map<String, dynamic> json) {
  return _JoJoContinuologyHeaderDetailDate.fromJson(json);
}

/// @nodoc
mixin _$JoJoContinuologyHeaderDetailDate {
  int? get status => throw _privateConstructorUsedError;

  ///状态 0 初始化 1 连续学习 2 连续学习中断
  String? get clientDynamic => throw _privateConstructorUsedError;
  String? get spineResource => throw _privateConstructorUsedError;

  ///spine 动效
  int? get consecutiveDays => throw _privateConstructorUsedError;

  ///连续天数
  String? get consecutiveUnit => throw _privateConstructorUsedError;

  ///连胜单位
  String? get consecutiveTitle => throw _privateConstructorUsedError;

  ///连胜标题,状态是初始化时 返回初始标题 连续坚持学习
  String? get consecutiveBest => throw _privateConstructorUsedError;

  ///连胜最佳  训练营无年课不返回
  String? get consecutiveBestDay => throw _privateConstructorUsedError;

  ///连胜最佳时长 训练营无年课不返回
  String? get initialTitle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $JoJoContinuologyHeaderDetailDateCopyWith<JoJoContinuologyHeaderDetailDate>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JoJoContinuologyHeaderDetailDateCopyWith<$Res> {
  factory $JoJoContinuologyHeaderDetailDateCopyWith(
          JoJoContinuologyHeaderDetailDate value,
          $Res Function(JoJoContinuologyHeaderDetailDate) then) =
      _$JoJoContinuologyHeaderDetailDateCopyWithImpl<$Res,
          JoJoContinuologyHeaderDetailDate>;
  @useResult
  $Res call(
      {int? status,
      String? clientDynamic,
      String? spineResource,
      int? consecutiveDays,
      String? consecutiveUnit,
      String? consecutiveTitle,
      String? consecutiveBest,
      String? consecutiveBestDay,
      String? initialTitle});
}

/// @nodoc
class _$JoJoContinuologyHeaderDetailDateCopyWithImpl<$Res,
        $Val extends JoJoContinuologyHeaderDetailDate>
    implements $JoJoContinuologyHeaderDetailDateCopyWith<$Res> {
  _$JoJoContinuologyHeaderDetailDateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? clientDynamic = freezed,
    Object? spineResource = freezed,
    Object? consecutiveDays = freezed,
    Object? consecutiveUnit = freezed,
    Object? consecutiveTitle = freezed,
    Object? consecutiveBest = freezed,
    Object? consecutiveBestDay = freezed,
    Object? initialTitle = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      spineResource: freezed == spineResource
          ? _value.spineResource
          : spineResource // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveDays: freezed == consecutiveDays
          ? _value.consecutiveDays
          : consecutiveDays // ignore: cast_nullable_to_non_nullable
              as int?,
      consecutiveUnit: freezed == consecutiveUnit
          ? _value.consecutiveUnit
          : consecutiveUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveTitle: freezed == consecutiveTitle
          ? _value.consecutiveTitle
          : consecutiveTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveBest: freezed == consecutiveBest
          ? _value.consecutiveBest
          : consecutiveBest // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveBestDay: freezed == consecutiveBestDay
          ? _value.consecutiveBestDay
          : consecutiveBestDay // ignore: cast_nullable_to_non_nullable
              as String?,
      initialTitle: freezed == initialTitle
          ? _value.initialTitle
          : initialTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_JoJoContinuologyHeaderDetailDateCopyWith<$Res>
    implements $JoJoContinuologyHeaderDetailDateCopyWith<$Res> {
  factory _$$_JoJoContinuologyHeaderDetailDateCopyWith(
          _$_JoJoContinuologyHeaderDetailDate value,
          $Res Function(_$_JoJoContinuologyHeaderDetailDate) then) =
      __$$_JoJoContinuologyHeaderDetailDateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? status,
      String? clientDynamic,
      String? spineResource,
      int? consecutiveDays,
      String? consecutiveUnit,
      String? consecutiveTitle,
      String? consecutiveBest,
      String? consecutiveBestDay,
      String? initialTitle});
}

/// @nodoc
class __$$_JoJoContinuologyHeaderDetailDateCopyWithImpl<$Res>
    extends _$JoJoContinuologyHeaderDetailDateCopyWithImpl<$Res,
        _$_JoJoContinuologyHeaderDetailDate>
    implements _$$_JoJoContinuologyHeaderDetailDateCopyWith<$Res> {
  __$$_JoJoContinuologyHeaderDetailDateCopyWithImpl(
      _$_JoJoContinuologyHeaderDetailDate _value,
      $Res Function(_$_JoJoContinuologyHeaderDetailDate) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? clientDynamic = freezed,
    Object? spineResource = freezed,
    Object? consecutiveDays = freezed,
    Object? consecutiveUnit = freezed,
    Object? consecutiveTitle = freezed,
    Object? consecutiveBest = freezed,
    Object? consecutiveBestDay = freezed,
    Object? initialTitle = freezed,
  }) {
    return _then(_$_JoJoContinuologyHeaderDetailDate(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      spineResource: freezed == spineResource
          ? _value.spineResource
          : spineResource // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveDays: freezed == consecutiveDays
          ? _value.consecutiveDays
          : consecutiveDays // ignore: cast_nullable_to_non_nullable
              as int?,
      consecutiveUnit: freezed == consecutiveUnit
          ? _value.consecutiveUnit
          : consecutiveUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveTitle: freezed == consecutiveTitle
          ? _value.consecutiveTitle
          : consecutiveTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveBest: freezed == consecutiveBest
          ? _value.consecutiveBest
          : consecutiveBest // ignore: cast_nullable_to_non_nullable
              as String?,
      consecutiveBestDay: freezed == consecutiveBestDay
          ? _value.consecutiveBestDay
          : consecutiveBestDay // ignore: cast_nullable_to_non_nullable
              as String?,
      initialTitle: freezed == initialTitle
          ? _value.initialTitle
          : initialTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_JoJoContinuologyHeaderDetailDate
    implements _JoJoContinuologyHeaderDetailDate {
  _$_JoJoContinuologyHeaderDetailDate(
      {this.status,
      this.clientDynamic,
      this.spineResource,
      this.consecutiveDays,
      this.consecutiveUnit,
      this.consecutiveTitle,
      this.consecutiveBest,
      this.consecutiveBestDay,
      this.initialTitle});

  factory _$_JoJoContinuologyHeaderDetailDate.fromJson(
          Map<String, dynamic> json) =>
      _$$_JoJoContinuologyHeaderDetailDateFromJson(json);

  @override
  final int? status;

  ///状态 0 初始化 1 连续学习 2 连续学习中断
  @override
  final String? clientDynamic;
  @override
  final String? spineResource;

  ///spine 动效
  @override
  final int? consecutiveDays;

  ///连续天数
  @override
  final String? consecutiveUnit;

  ///连胜单位
  @override
  final String? consecutiveTitle;

  ///连胜标题,状态是初始化时 返回初始标题 连续坚持学习
  @override
  final String? consecutiveBest;

  ///连胜最佳  训练营无年课不返回
  @override
  final String? consecutiveBestDay;

  ///连胜最佳时长 训练营无年课不返回
  @override
  final String? initialTitle;

  @override
  String toString() {
    return 'JoJoContinuologyHeaderDetailDate(status: $status, clientDynamic: $clientDynamic, spineResource: $spineResource, consecutiveDays: $consecutiveDays, consecutiveUnit: $consecutiveUnit, consecutiveTitle: $consecutiveTitle, consecutiveBest: $consecutiveBest, consecutiveBestDay: $consecutiveBestDay, initialTitle: $initialTitle)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_JoJoContinuologyHeaderDetailDate &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.clientDynamic, clientDynamic) ||
                other.clientDynamic == clientDynamic) &&
            (identical(other.spineResource, spineResource) ||
                other.spineResource == spineResource) &&
            (identical(other.consecutiveDays, consecutiveDays) ||
                other.consecutiveDays == consecutiveDays) &&
            (identical(other.consecutiveUnit, consecutiveUnit) ||
                other.consecutiveUnit == consecutiveUnit) &&
            (identical(other.consecutiveTitle, consecutiveTitle) ||
                other.consecutiveTitle == consecutiveTitle) &&
            (identical(other.consecutiveBest, consecutiveBest) ||
                other.consecutiveBest == consecutiveBest) &&
            (identical(other.consecutiveBestDay, consecutiveBestDay) ||
                other.consecutiveBestDay == consecutiveBestDay) &&
            (identical(other.initialTitle, initialTitle) ||
                other.initialTitle == initialTitle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      status,
      clientDynamic,
      spineResource,
      consecutiveDays,
      consecutiveUnit,
      consecutiveTitle,
      consecutiveBest,
      consecutiveBestDay,
      initialTitle);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_JoJoContinuologyHeaderDetailDateCopyWith<
          _$_JoJoContinuologyHeaderDetailDate>
      get copyWith => __$$_JoJoContinuologyHeaderDetailDateCopyWithImpl<
          _$_JoJoContinuologyHeaderDetailDate>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_JoJoContinuologyHeaderDetailDateToJson(
      this,
    );
  }
}

abstract class _JoJoContinuologyHeaderDetailDate
    implements JoJoContinuologyHeaderDetailDate {
  factory _JoJoContinuologyHeaderDetailDate(
      {final int? status,
      final String? clientDynamic,
      final String? spineResource,
      final int? consecutiveDays,
      final String? consecutiveUnit,
      final String? consecutiveTitle,
      final String? consecutiveBest,
      final String? consecutiveBestDay,
      final String? initialTitle}) = _$_JoJoContinuologyHeaderDetailDate;

  factory _JoJoContinuologyHeaderDetailDate.fromJson(
      Map<String, dynamic> json) = _$_JoJoContinuologyHeaderDetailDate.fromJson;

  @override
  int? get status;
  @override

  ///状态 0 初始化 1 连续学习 2 连续学习中断
  String? get clientDynamic;
  @override
  String? get spineResource;
  @override

  ///spine 动效
  int? get consecutiveDays;
  @override

  ///连续天数
  String? get consecutiveUnit;
  @override

  ///连胜单位
  String? get consecutiveTitle;
  @override

  ///连胜标题,状态是初始化时 返回初始标题 连续坚持学习
  String? get consecutiveBest;
  @override

  ///连胜最佳  训练营无年课不返回
  String? get consecutiveBestDay;
  @override

  ///连胜最佳时长 训练营无年课不返回
  String? get initialTitle;
  @override
  @JsonKey(ignore: true)
  _$$_JoJoContinuologyHeaderDetailDateCopyWith<
          _$_JoJoContinuologyHeaderDetailDate>
      get copyWith => throw _privateConstructorUsedError;
}

JoJoContinuologyHeaderSpineDate _$JoJoContinuologyHeaderSpineDateFromJson(
    Map<String, dynamic> json) {
  return _JoJoContinuologyHeaderSpineDate.fromJson(json);
}

/// @nodoc
mixin _$JoJoContinuologyHeaderSpineDate {
  String? get atlasFile => throw _privateConstructorUsedError;
  set atlasFile(String? value) => throw _privateConstructorUsedError;

  ///spine 动效
  String? get skelFile => throw _privateConstructorUsedError;

  ///spine 动效
  set skelFile(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $JoJoContinuologyHeaderSpineDateCopyWith<JoJoContinuologyHeaderSpineDate>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JoJoContinuologyHeaderSpineDateCopyWith<$Res> {
  factory $JoJoContinuologyHeaderSpineDateCopyWith(
          JoJoContinuologyHeaderSpineDate value,
          $Res Function(JoJoContinuologyHeaderSpineDate) then) =
      _$JoJoContinuologyHeaderSpineDateCopyWithImpl<$Res,
          JoJoContinuologyHeaderSpineDate>;
  @useResult
  $Res call({String? atlasFile, String? skelFile});
}

/// @nodoc
class _$JoJoContinuologyHeaderSpineDateCopyWithImpl<$Res,
        $Val extends JoJoContinuologyHeaderSpineDate>
    implements $JoJoContinuologyHeaderSpineDateCopyWith<$Res> {
  _$JoJoContinuologyHeaderSpineDateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? atlasFile = freezed,
    Object? skelFile = freezed,
  }) {
    return _then(_value.copyWith(
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
      skelFile: freezed == skelFile
          ? _value.skelFile
          : skelFile // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_JoJoContinuologyHeaderSpineDateCopyWith<$Res>
    implements $JoJoContinuologyHeaderSpineDateCopyWith<$Res> {
  factory _$$_JoJoContinuologyHeaderSpineDateCopyWith(
          _$_JoJoContinuologyHeaderSpineDate value,
          $Res Function(_$_JoJoContinuologyHeaderSpineDate) then) =
      __$$_JoJoContinuologyHeaderSpineDateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? atlasFile, String? skelFile});
}

/// @nodoc
class __$$_JoJoContinuologyHeaderSpineDateCopyWithImpl<$Res>
    extends _$JoJoContinuologyHeaderSpineDateCopyWithImpl<$Res,
        _$_JoJoContinuologyHeaderSpineDate>
    implements _$$_JoJoContinuologyHeaderSpineDateCopyWith<$Res> {
  __$$_JoJoContinuologyHeaderSpineDateCopyWithImpl(
      _$_JoJoContinuologyHeaderSpineDate _value,
      $Res Function(_$_JoJoContinuologyHeaderSpineDate) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? atlasFile = freezed,
    Object? skelFile = freezed,
  }) {
    return _then(_$_JoJoContinuologyHeaderSpineDate(
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
      skelFile: freezed == skelFile
          ? _value.skelFile
          : skelFile // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_JoJoContinuologyHeaderSpineDate
    implements _JoJoContinuologyHeaderSpineDate {
  _$_JoJoContinuologyHeaderSpineDate({this.atlasFile, this.skelFile});

  factory _$_JoJoContinuologyHeaderSpineDate.fromJson(
          Map<String, dynamic> json) =>
      _$$_JoJoContinuologyHeaderSpineDateFromJson(json);

  @override
  String? atlasFile;

  ///spine 动效
  @override
  String? skelFile;

  @override
  String toString() {
    return 'JoJoContinuologyHeaderSpineDate(atlasFile: $atlasFile, skelFile: $skelFile)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_JoJoContinuologyHeaderSpineDateCopyWith<
          _$_JoJoContinuologyHeaderSpineDate>
      get copyWith => __$$_JoJoContinuologyHeaderSpineDateCopyWithImpl<
          _$_JoJoContinuologyHeaderSpineDate>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_JoJoContinuologyHeaderSpineDateToJson(
      this,
    );
  }
}

abstract class _JoJoContinuologyHeaderSpineDate
    implements JoJoContinuologyHeaderSpineDate {
  factory _JoJoContinuologyHeaderSpineDate(
      {String? atlasFile,
      String? skelFile}) = _$_JoJoContinuologyHeaderSpineDate;

  factory _JoJoContinuologyHeaderSpineDate.fromJson(Map<String, dynamic> json) =
      _$_JoJoContinuologyHeaderSpineDate.fromJson;

  @override
  String? get atlasFile;
  set atlasFile(String? value);
  @override

  ///spine 动效
  String? get skelFile;

  ///spine 动效
  set skelFile(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_JoJoContinuologyHeaderSpineDateCopyWith<
          _$_JoJoContinuologyHeaderSpineDate>
      get copyWith => throw _privateConstructorUsedError;
}

JoJoContinuologyTargetData _$JoJoContinuologyTargetDataFromJson(
    Map<String, dynamic> json) {
  return _JoJoContinuologyTargetData.fromJson(json);
}

/// @nodoc
mixin _$JoJoContinuologyTargetData {
  int? get scene => throw _privateConstructorUsedError;
  set scene(int? value) => throw _privateConstructorUsedError;
  String? get propId => throw _privateConstructorUsedError;
  set propId(String? value) => throw _privateConstructorUsedError;
  JoJoContinuologyTargetDetail? get target =>
      throw _privateConstructorUsedError;
  set target(JoJoContinuologyTargetDetail? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $JoJoContinuologyTargetDataCopyWith<JoJoContinuologyTargetData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JoJoContinuologyTargetDataCopyWith<$Res> {
  factory $JoJoContinuologyTargetDataCopyWith(JoJoContinuologyTargetData value,
          $Res Function(JoJoContinuologyTargetData) then) =
      _$JoJoContinuologyTargetDataCopyWithImpl<$Res,
          JoJoContinuologyTargetData>;
  @useResult
  $Res call({int? scene, String? propId, JoJoContinuologyTargetDetail? target});

  $JoJoContinuologyTargetDetailCopyWith<$Res>? get target;
}

/// @nodoc
class _$JoJoContinuologyTargetDataCopyWithImpl<$Res,
        $Val extends JoJoContinuologyTargetData>
    implements $JoJoContinuologyTargetDataCopyWith<$Res> {
  _$JoJoContinuologyTargetDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? propId = freezed,
    Object? target = freezed,
  }) {
    return _then(_value.copyWith(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as int?,
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as JoJoContinuologyTargetDetail?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $JoJoContinuologyTargetDetailCopyWith<$Res>? get target {
    if (_value.target == null) {
      return null;
    }

    return $JoJoContinuologyTargetDetailCopyWith<$Res>(_value.target!, (value) {
      return _then(_value.copyWith(target: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_JoJoContinuologyTargetDataCopyWith<$Res>
    implements $JoJoContinuologyTargetDataCopyWith<$Res> {
  factory _$$_JoJoContinuologyTargetDataCopyWith(
          _$_JoJoContinuologyTargetData value,
          $Res Function(_$_JoJoContinuologyTargetData) then) =
      __$$_JoJoContinuologyTargetDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? scene, String? propId, JoJoContinuologyTargetDetail? target});

  @override
  $JoJoContinuologyTargetDetailCopyWith<$Res>? get target;
}

/// @nodoc
class __$$_JoJoContinuologyTargetDataCopyWithImpl<$Res>
    extends _$JoJoContinuologyTargetDataCopyWithImpl<$Res,
        _$_JoJoContinuologyTargetData>
    implements _$$_JoJoContinuologyTargetDataCopyWith<$Res> {
  __$$_JoJoContinuologyTargetDataCopyWithImpl(
      _$_JoJoContinuologyTargetData _value,
      $Res Function(_$_JoJoContinuologyTargetData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? propId = freezed,
    Object? target = freezed,
  }) {
    return _then(_$_JoJoContinuologyTargetData(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as int?,
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      target: freezed == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as JoJoContinuologyTargetDetail?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_JoJoContinuologyTargetData implements _JoJoContinuologyTargetData {
  _$_JoJoContinuologyTargetData({this.scene, this.propId, this.target});

  factory _$_JoJoContinuologyTargetData.fromJson(Map<String, dynamic> json) =>
      _$$_JoJoContinuologyTargetDataFromJson(json);

  @override
  int? scene;
  @override
  String? propId;
  @override
  JoJoContinuologyTargetDetail? target;

  @override
  String toString() {
    return 'JoJoContinuologyTargetData(scene: $scene, propId: $propId, target: $target)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_JoJoContinuologyTargetDataCopyWith<_$_JoJoContinuologyTargetData>
      get copyWith => __$$_JoJoContinuologyTargetDataCopyWithImpl<
          _$_JoJoContinuologyTargetData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_JoJoContinuologyTargetDataToJson(
      this,
    );
  }
}

abstract class _JoJoContinuologyTargetData
    implements JoJoContinuologyTargetData {
  factory _JoJoContinuologyTargetData(
      {int? scene,
      String? propId,
      JoJoContinuologyTargetDetail? target}) = _$_JoJoContinuologyTargetData;

  factory _JoJoContinuologyTargetData.fromJson(Map<String, dynamic> json) =
      _$_JoJoContinuologyTargetData.fromJson;

  @override
  int? get scene;
  set scene(int? value);
  @override
  String? get propId;
  set propId(String? value);
  @override
  JoJoContinuologyTargetDetail? get target;
  set target(JoJoContinuologyTargetDetail? value);
  @override
  @JsonKey(ignore: true)
  _$$_JoJoContinuologyTargetDataCopyWith<_$_JoJoContinuologyTargetData>
      get copyWith => throw _privateConstructorUsedError;
}

JoJoContinuologyTargetDetail _$JoJoContinuologyTargetDetailFromJson(
    Map<String, dynamic> json) {
  return _JoJoContinuologyTargetDetail.fromJson(json);
}

/// @nodoc
mixin _$JoJoContinuologyTargetDetail {
  String? get classKey => throw _privateConstructorUsedError;
  set classKey(String? value) => throw _privateConstructorUsedError;
  String? get targetId => throw _privateConstructorUsedError;
  set targetId(String? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  set type(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $JoJoContinuologyTargetDetailCopyWith<JoJoContinuologyTargetDetail>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JoJoContinuologyTargetDetailCopyWith<$Res> {
  factory $JoJoContinuologyTargetDetailCopyWith(
          JoJoContinuologyTargetDetail value,
          $Res Function(JoJoContinuologyTargetDetail) then) =
      _$JoJoContinuologyTargetDetailCopyWithImpl<$Res,
          JoJoContinuologyTargetDetail>;
  @useResult
  $Res call(
      {String? classKey, String? targetId, String? courseKey, String? type});
}

/// @nodoc
class _$JoJoContinuologyTargetDetailCopyWithImpl<$Res,
        $Val extends JoJoContinuologyTargetDetail>
    implements $JoJoContinuologyTargetDetailCopyWith<$Res> {
  _$JoJoContinuologyTargetDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? targetId = freezed,
    Object? courseKey = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_JoJoContinuologyTargetDetailCopyWith<$Res>
    implements $JoJoContinuologyTargetDetailCopyWith<$Res> {
  factory _$$_JoJoContinuologyTargetDetailCopyWith(
          _$_JoJoContinuologyTargetDetail value,
          $Res Function(_$_JoJoContinuologyTargetDetail) then) =
      __$$_JoJoContinuologyTargetDetailCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? classKey, String? targetId, String? courseKey, String? type});
}

/// @nodoc
class __$$_JoJoContinuologyTargetDetailCopyWithImpl<$Res>
    extends _$JoJoContinuologyTargetDetailCopyWithImpl<$Res,
        _$_JoJoContinuologyTargetDetail>
    implements _$$_JoJoContinuologyTargetDetailCopyWith<$Res> {
  __$$_JoJoContinuologyTargetDetailCopyWithImpl(
      _$_JoJoContinuologyTargetDetail _value,
      $Res Function(_$_JoJoContinuologyTargetDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? targetId = freezed,
    Object? courseKey = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_JoJoContinuologyTargetDetail(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_JoJoContinuologyTargetDetail implements _JoJoContinuologyTargetDetail {
  _$_JoJoContinuologyTargetDetail(
      {this.classKey, this.targetId, this.courseKey, this.type});

  factory _$_JoJoContinuologyTargetDetail.fromJson(Map<String, dynamic> json) =>
      _$$_JoJoContinuologyTargetDetailFromJson(json);

  @override
  String? classKey;
  @override
  String? targetId;
  @override
  String? courseKey;
  @override
  String? type;

  @override
  String toString() {
    return 'JoJoContinuologyTargetDetail(classKey: $classKey, targetId: $targetId, courseKey: $courseKey, type: $type)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_JoJoContinuologyTargetDetailCopyWith<_$_JoJoContinuologyTargetDetail>
      get copyWith => __$$_JoJoContinuologyTargetDetailCopyWithImpl<
          _$_JoJoContinuologyTargetDetail>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_JoJoContinuologyTargetDetailToJson(
      this,
    );
  }
}

abstract class _JoJoContinuologyTargetDetail
    implements JoJoContinuologyTargetDetail {
  factory _JoJoContinuologyTargetDetail(
      {String? classKey,
      String? targetId,
      String? courseKey,
      String? type}) = _$_JoJoContinuologyTargetDetail;

  factory _JoJoContinuologyTargetDetail.fromJson(Map<String, dynamic> json) =
      _$_JoJoContinuologyTargetDetail.fromJson;

  @override
  String? get classKey;
  set classKey(String? value);
  @override
  String? get targetId;
  set targetId(String? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override
  String? get type;
  set type(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_JoJoContinuologyTargetDetailCopyWith<_$_JoJoContinuologyTargetDetail>
      get copyWith => throw _privateConstructorUsedError;
}
