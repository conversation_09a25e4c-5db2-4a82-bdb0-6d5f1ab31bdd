// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jojo_continuology_api_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ContinuologyDataModel _$$_ContinuologyDataModelFromJson(
        Map<String, dynamic> json) =>
    _$_ContinuologyDataModel(
      subjectColor: json['subjectColor'] as String?,
      subjectName: json['subjectName'] as String?,
      ruleDescRoute: json['ruleDescRoute'] as String?,
      onlyTrainingCamp: json['onlyTrainingCamp'] as int?,
      continuous: json['continuous'] == null
          ? null
          : ContinuologyInfo.fromJson(
              json['continuous'] as Map<String, dynamic>),
      milestone: json['milestone'] == null
          ? null
          : Milestone.fromJson(json['milestone'] as Map<String, dynamic>),
      guideStatus: json['guideStatus'] as int? ?? -1,
      guideResourceList: (json['guideResourceList'] as List<dynamic>?)
          ?.map((e) => GuideResource.fromJson(e as Map<String, dynamic>))
          .toList(),
      prop: json['prop'] == null
          ? null
          : PropInfo.fromJson(json['prop'] as Map<String, dynamic>),
      calendar: (json['calendar'] as List<dynamic>?)
          ?.map((e) => CalendarInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ContinuologyDataModelToJson(
        _$_ContinuologyDataModel instance) =>
    <String, dynamic>{
      'subjectColor': instance.subjectColor,
      'subjectName': instance.subjectName,
      'ruleDescRoute': instance.ruleDescRoute,
      'onlyTrainingCamp': instance.onlyTrainingCamp,
      'continuous': instance.continuous,
      'milestone': instance.milestone,
      'guideStatus': instance.guideStatus,
      'guideResourceList': instance.guideResourceList,
      'prop': instance.prop,
      'calendar': instance.calendar,
    };

_$_ContinuologyInfo _$$_ContinuologyInfoFromJson(Map<String, dynamic> json) =>
    _$_ContinuologyInfo(
      days: json['days'] as int?,
      status: json['status'] as int?,
      resource: json['resource'] as String?,
      clientDynamic: json['clientDynamic'] as String?,
      bestInHistory: json['bestInHistory'] as int?,
    );

Map<String, dynamic> _$$_ContinuologyInfoToJson(_$_ContinuologyInfo instance) =>
    <String, dynamic>{
      'days': instance.days,
      'status': instance.status,
      'resource': instance.resource,
      'clientDynamic': instance.clientDynamic,
      'bestInHistory': instance.bestInHistory,
    };

_$_Milestone _$$_MilestoneFromJson(Map<String, dynamic> json) => _$_Milestone(
      title: json['title'] as String?,
      buttonText: json['buttonText'] as String?,
      buttonRoute: json['buttonRoute'] as String?,
      targetList: (json['targetList'] as List<dynamic>?)
          ?.map((e) => TargetList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MilestoneToJson(_$_Milestone instance) =>
    <String, dynamic>{
      'title': instance.title,
      'buttonText': instance.buttonText,
      'buttonRoute': instance.buttonRoute,
      'targetList': instance.targetList,
    };

_$_TargetList _$$_TargetListFromJson(Map<String, dynamic> json) =>
    _$_TargetList(
      value: json['value'] as int?,
    );

Map<String, dynamic> _$$_TargetListToJson(_$_TargetList instance) =>
    <String, dynamic>{
      'value': instance.value,
    };

_$_PropInfo _$$_PropInfoFromJson(Map<String, dynamic> json) => _$_PropInfo(
      title: json['title'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ItemInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PropInfoToJson(_$_PropInfo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'jumpRoute': instance.jumpRoute,
      'items': instance.items,
    };

_$_ItemInfo _$$_ItemInfoFromJson(Map<String, dynamic> json) => _$_ItemInfo(
      id: json['id'] as int?,
      type: json['type'] as int?,
      number: json['number'] as int?,
      isFree: json['isFree'] as int?,
    );

Map<String, dynamic> _$$_ItemInfoToJson(_$_ItemInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'number': instance.number,
      'isFree': instance.isFree,
    };

_$_CalendarInfo _$$_CalendarInfoFromJson(Map<String, dynamic> json) =>
    _$_CalendarInfo(
      time: json['time'] as int?,
      status: json['status'] as int?,
      frostEndTime: json['frostEndTime'] as int?,
      isUseConcatProp: json['isUseConcatProp'] as int?,
      isAdd: json['isAdd'] as bool?,
      lessonList: (json['lessonList'] as List<dynamic>?)
          ?.map((e) => LessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CalendarInfoToJson(_$_CalendarInfo instance) =>
    <String, dynamic>{
      'time': instance.time,
      'status': instance.status,
      'frostEndTime': instance.frostEndTime,
      'isUseConcatProp': instance.isUseConcatProp,
      'isAdd': instance.isAdd,
      'lessonList': instance.lessonList,
    };

_$_LessonInfo _$$_LessonInfoFromJson(Map<String, dynamic> json) =>
    _$_LessonInfo(
      usePropIds:
          (json['usePropIds'] as List<dynamic>?)?.map((e) => e as int).toList(),
      relationLesson: json['relationLesson'] == null
          ? null
          : LessonInfo.fromJson(json['relationLesson'] as Map<String, dynamic>),
      lessonId: json['lessonId'] as int?,
      status: json['status'] as int?,
      lessonKey: json['lessonKey'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonSort: json['lessonSort'] as int?,
      classKey: json['classKey'] as String?,
      courseKey: json['courseKey'] as String?,
      courseSegmentName: json['courseSegmentName'] as String?,
      courseTypeName: json['courseTypeName'] as String?,
      classId: json['classId'] as int?,
      unlockTime: json['unlockTime'] as int?,
      isFinish: json['isFinish'] as int?,
      router: json['router'] as String?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
    );

Map<String, dynamic> _$$_LessonInfoToJson(_$_LessonInfo instance) =>
    <String, dynamic>{
      'usePropIds': instance.usePropIds,
      'relationLesson': instance.relationLesson,
      'lessonId': instance.lessonId,
      'status': instance.status,
      'lessonKey': instance.lessonKey,
      'lessonName': instance.lessonName,
      'lessonSort': instance.lessonSort,
      'classKey': instance.classKey,
      'courseKey': instance.courseKey,
      'courseSegmentName': instance.courseSegmentName,
      'courseTypeName': instance.courseTypeName,
      'classId': instance.classId,
      'unlockTime': instance.unlockTime,
      'isFinish': instance.isFinish,
      'router': instance.router,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
    };

_$_GuideResource _$$_GuideResourceFromJson(Map<String, dynamic> json) =>
    _$_GuideResource(
      scene: json['scene'] as int?,
      audio: json['audio'] as String?,
    );

Map<String, dynamic> _$$_GuideResourceToJson(_$_GuideResource instance) =>
    <String, dynamic>{
      'scene': instance.scene,
      'audio': instance.audio,
    };
