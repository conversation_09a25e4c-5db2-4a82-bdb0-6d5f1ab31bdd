import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/env.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart'; // 假设S类在这里
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_controller.dart';

import '../../../static/fonts.dart';
import '../../../static/img.dart';
import '../model/jojo_continuology_api_data.dart';
import '../model/jojo_continuology_milestone_data.dart';

class JoJoContinuologyDetailMilestone extends StatelessWidget {
  static const String tag = "JoJoContinuologyDetailMilestone";

  static const double height = 134;
  static const double spacing = 20;

  final double boxWidth = 36.rdp;
  final double thumpWidth = 22.rdp;

  final JojoContinuologyMilestoneData milestoneData;
  JoJoContinuologyDetailMilestone({super.key, required this.milestoneData});

  @override
  Widget build(BuildContext context) {
    final String title = milestoneData.milestone?.title ?? "";
    final padding = context.dimensions.mediumSpacing.rdp;

    return Container(
      height: height.rdp,
      alignment: Alignment.centerLeft,
      margin:
          EdgeInsets.symmetric(vertical: spacing.rdp, horizontal: spacing.rdp),
      padding: EdgeInsets.symmetric(horizontal: spacing.rdp),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius:
            BorderRadius.circular(context.dimensions.largeCornerRadius.rdp),
        border: Border.all(color: context.appColors.jColorGray3, width: 1.rdp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleRow(context, title),
          SizedBox(height: padding),
          _buildProgressBar(context, milestoneData.milestone?.targetList ?? []),
        ],
      ),
    );
  }

  int getCurrentStreak(List<TargetList>? progressBoxList) {
    if (progressBoxList == null || progressBoxList.isEmpty) {
      return 0; // 数组为空，默认返回 0
    }

    int firstDaysMax = progressBoxList.first.value ?? 0;
    int lastDaysMin = progressBoxList.last.value ?? 0;

    int days = milestoneData.bestInHistory ?? 0;
    days = min(days, lastDaysMin);
    days = max(days, firstDaysMax);
    return days;
  }

  Widget _buildTitleRow(BuildContext context, String title) {
    final _ctl = context.read<JoJoContinuologyController>();
    return SizedBox(
      height: 27.rdp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: context.textstyles.headingEmphasis
                .copyWith(color: context.appColors.jColorGray6),
          ),
          GestureDetector(
            onTap: () {
              if (milestoneData.milestone?.buttonRoute != null) {
                RunEnv.jumpLink(milestoneData.milestone?.buttonRoute ?? "");
              }
              _ctl.stopStudyGuideAudio();
              RunEnv.sensorsTrack('\$AppClick', {
                '\$element_name': '连续学详情页_里程碑奖励入口点击',
                'material_id': milestoneData.subjectName ?? ''
              });
            },
            child: Row(
              children: [
                Text(
                  milestoneData.milestone?.buttonText ?? "", // "里程碑奖励"
                  style: context.textstyles.remark
                      .copyWith(color: context.appColors.jColorGray6),
                ),
                SizedBox(width: 4.rdp),
                Icon(Icons.arrow_forward_ios,
                    size: 12.rdp, color: const Color(0xFF666666)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context, List<TargetList> targetList) {
    if (targetList.isEmpty) return const SizedBox.shrink();

    final firstValue = targetList.first.value ?? 0;
    final lastValue = targetList.last.value ?? 0;

    if (lastValue < firstValue) {
      l.e(tag, "宝箱数组异常 targetList: $targetList");
      return const SizedBox.shrink();
    }

    final maxIndex = lastValue - firstValue;

    final int realDays = milestoneData.bestInHistory ?? 0;

    // 当realDays < 或者 >  progressBoxList的边界值，需要进行转换
    final int currentStreak = getCurrentStreak(targetList);

    final List<JojoContinuologyMilestoneProgressBoxData> progressBoxList =
        targetList
            .map((e) => JojoContinuologyMilestoneProgressBoxData(
                  days: e.value ?? 0,
                  isOpen: realDays >= (e.value ?? 0),
                  index: max(0, (e.value ?? 0) - firstValue),
                ))
            .toList();

    return SizedBox(
      height: 63.rdp,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final double layoutWidth = constraints.maxWidth;
          List<Widget> stackChildren = [];
          final double thumpPercent =
              maxIndex > 0 ? (currentStreak - firstValue) / maxIndex : 0;

          final sliderWidth = layoutWidth - boxWidth;
          double thumpPostion =
              thumpPercent * sliderWidth + (boxWidth - thumpWidth) / 2.0;

          stackChildren.add(
            Positioned(
              top: 17.5.rdp,
              left: boxWidth / 2.0,
              right: boxWidth / 2.0,
              child: Stack(
                children: [
                  _buildSlider(context, currentStreak),
                  Positioned(
                    left: context.dimensions.minimumSpacing.rdp,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: thumpPostion,
                      height: 16.rdp,
                      decoration: BoxDecoration(
                        color: context.appColors.jColorOrange4,
                        borderRadius: BorderRadius.circular(16.rdp),
                        border: Border.all(
                          color: context.appColors.jColorOrange5,
                          width: 1.rdp,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );

          stackChildren.add(
            Positioned(
              top: 13.rdp,
              left: thumpPostion,
              child: _getThump(context, realDays),
            ),
          );

          stackChildren.add(progressitemsRows(
              progressBoxList, maxIndex, context, layoutWidth));
          return Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topLeft,
            children: stackChildren,
          );
        },
      ),
    );
  }

  Widget progressitemsRows(
      List<JojoContinuologyMilestoneProgressBoxData> progressBoxList,
      int maxIndex,
      BuildContext context,
      double layoutWidth) {
    Widget progressItemsRow = SizedBox(
      width: layoutWidth,
      child: Stack(
        children: [
          for (int i = 0; i < progressBoxList.length; i++)
            (() {
              final width = layoutWidth - boxWidth;
              final item = progressBoxList[i];
              final double percent = maxIndex > 0 ? (item.index / maxIndex) : 0;
              final left = percent * width;
              return Positioned(
                left: left,
                top: 0,
                child: _buildMilestoneItem(context, item),
              );
            })(),
        ],
      ),
    );
    return progressItemsRow;
  }

  Widget _buildMilestoneItem(
      BuildContext context, JojoContinuologyMilestoneProgressBoxData target) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: boxWidth,
          height: boxWidth,
          child: ImageAssetWeb(
            assetName: target.imageAsset(),
            package: RunEnv.package,
            width: 24.rdp,
            height: 24.rdp,
          ),
        ),
        SizedBox(height: 5.rdp),
        Text(
          "${target.days}${S.of(context).consecutiveUnit}",
          style: context.textstyles.remark
              .copyWith(color: context.appColors.jColorGray6),
        ),
      ],
    );
  }

  Widget _buildSlider(BuildContext context, int days) {
    return Container(
      margin: EdgeInsets.only(
          left: context.dimensions.minimumSpacing.rdp,
          right: context.dimensions.minimumSpacing.rdp),
      height: 16.rdp,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.rdp),
          border:
              Border.all(color: context.appColors.jColorGray3, width: 1.rdp)),
    );
  }

  Widget _getThump(BuildContext context, int days) {
    return SizedBox(
      width: thumpWidth,
      height: 20.rdp,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AssetsImg.CONTINUE_STUDY_MILESTONE_THUMP,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            package: RunEnv.package,
          ),
          Text(
            days.toString(),
            style: context.textstyles.remark.copyWith(
                color: Colors.white, fontFamily: AssetsFonts.MohrRoundedBold),
          ),
        ],
      ),
    );
  }
}
