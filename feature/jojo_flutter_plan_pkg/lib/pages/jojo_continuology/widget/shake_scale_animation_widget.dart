import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

class ShakeScaleAnimationWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onAnimationComplete;
  final bool autoStart;
  final bool enableAnimation;
  final bool enableAutoReplay;
  final Duration replayDelay;

  const ShakeScaleAnimationWidget({
    Key? key,
    required this.child,
    this.onAnimationComplete,
    this.autoStart = false,
    this.enableAnimation = true,
    this.enableAutoReplay = false,
    this.replayDelay = const Duration(seconds: 10),
  }) : super(key: key);

  @override
  State<ShakeScaleAnimationWidget> createState() =>
      _ShakeScaleAnimationWidgetState();
}

class _ShakeScaleAnimationWidgetState extends State<ShakeScaleAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Timer? _replayTimer;

  // 预计算的角度值（弧度）
  static const double _angle5Rad = 5.0 * math.pi / 180;
  static const double _angleMinus5Rad = -5.0 * math.pi / 180;

  late Animation<double> _scaleAnimation;
  late Animation<double> _shakeAnimation;

  bool _isAnimating = false;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();

    if (widget.enableAnimation) {
      _initAnimation();
      if (widget.autoStart) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!_isDisposed) {
            startAnimation();
          }
        });
      }
    } else if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDisposed) {
          widget.onAnimationComplete?.call();
        }
      });
    }
  }

  void _initAnimation() {
    // 使用单个控制器，总动画时长2100ms
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2100),
      vsync: this,
    );

    // 缩放动画：0-300ms放大，300-1800ms保持，1800-2100ms缩小
    _scaleAnimation = TweenSequence<double>([
      // 放大阶段 (0-300ms)
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.25)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 300 / 2100, // 14.3%
      ),
      // 保持阶段 (300-1800ms)
      TweenSequenceItem(
        tween: ConstantTween<double>(1.25),
        weight: 1500 / 2100, // 71.4%
      ),
      // 缩小阶段 (1800-2100ms)
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.25, end: 1.0)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 300 / 2100, // 14.3%
      ),
    ]).animate(_controller);

    // 摇晃动画：500-1800ms执行摇晃
    _shakeAnimation = TweenSequence<double>([
      // 静止阶段 (0-500ms)
      TweenSequenceItem(
        tween: ConstantTween<double>(0.0),
        weight: 500 / 2100, // 23.8%
      ),
      // 摇晃序列 (500-1800ms，共1300ms)
      TweenSequenceItem(
        tween: _createShakeTween(),
        weight: 1300 / 2100, // 61.9%
      ),
      // 静止阶段 (1800-2100ms)
      TweenSequenceItem(
        tween: ConstantTween<double>(0.0),
        weight: 300 / 2100, // 14.3%
      ),
    ]).animate(_controller);
  }

  // 创建优化的摇晃动画
  Animatable<double> _createShakeTween() {
    return TweenSequence<double>([
      // 0→5度，100ms
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.0, end: _angle5Rad),
        weight: 100 / 1300,
      ),
      // 5→-5度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angle5Rad, end: _angleMinus5Rad),
        weight: 200 / 1300,
      ),
      // -5→5度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angleMinus5Rad, end: _angle5Rad),
        weight: 200 / 1300,
      ),
      // 5→-5度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angle5Rad, end: _angleMinus5Rad),
        weight: 200 / 1300,
      ),
      // -5→5度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angleMinus5Rad, end: _angle5Rad),
        weight: 200 / 1300,
      ),
      // 5→-5度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angle5Rad, end: _angleMinus5Rad),
        weight: 200 / 1300,
      ),
      // -5→0度，200ms
      TweenSequenceItem(
        tween: Tween<double>(begin: _angleMinus5Rad, end: 0.0),
        weight: 200 / 1300,
      ),
    ]);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _cancelReplayTimer();
    if (widget.enableAnimation) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _cancelReplayTimer() {
    _replayTimer?.cancel();
    _replayTimer = null;
  }

  void _startReplayTimer() {
    if (!widget.enableAutoReplay || _isDisposed) return;

    _cancelReplayTimer();
    _replayTimer = Timer(widget.replayDelay, () {
      if (!_isDisposed && mounted) {
        startAnimation();
      }
    });
  }

  Future<void> startAnimation() async {
    if (!widget.enableAnimation || _isDisposed || _isAnimating) {
      if (!widget.enableAnimation) {
        widget.onAnimationComplete?.call();
      }
      return;
    }

    _cancelReplayTimer();

    if (!mounted || _isDisposed) return;

    _isAnimating = true;

    try {
      _controller.reset();
      await _controller.forward();

      if (!_isDisposed) {
        widget.onAnimationComplete?.call();
        _startReplayTimer();
      }
    } catch (e) {
      debugPrint('动画执行出错: $e');
    } finally {
      if (!_isDisposed) {
        _isAnimating = false;
      }
    }
  }

  void stopAnimation() {
    if (!widget.enableAnimation || _isDisposed) return;

    _cancelReplayTimer();
    _controller.stop();
    _controller.reset();
    _isAnimating = false;
  }

  void resetAnimation() {
    stopAnimation();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enableAnimation) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          alignment: Alignment.bottomCenter,
          child: Transform.rotate(
            angle: _shakeAnimation.value,
            alignment: Alignment.bottomCenter,
            child: widget.child,
          ),
        );
      },
    );
  }
}
