import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../../static/img.dart';
import '../controller.dart';
import '../model/find_study_partner_model.dart';

class FindStudyPartnerItem extends StatefulWidget {
  final FindStudyPartnerModel model;
  const FindStudyPartnerItem({super.key, required this.model});

  @override
  State<FindStudyPartnerItem> createState() => _FindStudyPartnerItemState();
}

class _FindStudyPartnerItemState extends State<FindStudyPartnerItem>
    with AutomaticKeepAliveClientMixin {
  bool isAddAction = false;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      height: 120.rdp,
      padding: EdgeInsets.only(
        left: 20.rdp,
        right: 20.rdp,
        top: 7.rdp,
        bottom: 7.rdp,
      ),
      child: Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.rdp),
          border: Border.all(
            color: context.appColors.jColorGray3,
            width: 1.rdp,
          ),
        ),
        child: Stack(
          children: [
            _buildItemBg(),
            _buildAvatar(widget.model.img ?? ""),
            _buildContinuousDaysWidget(widget.model.continuousDays ?? 0),
            _buildNameWidget(widget.model.nickName ?? ""),
            _buildStudyDays(widget.model.studyDays ?? 0),
            _buildButton(widget.model),
          ],
        ),
      ),
    );
  }

  Widget _buildItemBg() {
    return Positioned(
      left: 0,
      top: 0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.rdp),
        child: ImageAssetWeb(
          assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_BG,
          fit: BoxFit.contain,
          package: Config.package,
          height: 113.rdp,
          width: 233.rdp,
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatar) {
    return Positioned(
      left: 16.rdp,
      top: 4.rdp,
      child: AlternativeImageWidget(
        imageUrl: avatar,
        displayWidth: 113.rdp,
        displayHeight: 100.rdp,
        displayConfig: ImageDisplayConfig.waist,
        hideDefaultHolder: true,
      ),
    );
  }

  Widget _buildContinuousDaysWidget(int continuousDays) {
    var str = getContinuousDaysStr(continuousDays);
    return Positioned(
      left: 28.rdp,
      bottom: 8.rdp,
      height: 36.rdp,
      width: 36.rdp,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_NUM_BG,
            fit: BoxFit.contain,
            package: Config.package,
            height: 36.rdp,
            width: 36.rdp,
          ),
          Padding(
            padding: EdgeInsets.only(top: 2.rdp),
            child: Text(
              str,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.rdp,
                color: Colors.white,
                fontFamily: 'MohrRounded_Bold',
                package: 'jojo_flutter_base',
              ),
            ),
          )
        ],
      ),
    );
  }

  String getContinuousDaysStr(int continuousDays) {
    if (continuousDays < 1000) {
      return continuousDays.toString();
    } else if (continuousDays < 10000) {
      int kValue = continuousDays ~/ 1000;
      return '${kValue}k';
    } else {
      int kValue = continuousDays ~/ 10000;
      return '${kValue}w';
    }
  }

  Widget _buildNameWidget(String name) {
    return Positioned(
      left: 165.rdp,
      top: 14.rdp,
      child: Text(
        name,
        style: TextStyle(
          fontSize: 16.rdp,
          color: context.appColors.jColorGray6,
          fontFamily: 'PingFang SC',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStudyDays(int days) {
    return Positioned(
      left: 165.rdp,
      top: 39.rdp,
      child: Text(
        S.of(context).studyDays(days),
        style: TextStyle(
          fontSize: 14.rdp,
          color: context.appColors.jColorGray4,
          fontFamily: 'PingFang SC',
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildButton(FindStudyPartnerModel model) {
    var partnerState = widget.model.partnerState;
    if (partnerState == StudyPartnerState.canApply && isAddAction) {
      return _buildWaitButton(model);
    }

    switch (partnerState) {
      case StudyPartnerState.canApply:
        return _buildAddButton(model);
      case StudyPartnerState.waitingAgreement:
        return _buildWaitButton(model);
      default:
        return Container();
    }
  }

  Widget _buildAddButton(FindStudyPartnerModel model) {
    return Positioned(
      right: 10.rdp,
      bottom: 10.rdp,
      width: 108.rdp,
      height: 32.rdp,
      child: GestureDetector(
        onTap: () => addPartner(model),
        child: Container(
          padding: EdgeInsets.only(right: 4.rdp, top: 1.rdp),
          decoration: BoxDecoration(
            color: context.appColors.jColorYellow4,
            borderRadius: BorderRadius.circular(24.rdp),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_ADD,
                fit: BoxFit.contain,
                package: Config.package,
                height: 20.rdp,
                width: 20.rdp,
              ),
              SizedBox(width: 4.rdp),
              Text(
                S.of(context).addPartner,
                style: TextStyle(
                  fontSize: 14.rdp,
                  color: context.appColors.jColorYellow6,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWaitButton(FindStudyPartnerModel model) {
    return Positioned(
      right: 10.rdp,
      bottom: 10.rdp,
      width: 108.rdp,
      height: 32.rdp,
      child: Container(
        padding: EdgeInsets.only(right: 4.rdp, top: 1.rdp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.rdp),
          border: Border.all(
            color: context.appColors.jColorGray2,
            width: 1.rdp,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_WAIT,
              fit: BoxFit.contain,
              package: Config.package,
              height: 20.rdp,
              width: 20.rdp,
            ),
            SizedBox(width: 4.rdp),
            Text(
              S.of(context).waitAgree,
              style: TextStyle(
                fontSize: 14.rdp,
                color: context.appColors.jColorGray4,
                fontWeight: FontWeight.w400,
                fontFamily: 'PingFang SC',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void addPartner(FindStudyPartnerModel model) async {
    RunEnv.sensorsTrack('\$AppClick', {"\$element_name": "发现学伴_点击加学伴按钮"});
    var partnerId = model.partnerId;
    if (partnerId == null) {
      return;
    }
    setState(() {
      isAddAction = true;
    });
    context.read<FindStudyPartnerController>().addPartner(partnerId);
  }
}
