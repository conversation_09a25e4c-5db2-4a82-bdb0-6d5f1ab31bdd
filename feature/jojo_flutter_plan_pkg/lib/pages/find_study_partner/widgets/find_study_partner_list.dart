import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';

import '../../../static/img.dart';
import '../model/find_study_partner_model.dart';
import 'find_study_partner_item.dart';

class FindStudyPartnerList extends StatefulWidget {
  final FindStudyPartnersModel? model;
  const FindStudyPartnerList({super.key, required this.model});

  @override
  State<FindStudyPartnerList> createState() => _FindStudyPartnerListState();
}

class _FindStudyPartnerListState extends State<FindStudyPartnerList>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return (widget.model?.partners ?? []).isEmpty
        ? _buildEmptyView()
        : _buildListView();
  }

  Widget _buildListView() {
    return ListView.builder(
      itemCount: widget.model?.partners?.length ?? 0,
      itemBuilder: (context, index) {
        var partner = widget.model?.partners?[index];
        if (partner == null) {
          return Container();
        }
        return GestureDetector(
          onTap: () {
            RunEnv.sensorsTrack(
                '\$AppClick', {"\$element_name": "发现学伴_点击资料卡片"});
            var url = partner.url;
            if (url != null) {
              RunEnv.jumpLink(url);
            }
          },
          child: FindStudyPartnerItem(model: partner),
        );
      },
    );
  }

  Widget _buildEmptyView() {
    var route = widget.model?.buyCourseJumpRoute ?? "";
    var emptyImg = widget.model?.emptyImg ?? "";

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          emptyImg.isEmpty
              ? ImageAssetWeb(
                  assetName:
                      AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_LIST_EMPTY,
                  package: Config.package,
                  height: 200.rdp,
                  width: 200.rdp,
                )
              : ImageNetworkCached(
                  height: 200.rdp,
                  width: 200.rdp,
                  fit: BoxFit.cover,
                  imageUrl: widget.model?.emptyImg ?? "",
                ),
          Padding(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            child: Text(
              widget.model?.buyCourseText ?? "",
              style: TextStyle(
                fontSize: 16.rdp,
                fontFamily: 'PingFang SC',
                fontWeight: FontWeight.w400,
                color: context.appColors.jColorGray4,
              ),
            ),
          ),
          SizedBox(height: 20.rdp),
          route.isEmpty
              ? Container()
              : SizedBox(
                  width: 280.rdp,
                  height: 44.rdp,
                  child: OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      backgroundColor: context.appColors.jColorYellow4,
                      foregroundColor: context.appColors.jColorYellow4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22.rdp),
                      ),
                      side: BorderSide.none,
                    ),
                    child: Text(
                      S.of(context).goGetLesson,
                      style: TextStyle(
                        fontSize: 16.rdp,
                        color: context.appColors.jColorYellow6,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'PingFang SC',
                      ),
                    ),
                    onPressed: () {
                      RunEnv.jumpLink(route);
                    },
                  ),
                ),
          SizedBox(height: 150.rdp),
        ],
      ),
    );
  }
}
