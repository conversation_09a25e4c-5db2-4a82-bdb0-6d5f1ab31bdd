import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';

import '../plan_home_map/model/course_map_home_page_tab_data.dart';
import 'controller.dart';
import 'model/find_study_partner_model.dart';
import 'state.dart';
import 'widgets/find_study_partner_list.dart';

class FindStudyPartnerView extends StatefulHookWidget {
  final FindStudyPartnerState state;
  final AudioPlayer audioPlayer;

  const FindStudyPartnerView(
      {super.key, required this.state, required this.audioPlayer});

  @override
  State<FindStudyPartnerView> createState() => _FindStudyPartnerViewState();
}

class _FindStudyPartnerViewState extends State<FindStudyPartnerView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  late int _currentIndex;
  final _spineController = JoJoSpineAnimationController();
  bool _showGuide = false;
  late AudioPlayer _guideAudioPlayer;
  bool _isPlayedGuide = false;

  @override
  void initState() {
    super.initState();
    _guideAudioPlayer = widget.audioPlayer;
    _currentIndex = widget.state.selectIndex ?? 0;
    var subjects = widget.state.subjectList ?? [];
    var partnersMap = widget.state.partnersModel ?? {};
    _tabController = TabController(length: subjects.length, vsync: this);
    _tabController.index = _currentIndex;
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _currentIndex = _tabController.index;
        context
            .read<FindStudyPartnerController>()
            .refreshDataAtIndex(_tabController.index);
      }
    });

    _showGuide = widget.state.isShowGuide ?? false;
    var subject = subjects[_currentIndex];
    var partners = partnersMap[subject.subjectType]?.partners ?? [];
    if (partners.isEmpty) {
      _showGuide = false;
    } else if (partners.first.partnerState != StudyPartnerState.canApply) {
      // 第一个学伴不是可申请状态，代表用户已经申请过了，不显示引导
      _showGuide = false;
      context.read<FindStudyPartnerController>().saveGuideStatus();
    }

    if (_showGuide) {
      context.read<FindStudyPartnerController>().saveGuideStatus();
    }

    RunEnv.sensorsTrack('\$AppViewScreen', {"\$screen_name": "发现学伴_列表页曝光"});
  }

  @override
  void dispose() {
    _tabController.dispose();
    _spineController.dispose();
    super.dispose();
  }

  // @override
  // void didUpdateWidget(FindStudyPartnerView oldWidget) {
  //   _currentIndex = widget.state.selectIndex ?? 0;
  //   _subjects = widget.state.subjectList ?? [];
  //   _partnersMap = widget.state.partnersModel ?? {};

  //   super.didUpdateWidget(oldWidget);
  // }

  void playGuideAudio() {
    if (_isPlayedGuide) return;

    String? package = Config.package;
    String audioPath = AssetsAudio.PLAN_FIND_PARTNER_GUIDE;
    String keyName =
        package == null ? audioPath : 'packages/$package/$audioPath';
    _guideAudioPlayer.audioCache.prefix = '';
    _guideAudioPlayer.onPlayerStateChanged.listen((event) {
      if (event == PlayerState.completed || event == PlayerState.stopped) {
        setState(() {
          _showGuide = false;
        });
      }
    });
    _guideAudioPlayer.play(AssetSource(keyName));
    _isPlayedGuide = true;
  }

  updateTabIndex(int index) {
    _tabController.animateTo(index);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        IgnorePointer(
          ignoring: _showGuide,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTabbarTitle(),
              Expanded(child: _buildViews()),
            ],
          ),
        ),
        Positioned(
          top: 80.rdp,
          right: 28.rdp,
          child: _buildGuideAnimaSpine(),
        ),
      ],
    );
  }

  Widget _buildViews() {
    var subjects = widget.state.subjectList ?? [];
    var partnersMap = widget.state.partnersModel ?? {};
    return TabBarView(
      controller: _tabController,
      children: List.generate(subjects.length, (index) {
        var subject = subjects[index];
        var model = partnersMap[subject.subjectType];
        return _buildPartnerList(subject.subjectType, model);
      }),
    );
  }

  final double _titleWidth = 64;
  final double _titleHeight = 30;

  Widget _buildTabbarTitle() {
    var subjects = widget.state.subjectList ?? [];
    return Container(
      padding: EdgeInsets.only(left: 16.rdp, right: 16.rdp),
      alignment: Alignment.centerLeft,
      width: subjects.length * _titleWidth + 32.rdp,
      height: 44.rdp,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: subjects
            .map(
              (subject) => _buildTitle(
                subject.subjectName ?? '',
                subjects.indexOf(subject),
              ),
            )
            .toList(),
        indicatorSize: TabBarIndicatorSize.tab,
        labelPadding: EdgeInsets.zero,
        indicatorColor: context.appColors.jColorYellow4,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          color: context.appColors.jColorGray4,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w400,
          color: context.appColors.jColorGray6,
        ),
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.circular(1.rdp),
          borderSide: BorderSide(
            width: 2.rdp,
            color: context.appColors.jColorYellow4,
          ),
          insets: EdgeInsets.only(
            bottom: 7.rdp,
            left: 19.rdp,
            right: 19.rdp,
          ),
        ),
        onTap: (int value) {},
      ),
    );
  }

  Widget _buildTitle(String title, int index) {
    return Tab(
      child: SizedBox(
        width: _titleWidth,
        height: _titleHeight,
        child: MaterialButton(
          onPressed: () {
            updateTabIndex(index);
          },
          highlightColor: Colors.white,
          child: Container(
            alignment: Alignment.center,
            width: _titleWidth,
            height: _titleHeight,
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: _currentIndex == index
                    ? context.appColors.jColorGray6
                    : context.appColors.jColorGray4,
                fontWeight:
                    _currentIndex == index ? FontWeight.w500 : FontWeight.w400,
                fontFamily: 'PingFang SC',
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPartnerList(int? subjectType, FindStudyPartnersModel? model) {
    if (subjectType == null || model == null) {
      return JoJoPageLoadingV25(
        status: PageStatus.loading,
        child: Container(),
      );
    }
    return FindStudyPartnerList(model: model);
  }

  Widget _buildGuideAnimaSpine() {
    if (!_showGuide) {
      return Container();
    }

    return Container(
      alignment: Alignment.bottomRight,
      height: 138.rdp,
      width: 60.rdp,
      child: JoJoSpineAnimationWidget(
        AssetsSpine.POP_FINGER_TIPS_ATLAS,
        AssetsSpine.POP_FINGER_TIPS_SKEL,
        LoadMode.assets,
        _spineController,
        package: Config.package,
        onInitialized: (_controller) {
          if (mounted) {
            _spineController.playAnimation(JoJoSpineAnimation(
              animaitonName: "play",
              trackIndex: 0,
              loop: true,
              listener: (type) {
                if (type == AnimationEventType.start) {
                  playGuideAudio();
                }
              },
            ));
          }
        },
      ),
    );
  }
}
