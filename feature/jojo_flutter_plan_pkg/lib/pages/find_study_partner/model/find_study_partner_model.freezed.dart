// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'find_study_partner_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FindStudyPartnersModel _$FindStudyPartnersModelFromJson(
    Map<String, dynamic> json) {
  return _FindStudyPartnersModel.fromJson(json);
}

/// @nodoc
mixin _$FindStudyPartnersModel {
  List<FindStudyPartnerModel>? get partners =>
      throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;
  String? get emptyImg => throw _privateConstructorUsedError;
  String? get buyCourseJumpRoute => throw _privateConstructorUsedError;
  String? get buyCourseText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FindStudyPartnersModelCopyWith<FindStudyPartnersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindStudyPartnersModelCopyWith<$Res> {
  factory $FindStudyPartnersModelCopyWith(FindStudyPartnersModel value,
          $Res Function(FindStudyPartnersModel) then) =
      _$FindStudyPartnersModelCopyWithImpl<$Res, FindStudyPartnersModel>;
  @useResult
  $Res call(
      {List<FindStudyPartnerModel>? partners,
      int? offset,
      int? size,
      String? emptyImg,
      String? buyCourseJumpRoute,
      String? buyCourseText});
}

/// @nodoc
class _$FindStudyPartnersModelCopyWithImpl<$Res,
        $Val extends FindStudyPartnersModel>
    implements $FindStudyPartnersModelCopyWith<$Res> {
  _$FindStudyPartnersModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? offset = freezed,
    Object? size = freezed,
    Object? emptyImg = freezed,
    Object? buyCourseJumpRoute = freezed,
    Object? buyCourseText = freezed,
  }) {
    return _then(_value.copyWith(
      partners: freezed == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<FindStudyPartnerModel>?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      emptyImg: freezed == emptyImg
          ? _value.emptyImg
          : emptyImg // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseJumpRoute: freezed == buyCourseJumpRoute
          ? _value.buyCourseJumpRoute
          : buyCourseJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseText: freezed == buyCourseText
          ? _value.buyCourseText
          : buyCourseText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FindStudyPartnersModelCopyWith<$Res>
    implements $FindStudyPartnersModelCopyWith<$Res> {
  factory _$$_FindStudyPartnersModelCopyWith(_$_FindStudyPartnersModel value,
          $Res Function(_$_FindStudyPartnersModel) then) =
      __$$_FindStudyPartnersModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FindStudyPartnerModel>? partners,
      int? offset,
      int? size,
      String? emptyImg,
      String? buyCourseJumpRoute,
      String? buyCourseText});
}

/// @nodoc
class __$$_FindStudyPartnersModelCopyWithImpl<$Res>
    extends _$FindStudyPartnersModelCopyWithImpl<$Res,
        _$_FindStudyPartnersModel>
    implements _$$_FindStudyPartnersModelCopyWith<$Res> {
  __$$_FindStudyPartnersModelCopyWithImpl(_$_FindStudyPartnersModel _value,
      $Res Function(_$_FindStudyPartnersModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = freezed,
    Object? offset = freezed,
    Object? size = freezed,
    Object? emptyImg = freezed,
    Object? buyCourseJumpRoute = freezed,
    Object? buyCourseText = freezed,
  }) {
    return _then(_$_FindStudyPartnersModel(
      partners: freezed == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<FindStudyPartnerModel>?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      emptyImg: freezed == emptyImg
          ? _value.emptyImg
          : emptyImg // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseJumpRoute: freezed == buyCourseJumpRoute
          ? _value.buyCourseJumpRoute
          : buyCourseJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseText: freezed == buyCourseText
          ? _value.buyCourseText
          : buyCourseText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FindStudyPartnersModel
    with DiagnosticableTreeMixin
    implements _FindStudyPartnersModel {
  const _$_FindStudyPartnersModel(
      {final List<FindStudyPartnerModel>? partners,
      this.offset,
      this.size,
      this.emptyImg,
      this.buyCourseJumpRoute,
      this.buyCourseText})
      : _partners = partners;

  factory _$_FindStudyPartnersModel.fromJson(Map<String, dynamic> json) =>
      _$$_FindStudyPartnersModelFromJson(json);

  final List<FindStudyPartnerModel>? _partners;
  @override
  List<FindStudyPartnerModel>? get partners {
    final value = _partners;
    if (value == null) return null;
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? offset;
  @override
  final int? size;
  @override
  final String? emptyImg;
  @override
  final String? buyCourseJumpRoute;
  @override
  final String? buyCourseText;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FindStudyPartnersModel(partners: $partners, offset: $offset, size: $size, emptyImg: $emptyImg, buyCourseJumpRoute: $buyCourseJumpRoute, buyCourseText: $buyCourseText)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'FindStudyPartnersModel'))
      ..add(DiagnosticsProperty('partners', partners))
      ..add(DiagnosticsProperty('offset', offset))
      ..add(DiagnosticsProperty('size', size))
      ..add(DiagnosticsProperty('emptyImg', emptyImg))
      ..add(DiagnosticsProperty('buyCourseJumpRoute', buyCourseJumpRoute))
      ..add(DiagnosticsProperty('buyCourseText', buyCourseText));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FindStudyPartnersModel &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.emptyImg, emptyImg) ||
                other.emptyImg == emptyImg) &&
            (identical(other.buyCourseJumpRoute, buyCourseJumpRoute) ||
                other.buyCourseJumpRoute == buyCourseJumpRoute) &&
            (identical(other.buyCourseText, buyCourseText) ||
                other.buyCourseText == buyCourseText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_partners),
      offset,
      size,
      emptyImg,
      buyCourseJumpRoute,
      buyCourseText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FindStudyPartnersModelCopyWith<_$_FindStudyPartnersModel> get copyWith =>
      __$$_FindStudyPartnersModelCopyWithImpl<_$_FindStudyPartnersModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FindStudyPartnersModelToJson(
      this,
    );
  }
}

abstract class _FindStudyPartnersModel implements FindStudyPartnersModel {
  const factory _FindStudyPartnersModel(
      {final List<FindStudyPartnerModel>? partners,
      final int? offset,
      final int? size,
      final String? emptyImg,
      final String? buyCourseJumpRoute,
      final String? buyCourseText}) = _$_FindStudyPartnersModel;

  factory _FindStudyPartnersModel.fromJson(Map<String, dynamic> json) =
      _$_FindStudyPartnersModel.fromJson;

  @override
  List<FindStudyPartnerModel>? get partners;
  @override
  int? get offset;
  @override
  int? get size;
  @override
  String? get emptyImg;
  @override
  String? get buyCourseJumpRoute;
  @override
  String? get buyCourseText;
  @override
  @JsonKey(ignore: true)
  _$$_FindStudyPartnersModelCopyWith<_$_FindStudyPartnersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

FindStudyPartnerModel _$FindStudyPartnerModelFromJson(
    Map<String, dynamic> json) {
  return _FindStudyPartnerModel.fromJson(json);
}

/// @nodoc
mixin _$FindStudyPartnerModel {
  String? get nickName => throw _privateConstructorUsedError;
  int? get continuousDays => throw _privateConstructorUsedError;
  int? get studyDays => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  int? get partnerId => throw _privateConstructorUsedError;
  StudyPartnerState? get partnerState => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FindStudyPartnerModelCopyWith<FindStudyPartnerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FindStudyPartnerModelCopyWith<$Res> {
  factory $FindStudyPartnerModelCopyWith(FindStudyPartnerModel value,
          $Res Function(FindStudyPartnerModel) then) =
      _$FindStudyPartnerModelCopyWithImpl<$Res, FindStudyPartnerModel>;
  @useResult
  $Res call(
      {String? nickName,
      int? continuousDays,
      int? studyDays,
      String? img,
      String? url,
      int? partnerId,
      StudyPartnerState? partnerState});
}

/// @nodoc
class _$FindStudyPartnerModelCopyWithImpl<$Res,
        $Val extends FindStudyPartnerModel>
    implements $FindStudyPartnerModelCopyWith<$Res> {
  _$FindStudyPartnerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
    Object? partnerState = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerState: freezed == partnerState
          ? _value.partnerState
          : partnerState // ignore: cast_nullable_to_non_nullable
              as StudyPartnerState?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FindStudyPartnerModelCopyWith<$Res>
    implements $FindStudyPartnerModelCopyWith<$Res> {
  factory _$$_FindStudyPartnerModelCopyWith(_$_FindStudyPartnerModel value,
          $Res Function(_$_FindStudyPartnerModel) then) =
      __$$_FindStudyPartnerModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickName,
      int? continuousDays,
      int? studyDays,
      String? img,
      String? url,
      int? partnerId,
      StudyPartnerState? partnerState});
}

/// @nodoc
class __$$_FindStudyPartnerModelCopyWithImpl<$Res>
    extends _$FindStudyPartnerModelCopyWithImpl<$Res, _$_FindStudyPartnerModel>
    implements _$$_FindStudyPartnerModelCopyWith<$Res> {
  __$$_FindStudyPartnerModelCopyWithImpl(_$_FindStudyPartnerModel _value,
      $Res Function(_$_FindStudyPartnerModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? img = freezed,
    Object? url = freezed,
    Object? partnerId = freezed,
    Object? partnerState = freezed,
  }) {
    return _then(_$_FindStudyPartnerModel(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerState: freezed == partnerState
          ? _value.partnerState
          : partnerState // ignore: cast_nullable_to_non_nullable
              as StudyPartnerState?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FindStudyPartnerModel
    with DiagnosticableTreeMixin
    implements _FindStudyPartnerModel {
  const _$_FindStudyPartnerModel(
      {this.nickName,
      this.continuousDays,
      this.studyDays,
      this.img,
      this.url,
      this.partnerId,
      this.partnerState});

  factory _$_FindStudyPartnerModel.fromJson(Map<String, dynamic> json) =>
      _$$_FindStudyPartnerModelFromJson(json);

  @override
  final String? nickName;
  @override
  final int? continuousDays;
  @override
  final int? studyDays;
  @override
  final String? img;
  @override
  final String? url;
  @override
  final int? partnerId;
  @override
  final StudyPartnerState? partnerState;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FindStudyPartnerModel(nickName: $nickName, continuousDays: $continuousDays, studyDays: $studyDays, img: $img, url: $url, partnerId: $partnerId, partnerState: $partnerState)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'FindStudyPartnerModel'))
      ..add(DiagnosticsProperty('nickName', nickName))
      ..add(DiagnosticsProperty('continuousDays', continuousDays))
      ..add(DiagnosticsProperty('studyDays', studyDays))
      ..add(DiagnosticsProperty('img', img))
      ..add(DiagnosticsProperty('url', url))
      ..add(DiagnosticsProperty('partnerId', partnerId))
      ..add(DiagnosticsProperty('partnerState', partnerState));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FindStudyPartnerModel &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.continuousDays, continuousDays) ||
                other.continuousDays == continuousDays) &&
            (identical(other.studyDays, studyDays) ||
                other.studyDays == studyDays) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.partnerState, partnerState) ||
                other.partnerState == partnerState));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickName, continuousDays,
      studyDays, img, url, partnerId, partnerState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FindStudyPartnerModelCopyWith<_$_FindStudyPartnerModel> get copyWith =>
      __$$_FindStudyPartnerModelCopyWithImpl<_$_FindStudyPartnerModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FindStudyPartnerModelToJson(
      this,
    );
  }
}

abstract class _FindStudyPartnerModel implements FindStudyPartnerModel {
  const factory _FindStudyPartnerModel(
      {final String? nickName,
      final int? continuousDays,
      final int? studyDays,
      final String? img,
      final String? url,
      final int? partnerId,
      final StudyPartnerState? partnerState}) = _$_FindStudyPartnerModel;

  factory _FindStudyPartnerModel.fromJson(Map<String, dynamic> json) =
      _$_FindStudyPartnerModel.fromJson;

  @override
  String? get nickName;
  @override
  int? get continuousDays;
  @override
  int? get studyDays;
  @override
  String? get img;
  @override
  String? get url;
  @override
  int? get partnerId;
  @override
  StudyPartnerState? get partnerState;
  @override
  @JsonKey(ignore: true)
  _$$_FindStudyPartnerModelCopyWith<_$_FindStudyPartnerModel> get copyWith =>
      throw _privateConstructorUsedError;
}
