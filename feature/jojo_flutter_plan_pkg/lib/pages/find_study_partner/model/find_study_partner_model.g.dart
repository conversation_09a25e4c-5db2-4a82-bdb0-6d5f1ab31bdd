// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'find_study_partner_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_FindStudyPartnersModel _$$_FindStudyPartnersModelFromJson(
        Map<String, dynamic> json) =>
    _$_FindStudyPartnersModel(
      partners: (json['partners'] as List<dynamic>?)
          ?.map(
              (e) => FindStudyPartnerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      offset: json['offset'] as int?,
      size: json['size'] as int?,
      emptyImg: json['emptyImg'] as String?,
      buyCourseJumpRoute: json['buyCourseJumpRoute'] as String?,
      buyCourseText: json['buyCourseText'] as String?,
    );

Map<String, dynamic> _$$_FindStudyPartnersModelToJson(
        _$_FindStudyPartnersModel instance) =>
    <String, dynamic>{
      'partners': instance.partners,
      'offset': instance.offset,
      'size': instance.size,
      'emptyImg': instance.emptyImg,
      'buyCourseJumpRoute': instance.buyCourseJumpRoute,
      'buyCourseText': instance.buyCourseText,
    };

_$_FindStudyPartnerModel _$$_FindStudyPartnerModelFromJson(
        Map<String, dynamic> json) =>
    _$_FindStudyPartnerModel(
      nickName: json['nickName'] as String?,
      continuousDays: json['continuousDays'] as int?,
      studyDays: json['studyDays'] as int?,
      img: json['img'] as String?,
      url: json['url'] as String?,
      partnerId: json['partnerId'] as int?,
      partnerState:
          $enumDecodeNullable(_$StudyPartnerStateEnumMap, json['partnerState']),
    );

Map<String, dynamic> _$$_FindStudyPartnerModelToJson(
        _$_FindStudyPartnerModel instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'continuousDays': instance.continuousDays,
      'studyDays': instance.studyDays,
      'img': instance.img,
      'url': instance.url,
      'partnerId': instance.partnerId,
      'partnerState': _$StudyPartnerStateEnumMap[instance.partnerState],
    };

const _$StudyPartnerStateEnumMap = {
  StudyPartnerState.canApply: 0,
  StudyPartnerState.waitingAgreement: 1,
  StudyPartnerState.forbiddenApply: 2,
};
