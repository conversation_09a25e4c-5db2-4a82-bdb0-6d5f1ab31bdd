import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'find_study_partner_model.freezed.dart';
part 'find_study_partner_model.g.dart';

enum StudyPartnerState {
  @JsonValue(0)
  canApply, // // 可发起学伴申请
  @JsonValue(1)
  waitingAgreement, // 等待同意
  @JsonValue(2)
  forbiddenApply // 禁止发起学伴申请
}

@freezed
class FindStudyPartnersModel with _$FindStudyPartnersModel {
  const factory FindStudyPartnersModel({
    List<FindStudyPartnerModel>? partners,
    int? offset,
    int? size,
    String? emptyImg,
    String? buyCourseJumpRoute,
    String? buyCourseText,
  }) = _FindStudyPartnersModel;

  factory FindStudyPartnersModel.fromJson(Map<String, Object?> json) =>
      _$FindStudyPartnersModelFromJson(json);
}

@freezed
class FindStudyPartnerModel with _$FindStudyPartnerModel {
  const factory FindStudyPartnerModel({
    String? nickName,
    int? continuousDays,
    int? studyDays,
    String? img,
    String? url,
    int? partnerId,
    StudyPartnerState? partnerState,
  }) = _FindStudyPartnerModel;

  factory FindStudyPartnerModel.fromJson(Map<String, Object?> json) =>
      _$FindStudyPartnerModelFromJson(json);
}
