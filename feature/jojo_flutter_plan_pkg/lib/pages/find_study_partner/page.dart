import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../../../generated/l10n.dart';
import 'controller.dart';
import 'state.dart';
import 'view.dart';

class FindStudyPartnerPage extends BasePage {
  final int? subjectType;
  const FindStudyPartnerPage({Key? key, required this.subjectType})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => FindStudyPartnerPageState();
}

class FindStudyPartnerPageState extends BaseState<FindStudyPartnerPage> {
  final ValueNotifier<double> _appBarOpacityNotifier = ValueNotifier(0.0);
  late FindStudyPartnerController _controller;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    jojoNativeBridge.operationNativeValueSet(key: "flutter_studty_partner_key", value: "1"); //学伴缓存
    super.initState();
    _controller = FindStudyPartnerController.withDefault(
      initSubjectType: widget.subjectType,
    );
  }

  @override
  void dispose() {
    _appBarOpacityNotifier.dispose();
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  void onPause() {
    super.onPause();
    if (_audioPlayer.state == PlayerState.playing) {
      _audioPlayer.pause();
    }
  }

  @override
  void onResume() {
    super.onResume();
    if (_audioPlayer.state == PlayerState.paused) {
      _audioPlayer.resume();
    }

    _controller.forceRefreshLatestPage();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _controller,
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<FindStudyPartnerController, FindStudyPartnerState>(
            builder: (context, state) {
              return Scaffold(
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(
                    const JoJoAppBar().preferredSize.height,
                  ),
                  child: ValueListenableBuilder<double>(
                    valueListenable: _appBarOpacityNotifier,
                    builder: (context, opacity, child) {
                      return JoJoAppBar(
                        title: S.of(context).findPartner,
                        backgroundColor: Color.fromRGBO(255, 255, 255, opacity),
                        onBack: (handler) {
                          _audioPlayer.stop();
                          handler?.call();
                        },
                      );
                    },
                  ),
                ),
                body: JoJoPageLoadingV25(
                  scene: PageScene.common,
                  hideProgress: true,
                  status: state.pageStatus,
                  child: FindStudyPartnerView(
                    state: state,
                    audioPlayer: _audioPlayer,
                  ),
                  retry: () {
                    context
                        .read<FindStudyPartnerController>()
                        .refreshLatestData();
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
