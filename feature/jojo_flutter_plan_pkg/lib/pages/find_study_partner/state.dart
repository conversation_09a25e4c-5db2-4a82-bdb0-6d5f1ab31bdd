import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/model/find_study_partner_model.dart';

import '../plan_home_map/model/course_map_home_page_tab_data.dart';

class FindStudyPartnerState {
  final PageStatus pageStatus;
  final Exception? exception;
  final int? selectIndex;
  final List<SubjectList>? subjectList;
  final Map<int, FindStudyPartnersModel>? partnersModel;
  final bool? isShowGuide;

  FindStudyPartnerState(
    this.pageStatus, {
    this.exception,
    this.selectIndex,
    this.subjectList,
    this.partnersModel,
    this.isShowGuide,
  });

  FindStudyPartnerState copyWith({
    PageStatus? pageStatus,
    Exception? exception,
    int? selectIndex,
    List<SubjectList>? subjectList,
    Map<int, FindStudyPartnersModel>? partnersModel,
    bool? isShowGuide,
  }) {
    return FindStudyPartnerState(
      pageStatus ?? this.pageStatus,
      exception: exception ?? this.exception,
      selectIndex: selectIndex ?? this.selectIndex,
      subjectList: subjectList ?? this.subjectList,
      partnersModel: partnersModel ?? this.partnersModel,
      isShowGuide: isShowGuide ?? this.isShowGuide,
    );
  }
}
