import 'package:collection/collection.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../service/find_study_partner_api.dart';
import '../plan_home_map/model/course_map_home_page_tab_data.dart';
import 'model/find_study_partner_model.dart';
import 'state.dart';

class FindStudyPartnerController extends Cubit<FindStudyPartnerState> {
  static const String tag = "FindStudyPartner";
  final FindStudyPartnerApi api;
  final int? initSubjectType;

  List<SubjectList>? _subjectList;
  int _selectIndex = 0;
  final Map<int, FindStudyPartnersModel> _partnersMap = {};
  final List<int> _requestSubjectList = [];
  int? _needUpdateWhenPreRequestFinishIndex;
  bool _needShowGuide = false;

  FindStudyPartnerController(this.api, {this.initSubjectType})
      : super(FindStudyPartnerState(PageStatus.loading));

  FindStudyPartnerController.withDefault({this.initSubjectType})
      : api = proFindStudyPartnerApi,
        // : api = FindStudyPartnerMockApi(),
        super(FindStudyPartnerState(PageStatus.loading)) {
    initData();
  }

  Future<void> _updateState(FindStudyPartnerState state) async {
    super.emit(state);
  }

  void _loadError() {
    _updateState(state.copyWith(
      pageStatus: PageStatus.error,
      exception: Exception('数据异常'),
    ));
  }

  void _loadSuccess(int index) async {
    _updateState(state.copyWith(
      pageStatus: PageStatus.success,
      selectIndex: index,
      subjectList: _subjectList,
      partnersModel: _partnersMap,
      isShowGuide: _needShowGuide,
    ));
  }

  int? _getSubjectTypeAtIndex(int index) {
    return getSubjectTypeAtIndex(_subjectList ?? [], index);
  }

  int? getSubjectTypeAtIndex(List<SubjectList> subjectList, int index) {
    var subject = subjectList.elementAtOrNull(index);
    return subject?.subjectType;
  }

  void setSubjectList(List<SubjectList> subjectList) {
    _subjectList = subjectList;
  }

  initData() async {
    getNeedShowGuide().then((data) => _needShowGuide = data);

    bool getTab = await refreshTab();
    if (!getTab) return;

    _selectIndex = 0;
    if (initSubjectType != null) {
      for (var i = 0; i < (_subjectList ?? []).length; i++) {
        if (initSubjectType == _subjectList?[i].subjectType) {
          _selectIndex = i;
        }
      }
    }

    refreshDataAtIndex(_selectIndex);
  }

  Future<bool> refreshTab() async {
    if (_subjectList == null) {
      var tabData = await requestSubjectTabData();
      if (tabData == null) {
        _loadError();
        return Future.value(false);
      }
      _subjectList = getCleanSubjectList(tabData);
    }
    return Future.value(true);
  }

  List<SubjectList> getCleanSubjectList(CourseSubjectTabData tabData) {
    Set subjectTypes = {};
    tabData.subjectClassList?.forEach((subjectClass) {
      subjectTypes.add(subjectClass.subjectType);
    });

    List<SubjectList> subjectList = [];
    tabData.subjectList?.forEach((subject) {
      if (subjectTypes.contains(subject.subjectType)) {
        subjectList.add(subject);
      }
    });
    return subjectList;
  }

  /// 点击重试
  refreshLatestData() async {
    _updateState(state.copyWith(pageStatus: PageStatus.loading));

    bool getTab = await refreshTab();
    if (!getTab) return;
    await refreshDataAtIndex(_selectIndex);
  }

  /// 强制刷新当前页面
  forceRefreshLatestPage() async {
    print("weich forceRefreshLatestPage");
    if (_subjectList != null) {
      await refreshDataAtIndex(_selectIndex, force: true);
    }
  }

  /// 获取指定页面数据
  refreshDataAtIndex(int index, {bool force = false}) async {
    _selectIndex = index;

    var subjectType = _getSubjectTypeAtIndex(index);
    if (subjectType == null) {
      _loadError();
      return;
    }

    FindStudyPartnersModel? model;

    // 已加载过，直接返回
    if (_partnersMap.keys.contains(subjectType) && !force) {
      model = _partnersMap[subjectType];
    }

    // 已在预加载中，但未加载完成
    if (model == null && _requestSubjectList.contains(subjectType) && !force) {
      // 标记需要更新，当请求完成时再更新
      _needUpdateWhenPreRequestFinishIndex = index;
      return;
    }

    // 未加载过，请求数据
    model ??= await requestStudyPartner(subjectType, true);

    if (model != null) {
      _partnersMap[subjectType] = model;
      _loadSuccess(index);
      findPreRequestPartner(index);
    } else {
      _loadError();
    }
  }

  findPreRequestPartner(int index) {
    if (index - 1 >= 0) {
      preRequestPartner(index - 1);
    }
    if (index + 1 < (_subjectList ?? []).length) {
      preRequestPartner(index + 1);
    }
  }

  /// 预加载前后页数据
  Future<void> preRequestPartner(int index) async {
    int? type = _getSubjectTypeAtIndex(index);

    if (type != null && !_requestSubjectList.contains(type)) {
      var model = await requestStudyPartner(type, false);
      if (model != null) {
        _partnersMap[type] = model;
      }

      // 预加载完成，如果标记需要更新，则更新
      if (_needUpdateWhenPreRequestFinishIndex == index) {
        _needUpdateWhenPreRequestFinishIndex = null;
        refreshDataAtIndex(index);
      }
    }
  }

  FindStudyPartnersModel? getPartnerModel(int index) {
    int? type = _getSubjectTypeAtIndex(index);
    if (_partnersMap.keys.contains(type)) {
      return _partnersMap[type];
    }
    return null;
  }

  Future<FindStudyPartnersModel?> requestStudyPartner(
      int subjectType, bool showError) async {
    try {
      _requestSubjectList.add(subjectType);
      final partnersModel = await api.getStudyPartners(subjectType, 0, 20);
      return partnersModel;
    } catch (e) {
      _requestSubjectList.remove(subjectType);
      if (showError) {
        _updateState(
          state.copyWith(
            pageStatus: PageStatus.error,
            exception: Exception(e.toString()),
          ),
        );
      }
      return null;
    }
  }

  Future<CourseSubjectTabData?> requestSubjectTabData() async {
    try {
      final tabData = await api.getUserSubjectClass();
      return tabData;
    } catch (e) {
      _updateState(
        state.copyWith(
          pageStatus: PageStatus.error,
          exception: Exception(e.toString()),
        ),
      );
      return Future.value(null);
    }
  }

  Future<bool> addPartner(int partnerId) async {
    try {
      await api.addPartner(partnerId);
      return Future.value(true);
    } catch (e) {
      l.e('FindPartner', '添加学伴失败：${e.toString()}');
      return Future.value(false);
    }
  }

  final String _guideKey = 'find_study_partner_guide';

  Future<bool> getNeedShowGuide() async {
    try {
      final result =
          await jojoNativeBridge.operationNativeValueGet(key: _guideKey);
      final guideStatus = result.data?.value ?? "0";
      l.i("FindPartner",
          'find_study_partner_guide get guideStatus: $guideStatus');
      return Future.value(guideStatus == '1' ? false : true);
    } catch (e) {
      return Future.value(false);
    }
  }

  saveGuideStatus() {
    l.i("FindPartner", 'find_study_partner_guide set guideStatus: 1');
    jojoNativeBridge.operationNativeValueSet(key: _guideKey, value: "1");
  }
}
