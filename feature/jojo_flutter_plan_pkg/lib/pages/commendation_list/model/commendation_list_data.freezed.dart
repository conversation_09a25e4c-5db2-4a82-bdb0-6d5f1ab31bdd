// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commendation_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CommendationListData _$CommendationListDataFromJson(Map<String, dynamic> json) {
  return _CommendationList.fromJson(json);
}

/// @nodoc
mixin _$CommendationListData {
  List<MonthItem>? get monthList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommendationListDataCopyWith<CommendationListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommendationListDataCopyWith<$Res> {
  factory $CommendationListDataCopyWith(CommendationListData value,
          $Res Function(CommendationListData) then) =
      _$CommendationListDataCopyWithImpl<$Res, CommendationListData>;
  @useResult
  $Res call({List<MonthItem>? monthList});
}

/// @nodoc
class _$CommendationListDataCopyWithImpl<$Res,
        $Val extends CommendationListData>
    implements $CommendationListDataCopyWith<$Res> {
  _$CommendationListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthList = freezed,
  }) {
    return _then(_value.copyWith(
      monthList: freezed == monthList
          ? _value.monthList
          : monthList // ignore: cast_nullable_to_non_nullable
              as List<MonthItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CommendationListCopyWith<$Res>
    implements $CommendationListDataCopyWith<$Res> {
  factory _$$_CommendationListCopyWith(
          _$_CommendationList value, $Res Function(_$_CommendationList) then) =
      __$$_CommendationListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<MonthItem>? monthList});
}

/// @nodoc
class __$$_CommendationListCopyWithImpl<$Res>
    extends _$CommendationListDataCopyWithImpl<$Res, _$_CommendationList>
    implements _$$_CommendationListCopyWith<$Res> {
  __$$_CommendationListCopyWithImpl(
      _$_CommendationList _value, $Res Function(_$_CommendationList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthList = freezed,
  }) {
    return _then(_$_CommendationList(
      monthList: freezed == monthList
          ? _value._monthList
          : monthList // ignore: cast_nullable_to_non_nullable
              as List<MonthItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CommendationList
    with DiagnosticableTreeMixin
    implements _CommendationList {
  const _$_CommendationList({final List<MonthItem>? monthList})
      : _monthList = monthList;

  factory _$_CommendationList.fromJson(Map<String, dynamic> json) =>
      _$$_CommendationListFromJson(json);

  final List<MonthItem>? _monthList;
  @override
  List<MonthItem>? get monthList {
    final value = _monthList;
    if (value == null) return null;
    if (_monthList is EqualUnmodifiableListView) return _monthList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CommendationListData(monthList: $monthList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CommendationListData'))
      ..add(DiagnosticsProperty('monthList', monthList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CommendationList &&
            const DeepCollectionEquality()
                .equals(other._monthList, _monthList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_monthList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CommendationListCopyWith<_$_CommendationList> get copyWith =>
      __$$_CommendationListCopyWithImpl<_$_CommendationList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CommendationListToJson(
      this,
    );
  }
}

abstract class _CommendationList implements CommendationListData {
  const factory _CommendationList({final List<MonthItem>? monthList}) =
      _$_CommendationList;

  factory _CommendationList.fromJson(Map<String, dynamic> json) =
      _$_CommendationList.fromJson;

  @override
  List<MonthItem>? get monthList;
  @override
  @JsonKey(ignore: true)
  _$$_CommendationListCopyWith<_$_CommendationList> get copyWith =>
      throw _privateConstructorUsedError;
}

MonthItem _$MonthItemFromJson(Map<String, dynamic> json) {
  return _MonthItem.fromJson(json);
}

/// @nodoc
mixin _$MonthItem {
  String? get backgroundImage => throw _privateConstructorUsedError; // 背景图
  String? get citeDetailUrl => throw _privateConstructorUsedError; // 详情地址
  int? get citeId => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;
  int? get hasRead => throw _privateConstructorUsedError;
  int? get startTime => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonthItemCopyWith<MonthItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonthItemCopyWith<$Res> {
  factory $MonthItemCopyWith(MonthItem value, $Res Function(MonthItem) then) =
      _$MonthItemCopyWithImpl<$Res, MonthItem>;
  @useResult
  $Res call(
      {String? backgroundImage,
      String? citeDetailUrl,
      int? citeId,
      int? endTime,
      int? hasRead,
      int? startTime,
      int? status,
      int? teacherId});
}

/// @nodoc
class _$MonthItemCopyWithImpl<$Res, $Val extends MonthItem>
    implements $MonthItemCopyWith<$Res> {
  _$MonthItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundImage = freezed,
    Object? citeDetailUrl = freezed,
    Object? citeId = freezed,
    Object? endTime = freezed,
    Object? hasRead = freezed,
    Object? startTime = freezed,
    Object? status = freezed,
    Object? teacherId = freezed,
  }) {
    return _then(_value.copyWith(
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      citeDetailUrl: freezed == citeDetailUrl
          ? _value.citeDetailUrl
          : citeDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      citeId: freezed == citeId
          ? _value.citeId
          : citeId // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      hasRead: freezed == hasRead
          ? _value.hasRead
          : hasRead // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MonthItemCopyWith<$Res> implements $MonthItemCopyWith<$Res> {
  factory _$$_MonthItemCopyWith(
          _$_MonthItem value, $Res Function(_$_MonthItem) then) =
      __$$_MonthItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? backgroundImage,
      String? citeDetailUrl,
      int? citeId,
      int? endTime,
      int? hasRead,
      int? startTime,
      int? status,
      int? teacherId});
}

/// @nodoc
class __$$_MonthItemCopyWithImpl<$Res>
    extends _$MonthItemCopyWithImpl<$Res, _$_MonthItem>
    implements _$$_MonthItemCopyWith<$Res> {
  __$$_MonthItemCopyWithImpl(
      _$_MonthItem _value, $Res Function(_$_MonthItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundImage = freezed,
    Object? citeDetailUrl = freezed,
    Object? citeId = freezed,
    Object? endTime = freezed,
    Object? hasRead = freezed,
    Object? startTime = freezed,
    Object? status = freezed,
    Object? teacherId = freezed,
  }) {
    return _then(_$_MonthItem(
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      citeDetailUrl: freezed == citeDetailUrl
          ? _value.citeDetailUrl
          : citeDetailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      citeId: freezed == citeId
          ? _value.citeId
          : citeId // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      hasRead: freezed == hasRead
          ? _value.hasRead
          : hasRead // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MonthItem with DiagnosticableTreeMixin implements _MonthItem {
  const _$_MonthItem(
      {this.backgroundImage,
      this.citeDetailUrl,
      this.citeId,
      this.endTime,
      this.hasRead,
      this.startTime,
      this.status,
      this.teacherId});

  factory _$_MonthItem.fromJson(Map<String, dynamic> json) =>
      _$$_MonthItemFromJson(json);

  @override
  final String? backgroundImage;
// 背景图
  @override
  final String? citeDetailUrl;
// 详情地址
  @override
  final int? citeId;
  @override
  final int? endTime;
  @override
  final int? hasRead;
  @override
  final int? startTime;
  @override
  final int? status;
  @override
  final int? teacherId;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MonthItem(backgroundImage: $backgroundImage, citeDetailUrl: $citeDetailUrl, citeId: $citeId, endTime: $endTime, hasRead: $hasRead, startTime: $startTime, status: $status, teacherId: $teacherId)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MonthItem'))
      ..add(DiagnosticsProperty('backgroundImage', backgroundImage))
      ..add(DiagnosticsProperty('citeDetailUrl', citeDetailUrl))
      ..add(DiagnosticsProperty('citeId', citeId))
      ..add(DiagnosticsProperty('endTime', endTime))
      ..add(DiagnosticsProperty('hasRead', hasRead))
      ..add(DiagnosticsProperty('startTime', startTime))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('teacherId', teacherId));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MonthItem &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.citeDetailUrl, citeDetailUrl) ||
                other.citeDetailUrl == citeDetailUrl) &&
            (identical(other.citeId, citeId) || other.citeId == citeId) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.hasRead, hasRead) || other.hasRead == hasRead) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, backgroundImage, citeDetailUrl,
      citeId, endTime, hasRead, startTime, status, teacherId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MonthItemCopyWith<_$_MonthItem> get copyWith =>
      __$$_MonthItemCopyWithImpl<_$_MonthItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MonthItemToJson(
      this,
    );
  }
}

abstract class _MonthItem implements MonthItem {
  const factory _MonthItem(
      {final String? backgroundImage,
      final String? citeDetailUrl,
      final int? citeId,
      final int? endTime,
      final int? hasRead,
      final int? startTime,
      final int? status,
      final int? teacherId}) = _$_MonthItem;

  factory _MonthItem.fromJson(Map<String, dynamic> json) =
      _$_MonthItem.fromJson;

  @override
  String? get backgroundImage;
  @override // 背景图
  String? get citeDetailUrl;
  @override // 详情地址
  int? get citeId;
  @override
  int? get endTime;
  @override
  int? get hasRead;
  @override
  int? get startTime;
  @override
  int? get status;
  @override
  int? get teacherId;
  @override
  @JsonKey(ignore: true)
  _$$_MonthItemCopyWith<_$_MonthItem> get copyWith =>
      throw _privateConstructorUsedError;
}
