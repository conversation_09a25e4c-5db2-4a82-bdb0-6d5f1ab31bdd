// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_center_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TaskCenterData _$$_TaskCenterDataFromJson(Map<String, dynamic> json) =>
    _$_TaskCenterData(
      todayTasks: (json['todayTasks'] as List<dynamic>?)
          ?.map((e) => TodayTasksItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      normalTaskMarker: json['normalTaskMarker'] as bool?,
      userAsserts: (json['userAsserts'] as List<dynamic>?)
          ?.map((e) => UserAssertsItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      completedNotClaimedTasks:
          (json['completedNotClaimedTasks'] as List<dynamic>?)
              ?.map((e) => UserTasksItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_TaskCenterDataToJson(_$_TaskCenterData instance) =>
    <String, dynamic>{
      'todayTasks': instance.todayTasks,
      'normalTaskMarker': instance.normalTaskMarker,
      'userAsserts': instance.userAsserts,
      'completedNotClaimedTasks': instance.completedNotClaimedTasks,
    };

_$_TodayTasksItem _$$_TodayTasksItemFromJson(Map<String, dynamic> json) =>
    _$_TodayTasksItem(
      subjectType: json['subjectType'] as int?,
      subjectTypeName: json['subjectTypeName'] as String?,
      subjectTypeNameDesc: json['subjectTypeNameDesc'] as String?,
      classId: json['classId'] as int?,
      userActivityId: json['userActivityId'] as String?,
      todayRes: json['todayRes'] as String?,
      beanPortalRes: json['beanPortalRes'] as String?,
      taskList: (json['taskList'] as List<dynamic>?)
          ?.map((e) => TaskItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      userClickField: json['userClickField'] == null
          ? null
          : UserClickField.fromJson(
              json['userClickField'] as Map<String, dynamic>),
      subjectTypeColor: json['subjectTypeColor'] as String?,
    );

Map<String, dynamic> _$$_TodayTasksItemToJson(_$_TodayTasksItem instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectTypeName': instance.subjectTypeName,
      'subjectTypeNameDesc': instance.subjectTypeNameDesc,
      'classId': instance.classId,
      'userActivityId': instance.userActivityId,
      'todayRes': instance.todayRes,
      'beanPortalRes': instance.beanPortalRes,
      'taskList': instance.taskList,
      'userClickField': instance.userClickField,
      'subjectTypeColor': instance.subjectTypeColor,
    };

_$_TaskItem _$$_TaskItemFromJson(Map<String, dynamic> json) => _$_TaskItem(
      id: json['id'] as String?,
      name: json['name'] as String?,
      goFinishRoute: json['goFinishRoute'] as String?,
      desc: json['desc'] as String?,
      isFinish: json['isFinish'] as int?,
      isGet: json['isGet'] as int?,
      targetValue: json['targetValue'] as String?,
      currentValue: json['currentValue'] as String?,
      sourceType: json['sourceType'] as int?,
      rewardList: (json['rewardList'] as List<dynamic>?)
          ?.map((e) => RewardItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TaskItemToJson(_$_TaskItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'goFinishRoute': instance.goFinishRoute,
      'desc': instance.desc,
      'isFinish': instance.isFinish,
      'isGet': instance.isGet,
      'targetValue': instance.targetValue,
      'currentValue': instance.currentValue,
      'sourceType': instance.sourceType,
      'rewardList': instance.rewardList,
    };

_$_RewardItem _$$_RewardItemFromJson(Map<String, dynamic> json) =>
    _$_RewardItem(
      id: json['id'] as String?,
      type: json['type'] as int?,
      value: json['value'] as int?,
      name: json['name'] as String?,
      picUrl: json['picUrl'] as String?,
      sourceType: json['sourceType'] as int?,
    );

Map<String, dynamic> _$$_RewardItemToJson(_$_RewardItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'value': instance.value,
      'name': instance.name,
      'picUrl': instance.picUrl,
      'sourceType': instance.sourceType,
    };

_$_UserClickField _$$_UserClickFieldFromJson(Map<String, dynamic> json) =>
    _$_UserClickField(
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
    );

Map<String, dynamic> _$$_UserClickFieldToJson(_$_UserClickField instance) =>
    <String, dynamic>{
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'classId': instance.classId,
      'classKey': instance.classKey,
    };

_$_UserAssertsItem _$$_UserAssertsItemFromJson(Map<String, dynamic> json) =>
    _$_UserAssertsItem(
      type: json['type'] as int?,
      name: json['name'] as String?,
      totalCount: json['totalCount'] as int?,
      picUrl: json['picUrl'] as String?,
    );

Map<String, dynamic> _$$_UserAssertsItemToJson(_$_UserAssertsItem instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'totalCount': instance.totalCount,
      'picUrl': instance.picUrl,
    };

_$_MissionCentersData _$$_MissionCentersDataFromJson(
        Map<String, dynamic> json) =>
    _$_MissionCentersData(
      userTasks: (json['userTasks'] as List<dynamic>?)
          ?.map((e) => UserTasksItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MissionCentersDataToJson(
        _$_MissionCentersData instance) =>
    <String, dynamic>{
      'userTasks': instance.userTasks,
    };

_$_UserTasksItem _$$_UserTasksItemFromJson(Map<String, dynamic> json) =>
    _$_UserTasksItem(
      userTaskId: json['userTaskId'] as String?,
    );

Map<String, dynamic> _$$_UserTasksItemToJson(_$_UserTasksItem instance) =>
    <String, dynamic>{
      'userTaskId': instance.userTaskId,
    };
