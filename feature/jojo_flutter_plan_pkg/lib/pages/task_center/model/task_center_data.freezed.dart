// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_center_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TaskCenterData _$TaskCenterDataFromJson(Map<String, dynamic> json) {
  return _TaskCenterData.fromJson(json);
}

/// @nodoc
mixin _$TaskCenterData {
  List<TodayTasksItem>? get todayTasks => throw _privateConstructorUsedError;
  bool? get normalTaskMarker => throw _privateConstructorUsedError;
  List<UserAssertsItem>? get userAsserts => throw _privateConstructorUsedError;
  List<UserTasksItem>? get completedNotClaimedTasks =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TaskCenterDataCopyWith<TaskCenterData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskCenterDataCopyWith<$Res> {
  factory $TaskCenterDataCopyWith(
          TaskCenterData value, $Res Function(TaskCenterData) then) =
      _$TaskCenterDataCopyWithImpl<$Res, TaskCenterData>;
  @useResult
  $Res call(
      {List<TodayTasksItem>? todayTasks,
      bool? normalTaskMarker,
      List<UserAssertsItem>? userAsserts,
      List<UserTasksItem>? completedNotClaimedTasks});
}

/// @nodoc
class _$TaskCenterDataCopyWithImpl<$Res, $Val extends TaskCenterData>
    implements $TaskCenterDataCopyWith<$Res> {
  _$TaskCenterDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayTasks = freezed,
    Object? normalTaskMarker = freezed,
    Object? userAsserts = freezed,
    Object? completedNotClaimedTasks = freezed,
  }) {
    return _then(_value.copyWith(
      todayTasks: freezed == todayTasks
          ? _value.todayTasks
          : todayTasks // ignore: cast_nullable_to_non_nullable
              as List<TodayTasksItem>?,
      normalTaskMarker: freezed == normalTaskMarker
          ? _value.normalTaskMarker
          : normalTaskMarker // ignore: cast_nullable_to_non_nullable
              as bool?,
      userAsserts: freezed == userAsserts
          ? _value.userAsserts
          : userAsserts // ignore: cast_nullable_to_non_nullable
              as List<UserAssertsItem>?,
      completedNotClaimedTasks: freezed == completedNotClaimedTasks
          ? _value.completedNotClaimedTasks
          : completedNotClaimedTasks // ignore: cast_nullable_to_non_nullable
              as List<UserTasksItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TaskCenterDataCopyWith<$Res>
    implements $TaskCenterDataCopyWith<$Res> {
  factory _$$_TaskCenterDataCopyWith(
          _$_TaskCenterData value, $Res Function(_$_TaskCenterData) then) =
      __$$_TaskCenterDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TodayTasksItem>? todayTasks,
      bool? normalTaskMarker,
      List<UserAssertsItem>? userAsserts,
      List<UserTasksItem>? completedNotClaimedTasks});
}

/// @nodoc
class __$$_TaskCenterDataCopyWithImpl<$Res>
    extends _$TaskCenterDataCopyWithImpl<$Res, _$_TaskCenterData>
    implements _$$_TaskCenterDataCopyWith<$Res> {
  __$$_TaskCenterDataCopyWithImpl(
      _$_TaskCenterData _value, $Res Function(_$_TaskCenterData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayTasks = freezed,
    Object? normalTaskMarker = freezed,
    Object? userAsserts = freezed,
    Object? completedNotClaimedTasks = freezed,
  }) {
    return _then(_$_TaskCenterData(
      todayTasks: freezed == todayTasks
          ? _value._todayTasks
          : todayTasks // ignore: cast_nullable_to_non_nullable
              as List<TodayTasksItem>?,
      normalTaskMarker: freezed == normalTaskMarker
          ? _value.normalTaskMarker
          : normalTaskMarker // ignore: cast_nullable_to_non_nullable
              as bool?,
      userAsserts: freezed == userAsserts
          ? _value._userAsserts
          : userAsserts // ignore: cast_nullable_to_non_nullable
              as List<UserAssertsItem>?,
      completedNotClaimedTasks: freezed == completedNotClaimedTasks
          ? _value._completedNotClaimedTasks
          : completedNotClaimedTasks // ignore: cast_nullable_to_non_nullable
              as List<UserTasksItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TaskCenterData implements _TaskCenterData {
  _$_TaskCenterData(
      {final List<TodayTasksItem>? todayTasks,
      this.normalTaskMarker,
      final List<UserAssertsItem>? userAsserts,
      final List<UserTasksItem>? completedNotClaimedTasks})
      : _todayTasks = todayTasks,
        _userAsserts = userAsserts,
        _completedNotClaimedTasks = completedNotClaimedTasks;

  factory _$_TaskCenterData.fromJson(Map<String, dynamic> json) =>
      _$$_TaskCenterDataFromJson(json);

  final List<TodayTasksItem>? _todayTasks;
  @override
  List<TodayTasksItem>? get todayTasks {
    final value = _todayTasks;
    if (value == null) return null;
    if (_todayTasks is EqualUnmodifiableListView) return _todayTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? normalTaskMarker;
  final List<UserAssertsItem>? _userAsserts;
  @override
  List<UserAssertsItem>? get userAsserts {
    final value = _userAsserts;
    if (value == null) return null;
    if (_userAsserts is EqualUnmodifiableListView) return _userAsserts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<UserTasksItem>? _completedNotClaimedTasks;
  @override
  List<UserTasksItem>? get completedNotClaimedTasks {
    final value = _completedNotClaimedTasks;
    if (value == null) return null;
    if (_completedNotClaimedTasks is EqualUnmodifiableListView)
      return _completedNotClaimedTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TaskCenterData(todayTasks: $todayTasks, normalTaskMarker: $normalTaskMarker, userAsserts: $userAsserts, completedNotClaimedTasks: $completedNotClaimedTasks)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TaskCenterData &&
            const DeepCollectionEquality()
                .equals(other._todayTasks, _todayTasks) &&
            (identical(other.normalTaskMarker, normalTaskMarker) ||
                other.normalTaskMarker == normalTaskMarker) &&
            const DeepCollectionEquality()
                .equals(other._userAsserts, _userAsserts) &&
            const DeepCollectionEquality().equals(
                other._completedNotClaimedTasks, _completedNotClaimedTasks));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_todayTasks),
      normalTaskMarker,
      const DeepCollectionEquality().hash(_userAsserts),
      const DeepCollectionEquality().hash(_completedNotClaimedTasks));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TaskCenterDataCopyWith<_$_TaskCenterData> get copyWith =>
      __$$_TaskCenterDataCopyWithImpl<_$_TaskCenterData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TaskCenterDataToJson(
      this,
    );
  }
}

abstract class _TaskCenterData implements TaskCenterData {
  factory _TaskCenterData(
      {final List<TodayTasksItem>? todayTasks,
      final bool? normalTaskMarker,
      final List<UserAssertsItem>? userAsserts,
      final List<UserTasksItem>? completedNotClaimedTasks}) = _$_TaskCenterData;

  factory _TaskCenterData.fromJson(Map<String, dynamic> json) =
      _$_TaskCenterData.fromJson;

  @override
  List<TodayTasksItem>? get todayTasks;
  @override
  bool? get normalTaskMarker;
  @override
  List<UserAssertsItem>? get userAsserts;
  @override
  List<UserTasksItem>? get completedNotClaimedTasks;
  @override
  @JsonKey(ignore: true)
  _$$_TaskCenterDataCopyWith<_$_TaskCenterData> get copyWith =>
      throw _privateConstructorUsedError;
}

TodayTasksItem _$TodayTasksItemFromJson(Map<String, dynamic> json) {
  return _TodayTasksItem.fromJson(json);
}

/// @nodoc
mixin _$TodayTasksItem {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeName => throw _privateConstructorUsedError;
  String? get subjectTypeNameDesc => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get userActivityId => throw _privateConstructorUsedError;
  String? get todayRes => throw _privateConstructorUsedError;
  String? get beanPortalRes => throw _privateConstructorUsedError;
  List<TaskItem>? get taskList => throw _privateConstructorUsedError;
  UserClickField? get userClickField => throw _privateConstructorUsedError;
  String? get subjectTypeColor => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TodayTasksItemCopyWith<TodayTasksItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TodayTasksItemCopyWith<$Res> {
  factory $TodayTasksItemCopyWith(
          TodayTasksItem value, $Res Function(TodayTasksItem) then) =
      _$TodayTasksItemCopyWithImpl<$Res, TodayTasksItem>;
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectTypeName,
      String? subjectTypeNameDesc,
      int? classId,
      String? userActivityId,
      String? todayRes,
      String? beanPortalRes,
      List<TaskItem>? taskList,
      UserClickField? userClickField,
      String? subjectTypeColor});

  $UserClickFieldCopyWith<$Res>? get userClickField;
}

/// @nodoc
class _$TodayTasksItemCopyWithImpl<$Res, $Val extends TodayTasksItem>
    implements $TodayTasksItemCopyWith<$Res> {
  _$TodayTasksItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeName = freezed,
    Object? subjectTypeNameDesc = freezed,
    Object? classId = freezed,
    Object? userActivityId = freezed,
    Object? todayRes = freezed,
    Object? beanPortalRes = freezed,
    Object? taskList = freezed,
    Object? userClickField = freezed,
    Object? subjectTypeColor = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeNameDesc: freezed == subjectTypeNameDesc
          ? _value.subjectTypeNameDesc
          : subjectTypeNameDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      userActivityId: freezed == userActivityId
          ? _value.userActivityId
          : userActivityId // ignore: cast_nullable_to_non_nullable
              as String?,
      todayRes: freezed == todayRes
          ? _value.todayRes
          : todayRes // ignore: cast_nullable_to_non_nullable
              as String?,
      beanPortalRes: freezed == beanPortalRes
          ? _value.beanPortalRes
          : beanPortalRes // ignore: cast_nullable_to_non_nullable
              as String?,
      taskList: freezed == taskList
          ? _value.taskList
          : taskList // ignore: cast_nullable_to_non_nullable
              as List<TaskItem>?,
      userClickField: freezed == userClickField
          ? _value.userClickField
          : userClickField // ignore: cast_nullable_to_non_nullable
              as UserClickField?,
      subjectTypeColor: freezed == subjectTypeColor
          ? _value.subjectTypeColor
          : subjectTypeColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserClickFieldCopyWith<$Res>? get userClickField {
    if (_value.userClickField == null) {
      return null;
    }

    return $UserClickFieldCopyWith<$Res>(_value.userClickField!, (value) {
      return _then(_value.copyWith(userClickField: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TodayTasksItemCopyWith<$Res>
    implements $TodayTasksItemCopyWith<$Res> {
  factory _$$_TodayTasksItemCopyWith(
          _$_TodayTasksItem value, $Res Function(_$_TodayTasksItem) then) =
      __$$_TodayTasksItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectTypeName,
      String? subjectTypeNameDesc,
      int? classId,
      String? userActivityId,
      String? todayRes,
      String? beanPortalRes,
      List<TaskItem>? taskList,
      UserClickField? userClickField,
      String? subjectTypeColor});

  @override
  $UserClickFieldCopyWith<$Res>? get userClickField;
}

/// @nodoc
class __$$_TodayTasksItemCopyWithImpl<$Res>
    extends _$TodayTasksItemCopyWithImpl<$Res, _$_TodayTasksItem>
    implements _$$_TodayTasksItemCopyWith<$Res> {
  __$$_TodayTasksItemCopyWithImpl(
      _$_TodayTasksItem _value, $Res Function(_$_TodayTasksItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeName = freezed,
    Object? subjectTypeNameDesc = freezed,
    Object? classId = freezed,
    Object? userActivityId = freezed,
    Object? todayRes = freezed,
    Object? beanPortalRes = freezed,
    Object? taskList = freezed,
    Object? userClickField = freezed,
    Object? subjectTypeColor = freezed,
  }) {
    return _then(_$_TodayTasksItem(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeNameDesc: freezed == subjectTypeNameDesc
          ? _value.subjectTypeNameDesc
          : subjectTypeNameDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      userActivityId: freezed == userActivityId
          ? _value.userActivityId
          : userActivityId // ignore: cast_nullable_to_non_nullable
              as String?,
      todayRes: freezed == todayRes
          ? _value.todayRes
          : todayRes // ignore: cast_nullable_to_non_nullable
              as String?,
      beanPortalRes: freezed == beanPortalRes
          ? _value.beanPortalRes
          : beanPortalRes // ignore: cast_nullable_to_non_nullable
              as String?,
      taskList: freezed == taskList
          ? _value._taskList
          : taskList // ignore: cast_nullable_to_non_nullable
              as List<TaskItem>?,
      userClickField: freezed == userClickField
          ? _value.userClickField
          : userClickField // ignore: cast_nullable_to_non_nullable
              as UserClickField?,
      subjectTypeColor: freezed == subjectTypeColor
          ? _value.subjectTypeColor
          : subjectTypeColor // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TodayTasksItem implements _TodayTasksItem {
  _$_TodayTasksItem(
      {this.subjectType,
      this.subjectTypeName,
      this.subjectTypeNameDesc,
      this.classId,
      this.userActivityId,
      this.todayRes,
      this.beanPortalRes,
      final List<TaskItem>? taskList,
      this.userClickField,
      this.subjectTypeColor})
      : _taskList = taskList;

  factory _$_TodayTasksItem.fromJson(Map<String, dynamic> json) =>
      _$$_TodayTasksItemFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectTypeName;
  @override
  final String? subjectTypeNameDesc;
  @override
  final int? classId;
  @override
  final String? userActivityId;
  @override
  final String? todayRes;
  @override
  final String? beanPortalRes;
  final List<TaskItem>? _taskList;
  @override
  List<TaskItem>? get taskList {
    final value = _taskList;
    if (value == null) return null;
    if (_taskList is EqualUnmodifiableListView) return _taskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UserClickField? userClickField;
  @override
  final String? subjectTypeColor;

  @override
  String toString() {
    return 'TodayTasksItem(subjectType: $subjectType, subjectTypeName: $subjectTypeName, subjectTypeNameDesc: $subjectTypeNameDesc, classId: $classId, userActivityId: $userActivityId, todayRes: $todayRes, beanPortalRes: $beanPortalRes, taskList: $taskList, userClickField: $userClickField, subjectTypeColor: $subjectTypeColor)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TodayTasksItem &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeName, subjectTypeName) ||
                other.subjectTypeName == subjectTypeName) &&
            (identical(other.subjectTypeNameDesc, subjectTypeNameDesc) ||
                other.subjectTypeNameDesc == subjectTypeNameDesc) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.userActivityId, userActivityId) ||
                other.userActivityId == userActivityId) &&
            (identical(other.todayRes, todayRes) ||
                other.todayRes == todayRes) &&
            (identical(other.beanPortalRes, beanPortalRes) ||
                other.beanPortalRes == beanPortalRes) &&
            const DeepCollectionEquality().equals(other._taskList, _taskList) &&
            (identical(other.userClickField, userClickField) ||
                other.userClickField == userClickField) &&
            (identical(other.subjectTypeColor, subjectTypeColor) ||
                other.subjectTypeColor == subjectTypeColor));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectType,
      subjectTypeName,
      subjectTypeNameDesc,
      classId,
      userActivityId,
      todayRes,
      beanPortalRes,
      const DeepCollectionEquality().hash(_taskList),
      userClickField,
      subjectTypeColor);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TodayTasksItemCopyWith<_$_TodayTasksItem> get copyWith =>
      __$$_TodayTasksItemCopyWithImpl<_$_TodayTasksItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TodayTasksItemToJson(
      this,
    );
  }
}

abstract class _TodayTasksItem implements TodayTasksItem {
  factory _TodayTasksItem(
      {final int? subjectType,
      final String? subjectTypeName,
      final String? subjectTypeNameDesc,
      final int? classId,
      final String? userActivityId,
      final String? todayRes,
      final String? beanPortalRes,
      final List<TaskItem>? taskList,
      final UserClickField? userClickField,
      final String? subjectTypeColor}) = _$_TodayTasksItem;

  factory _TodayTasksItem.fromJson(Map<String, dynamic> json) =
      _$_TodayTasksItem.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectTypeName;
  @override
  String? get subjectTypeNameDesc;
  @override
  int? get classId;
  @override
  String? get userActivityId;
  @override
  String? get todayRes;
  @override
  String? get beanPortalRes;
  @override
  List<TaskItem>? get taskList;
  @override
  UserClickField? get userClickField;
  @override
  String? get subjectTypeColor;
  @override
  @JsonKey(ignore: true)
  _$$_TodayTasksItemCopyWith<_$_TodayTasksItem> get copyWith =>
      throw _privateConstructorUsedError;
}

TaskItem _$TaskItemFromJson(Map<String, dynamic> json) {
  return _TaskItem.fromJson(json);
}

/// @nodoc
mixin _$TaskItem {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get goFinishRoute => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  int? get isFinish => throw _privateConstructorUsedError;
  int? get isGet => throw _privateConstructorUsedError;
  String? get targetValue => throw _privateConstructorUsedError;
  String? get currentValue => throw _privateConstructorUsedError;
  int? get sourceType => throw _privateConstructorUsedError;
  List<RewardItem>? get rewardList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TaskItemCopyWith<TaskItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskItemCopyWith<$Res> {
  factory $TaskItemCopyWith(TaskItem value, $Res Function(TaskItem) then) =
      _$TaskItemCopyWithImpl<$Res, TaskItem>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? goFinishRoute,
      String? desc,
      int? isFinish,
      int? isGet,
      String? targetValue,
      String? currentValue,
      int? sourceType,
      List<RewardItem>? rewardList});
}

/// @nodoc
class _$TaskItemCopyWithImpl<$Res, $Val extends TaskItem>
    implements $TaskItemCopyWith<$Res> {
  _$TaskItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? goFinishRoute = freezed,
    Object? desc = freezed,
    Object? isFinish = freezed,
    Object? isGet = freezed,
    Object? targetValue = freezed,
    Object? currentValue = freezed,
    Object? sourceType = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      goFinishRoute: freezed == goFinishRoute
          ? _value.goFinishRoute
          : goFinishRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as String?,
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceType: freezed == sourceType
          ? _value.sourceType
          : sourceType // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<RewardItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TaskItemCopyWith<$Res> implements $TaskItemCopyWith<$Res> {
  factory _$$_TaskItemCopyWith(
          _$_TaskItem value, $Res Function(_$_TaskItem) then) =
      __$$_TaskItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? goFinishRoute,
      String? desc,
      int? isFinish,
      int? isGet,
      String? targetValue,
      String? currentValue,
      int? sourceType,
      List<RewardItem>? rewardList});
}

/// @nodoc
class __$$_TaskItemCopyWithImpl<$Res>
    extends _$TaskItemCopyWithImpl<$Res, _$_TaskItem>
    implements _$$_TaskItemCopyWith<$Res> {
  __$$_TaskItemCopyWithImpl(
      _$_TaskItem _value, $Res Function(_$_TaskItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? goFinishRoute = freezed,
    Object? desc = freezed,
    Object? isFinish = freezed,
    Object? isGet = freezed,
    Object? targetValue = freezed,
    Object? currentValue = freezed,
    Object? sourceType = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_$_TaskItem(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      goFinishRoute: freezed == goFinishRoute
          ? _value.goFinishRoute
          : goFinishRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as String?,
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceType: freezed == sourceType
          ? _value.sourceType
          : sourceType // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value._rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<RewardItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TaskItem implements _TaskItem {
  const _$_TaskItem(
      {this.id,
      this.name,
      this.goFinishRoute,
      this.desc,
      this.isFinish,
      this.isGet,
      this.targetValue,
      this.currentValue,
      this.sourceType,
      final List<RewardItem>? rewardList})
      : _rewardList = rewardList;

  factory _$_TaskItem.fromJson(Map<String, dynamic> json) =>
      _$$_TaskItemFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? goFinishRoute;
  @override
  final String? desc;
  @override
  final int? isFinish;
  @override
  final int? isGet;
  @override
  final String? targetValue;
  @override
  final String? currentValue;
  @override
  final int? sourceType;
  final List<RewardItem>? _rewardList;
  @override
  List<RewardItem>? get rewardList {
    final value = _rewardList;
    if (value == null) return null;
    if (_rewardList is EqualUnmodifiableListView) return _rewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TaskItem(id: $id, name: $name, goFinishRoute: $goFinishRoute, desc: $desc, isFinish: $isFinish, isGet: $isGet, targetValue: $targetValue, currentValue: $currentValue, sourceType: $sourceType, rewardList: $rewardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TaskItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.goFinishRoute, goFinishRoute) ||
                other.goFinishRoute == goFinishRoute) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.isFinish, isFinish) ||
                other.isFinish == isFinish) &&
            (identical(other.isGet, isGet) || other.isGet == isGet) &&
            (identical(other.targetValue, targetValue) ||
                other.targetValue == targetValue) &&
            (identical(other.currentValue, currentValue) ||
                other.currentValue == currentValue) &&
            (identical(other.sourceType, sourceType) ||
                other.sourceType == sourceType) &&
            const DeepCollectionEquality()
                .equals(other._rewardList, _rewardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      goFinishRoute,
      desc,
      isFinish,
      isGet,
      targetValue,
      currentValue,
      sourceType,
      const DeepCollectionEquality().hash(_rewardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TaskItemCopyWith<_$_TaskItem> get copyWith =>
      __$$_TaskItemCopyWithImpl<_$_TaskItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TaskItemToJson(
      this,
    );
  }
}

abstract class _TaskItem implements TaskItem {
  const factory _TaskItem(
      {final String? id,
      final String? name,
      final String? goFinishRoute,
      final String? desc,
      final int? isFinish,
      final int? isGet,
      final String? targetValue,
      final String? currentValue,
      final int? sourceType,
      final List<RewardItem>? rewardList}) = _$_TaskItem;

  factory _TaskItem.fromJson(Map<String, dynamic> json) = _$_TaskItem.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  String? get goFinishRoute;
  @override
  String? get desc;
  @override
  int? get isFinish;
  @override
  int? get isGet;
  @override
  String? get targetValue;
  @override
  String? get currentValue;
  @override
  int? get sourceType;
  @override
  List<RewardItem>? get rewardList;
  @override
  @JsonKey(ignore: true)
  _$$_TaskItemCopyWith<_$_TaskItem> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardItem _$RewardItemFromJson(Map<String, dynamic> json) {
  return _RewardItem.fromJson(json);
}

/// @nodoc
mixin _$RewardItem {
  String? get id => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get value => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get picUrl => throw _privateConstructorUsedError;
  int? get sourceType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardItemCopyWith<RewardItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardItemCopyWith<$Res> {
  factory $RewardItemCopyWith(
          RewardItem value, $Res Function(RewardItem) then) =
      _$RewardItemCopyWithImpl<$Res, RewardItem>;
  @useResult
  $Res call(
      {String? id,
      int? type,
      int? value,
      String? name,
      String? picUrl,
      int? sourceType});
}

/// @nodoc
class _$RewardItemCopyWithImpl<$Res, $Val extends RewardItem>
    implements $RewardItemCopyWith<$Res> {
  _$RewardItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? value = freezed,
    Object? name = freezed,
    Object? picUrl = freezed,
    Object? sourceType = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceType: freezed == sourceType
          ? _value.sourceType
          : sourceType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RewardItemCopyWith<$Res>
    implements $RewardItemCopyWith<$Res> {
  factory _$$_RewardItemCopyWith(
          _$_RewardItem value, $Res Function(_$_RewardItem) then) =
      __$$_RewardItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? type,
      int? value,
      String? name,
      String? picUrl,
      int? sourceType});
}

/// @nodoc
class __$$_RewardItemCopyWithImpl<$Res>
    extends _$RewardItemCopyWithImpl<$Res, _$_RewardItem>
    implements _$$_RewardItemCopyWith<$Res> {
  __$$_RewardItemCopyWithImpl(
      _$_RewardItem _value, $Res Function(_$_RewardItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? value = freezed,
    Object? name = freezed,
    Object? picUrl = freezed,
    Object? sourceType = freezed,
  }) {
    return _then(_$_RewardItem(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceType: freezed == sourceType
          ? _value.sourceType
          : sourceType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RewardItem implements _RewardItem {
  _$_RewardItem(
      {this.id,
      this.type,
      this.value,
      this.name,
      this.picUrl,
      this.sourceType});

  factory _$_RewardItem.fromJson(Map<String, dynamic> json) =>
      _$$_RewardItemFromJson(json);

  @override
  final String? id;
  @override
  final int? type;
  @override
  final int? value;
  @override
  final String? name;
  @override
  final String? picUrl;
  @override
  final int? sourceType;

  @override
  String toString() {
    return 'RewardItem(id: $id, type: $type, value: $value, name: $name, picUrl: $picUrl, sourceType: $sourceType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RewardItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.picUrl, picUrl) || other.picUrl == picUrl) &&
            (identical(other.sourceType, sourceType) ||
                other.sourceType == sourceType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, type, value, name, picUrl, sourceType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RewardItemCopyWith<_$_RewardItem> get copyWith =>
      __$$_RewardItemCopyWithImpl<_$_RewardItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RewardItemToJson(
      this,
    );
  }
}

abstract class _RewardItem implements RewardItem {
  factory _RewardItem(
      {final String? id,
      final int? type,
      final int? value,
      final String? name,
      final String? picUrl,
      final int? sourceType}) = _$_RewardItem;

  factory _RewardItem.fromJson(Map<String, dynamic> json) =
      _$_RewardItem.fromJson;

  @override
  String? get id;
  @override
  int? get type;
  @override
  int? get value;
  @override
  String? get name;
  @override
  String? get picUrl;
  @override
  int? get sourceType;
  @override
  @JsonKey(ignore: true)
  _$$_RewardItemCopyWith<_$_RewardItem> get copyWith =>
      throw _privateConstructorUsedError;
}

UserClickField _$UserClickFieldFromJson(Map<String, dynamic> json) {
  return _UserClickField.fromJson(json);
}

/// @nodoc
mixin _$UserClickField {
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserClickFieldCopyWith<UserClickField> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserClickFieldCopyWith<$Res> {
  factory $UserClickFieldCopyWith(
          UserClickField value, $Res Function(UserClickField) then) =
      _$UserClickFieldCopyWithImpl<$Res, UserClickField>;
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      int? classId,
      String? classKey});
}

/// @nodoc
class _$UserClickFieldCopyWithImpl<$Res, $Val extends UserClickField>
    implements $UserClickFieldCopyWith<$Res> {
  _$UserClickFieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
  }) {
    return _then(_value.copyWith(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserClickFieldCopyWith<$Res>
    implements $UserClickFieldCopyWith<$Res> {
  factory _$$_UserClickFieldCopyWith(
          _$_UserClickField value, $Res Function(_$_UserClickField) then) =
      __$$_UserClickFieldCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? courseId,
      String? courseKey,
      String? courseName,
      int? classId,
      String? classKey});
}

/// @nodoc
class __$$_UserClickFieldCopyWithImpl<$Res>
    extends _$UserClickFieldCopyWithImpl<$Res, _$_UserClickField>
    implements _$$_UserClickFieldCopyWith<$Res> {
  __$$_UserClickFieldCopyWithImpl(
      _$_UserClickField _value, $Res Function(_$_UserClickField) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
  }) {
    return _then(_$_UserClickField(
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserClickField implements _UserClickField {
  _$_UserClickField(
      {this.courseId,
      this.courseKey,
      this.courseName,
      this.classId,
      this.classKey});

  factory _$_UserClickField.fromJson(Map<String, dynamic> json) =>
      _$$_UserClickFieldFromJson(json);

  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final int? classId;
  @override
  final String? classKey;

  @override
  String toString() {
    return 'UserClickField(courseId: $courseId, courseKey: $courseKey, courseName: $courseName, classId: $classId, classKey: $classKey)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserClickField &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, courseId, courseKey, courseName, classId, classKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserClickFieldCopyWith<_$_UserClickField> get copyWith =>
      __$$_UserClickFieldCopyWithImpl<_$_UserClickField>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserClickFieldToJson(
      this,
    );
  }
}

abstract class _UserClickField implements UserClickField {
  factory _UserClickField(
      {final int? courseId,
      final String? courseKey,
      final String? courseName,
      final int? classId,
      final String? classKey}) = _$_UserClickField;

  factory _UserClickField.fromJson(Map<String, dynamic> json) =
      _$_UserClickField.fromJson;

  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  @JsonKey(ignore: true)
  _$$_UserClickFieldCopyWith<_$_UserClickField> get copyWith =>
      throw _privateConstructorUsedError;
}

UserAssertsItem _$UserAssertsItemFromJson(Map<String, dynamic> json) {
  return _UserAssertsItem.fromJson(json);
}

/// @nodoc
mixin _$UserAssertsItem {
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;
  String? get picUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserAssertsItemCopyWith<UserAssertsItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserAssertsItemCopyWith<$Res> {
  factory $UserAssertsItemCopyWith(
          UserAssertsItem value, $Res Function(UserAssertsItem) then) =
      _$UserAssertsItemCopyWithImpl<$Res, UserAssertsItem>;
  @useResult
  $Res call({int? type, String? name, int? totalCount, String? picUrl});
}

/// @nodoc
class _$UserAssertsItemCopyWithImpl<$Res, $Val extends UserAssertsItem>
    implements $UserAssertsItemCopyWith<$Res> {
  _$UserAssertsItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? totalCount = freezed,
    Object? picUrl = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserAssertsItemCopyWith<$Res>
    implements $UserAssertsItemCopyWith<$Res> {
  factory _$$_UserAssertsItemCopyWith(
          _$_UserAssertsItem value, $Res Function(_$_UserAssertsItem) then) =
      __$$_UserAssertsItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? name, int? totalCount, String? picUrl});
}

/// @nodoc
class __$$_UserAssertsItemCopyWithImpl<$Res>
    extends _$UserAssertsItemCopyWithImpl<$Res, _$_UserAssertsItem>
    implements _$$_UserAssertsItemCopyWith<$Res> {
  __$$_UserAssertsItemCopyWithImpl(
      _$_UserAssertsItem _value, $Res Function(_$_UserAssertsItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? totalCount = freezed,
    Object? picUrl = freezed,
  }) {
    return _then(_$_UserAssertsItem(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserAssertsItem implements _UserAssertsItem {
  _$_UserAssertsItem({this.type, this.name, this.totalCount, this.picUrl});

  factory _$_UserAssertsItem.fromJson(Map<String, dynamic> json) =>
      _$$_UserAssertsItemFromJson(json);

  @override
  final int? type;
  @override
  final String? name;
  @override
  final int? totalCount;
  @override
  final String? picUrl;

  @override
  String toString() {
    return 'UserAssertsItem(type: $type, name: $name, totalCount: $totalCount, picUrl: $picUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserAssertsItem &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.picUrl, picUrl) || other.picUrl == picUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, name, totalCount, picUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserAssertsItemCopyWith<_$_UserAssertsItem> get copyWith =>
      __$$_UserAssertsItemCopyWithImpl<_$_UserAssertsItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserAssertsItemToJson(
      this,
    );
  }
}

abstract class _UserAssertsItem implements UserAssertsItem {
  factory _UserAssertsItem(
      {final int? type,
      final String? name,
      final int? totalCount,
      final String? picUrl}) = _$_UserAssertsItem;

  factory _UserAssertsItem.fromJson(Map<String, dynamic> json) =
      _$_UserAssertsItem.fromJson;

  @override
  int? get type;
  @override
  String? get name;
  @override
  int? get totalCount;
  @override
  String? get picUrl;
  @override
  @JsonKey(ignore: true)
  _$$_UserAssertsItemCopyWith<_$_UserAssertsItem> get copyWith =>
      throw _privateConstructorUsedError;
}

MissionCentersData _$MissionCentersDataFromJson(Map<String, dynamic> json) {
  return _MissionCentersData.fromJson(json);
}

/// @nodoc
mixin _$MissionCentersData {
  List<UserTasksItem>? get userTasks => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MissionCentersDataCopyWith<MissionCentersData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MissionCentersDataCopyWith<$Res> {
  factory $MissionCentersDataCopyWith(
          MissionCentersData value, $Res Function(MissionCentersData) then) =
      _$MissionCentersDataCopyWithImpl<$Res, MissionCentersData>;
  @useResult
  $Res call({List<UserTasksItem>? userTasks});
}

/// @nodoc
class _$MissionCentersDataCopyWithImpl<$Res, $Val extends MissionCentersData>
    implements $MissionCentersDataCopyWith<$Res> {
  _$MissionCentersDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userTasks = freezed,
  }) {
    return _then(_value.copyWith(
      userTasks: freezed == userTasks
          ? _value.userTasks
          : userTasks // ignore: cast_nullable_to_non_nullable
              as List<UserTasksItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MissionCentersDataCopyWith<$Res>
    implements $MissionCentersDataCopyWith<$Res> {
  factory _$$_MissionCentersDataCopyWith(_$_MissionCentersData value,
          $Res Function(_$_MissionCentersData) then) =
      __$$_MissionCentersDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<UserTasksItem>? userTasks});
}

/// @nodoc
class __$$_MissionCentersDataCopyWithImpl<$Res>
    extends _$MissionCentersDataCopyWithImpl<$Res, _$_MissionCentersData>
    implements _$$_MissionCentersDataCopyWith<$Res> {
  __$$_MissionCentersDataCopyWithImpl(
      _$_MissionCentersData _value, $Res Function(_$_MissionCentersData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userTasks = freezed,
  }) {
    return _then(_$_MissionCentersData(
      userTasks: freezed == userTasks
          ? _value._userTasks
          : userTasks // ignore: cast_nullable_to_non_nullable
              as List<UserTasksItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MissionCentersData implements _MissionCentersData {
  _$_MissionCentersData({final List<UserTasksItem>? userTasks})
      : _userTasks = userTasks;

  factory _$_MissionCentersData.fromJson(Map<String, dynamic> json) =>
      _$$_MissionCentersDataFromJson(json);

  final List<UserTasksItem>? _userTasks;
  @override
  List<UserTasksItem>? get userTasks {
    final value = _userTasks;
    if (value == null) return null;
    if (_userTasks is EqualUnmodifiableListView) return _userTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MissionCentersData(userTasks: $userTasks)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MissionCentersData &&
            const DeepCollectionEquality()
                .equals(other._userTasks, _userTasks));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_userTasks));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MissionCentersDataCopyWith<_$_MissionCentersData> get copyWith =>
      __$$_MissionCentersDataCopyWithImpl<_$_MissionCentersData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MissionCentersDataToJson(
      this,
    );
  }
}

abstract class _MissionCentersData implements MissionCentersData {
  factory _MissionCentersData({final List<UserTasksItem>? userTasks}) =
      _$_MissionCentersData;

  factory _MissionCentersData.fromJson(Map<String, dynamic> json) =
      _$_MissionCentersData.fromJson;

  @override
  List<UserTasksItem>? get userTasks;
  @override
  @JsonKey(ignore: true)
  _$$_MissionCentersDataCopyWith<_$_MissionCentersData> get copyWith =>
      throw _privateConstructorUsedError;
}

UserTasksItem _$UserTasksItemFromJson(Map<String, dynamic> json) {
  return _UserTasksItem.fromJson(json);
}

/// @nodoc
mixin _$UserTasksItem {
  String? get userTaskId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserTasksItemCopyWith<UserTasksItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserTasksItemCopyWith<$Res> {
  factory $UserTasksItemCopyWith(
          UserTasksItem value, $Res Function(UserTasksItem) then) =
      _$UserTasksItemCopyWithImpl<$Res, UserTasksItem>;
  @useResult
  $Res call({String? userTaskId});
}

/// @nodoc
class _$UserTasksItemCopyWithImpl<$Res, $Val extends UserTasksItem>
    implements $UserTasksItemCopyWith<$Res> {
  _$UserTasksItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userTaskId = freezed,
  }) {
    return _then(_value.copyWith(
      userTaskId: freezed == userTaskId
          ? _value.userTaskId
          : userTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserTasksItemCopyWith<$Res>
    implements $UserTasksItemCopyWith<$Res> {
  factory _$$_UserTasksItemCopyWith(
          _$_UserTasksItem value, $Res Function(_$_UserTasksItem) then) =
      __$$_UserTasksItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? userTaskId});
}

/// @nodoc
class __$$_UserTasksItemCopyWithImpl<$Res>
    extends _$UserTasksItemCopyWithImpl<$Res, _$_UserTasksItem>
    implements _$$_UserTasksItemCopyWith<$Res> {
  __$$_UserTasksItemCopyWithImpl(
      _$_UserTasksItem _value, $Res Function(_$_UserTasksItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userTaskId = freezed,
  }) {
    return _then(_$_UserTasksItem(
      userTaskId: freezed == userTaskId
          ? _value.userTaskId
          : userTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserTasksItem implements _UserTasksItem {
  _$_UserTasksItem({this.userTaskId});

  factory _$_UserTasksItem.fromJson(Map<String, dynamic> json) =>
      _$$_UserTasksItemFromJson(json);

  @override
  final String? userTaskId;

  @override
  String toString() {
    return 'UserTasksItem(userTaskId: $userTaskId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserTasksItem &&
            (identical(other.userTaskId, userTaskId) ||
                other.userTaskId == userTaskId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userTaskId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserTasksItemCopyWith<_$_UserTasksItem> get copyWith =>
      __$$_UserTasksItemCopyWithImpl<_$_UserTasksItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserTasksItemToJson(
      this,
    );
  }
}

abstract class _UserTasksItem implements UserTasksItem {
  factory _UserTasksItem({final String? userTaskId}) = _$_UserTasksItem;

  factory _UserTasksItem.fromJson(Map<String, dynamic> json) =
      _$_UserTasksItem.fromJson;

  @override
  String? get userTaskId;
  @override
  @JsonKey(ignore: true)
  _$$_UserTasksItemCopyWith<_$_UserTasksItem> get copyWith =>
      throw _privateConstructorUsedError;
}
