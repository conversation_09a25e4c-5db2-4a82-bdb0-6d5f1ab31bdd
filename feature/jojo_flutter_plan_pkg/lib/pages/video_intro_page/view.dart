import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/widget/video/video_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/video_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/state.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:video_player/video_player.dart';

class VideoIntroducePageView extends StatefulWidget {

  final VideoIntroduceState state;
  final int? isFromDetailPage;

  const VideoIntroducePageView({super.key, required this.state, required this.isFromDetailPage});

  @override
  State<StatefulWidget> createState() {
    return VideoIntroducePageViewState();
  }
}

class VideoIntroducePageViewState extends State<VideoIntroducePageView>{

  late VideoData _videoData;
  VideoController? videoController;
  StreamSubscription? _lifeCycleEventBus;
  late Future<void> _initializeVideoPlayerFuture;
  bool _isPlayEnd = false;

  @override
  void initState() {
    super.initState();
    _initEventBus();
    VideoPlayerController videoPlayController = _initPlayer();
    _initVideoData(videoPlayController);
    videoController?.updateVideoData(context, _videoData);
  }

  @override
  void dispose() {
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _videoData.controller.removeListener(_videoCallback);
    videoController?.updateVideoData(context, null);
    super.dispose();
  }

  void _initEventBus() {
    //监听生命周期事件
    _lifeCycleEventBus = jojoEventBus.on<EventBusVideoIntroduceLifecycleData>().listen((event) async {
      if (event.lifecycleType == LifecycleType.resumed) {
        _videoData.isVisible = true;
        setState(() {
          _videoData.play();
        });
      } else {
        _pausePlayer();
      }
    });
    // 浏览埋点
    if (widget.state.dataString != null) {
      Map<String, dynamic> buriedMap = jsonDecode(widget.state.dataString!);
      Map<String, dynamic> properties = {
        '\$screen_name': "2025改版后学习页",
        'c_element_name': "完课活动引入视频_曝光",
        ...buriedMap,
      };
      RunEnv.sensorsTrack('ElementView',properties,);
    }
  }

  Widget _buildCancelWidget() {
    return Visibility(
      visible: _isPlayEnd && (widget.isFromDetailPage ?? 0) != 1,
      child: GestureDetector(
      onTap: () {
        if (mounted == false) return;
        VideoIntroduceCtrl ctrl = context.read<VideoIntroduceCtrl>();
        ctrl.closeBtnOnclick();
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        child: Container(
      decoration: BoxDecoration(
          color: HexColor('#FCDA00'),
          borderRadius: BorderRadius.circular(20.rdp)),
      width: 160.rdp,
      height: 40.rdp,
      alignment: Alignment.center,
      child: Center(
        child: Text(
          S.of(context).iamGood,
          style: TextStyle(
            fontSize: 18.rdp,
            color: HexColor('#544300'),
            fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    ),
      ),
    ));
  }

  VideoPlayerController _initPlayer() {
    final videoPlayController = VideoPlayerController.networkUrl(
      Uri.parse(widget.state.url ?? ""),
    )
    ..setLooping(false)
    ..setVolume(1)
    ..addListener(_videoCallback);

    _initializeVideoPlayerFuture = videoPlayController.initialize().then((value) {
      setState(() {
          _videoData.play();
        });
    }).onError((error, stackTrace) {
      try {
        _videoData.errorCallback = _errorCallback;
        setState(() {});
      } catch (e) {
        l.e("促完课视频播放页", "initialize 出错 error=$e");
      }
    });

    return videoPlayController;
  }

  void _initVideoData(VideoPlayerController videoPlayController) {
    _videoData = VideoData(
        videoUrl: widget.state.url ?? "",
        imageUrl: "",
        context: context,
        controller: videoPlayController,
        errorCallback: null,
        refreshCallback: (action) {
          setState(() {
            action.call();
          });
        });
  }

  Future<void> _pausePlayer() async {
    _videoData.shouldPlay = false;
    _videoData.isVisible = false;
    try {
      await _videoData.controller.pause();
    } catch (e) {
      l.e("促完课视频播放页", "暂停异常 error=$e");
    }
  }

  void _videoCallback() {
    if (_videoData.controller.value.isCompleted) {
      setState(() {
        _isPlayEnd = true;
      });
    }

    if (_videoData.controller.value.hasError) {
      _videoData.errorCallback = _errorCallback;
      setState(() {});
    }

    if (_videoData.hasBuffer && !_videoData.controller.value.isPlaying) {
      _videoData.shouldPlay = false;
      _videoData.hasBuffer = false;
    }

    if (!_videoData.hasBuffer && _videoData.controller.value.isPlaying && _videoData.canSetBuffer()) {
      _videoData.hasBuffer = _videoData.controller.value.buffered.isNotEmpty;
      if (_videoData.hasBuffer) {
        setState(() {});
      }
    }
  }

  void _errorCallback() {
    // 释放资源
    _videoData.controller.removeListener(_videoCallback);
    _videoData.controller.dispose();

    // 重新初始化
    final videoPlayController = _initPlayer();
    _videoData.controller = videoPlayController;
    setState(() {});
  }

  Widget _buildBackBtnWidget() {
    return GestureDetector(
      onTap: () {
        if (mounted == false) return;
        VideoIntroduceCtrl ctrl = context.read<VideoIntroduceCtrl>();
        ctrl.closeBtnOnclick();
      },
      child: ImageAssetWeb(
          assetName: AssetsImg.PLAN_IMAGE_PROMOTE_GUIDE_DIALOG_BACK_ICON,
          width: 60.rdp,
          height: 60.rdp,
          package: Config.package,
        )
    );
  }

  Widget _buildVideoWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Center(
      child: FutureBuilder<void>(
      future: _initializeVideoPlayerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          // 获取视频的宽高比
          final aspectRatio = _videoData.controller.value.aspectRatio;
          // 计算视频的显示尺寸，宽度和高度根据屏幕比例调整
          double videoWidth = screenWidth;
          double videoHeight = aspectRatio > 0? videoWidth / aspectRatio : videoWidth;
          return SizedBox(
            width: videoWidth,
            height: videoHeight,
              child: AspectRatio(
                aspectRatio: aspectRatio,
                child: VideoPlayer(_videoData.controller),
              ),
            );
          } else {
            return const CircularProgressIndicator();
          }
        },
    ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      child: Stack(
        children: [
          Positioned.fill(
            child: _buildVideoWidget(),
          ),
          SafeArea(
        child: Stack(
          children: [
            Positioned(
              left: 10.rdp,
              child: _buildBackBtnWidget()),
            Positioned(
              left: 0.rdp,
              right: 0.rdp,
              bottom: 30.rdp,
              child: _buildCancelWidget()
            ),
          ],
        ),
      )
        ],
      ),
    );
  }
}
