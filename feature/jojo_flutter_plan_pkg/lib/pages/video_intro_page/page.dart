import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/view.dart';

class VideoIntroducePageModel extends BasePage {
  final String? url;
  final String? dataString;
  final int? isFromDetailPage;

  const VideoIntroducePageModel({Key? key, this.url, this.dataString, this.isFromDetailPage}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _VideoIntroducePageModelState();
}

class _VideoIntroducePageModelState extends BaseState<VideoIntroducePageModel> with BasicInitPage {

  @override
  void onResume() {
    super.onResume();
    jojoEventBus.fire(EventBusVideoIntroduceLifecycleData(LifecycleType.resumed));
  }
  
  @override
  void onPause() {
    super.onPause();
    jojoEventBus.fire(EventBusVideoIntroduceLifecycleData(LifecycleType.paused));
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => VideoIntroduceCtrl(url: widget.url, dataString: widget.dataString, isFromDetailPage: widget.isFromDetailPage),
      child: BlocBuilder<VideoIntroduceCtrl, VideoIntroduceState>(
          builder: (context, state) {
        return VideoIntroducePageView(state: state, isFromDetailPage: widget.isFromDetailPage,);
      }),
    );
  }
}
