
import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/debouncer.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/state.dart';

class VideoIntroduceCtrl extends Cubit<VideoIntroduceState> {
  final String? url;
  final int? isFromDetailPage;
  final String? dataString;

  VideoIntroduceCtrl({required this.url, required this.dataString, required this.isFromDetailPage}) : super(VideoIntroduceState(pageStatus: PageStatus.success,url: url, dataString: dataString),);

  /// 点击按钮关闭页面
  closeBtnOnclick() {
    // 按钮点击埋点
    if (dataString != null) {
      Map<String, dynamic> buriedMap = jsonDecode(dataString!);
      Map<String, dynamic> properties = {
        '\$screen_name': "2025改版后学习页",
        '\$element_name': "完课活动引入视频_按钮点击",
        ...buriedMap,
      };
      RunEnv.sensorsTrack('\$AppClick', properties);
    }
    // 返回
    debouncer.run(() {
      JoJoRouter.pop();
    });
  }
}

/// 生命周期通知
class EventBusVideoIntroduceLifecycleData {
  final LifecycleType lifecycleType;
  EventBusVideoIntroduceLifecycleData(this.lifecycleType);
}
