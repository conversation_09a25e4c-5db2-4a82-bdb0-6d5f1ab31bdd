// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mine_honor_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MineHonorData _$MineHonorDataFromJson(Map<String, dynamic> json) {
  return _MineHonorData.fromJson(json);
}

/// @nodoc
mixin _$MineHonorData {
  int? get certificateCount => throw _privateConstructorUsedError;
  String? get certificateCountCopywriting => throw _privateConstructorUsedError;
  String? get certificateCopywriting => throw _privateConstructorUsedError;
  String? get certificateShareBtn => throw _privateConstructorUsedError;
  bool? get sharedStatus => throw _privateConstructorUsedError;
  bool? get isSelf => throw _privateConstructorUsedError;
  String? get receiveCourseUrl => throw _privateConstructorUsedError;
  BabyInfo? get babyInfo => throw _privateConstructorUsedError;
  CertificateWallShareContent? get certificateWallShareContent =>
      throw _privateConstructorUsedError;
  List<HonorCertificateList>? get honorCertificateList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MineHonorDataCopyWith<MineHonorData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MineHonorDataCopyWith<$Res> {
  factory $MineHonorDataCopyWith(
          MineHonorData value, $Res Function(MineHonorData) then) =
      _$MineHonorDataCopyWithImpl<$Res, MineHonorData>;
  @useResult
  $Res call(
      {int? certificateCount,
      String? certificateCountCopywriting,
      String? certificateCopywriting,
      String? certificateShareBtn,
      bool? sharedStatus,
      bool? isSelf,
      String? receiveCourseUrl,
      BabyInfo? babyInfo,
      CertificateWallShareContent? certificateWallShareContent,
      List<HonorCertificateList>? honorCertificateList});

  $BabyInfoCopyWith<$Res>? get babyInfo;
  $CertificateWallShareContentCopyWith<$Res>? get certificateWallShareContent;
}

/// @nodoc
class _$MineHonorDataCopyWithImpl<$Res, $Val extends MineHonorData>
    implements $MineHonorDataCopyWith<$Res> {
  _$MineHonorDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificateCount = freezed,
    Object? certificateCountCopywriting = freezed,
    Object? certificateCopywriting = freezed,
    Object? certificateShareBtn = freezed,
    Object? sharedStatus = freezed,
    Object? isSelf = freezed,
    Object? receiveCourseUrl = freezed,
    Object? babyInfo = freezed,
    Object? certificateWallShareContent = freezed,
    Object? honorCertificateList = freezed,
  }) {
    return _then(_value.copyWith(
      certificateCount: freezed == certificateCount
          ? _value.certificateCount
          : certificateCount // ignore: cast_nullable_to_non_nullable
              as int?,
      certificateCountCopywriting: freezed == certificateCountCopywriting
          ? _value.certificateCountCopywriting
          : certificateCountCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateCopywriting: freezed == certificateCopywriting
          ? _value.certificateCopywriting
          : certificateCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateShareBtn: freezed == certificateShareBtn
          ? _value.certificateShareBtn
          : certificateShareBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedStatus: freezed == sharedStatus
          ? _value.sharedStatus
          : sharedStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      receiveCourseUrl: freezed == receiveCourseUrl
          ? _value.receiveCourseUrl
          : receiveCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as BabyInfo?,
      certificateWallShareContent: freezed == certificateWallShareContent
          ? _value.certificateWallShareContent
          : certificateWallShareContent // ignore: cast_nullable_to_non_nullable
              as CertificateWallShareContent?,
      honorCertificateList: freezed == honorCertificateList
          ? _value.honorCertificateList
          : honorCertificateList // ignore: cast_nullable_to_non_nullable
              as List<HonorCertificateList>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BabyInfoCopyWith<$Res>? get babyInfo {
    if (_value.babyInfo == null) {
      return null;
    }

    return $BabyInfoCopyWith<$Res>(_value.babyInfo!, (value) {
      return _then(_value.copyWith(babyInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CertificateWallShareContentCopyWith<$Res>? get certificateWallShareContent {
    if (_value.certificateWallShareContent == null) {
      return null;
    }

    return $CertificateWallShareContentCopyWith<$Res>(
        _value.certificateWallShareContent!, (value) {
      return _then(_value.copyWith(certificateWallShareContent: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MineHonorDataCopyWith<$Res>
    implements $MineHonorDataCopyWith<$Res> {
  factory _$$_MineHonorDataCopyWith(
          _$_MineHonorData value, $Res Function(_$_MineHonorData) then) =
      __$$_MineHonorDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? certificateCount,
      String? certificateCountCopywriting,
      String? certificateCopywriting,
      String? certificateShareBtn,
      bool? sharedStatus,
      bool? isSelf,
      String? receiveCourseUrl,
      BabyInfo? babyInfo,
      CertificateWallShareContent? certificateWallShareContent,
      List<HonorCertificateList>? honorCertificateList});

  @override
  $BabyInfoCopyWith<$Res>? get babyInfo;
  @override
  $CertificateWallShareContentCopyWith<$Res>? get certificateWallShareContent;
}

/// @nodoc
class __$$_MineHonorDataCopyWithImpl<$Res>
    extends _$MineHonorDataCopyWithImpl<$Res, _$_MineHonorData>
    implements _$$_MineHonorDataCopyWith<$Res> {
  __$$_MineHonorDataCopyWithImpl(
      _$_MineHonorData _value, $Res Function(_$_MineHonorData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? certificateCount = freezed,
    Object? certificateCountCopywriting = freezed,
    Object? certificateCopywriting = freezed,
    Object? certificateShareBtn = freezed,
    Object? sharedStatus = freezed,
    Object? isSelf = freezed,
    Object? receiveCourseUrl = freezed,
    Object? babyInfo = freezed,
    Object? certificateWallShareContent = freezed,
    Object? honorCertificateList = freezed,
  }) {
    return _then(_$_MineHonorData(
      certificateCount: freezed == certificateCount
          ? _value.certificateCount
          : certificateCount // ignore: cast_nullable_to_non_nullable
              as int?,
      certificateCountCopywriting: freezed == certificateCountCopywriting
          ? _value.certificateCountCopywriting
          : certificateCountCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateCopywriting: freezed == certificateCopywriting
          ? _value.certificateCopywriting
          : certificateCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateShareBtn: freezed == certificateShareBtn
          ? _value.certificateShareBtn
          : certificateShareBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedStatus: freezed == sharedStatus
          ? _value.sharedStatus
          : sharedStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      receiveCourseUrl: freezed == receiveCourseUrl
          ? _value.receiveCourseUrl
          : receiveCourseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as BabyInfo?,
      certificateWallShareContent: freezed == certificateWallShareContent
          ? _value.certificateWallShareContent
          : certificateWallShareContent // ignore: cast_nullable_to_non_nullable
              as CertificateWallShareContent?,
      honorCertificateList: freezed == honorCertificateList
          ? _value._honorCertificateList
          : honorCertificateList // ignore: cast_nullable_to_non_nullable
              as List<HonorCertificateList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MineHonorData with DiagnosticableTreeMixin implements _MineHonorData {
  const _$_MineHonorData(
      {this.certificateCount,
      this.certificateCountCopywriting,
      this.certificateCopywriting,
      this.certificateShareBtn,
      this.sharedStatus,
      this.isSelf,
      this.receiveCourseUrl,
      this.babyInfo,
      this.certificateWallShareContent,
      final List<HonorCertificateList>? honorCertificateList})
      : _honorCertificateList = honorCertificateList;

  factory _$_MineHonorData.fromJson(Map<String, dynamic> json) =>
      _$$_MineHonorDataFromJson(json);

  @override
  final int? certificateCount;
  @override
  final String? certificateCountCopywriting;
  @override
  final String? certificateCopywriting;
  @override
  final String? certificateShareBtn;
  @override
  final bool? sharedStatus;
  @override
  final bool? isSelf;
  @override
  final String? receiveCourseUrl;
  @override
  final BabyInfo? babyInfo;
  @override
  final CertificateWallShareContent? certificateWallShareContent;
  final List<HonorCertificateList>? _honorCertificateList;
  @override
  List<HonorCertificateList>? get honorCertificateList {
    final value = _honorCertificateList;
    if (value == null) return null;
    if (_honorCertificateList is EqualUnmodifiableListView)
      return _honorCertificateList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MineHonorData(certificateCount: $certificateCount, certificateCountCopywriting: $certificateCountCopywriting, certificateCopywriting: $certificateCopywriting, certificateShareBtn: $certificateShareBtn, sharedStatus: $sharedStatus, isSelf: $isSelf, receiveCourseUrl: $receiveCourseUrl, babyInfo: $babyInfo, certificateWallShareContent: $certificateWallShareContent, honorCertificateList: $honorCertificateList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MineHonorData'))
      ..add(DiagnosticsProperty('certificateCount', certificateCount))
      ..add(DiagnosticsProperty(
          'certificateCountCopywriting', certificateCountCopywriting))
      ..add(
          DiagnosticsProperty('certificateCopywriting', certificateCopywriting))
      ..add(DiagnosticsProperty('certificateShareBtn', certificateShareBtn))
      ..add(DiagnosticsProperty('sharedStatus', sharedStatus))
      ..add(DiagnosticsProperty('isSelf', isSelf))
      ..add(DiagnosticsProperty('receiveCourseUrl', receiveCourseUrl))
      ..add(DiagnosticsProperty('babyInfo', babyInfo))
      ..add(DiagnosticsProperty(
          'certificateWallShareContent', certificateWallShareContent))
      ..add(DiagnosticsProperty('honorCertificateList', honorCertificateList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MineHonorData &&
            (identical(other.certificateCount, certificateCount) ||
                other.certificateCount == certificateCount) &&
            (identical(other.certificateCountCopywriting,
                    certificateCountCopywriting) ||
                other.certificateCountCopywriting ==
                    certificateCountCopywriting) &&
            (identical(other.certificateCopywriting, certificateCopywriting) ||
                other.certificateCopywriting == certificateCopywriting) &&
            (identical(other.certificateShareBtn, certificateShareBtn) ||
                other.certificateShareBtn == certificateShareBtn) &&
            (identical(other.sharedStatus, sharedStatus) ||
                other.sharedStatus == sharedStatus) &&
            (identical(other.isSelf, isSelf) || other.isSelf == isSelf) &&
            (identical(other.receiveCourseUrl, receiveCourseUrl) ||
                other.receiveCourseUrl == receiveCourseUrl) &&
            (identical(other.babyInfo, babyInfo) ||
                other.babyInfo == babyInfo) &&
            (identical(other.certificateWallShareContent,
                    certificateWallShareContent) ||
                other.certificateWallShareContent ==
                    certificateWallShareContent) &&
            const DeepCollectionEquality()
                .equals(other._honorCertificateList, _honorCertificateList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      certificateCount,
      certificateCountCopywriting,
      certificateCopywriting,
      certificateShareBtn,
      sharedStatus,
      isSelf,
      receiveCourseUrl,
      babyInfo,
      certificateWallShareContent,
      const DeepCollectionEquality().hash(_honorCertificateList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MineHonorDataCopyWith<_$_MineHonorData> get copyWith =>
      __$$_MineHonorDataCopyWithImpl<_$_MineHonorData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MineHonorDataToJson(
      this,
    );
  }
}

abstract class _MineHonorData implements MineHonorData {
  const factory _MineHonorData(
          {final int? certificateCount,
          final String? certificateCountCopywriting,
          final String? certificateCopywriting,
          final String? certificateShareBtn,
          final bool? sharedStatus,
          final bool? isSelf,
          final String? receiveCourseUrl,
          final BabyInfo? babyInfo,
          final CertificateWallShareContent? certificateWallShareContent,
          final List<HonorCertificateList>? honorCertificateList}) =
      _$_MineHonorData;

  factory _MineHonorData.fromJson(Map<String, dynamic> json) =
      _$_MineHonorData.fromJson;

  @override
  int? get certificateCount;
  @override
  String? get certificateCountCopywriting;
  @override
  String? get certificateCopywriting;
  @override
  String? get certificateShareBtn;
  @override
  bool? get sharedStatus;
  @override
  bool? get isSelf;
  @override
  String? get receiveCourseUrl;
  @override
  BabyInfo? get babyInfo;
  @override
  CertificateWallShareContent? get certificateWallShareContent;
  @override
  List<HonorCertificateList>? get honorCertificateList;
  @override
  @JsonKey(ignore: true)
  _$$_MineHonorDataCopyWith<_$_MineHonorData> get copyWith =>
      throw _privateConstructorUsedError;
}

BabyInfo _$BabyInfoFromJson(Map<String, dynamic> json) {
  return _BabyInfo.fromJson(json);
}

/// @nodoc
mixin _$BabyInfo {
  String? get nickName => throw _privateConstructorUsedError;
  String? get avatar => throw _privateConstructorUsedError;
  dynamic get age => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BabyInfoCopyWith<BabyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BabyInfoCopyWith<$Res> {
  factory $BabyInfoCopyWith(BabyInfo value, $Res Function(BabyInfo) then) =
      _$BabyInfoCopyWithImpl<$Res, BabyInfo>;
  @useResult
  $Res call({String? nickName, String? avatar, dynamic age});
}

/// @nodoc
class _$BabyInfoCopyWithImpl<$Res, $Val extends BabyInfo>
    implements $BabyInfoCopyWith<$Res> {
  _$BabyInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? avatar = freezed,
    Object? age = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BabyInfoCopyWith<$Res> implements $BabyInfoCopyWith<$Res> {
  factory _$$_BabyInfoCopyWith(
          _$_BabyInfo value, $Res Function(_$_BabyInfo) then) =
      __$$_BabyInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? nickName, String? avatar, dynamic age});
}

/// @nodoc
class __$$_BabyInfoCopyWithImpl<$Res>
    extends _$BabyInfoCopyWithImpl<$Res, _$_BabyInfo>
    implements _$$_BabyInfoCopyWith<$Res> {
  __$$_BabyInfoCopyWithImpl(
      _$_BabyInfo _value, $Res Function(_$_BabyInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? avatar = freezed,
    Object? age = freezed,
  }) {
    return _then(_$_BabyInfo(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BabyInfo with DiagnosticableTreeMixin implements _BabyInfo {
  const _$_BabyInfo({this.nickName, this.avatar, this.age});

  factory _$_BabyInfo.fromJson(Map<String, dynamic> json) =>
      _$$_BabyInfoFromJson(json);

  @override
  final String? nickName;
  @override
  final String? avatar;
  @override
  final dynamic age;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BabyInfo(nickName: $nickName, avatar: $avatar, age: $age)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BabyInfo'))
      ..add(DiagnosticsProperty('nickName', nickName))
      ..add(DiagnosticsProperty('avatar', avatar))
      ..add(DiagnosticsProperty('age', age));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BabyInfo &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            const DeepCollectionEquality().equals(other.age, age));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, nickName, avatar, const DeepCollectionEquality().hash(age));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BabyInfoCopyWith<_$_BabyInfo> get copyWith =>
      __$$_BabyInfoCopyWithImpl<_$_BabyInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BabyInfoToJson(
      this,
    );
  }
}

abstract class _BabyInfo implements BabyInfo {
  const factory _BabyInfo(
      {final String? nickName,
      final String? avatar,
      final dynamic age}) = _$_BabyInfo;

  factory _BabyInfo.fromJson(Map<String, dynamic> json) = _$_BabyInfo.fromJson;

  @override
  String? get nickName;
  @override
  String? get avatar;
  @override
  dynamic get age;
  @override
  @JsonKey(ignore: true)
  _$$_BabyInfoCopyWith<_$_BabyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

CertificateWallShareContent _$CertificateWallShareContentFromJson(
    Map<String, dynamic> json) {
  return _CertificateWallShareContent.fromJson(json);
}

/// @nodoc
mixin _$CertificateWallShareContent {
  String? get obtainReasonCopywriting => throw _privateConstructorUsedError;
  String? get obtainCountCopywriting => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get shareCopywriting => throw _privateConstructorUsedError;
  String? get shareSubCopywriting => throw _privateConstructorUsedError;
  String? get sharePage => throw _privateConstructorUsedError;
  String? get shareImage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CertificateWallShareContentCopyWith<CertificateWallShareContent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CertificateWallShareContentCopyWith<$Res> {
  factory $CertificateWallShareContentCopyWith(
          CertificateWallShareContent value,
          $Res Function(CertificateWallShareContent) then) =
      _$CertificateWallShareContentCopyWithImpl<$Res,
          CertificateWallShareContent>;
  @useResult
  $Res call(
      {String? obtainReasonCopywriting,
      String? obtainCountCopywriting,
      String? image,
      String? shareCopywriting,
      String? shareSubCopywriting,
      String? sharePage,
      String? shareImage});
}

/// @nodoc
class _$CertificateWallShareContentCopyWithImpl<$Res,
        $Val extends CertificateWallShareContent>
    implements $CertificateWallShareContentCopyWith<$Res> {
  _$CertificateWallShareContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? obtainReasonCopywriting = freezed,
    Object? obtainCountCopywriting = freezed,
    Object? image = freezed,
    Object? shareCopywriting = freezed,
    Object? shareSubCopywriting = freezed,
    Object? sharePage = freezed,
    Object? shareImage = freezed,
  }) {
    return _then(_value.copyWith(
      obtainReasonCopywriting: freezed == obtainReasonCopywriting
          ? _value.obtainReasonCopywriting
          : obtainReasonCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCountCopywriting: freezed == obtainCountCopywriting
          ? _value.obtainCountCopywriting
          : obtainCountCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shareCopywriting: freezed == shareCopywriting
          ? _value.shareCopywriting
          : shareCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      shareSubCopywriting: freezed == shareSubCopywriting
          ? _value.shareSubCopywriting
          : shareSubCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      sharePage: freezed == sharePage
          ? _value.sharePage
          : sharePage // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImage: freezed == shareImage
          ? _value.shareImage
          : shareImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CertificateWallShareContentCopyWith<$Res>
    implements $CertificateWallShareContentCopyWith<$Res> {
  factory _$$_CertificateWallShareContentCopyWith(
          _$_CertificateWallShareContent value,
          $Res Function(_$_CertificateWallShareContent) then) =
      __$$_CertificateWallShareContentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? obtainReasonCopywriting,
      String? obtainCountCopywriting,
      String? image,
      String? shareCopywriting,
      String? shareSubCopywriting,
      String? sharePage,
      String? shareImage});
}

/// @nodoc
class __$$_CertificateWallShareContentCopyWithImpl<$Res>
    extends _$CertificateWallShareContentCopyWithImpl<$Res,
        _$_CertificateWallShareContent>
    implements _$$_CertificateWallShareContentCopyWith<$Res> {
  __$$_CertificateWallShareContentCopyWithImpl(
      _$_CertificateWallShareContent _value,
      $Res Function(_$_CertificateWallShareContent) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? obtainReasonCopywriting = freezed,
    Object? obtainCountCopywriting = freezed,
    Object? image = freezed,
    Object? shareCopywriting = freezed,
    Object? shareSubCopywriting = freezed,
    Object? sharePage = freezed,
    Object? shareImage = freezed,
  }) {
    return _then(_$_CertificateWallShareContent(
      obtainReasonCopywriting: freezed == obtainReasonCopywriting
          ? _value.obtainReasonCopywriting
          : obtainReasonCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCountCopywriting: freezed == obtainCountCopywriting
          ? _value.obtainCountCopywriting
          : obtainCountCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shareCopywriting: freezed == shareCopywriting
          ? _value.shareCopywriting
          : shareCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      shareSubCopywriting: freezed == shareSubCopywriting
          ? _value.shareSubCopywriting
          : shareSubCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      sharePage: freezed == sharePage
          ? _value.sharePage
          : sharePage // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImage: freezed == shareImage
          ? _value.shareImage
          : shareImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CertificateWallShareContent
    with DiagnosticableTreeMixin
    implements _CertificateWallShareContent {
  const _$_CertificateWallShareContent(
      {this.obtainReasonCopywriting,
      this.obtainCountCopywriting,
      this.image,
      this.shareCopywriting,
      this.shareSubCopywriting,
      this.sharePage,
      this.shareImage});

  factory _$_CertificateWallShareContent.fromJson(Map<String, dynamic> json) =>
      _$$_CertificateWallShareContentFromJson(json);

  @override
  final String? obtainReasonCopywriting;
  @override
  final String? obtainCountCopywriting;
  @override
  final String? image;
  @override
  final String? shareCopywriting;
  @override
  final String? shareSubCopywriting;
  @override
  final String? sharePage;
  @override
  final String? shareImage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CertificateWallShareContent(obtainReasonCopywriting: $obtainReasonCopywriting, obtainCountCopywriting: $obtainCountCopywriting, image: $image, shareCopywriting: $shareCopywriting, shareSubCopywriting: $shareSubCopywriting, sharePage: $sharePage, shareImage: $shareImage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CertificateWallShareContent'))
      ..add(DiagnosticsProperty(
          'obtainReasonCopywriting', obtainReasonCopywriting))
      ..add(
          DiagnosticsProperty('obtainCountCopywriting', obtainCountCopywriting))
      ..add(DiagnosticsProperty('image', image))
      ..add(DiagnosticsProperty('shareCopywriting', shareCopywriting))
      ..add(DiagnosticsProperty('shareSubCopywriting', shareSubCopywriting))
      ..add(DiagnosticsProperty('sharePage', sharePage))
      ..add(DiagnosticsProperty('shareImage', shareImage));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CertificateWallShareContent &&
            (identical(
                    other.obtainReasonCopywriting, obtainReasonCopywriting) ||
                other.obtainReasonCopywriting == obtainReasonCopywriting) &&
            (identical(other.obtainCountCopywriting, obtainCountCopywriting) ||
                other.obtainCountCopywriting == obtainCountCopywriting) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.shareCopywriting, shareCopywriting) ||
                other.shareCopywriting == shareCopywriting) &&
            (identical(other.shareSubCopywriting, shareSubCopywriting) ||
                other.shareSubCopywriting == shareSubCopywriting) &&
            (identical(other.sharePage, sharePage) ||
                other.sharePage == sharePage) &&
            (identical(other.shareImage, shareImage) ||
                other.shareImage == shareImage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      obtainReasonCopywriting,
      obtainCountCopywriting,
      image,
      shareCopywriting,
      shareSubCopywriting,
      sharePage,
      shareImage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CertificateWallShareContentCopyWith<_$_CertificateWallShareContent>
      get copyWith => __$$_CertificateWallShareContentCopyWithImpl<
          _$_CertificateWallShareContent>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CertificateWallShareContentToJson(
      this,
    );
  }
}

abstract class _CertificateWallShareContent
    implements CertificateWallShareContent {
  const factory _CertificateWallShareContent(
      {final String? obtainReasonCopywriting,
      final String? obtainCountCopywriting,
      final String? image,
      final String? shareCopywriting,
      final String? shareSubCopywriting,
      final String? sharePage,
      final String? shareImage}) = _$_CertificateWallShareContent;

  factory _CertificateWallShareContent.fromJson(Map<String, dynamic> json) =
      _$_CertificateWallShareContent.fromJson;

  @override
  String? get obtainReasonCopywriting;
  @override
  String? get obtainCountCopywriting;
  @override
  String? get image;
  @override
  String? get shareCopywriting;
  @override
  String? get shareSubCopywriting;
  @override
  String? get sharePage;
  @override
  String? get shareImage;
  @override
  @JsonKey(ignore: true)
  _$$_CertificateWallShareContentCopyWith<_$_CertificateWallShareContent>
      get copyWith => throw _privateConstructorUsedError;
}

HonorCertificateList _$HonorCertificateListFromJson(Map<String, dynamic> json) {
  return _HonorCertificateList.fromJson(json);
}

/// @nodoc
mixin _$HonorCertificateList {
  String? get courseName => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  int? get obtainCertificateTime => throw _privateConstructorUsedError;
  bool? get readStatus => throw _privateConstructorUsedError;
  CertificateShareContent? get certificateShareContent =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HonorCertificateListCopyWith<HonorCertificateList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HonorCertificateListCopyWith<$Res> {
  factory $HonorCertificateListCopyWith(HonorCertificateList value,
          $Res Function(HonorCertificateList) then) =
      _$HonorCertificateListCopyWithImpl<$Res, HonorCertificateList>;
  @useResult
  $Res call(
      {String? courseName,
      String? courseSegment,
      String? segmentName,
      String? title,
      String? image,
      int? obtainCertificateTime,
      bool? readStatus,
      CertificateShareContent? certificateShareContent});

  $CertificateShareContentCopyWith<$Res>? get certificateShareContent;
}

/// @nodoc
class _$HonorCertificateListCopyWithImpl<$Res,
        $Val extends HonorCertificateList>
    implements $HonorCertificateListCopyWith<$Res> {
  _$HonorCertificateListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? segmentName = freezed,
    Object? title = freezed,
    Object? image = freezed,
    Object? obtainCertificateTime = freezed,
    Object? readStatus = freezed,
    Object? certificateShareContent = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCertificateTime: freezed == obtainCertificateTime
          ? _value.obtainCertificateTime
          : obtainCertificateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      readStatus: freezed == readStatus
          ? _value.readStatus
          : readStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      certificateShareContent: freezed == certificateShareContent
          ? _value.certificateShareContent
          : certificateShareContent // ignore: cast_nullable_to_non_nullable
              as CertificateShareContent?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CertificateShareContentCopyWith<$Res>? get certificateShareContent {
    if (_value.certificateShareContent == null) {
      return null;
    }

    return $CertificateShareContentCopyWith<$Res>(
        _value.certificateShareContent!, (value) {
      return _then(_value.copyWith(certificateShareContent: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_HonorCertificateListCopyWith<$Res>
    implements $HonorCertificateListCopyWith<$Res> {
  factory _$$_HonorCertificateListCopyWith(_$_HonorCertificateList value,
          $Res Function(_$_HonorCertificateList) then) =
      __$$_HonorCertificateListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseName,
      String? courseSegment,
      String? segmentName,
      String? title,
      String? image,
      int? obtainCertificateTime,
      bool? readStatus,
      CertificateShareContent? certificateShareContent});

  @override
  $CertificateShareContentCopyWith<$Res>? get certificateShareContent;
}

/// @nodoc
class __$$_HonorCertificateListCopyWithImpl<$Res>
    extends _$HonorCertificateListCopyWithImpl<$Res, _$_HonorCertificateList>
    implements _$$_HonorCertificateListCopyWith<$Res> {
  __$$_HonorCertificateListCopyWithImpl(_$_HonorCertificateList _value,
      $Res Function(_$_HonorCertificateList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseSegment = freezed,
    Object? segmentName = freezed,
    Object? title = freezed,
    Object? image = freezed,
    Object? obtainCertificateTime = freezed,
    Object? readStatus = freezed,
    Object? certificateShareContent = freezed,
  }) {
    return _then(_$_HonorCertificateList(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCertificateTime: freezed == obtainCertificateTime
          ? _value.obtainCertificateTime
          : obtainCertificateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      readStatus: freezed == readStatus
          ? _value.readStatus
          : readStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
      certificateShareContent: freezed == certificateShareContent
          ? _value.certificateShareContent
          : certificateShareContent // ignore: cast_nullable_to_non_nullable
              as CertificateShareContent?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_HonorCertificateList
    with DiagnosticableTreeMixin
    implements _HonorCertificateList {
  const _$_HonorCertificateList(
      {this.courseName,
      this.courseSegment,
      this.segmentName,
      this.title,
      this.image,
      this.obtainCertificateTime,
      this.readStatus,
      this.certificateShareContent});

  factory _$_HonorCertificateList.fromJson(Map<String, dynamic> json) =>
      _$$_HonorCertificateListFromJson(json);

  @override
  final String? courseName;
  @override
  final String? courseSegment;
  @override
  final String? segmentName;
  @override
  final String? title;
  @override
  final String? image;
  @override
  final int? obtainCertificateTime;
  @override
  final bool? readStatus;
  @override
  final CertificateShareContent? certificateShareContent;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HonorCertificateList(courseName: $courseName, courseSegment: $courseSegment, segmentName: $segmentName, title: $title, image: $image, obtainCertificateTime: $obtainCertificateTime, readStatus: $readStatus, certificateShareContent: $certificateShareContent)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HonorCertificateList'))
      ..add(DiagnosticsProperty('courseName', courseName))
      ..add(DiagnosticsProperty('courseSegment', courseSegment))
      ..add(DiagnosticsProperty('segmentName', segmentName))
      ..add(DiagnosticsProperty('title', title))
      ..add(DiagnosticsProperty('image', image))
      ..add(DiagnosticsProperty('obtainCertificateTime', obtainCertificateTime))
      ..add(DiagnosticsProperty('readStatus', readStatus))
      ..add(DiagnosticsProperty(
          'certificateShareContent', certificateShareContent));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HonorCertificateList &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.obtainCertificateTime, obtainCertificateTime) ||
                other.obtainCertificateTime == obtainCertificateTime) &&
            (identical(other.readStatus, readStatus) ||
                other.readStatus == readStatus) &&
            (identical(
                    other.certificateShareContent, certificateShareContent) ||
                other.certificateShareContent == certificateShareContent));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseName,
      courseSegment,
      segmentName,
      title,
      image,
      obtainCertificateTime,
      readStatus,
      certificateShareContent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HonorCertificateListCopyWith<_$_HonorCertificateList> get copyWith =>
      __$$_HonorCertificateListCopyWithImpl<_$_HonorCertificateList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_HonorCertificateListToJson(
      this,
    );
  }
}

abstract class _HonorCertificateList implements HonorCertificateList {
  const factory _HonorCertificateList(
          {final String? courseName,
          final String? courseSegment,
          final String? segmentName,
          final String? title,
          final String? image,
          final int? obtainCertificateTime,
          final bool? readStatus,
          final CertificateShareContent? certificateShareContent}) =
      _$_HonorCertificateList;

  factory _HonorCertificateList.fromJson(Map<String, dynamic> json) =
      _$_HonorCertificateList.fromJson;

  @override
  String? get courseName;
  @override
  String? get courseSegment;
  @override
  String? get segmentName;
  @override
  String? get title;
  @override
  String? get image;
  @override
  int? get obtainCertificateTime;
  @override
  bool? get readStatus;
  @override
  CertificateShareContent? get certificateShareContent;
  @override
  @JsonKey(ignore: true)
  _$$_HonorCertificateListCopyWith<_$_HonorCertificateList> get copyWith =>
      throw _privateConstructorUsedError;
}

CertificateShareContent _$CertificateShareContentFromJson(
    Map<String, dynamic> json) {
  return _CertificateShareContent.fromJson(json);
}

/// @nodoc
mixin _$CertificateShareContent {
  String? get cardName => throw _privateConstructorUsedError;
  String? get nickNameCopywriting => throw _privateConstructorUsedError;
  String? get titleReasonCopywriting => throw _privateConstructorUsedError;
  String? get titleCopywriting => throw _privateConstructorUsedError;
  String? get obtainCertificateDate => throw _privateConstructorUsedError;
  String? get praiseDetailSharePage => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get shareImage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CertificateShareContentCopyWith<CertificateShareContent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CertificateShareContentCopyWith<$Res> {
  factory $CertificateShareContentCopyWith(CertificateShareContent value,
          $Res Function(CertificateShareContent) then) =
      _$CertificateShareContentCopyWithImpl<$Res, CertificateShareContent>;
  @useResult
  $Res call(
      {String? cardName,
      String? nickNameCopywriting,
      String? titleReasonCopywriting,
      String? titleCopywriting,
      String? obtainCertificateDate,
      String? praiseDetailSharePage,
      String? image,
      String? shareImage});
}

/// @nodoc
class _$CertificateShareContentCopyWithImpl<$Res,
        $Val extends CertificateShareContent>
    implements $CertificateShareContentCopyWith<$Res> {
  _$CertificateShareContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardName = freezed,
    Object? nickNameCopywriting = freezed,
    Object? titleReasonCopywriting = freezed,
    Object? titleCopywriting = freezed,
    Object? obtainCertificateDate = freezed,
    Object? praiseDetailSharePage = freezed,
    Object? image = freezed,
    Object? shareImage = freezed,
  }) {
    return _then(_value.copyWith(
      cardName: freezed == cardName
          ? _value.cardName
          : cardName // ignore: cast_nullable_to_non_nullable
              as String?,
      nickNameCopywriting: freezed == nickNameCopywriting
          ? _value.nickNameCopywriting
          : nickNameCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      titleReasonCopywriting: freezed == titleReasonCopywriting
          ? _value.titleReasonCopywriting
          : titleReasonCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      titleCopywriting: freezed == titleCopywriting
          ? _value.titleCopywriting
          : titleCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCertificateDate: freezed == obtainCertificateDate
          ? _value.obtainCertificateDate
          : obtainCertificateDate // ignore: cast_nullable_to_non_nullable
              as String?,
      praiseDetailSharePage: freezed == praiseDetailSharePage
          ? _value.praiseDetailSharePage
          : praiseDetailSharePage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImage: freezed == shareImage
          ? _value.shareImage
          : shareImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CertificateShareContentCopyWith<$Res>
    implements $CertificateShareContentCopyWith<$Res> {
  factory _$$_CertificateShareContentCopyWith(_$_CertificateShareContent value,
          $Res Function(_$_CertificateShareContent) then) =
      __$$_CertificateShareContentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? cardName,
      String? nickNameCopywriting,
      String? titleReasonCopywriting,
      String? titleCopywriting,
      String? obtainCertificateDate,
      String? praiseDetailSharePage,
      String? image,
      String? shareImage});
}

/// @nodoc
class __$$_CertificateShareContentCopyWithImpl<$Res>
    extends _$CertificateShareContentCopyWithImpl<$Res,
        _$_CertificateShareContent>
    implements _$$_CertificateShareContentCopyWith<$Res> {
  __$$_CertificateShareContentCopyWithImpl(_$_CertificateShareContent _value,
      $Res Function(_$_CertificateShareContent) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardName = freezed,
    Object? nickNameCopywriting = freezed,
    Object? titleReasonCopywriting = freezed,
    Object? titleCopywriting = freezed,
    Object? obtainCertificateDate = freezed,
    Object? praiseDetailSharePage = freezed,
    Object? image = freezed,
    Object? shareImage = freezed,
  }) {
    return _then(_$_CertificateShareContent(
      cardName: freezed == cardName
          ? _value.cardName
          : cardName // ignore: cast_nullable_to_non_nullable
              as String?,
      nickNameCopywriting: freezed == nickNameCopywriting
          ? _value.nickNameCopywriting
          : nickNameCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      titleReasonCopywriting: freezed == titleReasonCopywriting
          ? _value.titleReasonCopywriting
          : titleReasonCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      titleCopywriting: freezed == titleCopywriting
          ? _value.titleCopywriting
          : titleCopywriting // ignore: cast_nullable_to_non_nullable
              as String?,
      obtainCertificateDate: freezed == obtainCertificateDate
          ? _value.obtainCertificateDate
          : obtainCertificateDate // ignore: cast_nullable_to_non_nullable
              as String?,
      praiseDetailSharePage: freezed == praiseDetailSharePage
          ? _value.praiseDetailSharePage
          : praiseDetailSharePage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      shareImage: freezed == shareImage
          ? _value.shareImage
          : shareImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CertificateShareContent
    with DiagnosticableTreeMixin
    implements _CertificateShareContent {
  const _$_CertificateShareContent(
      {this.cardName,
      this.nickNameCopywriting,
      this.titleReasonCopywriting,
      this.titleCopywriting,
      this.obtainCertificateDate,
      this.praiseDetailSharePage,
      this.image,
      this.shareImage});

  factory _$_CertificateShareContent.fromJson(Map<String, dynamic> json) =>
      _$$_CertificateShareContentFromJson(json);

  @override
  final String? cardName;
  @override
  final String? nickNameCopywriting;
  @override
  final String? titleReasonCopywriting;
  @override
  final String? titleCopywriting;
  @override
  final String? obtainCertificateDate;
  @override
  final String? praiseDetailSharePage;
  @override
  final String? image;
  @override
  final String? shareImage;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'CertificateShareContent(cardName: $cardName, nickNameCopywriting: $nickNameCopywriting, titleReasonCopywriting: $titleReasonCopywriting, titleCopywriting: $titleCopywriting, obtainCertificateDate: $obtainCertificateDate, praiseDetailSharePage: $praiseDetailSharePage, image: $image, shareImage: $shareImage)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'CertificateShareContent'))
      ..add(DiagnosticsProperty('cardName', cardName))
      ..add(DiagnosticsProperty('nickNameCopywriting', nickNameCopywriting))
      ..add(
          DiagnosticsProperty('titleReasonCopywriting', titleReasonCopywriting))
      ..add(DiagnosticsProperty('titleCopywriting', titleCopywriting))
      ..add(DiagnosticsProperty('obtainCertificateDate', obtainCertificateDate))
      ..add(DiagnosticsProperty('praiseDetailSharePage', praiseDetailSharePage))
      ..add(DiagnosticsProperty('image', image))
      ..add(DiagnosticsProperty('shareImage', shareImage));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CertificateShareContent &&
            (identical(other.cardName, cardName) ||
                other.cardName == cardName) &&
            (identical(other.nickNameCopywriting, nickNameCopywriting) ||
                other.nickNameCopywriting == nickNameCopywriting) &&
            (identical(other.titleReasonCopywriting, titleReasonCopywriting) ||
                other.titleReasonCopywriting == titleReasonCopywriting) &&
            (identical(other.titleCopywriting, titleCopywriting) ||
                other.titleCopywriting == titleCopywriting) &&
            (identical(other.obtainCertificateDate, obtainCertificateDate) ||
                other.obtainCertificateDate == obtainCertificateDate) &&
            (identical(other.praiseDetailSharePage, praiseDetailSharePage) ||
                other.praiseDetailSharePage == praiseDetailSharePage) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.shareImage, shareImage) ||
                other.shareImage == shareImage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      cardName,
      nickNameCopywriting,
      titleReasonCopywriting,
      titleCopywriting,
      obtainCertificateDate,
      praiseDetailSharePage,
      image,
      shareImage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CertificateShareContentCopyWith<_$_CertificateShareContent>
      get copyWith =>
          __$$_CertificateShareContentCopyWithImpl<_$_CertificateShareContent>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CertificateShareContentToJson(
      this,
    );
  }
}

abstract class _CertificateShareContent implements CertificateShareContent {
  const factory _CertificateShareContent(
      {final String? cardName,
      final String? nickNameCopywriting,
      final String? titleReasonCopywriting,
      final String? titleCopywriting,
      final String? obtainCertificateDate,
      final String? praiseDetailSharePage,
      final String? image,
      final String? shareImage}) = _$_CertificateShareContent;

  factory _CertificateShareContent.fromJson(Map<String, dynamic> json) =
      _$_CertificateShareContent.fromJson;

  @override
  String? get cardName;
  @override
  String? get nickNameCopywriting;
  @override
  String? get titleReasonCopywriting;
  @override
  String? get titleCopywriting;
  @override
  String? get obtainCertificateDate;
  @override
  String? get praiseDetailSharePage;
  @override
  String? get image;
  @override
  String? get shareImage;
  @override
  @JsonKey(ignore: true)
  _$$_CertificateShareContentCopyWith<_$_CertificateShareContent>
      get copyWith => throw _privateConstructorUsedError;
}
