// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mine_honor_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MineHonorData _$$_MineHonorDataFromJson(Map<String, dynamic> json) =>
    _$_MineHonorData(
      certificateCount: json['certificateCount'] as int?,
      certificateCountCopywriting:
          json['certificateCountCopywriting'] as String?,
      certificateCopywriting: json['certificateCopywriting'] as String?,
      certificateShareBtn: json['certificateShareBtn'] as String?,
      sharedStatus: json['sharedStatus'] as bool?,
      isSelf: json['isSelf'] as bool?,
      receiveCourseUrl: json['receiveCourseUrl'] as String?,
      babyInfo: json['babyInfo'] == null
          ? null
          : BabyInfo.fromJson(json['babyInfo'] as Map<String, dynamic>),
      certificateWallShareContent: json['certificateWallShareContent'] == null
          ? null
          : CertificateWallShareContent.fromJson(
              json['certificateWallShareContent'] as Map<String, dynamic>),
      honorCertificateList: (json['honorCertificateList'] as List<dynamic>?)
          ?.map((e) => HonorCertificateList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MineHonorDataToJson(_$_MineHonorData instance) =>
    <String, dynamic>{
      'certificateCount': instance.certificateCount,
      'certificateCountCopywriting': instance.certificateCountCopywriting,
      'certificateCopywriting': instance.certificateCopywriting,
      'certificateShareBtn': instance.certificateShareBtn,
      'sharedStatus': instance.sharedStatus,
      'isSelf': instance.isSelf,
      'receiveCourseUrl': instance.receiveCourseUrl,
      'babyInfo': instance.babyInfo,
      'certificateWallShareContent': instance.certificateWallShareContent,
      'honorCertificateList': instance.honorCertificateList,
    };

_$_BabyInfo _$$_BabyInfoFromJson(Map<String, dynamic> json) => _$_BabyInfo(
      nickName: json['nickName'] as String?,
      avatar: json['avatar'] as String?,
      age: json['age'],
    );

Map<String, dynamic> _$$_BabyInfoToJson(_$_BabyInfo instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'avatar': instance.avatar,
      'age': instance.age,
    };

_$_CertificateWallShareContent _$$_CertificateWallShareContentFromJson(
        Map<String, dynamic> json) =>
    _$_CertificateWallShareContent(
      obtainReasonCopywriting: json['obtainReasonCopywriting'] as String?,
      obtainCountCopywriting: json['obtainCountCopywriting'] as String?,
      image: json['image'] as String?,
      shareCopywriting: json['shareCopywriting'] as String?,
      shareSubCopywriting: json['shareSubCopywriting'] as String?,
      sharePage: json['sharePage'] as String?,
      shareImage: json['shareImage'] as String?,
    );

Map<String, dynamic> _$$_CertificateWallShareContentToJson(
        _$_CertificateWallShareContent instance) =>
    <String, dynamic>{
      'obtainReasonCopywriting': instance.obtainReasonCopywriting,
      'obtainCountCopywriting': instance.obtainCountCopywriting,
      'image': instance.image,
      'shareCopywriting': instance.shareCopywriting,
      'shareSubCopywriting': instance.shareSubCopywriting,
      'sharePage': instance.sharePage,
      'shareImage': instance.shareImage,
    };

_$_HonorCertificateList _$$_HonorCertificateListFromJson(
        Map<String, dynamic> json) =>
    _$_HonorCertificateList(
      courseName: json['courseName'] as String?,
      courseSegment: json['courseSegment'] as String?,
      segmentName: json['segmentName'] as String?,
      title: json['title'] as String?,
      image: json['image'] as String?,
      obtainCertificateTime: json['obtainCertificateTime'] as int?,
      readStatus: json['readStatus'] as bool?,
      certificateShareContent: json['certificateShareContent'] == null
          ? null
          : CertificateShareContent.fromJson(
              json['certificateShareContent'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_HonorCertificateListToJson(
        _$_HonorCertificateList instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'courseSegment': instance.courseSegment,
      'segmentName': instance.segmentName,
      'title': instance.title,
      'image': instance.image,
      'obtainCertificateTime': instance.obtainCertificateTime,
      'readStatus': instance.readStatus,
      'certificateShareContent': instance.certificateShareContent,
    };

_$_CertificateShareContent _$$_CertificateShareContentFromJson(
        Map<String, dynamic> json) =>
    _$_CertificateShareContent(
      cardName: json['cardName'] as String?,
      nickNameCopywriting: json['nickNameCopywriting'] as String?,
      titleReasonCopywriting: json['titleReasonCopywriting'] as String?,
      titleCopywriting: json['titleCopywriting'] as String?,
      obtainCertificateDate: json['obtainCertificateDate'] as String?,
      praiseDetailSharePage: json['praiseDetailSharePage'] as String?,
      image: json['image'] as String?,
      shareImage: json['shareImage'] as String?,
    );

Map<String, dynamic> _$$_CertificateShareContentToJson(
        _$_CertificateShareContent instance) =>
    <String, dynamic>{
      'cardName': instance.cardName,
      'nickNameCopywriting': instance.nickNameCopywriting,
      'titleReasonCopywriting': instance.titleReasonCopywriting,
      'titleCopywriting': instance.titleCopywriting,
      'obtainCertificateDate': instance.obtainCertificateDate,
      'praiseDetailSharePage': instance.praiseDetailSharePage,
      'image': instance.image,
      'shareImage': instance.shareImage,
    };
