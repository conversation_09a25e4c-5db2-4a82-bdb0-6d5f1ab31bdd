// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jojo_shop_api_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LearnBean _$$_LearnBeanFromJson(Map<String, dynamic> json) => _$_LearnBean(
      amount: json['amount'] as int?,
      icon: json['icon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
      rightIcon: json['rightIcon'] as String?,
      rightBtnText: json['rightBtnText'] as String?,
      rightBtnRoute: json['rightBtnRoute'] as String?,
    );

Map<String, dynamic> _$$_LearnBeanToJson(_$_LearnBean instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'icon': instance.icon,
      'jumpRoute': instance.jumpRoute,
      'rightIcon': instance.rightIcon,
      'rightBtnText': instance.rightBtnText,
      'rightBtnRoute': instance.rightBtnRoute,
    };

_$_ModuleItem _$$_ModuleItemFromJson(Map<String, dynamic> json) =>
    _$_ModuleItem(
      name: json['name'] as String?,
      img: json['img'] as String?,
      price: json['price'] as String?,
      priceIcon: json['priceIcon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_ModuleItemToJson(_$_ModuleItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'img': instance.img,
      'price': instance.price,
      'priceIcon': instance.priceIcon,
      'jumpRoute': instance.jumpRoute,
    };

_$_BannerModule _$$_BannerModuleFromJson(Map<String, dynamic> json) =>
    _$_BannerModule(
      bannerImg: json['bannerImg'] as String?,
      bannerJumpRoute: json['bannerJumpRoute'] as String?,
    );

Map<String, dynamic> _$$_BannerModuleToJson(_$_BannerModule instance) =>
    <String, dynamic>{
      'bannerImg': instance.bannerImg,
      'bannerJumpRoute': instance.bannerJumpRoute,
    };

_$ItemModule _$$ItemModuleFromJson(Map<String, dynamic> json) => _$ItemModule(
      type: json['type'] as String?,
      name: json['name'] as String?,
      more: json['more'] as String?,
      moreJumpRoute: json['moreJumpRoute'] as String?,
      bannerImg: json['bannerImg'] as String?,
      bannerJumpRoute: json['bannerJumpRoute'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ModuleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ItemModuleToJson(_$ItemModule instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'more': instance.more,
      'moreJumpRoute': instance.moreJumpRoute,
      'bannerImg': instance.bannerImg,
      'bannerJumpRoute': instance.bannerJumpRoute,
      'items': instance.items,
    };

_$_ShopDataModel _$$_ShopDataModelFromJson(Map<String, dynamic> json) =>
    _$_ShopDataModel(
      learnBean: json['learnBean'] == null
          ? null
          : LearnBean.fromJson(json['learnBean'] as Map<String, dynamic>),
      modules: (json['modules'] as List<dynamic>?)
          ?.map((e) => Module.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ShopDataModelToJson(_$_ShopDataModel instance) =>
    <String, dynamic>{
      'learnBean': instance.learnBean,
      'modules': instance.modules,
    };
