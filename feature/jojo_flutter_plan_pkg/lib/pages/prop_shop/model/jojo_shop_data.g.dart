// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jojo_shop_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ShopItem _$$_ShopItemFromJson(Map<String, dynamic> json) => _$_ShopItem(
      icon: json['icon'] as String?,
      priceIcon: json['priceIcon'] as String?,
      price: json['price'] as String?,
      title: json['title'] as String?,
      funcDesc: json['funcDesc'] as String?,
      getFunc: json['getFunc'] as String?,
      remainingNum: json['remainingNum'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_ShopItemToJson(_$_ShopItem instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'priceIcon': instance.priceIcon,
      'price': instance.price,
      'title': instance.title,
      'funcDesc': instance.funcDesc,
      'getFunc': instance.getFunc,
      'remainingNum': instance.remainingNum,
      'route': instance.route,
    };

_$_ShopData _$$_ShopDataFromJson(Map<String, dynamic> json) => _$_ShopData(
      title: json['title'] as String?,
      more: json['more'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ShopItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      moreRoute: json['moreRoute'] as String?,
    );

Map<String, dynamic> _$$_ShopDataToJson(_$_ShopData instance) =>
    <String, dynamic>{
      'title': instance.title,
      'more': instance.more,
      'items': instance.items,
      'moreRoute': instance.moreRoute,
    };

_$_BannerData _$$_BannerDataFromJson(Map<String, dynamic> json) =>
    _$_BannerData(
      name: json['name'] as String?,
      image: json['image'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_BannerDataToJson(_$_BannerData instance) =>
    <String, dynamic>{
      'name': instance.name,
      'image': instance.image,
      'route': instance.route,
    };

_$_HeadData _$$_HeadDataFromJson(Map<String, dynamic> json) => _$_HeadData(
      amount: json['amount'] as int?,
      icon: json['icon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
      rightIcon: json['rightIcon'] as String?,
      rightBtnText: json['rightBtnText'] as String?,
      rightBtnRoute: json['rightBtnRoute'] as String?,
    );

Map<String, dynamic> _$$_HeadDataToJson(_$_HeadData instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'icon': instance.icon,
      'jumpRoute': instance.jumpRoute,
      'rightIcon': instance.rightIcon,
      'rightBtnText': instance.rightBtnText,
      'rightBtnRoute': instance.rightBtnRoute,
    };
