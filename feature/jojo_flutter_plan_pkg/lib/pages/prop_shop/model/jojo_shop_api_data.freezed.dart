// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jojo_shop_api_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LearnBean _$LearnBeanFromJson(Map<String, dynamic> json) {
  return _LearnBean.fromJson(json);
}

/// @nodoc
mixin _$LearnBean {
  int? get amount => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get rightIcon => throw _privateConstructorUsedError;
  String? get rightBtnText => throw _privateConstructorUsedError;
  String? get rightBtnRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearnBeanCopyWith<LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearnBeanCopyWith<$Res> {
  factory $LearnBeanCopyWith(LearnBean value, $Res Function(LearnBean) then) =
      _$LearnBeanCopyWithImpl<$Res, LearnBean>;
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class _$LearnBeanCopyWithImpl<$Res, $Val extends LearnBean>
    implements $LearnBeanCopyWith<$Res> {
  _$LearnBeanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearnBeanCopyWith<$Res> implements $LearnBeanCopyWith<$Res> {
  factory _$$_LearnBeanCopyWith(
          _$_LearnBean value, $Res Function(_$_LearnBean) then) =
      __$$_LearnBeanCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? amount,
      String? icon,
      String? jumpRoute,
      String? rightIcon,
      String? rightBtnText,
      String? rightBtnRoute});
}

/// @nodoc
class __$$_LearnBeanCopyWithImpl<$Res>
    extends _$LearnBeanCopyWithImpl<$Res, _$_LearnBean>
    implements _$$_LearnBeanCopyWith<$Res> {
  __$$_LearnBeanCopyWithImpl(
      _$_LearnBean _value, $Res Function(_$_LearnBean) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
    Object? rightIcon = freezed,
    Object? rightBtnText = freezed,
    Object? rightBtnRoute = freezed,
  }) {
    return _then(_$_LearnBean(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      rightIcon: freezed == rightIcon
          ? _value.rightIcon
          : rightIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnText: freezed == rightBtnText
          ? _value.rightBtnText
          : rightBtnText // ignore: cast_nullable_to_non_nullable
              as String?,
      rightBtnRoute: freezed == rightBtnRoute
          ? _value.rightBtnRoute
          : rightBtnRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearnBean implements _LearnBean {
  _$_LearnBean(
      {this.amount,
      this.icon,
      this.jumpRoute,
      this.rightIcon,
      this.rightBtnText,
      this.rightBtnRoute});

  factory _$_LearnBean.fromJson(Map<String, dynamic> json) =>
      _$$_LearnBeanFromJson(json);

  @override
  final int? amount;
  @override
  final String? icon;
  @override
  final String? jumpRoute;
  @override
  final String? rightIcon;
  @override
  final String? rightBtnText;
  @override
  final String? rightBtnRoute;

  @override
  String toString() {
    return 'LearnBean(amount: $amount, icon: $icon, jumpRoute: $jumpRoute, rightIcon: $rightIcon, rightBtnText: $rightBtnText, rightBtnRoute: $rightBtnRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LearnBean &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.rightIcon, rightIcon) ||
                other.rightIcon == rightIcon) &&
            (identical(other.rightBtnText, rightBtnText) ||
                other.rightBtnText == rightBtnText) &&
            (identical(other.rightBtnRoute, rightBtnRoute) ||
                other.rightBtnRoute == rightBtnRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, amount, icon, jumpRoute,
      rightIcon, rightBtnText, rightBtnRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      __$$_LearnBeanCopyWithImpl<_$_LearnBean>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearnBeanToJson(
      this,
    );
  }
}

abstract class _LearnBean implements LearnBean {
  factory _LearnBean(
      {final int? amount,
      final String? icon,
      final String? jumpRoute,
      final String? rightIcon,
      final String? rightBtnText,
      final String? rightBtnRoute}) = _$_LearnBean;

  factory _LearnBean.fromJson(Map<String, dynamic> json) =
      _$_LearnBean.fromJson;

  @override
  int? get amount;
  @override
  String? get icon;
  @override
  String? get jumpRoute;
  @override
  String? get rightIcon;
  @override
  String? get rightBtnText;
  @override
  String? get rightBtnRoute;
  @override
  @JsonKey(ignore: true)
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

ModuleItem _$ModuleItemFromJson(Map<String, dynamic> json) {
  return _ModuleItem.fromJson(json);
}

/// @nodoc
mixin _$ModuleItem {
  String? get name => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get price => throw _privateConstructorUsedError;
  String? get priceIcon => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ModuleItemCopyWith<ModuleItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModuleItemCopyWith<$Res> {
  factory $ModuleItemCopyWith(
          ModuleItem value, $Res Function(ModuleItem) then) =
      _$ModuleItemCopyWithImpl<$Res, ModuleItem>;
  @useResult
  $Res call(
      {String? name,
      String? img,
      String? price,
      String? priceIcon,
      String? jumpRoute});
}

/// @nodoc
class _$ModuleItemCopyWithImpl<$Res, $Val extends ModuleItem>
    implements $ModuleItemCopyWith<$Res> {
  _$ModuleItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
    Object? price = freezed,
    Object? priceIcon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ModuleItemCopyWith<$Res>
    implements $ModuleItemCopyWith<$Res> {
  factory _$$_ModuleItemCopyWith(
          _$_ModuleItem value, $Res Function(_$_ModuleItem) then) =
      __$$_ModuleItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name,
      String? img,
      String? price,
      String? priceIcon,
      String? jumpRoute});
}

/// @nodoc
class __$$_ModuleItemCopyWithImpl<$Res>
    extends _$ModuleItemCopyWithImpl<$Res, _$_ModuleItem>
    implements _$$_ModuleItemCopyWith<$Res> {
  __$$_ModuleItemCopyWithImpl(
      _$_ModuleItem _value, $Res Function(_$_ModuleItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
    Object? price = freezed,
    Object? priceIcon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_ModuleItem(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String?,
      priceIcon: freezed == priceIcon
          ? _value.priceIcon
          : priceIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ModuleItem implements _ModuleItem {
  _$_ModuleItem(
      {this.name, this.img, this.price, this.priceIcon, this.jumpRoute});

  factory _$_ModuleItem.fromJson(Map<String, dynamic> json) =>
      _$$_ModuleItemFromJson(json);

  @override
  final String? name;
  @override
  final String? img;
  @override
  final String? price;
  @override
  final String? priceIcon;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'ModuleItem(name: $name, img: $img, price: $price, priceIcon: $priceIcon, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ModuleItem &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceIcon, priceIcon) ||
                other.priceIcon == priceIcon) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, img, price, priceIcon, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ModuleItemCopyWith<_$_ModuleItem> get copyWith =>
      __$$_ModuleItemCopyWithImpl<_$_ModuleItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ModuleItemToJson(
      this,
    );
  }
}

abstract class _ModuleItem implements ModuleItem {
  factory _ModuleItem(
      {final String? name,
      final String? img,
      final String? price,
      final String? priceIcon,
      final String? jumpRoute}) = _$_ModuleItem;

  factory _ModuleItem.fromJson(Map<String, dynamic> json) =
      _$_ModuleItem.fromJson;

  @override
  String? get name;
  @override
  String? get img;
  @override
  String? get price;
  @override
  String? get priceIcon;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_ModuleItemCopyWith<_$_ModuleItem> get copyWith =>
      throw _privateConstructorUsedError;
}

BannerModule _$BannerModuleFromJson(Map<String, dynamic> json) {
  return _BannerModule.fromJson(json);
}

/// @nodoc
mixin _$BannerModule {
  String? get bannerImg => throw _privateConstructorUsedError;
  String? get bannerJumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BannerModuleCopyWith<BannerModule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerModuleCopyWith<$Res> {
  factory $BannerModuleCopyWith(
          BannerModule value, $Res Function(BannerModule) then) =
      _$BannerModuleCopyWithImpl<$Res, BannerModule>;
  @useResult
  $Res call({String? bannerImg, String? bannerJumpRoute});
}

/// @nodoc
class _$BannerModuleCopyWithImpl<$Res, $Val extends BannerModule>
    implements $BannerModuleCopyWith<$Res> {
  _$BannerModuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BannerModuleCopyWith<$Res>
    implements $BannerModuleCopyWith<$Res> {
  factory _$$_BannerModuleCopyWith(
          _$_BannerModule value, $Res Function(_$_BannerModule) then) =
      __$$_BannerModuleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? bannerImg, String? bannerJumpRoute});
}

/// @nodoc
class __$$_BannerModuleCopyWithImpl<$Res>
    extends _$BannerModuleCopyWithImpl<$Res, _$_BannerModule>
    implements _$$_BannerModuleCopyWith<$Res> {
  __$$_BannerModuleCopyWithImpl(
      _$_BannerModule _value, $Res Function(_$_BannerModule) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
  }) {
    return _then(_$_BannerModule(
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BannerModule implements _BannerModule {
  _$_BannerModule({this.bannerImg, this.bannerJumpRoute});

  factory _$_BannerModule.fromJson(Map<String, dynamic> json) =>
      _$$_BannerModuleFromJson(json);

  @override
  final String? bannerImg;
  @override
  final String? bannerJumpRoute;

  @override
  String toString() {
    return 'BannerModule(bannerImg: $bannerImg, bannerJumpRoute: $bannerJumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BannerModule &&
            (identical(other.bannerImg, bannerImg) ||
                other.bannerImg == bannerImg) &&
            (identical(other.bannerJumpRoute, bannerJumpRoute) ||
                other.bannerJumpRoute == bannerJumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bannerImg, bannerJumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BannerModuleCopyWith<_$_BannerModule> get copyWith =>
      __$$_BannerModuleCopyWithImpl<_$_BannerModule>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BannerModuleToJson(
      this,
    );
  }
}

abstract class _BannerModule implements BannerModule {
  factory _BannerModule(
      {final String? bannerImg,
      final String? bannerJumpRoute}) = _$_BannerModule;

  factory _BannerModule.fromJson(Map<String, dynamic> json) =
      _$_BannerModule.fromJson;

  @override
  String? get bannerImg;
  @override
  String? get bannerJumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_BannerModuleCopyWith<_$_BannerModule> get copyWith =>
      throw _privateConstructorUsedError;
}

Module _$ModuleFromJson(Map<String, dynamic> json) {
  return ItemModule.fromJson(json);
}

/// @nodoc
mixin _$Module {
  String? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get more => throw _privateConstructorUsedError;
  String? get moreJumpRoute => throw _privateConstructorUsedError;
  String? get bannerImg => throw _privateConstructorUsedError;
  String? get bannerJumpRoute => throw _privateConstructorUsedError;
  List<ModuleItem>? get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ModuleCopyWith<Module> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModuleCopyWith<$Res> {
  factory $ModuleCopyWith(Module value, $Res Function(Module) then) =
      _$ModuleCopyWithImpl<$Res, Module>;
  @useResult
  $Res call(
      {String? type,
      String? name,
      String? more,
      String? moreJumpRoute,
      String? bannerImg,
      String? bannerJumpRoute,
      List<ModuleItem>? items});
}

/// @nodoc
class _$ModuleCopyWithImpl<$Res, $Val extends Module>
    implements $ModuleCopyWith<$Res> {
  _$ModuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? more = freezed,
    Object? moreJumpRoute = freezed,
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      moreJumpRoute: freezed == moreJumpRoute
          ? _value.moreJumpRoute
          : moreJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ModuleItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ItemModuleCopyWith<$Res> implements $ModuleCopyWith<$Res> {
  factory _$$ItemModuleCopyWith(
          _$ItemModule value, $Res Function(_$ItemModule) then) =
      __$$ItemModuleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? name,
      String? more,
      String? moreJumpRoute,
      String? bannerImg,
      String? bannerJumpRoute,
      List<ModuleItem>? items});
}

/// @nodoc
class __$$ItemModuleCopyWithImpl<$Res>
    extends _$ModuleCopyWithImpl<$Res, _$ItemModule>
    implements _$$ItemModuleCopyWith<$Res> {
  __$$ItemModuleCopyWithImpl(
      _$ItemModule _value, $Res Function(_$ItemModule) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? more = freezed,
    Object? moreJumpRoute = freezed,
    Object? bannerImg = freezed,
    Object? bannerJumpRoute = freezed,
    Object? items = freezed,
  }) {
    return _then(_$ItemModule(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      more: freezed == more
          ? _value.more
          : more // ignore: cast_nullable_to_non_nullable
              as String?,
      moreJumpRoute: freezed == moreJumpRoute
          ? _value.moreJumpRoute
          : moreJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImg: freezed == bannerImg
          ? _value.bannerImg
          : bannerImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerJumpRoute: freezed == bannerJumpRoute
          ? _value.bannerJumpRoute
          : bannerJumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ModuleItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ItemModule implements ItemModule {
  const _$ItemModule(
      {this.type,
      this.name,
      this.more,
      this.moreJumpRoute,
      this.bannerImg,
      this.bannerJumpRoute,
      final List<ModuleItem>? items})
      : _items = items;

  factory _$ItemModule.fromJson(Map<String, dynamic> json) =>
      _$$ItemModuleFromJson(json);

  @override
  final String? type;
  @override
  final String? name;
  @override
  final String? more;
  @override
  final String? moreJumpRoute;
  @override
  final String? bannerImg;
  @override
  final String? bannerJumpRoute;
  final List<ModuleItem>? _items;
  @override
  List<ModuleItem>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Module(type: $type, name: $name, more: $more, moreJumpRoute: $moreJumpRoute, bannerImg: $bannerImg, bannerJumpRoute: $bannerJumpRoute, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ItemModule &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.more, more) || other.more == more) &&
            (identical(other.moreJumpRoute, moreJumpRoute) ||
                other.moreJumpRoute == moreJumpRoute) &&
            (identical(other.bannerImg, bannerImg) ||
                other.bannerImg == bannerImg) &&
            (identical(other.bannerJumpRoute, bannerJumpRoute) ||
                other.bannerJumpRoute == bannerJumpRoute) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, name, more, moreJumpRoute,
      bannerImg, bannerJumpRoute, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ItemModuleCopyWith<_$ItemModule> get copyWith =>
      __$$ItemModuleCopyWithImpl<_$ItemModule>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ItemModuleToJson(
      this,
    );
  }
}

abstract class ItemModule implements Module {
  const factory ItemModule(
      {final String? type,
      final String? name,
      final String? more,
      final String? moreJumpRoute,
      final String? bannerImg,
      final String? bannerJumpRoute,
      final List<ModuleItem>? items}) = _$ItemModule;

  factory ItemModule.fromJson(Map<String, dynamic> json) =
      _$ItemModule.fromJson;

  @override
  String? get type;
  @override
  String? get name;
  @override
  String? get more;
  @override
  String? get moreJumpRoute;
  @override
  String? get bannerImg;
  @override
  String? get bannerJumpRoute;
  @override
  List<ModuleItem>? get items;
  @override
  @JsonKey(ignore: true)
  _$$ItemModuleCopyWith<_$ItemModule> get copyWith =>
      throw _privateConstructorUsedError;
}

ShopDataModel _$ShopDataModelFromJson(Map<String, dynamic> json) {
  return _ShopDataModel.fromJson(json);
}

/// @nodoc
mixin _$ShopDataModel {
  LearnBean? get learnBean => throw _privateConstructorUsedError;
  List<Module>? get modules => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShopDataModelCopyWith<ShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopDataModelCopyWith<$Res> {
  factory $ShopDataModelCopyWith(
          ShopDataModel value, $Res Function(ShopDataModel) then) =
      _$ShopDataModelCopyWithImpl<$Res, ShopDataModel>;
  @useResult
  $Res call({LearnBean? learnBean, List<Module>? modules});

  $LearnBeanCopyWith<$Res>? get learnBean;
}

/// @nodoc
class _$ShopDataModelCopyWithImpl<$Res, $Val extends ShopDataModel>
    implements $ShopDataModelCopyWith<$Res> {
  _$ShopDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? learnBean = freezed,
    Object? modules = freezed,
  }) {
    return _then(_value.copyWith(
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      modules: freezed == modules
          ? _value.modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LearnBeanCopyWith<$Res>? get learnBean {
    if (_value.learnBean == null) {
      return null;
    }

    return $LearnBeanCopyWith<$Res>(_value.learnBean!, (value) {
      return _then(_value.copyWith(learnBean: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ShopDataModelCopyWith<$Res>
    implements $ShopDataModelCopyWith<$Res> {
  factory _$$_ShopDataModelCopyWith(
          _$_ShopDataModel value, $Res Function(_$_ShopDataModel) then) =
      __$$_ShopDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({LearnBean? learnBean, List<Module>? modules});

  @override
  $LearnBeanCopyWith<$Res>? get learnBean;
}

/// @nodoc
class __$$_ShopDataModelCopyWithImpl<$Res>
    extends _$ShopDataModelCopyWithImpl<$Res, _$_ShopDataModel>
    implements _$$_ShopDataModelCopyWith<$Res> {
  __$$_ShopDataModelCopyWithImpl(
      _$_ShopDataModel _value, $Res Function(_$_ShopDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? learnBean = freezed,
    Object? modules = freezed,
  }) {
    return _then(_$_ShopDataModel(
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      modules: freezed == modules
          ? _value._modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShopDataModel implements _ShopDataModel {
  _$_ShopDataModel({this.learnBean, final List<Module>? modules})
      : _modules = modules;

  factory _$_ShopDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_ShopDataModelFromJson(json);

  @override
  final LearnBean? learnBean;
  final List<Module>? _modules;
  @override
  List<Module>? get modules {
    final value = _modules;
    if (value == null) return null;
    if (_modules is EqualUnmodifiableListView) return _modules;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ShopDataModel(learnBean: $learnBean, modules: $modules)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShopDataModel &&
            (identical(other.learnBean, learnBean) ||
                other.learnBean == learnBean) &&
            const DeepCollectionEquality().equals(other._modules, _modules));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, learnBean, const DeepCollectionEquality().hash(_modules));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShopDataModelCopyWith<_$_ShopDataModel> get copyWith =>
      __$$_ShopDataModelCopyWithImpl<_$_ShopDataModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShopDataModelToJson(
      this,
    );
  }
}

abstract class _ShopDataModel implements ShopDataModel {
  factory _ShopDataModel(
      {final LearnBean? learnBean,
      final List<Module>? modules}) = _$_ShopDataModel;

  factory _ShopDataModel.fromJson(Map<String, dynamic> json) =
      _$_ShopDataModel.fromJson;

  @override
  LearnBean? get learnBean;
  @override
  List<Module>? get modules;
  @override
  @JsonKey(ignore: true)
  _$$_ShopDataModelCopyWith<_$_ShopDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}
