// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_config_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CustomerConfigDataType _$$_CustomerConfigDataTypeFromJson(
        Map<String, dynamic> json) =>
    _$_CustomerConfigDataType(
      sessionId: json['sessionId'] as int?,
      greetInfoList: (json['greetInfoList'] as List<dynamic>?)
          ?.map((e) => GreetInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      aiQuickEntranceInfoList:
          (json['aiQuickEntranceInfoList'] as List<dynamic>?)
              ?.map((e) =>
                  AiQuickEntranceInfoList.fromJson(e as Map<String, dynamic>))
              .toList(),
      aiCardInfo: json['aiCardInfo'] == null
          ? null
          : AiCardInfo.fromJson(json['aiCardInfo'] as Map<String, dynamic>),
      visitorMark: json['visitorMark'] as bool?,
    );

Map<String, dynamic> _$$_CustomerConfigDataTypeToJson(
        _$_CustomerConfigDataType instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'greetInfoList': instance.greetInfoList,
      'aiQuickEntranceInfoList': instance.aiQuickEntranceInfoList,
      'aiCardInfo': instance.aiCardInfo,
      'visitorMark': instance.visitorMark,
    };

_$_AiCardInfo _$$_AiCardInfoFromJson(Map<String, dynamic> json) =>
    _$_AiCardInfo(
      faqList: (json['faqList'] as List<dynamic>?)
          ?.map((e) => FaqList.fromJson(e as Map<String, dynamic>))
          .toList(),
      quickServiceList: (json['quickServiceList'] as List<dynamic>?)
          ?.map((e) => QuickServiceList.fromJson(e as Map<String, dynamic>))
          .toList(),
      faqTitle: json['faqTitle'] as String?,
      quickServiceSubLink: json['quickServiceSubLink'] as String?,
      quickServiceSubText: json['quickServiceSubText'] as String?,
      quickServiceTitle: json['quickServiceTitle'] as String?,
      tabOnlineLink: json['tabOnlineLink'] as String?,
      tabOrderLink: json['tabOrderLink'] as String?,
      tabTelPhone: json['tabTelPhone'] as String?,
    );

Map<String, dynamic> _$$_AiCardInfoToJson(_$_AiCardInfo instance) =>
    <String, dynamic>{
      'faqList': instance.faqList,
      'quickServiceList': instance.quickServiceList,
      'faqTitle': instance.faqTitle,
      'quickServiceSubLink': instance.quickServiceSubLink,
      'quickServiceSubText': instance.quickServiceSubText,
      'quickServiceTitle': instance.quickServiceTitle,
      'tabOnlineLink': instance.tabOnlineLink,
      'tabOrderLink': instance.tabOrderLink,
      'tabTelPhone': instance.tabTelPhone,
    };

_$_FaqList _$$_FaqListFromJson(Map<String, dynamic> json) => _$_FaqList(
      subTitle: json['subTitle'] as String?,
      text: json['text'] as String?,
      title: json['title'] as String?,
      indexId: json['indexId'] as int?,
    );

Map<String, dynamic> _$$_FaqListToJson(_$_FaqList instance) =>
    <String, dynamic>{
      'subTitle': instance.subTitle,
      'text': instance.text,
      'title': instance.title,
      'indexId': instance.indexId,
    };

_$_QuickServiceList _$$_QuickServiceListFromJson(Map<String, dynamic> json) =>
    _$_QuickServiceList(
      icon: json['icon'] as String?,
      link: json['link'] as String?,
      text: json['text'] as String?,
    );

Map<String, dynamic> _$$_QuickServiceListToJson(_$_QuickServiceList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'link': instance.link,
      'text': instance.text,
    };

_$_GreetInfoList _$$_GreetInfoListFromJson(Map<String, dynamic> json) =>
    _$_GreetInfoList(
      icon: json['icon'] as String?,
      type: json['type'] as String?,
      text: json['text'] as String?,
    );

Map<String, dynamic> _$$_GreetInfoListToJson(_$_GreetInfoList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'type': instance.type,
      'text': instance.text,
    };

_$_AiQuickEntranceInfoList _$$_AiQuickEntranceInfoListFromJson(
        Map<String, dynamic> json) =>
    _$_AiQuickEntranceInfoList(
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      url: json['url'] as String?,
      order: json['order'] as int?,
    );

Map<String, dynamic> _$$_AiQuickEntranceInfoListToJson(
        _$_AiQuickEntranceInfoList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'name': instance.name,
      'url': instance.url,
      'order': instance.order,
    };

_$_ReplyData _$$_ReplyDataFromJson(Map<String, dynamic> json) => _$_ReplyData(
      content: json['content'] as String?,
      role: json['role'] as String?,
      id: json['id'] as int?,
      intentVo: json['intentVo'] == null
          ? null
          : IntentVo.fromJson(json['intentVo'] as Map<String, dynamic>),
      ref: json['ref'] == null
          ? null
          : ReplyRef.fromJson(json['ref'] as Map<String, dynamic>),
      visitorSessionId: json['visitorSessionId'] as int?,
      cardConfig: jsonDecodeVal(json, 'cardConfig') == null
          ? null
          : CardConfig.fromJson(
              jsonDecodeVal(json, 'cardConfig') as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ReplyDataToJson(_$_ReplyData instance) =>
    <String, dynamic>{
      'content': instance.content,
      'role': instance.role,
      'id': instance.id,
      'intentVo': instance.intentVo,
      'ref': instance.ref,
      'visitorSessionId': instance.visitorSessionId,
      'cardConfig': instance.cardConfig,
    };

_$_CardConfig _$$_CardConfigFromJson(Map<String, dynamic> json) =>
    _$_CardConfig(
      link: json['link'] as String?,
      title: json['title'] as String?,
      desc: json['desc'] as String?,
      imgUrl: json['imgUrl'] as String?,
      sceneTag: json['sceneTag'] as String?,
    );

Map<String, dynamic> _$$_CardConfigToJson(_$_CardConfig instance) =>
    <String, dynamic>{
      'link': instance.link,
      'title': instance.title,
      'desc': instance.desc,
      'imgUrl': instance.imgUrl,
      'sceneTag': instance.sceneTag,
    };

_$_IntentVo _$$_IntentVoFromJson(Map<String, dynamic> json) => _$_IntentVo(
      intent: json['intent'] as int?,
      url: json['url'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$_IntentVoToJson(_$_IntentVo instance) =>
    <String, dynamic>{
      'intent': instance.intent,
      'url': instance.url,
      'type': instance.type,
    };

_$_ReplyRef _$$_ReplyRefFromJson(Map<String, dynamic> json) => _$_ReplyRef(
      id: json['id'] as int?,
      user: json['user'] as String?,
      content: json['content'] as String?,
    );

Map<String, dynamic> _$$_ReplyRefToJson(_$_ReplyRef instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'content': instance.content,
    };
