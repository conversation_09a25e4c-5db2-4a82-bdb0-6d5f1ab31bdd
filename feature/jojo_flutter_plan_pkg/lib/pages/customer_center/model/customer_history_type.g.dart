// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_history_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_HistoryMsgData _$$_HistoryMsgDataFromJson(Map<String, dynamic> json) =>
    _$_HistoryMsgData(
      sessionId: json['sessionId'] as int?,
      chatPageResp: json['chatPageResp'] == null
          ? null
          : ChatPageResp.fromJson(json['chatPageResp'] as Map<String, dynamic>),
      time: json['time'] as String?,
    );

Map<String, dynamic> _$$_HistoryMsgDataToJson(_$_HistoryMsgData instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'chatPageResp': instance.chatPageResp,
      'time': instance.time,
    };

_$_ChatPageResp _$$_ChatPageRespFromJson(Map<String, dynamic> json) =>
    _$_ChatPageResp(
      pageRecords: (json['pageRecords'] as List<dynamic>?)
          ?.map((e) => ReplyData.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: json['pageNum'] as int?,
      pageSize: json['pageSize'] as int?,
      pageCount: json['pageCount'] as int?,
      totalCount: json['totalCount'] as int?,
    );

Map<String, dynamic> _$$_ChatPageRespToJson(_$_ChatPageResp instance) =>
    <String, dynamic>{
      'pageRecords': instance.pageRecords,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'pageCount': instance.pageCount,
      'totalCount': instance.totalCount,
    };
