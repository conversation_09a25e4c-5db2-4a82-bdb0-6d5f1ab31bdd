// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'customer_history_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

HistoryMsgData _$HistoryMsgDataFromJson(Map<String, dynamic> json) {
  return _HistoryMsgData.fromJson(json);
}

/// @nodoc
mixin _$HistoryMsgData {
  int? get sessionId => throw _privateConstructorUsedError;
  ChatPageResp? get chatPageResp => throw _privateConstructorUsedError;
  String? get time => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryMsgDataCopyWith<HistoryMsgData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryMsgDataCopyWith<$Res> {
  factory $HistoryMsgDataCopyWith(
          HistoryMsgData value, $Res Function(HistoryMsgData) then) =
      _$HistoryMsgDataCopyWithImpl<$Res, HistoryMsgData>;
  @useResult
  $Res call({int? sessionId, ChatPageResp? chatPageResp, String? time});

  $ChatPageRespCopyWith<$Res>? get chatPageResp;
}

/// @nodoc
class _$HistoryMsgDataCopyWithImpl<$Res, $Val extends HistoryMsgData>
    implements $HistoryMsgDataCopyWith<$Res> {
  _$HistoryMsgDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? chatPageResp = freezed,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      chatPageResp: freezed == chatPageResp
          ? _value.chatPageResp
          : chatPageResp // ignore: cast_nullable_to_non_nullable
              as ChatPageResp?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ChatPageRespCopyWith<$Res>? get chatPageResp {
    if (_value.chatPageResp == null) {
      return null;
    }

    return $ChatPageRespCopyWith<$Res>(_value.chatPageResp!, (value) {
      return _then(_value.copyWith(chatPageResp: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_HistoryMsgDataCopyWith<$Res>
    implements $HistoryMsgDataCopyWith<$Res> {
  factory _$$_HistoryMsgDataCopyWith(
          _$_HistoryMsgData value, $Res Function(_$_HistoryMsgData) then) =
      __$$_HistoryMsgDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? sessionId, ChatPageResp? chatPageResp, String? time});

  @override
  $ChatPageRespCopyWith<$Res>? get chatPageResp;
}

/// @nodoc
class __$$_HistoryMsgDataCopyWithImpl<$Res>
    extends _$HistoryMsgDataCopyWithImpl<$Res, _$_HistoryMsgData>
    implements _$$_HistoryMsgDataCopyWith<$Res> {
  __$$_HistoryMsgDataCopyWithImpl(
      _$_HistoryMsgData _value, $Res Function(_$_HistoryMsgData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? chatPageResp = freezed,
    Object? time = freezed,
  }) {
    return _then(_$_HistoryMsgData(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      chatPageResp: freezed == chatPageResp
          ? _value.chatPageResp
          : chatPageResp // ignore: cast_nullable_to_non_nullable
              as ChatPageResp?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_HistoryMsgData implements _HistoryMsgData {
  const _$_HistoryMsgData({this.sessionId, this.chatPageResp, this.time});

  factory _$_HistoryMsgData.fromJson(Map<String, dynamic> json) =>
      _$$_HistoryMsgDataFromJson(json);

  @override
  final int? sessionId;
  @override
  final ChatPageResp? chatPageResp;
  @override
  final String? time;

  @override
  String toString() {
    return 'HistoryMsgData(sessionId: $sessionId, chatPageResp: $chatPageResp, time: $time)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HistoryMsgData &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.chatPageResp, chatPageResp) ||
                other.chatPageResp == chatPageResp) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, sessionId, chatPageResp, time);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HistoryMsgDataCopyWith<_$_HistoryMsgData> get copyWith =>
      __$$_HistoryMsgDataCopyWithImpl<_$_HistoryMsgData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_HistoryMsgDataToJson(
      this,
    );
  }
}

abstract class _HistoryMsgData implements HistoryMsgData {
  const factory _HistoryMsgData(
      {final int? sessionId,
      final ChatPageResp? chatPageResp,
      final String? time}) = _$_HistoryMsgData;

  factory _HistoryMsgData.fromJson(Map<String, dynamic> json) =
      _$_HistoryMsgData.fromJson;

  @override
  int? get sessionId;
  @override
  ChatPageResp? get chatPageResp;
  @override
  String? get time;
  @override
  @JsonKey(ignore: true)
  _$$_HistoryMsgDataCopyWith<_$_HistoryMsgData> get copyWith =>
      throw _privateConstructorUsedError;
}

ChatPageResp _$ChatPageRespFromJson(Map<String, dynamic> json) {
  return _ChatPageResp.fromJson(json);
}

/// @nodoc
mixin _$ChatPageResp {
  List<ReplyData>? get pageRecords => throw _privateConstructorUsedError;
  int? get pageNum => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get pageCount => throw _privateConstructorUsedError;
  int? get totalCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChatPageRespCopyWith<ChatPageResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatPageRespCopyWith<$Res> {
  factory $ChatPageRespCopyWith(
          ChatPageResp value, $Res Function(ChatPageResp) then) =
      _$ChatPageRespCopyWithImpl<$Res, ChatPageResp>;
  @useResult
  $Res call(
      {List<ReplyData>? pageRecords,
      int? pageNum,
      int? pageSize,
      int? pageCount,
      int? totalCount});
}

/// @nodoc
class _$ChatPageRespCopyWithImpl<$Res, $Val extends ChatPageResp>
    implements $ChatPageRespCopyWith<$Res> {
  _$ChatPageRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageRecords = freezed,
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? pageCount = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_value.copyWith(
      pageRecords: freezed == pageRecords
          ? _value.pageRecords
          : pageRecords // ignore: cast_nullable_to_non_nullable
              as List<ReplyData>?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ChatPageRespCopyWith<$Res>
    implements $ChatPageRespCopyWith<$Res> {
  factory _$$_ChatPageRespCopyWith(
          _$_ChatPageResp value, $Res Function(_$_ChatPageResp) then) =
      __$$_ChatPageRespCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ReplyData>? pageRecords,
      int? pageNum,
      int? pageSize,
      int? pageCount,
      int? totalCount});
}

/// @nodoc
class __$$_ChatPageRespCopyWithImpl<$Res>
    extends _$ChatPageRespCopyWithImpl<$Res, _$_ChatPageResp>
    implements _$$_ChatPageRespCopyWith<$Res> {
  __$$_ChatPageRespCopyWithImpl(
      _$_ChatPageResp _value, $Res Function(_$_ChatPageResp) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageRecords = freezed,
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? pageCount = freezed,
    Object? totalCount = freezed,
  }) {
    return _then(_$_ChatPageResp(
      pageRecords: freezed == pageRecords
          ? _value._pageRecords
          : pageRecords // ignore: cast_nullable_to_non_nullable
              as List<ReplyData>?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalCount: freezed == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ChatPageResp implements _ChatPageResp {
  const _$_ChatPageResp(
      {final List<ReplyData>? pageRecords,
      this.pageNum,
      this.pageSize,
      this.pageCount,
      this.totalCount})
      : _pageRecords = pageRecords;

  factory _$_ChatPageResp.fromJson(Map<String, dynamic> json) =>
      _$$_ChatPageRespFromJson(json);

  final List<ReplyData>? _pageRecords;
  @override
  List<ReplyData>? get pageRecords {
    final value = _pageRecords;
    if (value == null) return null;
    if (_pageRecords is EqualUnmodifiableListView) return _pageRecords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? pageNum;
  @override
  final int? pageSize;
  @override
  final int? pageCount;
  @override
  final int? totalCount;

  @override
  String toString() {
    return 'ChatPageResp(pageRecords: $pageRecords, pageNum: $pageNum, pageSize: $pageSize, pageCount: $pageCount, totalCount: $totalCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChatPageResp &&
            const DeepCollectionEquality()
                .equals(other._pageRecords, _pageRecords) &&
            (identical(other.pageNum, pageNum) || other.pageNum == pageNum) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.pageCount, pageCount) ||
                other.pageCount == pageCount) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_pageRecords),
      pageNum,
      pageSize,
      pageCount,
      totalCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChatPageRespCopyWith<_$_ChatPageResp> get copyWith =>
      __$$_ChatPageRespCopyWithImpl<_$_ChatPageResp>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ChatPageRespToJson(
      this,
    );
  }
}

abstract class _ChatPageResp implements ChatPageResp {
  const factory _ChatPageResp(
      {final List<ReplyData>? pageRecords,
      final int? pageNum,
      final int? pageSize,
      final int? pageCount,
      final int? totalCount}) = _$_ChatPageResp;

  factory _ChatPageResp.fromJson(Map<String, dynamic> json) =
      _$_ChatPageResp.fromJson;

  @override
  List<ReplyData>? get pageRecords;
  @override
  int? get pageNum;
  @override
  int? get pageSize;
  @override
  int? get pageCount;
  @override
  int? get totalCount;
  @override
  @JsonKey(ignore: true)
  _$$_ChatPageRespCopyWith<_$_ChatPageResp> get copyWith =>
      throw _privateConstructorUsedError;
}
