// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'customer_config_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CustomerConfigDataType _$CustomerConfigDataTypeFromJson(
    Map<String, dynamic> json) {
  return _CustomerConfigDataType.fromJson(json);
}

/// @nodoc
mixin _$CustomerConfigDataType {
  int? get sessionId => throw _privateConstructorUsedError;
  List<GreetInfoList>? get greetInfoList => throw _privateConstructorUsedError;
  List<AiQuickEntranceInfoList>? get aiQuickEntranceInfoList =>
      throw _privateConstructorUsedError;
  AiCardInfo? get aiCardInfo => throw _privateConstructorUsedError;
  bool? get visitorMark => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CustomerConfigDataTypeCopyWith<CustomerConfigDataType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerConfigDataTypeCopyWith<$Res> {
  factory $CustomerConfigDataTypeCopyWith(CustomerConfigDataType value,
          $Res Function(CustomerConfigDataType) then) =
      _$CustomerConfigDataTypeCopyWithImpl<$Res, CustomerConfigDataType>;
  @useResult
  $Res call(
      {int? sessionId,
      List<GreetInfoList>? greetInfoList,
      List<AiQuickEntranceInfoList>? aiQuickEntranceInfoList,
      AiCardInfo? aiCardInfo,
      bool? visitorMark});

  $AiCardInfoCopyWith<$Res>? get aiCardInfo;
}

/// @nodoc
class _$CustomerConfigDataTypeCopyWithImpl<$Res,
        $Val extends CustomerConfigDataType>
    implements $CustomerConfigDataTypeCopyWith<$Res> {
  _$CustomerConfigDataTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? greetInfoList = freezed,
    Object? aiQuickEntranceInfoList = freezed,
    Object? aiCardInfo = freezed,
    Object? visitorMark = freezed,
  }) {
    return _then(_value.copyWith(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      greetInfoList: freezed == greetInfoList
          ? _value.greetInfoList
          : greetInfoList // ignore: cast_nullable_to_non_nullable
              as List<GreetInfoList>?,
      aiQuickEntranceInfoList: freezed == aiQuickEntranceInfoList
          ? _value.aiQuickEntranceInfoList
          : aiQuickEntranceInfoList // ignore: cast_nullable_to_non_nullable
              as List<AiQuickEntranceInfoList>?,
      aiCardInfo: freezed == aiCardInfo
          ? _value.aiCardInfo
          : aiCardInfo // ignore: cast_nullable_to_non_nullable
              as AiCardInfo?,
      visitorMark: freezed == visitorMark
          ? _value.visitorMark
          : visitorMark // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AiCardInfoCopyWith<$Res>? get aiCardInfo {
    if (_value.aiCardInfo == null) {
      return null;
    }

    return $AiCardInfoCopyWith<$Res>(_value.aiCardInfo!, (value) {
      return _then(_value.copyWith(aiCardInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CustomerConfigDataTypeCopyWith<$Res>
    implements $CustomerConfigDataTypeCopyWith<$Res> {
  factory _$$_CustomerConfigDataTypeCopyWith(_$_CustomerConfigDataType value,
          $Res Function(_$_CustomerConfigDataType) then) =
      __$$_CustomerConfigDataTypeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? sessionId,
      List<GreetInfoList>? greetInfoList,
      List<AiQuickEntranceInfoList>? aiQuickEntranceInfoList,
      AiCardInfo? aiCardInfo,
      bool? visitorMark});

  @override
  $AiCardInfoCopyWith<$Res>? get aiCardInfo;
}

/// @nodoc
class __$$_CustomerConfigDataTypeCopyWithImpl<$Res>
    extends _$CustomerConfigDataTypeCopyWithImpl<$Res,
        _$_CustomerConfigDataType>
    implements _$$_CustomerConfigDataTypeCopyWith<$Res> {
  __$$_CustomerConfigDataTypeCopyWithImpl(_$_CustomerConfigDataType _value,
      $Res Function(_$_CustomerConfigDataType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? greetInfoList = freezed,
    Object? aiQuickEntranceInfoList = freezed,
    Object? aiCardInfo = freezed,
    Object? visitorMark = freezed,
  }) {
    return _then(_$_CustomerConfigDataType(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      greetInfoList: freezed == greetInfoList
          ? _value._greetInfoList
          : greetInfoList // ignore: cast_nullable_to_non_nullable
              as List<GreetInfoList>?,
      aiQuickEntranceInfoList: freezed == aiQuickEntranceInfoList
          ? _value._aiQuickEntranceInfoList
          : aiQuickEntranceInfoList // ignore: cast_nullable_to_non_nullable
              as List<AiQuickEntranceInfoList>?,
      aiCardInfo: freezed == aiCardInfo
          ? _value.aiCardInfo
          : aiCardInfo // ignore: cast_nullable_to_non_nullable
              as AiCardInfo?,
      visitorMark: freezed == visitorMark
          ? _value.visitorMark
          : visitorMark // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CustomerConfigDataType implements _CustomerConfigDataType {
  const _$_CustomerConfigDataType(
      {this.sessionId,
      final List<GreetInfoList>? greetInfoList,
      final List<AiQuickEntranceInfoList>? aiQuickEntranceInfoList,
      this.aiCardInfo,
      this.visitorMark})
      : _greetInfoList = greetInfoList,
        _aiQuickEntranceInfoList = aiQuickEntranceInfoList;

  factory _$_CustomerConfigDataType.fromJson(Map<String, dynamic> json) =>
      _$$_CustomerConfigDataTypeFromJson(json);

  @override
  final int? sessionId;
  final List<GreetInfoList>? _greetInfoList;
  @override
  List<GreetInfoList>? get greetInfoList {
    final value = _greetInfoList;
    if (value == null) return null;
    if (_greetInfoList is EqualUnmodifiableListView) return _greetInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<AiQuickEntranceInfoList>? _aiQuickEntranceInfoList;
  @override
  List<AiQuickEntranceInfoList>? get aiQuickEntranceInfoList {
    final value = _aiQuickEntranceInfoList;
    if (value == null) return null;
    if (_aiQuickEntranceInfoList is EqualUnmodifiableListView)
      return _aiQuickEntranceInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AiCardInfo? aiCardInfo;
  @override
  final bool? visitorMark;

  @override
  String toString() {
    return 'CustomerConfigDataType(sessionId: $sessionId, greetInfoList: $greetInfoList, aiQuickEntranceInfoList: $aiQuickEntranceInfoList, aiCardInfo: $aiCardInfo, visitorMark: $visitorMark)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CustomerConfigDataType &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            const DeepCollectionEquality()
                .equals(other._greetInfoList, _greetInfoList) &&
            const DeepCollectionEquality().equals(
                other._aiQuickEntranceInfoList, _aiQuickEntranceInfoList) &&
            (identical(other.aiCardInfo, aiCardInfo) ||
                other.aiCardInfo == aiCardInfo) &&
            (identical(other.visitorMark, visitorMark) ||
                other.visitorMark == visitorMark));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionId,
      const DeepCollectionEquality().hash(_greetInfoList),
      const DeepCollectionEquality().hash(_aiQuickEntranceInfoList),
      aiCardInfo,
      visitorMark);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CustomerConfigDataTypeCopyWith<_$_CustomerConfigDataType> get copyWith =>
      __$$_CustomerConfigDataTypeCopyWithImpl<_$_CustomerConfigDataType>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CustomerConfigDataTypeToJson(
      this,
    );
  }
}

abstract class _CustomerConfigDataType implements CustomerConfigDataType {
  const factory _CustomerConfigDataType(
      {final int? sessionId,
      final List<GreetInfoList>? greetInfoList,
      final List<AiQuickEntranceInfoList>? aiQuickEntranceInfoList,
      final AiCardInfo? aiCardInfo,
      final bool? visitorMark}) = _$_CustomerConfigDataType;

  factory _CustomerConfigDataType.fromJson(Map<String, dynamic> json) =
      _$_CustomerConfigDataType.fromJson;

  @override
  int? get sessionId;
  @override
  List<GreetInfoList>? get greetInfoList;
  @override
  List<AiQuickEntranceInfoList>? get aiQuickEntranceInfoList;
  @override
  AiCardInfo? get aiCardInfo;
  @override
  bool? get visitorMark;
  @override
  @JsonKey(ignore: true)
  _$$_CustomerConfigDataTypeCopyWith<_$_CustomerConfigDataType> get copyWith =>
      throw _privateConstructorUsedError;
}

AiCardInfo _$AiCardInfoFromJson(Map<String, dynamic> json) {
  return _AiCardInfo.fromJson(json);
}

/// @nodoc
mixin _$AiCardInfo {
  List<FaqList>? get faqList => throw _privateConstructorUsedError;
  List<QuickServiceList>? get quickServiceList =>
      throw _privateConstructorUsedError;
  String? get faqTitle => throw _privateConstructorUsedError;
  String? get quickServiceSubLink => throw _privateConstructorUsedError;
  String? get quickServiceSubText => throw _privateConstructorUsedError;
  String? get quickServiceTitle => throw _privateConstructorUsedError;
  String? get tabOnlineLink => throw _privateConstructorUsedError;
  String? get tabOrderLink => throw _privateConstructorUsedError;
  String? get tabTelPhone => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AiCardInfoCopyWith<AiCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiCardInfoCopyWith<$Res> {
  factory $AiCardInfoCopyWith(
          AiCardInfo value, $Res Function(AiCardInfo) then) =
      _$AiCardInfoCopyWithImpl<$Res, AiCardInfo>;
  @useResult
  $Res call(
      {List<FaqList>? faqList,
      List<QuickServiceList>? quickServiceList,
      String? faqTitle,
      String? quickServiceSubLink,
      String? quickServiceSubText,
      String? quickServiceTitle,
      String? tabOnlineLink,
      String? tabOrderLink,
      String? tabTelPhone});
}

/// @nodoc
class _$AiCardInfoCopyWithImpl<$Res, $Val extends AiCardInfo>
    implements $AiCardInfoCopyWith<$Res> {
  _$AiCardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? faqList = freezed,
    Object? quickServiceList = freezed,
    Object? faqTitle = freezed,
    Object? quickServiceSubLink = freezed,
    Object? quickServiceSubText = freezed,
    Object? quickServiceTitle = freezed,
    Object? tabOnlineLink = freezed,
    Object? tabOrderLink = freezed,
    Object? tabTelPhone = freezed,
  }) {
    return _then(_value.copyWith(
      faqList: freezed == faqList
          ? _value.faqList
          : faqList // ignore: cast_nullable_to_non_nullable
              as List<FaqList>?,
      quickServiceList: freezed == quickServiceList
          ? _value.quickServiceList
          : quickServiceList // ignore: cast_nullable_to_non_nullable
              as List<QuickServiceList>?,
      faqTitle: freezed == faqTitle
          ? _value.faqTitle
          : faqTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceSubLink: freezed == quickServiceSubLink
          ? _value.quickServiceSubLink
          : quickServiceSubLink // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceSubText: freezed == quickServiceSubText
          ? _value.quickServiceSubText
          : quickServiceSubText // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceTitle: freezed == quickServiceTitle
          ? _value.quickServiceTitle
          : quickServiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      tabOnlineLink: freezed == tabOnlineLink
          ? _value.tabOnlineLink
          : tabOnlineLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tabOrderLink: freezed == tabOrderLink
          ? _value.tabOrderLink
          : tabOrderLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tabTelPhone: freezed == tabTelPhone
          ? _value.tabTelPhone
          : tabTelPhone // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AiCardInfoCopyWith<$Res>
    implements $AiCardInfoCopyWith<$Res> {
  factory _$$_AiCardInfoCopyWith(
          _$_AiCardInfo value, $Res Function(_$_AiCardInfo) then) =
      __$$_AiCardInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FaqList>? faqList,
      List<QuickServiceList>? quickServiceList,
      String? faqTitle,
      String? quickServiceSubLink,
      String? quickServiceSubText,
      String? quickServiceTitle,
      String? tabOnlineLink,
      String? tabOrderLink,
      String? tabTelPhone});
}

/// @nodoc
class __$$_AiCardInfoCopyWithImpl<$Res>
    extends _$AiCardInfoCopyWithImpl<$Res, _$_AiCardInfo>
    implements _$$_AiCardInfoCopyWith<$Res> {
  __$$_AiCardInfoCopyWithImpl(
      _$_AiCardInfo _value, $Res Function(_$_AiCardInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? faqList = freezed,
    Object? quickServiceList = freezed,
    Object? faqTitle = freezed,
    Object? quickServiceSubLink = freezed,
    Object? quickServiceSubText = freezed,
    Object? quickServiceTitle = freezed,
    Object? tabOnlineLink = freezed,
    Object? tabOrderLink = freezed,
    Object? tabTelPhone = freezed,
  }) {
    return _then(_$_AiCardInfo(
      faqList: freezed == faqList
          ? _value._faqList
          : faqList // ignore: cast_nullable_to_non_nullable
              as List<FaqList>?,
      quickServiceList: freezed == quickServiceList
          ? _value._quickServiceList
          : quickServiceList // ignore: cast_nullable_to_non_nullable
              as List<QuickServiceList>?,
      faqTitle: freezed == faqTitle
          ? _value.faqTitle
          : faqTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceSubLink: freezed == quickServiceSubLink
          ? _value.quickServiceSubLink
          : quickServiceSubLink // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceSubText: freezed == quickServiceSubText
          ? _value.quickServiceSubText
          : quickServiceSubText // ignore: cast_nullable_to_non_nullable
              as String?,
      quickServiceTitle: freezed == quickServiceTitle
          ? _value.quickServiceTitle
          : quickServiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      tabOnlineLink: freezed == tabOnlineLink
          ? _value.tabOnlineLink
          : tabOnlineLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tabOrderLink: freezed == tabOrderLink
          ? _value.tabOrderLink
          : tabOrderLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tabTelPhone: freezed == tabTelPhone
          ? _value.tabTelPhone
          : tabTelPhone // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AiCardInfo implements _AiCardInfo {
  const _$_AiCardInfo(
      {final List<FaqList>? faqList,
      final List<QuickServiceList>? quickServiceList,
      this.faqTitle,
      this.quickServiceSubLink,
      this.quickServiceSubText,
      this.quickServiceTitle,
      this.tabOnlineLink,
      this.tabOrderLink,
      this.tabTelPhone})
      : _faqList = faqList,
        _quickServiceList = quickServiceList;

  factory _$_AiCardInfo.fromJson(Map<String, dynamic> json) =>
      _$$_AiCardInfoFromJson(json);

  final List<FaqList>? _faqList;
  @override
  List<FaqList>? get faqList {
    final value = _faqList;
    if (value == null) return null;
    if (_faqList is EqualUnmodifiableListView) return _faqList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<QuickServiceList>? _quickServiceList;
  @override
  List<QuickServiceList>? get quickServiceList {
    final value = _quickServiceList;
    if (value == null) return null;
    if (_quickServiceList is EqualUnmodifiableListView)
      return _quickServiceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? faqTitle;
  @override
  final String? quickServiceSubLink;
  @override
  final String? quickServiceSubText;
  @override
  final String? quickServiceTitle;
  @override
  final String? tabOnlineLink;
  @override
  final String? tabOrderLink;
  @override
  final String? tabTelPhone;

  @override
  String toString() {
    return 'AiCardInfo(faqList: $faqList, quickServiceList: $quickServiceList, faqTitle: $faqTitle, quickServiceSubLink: $quickServiceSubLink, quickServiceSubText: $quickServiceSubText, quickServiceTitle: $quickServiceTitle, tabOnlineLink: $tabOnlineLink, tabOrderLink: $tabOrderLink, tabTelPhone: $tabTelPhone)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AiCardInfo &&
            const DeepCollectionEquality().equals(other._faqList, _faqList) &&
            const DeepCollectionEquality()
                .equals(other._quickServiceList, _quickServiceList) &&
            (identical(other.faqTitle, faqTitle) ||
                other.faqTitle == faqTitle) &&
            (identical(other.quickServiceSubLink, quickServiceSubLink) ||
                other.quickServiceSubLink == quickServiceSubLink) &&
            (identical(other.quickServiceSubText, quickServiceSubText) ||
                other.quickServiceSubText == quickServiceSubText) &&
            (identical(other.quickServiceTitle, quickServiceTitle) ||
                other.quickServiceTitle == quickServiceTitle) &&
            (identical(other.tabOnlineLink, tabOnlineLink) ||
                other.tabOnlineLink == tabOnlineLink) &&
            (identical(other.tabOrderLink, tabOrderLink) ||
                other.tabOrderLink == tabOrderLink) &&
            (identical(other.tabTelPhone, tabTelPhone) ||
                other.tabTelPhone == tabTelPhone));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_faqList),
      const DeepCollectionEquality().hash(_quickServiceList),
      faqTitle,
      quickServiceSubLink,
      quickServiceSubText,
      quickServiceTitle,
      tabOnlineLink,
      tabOrderLink,
      tabTelPhone);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AiCardInfoCopyWith<_$_AiCardInfo> get copyWith =>
      __$$_AiCardInfoCopyWithImpl<_$_AiCardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AiCardInfoToJson(
      this,
    );
  }
}

abstract class _AiCardInfo implements AiCardInfo {
  const factory _AiCardInfo(
      {final List<FaqList>? faqList,
      final List<QuickServiceList>? quickServiceList,
      final String? faqTitle,
      final String? quickServiceSubLink,
      final String? quickServiceSubText,
      final String? quickServiceTitle,
      final String? tabOnlineLink,
      final String? tabOrderLink,
      final String? tabTelPhone}) = _$_AiCardInfo;

  factory _AiCardInfo.fromJson(Map<String, dynamic> json) =
      _$_AiCardInfo.fromJson;

  @override
  List<FaqList>? get faqList;
  @override
  List<QuickServiceList>? get quickServiceList;
  @override
  String? get faqTitle;
  @override
  String? get quickServiceSubLink;
  @override
  String? get quickServiceSubText;
  @override
  String? get quickServiceTitle;
  @override
  String? get tabOnlineLink;
  @override
  String? get tabOrderLink;
  @override
  String? get tabTelPhone;
  @override
  @JsonKey(ignore: true)
  _$$_AiCardInfoCopyWith<_$_AiCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

FaqList _$FaqListFromJson(Map<String, dynamic> json) {
  return _FaqList.fromJson(json);
}

/// @nodoc
mixin _$FaqList {
  String? get subTitle => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  int? get indexId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FaqListCopyWith<FaqList> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FaqListCopyWith<$Res> {
  factory $FaqListCopyWith(FaqList value, $Res Function(FaqList) then) =
      _$FaqListCopyWithImpl<$Res, FaqList>;
  @useResult
  $Res call({String? subTitle, String? text, String? title, int? indexId});
}

/// @nodoc
class _$FaqListCopyWithImpl<$Res, $Val extends FaqList>
    implements $FaqListCopyWith<$Res> {
  _$FaqListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subTitle = freezed,
    Object? text = freezed,
    Object? title = freezed,
    Object? indexId = freezed,
  }) {
    return _then(_value.copyWith(
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      indexId: freezed == indexId
          ? _value.indexId
          : indexId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FaqListCopyWith<$Res> implements $FaqListCopyWith<$Res> {
  factory _$$_FaqListCopyWith(
          _$_FaqList value, $Res Function(_$_FaqList) then) =
      __$$_FaqListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? subTitle, String? text, String? title, int? indexId});
}

/// @nodoc
class __$$_FaqListCopyWithImpl<$Res>
    extends _$FaqListCopyWithImpl<$Res, _$_FaqList>
    implements _$$_FaqListCopyWith<$Res> {
  __$$_FaqListCopyWithImpl(_$_FaqList _value, $Res Function(_$_FaqList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subTitle = freezed,
    Object? text = freezed,
    Object? title = freezed,
    Object? indexId = freezed,
  }) {
    return _then(_$_FaqList(
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      indexId: freezed == indexId
          ? _value.indexId
          : indexId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FaqList implements _FaqList {
  const _$_FaqList({this.subTitle, this.text, this.title, this.indexId});

  factory _$_FaqList.fromJson(Map<String, dynamic> json) =>
      _$$_FaqListFromJson(json);

  @override
  final String? subTitle;
  @override
  final String? text;
  @override
  final String? title;
  @override
  final int? indexId;

  @override
  String toString() {
    return 'FaqList(subTitle: $subTitle, text: $text, title: $title, indexId: $indexId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FaqList &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.indexId, indexId) || other.indexId == indexId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subTitle, text, title, indexId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FaqListCopyWith<_$_FaqList> get copyWith =>
      __$$_FaqListCopyWithImpl<_$_FaqList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FaqListToJson(
      this,
    );
  }
}

abstract class _FaqList implements FaqList {
  const factory _FaqList(
      {final String? subTitle,
      final String? text,
      final String? title,
      final int? indexId}) = _$_FaqList;

  factory _FaqList.fromJson(Map<String, dynamic> json) = _$_FaqList.fromJson;

  @override
  String? get subTitle;
  @override
  String? get text;
  @override
  String? get title;
  @override
  int? get indexId;
  @override
  @JsonKey(ignore: true)
  _$$_FaqListCopyWith<_$_FaqList> get copyWith =>
      throw _privateConstructorUsedError;
}

QuickServiceList _$QuickServiceListFromJson(Map<String, dynamic> json) {
  return _QuickServiceList.fromJson(json);
}

/// @nodoc
mixin _$QuickServiceList {
  String? get icon => throw _privateConstructorUsedError;
  String? get link => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuickServiceListCopyWith<QuickServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickServiceListCopyWith<$Res> {
  factory $QuickServiceListCopyWith(
          QuickServiceList value, $Res Function(QuickServiceList) then) =
      _$QuickServiceListCopyWithImpl<$Res, QuickServiceList>;
  @useResult
  $Res call({String? icon, String? link, String? text});
}

/// @nodoc
class _$QuickServiceListCopyWithImpl<$Res, $Val extends QuickServiceList>
    implements $QuickServiceListCopyWith<$Res> {
  _$QuickServiceListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? link = freezed,
    Object? text = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      link: freezed == link
          ? _value.link
          : link // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_QuickServiceListCopyWith<$Res>
    implements $QuickServiceListCopyWith<$Res> {
  factory _$$_QuickServiceListCopyWith(
          _$_QuickServiceList value, $Res Function(_$_QuickServiceList) then) =
      __$$_QuickServiceListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, String? link, String? text});
}

/// @nodoc
class __$$_QuickServiceListCopyWithImpl<$Res>
    extends _$QuickServiceListCopyWithImpl<$Res, _$_QuickServiceList>
    implements _$$_QuickServiceListCopyWith<$Res> {
  __$$_QuickServiceListCopyWithImpl(
      _$_QuickServiceList _value, $Res Function(_$_QuickServiceList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? link = freezed,
    Object? text = freezed,
  }) {
    return _then(_$_QuickServiceList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      link: freezed == link
          ? _value.link
          : link // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_QuickServiceList implements _QuickServiceList {
  const _$_QuickServiceList({this.icon, this.link, this.text});

  factory _$_QuickServiceList.fromJson(Map<String, dynamic> json) =>
      _$$_QuickServiceListFromJson(json);

  @override
  final String? icon;
  @override
  final String? link;
  @override
  final String? text;

  @override
  String toString() {
    return 'QuickServiceList(icon: $icon, link: $link, text: $text)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_QuickServiceList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.text, text) || other.text == text));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, icon, link, text);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_QuickServiceListCopyWith<_$_QuickServiceList> get copyWith =>
      __$$_QuickServiceListCopyWithImpl<_$_QuickServiceList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_QuickServiceListToJson(
      this,
    );
  }
}

abstract class _QuickServiceList implements QuickServiceList {
  const factory _QuickServiceList(
      {final String? icon,
      final String? link,
      final String? text}) = _$_QuickServiceList;

  factory _QuickServiceList.fromJson(Map<String, dynamic> json) =
      _$_QuickServiceList.fromJson;

  @override
  String? get icon;
  @override
  String? get link;
  @override
  String? get text;
  @override
  @JsonKey(ignore: true)
  _$$_QuickServiceListCopyWith<_$_QuickServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

GreetInfoList _$GreetInfoListFromJson(Map<String, dynamic> json) {
  return _GreetInfoList.fromJson(json);
}

/// @nodoc
mixin _$GreetInfoList {
  String? get icon => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GreetInfoListCopyWith<GreetInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GreetInfoListCopyWith<$Res> {
  factory $GreetInfoListCopyWith(
          GreetInfoList value, $Res Function(GreetInfoList) then) =
      _$GreetInfoListCopyWithImpl<$Res, GreetInfoList>;
  @useResult
  $Res call({String? icon, String? type, String? text});
}

/// @nodoc
class _$GreetInfoListCopyWithImpl<$Res, $Val extends GreetInfoList>
    implements $GreetInfoListCopyWith<$Res> {
  _$GreetInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? type = freezed,
    Object? text = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GreetInfoListCopyWith<$Res>
    implements $GreetInfoListCopyWith<$Res> {
  factory _$$_GreetInfoListCopyWith(
          _$_GreetInfoList value, $Res Function(_$_GreetInfoList) then) =
      __$$_GreetInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, String? type, String? text});
}

/// @nodoc
class __$$_GreetInfoListCopyWithImpl<$Res>
    extends _$GreetInfoListCopyWithImpl<$Res, _$_GreetInfoList>
    implements _$$_GreetInfoListCopyWith<$Res> {
  __$$_GreetInfoListCopyWithImpl(
      _$_GreetInfoList _value, $Res Function(_$_GreetInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? type = freezed,
    Object? text = freezed,
  }) {
    return _then(_$_GreetInfoList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GreetInfoList implements _GreetInfoList {
  const _$_GreetInfoList({this.icon, this.type, this.text});

  factory _$_GreetInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_GreetInfoListFromJson(json);

  @override
  final String? icon;
  @override
  final String? type;
  @override
  final String? text;

  @override
  String toString() {
    return 'GreetInfoList(icon: $icon, type: $type, text: $text)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GreetInfoList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.text, text) || other.text == text));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, icon, type, text);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GreetInfoListCopyWith<_$_GreetInfoList> get copyWith =>
      __$$_GreetInfoListCopyWithImpl<_$_GreetInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GreetInfoListToJson(
      this,
    );
  }
}

abstract class _GreetInfoList implements GreetInfoList {
  const factory _GreetInfoList(
      {final String? icon,
      final String? type,
      final String? text}) = _$_GreetInfoList;

  factory _GreetInfoList.fromJson(Map<String, dynamic> json) =
      _$_GreetInfoList.fromJson;

  @override
  String? get icon;
  @override
  String? get type;
  @override
  String? get text;
  @override
  @JsonKey(ignore: true)
  _$$_GreetInfoListCopyWith<_$_GreetInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

AiQuickEntranceInfoList _$AiQuickEntranceInfoListFromJson(
    Map<String, dynamic> json) {
  return _AiQuickEntranceInfoList.fromJson(json);
}

/// @nodoc
mixin _$AiQuickEntranceInfoList {
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AiQuickEntranceInfoListCopyWith<AiQuickEntranceInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiQuickEntranceInfoListCopyWith<$Res> {
  factory $AiQuickEntranceInfoListCopyWith(AiQuickEntranceInfoList value,
          $Res Function(AiQuickEntranceInfoList) then) =
      _$AiQuickEntranceInfoListCopyWithImpl<$Res, AiQuickEntranceInfoList>;
  @useResult
  $Res call({String? icon, String? name, String? url, int? order});
}

/// @nodoc
class _$AiQuickEntranceInfoListCopyWithImpl<$Res,
        $Val extends AiQuickEntranceInfoList>
    implements $AiQuickEntranceInfoListCopyWith<$Res> {
  _$AiQuickEntranceInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? url = freezed,
    Object? order = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AiQuickEntranceInfoListCopyWith<$Res>
    implements $AiQuickEntranceInfoListCopyWith<$Res> {
  factory _$$_AiQuickEntranceInfoListCopyWith(_$_AiQuickEntranceInfoList value,
          $Res Function(_$_AiQuickEntranceInfoList) then) =
      __$$_AiQuickEntranceInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, String? name, String? url, int? order});
}

/// @nodoc
class __$$_AiQuickEntranceInfoListCopyWithImpl<$Res>
    extends _$AiQuickEntranceInfoListCopyWithImpl<$Res,
        _$_AiQuickEntranceInfoList>
    implements _$$_AiQuickEntranceInfoListCopyWith<$Res> {
  __$$_AiQuickEntranceInfoListCopyWithImpl(_$_AiQuickEntranceInfoList _value,
      $Res Function(_$_AiQuickEntranceInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? url = freezed,
    Object? order = freezed,
  }) {
    return _then(_$_AiQuickEntranceInfoList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AiQuickEntranceInfoList implements _AiQuickEntranceInfoList {
  const _$_AiQuickEntranceInfoList(
      {this.icon, this.name, this.url, this.order});

  factory _$_AiQuickEntranceInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_AiQuickEntranceInfoListFromJson(json);

  @override
  final String? icon;
  @override
  final String? name;
  @override
  final String? url;
  @override
  final int? order;

  @override
  String toString() {
    return 'AiQuickEntranceInfoList(icon: $icon, name: $name, url: $url, order: $order)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AiQuickEntranceInfoList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.order, order) || other.order == order));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, icon, name, url, order);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AiQuickEntranceInfoListCopyWith<_$_AiQuickEntranceInfoList>
      get copyWith =>
          __$$_AiQuickEntranceInfoListCopyWithImpl<_$_AiQuickEntranceInfoList>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AiQuickEntranceInfoListToJson(
      this,
    );
  }
}

abstract class _AiQuickEntranceInfoList implements AiQuickEntranceInfoList {
  const factory _AiQuickEntranceInfoList(
      {final String? icon,
      final String? name,
      final String? url,
      final int? order}) = _$_AiQuickEntranceInfoList;

  factory _AiQuickEntranceInfoList.fromJson(Map<String, dynamic> json) =
      _$_AiQuickEntranceInfoList.fromJson;

  @override
  String? get icon;
  @override
  String? get name;
  @override
  String? get url;
  @override
  int? get order;
  @override
  @JsonKey(ignore: true)
  _$$_AiQuickEntranceInfoListCopyWith<_$_AiQuickEntranceInfoList>
      get copyWith => throw _privateConstructorUsedError;
}

ReplyData _$ReplyDataFromJson(Map<String, dynamic> json) {
  return _ReplyData.fromJson(json);
}

/// @nodoc
mixin _$ReplyData {
  String? get content => throw _privateConstructorUsedError;
  String? get role => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  IntentVo? get intentVo => throw _privateConstructorUsedError;
  ReplyRef? get ref => throw _privateConstructorUsedError;
  int? get visitorSessionId =>
      throw _privateConstructorUsedError; // 游客模式下会话的唯一ID
// ignore: invalid_annotation_target
  @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
  CardConfig? get cardConfig => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReplyDataCopyWith<ReplyData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReplyDataCopyWith<$Res> {
  factory $ReplyDataCopyWith(ReplyData value, $Res Function(ReplyData) then) =
      _$ReplyDataCopyWithImpl<$Res, ReplyData>;
  @useResult
  $Res call(
      {String? content,
      String? role,
      int? id,
      IntentVo? intentVo,
      ReplyRef? ref,
      int? visitorSessionId,
      @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
      CardConfig? cardConfig});

  $IntentVoCopyWith<$Res>? get intentVo;
  $ReplyRefCopyWith<$Res>? get ref;
  $CardConfigCopyWith<$Res>? get cardConfig;
}

/// @nodoc
class _$ReplyDataCopyWithImpl<$Res, $Val extends ReplyData>
    implements $ReplyDataCopyWith<$Res> {
  _$ReplyDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? role = freezed,
    Object? id = freezed,
    Object? intentVo = freezed,
    Object? ref = freezed,
    Object? visitorSessionId = freezed,
    Object? cardConfig = freezed,
  }) {
    return _then(_value.copyWith(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      intentVo: freezed == intentVo
          ? _value.intentVo
          : intentVo // ignore: cast_nullable_to_non_nullable
              as IntentVo?,
      ref: freezed == ref
          ? _value.ref
          : ref // ignore: cast_nullable_to_non_nullable
              as ReplyRef?,
      visitorSessionId: freezed == visitorSessionId
          ? _value.visitorSessionId
          : visitorSessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      cardConfig: freezed == cardConfig
          ? _value.cardConfig
          : cardConfig // ignore: cast_nullable_to_non_nullable
              as CardConfig?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $IntentVoCopyWith<$Res>? get intentVo {
    if (_value.intentVo == null) {
      return null;
    }

    return $IntentVoCopyWith<$Res>(_value.intentVo!, (value) {
      return _then(_value.copyWith(intentVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ReplyRefCopyWith<$Res>? get ref {
    if (_value.ref == null) {
      return null;
    }

    return $ReplyRefCopyWith<$Res>(_value.ref!, (value) {
      return _then(_value.copyWith(ref: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CardConfigCopyWith<$Res>? get cardConfig {
    if (_value.cardConfig == null) {
      return null;
    }

    return $CardConfigCopyWith<$Res>(_value.cardConfig!, (value) {
      return _then(_value.copyWith(cardConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ReplyDataCopyWith<$Res> implements $ReplyDataCopyWith<$Res> {
  factory _$$_ReplyDataCopyWith(
          _$_ReplyData value, $Res Function(_$_ReplyData) then) =
      __$$_ReplyDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? content,
      String? role,
      int? id,
      IntentVo? intentVo,
      ReplyRef? ref,
      int? visitorSessionId,
      @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
      CardConfig? cardConfig});

  @override
  $IntentVoCopyWith<$Res>? get intentVo;
  @override
  $ReplyRefCopyWith<$Res>? get ref;
  @override
  $CardConfigCopyWith<$Res>? get cardConfig;
}

/// @nodoc
class __$$_ReplyDataCopyWithImpl<$Res>
    extends _$ReplyDataCopyWithImpl<$Res, _$_ReplyData>
    implements _$$_ReplyDataCopyWith<$Res> {
  __$$_ReplyDataCopyWithImpl(
      _$_ReplyData _value, $Res Function(_$_ReplyData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? role = freezed,
    Object? id = freezed,
    Object? intentVo = freezed,
    Object? ref = freezed,
    Object? visitorSessionId = freezed,
    Object? cardConfig = freezed,
  }) {
    return _then(_$_ReplyData(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      intentVo: freezed == intentVo
          ? _value.intentVo
          : intentVo // ignore: cast_nullable_to_non_nullable
              as IntentVo?,
      ref: freezed == ref
          ? _value.ref
          : ref // ignore: cast_nullable_to_non_nullable
              as ReplyRef?,
      visitorSessionId: freezed == visitorSessionId
          ? _value.visitorSessionId
          : visitorSessionId // ignore: cast_nullable_to_non_nullable
              as int?,
      cardConfig: freezed == cardConfig
          ? _value.cardConfig
          : cardConfig // ignore: cast_nullable_to_non_nullable
              as CardConfig?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ReplyData implements _ReplyData {
  const _$_ReplyData(
      {this.content,
      this.role,
      this.id,
      this.intentVo,
      this.ref,
      this.visitorSessionId,
      @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal) this.cardConfig});

  factory _$_ReplyData.fromJson(Map<String, dynamic> json) =>
      _$$_ReplyDataFromJson(json);

  @override
  final String? content;
  @override
  final String? role;
  @override
  final int? id;
  @override
  final IntentVo? intentVo;
  @override
  final ReplyRef? ref;
  @override
  final int? visitorSessionId;
// 游客模式下会话的唯一ID
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
  final CardConfig? cardConfig;

  @override
  String toString() {
    return 'ReplyData(content: $content, role: $role, id: $id, intentVo: $intentVo, ref: $ref, visitorSessionId: $visitorSessionId, cardConfig: $cardConfig)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ReplyData &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.intentVo, intentVo) ||
                other.intentVo == intentVo) &&
            (identical(other.ref, ref) || other.ref == ref) &&
            (identical(other.visitorSessionId, visitorSessionId) ||
                other.visitorSessionId == visitorSessionId) &&
            (identical(other.cardConfig, cardConfig) ||
                other.cardConfig == cardConfig));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, content, role, id, intentVo, ref,
      visitorSessionId, cardConfig);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ReplyDataCopyWith<_$_ReplyData> get copyWith =>
      __$$_ReplyDataCopyWithImpl<_$_ReplyData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ReplyDataToJson(
      this,
    );
  }
}

abstract class _ReplyData implements ReplyData {
  const factory _ReplyData(
      {final String? content,
      final String? role,
      final int? id,
      final IntentVo? intentVo,
      final ReplyRef? ref,
      final int? visitorSessionId,
      @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
      final CardConfig? cardConfig}) = _$_ReplyData;

  factory _ReplyData.fromJson(Map<String, dynamic> json) =
      _$_ReplyData.fromJson;

  @override
  String? get content;
  @override
  String? get role;
  @override
  int? get id;
  @override
  IntentVo? get intentVo;
  @override
  ReplyRef? get ref;
  @override
  int? get visitorSessionId;
  @override // 游客模式下会话的唯一ID
// ignore: invalid_annotation_target
  @JsonKey(name: 'cardConfig', readValue: jsonDecodeVal)
  CardConfig? get cardConfig;
  @override
  @JsonKey(ignore: true)
  _$$_ReplyDataCopyWith<_$_ReplyData> get copyWith =>
      throw _privateConstructorUsedError;
}

CardConfig _$CardConfigFromJson(Map<String, dynamic> json) {
  return _CardConfig.fromJson(json);
}

/// @nodoc
mixin _$CardConfig {
  String? get link => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get imgUrl => throw _privateConstructorUsedError;
  String? get sceneTag => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CardConfigCopyWith<CardConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardConfigCopyWith<$Res> {
  factory $CardConfigCopyWith(
          CardConfig value, $Res Function(CardConfig) then) =
      _$CardConfigCopyWithImpl<$Res, CardConfig>;
  @useResult
  $Res call(
      {String? link,
      String? title,
      String? desc,
      String? imgUrl,
      String? sceneTag});
}

/// @nodoc
class _$CardConfigCopyWithImpl<$Res, $Val extends CardConfig>
    implements $CardConfigCopyWith<$Res> {
  _$CardConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? link = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? imgUrl = freezed,
    Object? sceneTag = freezed,
  }) {
    return _then(_value.copyWith(
      link: freezed == link
          ? _value.link
          : link // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      imgUrl: freezed == imgUrl
          ? _value.imgUrl
          : imgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sceneTag: freezed == sceneTag
          ? _value.sceneTag
          : sceneTag // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CardConfigCopyWith<$Res>
    implements $CardConfigCopyWith<$Res> {
  factory _$$_CardConfigCopyWith(
          _$_CardConfig value, $Res Function(_$_CardConfig) then) =
      __$$_CardConfigCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? link,
      String? title,
      String? desc,
      String? imgUrl,
      String? sceneTag});
}

/// @nodoc
class __$$_CardConfigCopyWithImpl<$Res>
    extends _$CardConfigCopyWithImpl<$Res, _$_CardConfig>
    implements _$$_CardConfigCopyWith<$Res> {
  __$$_CardConfigCopyWithImpl(
      _$_CardConfig _value, $Res Function(_$_CardConfig) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? link = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? imgUrl = freezed,
    Object? sceneTag = freezed,
  }) {
    return _then(_$_CardConfig(
      link: freezed == link
          ? _value.link
          : link // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      imgUrl: freezed == imgUrl
          ? _value.imgUrl
          : imgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sceneTag: freezed == sceneTag
          ? _value.sceneTag
          : sceneTag // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CardConfig implements _CardConfig {
  const _$_CardConfig(
      {this.link, this.title, this.desc, this.imgUrl, this.sceneTag});

  factory _$_CardConfig.fromJson(Map<String, dynamic> json) =>
      _$$_CardConfigFromJson(json);

  @override
  final String? link;
  @override
  final String? title;
  @override
  final String? desc;
  @override
  final String? imgUrl;
  @override
  final String? sceneTag;

  @override
  String toString() {
    return 'CardConfig(link: $link, title: $title, desc: $desc, imgUrl: $imgUrl, sceneTag: $sceneTag)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CardConfig &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.imgUrl, imgUrl) || other.imgUrl == imgUrl) &&
            (identical(other.sceneTag, sceneTag) ||
                other.sceneTag == sceneTag));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, link, title, desc, imgUrl, sceneTag);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CardConfigCopyWith<_$_CardConfig> get copyWith =>
      __$$_CardConfigCopyWithImpl<_$_CardConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CardConfigToJson(
      this,
    );
  }
}

abstract class _CardConfig implements CardConfig {
  const factory _CardConfig(
      {final String? link,
      final String? title,
      final String? desc,
      final String? imgUrl,
      final String? sceneTag}) = _$_CardConfig;

  factory _CardConfig.fromJson(Map<String, dynamic> json) =
      _$_CardConfig.fromJson;

  @override
  String? get link;
  @override
  String? get title;
  @override
  String? get desc;
  @override
  String? get imgUrl;
  @override
  String? get sceneTag;
  @override
  @JsonKey(ignore: true)
  _$$_CardConfigCopyWith<_$_CardConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

IntentVo _$IntentVoFromJson(Map<String, dynamic> json) {
  return _IntentVo.fromJson(json);
}

/// @nodoc
mixin _$IntentVo {
  int? get intent => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IntentVoCopyWith<IntentVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IntentVoCopyWith<$Res> {
  factory $IntentVoCopyWith(IntentVo value, $Res Function(IntentVo) then) =
      _$IntentVoCopyWithImpl<$Res, IntentVo>;
  @useResult
  $Res call({int? intent, String? url, String? type});
}

/// @nodoc
class _$IntentVoCopyWithImpl<$Res, $Val extends IntentVo>
    implements $IntentVoCopyWith<$Res> {
  _$IntentVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? intent = freezed,
    Object? url = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      intent: freezed == intent
          ? _value.intent
          : intent // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_IntentVoCopyWith<$Res> implements $IntentVoCopyWith<$Res> {
  factory _$$_IntentVoCopyWith(
          _$_IntentVo value, $Res Function(_$_IntentVo) then) =
      __$$_IntentVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? intent, String? url, String? type});
}

/// @nodoc
class __$$_IntentVoCopyWithImpl<$Res>
    extends _$IntentVoCopyWithImpl<$Res, _$_IntentVo>
    implements _$$_IntentVoCopyWith<$Res> {
  __$$_IntentVoCopyWithImpl(
      _$_IntentVo _value, $Res Function(_$_IntentVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? intent = freezed,
    Object? url = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_IntentVo(
      intent: freezed == intent
          ? _value.intent
          : intent // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_IntentVo implements _IntentVo {
  const _$_IntentVo({this.intent, this.url, this.type});

  factory _$_IntentVo.fromJson(Map<String, dynamic> json) =>
      _$$_IntentVoFromJson(json);

  @override
  final int? intent;
  @override
  final String? url;
  @override
  final String? type;

  @override
  String toString() {
    return 'IntentVo(intent: $intent, url: $url, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_IntentVo &&
            (identical(other.intent, intent) || other.intent == intent) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, intent, url, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_IntentVoCopyWith<_$_IntentVo> get copyWith =>
      __$$_IntentVoCopyWithImpl<_$_IntentVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_IntentVoToJson(
      this,
    );
  }
}

abstract class _IntentVo implements IntentVo {
  const factory _IntentVo(
      {final int? intent, final String? url, final String? type}) = _$_IntentVo;

  factory _IntentVo.fromJson(Map<String, dynamic> json) = _$_IntentVo.fromJson;

  @override
  int? get intent;
  @override
  String? get url;
  @override
  String? get type;
  @override
  @JsonKey(ignore: true)
  _$$_IntentVoCopyWith<_$_IntentVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ReplyRef _$ReplyRefFromJson(Map<String, dynamic> json) {
  return _ReplyRef.fromJson(json);
}

/// @nodoc
mixin _$ReplyRef {
  int? get id => throw _privateConstructorUsedError;
  String? get user => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReplyRefCopyWith<ReplyRef> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReplyRefCopyWith<$Res> {
  factory $ReplyRefCopyWith(ReplyRef value, $Res Function(ReplyRef) then) =
      _$ReplyRefCopyWithImpl<$Res, ReplyRef>;
  @useResult
  $Res call({int? id, String? user, String? content});
}

/// @nodoc
class _$ReplyRefCopyWithImpl<$Res, $Val extends ReplyRef>
    implements $ReplyRefCopyWith<$Res> {
  _$ReplyRefCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? user = freezed,
    Object? content = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ReplyRefCopyWith<$Res> implements $ReplyRefCopyWith<$Res> {
  factory _$$_ReplyRefCopyWith(
          _$_ReplyRef value, $Res Function(_$_ReplyRef) then) =
      __$$_ReplyRefCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, String? user, String? content});
}

/// @nodoc
class __$$_ReplyRefCopyWithImpl<$Res>
    extends _$ReplyRefCopyWithImpl<$Res, _$_ReplyRef>
    implements _$$_ReplyRefCopyWith<$Res> {
  __$$_ReplyRefCopyWithImpl(
      _$_ReplyRef _value, $Res Function(_$_ReplyRef) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? user = freezed,
    Object? content = freezed,
  }) {
    return _then(_$_ReplyRef(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ReplyRef implements _ReplyRef {
  const _$_ReplyRef({this.id, this.user, this.content});

  factory _$_ReplyRef.fromJson(Map<String, dynamic> json) =>
      _$$_ReplyRefFromJson(json);

  @override
  final int? id;
  @override
  final String? user;
  @override
  final String? content;

  @override
  String toString() {
    return 'ReplyRef(id: $id, user: $user, content: $content)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ReplyRef &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, user, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ReplyRefCopyWith<_$_ReplyRef> get copyWith =>
      __$$_ReplyRefCopyWithImpl<_$_ReplyRef>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ReplyRefToJson(
      this,
    );
  }
}

abstract class _ReplyRef implements ReplyRef {
  const factory _ReplyRef(
      {final int? id, final String? user, final String? content}) = _$_ReplyRef;

  factory _ReplyRef.fromJson(Map<String, dynamic> json) = _$_ReplyRef.fromJson;

  @override
  int? get id;
  @override
  String? get user;
  @override
  String? get content;
  @override
  @JsonKey(ignore: true)
  _$$_ReplyRefCopyWith<_$_ReplyRef> get copyWith =>
      throw _privateConstructorUsedError;
}
