import 'package:jojo_flutter_base/widgets/dialog/dialog_ext.dart';

const _initMustSort = 0;
const _initGuideSort = 100;
const _initNormalSort = 200;
const _pagePath = "/plan/planAutoTransform";

enum AutoTransFormDialogsMessage {
  classNewOpenDialog(_pagePath, "classNewOpenDialog", _initNormalSort + 10, subTypeNormal),
  fullFireDialog(_pagePath, "course_plan_auto_transform_fire",  _initMustSort + 50, subTypeMust),
  planTrainAddTeacherAutoTransFormDialog(_pagePath, "course_plan_auto_train_add_teacher",  _initNormalSort + 10, subTypeNormal),
  planLearnRemindDialog(_pagePath, "course_plan_auto_transform_learn_remind", _initNormalSort + 20, subTypeNormal),
  planAutoCmsDialogDialog(_pagePath, "course_plan_auto_transform_half_cms", _initNormalSort + 30, subTypeNormal);

  const AutoTransFormDialogsMessage(this.pagePath, this.key, this.sort, this.dialogType);

  final String pagePath;

  final String key;

  final int sort;

  final String dialogType;
}