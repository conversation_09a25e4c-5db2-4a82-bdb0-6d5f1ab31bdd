import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/dailytask/dialog/course_dialog_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/dailytask/dialog/landscape_train_add_teacher_dialog.dart'
    as land;
import 'package:jojo_flutter_plan_pkg/pages/plan_home/dailytask/dialog/train_add_teacher_dialog.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/course_home_page_data.dart';

import '../../../guide/page_guide_helper.dart';
import 'auto_transform_dialogs_message.dart';

class TrainAddTeacherAutoTransFormDialog extends BaseDialogWidget {
  List<SchedulePopup>? popups;
  SchedulePopup? coursePopData;
  final String? screenName = "2024试用版学习页";

  TrainAddTeacherAutoTransFormDialog(
      {this.popups,super.key, super.onDialogDismiss, super.onDialogShow})
      : super(
          pagePath: AutoTransFormDialogsMessage
              .planTrainAddTeacherAutoTransFormDialog.pagePath,
          dialogKey: AutoTransFormDialogsMessage
              .planTrainAddTeacherAutoTransFormDialog.key,
          dialogSort: AutoTransFormDialogsMessage
              .planTrainAddTeacherAutoTransFormDialog.sort,
          dialogType: AutoTransFormDialogsMessage
              .planTrainAddTeacherAutoTransFormDialog.dialogType,
        );

  @override
  Future<bool> canShowDialog() async {
    coursePopData =
        await CourseTrainAddTeacherDialogHelper.findCanAutoShowCoursePopupData(
            popups ?? []);
    l.d(tag,"加老师弹窗是否显示 ${coursePopData != null} popups: ${popups}");
    return coursePopData != null;
  }

  @override
  BaseDialogWidgetState<BaseDialogWidget> createState() {
    return _TrainAddTeacher2025DialogState();
  }
}

class _TrainAddTeacher2025DialogState
    extends BaseDialogWidgetState<TrainAddTeacherAutoTransFormDialog> {
  @override
  void initState() {
    super.initState();
    TrainAddTeacherDialog.isDialogShowing = false;
    land.TrainAddTeacherLandscapeDialog.isDialogShowing = false;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.coursePopData == null) {
      l.e("自转化加老师弹窗显示错误"," coursePopData is null");
      widget.dismissDialog();
      return Container();
    }
    return AdaptiveOrientationLayout(portrait: (context) {
      return _portrait(context, widget.coursePopData!);
    }, landscape: (context) {
      return _landscape(context, widget.coursePopData!);
    });
  }

  Widget _portrait(BuildContext context, SchedulePopup coursePopData) {
    return TrainAddTeacherDialog(
      coursePopData: coursePopData,
      canShow: () => true,
      screenName: widget.screenName,
      dismissAction: () => widget.dismissDialog(),
      customHandleAlertTimes: true,
    );
  }

  Widget _landscape(BuildContext context, SchedulePopup coursePopData) {
    return land.TrainAddTeacherLandscapeDialog(
      coursePopData: coursePopData,
      canShow: () => true,
      screenName: widget.screenName,
      dismissAction: () => widget.dismissDialog(),
      customHandleAlertTimes: true,
    );
  }
}
