// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_auto_report_module_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanAutoReportModuleData _$PlanAutoReportModuleDataFromJson(
    Map<String, dynamic> json) {
  return _PlanAutoReportModuleData.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoReportModuleData {
  List<SkinInfo>? get skinInfoList => throw _privateConstructorUsedError;
  String? get briefIntroduction => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoReportModuleDataCopyWith<PlanAutoReportModuleData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoReportModuleDataCopyWith<$Res> {
  factory $PlanAutoReportModuleDataCopyWith(PlanAutoReportModuleData value,
          $Res Function(PlanAutoReportModuleData) then) =
      _$PlanAutoReportModuleDataCopyWithImpl<$Res, PlanAutoReportModuleData>;
  @useResult
  $Res call({List<SkinInfo>? skinInfoList, String? briefIntroduction});
}

/// @nodoc
class _$PlanAutoReportModuleDataCopyWithImpl<$Res,
        $Val extends PlanAutoReportModuleData>
    implements $PlanAutoReportModuleDataCopyWith<$Res> {
  _$PlanAutoReportModuleDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinInfoList = freezed,
    Object? briefIntroduction = freezed,
  }) {
    return _then(_value.copyWith(
      skinInfoList: freezed == skinInfoList
          ? _value.skinInfoList
          : skinInfoList // ignore: cast_nullable_to_non_nullable
              as List<SkinInfo>?,
      briefIntroduction: freezed == briefIntroduction
          ? _value.briefIntroduction
          : briefIntroduction // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanAutoReportModuleDataCopyWith<$Res>
    implements $PlanAutoReportModuleDataCopyWith<$Res> {
  factory _$$_PlanAutoReportModuleDataCopyWith(
          _$_PlanAutoReportModuleData value,
          $Res Function(_$_PlanAutoReportModuleData) then) =
      __$$_PlanAutoReportModuleDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SkinInfo>? skinInfoList, String? briefIntroduction});
}

/// @nodoc
class __$$_PlanAutoReportModuleDataCopyWithImpl<$Res>
    extends _$PlanAutoReportModuleDataCopyWithImpl<$Res,
        _$_PlanAutoReportModuleData>
    implements _$$_PlanAutoReportModuleDataCopyWith<$Res> {
  __$$_PlanAutoReportModuleDataCopyWithImpl(_$_PlanAutoReportModuleData _value,
      $Res Function(_$_PlanAutoReportModuleData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinInfoList = freezed,
    Object? briefIntroduction = freezed,
  }) {
    return _then(_$_PlanAutoReportModuleData(
      skinInfoList: freezed == skinInfoList
          ? _value._skinInfoList
          : skinInfoList // ignore: cast_nullable_to_non_nullable
              as List<SkinInfo>?,
      briefIntroduction: freezed == briefIntroduction
          ? _value.briefIntroduction
          : briefIntroduction // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoReportModuleData implements _PlanAutoReportModuleData {
  const _$_PlanAutoReportModuleData(
      {final List<SkinInfo>? skinInfoList, this.briefIntroduction})
      : _skinInfoList = skinInfoList;

  factory _$_PlanAutoReportModuleData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoReportModuleDataFromJson(json);

  final List<SkinInfo>? _skinInfoList;
  @override
  List<SkinInfo>? get skinInfoList {
    final value = _skinInfoList;
    if (value == null) return null;
    if (_skinInfoList is EqualUnmodifiableListView) return _skinInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? briefIntroduction;

  @override
  String toString() {
    return 'PlanAutoReportModuleData(skinInfoList: $skinInfoList, briefIntroduction: $briefIntroduction)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoReportModuleData &&
            const DeepCollectionEquality()
                .equals(other._skinInfoList, _skinInfoList) &&
            (identical(other.briefIntroduction, briefIntroduction) ||
                other.briefIntroduction == briefIntroduction));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_skinInfoList), briefIntroduction);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoReportModuleDataCopyWith<_$_PlanAutoReportModuleData>
      get copyWith => __$$_PlanAutoReportModuleDataCopyWithImpl<
          _$_PlanAutoReportModuleData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoReportModuleDataToJson(
      this,
    );
  }
}

abstract class _PlanAutoReportModuleData implements PlanAutoReportModuleData {
  const factory _PlanAutoReportModuleData(
      {final List<SkinInfo>? skinInfoList,
      final String? briefIntroduction}) = _$_PlanAutoReportModuleData;

  factory _PlanAutoReportModuleData.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoReportModuleData.fromJson;

  @override
  List<SkinInfo>? get skinInfoList;
  @override
  String? get briefIntroduction;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoReportModuleDataCopyWith<_$_PlanAutoReportModuleData>
      get copyWith => throw _privateConstructorUsedError;
}

SkinInfo _$SkinInfoFromJson(Map<String, dynamic> json) {
  return _SkinInfo.fromJson(json);
}

/// @nodoc
mixin _$SkinInfo {
  String? get skinName => throw _privateConstructorUsedError;
  String? get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkinInfoCopyWith<SkinInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinInfoCopyWith<$Res> {
  factory $SkinInfoCopyWith(SkinInfo value, $Res Function(SkinInfo) then) =
      _$SkinInfoCopyWithImpl<$Res, SkinInfo>;
  @useResult
  $Res call({String? skinName, String? data});
}

/// @nodoc
class _$SkinInfoCopyWithImpl<$Res, $Val extends SkinInfo>
    implements $SkinInfoCopyWith<$Res> {
  _$SkinInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinName = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      skinName: freezed == skinName
          ? _value.skinName
          : skinName // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkinInfoCopyWith<$Res> implements $SkinInfoCopyWith<$Res> {
  factory _$$_SkinInfoCopyWith(
          _$_SkinInfo value, $Res Function(_$_SkinInfo) then) =
      __$$_SkinInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? skinName, String? data});
}

/// @nodoc
class __$$_SkinInfoCopyWithImpl<$Res>
    extends _$SkinInfoCopyWithImpl<$Res, _$_SkinInfo>
    implements _$$_SkinInfoCopyWith<$Res> {
  __$$_SkinInfoCopyWithImpl(
      _$_SkinInfo _value, $Res Function(_$_SkinInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinName = freezed,
    Object? data = freezed,
  }) {
    return _then(_$_SkinInfo(
      skinName: freezed == skinName
          ? _value.skinName
          : skinName // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SkinInfo implements _SkinInfo {
  const _$_SkinInfo({this.skinName, this.data});

  factory _$_SkinInfo.fromJson(Map<String, dynamic> json) =>
      _$$_SkinInfoFromJson(json);

  @override
  final String? skinName;
  @override
  final String? data;

  @override
  String toString() {
    return 'SkinInfo(skinName: $skinName, data: $data)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SkinInfo &&
            (identical(other.skinName, skinName) ||
                other.skinName == skinName) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skinName, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkinInfoCopyWith<_$_SkinInfo> get copyWith =>
      __$$_SkinInfoCopyWithImpl<_$_SkinInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkinInfoToJson(
      this,
    );
  }
}

abstract class _SkinInfo implements SkinInfo {
  const factory _SkinInfo({final String? skinName, final String? data}) =
      _$_SkinInfo;

  factory _SkinInfo.fromJson(Map<String, dynamic> json) = _$_SkinInfo.fromJson;

  @override
  String? get skinName;
  @override
  String? get data;
  @override
  @JsonKey(ignore: true)
  _$$_SkinInfoCopyWith<_$_SkinInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
