// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_auto_transform_page_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanAutoTransformPageData _$PlanAutoTransformPageDataFromJson(
    Map<String, dynamic> json) {
  return _PlanAutoTransformPageData.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoTransformPageData {
  ///训练营 纯享版 增加自转化卡片时 增加了 classId 和 courseKey 对自转化页面无影响，处理多科目显示问题
  int? get classId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  List<ScheduleList>? get scheduleList => throw _privateConstructorUsedError;
  TrialContactTeacherVo? get trialContactTeacherVo =>
      throw _privateConstructorUsedError;
  MoreTrialScheduleVo? get moreTrialScheduleVo =>
      throw _privateConstructorUsedError;
  int? get trialSchedule => throw _privateConstructorUsedError;
  MotivatePopupInfo? get motivatePopupInfo =>
      throw _privateConstructorUsedError;
  LearnRemindPopupInfo? get learnRemindPopupInfo =>
      throw _privateConstructorUsedError;
  UserPageTrialCourseInfo? get userPageTrialCourseInfo =>
      throw _privateConstructorUsedError;
  List<SchedulePopup>? get schedulePopupList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoTransformPageDataCopyWith<PlanAutoTransformPageData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoTransformPageDataCopyWith<$Res> {
  factory $PlanAutoTransformPageDataCopyWith(PlanAutoTransformPageData value,
          $Res Function(PlanAutoTransformPageData) then) =
      _$PlanAutoTransformPageDataCopyWithImpl<$Res, PlanAutoTransformPageData>;
  @useResult
  $Res call(
      {int? classId,
      String? courseKey,
      List<ScheduleList>? scheduleList,
      TrialContactTeacherVo? trialContactTeacherVo,
      MoreTrialScheduleVo? moreTrialScheduleVo,
      int? trialSchedule,
      MotivatePopupInfo? motivatePopupInfo,
      LearnRemindPopupInfo? learnRemindPopupInfo,
      UserPageTrialCourseInfo? userPageTrialCourseInfo,
      List<SchedulePopup>? schedulePopupList});

  $TrialContactTeacherVoCopyWith<$Res>? get trialContactTeacherVo;
  $MoreTrialScheduleVoCopyWith<$Res>? get moreTrialScheduleVo;
  $MotivatePopupInfoCopyWith<$Res>? get motivatePopupInfo;
  $LearnRemindPopupInfoCopyWith<$Res>? get learnRemindPopupInfo;
  $UserPageTrialCourseInfoCopyWith<$Res>? get userPageTrialCourseInfo;
}

/// @nodoc
class _$PlanAutoTransformPageDataCopyWithImpl<$Res,
        $Val extends PlanAutoTransformPageData>
    implements $PlanAutoTransformPageDataCopyWith<$Res> {
  _$PlanAutoTransformPageDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? scheduleList = freezed,
    Object? trialContactTeacherVo = freezed,
    Object? moreTrialScheduleVo = freezed,
    Object? trialSchedule = freezed,
    Object? motivatePopupInfo = freezed,
    Object? learnRemindPopupInfo = freezed,
    Object? userPageTrialCourseInfo = freezed,
    Object? schedulePopupList = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleList: freezed == scheduleList
          ? _value.scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleList>?,
      trialContactTeacherVo: freezed == trialContactTeacherVo
          ? _value.trialContactTeacherVo
          : trialContactTeacherVo // ignore: cast_nullable_to_non_nullable
              as TrialContactTeacherVo?,
      moreTrialScheduleVo: freezed == moreTrialScheduleVo
          ? _value.moreTrialScheduleVo
          : moreTrialScheduleVo // ignore: cast_nullable_to_non_nullable
              as MoreTrialScheduleVo?,
      trialSchedule: freezed == trialSchedule
          ? _value.trialSchedule
          : trialSchedule // ignore: cast_nullable_to_non_nullable
              as int?,
      motivatePopupInfo: freezed == motivatePopupInfo
          ? _value.motivatePopupInfo
          : motivatePopupInfo // ignore: cast_nullable_to_non_nullable
              as MotivatePopupInfo?,
      learnRemindPopupInfo: freezed == learnRemindPopupInfo
          ? _value.learnRemindPopupInfo
          : learnRemindPopupInfo // ignore: cast_nullable_to_non_nullable
              as LearnRemindPopupInfo?,
      userPageTrialCourseInfo: freezed == userPageTrialCourseInfo
          ? _value.userPageTrialCourseInfo
          : userPageTrialCourseInfo // ignore: cast_nullable_to_non_nullable
              as UserPageTrialCourseInfo?,
      schedulePopupList: freezed == schedulePopupList
          ? _value.schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TrialContactTeacherVoCopyWith<$Res>? get trialContactTeacherVo {
    if (_value.trialContactTeacherVo == null) {
      return null;
    }

    return $TrialContactTeacherVoCopyWith<$Res>(_value.trialContactTeacherVo!,
        (value) {
      return _then(_value.copyWith(trialContactTeacherVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MoreTrialScheduleVoCopyWith<$Res>? get moreTrialScheduleVo {
    if (_value.moreTrialScheduleVo == null) {
      return null;
    }

    return $MoreTrialScheduleVoCopyWith<$Res>(_value.moreTrialScheduleVo!,
        (value) {
      return _then(_value.copyWith(moreTrialScheduleVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MotivatePopupInfoCopyWith<$Res>? get motivatePopupInfo {
    if (_value.motivatePopupInfo == null) {
      return null;
    }

    return $MotivatePopupInfoCopyWith<$Res>(_value.motivatePopupInfo!, (value) {
      return _then(_value.copyWith(motivatePopupInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LearnRemindPopupInfoCopyWith<$Res>? get learnRemindPopupInfo {
    if (_value.learnRemindPopupInfo == null) {
      return null;
    }

    return $LearnRemindPopupInfoCopyWith<$Res>(_value.learnRemindPopupInfo!,
        (value) {
      return _then(_value.copyWith(learnRemindPopupInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $UserPageTrialCourseInfoCopyWith<$Res>? get userPageTrialCourseInfo {
    if (_value.userPageTrialCourseInfo == null) {
      return null;
    }

    return $UserPageTrialCourseInfoCopyWith<$Res>(
        _value.userPageTrialCourseInfo!, (value) {
      return _then(_value.copyWith(userPageTrialCourseInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PlanAutoTransformPageDataCopyWith<$Res>
    implements $PlanAutoTransformPageDataCopyWith<$Res> {
  factory _$$_PlanAutoTransformPageDataCopyWith(
          _$_PlanAutoTransformPageData value,
          $Res Function(_$_PlanAutoTransformPageData) then) =
      __$$_PlanAutoTransformPageDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? classId,
      String? courseKey,
      List<ScheduleList>? scheduleList,
      TrialContactTeacherVo? trialContactTeacherVo,
      MoreTrialScheduleVo? moreTrialScheduleVo,
      int? trialSchedule,
      MotivatePopupInfo? motivatePopupInfo,
      LearnRemindPopupInfo? learnRemindPopupInfo,
      UserPageTrialCourseInfo? userPageTrialCourseInfo,
      List<SchedulePopup>? schedulePopupList});

  @override
  $TrialContactTeacherVoCopyWith<$Res>? get trialContactTeacherVo;
  @override
  $MoreTrialScheduleVoCopyWith<$Res>? get moreTrialScheduleVo;
  @override
  $MotivatePopupInfoCopyWith<$Res>? get motivatePopupInfo;
  @override
  $LearnRemindPopupInfoCopyWith<$Res>? get learnRemindPopupInfo;
  @override
  $UserPageTrialCourseInfoCopyWith<$Res>? get userPageTrialCourseInfo;
}

/// @nodoc
class __$$_PlanAutoTransformPageDataCopyWithImpl<$Res>
    extends _$PlanAutoTransformPageDataCopyWithImpl<$Res,
        _$_PlanAutoTransformPageData>
    implements _$$_PlanAutoTransformPageDataCopyWith<$Res> {
  __$$_PlanAutoTransformPageDataCopyWithImpl(
      _$_PlanAutoTransformPageData _value,
      $Res Function(_$_PlanAutoTransformPageData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? scheduleList = freezed,
    Object? trialContactTeacherVo = freezed,
    Object? moreTrialScheduleVo = freezed,
    Object? trialSchedule = freezed,
    Object? motivatePopupInfo = freezed,
    Object? learnRemindPopupInfo = freezed,
    Object? userPageTrialCourseInfo = freezed,
    Object? schedulePopupList = freezed,
  }) {
    return _then(_$_PlanAutoTransformPageData(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleList: freezed == scheduleList
          ? _value._scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleList>?,
      trialContactTeacherVo: freezed == trialContactTeacherVo
          ? _value.trialContactTeacherVo
          : trialContactTeacherVo // ignore: cast_nullable_to_non_nullable
              as TrialContactTeacherVo?,
      moreTrialScheduleVo: freezed == moreTrialScheduleVo
          ? _value.moreTrialScheduleVo
          : moreTrialScheduleVo // ignore: cast_nullable_to_non_nullable
              as MoreTrialScheduleVo?,
      trialSchedule: freezed == trialSchedule
          ? _value.trialSchedule
          : trialSchedule // ignore: cast_nullable_to_non_nullable
              as int?,
      motivatePopupInfo: freezed == motivatePopupInfo
          ? _value.motivatePopupInfo
          : motivatePopupInfo // ignore: cast_nullable_to_non_nullable
              as MotivatePopupInfo?,
      learnRemindPopupInfo: freezed == learnRemindPopupInfo
          ? _value.learnRemindPopupInfo
          : learnRemindPopupInfo // ignore: cast_nullable_to_non_nullable
              as LearnRemindPopupInfo?,
      userPageTrialCourseInfo: freezed == userPageTrialCourseInfo
          ? _value.userPageTrialCourseInfo
          : userPageTrialCourseInfo // ignore: cast_nullable_to_non_nullable
              as UserPageTrialCourseInfo?,
      schedulePopupList: freezed == schedulePopupList
          ? _value._schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoTransformPageData implements _PlanAutoTransformPageData {
  const _$_PlanAutoTransformPageData(
      {this.classId,
      this.courseKey,
      final List<ScheduleList>? scheduleList,
      this.trialContactTeacherVo,
      this.moreTrialScheduleVo,
      this.trialSchedule,
      this.motivatePopupInfo,
      this.learnRemindPopupInfo,
      this.userPageTrialCourseInfo,
      final List<SchedulePopup>? schedulePopupList})
      : _scheduleList = scheduleList,
        _schedulePopupList = schedulePopupList;

  factory _$_PlanAutoTransformPageData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoTransformPageDataFromJson(json);

  ///训练营 纯享版 增加自转化卡片时 增加了 classId 和 courseKey 对自转化页面无影响，处理多科目显示问题
  @override
  final int? classId;
  @override
  final String? courseKey;
  final List<ScheduleList>? _scheduleList;
  @override
  List<ScheduleList>? get scheduleList {
    final value = _scheduleList;
    if (value == null) return null;
    if (_scheduleList is EqualUnmodifiableListView) return _scheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final TrialContactTeacherVo? trialContactTeacherVo;
  @override
  final MoreTrialScheduleVo? moreTrialScheduleVo;
  @override
  final int? trialSchedule;
  @override
  final MotivatePopupInfo? motivatePopupInfo;
  @override
  final LearnRemindPopupInfo? learnRemindPopupInfo;
  @override
  final UserPageTrialCourseInfo? userPageTrialCourseInfo;
  final List<SchedulePopup>? _schedulePopupList;
  @override
  List<SchedulePopup>? get schedulePopupList {
    final value = _schedulePopupList;
    if (value == null) return null;
    if (_schedulePopupList is EqualUnmodifiableListView)
      return _schedulePopupList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PlanAutoTransformPageData(classId: $classId, courseKey: $courseKey, scheduleList: $scheduleList, trialContactTeacherVo: $trialContactTeacherVo, moreTrialScheduleVo: $moreTrialScheduleVo, trialSchedule: $trialSchedule, motivatePopupInfo: $motivatePopupInfo, learnRemindPopupInfo: $learnRemindPopupInfo, userPageTrialCourseInfo: $userPageTrialCourseInfo, schedulePopupList: $schedulePopupList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoTransformPageData &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            const DeepCollectionEquality()
                .equals(other._scheduleList, _scheduleList) &&
            (identical(other.trialContactTeacherVo, trialContactTeacherVo) ||
                other.trialContactTeacherVo == trialContactTeacherVo) &&
            (identical(other.moreTrialScheduleVo, moreTrialScheduleVo) ||
                other.moreTrialScheduleVo == moreTrialScheduleVo) &&
            (identical(other.trialSchedule, trialSchedule) ||
                other.trialSchedule == trialSchedule) &&
            (identical(other.motivatePopupInfo, motivatePopupInfo) ||
                other.motivatePopupInfo == motivatePopupInfo) &&
            (identical(other.learnRemindPopupInfo, learnRemindPopupInfo) ||
                other.learnRemindPopupInfo == learnRemindPopupInfo) &&
            (identical(
                    other.userPageTrialCourseInfo, userPageTrialCourseInfo) ||
                other.userPageTrialCourseInfo == userPageTrialCourseInfo) &&
            const DeepCollectionEquality()
                .equals(other._schedulePopupList, _schedulePopupList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      classId,
      courseKey,
      const DeepCollectionEquality().hash(_scheduleList),
      trialContactTeacherVo,
      moreTrialScheduleVo,
      trialSchedule,
      motivatePopupInfo,
      learnRemindPopupInfo,
      userPageTrialCourseInfo,
      const DeepCollectionEquality().hash(_schedulePopupList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoTransformPageDataCopyWith<_$_PlanAutoTransformPageData>
      get copyWith => __$$_PlanAutoTransformPageDataCopyWithImpl<
          _$_PlanAutoTransformPageData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoTransformPageDataToJson(
      this,
    );
  }
}

abstract class _PlanAutoTransformPageData implements PlanAutoTransformPageData {
  const factory _PlanAutoTransformPageData(
          {final int? classId,
          final String? courseKey,
          final List<ScheduleList>? scheduleList,
          final TrialContactTeacherVo? trialContactTeacherVo,
          final MoreTrialScheduleVo? moreTrialScheduleVo,
          final int? trialSchedule,
          final MotivatePopupInfo? motivatePopupInfo,
          final LearnRemindPopupInfo? learnRemindPopupInfo,
          final UserPageTrialCourseInfo? userPageTrialCourseInfo,
          final List<SchedulePopup>? schedulePopupList}) =
      _$_PlanAutoTransformPageData;

  factory _PlanAutoTransformPageData.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoTransformPageData.fromJson;

  @override

  ///训练营 纯享版 增加自转化卡片时 增加了 classId 和 courseKey 对自转化页面无影响，处理多科目显示问题
  int? get classId;
  @override
  String? get courseKey;
  @override
  List<ScheduleList>? get scheduleList;
  @override
  TrialContactTeacherVo? get trialContactTeacherVo;
  @override
  MoreTrialScheduleVo? get moreTrialScheduleVo;
  @override
  int? get trialSchedule;
  @override
  MotivatePopupInfo? get motivatePopupInfo;
  @override
  LearnRemindPopupInfo? get learnRemindPopupInfo;
  @override
  UserPageTrialCourseInfo? get userPageTrialCourseInfo;
  @override
  List<SchedulePopup>? get schedulePopupList;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoTransformPageDataCopyWith<_$_PlanAutoTransformPageData>
      get copyWith => throw _privateConstructorUsedError;
}

UserPageTrialCourseInfo _$UserPageTrialCourseInfoFromJson(
    Map<String, dynamic> json) {
  return _UserPageTrialCourseInfo.fromJson(json);
}

/// @nodoc
mixin _$UserPageTrialCourseInfo {
  int? get trialRemainingDays => throw _privateConstructorUsedError;
  set trialRemainingDays(int? value) => throw _privateConstructorUsedError;
  int? get finishDays => throw _privateConstructorUsedError;
  set finishDays(int? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  String? get trialCourseSegment => throw _privateConstructorUsedError;
  set trialCourseSegment(String? value) => throw _privateConstructorUsedError;
  String? get trialCourseKey => throw _privateConstructorUsedError;
  set trialCourseKey(String? value) => throw _privateConstructorUsedError;
  String? get trialUserCourseBusinessTag => throw _privateConstructorUsedError;
  set trialUserCourseBusinessTag(String? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserPageTrialCourseInfoCopyWith<UserPageTrialCourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPageTrialCourseInfoCopyWith<$Res> {
  factory $UserPageTrialCourseInfoCopyWith(UserPageTrialCourseInfo value,
          $Res Function(UserPageTrialCourseInfo) then) =
      _$UserPageTrialCourseInfoCopyWithImpl<$Res, UserPageTrialCourseInfo>;
  @useResult
  $Res call(
      {int? trialRemainingDays,
      int? finishDays,
      int? classId,
      String? trialCourseSegment,
      String? trialCourseKey,
      String? trialUserCourseBusinessTag});
}

/// @nodoc
class _$UserPageTrialCourseInfoCopyWithImpl<$Res,
        $Val extends UserPageTrialCourseInfo>
    implements $UserPageTrialCourseInfoCopyWith<$Res> {
  _$UserPageTrialCourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trialRemainingDays = freezed,
    Object? finishDays = freezed,
    Object? classId = freezed,
    Object? trialCourseSegment = freezed,
    Object? trialCourseKey = freezed,
    Object? trialUserCourseBusinessTag = freezed,
  }) {
    return _then(_value.copyWith(
      trialRemainingDays: freezed == trialRemainingDays
          ? _value.trialRemainingDays
          : trialRemainingDays // ignore: cast_nullable_to_non_nullable
              as int?,
      finishDays: freezed == finishDays
          ? _value.finishDays
          : finishDays // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      trialCourseSegment: freezed == trialCourseSegment
          ? _value.trialCourseSegment
          : trialCourseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      trialCourseKey: freezed == trialCourseKey
          ? _value.trialCourseKey
          : trialCourseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      trialUserCourseBusinessTag: freezed == trialUserCourseBusinessTag
          ? _value.trialUserCourseBusinessTag
          : trialUserCourseBusinessTag // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserPageTrialCourseInfoCopyWith<$Res>
    implements $UserPageTrialCourseInfoCopyWith<$Res> {
  factory _$$_UserPageTrialCourseInfoCopyWith(_$_UserPageTrialCourseInfo value,
          $Res Function(_$_UserPageTrialCourseInfo) then) =
      __$$_UserPageTrialCourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? trialRemainingDays,
      int? finishDays,
      int? classId,
      String? trialCourseSegment,
      String? trialCourseKey,
      String? trialUserCourseBusinessTag});
}

/// @nodoc
class __$$_UserPageTrialCourseInfoCopyWithImpl<$Res>
    extends _$UserPageTrialCourseInfoCopyWithImpl<$Res,
        _$_UserPageTrialCourseInfo>
    implements _$$_UserPageTrialCourseInfoCopyWith<$Res> {
  __$$_UserPageTrialCourseInfoCopyWithImpl(_$_UserPageTrialCourseInfo _value,
      $Res Function(_$_UserPageTrialCourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trialRemainingDays = freezed,
    Object? finishDays = freezed,
    Object? classId = freezed,
    Object? trialCourseSegment = freezed,
    Object? trialCourseKey = freezed,
    Object? trialUserCourseBusinessTag = freezed,
  }) {
    return _then(_$_UserPageTrialCourseInfo(
      trialRemainingDays: freezed == trialRemainingDays
          ? _value.trialRemainingDays
          : trialRemainingDays // ignore: cast_nullable_to_non_nullable
              as int?,
      finishDays: freezed == finishDays
          ? _value.finishDays
          : finishDays // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      trialCourseSegment: freezed == trialCourseSegment
          ? _value.trialCourseSegment
          : trialCourseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      trialCourseKey: freezed == trialCourseKey
          ? _value.trialCourseKey
          : trialCourseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      trialUserCourseBusinessTag: freezed == trialUserCourseBusinessTag
          ? _value.trialUserCourseBusinessTag
          : trialUserCourseBusinessTag // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserPageTrialCourseInfo implements _UserPageTrialCourseInfo {
  _$_UserPageTrialCourseInfo(
      {this.trialRemainingDays,
      this.finishDays,
      this.classId,
      this.trialCourseSegment,
      this.trialCourseKey,
      this.trialUserCourseBusinessTag});

  factory _$_UserPageTrialCourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_UserPageTrialCourseInfoFromJson(json);

  @override
  int? trialRemainingDays;
  @override
  int? finishDays;
  @override
  int? classId;
  @override
  String? trialCourseSegment;
  @override
  String? trialCourseKey;
  @override
  String? trialUserCourseBusinessTag;

  @override
  String toString() {
    return 'UserPageTrialCourseInfo(trialRemainingDays: $trialRemainingDays, finishDays: $finishDays, classId: $classId, trialCourseSegment: $trialCourseSegment, trialCourseKey: $trialCourseKey, trialUserCourseBusinessTag: $trialUserCourseBusinessTag)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserPageTrialCourseInfoCopyWith<_$_UserPageTrialCourseInfo>
      get copyWith =>
          __$$_UserPageTrialCourseInfoCopyWithImpl<_$_UserPageTrialCourseInfo>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserPageTrialCourseInfoToJson(
      this,
    );
  }
}

abstract class _UserPageTrialCourseInfo implements UserPageTrialCourseInfo {
  factory _UserPageTrialCourseInfo(
      {int? trialRemainingDays,
      int? finishDays,
      int? classId,
      String? trialCourseSegment,
      String? trialCourseKey,
      String? trialUserCourseBusinessTag}) = _$_UserPageTrialCourseInfo;

  factory _UserPageTrialCourseInfo.fromJson(Map<String, dynamic> json) =
      _$_UserPageTrialCourseInfo.fromJson;

  @override
  int? get trialRemainingDays;
  set trialRemainingDays(int? value);
  @override
  int? get finishDays;
  set finishDays(int? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  String? get trialCourseSegment;
  set trialCourseSegment(String? value);
  @override
  String? get trialCourseKey;
  set trialCourseKey(String? value);
  @override
  String? get trialUserCourseBusinessTag;
  set trialUserCourseBusinessTag(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_UserPageTrialCourseInfoCopyWith<_$_UserPageTrialCourseInfo>
      get copyWith => throw _privateConstructorUsedError;
}

MoreTrialScheduleVo _$MoreTrialScheduleVoFromJson(Map<String, dynamic> json) {
  return _MoreTrialScheduleVo.fromJson(json);
}

/// @nodoc
mixin _$MoreTrialScheduleVo {
  ActivateYearCourseTips? get activateYearCourseTips =>
      throw _privateConstructorUsedError;
  List<ScheduleTaskBo>? get scheduleTaskList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MoreTrialScheduleVoCopyWith<MoreTrialScheduleVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoreTrialScheduleVoCopyWith<$Res> {
  factory $MoreTrialScheduleVoCopyWith(
          MoreTrialScheduleVo value, $Res Function(MoreTrialScheduleVo) then) =
      _$MoreTrialScheduleVoCopyWithImpl<$Res, MoreTrialScheduleVo>;
  @useResult
  $Res call(
      {ActivateYearCourseTips? activateYearCourseTips,
      List<ScheduleTaskBo>? scheduleTaskList});

  $ActivateYearCourseTipsCopyWith<$Res>? get activateYearCourseTips;
}

/// @nodoc
class _$MoreTrialScheduleVoCopyWithImpl<$Res, $Val extends MoreTrialScheduleVo>
    implements $MoreTrialScheduleVoCopyWith<$Res> {
  _$MoreTrialScheduleVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activateYearCourseTips = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      activateYearCourseTips: freezed == activateYearCourseTips
          ? _value.activateYearCourseTips
          : activateYearCourseTips // ignore: cast_nullable_to_non_nullable
              as ActivateYearCourseTips?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value.scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ActivateYearCourseTipsCopyWith<$Res>? get activateYearCourseTips {
    if (_value.activateYearCourseTips == null) {
      return null;
    }

    return $ActivateYearCourseTipsCopyWith<$Res>(_value.activateYearCourseTips!,
        (value) {
      return _then(_value.copyWith(activateYearCourseTips: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MoreTrialScheduleVoCopyWith<$Res>
    implements $MoreTrialScheduleVoCopyWith<$Res> {
  factory _$$_MoreTrialScheduleVoCopyWith(_$_MoreTrialScheduleVo value,
          $Res Function(_$_MoreTrialScheduleVo) then) =
      __$$_MoreTrialScheduleVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ActivateYearCourseTips? activateYearCourseTips,
      List<ScheduleTaskBo>? scheduleTaskList});

  @override
  $ActivateYearCourseTipsCopyWith<$Res>? get activateYearCourseTips;
}

/// @nodoc
class __$$_MoreTrialScheduleVoCopyWithImpl<$Res>
    extends _$MoreTrialScheduleVoCopyWithImpl<$Res, _$_MoreTrialScheduleVo>
    implements _$$_MoreTrialScheduleVoCopyWith<$Res> {
  __$$_MoreTrialScheduleVoCopyWithImpl(_$_MoreTrialScheduleVo _value,
      $Res Function(_$_MoreTrialScheduleVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activateYearCourseTips = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_$_MoreTrialScheduleVo(
      activateYearCourseTips: freezed == activateYearCourseTips
          ? _value.activateYearCourseTips
          : activateYearCourseTips // ignore: cast_nullable_to_non_nullable
              as ActivateYearCourseTips?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value._scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MoreTrialScheduleVo implements _MoreTrialScheduleVo {
  const _$_MoreTrialScheduleVo(
      {this.activateYearCourseTips,
      final List<ScheduleTaskBo>? scheduleTaskList})
      : _scheduleTaskList = scheduleTaskList;

  factory _$_MoreTrialScheduleVo.fromJson(Map<String, dynamic> json) =>
      _$$_MoreTrialScheduleVoFromJson(json);

  @override
  final ActivateYearCourseTips? activateYearCourseTips;
  final List<ScheduleTaskBo>? _scheduleTaskList;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList {
    final value = _scheduleTaskList;
    if (value == null) return null;
    if (_scheduleTaskList is EqualUnmodifiableListView)
      return _scheduleTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MoreTrialScheduleVo(activateYearCourseTips: $activateYearCourseTips, scheduleTaskList: $scheduleTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MoreTrialScheduleVo &&
            (identical(other.activateYearCourseTips, activateYearCourseTips) ||
                other.activateYearCourseTips == activateYearCourseTips) &&
            const DeepCollectionEquality()
                .equals(other._scheduleTaskList, _scheduleTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, activateYearCourseTips,
      const DeepCollectionEquality().hash(_scheduleTaskList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MoreTrialScheduleVoCopyWith<_$_MoreTrialScheduleVo> get copyWith =>
      __$$_MoreTrialScheduleVoCopyWithImpl<_$_MoreTrialScheduleVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MoreTrialScheduleVoToJson(
      this,
    );
  }
}

abstract class _MoreTrialScheduleVo implements MoreTrialScheduleVo {
  const factory _MoreTrialScheduleVo(
      {final ActivateYearCourseTips? activateYearCourseTips,
      final List<ScheduleTaskBo>? scheduleTaskList}) = _$_MoreTrialScheduleVo;

  factory _MoreTrialScheduleVo.fromJson(Map<String, dynamic> json) =
      _$_MoreTrialScheduleVo.fromJson;

  @override
  ActivateYearCourseTips? get activateYearCourseTips;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_MoreTrialScheduleVoCopyWith<_$_MoreTrialScheduleVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ActivateYearCourseTips _$ActivateYearCourseTipsFromJson(
    Map<String, dynamic> json) {
  return _ActivateYearCourseTips.fromJson(json);
}

/// @nodoc
mixin _$ActivateYearCourseTips {
  String? get yearButtonRoute => throw _privateConstructorUsedError;
  String? get vipCornerMarkRoute => throw _privateConstructorUsedError;
  String? get monthButtonText => throw _privateConstructorUsedError;
  String? get yearButtonText => throw _privateConstructorUsedError;
  String? get periodTabDesc => throw _privateConstructorUsedError;
  String? get cardButtonText => throw _privateConstructorUsedError;
  String? get vipCornerMarkText => throw _privateConstructorUsedError;
  String? get cardImage => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActivateYearCourseTipsCopyWith<ActivateYearCourseTips> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActivateYearCourseTipsCopyWith<$Res> {
  factory $ActivateYearCourseTipsCopyWith(ActivateYearCourseTips value,
          $Res Function(ActivateYearCourseTips) then) =
      _$ActivateYearCourseTipsCopyWithImpl<$Res, ActivateYearCourseTips>;
  @useResult
  $Res call(
      {String? yearButtonRoute,
      String? vipCornerMarkRoute,
      String? monthButtonText,
      String? yearButtonText,
      String? periodTabDesc,
      String? cardButtonText,
      String? vipCornerMarkText,
      String? cardImage,
      String? route});
}

/// @nodoc
class _$ActivateYearCourseTipsCopyWithImpl<$Res,
        $Val extends ActivateYearCourseTips>
    implements $ActivateYearCourseTipsCopyWith<$Res> {
  _$ActivateYearCourseTipsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yearButtonRoute = freezed,
    Object? vipCornerMarkRoute = freezed,
    Object? monthButtonText = freezed,
    Object? yearButtonText = freezed,
    Object? periodTabDesc = freezed,
    Object? cardButtonText = freezed,
    Object? vipCornerMarkText = freezed,
    Object? cardImage = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      yearButtonRoute: freezed == yearButtonRoute
          ? _value.yearButtonRoute
          : yearButtonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      vipCornerMarkRoute: freezed == vipCornerMarkRoute
          ? _value.vipCornerMarkRoute
          : vipCornerMarkRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      monthButtonText: freezed == monthButtonText
          ? _value.monthButtonText
          : monthButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      yearButtonText: freezed == yearButtonText
          ? _value.yearButtonText
          : yearButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      periodTabDesc: freezed == periodTabDesc
          ? _value.periodTabDesc
          : periodTabDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      cardButtonText: freezed == cardButtonText
          ? _value.cardButtonText
          : cardButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      vipCornerMarkText: freezed == vipCornerMarkText
          ? _value.vipCornerMarkText
          : vipCornerMarkText // ignore: cast_nullable_to_non_nullable
              as String?,
      cardImage: freezed == cardImage
          ? _value.cardImage
          : cardImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ActivateYearCourseTipsCopyWith<$Res>
    implements $ActivateYearCourseTipsCopyWith<$Res> {
  factory _$$_ActivateYearCourseTipsCopyWith(_$_ActivateYearCourseTips value,
          $Res Function(_$_ActivateYearCourseTips) then) =
      __$$_ActivateYearCourseTipsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? yearButtonRoute,
      String? vipCornerMarkRoute,
      String? monthButtonText,
      String? yearButtonText,
      String? periodTabDesc,
      String? cardButtonText,
      String? vipCornerMarkText,
      String? cardImage,
      String? route});
}

/// @nodoc
class __$$_ActivateYearCourseTipsCopyWithImpl<$Res>
    extends _$ActivateYearCourseTipsCopyWithImpl<$Res,
        _$_ActivateYearCourseTips>
    implements _$$_ActivateYearCourseTipsCopyWith<$Res> {
  __$$_ActivateYearCourseTipsCopyWithImpl(_$_ActivateYearCourseTips _value,
      $Res Function(_$_ActivateYearCourseTips) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yearButtonRoute = freezed,
    Object? vipCornerMarkRoute = freezed,
    Object? monthButtonText = freezed,
    Object? yearButtonText = freezed,
    Object? periodTabDesc = freezed,
    Object? cardButtonText = freezed,
    Object? vipCornerMarkText = freezed,
    Object? cardImage = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_ActivateYearCourseTips(
      yearButtonRoute: freezed == yearButtonRoute
          ? _value.yearButtonRoute
          : yearButtonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      vipCornerMarkRoute: freezed == vipCornerMarkRoute
          ? _value.vipCornerMarkRoute
          : vipCornerMarkRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      monthButtonText: freezed == monthButtonText
          ? _value.monthButtonText
          : monthButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      yearButtonText: freezed == yearButtonText
          ? _value.yearButtonText
          : yearButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      periodTabDesc: freezed == periodTabDesc
          ? _value.periodTabDesc
          : periodTabDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      cardButtonText: freezed == cardButtonText
          ? _value.cardButtonText
          : cardButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      vipCornerMarkText: freezed == vipCornerMarkText
          ? _value.vipCornerMarkText
          : vipCornerMarkText // ignore: cast_nullable_to_non_nullable
              as String?,
      cardImage: freezed == cardImage
          ? _value.cardImage
          : cardImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ActivateYearCourseTips implements _ActivateYearCourseTips {
  const _$_ActivateYearCourseTips(
      {this.yearButtonRoute,
      this.vipCornerMarkRoute,
      this.monthButtonText,
      this.yearButtonText,
      this.periodTabDesc,
      this.cardButtonText,
      this.vipCornerMarkText,
      this.cardImage,
      this.route});

  factory _$_ActivateYearCourseTips.fromJson(Map<String, dynamic> json) =>
      _$$_ActivateYearCourseTipsFromJson(json);

  @override
  final String? yearButtonRoute;
  @override
  final String? vipCornerMarkRoute;
  @override
  final String? monthButtonText;
  @override
  final String? yearButtonText;
  @override
  final String? periodTabDesc;
  @override
  final String? cardButtonText;
  @override
  final String? vipCornerMarkText;
  @override
  final String? cardImage;
  @override
  final String? route;

  @override
  String toString() {
    return 'ActivateYearCourseTips(yearButtonRoute: $yearButtonRoute, vipCornerMarkRoute: $vipCornerMarkRoute, monthButtonText: $monthButtonText, yearButtonText: $yearButtonText, periodTabDesc: $periodTabDesc, cardButtonText: $cardButtonText, vipCornerMarkText: $vipCornerMarkText, cardImage: $cardImage, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ActivateYearCourseTips &&
            (identical(other.yearButtonRoute, yearButtonRoute) ||
                other.yearButtonRoute == yearButtonRoute) &&
            (identical(other.vipCornerMarkRoute, vipCornerMarkRoute) ||
                other.vipCornerMarkRoute == vipCornerMarkRoute) &&
            (identical(other.monthButtonText, monthButtonText) ||
                other.monthButtonText == monthButtonText) &&
            (identical(other.yearButtonText, yearButtonText) ||
                other.yearButtonText == yearButtonText) &&
            (identical(other.periodTabDesc, periodTabDesc) ||
                other.periodTabDesc == periodTabDesc) &&
            (identical(other.cardButtonText, cardButtonText) ||
                other.cardButtonText == cardButtonText) &&
            (identical(other.vipCornerMarkText, vipCornerMarkText) ||
                other.vipCornerMarkText == vipCornerMarkText) &&
            (identical(other.cardImage, cardImage) ||
                other.cardImage == cardImage) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      yearButtonRoute,
      vipCornerMarkRoute,
      monthButtonText,
      yearButtonText,
      periodTabDesc,
      cardButtonText,
      vipCornerMarkText,
      cardImage,
      route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ActivateYearCourseTipsCopyWith<_$_ActivateYearCourseTips> get copyWith =>
      __$$_ActivateYearCourseTipsCopyWithImpl<_$_ActivateYearCourseTips>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ActivateYearCourseTipsToJson(
      this,
    );
  }
}

abstract class _ActivateYearCourseTips implements ActivateYearCourseTips {
  const factory _ActivateYearCourseTips(
      {final String? yearButtonRoute,
      final String? vipCornerMarkRoute,
      final String? monthButtonText,
      final String? yearButtonText,
      final String? periodTabDesc,
      final String? cardButtonText,
      final String? vipCornerMarkText,
      final String? cardImage,
      final String? route}) = _$_ActivateYearCourseTips;

  factory _ActivateYearCourseTips.fromJson(Map<String, dynamic> json) =
      _$_ActivateYearCourseTips.fromJson;

  @override
  String? get yearButtonRoute;
  @override
  String? get vipCornerMarkRoute;
  @override
  String? get monthButtonText;
  @override
  String? get yearButtonText;
  @override
  String? get periodTabDesc;
  @override
  String? get cardButtonText;
  @override
  String? get vipCornerMarkText;
  @override
  String? get cardImage;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_ActivateYearCourseTipsCopyWith<_$_ActivateYearCourseTips> get copyWith =>
      throw _privateConstructorUsedError;
}

LearnRemindPopupInfo _$LearnRemindPopupInfoFromJson(Map<String, dynamic> json) {
  return _LearnRemindPopupInfo.fromJson(json);
}

/// @nodoc
mixin _$LearnRemindPopupInfo {
  String? get remindHeaderImage => throw _privateConstructorUsedError;
  String? get remindTitle => throw _privateConstructorUsedError;
  List<RemindSwitchVo>? get remindSwitchVoList =>
      throw _privateConstructorUsedError;
  String? get agreeText => throw _privateConstructorUsedError;
  String? get closedText => throw _privateConstructorUsedError;
  String? get remindAnimations => throw _privateConstructorUsedError;
  List<int>? get ruleIds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearnRemindPopupInfoCopyWith<LearnRemindPopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearnRemindPopupInfoCopyWith<$Res> {
  factory $LearnRemindPopupInfoCopyWith(LearnRemindPopupInfo value,
          $Res Function(LearnRemindPopupInfo) then) =
      _$LearnRemindPopupInfoCopyWithImpl<$Res, LearnRemindPopupInfo>;
  @useResult
  $Res call(
      {String? remindHeaderImage,
      String? remindTitle,
      List<RemindSwitchVo>? remindSwitchVoList,
      String? agreeText,
      String? closedText,
      String? remindAnimations,
      List<int>? ruleIds});
}

/// @nodoc
class _$LearnRemindPopupInfoCopyWithImpl<$Res,
        $Val extends LearnRemindPopupInfo>
    implements $LearnRemindPopupInfoCopyWith<$Res> {
  _$LearnRemindPopupInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remindHeaderImage = freezed,
    Object? remindTitle = freezed,
    Object? remindSwitchVoList = freezed,
    Object? agreeText = freezed,
    Object? closedText = freezed,
    Object? remindAnimations = freezed,
    Object? ruleIds = freezed,
  }) {
    return _then(_value.copyWith(
      remindHeaderImage: freezed == remindHeaderImage
          ? _value.remindHeaderImage
          : remindHeaderImage // ignore: cast_nullable_to_non_nullable
              as String?,
      remindTitle: freezed == remindTitle
          ? _value.remindTitle
          : remindTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      remindSwitchVoList: freezed == remindSwitchVoList
          ? _value.remindSwitchVoList
          : remindSwitchVoList // ignore: cast_nullable_to_non_nullable
              as List<RemindSwitchVo>?,
      agreeText: freezed == agreeText
          ? _value.agreeText
          : agreeText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
      remindAnimations: freezed == remindAnimations
          ? _value.remindAnimations
          : remindAnimations // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleIds: freezed == ruleIds
          ? _value.ruleIds
          : ruleIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearnRemindPopupInfoCopyWith<$Res>
    implements $LearnRemindPopupInfoCopyWith<$Res> {
  factory _$$_LearnRemindPopupInfoCopyWith(_$_LearnRemindPopupInfo value,
          $Res Function(_$_LearnRemindPopupInfo) then) =
      __$$_LearnRemindPopupInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? remindHeaderImage,
      String? remindTitle,
      List<RemindSwitchVo>? remindSwitchVoList,
      String? agreeText,
      String? closedText,
      String? remindAnimations,
      List<int>? ruleIds});
}

/// @nodoc
class __$$_LearnRemindPopupInfoCopyWithImpl<$Res>
    extends _$LearnRemindPopupInfoCopyWithImpl<$Res, _$_LearnRemindPopupInfo>
    implements _$$_LearnRemindPopupInfoCopyWith<$Res> {
  __$$_LearnRemindPopupInfoCopyWithImpl(_$_LearnRemindPopupInfo _value,
      $Res Function(_$_LearnRemindPopupInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remindHeaderImage = freezed,
    Object? remindTitle = freezed,
    Object? remindSwitchVoList = freezed,
    Object? agreeText = freezed,
    Object? closedText = freezed,
    Object? remindAnimations = freezed,
    Object? ruleIds = freezed,
  }) {
    return _then(_$_LearnRemindPopupInfo(
      remindHeaderImage: freezed == remindHeaderImage
          ? _value.remindHeaderImage
          : remindHeaderImage // ignore: cast_nullable_to_non_nullable
              as String?,
      remindTitle: freezed == remindTitle
          ? _value.remindTitle
          : remindTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      remindSwitchVoList: freezed == remindSwitchVoList
          ? _value._remindSwitchVoList
          : remindSwitchVoList // ignore: cast_nullable_to_non_nullable
              as List<RemindSwitchVo>?,
      agreeText: freezed == agreeText
          ? _value.agreeText
          : agreeText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
      remindAnimations: freezed == remindAnimations
          ? _value.remindAnimations
          : remindAnimations // ignore: cast_nullable_to_non_nullable
              as String?,
      ruleIds: freezed == ruleIds
          ? _value._ruleIds
          : ruleIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearnRemindPopupInfo implements _LearnRemindPopupInfo {
  const _$_LearnRemindPopupInfo(
      {this.remindHeaderImage,
      this.remindTitle,
      final List<RemindSwitchVo>? remindSwitchVoList,
      this.agreeText,
      this.closedText,
      this.remindAnimations,
      final List<int>? ruleIds})
      : _remindSwitchVoList = remindSwitchVoList,
        _ruleIds = ruleIds;

  factory _$_LearnRemindPopupInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LearnRemindPopupInfoFromJson(json);

  @override
  final String? remindHeaderImage;
  @override
  final String? remindTitle;
  final List<RemindSwitchVo>? _remindSwitchVoList;
  @override
  List<RemindSwitchVo>? get remindSwitchVoList {
    final value = _remindSwitchVoList;
    if (value == null) return null;
    if (_remindSwitchVoList is EqualUnmodifiableListView)
      return _remindSwitchVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? agreeText;
  @override
  final String? closedText;
  @override
  final String? remindAnimations;
  final List<int>? _ruleIds;
  @override
  List<int>? get ruleIds {
    final value = _ruleIds;
    if (value == null) return null;
    if (_ruleIds is EqualUnmodifiableListView) return _ruleIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LearnRemindPopupInfo(remindHeaderImage: $remindHeaderImage, remindTitle: $remindTitle, remindSwitchVoList: $remindSwitchVoList, agreeText: $agreeText, closedText: $closedText, remindAnimations: $remindAnimations, ruleIds: $ruleIds)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LearnRemindPopupInfo &&
            (identical(other.remindHeaderImage, remindHeaderImage) ||
                other.remindHeaderImage == remindHeaderImage) &&
            (identical(other.remindTitle, remindTitle) ||
                other.remindTitle == remindTitle) &&
            const DeepCollectionEquality()
                .equals(other._remindSwitchVoList, _remindSwitchVoList) &&
            (identical(other.agreeText, agreeText) ||
                other.agreeText == agreeText) &&
            (identical(other.closedText, closedText) ||
                other.closedText == closedText) &&
            (identical(other.remindAnimations, remindAnimations) ||
                other.remindAnimations == remindAnimations) &&
            const DeepCollectionEquality().equals(other._ruleIds, _ruleIds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      remindHeaderImage,
      remindTitle,
      const DeepCollectionEquality().hash(_remindSwitchVoList),
      agreeText,
      closedText,
      remindAnimations,
      const DeepCollectionEquality().hash(_ruleIds));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearnRemindPopupInfoCopyWith<_$_LearnRemindPopupInfo> get copyWith =>
      __$$_LearnRemindPopupInfoCopyWithImpl<_$_LearnRemindPopupInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearnRemindPopupInfoToJson(
      this,
    );
  }
}

abstract class _LearnRemindPopupInfo implements LearnRemindPopupInfo {
  const factory _LearnRemindPopupInfo(
      {final String? remindHeaderImage,
      final String? remindTitle,
      final List<RemindSwitchVo>? remindSwitchVoList,
      final String? agreeText,
      final String? closedText,
      final String? remindAnimations,
      final List<int>? ruleIds}) = _$_LearnRemindPopupInfo;

  factory _LearnRemindPopupInfo.fromJson(Map<String, dynamic> json) =
      _$_LearnRemindPopupInfo.fromJson;

  @override
  String? get remindHeaderImage;
  @override
  String? get remindTitle;
  @override
  List<RemindSwitchVo>? get remindSwitchVoList;
  @override
  String? get agreeText;
  @override
  String? get closedText;
  @override
  String? get remindAnimations;
  @override
  List<int>? get ruleIds;
  @override
  @JsonKey(ignore: true)
  _$$_LearnRemindPopupInfoCopyWith<_$_LearnRemindPopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

RemindSwitchVo _$RemindSwitchVoFromJson(Map<String, dynamic> json) {
  return _RemindSwitchVo.fromJson(json);
}

/// @nodoc
mixin _$RemindSwitchVo {
  String? get switchName => throw _privateConstructorUsedError;
  int? get switchType => throw _privateConstructorUsedError;
  bool? get switchStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RemindSwitchVoCopyWith<RemindSwitchVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RemindSwitchVoCopyWith<$Res> {
  factory $RemindSwitchVoCopyWith(
          RemindSwitchVo value, $Res Function(RemindSwitchVo) then) =
      _$RemindSwitchVoCopyWithImpl<$Res, RemindSwitchVo>;
  @useResult
  $Res call({String? switchName, int? switchType, bool? switchStatus});
}

/// @nodoc
class _$RemindSwitchVoCopyWithImpl<$Res, $Val extends RemindSwitchVo>
    implements $RemindSwitchVoCopyWith<$Res> {
  _$RemindSwitchVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? switchName = freezed,
    Object? switchType = freezed,
    Object? switchStatus = freezed,
  }) {
    return _then(_value.copyWith(
      switchName: freezed == switchName
          ? _value.switchName
          : switchName // ignore: cast_nullable_to_non_nullable
              as String?,
      switchType: freezed == switchType
          ? _value.switchType
          : switchType // ignore: cast_nullable_to_non_nullable
              as int?,
      switchStatus: freezed == switchStatus
          ? _value.switchStatus
          : switchStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RemindSwitchVoCopyWith<$Res>
    implements $RemindSwitchVoCopyWith<$Res> {
  factory _$$_RemindSwitchVoCopyWith(
          _$_RemindSwitchVo value, $Res Function(_$_RemindSwitchVo) then) =
      __$$_RemindSwitchVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? switchName, int? switchType, bool? switchStatus});
}

/// @nodoc
class __$$_RemindSwitchVoCopyWithImpl<$Res>
    extends _$RemindSwitchVoCopyWithImpl<$Res, _$_RemindSwitchVo>
    implements _$$_RemindSwitchVoCopyWith<$Res> {
  __$$_RemindSwitchVoCopyWithImpl(
      _$_RemindSwitchVo _value, $Res Function(_$_RemindSwitchVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? switchName = freezed,
    Object? switchType = freezed,
    Object? switchStatus = freezed,
  }) {
    return _then(_$_RemindSwitchVo(
      switchName: freezed == switchName
          ? _value.switchName
          : switchName // ignore: cast_nullable_to_non_nullable
              as String?,
      switchType: freezed == switchType
          ? _value.switchType
          : switchType // ignore: cast_nullable_to_non_nullable
              as int?,
      switchStatus: freezed == switchStatus
          ? _value.switchStatus
          : switchStatus // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RemindSwitchVo implements _RemindSwitchVo {
  const _$_RemindSwitchVo(
      {this.switchName, this.switchType, this.switchStatus});

  factory _$_RemindSwitchVo.fromJson(Map<String, dynamic> json) =>
      _$$_RemindSwitchVoFromJson(json);

  @override
  final String? switchName;
  @override
  final int? switchType;
  @override
  final bool? switchStatus;

  @override
  String toString() {
    return 'RemindSwitchVo(switchName: $switchName, switchType: $switchType, switchStatus: $switchStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RemindSwitchVo &&
            (identical(other.switchName, switchName) ||
                other.switchName == switchName) &&
            (identical(other.switchType, switchType) ||
                other.switchType == switchType) &&
            (identical(other.switchStatus, switchStatus) ||
                other.switchStatus == switchStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, switchName, switchType, switchStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RemindSwitchVoCopyWith<_$_RemindSwitchVo> get copyWith =>
      __$$_RemindSwitchVoCopyWithImpl<_$_RemindSwitchVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RemindSwitchVoToJson(
      this,
    );
  }
}

abstract class _RemindSwitchVo implements RemindSwitchVo {
  const factory _RemindSwitchVo(
      {final String? switchName,
      final int? switchType,
      final bool? switchStatus}) = _$_RemindSwitchVo;

  factory _RemindSwitchVo.fromJson(Map<String, dynamic> json) =
      _$_RemindSwitchVo.fromJson;

  @override
  String? get switchName;
  @override
  int? get switchType;
  @override
  bool? get switchStatus;
  @override
  @JsonKey(ignore: true)
  _$$_RemindSwitchVoCopyWith<_$_RemindSwitchVo> get copyWith =>
      throw _privateConstructorUsedError;
}

MotivatePopupInfo _$MotivatePopupInfoFromJson(Map<String, dynamic> json) {
  return _MotivatePopupInfo.fromJson(json);
}

/// @nodoc
mixin _$MotivatePopupInfo {
  String? get motivateHeaderImage => throw _privateConstructorUsedError;
  String? get motivateDesc => throw _privateConstructorUsedError;
  int? get finishDays => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  List<LessonMotivatePopup>? get lessonMotivatePopups =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MotivatePopupInfoCopyWith<MotivatePopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MotivatePopupInfoCopyWith<$Res> {
  factory $MotivatePopupInfoCopyWith(
          MotivatePopupInfo value, $Res Function(MotivatePopupInfo) then) =
      _$MotivatePopupInfoCopyWithImpl<$Res, MotivatePopupInfo>;
  @useResult
  $Res call(
      {String? motivateHeaderImage,
      String? motivateDesc,
      int? finishDays,
      String? buttonText,
      List<LessonMotivatePopup>? lessonMotivatePopups});
}

/// @nodoc
class _$MotivatePopupInfoCopyWithImpl<$Res, $Val extends MotivatePopupInfo>
    implements $MotivatePopupInfoCopyWith<$Res> {
  _$MotivatePopupInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motivateHeaderImage = freezed,
    Object? motivateDesc = freezed,
    Object? finishDays = freezed,
    Object? buttonText = freezed,
    Object? lessonMotivatePopups = freezed,
  }) {
    return _then(_value.copyWith(
      motivateHeaderImage: freezed == motivateHeaderImage
          ? _value.motivateHeaderImage
          : motivateHeaderImage // ignore: cast_nullable_to_non_nullable
              as String?,
      motivateDesc: freezed == motivateDesc
          ? _value.motivateDesc
          : motivateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      finishDays: freezed == finishDays
          ? _value.finishDays
          : finishDays // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonMotivatePopups: freezed == lessonMotivatePopups
          ? _value.lessonMotivatePopups
          : lessonMotivatePopups // ignore: cast_nullable_to_non_nullable
              as List<LessonMotivatePopup>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MotivatePopupInfoCopyWith<$Res>
    implements $MotivatePopupInfoCopyWith<$Res> {
  factory _$$_MotivatePopupInfoCopyWith(_$_MotivatePopupInfo value,
          $Res Function(_$_MotivatePopupInfo) then) =
      __$$_MotivatePopupInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? motivateHeaderImage,
      String? motivateDesc,
      int? finishDays,
      String? buttonText,
      List<LessonMotivatePopup>? lessonMotivatePopups});
}

/// @nodoc
class __$$_MotivatePopupInfoCopyWithImpl<$Res>
    extends _$MotivatePopupInfoCopyWithImpl<$Res, _$_MotivatePopupInfo>
    implements _$$_MotivatePopupInfoCopyWith<$Res> {
  __$$_MotivatePopupInfoCopyWithImpl(
      _$_MotivatePopupInfo _value, $Res Function(_$_MotivatePopupInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? motivateHeaderImage = freezed,
    Object? motivateDesc = freezed,
    Object? finishDays = freezed,
    Object? buttonText = freezed,
    Object? lessonMotivatePopups = freezed,
  }) {
    return _then(_$_MotivatePopupInfo(
      motivateHeaderImage: freezed == motivateHeaderImage
          ? _value.motivateHeaderImage
          : motivateHeaderImage // ignore: cast_nullable_to_non_nullable
              as String?,
      motivateDesc: freezed == motivateDesc
          ? _value.motivateDesc
          : motivateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      finishDays: freezed == finishDays
          ? _value.finishDays
          : finishDays // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonMotivatePopups: freezed == lessonMotivatePopups
          ? _value._lessonMotivatePopups
          : lessonMotivatePopups // ignore: cast_nullable_to_non_nullable
              as List<LessonMotivatePopup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MotivatePopupInfo implements _MotivatePopupInfo {
  const _$_MotivatePopupInfo(
      {this.motivateHeaderImage,
      this.motivateDesc,
      this.finishDays,
      this.buttonText,
      final List<LessonMotivatePopup>? lessonMotivatePopups})
      : _lessonMotivatePopups = lessonMotivatePopups;

  factory _$_MotivatePopupInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MotivatePopupInfoFromJson(json);

  @override
  final String? motivateHeaderImage;
  @override
  final String? motivateDesc;
  @override
  final int? finishDays;
  @override
  final String? buttonText;
  final List<LessonMotivatePopup>? _lessonMotivatePopups;
  @override
  List<LessonMotivatePopup>? get lessonMotivatePopups {
    final value = _lessonMotivatePopups;
    if (value == null) return null;
    if (_lessonMotivatePopups is EqualUnmodifiableListView)
      return _lessonMotivatePopups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MotivatePopupInfo(motivateHeaderImage: $motivateHeaderImage, motivateDesc: $motivateDesc, finishDays: $finishDays, buttonText: $buttonText, lessonMotivatePopups: $lessonMotivatePopups)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MotivatePopupInfo &&
            (identical(other.motivateHeaderImage, motivateHeaderImage) ||
                other.motivateHeaderImage == motivateHeaderImage) &&
            (identical(other.motivateDesc, motivateDesc) ||
                other.motivateDesc == motivateDesc) &&
            (identical(other.finishDays, finishDays) ||
                other.finishDays == finishDays) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            const DeepCollectionEquality()
                .equals(other._lessonMotivatePopups, _lessonMotivatePopups));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      motivateHeaderImage,
      motivateDesc,
      finishDays,
      buttonText,
      const DeepCollectionEquality().hash(_lessonMotivatePopups));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MotivatePopupInfoCopyWith<_$_MotivatePopupInfo> get copyWith =>
      __$$_MotivatePopupInfoCopyWithImpl<_$_MotivatePopupInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MotivatePopupInfoToJson(
      this,
    );
  }
}

abstract class _MotivatePopupInfo implements MotivatePopupInfo {
  const factory _MotivatePopupInfo(
          {final String? motivateHeaderImage,
          final String? motivateDesc,
          final int? finishDays,
          final String? buttonText,
          final List<LessonMotivatePopup>? lessonMotivatePopups}) =
      _$_MotivatePopupInfo;

  factory _MotivatePopupInfo.fromJson(Map<String, dynamic> json) =
      _$_MotivatePopupInfo.fromJson;

  @override
  String? get motivateHeaderImage;
  @override
  String? get motivateDesc;
  @override
  int? get finishDays;
  @override
  String? get buttonText;
  @override
  List<LessonMotivatePopup>? get lessonMotivatePopups;
  @override
  @JsonKey(ignore: true)
  _$$_MotivatePopupInfoCopyWith<_$_MotivatePopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonMotivatePopup _$LessonMotivatePopupFromJson(Map<String, dynamic> json) {
  return _LessonMotivatePopup.fromJson(json);
}

/// @nodoc
mixin _$LessonMotivatePopup {
  int? get lessonCount => throw _privateConstructorUsedError;
  String? get motivateDesc => throw _privateConstructorUsedError;
  String? get animations => throw _privateConstructorUsedError;
  String? get animationsForPad => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonMotivatePopupCopyWith<LessonMotivatePopup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonMotivatePopupCopyWith<$Res> {
  factory $LessonMotivatePopupCopyWith(
          LessonMotivatePopup value, $Res Function(LessonMotivatePopup) then) =
      _$LessonMotivatePopupCopyWithImpl<$Res, LessonMotivatePopup>;
  @useResult
  $Res call(
      {int? lessonCount,
      String? motivateDesc,
      String? animations,
      String? animationsForPad,
      String? buttonText});
}

/// @nodoc
class _$LessonMotivatePopupCopyWithImpl<$Res, $Val extends LessonMotivatePopup>
    implements $LessonMotivatePopupCopyWith<$Res> {
  _$LessonMotivatePopupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonCount = freezed,
    Object? motivateDesc = freezed,
    Object? animations = freezed,
    Object? animationsForPad = freezed,
    Object? buttonText = freezed,
  }) {
    return _then(_value.copyWith(
      lessonCount: freezed == lessonCount
          ? _value.lessonCount
          : lessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      motivateDesc: freezed == motivateDesc
          ? _value.motivateDesc
          : motivateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      animations: freezed == animations
          ? _value.animations
          : animations // ignore: cast_nullable_to_non_nullable
              as String?,
      animationsForPad: freezed == animationsForPad
          ? _value.animationsForPad
          : animationsForPad // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonMotivatePopupCopyWith<$Res>
    implements $LessonMotivatePopupCopyWith<$Res> {
  factory _$$_LessonMotivatePopupCopyWith(_$_LessonMotivatePopup value,
          $Res Function(_$_LessonMotivatePopup) then) =
      __$$_LessonMotivatePopupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? lessonCount,
      String? motivateDesc,
      String? animations,
      String? animationsForPad,
      String? buttonText});
}

/// @nodoc
class __$$_LessonMotivatePopupCopyWithImpl<$Res>
    extends _$LessonMotivatePopupCopyWithImpl<$Res, _$_LessonMotivatePopup>
    implements _$$_LessonMotivatePopupCopyWith<$Res> {
  __$$_LessonMotivatePopupCopyWithImpl(_$_LessonMotivatePopup _value,
      $Res Function(_$_LessonMotivatePopup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonCount = freezed,
    Object? motivateDesc = freezed,
    Object? animations = freezed,
    Object? animationsForPad = freezed,
    Object? buttonText = freezed,
  }) {
    return _then(_$_LessonMotivatePopup(
      lessonCount: freezed == lessonCount
          ? _value.lessonCount
          : lessonCount // ignore: cast_nullable_to_non_nullable
              as int?,
      motivateDesc: freezed == motivateDesc
          ? _value.motivateDesc
          : motivateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      animations: freezed == animations
          ? _value.animations
          : animations // ignore: cast_nullable_to_non_nullable
              as String?,
      animationsForPad: freezed == animationsForPad
          ? _value.animationsForPad
          : animationsForPad // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonMotivatePopup implements _LessonMotivatePopup {
  const _$_LessonMotivatePopup(
      {this.lessonCount,
      this.motivateDesc,
      this.animations,
      this.animationsForPad,
      this.buttonText});

  factory _$_LessonMotivatePopup.fromJson(Map<String, dynamic> json) =>
      _$$_LessonMotivatePopupFromJson(json);

  @override
  final int? lessonCount;
  @override
  final String? motivateDesc;
  @override
  final String? animations;
  @override
  final String? animationsForPad;
  @override
  final String? buttonText;

  @override
  String toString() {
    return 'LessonMotivatePopup(lessonCount: $lessonCount, motivateDesc: $motivateDesc, animations: $animations, animationsForPad: $animationsForPad, buttonText: $buttonText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonMotivatePopup &&
            (identical(other.lessonCount, lessonCount) ||
                other.lessonCount == lessonCount) &&
            (identical(other.motivateDesc, motivateDesc) ||
                other.motivateDesc == motivateDesc) &&
            (identical(other.animations, animations) ||
                other.animations == animations) &&
            (identical(other.animationsForPad, animationsForPad) ||
                other.animationsForPad == animationsForPad) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lessonCount, motivateDesc,
      animations, animationsForPad, buttonText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonMotivatePopupCopyWith<_$_LessonMotivatePopup> get copyWith =>
      __$$_LessonMotivatePopupCopyWithImpl<_$_LessonMotivatePopup>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonMotivatePopupToJson(
      this,
    );
  }
}

abstract class _LessonMotivatePopup implements LessonMotivatePopup {
  const factory _LessonMotivatePopup(
      {final int? lessonCount,
      final String? motivateDesc,
      final String? animations,
      final String? animationsForPad,
      final String? buttonText}) = _$_LessonMotivatePopup;

  factory _LessonMotivatePopup.fromJson(Map<String, dynamic> json) =
      _$_LessonMotivatePopup.fromJson;

  @override
  int? get lessonCount;
  @override
  String? get motivateDesc;
  @override
  String? get animations;
  @override
  String? get animationsForPad;
  @override
  String? get buttonText;
  @override
  @JsonKey(ignore: true)
  _$$_LessonMotivatePopupCopyWith<_$_LessonMotivatePopup> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleList _$ScheduleListFromJson(Map<String, dynamic> json) {
  return _ScheduleList.fromJson(json);
}

/// @nodoc
mixin _$ScheduleList {
  int? get showDateTime => throw _privateConstructorUsedError;
  String? get showDate => throw _privateConstructorUsedError;
  String? get dateDesc => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  int? get scheduleStatus => throw _privateConstructorUsedError;
  List<ScheduleTaskBo>? get scheduleTaskList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleListCopyWith<ScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleListCopyWith<$Res> {
  factory $ScheduleListCopyWith(
          ScheduleList value, $Res Function(ScheduleList) then) =
      _$ScheduleListCopyWithImpl<$Res, ScheduleList>;
  @useResult
  $Res call(
      {int? showDateTime,
      String? showDate,
      String? dateDesc,
      bool? today,
      int? scheduleStatus,
      List<ScheduleTaskBo>? scheduleTaskList});
}

/// @nodoc
class _$ScheduleListCopyWithImpl<$Res, $Val extends ScheduleList>
    implements $ScheduleListCopyWith<$Res> {
  _$ScheduleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? dateDesc = freezed,
    Object? today = freezed,
    Object? scheduleStatus = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      dateDesc: freezed == dateDesc
          ? _value.dateDesc
          : dateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value.scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleListCopyWith<$Res>
    implements $ScheduleListCopyWith<$Res> {
  factory _$$_ScheduleListCopyWith(
          _$_ScheduleList value, $Res Function(_$_ScheduleList) then) =
      __$$_ScheduleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? showDateTime,
      String? showDate,
      String? dateDesc,
      bool? today,
      int? scheduleStatus,
      List<ScheduleTaskBo>? scheduleTaskList});
}

/// @nodoc
class __$$_ScheduleListCopyWithImpl<$Res>
    extends _$ScheduleListCopyWithImpl<$Res, _$_ScheduleList>
    implements _$$_ScheduleListCopyWith<$Res> {
  __$$_ScheduleListCopyWithImpl(
      _$_ScheduleList _value, $Res Function(_$_ScheduleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? dateDesc = freezed,
    Object? today = freezed,
    Object? scheduleStatus = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_$_ScheduleList(
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      dateDesc: freezed == dateDesc
          ? _value.dateDesc
          : dateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value._scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleList implements _ScheduleList {
  const _$_ScheduleList(
      {this.showDateTime,
      this.showDate,
      this.dateDesc,
      this.today,
      this.scheduleStatus,
      final List<ScheduleTaskBo>? scheduleTaskList})
      : _scheduleTaskList = scheduleTaskList;

  factory _$_ScheduleList.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleListFromJson(json);

  @override
  final int? showDateTime;
  @override
  final String? showDate;
  @override
  final String? dateDesc;
  @override
  final bool? today;
  @override
  final int? scheduleStatus;
  final List<ScheduleTaskBo>? _scheduleTaskList;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList {
    final value = _scheduleTaskList;
    if (value == null) return null;
    if (_scheduleTaskList is EqualUnmodifiableListView)
      return _scheduleTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ScheduleList(showDateTime: $showDateTime, showDate: $showDate, dateDesc: $dateDesc, today: $today, scheduleStatus: $scheduleStatus, scheduleTaskList: $scheduleTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleList &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.showDate, showDate) ||
                other.showDate == showDate) &&
            (identical(other.dateDesc, dateDesc) ||
                other.dateDesc == dateDesc) &&
            (identical(other.today, today) || other.today == today) &&
            (identical(other.scheduleStatus, scheduleStatus) ||
                other.scheduleStatus == scheduleStatus) &&
            const DeepCollectionEquality()
                .equals(other._scheduleTaskList, _scheduleTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showDateTime,
      showDate,
      dateDesc,
      today,
      scheduleStatus,
      const DeepCollectionEquality().hash(_scheduleTaskList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleListCopyWith<_$_ScheduleList> get copyWith =>
      __$$_ScheduleListCopyWithImpl<_$_ScheduleList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleListToJson(
      this,
    );
  }
}

abstract class _ScheduleList implements ScheduleList {
  const factory _ScheduleList(
      {final int? showDateTime,
      final String? showDate,
      final String? dateDesc,
      final bool? today,
      final int? scheduleStatus,
      final List<ScheduleTaskBo>? scheduleTaskList}) = _$_ScheduleList;

  factory _ScheduleList.fromJson(Map<String, dynamic> json) =
      _$_ScheduleList.fromJson;

  @override
  int? get showDateTime;
  @override
  String? get showDate;
  @override
  String? get dateDesc;
  @override
  bool? get today;
  @override
  int? get scheduleStatus;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleListCopyWith<_$_ScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

TrialContactTeacherVo _$TrialContactTeacherVoFromJson(
    Map<String, dynamic> json) {
  return _TrialContactTeacherVo.fromJson(json);
}

/// @nodoc
mixin _$TrialContactTeacherVo {
  int? get teacherId => throw _privateConstructorUsedError;
  String? get teacherAvatarImg => throw _privateConstructorUsedError;
  String? get contactText => throw _privateConstructorUsedError;
  String? get contactRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TrialContactTeacherVoCopyWith<TrialContactTeacherVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrialContactTeacherVoCopyWith<$Res> {
  factory $TrialContactTeacherVoCopyWith(TrialContactTeacherVo value,
          $Res Function(TrialContactTeacherVo) then) =
      _$TrialContactTeacherVoCopyWithImpl<$Res, TrialContactTeacherVo>;
  @useResult
  $Res call(
      {int? teacherId,
      String? teacherAvatarImg,
      String? contactText,
      String? contactRoute});
}

/// @nodoc
class _$TrialContactTeacherVoCopyWithImpl<$Res,
        $Val extends TrialContactTeacherVo>
    implements $TrialContactTeacherVoCopyWith<$Res> {
  _$TrialContactTeacherVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherAvatarImg = freezed,
    Object? contactText = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_value.copyWith(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherAvatarImg: freezed == teacherAvatarImg
          ? _value.teacherAvatarImg
          : teacherAvatarImg // ignore: cast_nullable_to_non_nullable
              as String?,
      contactText: freezed == contactText
          ? _value.contactText
          : contactText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TrialContactTeacherVoCopyWith<$Res>
    implements $TrialContactTeacherVoCopyWith<$Res> {
  factory _$$_TrialContactTeacherVoCopyWith(_$_TrialContactTeacherVo value,
          $Res Function(_$_TrialContactTeacherVo) then) =
      __$$_TrialContactTeacherVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teacherId,
      String? teacherAvatarImg,
      String? contactText,
      String? contactRoute});
}

/// @nodoc
class __$$_TrialContactTeacherVoCopyWithImpl<$Res>
    extends _$TrialContactTeacherVoCopyWithImpl<$Res, _$_TrialContactTeacherVo>
    implements _$$_TrialContactTeacherVoCopyWith<$Res> {
  __$$_TrialContactTeacherVoCopyWithImpl(_$_TrialContactTeacherVo _value,
      $Res Function(_$_TrialContactTeacherVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherAvatarImg = freezed,
    Object? contactText = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_$_TrialContactTeacherVo(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherAvatarImg: freezed == teacherAvatarImg
          ? _value.teacherAvatarImg
          : teacherAvatarImg // ignore: cast_nullable_to_non_nullable
              as String?,
      contactText: freezed == contactText
          ? _value.contactText
          : contactText // ignore: cast_nullable_to_non_nullable
              as String?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TrialContactTeacherVo implements _TrialContactTeacherVo {
  const _$_TrialContactTeacherVo(
      {this.teacherId,
      this.teacherAvatarImg,
      this.contactText,
      this.contactRoute});

  factory _$_TrialContactTeacherVo.fromJson(Map<String, dynamic> json) =>
      _$$_TrialContactTeacherVoFromJson(json);

  @override
  final int? teacherId;
  @override
  final String? teacherAvatarImg;
  @override
  final String? contactText;
  @override
  final String? contactRoute;

  @override
  String toString() {
    return 'TrialContactTeacherVo(teacherId: $teacherId, teacherAvatarImg: $teacherAvatarImg, contactText: $contactText, contactRoute: $contactRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TrialContactTeacherVo &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherAvatarImg, teacherAvatarImg) ||
                other.teacherAvatarImg == teacherAvatarImg) &&
            (identical(other.contactText, contactText) ||
                other.contactText == contactText) &&
            (identical(other.contactRoute, contactRoute) ||
                other.contactRoute == contactRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, teacherId, teacherAvatarImg, contactText, contactRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TrialContactTeacherVoCopyWith<_$_TrialContactTeacherVo> get copyWith =>
      __$$_TrialContactTeacherVoCopyWithImpl<_$_TrialContactTeacherVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TrialContactTeacherVoToJson(
      this,
    );
  }
}

abstract class _TrialContactTeacherVo implements TrialContactTeacherVo {
  const factory _TrialContactTeacherVo(
      {final int? teacherId,
      final String? teacherAvatarImg,
      final String? contactText,
      final String? contactRoute}) = _$_TrialContactTeacherVo;

  factory _TrialContactTeacherVo.fromJson(Map<String, dynamic> json) =
      _$_TrialContactTeacherVo.fromJson;

  @override
  int? get teacherId;
  @override
  String? get teacherAvatarImg;
  @override
  String? get contactText;
  @override
  String? get contactRoute;
  @override
  @JsonKey(ignore: true)
  _$$_TrialContactTeacherVoCopyWith<_$_TrialContactTeacherVo> get copyWith =>
      throw _privateConstructorUsedError;
}
