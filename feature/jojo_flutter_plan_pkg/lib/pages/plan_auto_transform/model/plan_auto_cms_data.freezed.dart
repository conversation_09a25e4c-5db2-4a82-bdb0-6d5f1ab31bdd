// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_auto_cms_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanAutoCmsData _$PlanAutoCmsDataFromJson(Map<String, dynamic> json) {
  return _PlanAutoCmsData.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoCmsData {
  int? get popupStyle => throw _privateConstructorUsedError;
  String? get mainButtonText => throw _privateConstructorUsedError;
  String? get exitButtonText => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  int? get pictureStyle => throw _privateConstructorUsedError;
  int? get popupId => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get popupName => throw _privateConstructorUsedError;
  int? get materialFormat =>
      throw _privateConstructorUsedError; // 1：图片，2:SPINE动画
  String? get spineUrl => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;
  String? get downloadOriginUrl =>
      throw _privateConstructorUsedError; // 下载后原图地址
  int? get materialId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoCmsDataCopyWith<PlanAutoCmsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoCmsDataCopyWith<$Res> {
  factory $PlanAutoCmsDataCopyWith(
          PlanAutoCmsData value, $Res Function(PlanAutoCmsData) then) =
      _$PlanAutoCmsDataCopyWithImpl<$Res, PlanAutoCmsData>;
  @useResult
  $Res call(
      {int? popupStyle,
      String? mainButtonText,
      String? exitButtonText,
      String? title,
      String? subTitle,
      int? pictureStyle,
      int? popupId,
      String? pictureUrl,
      String? linkUrl,
      String? popupName,
      int? materialFormat,
      String? spineUrl,
      String? businessTagId,
      String? downloadOriginUrl,
      int? materialId});
}

/// @nodoc
class _$PlanAutoCmsDataCopyWithImpl<$Res, $Val extends PlanAutoCmsData>
    implements $PlanAutoCmsDataCopyWith<$Res> {
  _$PlanAutoCmsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupStyle = freezed,
    Object? mainButtonText = freezed,
    Object? exitButtonText = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? pictureStyle = freezed,
    Object? popupId = freezed,
    Object? pictureUrl = freezed,
    Object? linkUrl = freezed,
    Object? popupName = freezed,
    Object? materialFormat = freezed,
    Object? spineUrl = freezed,
    Object? businessTagId = freezed,
    Object? downloadOriginUrl = freezed,
    Object? materialId = freezed,
  }) {
    return _then(_value.copyWith(
      popupStyle: freezed == popupStyle
          ? _value.popupStyle
          : popupStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      mainButtonText: freezed == mainButtonText
          ? _value.mainButtonText
          : mainButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      exitButtonText: freezed == exitButtonText
          ? _value.exitButtonText
          : exitButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureStyle: freezed == pictureStyle
          ? _value.pictureStyle
          : pictureStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      popupId: freezed == popupId
          ? _value.popupId
          : popupId // ignore: cast_nullable_to_non_nullable
              as int?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      popupName: freezed == popupName
          ? _value.popupName
          : popupName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialFormat: freezed == materialFormat
          ? _value.materialFormat
          : materialFormat // ignore: cast_nullable_to_non_nullable
              as int?,
      spineUrl: freezed == spineUrl
          ? _value.spineUrl
          : spineUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadOriginUrl: freezed == downloadOriginUrl
          ? _value.downloadOriginUrl
          : downloadOriginUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanAutoCmsDataCopyWith<$Res>
    implements $PlanAutoCmsDataCopyWith<$Res> {
  factory _$$_PlanAutoCmsDataCopyWith(
          _$_PlanAutoCmsData value, $Res Function(_$_PlanAutoCmsData) then) =
      __$$_PlanAutoCmsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? popupStyle,
      String? mainButtonText,
      String? exitButtonText,
      String? title,
      String? subTitle,
      int? pictureStyle,
      int? popupId,
      String? pictureUrl,
      String? linkUrl,
      String? popupName,
      int? materialFormat,
      String? spineUrl,
      String? businessTagId,
      String? downloadOriginUrl,
      int? materialId});
}

/// @nodoc
class __$$_PlanAutoCmsDataCopyWithImpl<$Res>
    extends _$PlanAutoCmsDataCopyWithImpl<$Res, _$_PlanAutoCmsData>
    implements _$$_PlanAutoCmsDataCopyWith<$Res> {
  __$$_PlanAutoCmsDataCopyWithImpl(
      _$_PlanAutoCmsData _value, $Res Function(_$_PlanAutoCmsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupStyle = freezed,
    Object? mainButtonText = freezed,
    Object? exitButtonText = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? pictureStyle = freezed,
    Object? popupId = freezed,
    Object? pictureUrl = freezed,
    Object? linkUrl = freezed,
    Object? popupName = freezed,
    Object? materialFormat = freezed,
    Object? spineUrl = freezed,
    Object? businessTagId = freezed,
    Object? downloadOriginUrl = freezed,
    Object? materialId = freezed,
  }) {
    return _then(_$_PlanAutoCmsData(
      popupStyle: freezed == popupStyle
          ? _value.popupStyle
          : popupStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      mainButtonText: freezed == mainButtonText
          ? _value.mainButtonText
          : mainButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      exitButtonText: freezed == exitButtonText
          ? _value.exitButtonText
          : exitButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureStyle: freezed == pictureStyle
          ? _value.pictureStyle
          : pictureStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      popupId: freezed == popupId
          ? _value.popupId
          : popupId // ignore: cast_nullable_to_non_nullable
              as int?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      popupName: freezed == popupName
          ? _value.popupName
          : popupName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialFormat: freezed == materialFormat
          ? _value.materialFormat
          : materialFormat // ignore: cast_nullable_to_non_nullable
              as int?,
      spineUrl: freezed == spineUrl
          ? _value.spineUrl
          : spineUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadOriginUrl: freezed == downloadOriginUrl
          ? _value.downloadOriginUrl
          : downloadOriginUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoCmsData implements _PlanAutoCmsData {
  const _$_PlanAutoCmsData(
      {this.popupStyle,
      this.mainButtonText,
      this.exitButtonText,
      this.title,
      this.subTitle,
      this.pictureStyle,
      this.popupId,
      this.pictureUrl,
      this.linkUrl,
      this.popupName,
      this.materialFormat,
      this.spineUrl,
      this.businessTagId,
      this.downloadOriginUrl,
      this.materialId});

  factory _$_PlanAutoCmsData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoCmsDataFromJson(json);

  @override
  final int? popupStyle;
  @override
  final String? mainButtonText;
  @override
  final String? exitButtonText;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final int? pictureStyle;
  @override
  final int? popupId;
  @override
  final String? pictureUrl;
  @override
  final String? linkUrl;
  @override
  final String? popupName;
  @override
  final int? materialFormat;
// 1：图片，2:SPINE动画
  @override
  final String? spineUrl;
  @override
  final String? businessTagId;
  @override
  final String? downloadOriginUrl;
// 下载后原图地址
  @override
  final int? materialId;

  @override
  String toString() {
    return 'PlanAutoCmsData(popupStyle: $popupStyle, mainButtonText: $mainButtonText, exitButtonText: $exitButtonText, title: $title, subTitle: $subTitle, pictureStyle: $pictureStyle, popupId: $popupId, pictureUrl: $pictureUrl, linkUrl: $linkUrl, popupName: $popupName, materialFormat: $materialFormat, spineUrl: $spineUrl, businessTagId: $businessTagId, downloadOriginUrl: $downloadOriginUrl, materialId: $materialId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoCmsData &&
            (identical(other.popupStyle, popupStyle) ||
                other.popupStyle == popupStyle) &&
            (identical(other.mainButtonText, mainButtonText) ||
                other.mainButtonText == mainButtonText) &&
            (identical(other.exitButtonText, exitButtonText) ||
                other.exitButtonText == exitButtonText) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.pictureStyle, pictureStyle) ||
                other.pictureStyle == pictureStyle) &&
            (identical(other.popupId, popupId) || other.popupId == popupId) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.popupName, popupName) ||
                other.popupName == popupName) &&
            (identical(other.materialFormat, materialFormat) ||
                other.materialFormat == materialFormat) &&
            (identical(other.spineUrl, spineUrl) ||
                other.spineUrl == spineUrl) &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId) &&
            (identical(other.downloadOriginUrl, downloadOriginUrl) ||
                other.downloadOriginUrl == downloadOriginUrl) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      popupStyle,
      mainButtonText,
      exitButtonText,
      title,
      subTitle,
      pictureStyle,
      popupId,
      pictureUrl,
      linkUrl,
      popupName,
      materialFormat,
      spineUrl,
      businessTagId,
      downloadOriginUrl,
      materialId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoCmsDataCopyWith<_$_PlanAutoCmsData> get copyWith =>
      __$$_PlanAutoCmsDataCopyWithImpl<_$_PlanAutoCmsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoCmsDataToJson(
      this,
    );
  }
}

abstract class _PlanAutoCmsData implements PlanAutoCmsData {
  const factory _PlanAutoCmsData(
      {final int? popupStyle,
      final String? mainButtonText,
      final String? exitButtonText,
      final String? title,
      final String? subTitle,
      final int? pictureStyle,
      final int? popupId,
      final String? pictureUrl,
      final String? linkUrl,
      final String? popupName,
      final int? materialFormat,
      final String? spineUrl,
      final String? businessTagId,
      final String? downloadOriginUrl,
      final int? materialId}) = _$_PlanAutoCmsData;

  factory _PlanAutoCmsData.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoCmsData.fromJson;

  @override
  int? get popupStyle;
  @override
  String? get mainButtonText;
  @override
  String? get exitButtonText;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  int? get pictureStyle;
  @override
  int? get popupId;
  @override
  String? get pictureUrl;
  @override
  String? get linkUrl;
  @override
  String? get popupName;
  @override
  int? get materialFormat;
  @override // 1：图片，2:SPINE动画
  String? get spineUrl;
  @override
  String? get businessTagId;
  @override
  String? get downloadOriginUrl;
  @override // 下载后原图地址
  int? get materialId;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoCmsDataCopyWith<_$_PlanAutoCmsData> get copyWith =>
      throw _privateConstructorUsedError;
}
