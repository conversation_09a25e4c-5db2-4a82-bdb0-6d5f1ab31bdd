// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_auto_cms_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlanAutoCmsData _$$_PlanAutoCmsDataFromJson(Map<String, dynamic> json) =>
    _$_PlanAutoCmsData(
      popupStyle: json['popupStyle'] as int?,
      mainButtonText: json['mainButtonText'] as String?,
      exitButtonText: json['exitButtonText'] as String?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      pictureStyle: json['pictureStyle'] as int?,
      popupId: json['popupId'] as int?,
      pictureUrl: json['pictureUrl'] as String?,
      linkUrl: json['linkUrl'] as String?,
      popupName: json['popupName'] as String?,
      materialFormat: json['materialFormat'] as int?,
      spineUrl: json['spineUrl'] as String?,
      businessTagId: json['businessTagId'] as String?,
      downloadOriginUrl: json['downloadOriginUrl'] as String?,
      materialId: json['materialId'] as int?,
    );

Map<String, dynamic> _$$_PlanAutoCmsDataToJson(_$_PlanAutoCmsData instance) =>
    <String, dynamic>{
      'popupStyle': instance.popupStyle,
      'mainButtonText': instance.mainButtonText,
      'exitButtonText': instance.exitButtonText,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'pictureStyle': instance.pictureStyle,
      'popupId': instance.popupId,
      'pictureUrl': instance.pictureUrl,
      'linkUrl': instance.linkUrl,
      'popupName': instance.popupName,
      'materialFormat': instance.materialFormat,
      'spineUrl': instance.spineUrl,
      'businessTagId': instance.businessTagId,
      'downloadOriginUrl': instance.downloadOriginUrl,
      'materialId': instance.materialId,
    };
