// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_auto_transform_page_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlanAutoTransformPageData _$$_PlanAutoTransformPageDataFromJson(
        Map<String, dynamic> json) =>
    _$_PlanAutoTransformPageData(
      classId: json['classId'] as int?,
      courseKey: json['courseKey'] as String?,
      scheduleList: (json['scheduleList'] as List<dynamic>?)
          ?.map((e) => ScheduleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      trialContactTeacherVo: json['trialContactTeacherVo'] == null
          ? null
          : TrialContactTeacherVo.fromJson(
              json['trialContactTeacherVo'] as Map<String, dynamic>),
      moreTrialScheduleVo: json['moreTrialScheduleVo'] == null
          ? null
          : MoreTrialScheduleVo.fromJson(
              json['moreTrialScheduleVo'] as Map<String, dynamic>),
      trialSchedule: json['trialSchedule'] as int?,
      motivatePopupInfo: json['motivatePopupInfo'] == null
          ? null
          : MotivatePopupInfo.fromJson(
              json['motivatePopupInfo'] as Map<String, dynamic>),
      learnRemindPopupInfo: json['learnRemindPopupInfo'] == null
          ? null
          : LearnRemindPopupInfo.fromJson(
              json['learnRemindPopupInfo'] as Map<String, dynamic>),
      userPageTrialCourseInfo: json['userPageTrialCourseInfo'] == null
          ? null
          : UserPageTrialCourseInfo.fromJson(
              json['userPageTrialCourseInfo'] as Map<String, dynamic>),
      schedulePopupList: (json['schedulePopupList'] as List<dynamic>?)
          ?.map((e) => SchedulePopup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PlanAutoTransformPageDataToJson(
        _$_PlanAutoTransformPageData instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'courseKey': instance.courseKey,
      'scheduleList': instance.scheduleList,
      'trialContactTeacherVo': instance.trialContactTeacherVo,
      'moreTrialScheduleVo': instance.moreTrialScheduleVo,
      'trialSchedule': instance.trialSchedule,
      'motivatePopupInfo': instance.motivatePopupInfo,
      'learnRemindPopupInfo': instance.learnRemindPopupInfo,
      'userPageTrialCourseInfo': instance.userPageTrialCourseInfo,
      'schedulePopupList': instance.schedulePopupList,
    };

_$_UserPageTrialCourseInfo _$$_UserPageTrialCourseInfoFromJson(
        Map<String, dynamic> json) =>
    _$_UserPageTrialCourseInfo(
      trialRemainingDays: json['trialRemainingDays'] as int?,
      finishDays: json['finishDays'] as int?,
      classId: json['classId'] as int?,
      trialCourseSegment: json['trialCourseSegment'] as String?,
      trialCourseKey: json['trialCourseKey'] as String?,
      trialUserCourseBusinessTag: json['trialUserCourseBusinessTag'] as String?,
    );

Map<String, dynamic> _$$_UserPageTrialCourseInfoToJson(
        _$_UserPageTrialCourseInfo instance) =>
    <String, dynamic>{
      'trialRemainingDays': instance.trialRemainingDays,
      'finishDays': instance.finishDays,
      'classId': instance.classId,
      'trialCourseSegment': instance.trialCourseSegment,
      'trialCourseKey': instance.trialCourseKey,
      'trialUserCourseBusinessTag': instance.trialUserCourseBusinessTag,
    };

_$_MoreTrialScheduleVo _$$_MoreTrialScheduleVoFromJson(
        Map<String, dynamic> json) =>
    _$_MoreTrialScheduleVo(
      activateYearCourseTips: json['activateYearCourseTips'] == null
          ? null
          : ActivateYearCourseTips.fromJson(
              json['activateYearCourseTips'] as Map<String, dynamic>),
      scheduleTaskList: (json['scheduleTaskList'] as List<dynamic>?)
          ?.map((e) => ScheduleTaskBo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MoreTrialScheduleVoToJson(
        _$_MoreTrialScheduleVo instance) =>
    <String, dynamic>{
      'activateYearCourseTips': instance.activateYearCourseTips,
      'scheduleTaskList': instance.scheduleTaskList,
    };

_$_ActivateYearCourseTips _$$_ActivateYearCourseTipsFromJson(
        Map<String, dynamic> json) =>
    _$_ActivateYearCourseTips(
      yearButtonRoute: json['yearButtonRoute'] as String?,
      vipCornerMarkRoute: json['vipCornerMarkRoute'] as String?,
      monthButtonText: json['monthButtonText'] as String?,
      yearButtonText: json['yearButtonText'] as String?,
      periodTabDesc: json['periodTabDesc'] as String?,
      cardButtonText: json['cardButtonText'] as String?,
      vipCornerMarkText: json['vipCornerMarkText'] as String?,
      cardImage: json['cardImage'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_ActivateYearCourseTipsToJson(
        _$_ActivateYearCourseTips instance) =>
    <String, dynamic>{
      'yearButtonRoute': instance.yearButtonRoute,
      'vipCornerMarkRoute': instance.vipCornerMarkRoute,
      'monthButtonText': instance.monthButtonText,
      'yearButtonText': instance.yearButtonText,
      'periodTabDesc': instance.periodTabDesc,
      'cardButtonText': instance.cardButtonText,
      'vipCornerMarkText': instance.vipCornerMarkText,
      'cardImage': instance.cardImage,
      'route': instance.route,
    };

_$_LearnRemindPopupInfo _$$_LearnRemindPopupInfoFromJson(
        Map<String, dynamic> json) =>
    _$_LearnRemindPopupInfo(
      remindHeaderImage: json['remindHeaderImage'] as String?,
      remindTitle: json['remindTitle'] as String?,
      remindSwitchVoList: (json['remindSwitchVoList'] as List<dynamic>?)
          ?.map((e) => RemindSwitchVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      agreeText: json['agreeText'] as String?,
      closedText: json['closedText'] as String?,
      remindAnimations: json['remindAnimations'] as String?,
      ruleIds:
          (json['ruleIds'] as List<dynamic>?)?.map((e) => e as int).toList(),
    );

Map<String, dynamic> _$$_LearnRemindPopupInfoToJson(
        _$_LearnRemindPopupInfo instance) =>
    <String, dynamic>{
      'remindHeaderImage': instance.remindHeaderImage,
      'remindTitle': instance.remindTitle,
      'remindSwitchVoList': instance.remindSwitchVoList,
      'agreeText': instance.agreeText,
      'closedText': instance.closedText,
      'remindAnimations': instance.remindAnimations,
      'ruleIds': instance.ruleIds,
    };

_$_RemindSwitchVo _$$_RemindSwitchVoFromJson(Map<String, dynamic> json) =>
    _$_RemindSwitchVo(
      switchName: json['switchName'] as String?,
      switchType: json['switchType'] as int?,
      switchStatus: json['switchStatus'] as bool?,
    );

Map<String, dynamic> _$$_RemindSwitchVoToJson(_$_RemindSwitchVo instance) =>
    <String, dynamic>{
      'switchName': instance.switchName,
      'switchType': instance.switchType,
      'switchStatus': instance.switchStatus,
    };

_$_MotivatePopupInfo _$$_MotivatePopupInfoFromJson(Map<String, dynamic> json) =>
    _$_MotivatePopupInfo(
      motivateHeaderImage: json['motivateHeaderImage'] as String?,
      motivateDesc: json['motivateDesc'] as String?,
      finishDays: json['finishDays'] as int?,
      buttonText: json['buttonText'] as String?,
      lessonMotivatePopups: (json['lessonMotivatePopups'] as List<dynamic>?)
          ?.map((e) => LessonMotivatePopup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MotivatePopupInfoToJson(
        _$_MotivatePopupInfo instance) =>
    <String, dynamic>{
      'motivateHeaderImage': instance.motivateHeaderImage,
      'motivateDesc': instance.motivateDesc,
      'finishDays': instance.finishDays,
      'buttonText': instance.buttonText,
      'lessonMotivatePopups': instance.lessonMotivatePopups,
    };

_$_LessonMotivatePopup _$$_LessonMotivatePopupFromJson(
        Map<String, dynamic> json) =>
    _$_LessonMotivatePopup(
      lessonCount: json['lessonCount'] as int?,
      motivateDesc: json['motivateDesc'] as String?,
      animations: json['animations'] as String?,
      animationsForPad: json['animationsForPad'] as String?,
      buttonText: json['buttonText'] as String?,
    );

Map<String, dynamic> _$$_LessonMotivatePopupToJson(
        _$_LessonMotivatePopup instance) =>
    <String, dynamic>{
      'lessonCount': instance.lessonCount,
      'motivateDesc': instance.motivateDesc,
      'animations': instance.animations,
      'animationsForPad': instance.animationsForPad,
      'buttonText': instance.buttonText,
    };

_$_ScheduleList _$$_ScheduleListFromJson(Map<String, dynamic> json) =>
    _$_ScheduleList(
      showDateTime: json['showDateTime'] as int?,
      showDate: json['showDate'] as String?,
      dateDesc: json['dateDesc'] as String?,
      today: json['today'] as bool?,
      scheduleStatus: json['scheduleStatus'] as int?,
      scheduleTaskList: (json['scheduleTaskList'] as List<dynamic>?)
          ?.map((e) => ScheduleTaskBo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ScheduleListToJson(_$_ScheduleList instance) =>
    <String, dynamic>{
      'showDateTime': instance.showDateTime,
      'showDate': instance.showDate,
      'dateDesc': instance.dateDesc,
      'today': instance.today,
      'scheduleStatus': instance.scheduleStatus,
      'scheduleTaskList': instance.scheduleTaskList,
    };

_$_TrialContactTeacherVo _$$_TrialContactTeacherVoFromJson(
        Map<String, dynamic> json) =>
    _$_TrialContactTeacherVo(
      teacherId: json['teacherId'] as int?,
      teacherAvatarImg: json['teacherAvatarImg'] as String?,
      contactText: json['contactText'] as String?,
      contactRoute: json['contactRoute'] as String?,
    );

Map<String, dynamic> _$$_TrialContactTeacherVoToJson(
        _$_TrialContactTeacherVo instance) =>
    <String, dynamic>{
      'teacherId': instance.teacherId,
      'teacherAvatarImg': instance.teacherAvatarImg,
      'contactText': instance.contactText,
      'contactRoute': instance.contactRoute,
    };
