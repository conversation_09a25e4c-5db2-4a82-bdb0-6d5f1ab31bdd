// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_auto_pagani_popups_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanAutoPaganiPopupsData _$PlanAutoPaganiPopupsDataFromJson(
    Map<String, dynamic> json) {
  return _PlanAutoPaganiPopupsData.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoPaganiPopupsData {
  bool? get disablePopups => throw _privateConstructorUsedError;
  List<PlanAutoPaganiPopup>? get popups => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoPaganiPopupsDataCopyWith<PlanAutoPaganiPopupsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoPaganiPopupsDataCopyWith<$Res> {
  factory $PlanAutoPaganiPopupsDataCopyWith(PlanAutoPaganiPopupsData value,
          $Res Function(PlanAutoPaganiPopupsData) then) =
      _$PlanAutoPaganiPopupsDataCopyWithImpl<$Res, PlanAutoPaganiPopupsData>;
  @useResult
  $Res call({bool? disablePopups, List<PlanAutoPaganiPopup>? popups});
}

/// @nodoc
class _$PlanAutoPaganiPopupsDataCopyWithImpl<$Res,
        $Val extends PlanAutoPaganiPopupsData>
    implements $PlanAutoPaganiPopupsDataCopyWith<$Res> {
  _$PlanAutoPaganiPopupsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? disablePopups = freezed,
    Object? popups = freezed,
  }) {
    return _then(_value.copyWith(
      disablePopups: freezed == disablePopups
          ? _value.disablePopups
          : disablePopups // ignore: cast_nullable_to_non_nullable
              as bool?,
      popups: freezed == popups
          ? _value.popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<PlanAutoPaganiPopup>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanAutoPaganiPopupsDataCopyWith<$Res>
    implements $PlanAutoPaganiPopupsDataCopyWith<$Res> {
  factory _$$_PlanAutoPaganiPopupsDataCopyWith(
          _$_PlanAutoPaganiPopupsData value,
          $Res Function(_$_PlanAutoPaganiPopupsData) then) =
      __$$_PlanAutoPaganiPopupsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? disablePopups, List<PlanAutoPaganiPopup>? popups});
}

/// @nodoc
class __$$_PlanAutoPaganiPopupsDataCopyWithImpl<$Res>
    extends _$PlanAutoPaganiPopupsDataCopyWithImpl<$Res,
        _$_PlanAutoPaganiPopupsData>
    implements _$$_PlanAutoPaganiPopupsDataCopyWith<$Res> {
  __$$_PlanAutoPaganiPopupsDataCopyWithImpl(_$_PlanAutoPaganiPopupsData _value,
      $Res Function(_$_PlanAutoPaganiPopupsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? disablePopups = freezed,
    Object? popups = freezed,
  }) {
    return _then(_$_PlanAutoPaganiPopupsData(
      disablePopups: freezed == disablePopups
          ? _value.disablePopups
          : disablePopups // ignore: cast_nullable_to_non_nullable
              as bool?,
      popups: freezed == popups
          ? _value._popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<PlanAutoPaganiPopup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoPaganiPopupsData implements _PlanAutoPaganiPopupsData {
  const _$_PlanAutoPaganiPopupsData(
      {this.disablePopups, final List<PlanAutoPaganiPopup>? popups})
      : _popups = popups;

  factory _$_PlanAutoPaganiPopupsData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoPaganiPopupsDataFromJson(json);

  @override
  final bool? disablePopups;
  final List<PlanAutoPaganiPopup>? _popups;
  @override
  List<PlanAutoPaganiPopup>? get popups {
    final value = _popups;
    if (value == null) return null;
    if (_popups is EqualUnmodifiableListView) return _popups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PlanAutoPaganiPopupsData(disablePopups: $disablePopups, popups: $popups)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoPaganiPopupsData &&
            (identical(other.disablePopups, disablePopups) ||
                other.disablePopups == disablePopups) &&
            const DeepCollectionEquality().equals(other._popups, _popups));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, disablePopups, const DeepCollectionEquality().hash(_popups));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoPaganiPopupsDataCopyWith<_$_PlanAutoPaganiPopupsData>
      get copyWith => __$$_PlanAutoPaganiPopupsDataCopyWithImpl<
          _$_PlanAutoPaganiPopupsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoPaganiPopupsDataToJson(
      this,
    );
  }
}

abstract class _PlanAutoPaganiPopupsData implements PlanAutoPaganiPopupsData {
  const factory _PlanAutoPaganiPopupsData(
      {final bool? disablePopups,
      final List<PlanAutoPaganiPopup>? popups}) = _$_PlanAutoPaganiPopupsData;

  factory _PlanAutoPaganiPopupsData.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoPaganiPopupsData.fromJson;

  @override
  bool? get disablePopups;
  @override
  List<PlanAutoPaganiPopup>? get popups;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoPaganiPopupsDataCopyWith<_$_PlanAutoPaganiPopupsData>
      get copyWith => throw _privateConstructorUsedError;
}

PlanAutoPaganiPopup _$PlanAutoPaganiPopupFromJson(Map<String, dynamic> json) {
  return _PlanAutoPaganiPopup.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoPaganiPopup {
  String? get type => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  PlanAutoPaganiPopupExtend? get extend => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoPaganiPopupCopyWith<PlanAutoPaganiPopup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoPaganiPopupCopyWith<$Res> {
  factory $PlanAutoPaganiPopupCopyWith(
          PlanAutoPaganiPopup value, $Res Function(PlanAutoPaganiPopup) then) =
      _$PlanAutoPaganiPopupCopyWithImpl<$Res, PlanAutoPaganiPopup>;
  @useResult
  $Res call({String? type, int? order, PlanAutoPaganiPopupExtend? extend});

  $PlanAutoPaganiPopupExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class _$PlanAutoPaganiPopupCopyWithImpl<$Res, $Val extends PlanAutoPaganiPopup>
    implements $PlanAutoPaganiPopupCopyWith<$Res> {
  _$PlanAutoPaganiPopupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? order = freezed,
    Object? extend = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as PlanAutoPaganiPopupExtend?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PlanAutoPaganiPopupExtendCopyWith<$Res>? get extend {
    if (_value.extend == null) {
      return null;
    }

    return $PlanAutoPaganiPopupExtendCopyWith<$Res>(_value.extend!, (value) {
      return _then(_value.copyWith(extend: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PlanAutoPaganiPopupCopyWith<$Res>
    implements $PlanAutoPaganiPopupCopyWith<$Res> {
  factory _$$_PlanAutoPaganiPopupCopyWith(_$_PlanAutoPaganiPopup value,
          $Res Function(_$_PlanAutoPaganiPopup) then) =
      __$$_PlanAutoPaganiPopupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? type, int? order, PlanAutoPaganiPopupExtend? extend});

  @override
  $PlanAutoPaganiPopupExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class __$$_PlanAutoPaganiPopupCopyWithImpl<$Res>
    extends _$PlanAutoPaganiPopupCopyWithImpl<$Res, _$_PlanAutoPaganiPopup>
    implements _$$_PlanAutoPaganiPopupCopyWith<$Res> {
  __$$_PlanAutoPaganiPopupCopyWithImpl(_$_PlanAutoPaganiPopup _value,
      $Res Function(_$_PlanAutoPaganiPopup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? order = freezed,
    Object? extend = freezed,
  }) {
    return _then(_$_PlanAutoPaganiPopup(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as PlanAutoPaganiPopupExtend?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoPaganiPopup implements _PlanAutoPaganiPopup {
  const _$_PlanAutoPaganiPopup({this.type, this.order, this.extend});

  factory _$_PlanAutoPaganiPopup.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoPaganiPopupFromJson(json);

  @override
  final String? type;
  @override
  final int? order;
  @override
  final PlanAutoPaganiPopupExtend? extend;

  @override
  String toString() {
    return 'PlanAutoPaganiPopup(type: $type, order: $order, extend: $extend)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoPaganiPopup &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.extend, extend) || other.extend == extend));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, order, extend);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoPaganiPopupCopyWith<_$_PlanAutoPaganiPopup> get copyWith =>
      __$$_PlanAutoPaganiPopupCopyWithImpl<_$_PlanAutoPaganiPopup>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoPaganiPopupToJson(
      this,
    );
  }
}

abstract class _PlanAutoPaganiPopup implements PlanAutoPaganiPopup {
  const factory _PlanAutoPaganiPopup(
      {final String? type,
      final int? order,
      final PlanAutoPaganiPopupExtend? extend}) = _$_PlanAutoPaganiPopup;

  factory _PlanAutoPaganiPopup.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoPaganiPopup.fromJson;

  @override
  String? get type;
  @override
  int? get order;
  @override
  PlanAutoPaganiPopupExtend? get extend;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoPaganiPopupCopyWith<_$_PlanAutoPaganiPopup> get copyWith =>
      throw _privateConstructorUsedError;
}

PlanAutoPaganiPopupExtend _$PlanAutoPaganiPopupExtendFromJson(
    Map<String, dynamic> json) {
  return _PlanAutoPaganiPopupExtend.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoPaganiPopupExtend {
  LessonFinishData? get lessonFinishData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoPaganiPopupExtendCopyWith<PlanAutoPaganiPopupExtend> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoPaganiPopupExtendCopyWith<$Res> {
  factory $PlanAutoPaganiPopupExtendCopyWith(PlanAutoPaganiPopupExtend value,
          $Res Function(PlanAutoPaganiPopupExtend) then) =
      _$PlanAutoPaganiPopupExtendCopyWithImpl<$Res, PlanAutoPaganiPopupExtend>;
  @useResult
  $Res call({LessonFinishData? lessonFinishData});

  $LessonFinishDataCopyWith<$Res>? get lessonFinishData;
}

/// @nodoc
class _$PlanAutoPaganiPopupExtendCopyWithImpl<$Res,
        $Val extends PlanAutoPaganiPopupExtend>
    implements $PlanAutoPaganiPopupExtendCopyWith<$Res> {
  _$PlanAutoPaganiPopupExtendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonFinishData = freezed,
  }) {
    return _then(_value.copyWith(
      lessonFinishData: freezed == lessonFinishData
          ? _value.lessonFinishData
          : lessonFinishData // ignore: cast_nullable_to_non_nullable
              as LessonFinishData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LessonFinishDataCopyWith<$Res>? get lessonFinishData {
    if (_value.lessonFinishData == null) {
      return null;
    }

    return $LessonFinishDataCopyWith<$Res>(_value.lessonFinishData!, (value) {
      return _then(_value.copyWith(lessonFinishData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PlanAutoPaganiPopupExtendCopyWith<$Res>
    implements $PlanAutoPaganiPopupExtendCopyWith<$Res> {
  factory _$$_PlanAutoPaganiPopupExtendCopyWith(
          _$_PlanAutoPaganiPopupExtend value,
          $Res Function(_$_PlanAutoPaganiPopupExtend) then) =
      __$$_PlanAutoPaganiPopupExtendCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({LessonFinishData? lessonFinishData});

  @override
  $LessonFinishDataCopyWith<$Res>? get lessonFinishData;
}

/// @nodoc
class __$$_PlanAutoPaganiPopupExtendCopyWithImpl<$Res>
    extends _$PlanAutoPaganiPopupExtendCopyWithImpl<$Res,
        _$_PlanAutoPaganiPopupExtend>
    implements _$$_PlanAutoPaganiPopupExtendCopyWith<$Res> {
  __$$_PlanAutoPaganiPopupExtendCopyWithImpl(
      _$_PlanAutoPaganiPopupExtend _value,
      $Res Function(_$_PlanAutoPaganiPopupExtend) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonFinishData = freezed,
  }) {
    return _then(_$_PlanAutoPaganiPopupExtend(
      lessonFinishData: freezed == lessonFinishData
          ? _value.lessonFinishData
          : lessonFinishData // ignore: cast_nullable_to_non_nullable
              as LessonFinishData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoPaganiPopupExtend implements _PlanAutoPaganiPopupExtend {
  const _$_PlanAutoPaganiPopupExtend({this.lessonFinishData});

  factory _$_PlanAutoPaganiPopupExtend.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoPaganiPopupExtendFromJson(json);

  @override
  final LessonFinishData? lessonFinishData;

  @override
  String toString() {
    return 'PlanAutoPaganiPopupExtend(lessonFinishData: $lessonFinishData)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoPaganiPopupExtend &&
            (identical(other.lessonFinishData, lessonFinishData) ||
                other.lessonFinishData == lessonFinishData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lessonFinishData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoPaganiPopupExtendCopyWith<_$_PlanAutoPaganiPopupExtend>
      get copyWith => __$$_PlanAutoPaganiPopupExtendCopyWithImpl<
          _$_PlanAutoPaganiPopupExtend>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoPaganiPopupExtendToJson(
      this,
    );
  }
}

abstract class _PlanAutoPaganiPopupExtend implements PlanAutoPaganiPopupExtend {
  const factory _PlanAutoPaganiPopupExtend(
          {final LessonFinishData? lessonFinishData}) =
      _$_PlanAutoPaganiPopupExtend;

  factory _PlanAutoPaganiPopupExtend.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoPaganiPopupExtend.fromJson;

  @override
  LessonFinishData? get lessonFinishData;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoPaganiPopupExtendCopyWith<_$_PlanAutoPaganiPopupExtend>
      get copyWith => throw _privateConstructorUsedError;
}

LessonFinishData _$LessonFinishDataFromJson(Map<String, dynamic> json) {
  return _LessonFinishData.fromJson(json);
}

/// @nodoc
mixin _$LessonFinishData {
  int? get finishCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonFinishDataCopyWith<LessonFinishData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonFinishDataCopyWith<$Res> {
  factory $LessonFinishDataCopyWith(
          LessonFinishData value, $Res Function(LessonFinishData) then) =
      _$LessonFinishDataCopyWithImpl<$Res, LessonFinishData>;
  @useResult
  $Res call({int? finishCount});
}

/// @nodoc
class _$LessonFinishDataCopyWithImpl<$Res, $Val extends LessonFinishData>
    implements $LessonFinishDataCopyWith<$Res> {
  _$LessonFinishDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? finishCount = freezed,
  }) {
    return _then(_value.copyWith(
      finishCount: freezed == finishCount
          ? _value.finishCount
          : finishCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonFinishDataCopyWith<$Res>
    implements $LessonFinishDataCopyWith<$Res> {
  factory _$$_LessonFinishDataCopyWith(
          _$_LessonFinishData value, $Res Function(_$_LessonFinishData) then) =
      __$$_LessonFinishDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? finishCount});
}

/// @nodoc
class __$$_LessonFinishDataCopyWithImpl<$Res>
    extends _$LessonFinishDataCopyWithImpl<$Res, _$_LessonFinishData>
    implements _$$_LessonFinishDataCopyWith<$Res> {
  __$$_LessonFinishDataCopyWithImpl(
      _$_LessonFinishData _value, $Res Function(_$_LessonFinishData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? finishCount = freezed,
  }) {
    return _then(_$_LessonFinishData(
      finishCount: freezed == finishCount
          ? _value.finishCount
          : finishCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonFinishData implements _LessonFinishData {
  const _$_LessonFinishData({this.finishCount});

  factory _$_LessonFinishData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonFinishDataFromJson(json);

  @override
  final int? finishCount;

  @override
  String toString() {
    return 'LessonFinishData(finishCount: $finishCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonFinishData &&
            (identical(other.finishCount, finishCount) ||
                other.finishCount == finishCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, finishCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonFinishDataCopyWith<_$_LessonFinishData> get copyWith =>
      __$$_LessonFinishDataCopyWithImpl<_$_LessonFinishData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonFinishDataToJson(
      this,
    );
  }
}

abstract class _LessonFinishData implements LessonFinishData {
  const factory _LessonFinishData({final int? finishCount}) =
      _$_LessonFinishData;

  factory _LessonFinishData.fromJson(Map<String, dynamic> json) =
      _$_LessonFinishData.fromJson;

  @override
  int? get finishCount;
  @override
  @JsonKey(ignore: true)
  _$$_LessonFinishDataCopyWith<_$_LessonFinishData> get copyWith =>
      throw _privateConstructorUsedError;
}
