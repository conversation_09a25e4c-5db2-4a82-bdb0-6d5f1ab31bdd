// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_auto_transform_years_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlanAutoTransformYearsData _$$_PlanAutoTransformYearsDataFromJson(
        Map<String, dynamic> json) =>
    _$_PlanAutoTransformYearsData(
      year: json['year'] as int?,
      currentCourseLabel: json['currentCourseLabel'] as String?,
      trialRemainingDays: json['trialRemainingDays'] as int?,
      monthScheduleList: (json['monthScheduleList'] as List<dynamic>?)
          ?.map((e) => MonthScheduleList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PlanAutoTransformYearsDataToJson(
        _$_PlanAutoTransformYearsData instance) =>
    <String, dynamic>{
      'year': instance.year,
      'currentCourseLabel': instance.currentCourseLabel,
      'trialRemainingDays': instance.trialRemainingDays,
      'monthScheduleList': instance.monthScheduleList,
    };

_$_MonthScheduleList _$$_MonthScheduleListFromJson(Map<String, dynamic> json) =>
    _$_MonthScheduleList(
      month: json['month'] as String?,
      monthNum: json['monthNum'] as int?,
      currentMonth: json['currentMonth'] as bool?,
      yearCourseImage: json['yearCourseImage'] == null
          ? null
          : YearCourseImage.fromJson(
              json['yearCourseImage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_MonthScheduleListToJson(
        _$_MonthScheduleList instance) =>
    <String, dynamic>{
      'month': instance.month,
      'monthNum': instance.monthNum,
      'currentMonth': instance.currentMonth,
      'yearCourseImage': instance.yearCourseImage,
    };

_$_YearCourseImage _$$_YearCourseImageFromJson(Map<String, dynamic> json) =>
    _$_YearCourseImage(
      subjectType: json['subjectType'] as int?,
      image: json['image'] as String?,
      lock: json['lock'] as bool?,
      buy: json['buy'] as bool?,
      buyType: json['buyType'] as String?,
      buyImage: json['buyImage'] as String?,
      buyRoute: json['buyRoute'] as String?,
      segmentId: json['segmentId'] as int?,
      segmentMonthDate: json['segmentMonthDate'] as String?,
      courseSegmentName: json['courseSegmentName'] as String?,
      newGetFlag: json['newGetFlag'] as bool?,
      newGetClassId: json['newGetClassId'] as int?,
    );

Map<String, dynamic> _$$_YearCourseImageToJson(_$_YearCourseImage instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'image': instance.image,
      'lock': instance.lock,
      'buy': instance.buy,
      'buyType': instance.buyType,
      'buyImage': instance.buyImage,
      'buyRoute': instance.buyRoute,
      'segmentId': instance.segmentId,
      'segmentMonthDate': instance.segmentMonthDate,
      'courseSegmentName': instance.courseSegmentName,
      'newGetFlag': instance.newGetFlag,
      'newGetClassId': instance.newGetClassId,
    };
