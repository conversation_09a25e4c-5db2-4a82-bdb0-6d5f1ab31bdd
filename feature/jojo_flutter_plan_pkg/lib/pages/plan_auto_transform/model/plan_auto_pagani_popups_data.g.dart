// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_auto_pagani_popups_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlanAutoPaganiPopupsData _$$_PlanAutoPaganiPopupsDataFromJson(
        Map<String, dynamic> json) =>
    _$_PlanAutoPaganiPopupsData(
      disablePopups: json['disablePopups'] as bool?,
      popups: (json['popups'] as List<dynamic>?)
          ?.map((e) => PlanAutoPaganiPopup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PlanAutoPaganiPopupsDataToJson(
        _$_PlanAutoPaganiPopupsData instance) =>
    <String, dynamic>{
      'disablePopups': instance.disablePopups,
      'popups': instance.popups,
    };

_$_PlanAutoPaganiPopup _$$_PlanAutoPaganiPopupFromJson(
        Map<String, dynamic> json) =>
    _$_PlanAutoPaganiPopup(
      type: json['type'] as String?,
      order: json['order'] as int?,
      extend: json['extend'] == null
          ? null
          : PlanAutoPaganiPopupExtend.fromJson(
              json['extend'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_PlanAutoPaganiPopupToJson(
        _$_PlanAutoPaganiPopup instance) =>
    <String, dynamic>{
      'type': instance.type,
      'order': instance.order,
      'extend': instance.extend,
    };

_$_PlanAutoPaganiPopupExtend _$$_PlanAutoPaganiPopupExtendFromJson(
        Map<String, dynamic> json) =>
    _$_PlanAutoPaganiPopupExtend(
      lessonFinishData: json['lessonFinishData'] == null
          ? null
          : LessonFinishData.fromJson(
              json['lessonFinishData'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_PlanAutoPaganiPopupExtendToJson(
        _$_PlanAutoPaganiPopupExtend instance) =>
    <String, dynamic>{
      'lessonFinishData': instance.lessonFinishData,
    };

_$_LessonFinishData _$$_LessonFinishDataFromJson(Map<String, dynamic> json) =>
    _$_LessonFinishData(
      finishCount: json['finishCount'] as int?,
    );

Map<String, dynamic> _$$_LessonFinishDataToJson(_$_LessonFinishData instance) =>
    <String, dynamic>{
      'finishCount': instance.finishCount,
    };
