// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_auto_transform_years_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanAutoTransformYearsData _$PlanAutoTransformYearsDataFromJson(
    Map<String, dynamic> json) {
  return _PlanAutoTransformYearsData.fromJson(json);
}

/// @nodoc
mixin _$PlanAutoTransformYearsData {
  int? get year => throw _privateConstructorUsedError;
  String? get currentCourseLabel => throw _privateConstructorUsedError;
  int? get trialRemainingDays => throw _privateConstructorUsedError;
  List<MonthScheduleList>? get monthScheduleList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanAutoTransformYearsDataCopyWith<PlanAutoTransformYearsData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAutoTransformYearsDataCopyWith<$Res> {
  factory $PlanAutoTransformYearsDataCopyWith(PlanAutoTransformYearsData value,
          $Res Function(PlanAutoTransformYearsData) then) =
      _$PlanAutoTransformYearsDataCopyWithImpl<$Res,
          PlanAutoTransformYearsData>;
  @useResult
  $Res call(
      {int? year,
      String? currentCourseLabel,
      int? trialRemainingDays,
      List<MonthScheduleList>? monthScheduleList});
}

/// @nodoc
class _$PlanAutoTransformYearsDataCopyWithImpl<$Res,
        $Val extends PlanAutoTransformYearsData>
    implements $PlanAutoTransformYearsDataCopyWith<$Res> {
  _$PlanAutoTransformYearsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? currentCourseLabel = freezed,
    Object? trialRemainingDays = freezed,
    Object? monthScheduleList = freezed,
  }) {
    return _then(_value.copyWith(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      currentCourseLabel: freezed == currentCourseLabel
          ? _value.currentCourseLabel
          : currentCourseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      trialRemainingDays: freezed == trialRemainingDays
          ? _value.trialRemainingDays
          : trialRemainingDays // ignore: cast_nullable_to_non_nullable
              as int?,
      monthScheduleList: freezed == monthScheduleList
          ? _value.monthScheduleList
          : monthScheduleList // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanAutoTransformYearsDataCopyWith<$Res>
    implements $PlanAutoTransformYearsDataCopyWith<$Res> {
  factory _$$_PlanAutoTransformYearsDataCopyWith(
          _$_PlanAutoTransformYearsData value,
          $Res Function(_$_PlanAutoTransformYearsData) then) =
      __$$_PlanAutoTransformYearsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? year,
      String? currentCourseLabel,
      int? trialRemainingDays,
      List<MonthScheduleList>? monthScheduleList});
}

/// @nodoc
class __$$_PlanAutoTransformYearsDataCopyWithImpl<$Res>
    extends _$PlanAutoTransformYearsDataCopyWithImpl<$Res,
        _$_PlanAutoTransformYearsData>
    implements _$$_PlanAutoTransformYearsDataCopyWith<$Res> {
  __$$_PlanAutoTransformYearsDataCopyWithImpl(
      _$_PlanAutoTransformYearsData _value,
      $Res Function(_$_PlanAutoTransformYearsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? currentCourseLabel = freezed,
    Object? trialRemainingDays = freezed,
    Object? monthScheduleList = freezed,
  }) {
    return _then(_$_PlanAutoTransformYearsData(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      currentCourseLabel: freezed == currentCourseLabel
          ? _value.currentCourseLabel
          : currentCourseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      trialRemainingDays: freezed == trialRemainingDays
          ? _value.trialRemainingDays
          : trialRemainingDays // ignore: cast_nullable_to_non_nullable
              as int?,
      monthScheduleList: freezed == monthScheduleList
          ? _value._monthScheduleList
          : monthScheduleList // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanAutoTransformYearsData implements _PlanAutoTransformYearsData {
  const _$_PlanAutoTransformYearsData(
      {this.year,
      this.currentCourseLabel,
      this.trialRemainingDays,
      final List<MonthScheduleList>? monthScheduleList})
      : _monthScheduleList = monthScheduleList;

  factory _$_PlanAutoTransformYearsData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanAutoTransformYearsDataFromJson(json);

  @override
  final int? year;
  @override
  final String? currentCourseLabel;
  @override
  final int? trialRemainingDays;
  final List<MonthScheduleList>? _monthScheduleList;
  @override
  List<MonthScheduleList>? get monthScheduleList {
    final value = _monthScheduleList;
    if (value == null) return null;
    if (_monthScheduleList is EqualUnmodifiableListView)
      return _monthScheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PlanAutoTransformYearsData(year: $year, currentCourseLabel: $currentCourseLabel, trialRemainingDays: $trialRemainingDays, monthScheduleList: $monthScheduleList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanAutoTransformYearsData &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.currentCourseLabel, currentCourseLabel) ||
                other.currentCourseLabel == currentCourseLabel) &&
            (identical(other.trialRemainingDays, trialRemainingDays) ||
                other.trialRemainingDays == trialRemainingDays) &&
            const DeepCollectionEquality()
                .equals(other._monthScheduleList, _monthScheduleList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      year,
      currentCourseLabel,
      trialRemainingDays,
      const DeepCollectionEquality().hash(_monthScheduleList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanAutoTransformYearsDataCopyWith<_$_PlanAutoTransformYearsData>
      get copyWith => __$$_PlanAutoTransformYearsDataCopyWithImpl<
          _$_PlanAutoTransformYearsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanAutoTransformYearsDataToJson(
      this,
    );
  }
}

abstract class _PlanAutoTransformYearsData
    implements PlanAutoTransformYearsData {
  const factory _PlanAutoTransformYearsData(
          {final int? year,
          final String? currentCourseLabel,
          final int? trialRemainingDays,
          final List<MonthScheduleList>? monthScheduleList}) =
      _$_PlanAutoTransformYearsData;

  factory _PlanAutoTransformYearsData.fromJson(Map<String, dynamic> json) =
      _$_PlanAutoTransformYearsData.fromJson;

  @override
  int? get year;
  @override
  String? get currentCourseLabel;
  @override
  int? get trialRemainingDays;
  @override
  List<MonthScheduleList>? get monthScheduleList;
  @override
  @JsonKey(ignore: true)
  _$$_PlanAutoTransformYearsDataCopyWith<_$_PlanAutoTransformYearsData>
      get copyWith => throw _privateConstructorUsedError;
}

MonthScheduleList _$MonthScheduleListFromJson(Map<String, dynamic> json) {
  return _MonthScheduleList.fromJson(json);
}

/// @nodoc
mixin _$MonthScheduleList {
  String? get month => throw _privateConstructorUsedError;
  int? get monthNum => throw _privateConstructorUsedError;
  bool? get currentMonth => throw _privateConstructorUsedError;
  YearCourseImage? get yearCourseImage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonthScheduleListCopyWith<MonthScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonthScheduleListCopyWith<$Res> {
  factory $MonthScheduleListCopyWith(
          MonthScheduleList value, $Res Function(MonthScheduleList) then) =
      _$MonthScheduleListCopyWithImpl<$Res, MonthScheduleList>;
  @useResult
  $Res call(
      {String? month,
      int? monthNum,
      bool? currentMonth,
      YearCourseImage? yearCourseImage});

  $YearCourseImageCopyWith<$Res>? get yearCourseImage;
}

/// @nodoc
class _$MonthScheduleListCopyWithImpl<$Res, $Val extends MonthScheduleList>
    implements $MonthScheduleListCopyWith<$Res> {
  _$MonthScheduleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? currentMonth = freezed,
    Object? yearCourseImage = freezed,
  }) {
    return _then(_value.copyWith(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      currentMonth: freezed == currentMonth
          ? _value.currentMonth
          : currentMonth // ignore: cast_nullable_to_non_nullable
              as bool?,
      yearCourseImage: freezed == yearCourseImage
          ? _value.yearCourseImage
          : yearCourseImage // ignore: cast_nullable_to_non_nullable
              as YearCourseImage?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $YearCourseImageCopyWith<$Res>? get yearCourseImage {
    if (_value.yearCourseImage == null) {
      return null;
    }

    return $YearCourseImageCopyWith<$Res>(_value.yearCourseImage!, (value) {
      return _then(_value.copyWith(yearCourseImage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MonthScheduleListCopyWith<$Res>
    implements $MonthScheduleListCopyWith<$Res> {
  factory _$$_MonthScheduleListCopyWith(_$_MonthScheduleList value,
          $Res Function(_$_MonthScheduleList) then) =
      __$$_MonthScheduleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? month,
      int? monthNum,
      bool? currentMonth,
      YearCourseImage? yearCourseImage});

  @override
  $YearCourseImageCopyWith<$Res>? get yearCourseImage;
}

/// @nodoc
class __$$_MonthScheduleListCopyWithImpl<$Res>
    extends _$MonthScheduleListCopyWithImpl<$Res, _$_MonthScheduleList>
    implements _$$_MonthScheduleListCopyWith<$Res> {
  __$$_MonthScheduleListCopyWithImpl(
      _$_MonthScheduleList _value, $Res Function(_$_MonthScheduleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? currentMonth = freezed,
    Object? yearCourseImage = freezed,
  }) {
    return _then(_$_MonthScheduleList(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      currentMonth: freezed == currentMonth
          ? _value.currentMonth
          : currentMonth // ignore: cast_nullable_to_non_nullable
              as bool?,
      yearCourseImage: freezed == yearCourseImage
          ? _value.yearCourseImage
          : yearCourseImage // ignore: cast_nullable_to_non_nullable
              as YearCourseImage?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MonthScheduleList implements _MonthScheduleList {
  const _$_MonthScheduleList(
      {this.month, this.monthNum, this.currentMonth, this.yearCourseImage});

  factory _$_MonthScheduleList.fromJson(Map<String, dynamic> json) =>
      _$$_MonthScheduleListFromJson(json);

  @override
  final String? month;
  @override
  final int? monthNum;
  @override
  final bool? currentMonth;
  @override
  final YearCourseImage? yearCourseImage;

  @override
  String toString() {
    return 'MonthScheduleList(month: $month, monthNum: $monthNum, currentMonth: $currentMonth, yearCourseImage: $yearCourseImage)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MonthScheduleList &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.monthNum, monthNum) ||
                other.monthNum == monthNum) &&
            (identical(other.currentMonth, currentMonth) ||
                other.currentMonth == currentMonth) &&
            (identical(other.yearCourseImage, yearCourseImage) ||
                other.yearCourseImage == yearCourseImage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, month, monthNum, currentMonth, yearCourseImage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MonthScheduleListCopyWith<_$_MonthScheduleList> get copyWith =>
      __$$_MonthScheduleListCopyWithImpl<_$_MonthScheduleList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MonthScheduleListToJson(
      this,
    );
  }
}

abstract class _MonthScheduleList implements MonthScheduleList {
  const factory _MonthScheduleList(
      {final String? month,
      final int? monthNum,
      final bool? currentMonth,
      final YearCourseImage? yearCourseImage}) = _$_MonthScheduleList;

  factory _MonthScheduleList.fromJson(Map<String, dynamic> json) =
      _$_MonthScheduleList.fromJson;

  @override
  String? get month;
  @override
  int? get monthNum;
  @override
  bool? get currentMonth;
  @override
  YearCourseImage? get yearCourseImage;
  @override
  @JsonKey(ignore: true)
  _$$_MonthScheduleListCopyWith<_$_MonthScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

YearCourseImage _$YearCourseImageFromJson(Map<String, dynamic> json) {
  return _YearCourseImage.fromJson(json);
}

/// @nodoc
mixin _$YearCourseImage {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  bool? get lock => throw _privateConstructorUsedError;
  bool? get buy => throw _privateConstructorUsedError;
  String? get buyType => throw _privateConstructorUsedError;
  String? get buyImage => throw _privateConstructorUsedError;
  String? get buyRoute => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  String? get segmentMonthDate => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  bool? get newGetFlag => throw _privateConstructorUsedError;
  int? get newGetClassId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $YearCourseImageCopyWith<YearCourseImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $YearCourseImageCopyWith<$Res> {
  factory $YearCourseImageCopyWith(
          YearCourseImage value, $Res Function(YearCourseImage) then) =
      _$YearCourseImageCopyWithImpl<$Res, YearCourseImage>;
  @useResult
  $Res call(
      {int? subjectType,
      String? image,
      bool? lock,
      bool? buy,
      String? buyType,
      String? buyImage,
      String? buyRoute,
      int? segmentId,
      String? segmentMonthDate,
      String? courseSegmentName,
      bool? newGetFlag,
      int? newGetClassId});
}

/// @nodoc
class _$YearCourseImageCopyWithImpl<$Res, $Val extends YearCourseImage>
    implements $YearCourseImageCopyWith<$Res> {
  _$YearCourseImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? image = freezed,
    Object? lock = freezed,
    Object? buy = freezed,
    Object? buyType = freezed,
    Object? buyImage = freezed,
    Object? buyRoute = freezed,
    Object? segmentId = freezed,
    Object? segmentMonthDate = freezed,
    Object? courseSegmentName = freezed,
    Object? newGetFlag = freezed,
    Object? newGetClassId = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      buy: freezed == buy
          ? _value.buy
          : buy // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyType: freezed == buyType
          ? _value.buyType
          : buyType // ignore: cast_nullable_to_non_nullable
              as String?,
      buyImage: freezed == buyImage
          ? _value.buyImage
          : buyImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRoute: freezed == buyRoute
          ? _value.buyRoute
          : buyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentMonthDate: freezed == segmentMonthDate
          ? _value.segmentMonthDate
          : segmentMonthDate // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetClassId: freezed == newGetClassId
          ? _value.newGetClassId
          : newGetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_YearCourseImageCopyWith<$Res>
    implements $YearCourseImageCopyWith<$Res> {
  factory _$$_YearCourseImageCopyWith(
          _$_YearCourseImage value, $Res Function(_$_YearCourseImage) then) =
      __$$_YearCourseImageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? image,
      bool? lock,
      bool? buy,
      String? buyType,
      String? buyImage,
      String? buyRoute,
      int? segmentId,
      String? segmentMonthDate,
      String? courseSegmentName,
      bool? newGetFlag,
      int? newGetClassId});
}

/// @nodoc
class __$$_YearCourseImageCopyWithImpl<$Res>
    extends _$YearCourseImageCopyWithImpl<$Res, _$_YearCourseImage>
    implements _$$_YearCourseImageCopyWith<$Res> {
  __$$_YearCourseImageCopyWithImpl(
      _$_YearCourseImage _value, $Res Function(_$_YearCourseImage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? image = freezed,
    Object? lock = freezed,
    Object? buy = freezed,
    Object? buyType = freezed,
    Object? buyImage = freezed,
    Object? buyRoute = freezed,
    Object? segmentId = freezed,
    Object? segmentMonthDate = freezed,
    Object? courseSegmentName = freezed,
    Object? newGetFlag = freezed,
    Object? newGetClassId = freezed,
  }) {
    return _then(_$_YearCourseImage(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      buy: freezed == buy
          ? _value.buy
          : buy // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyType: freezed == buyType
          ? _value.buyType
          : buyType // ignore: cast_nullable_to_non_nullable
              as String?,
      buyImage: freezed == buyImage
          ? _value.buyImage
          : buyImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRoute: freezed == buyRoute
          ? _value.buyRoute
          : buyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentMonthDate: freezed == segmentMonthDate
          ? _value.segmentMonthDate
          : segmentMonthDate // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetClassId: freezed == newGetClassId
          ? _value.newGetClassId
          : newGetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_YearCourseImage implements _YearCourseImage {
  const _$_YearCourseImage(
      {this.subjectType,
      this.image,
      this.lock,
      this.buy,
      this.buyType,
      this.buyImage,
      this.buyRoute,
      this.segmentId,
      this.segmentMonthDate,
      this.courseSegmentName,
      this.newGetFlag,
      this.newGetClassId});

  factory _$_YearCourseImage.fromJson(Map<String, dynamic> json) =>
      _$$_YearCourseImageFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? image;
  @override
  final bool? lock;
  @override
  final bool? buy;
  @override
  final String? buyType;
  @override
  final String? buyImage;
  @override
  final String? buyRoute;
  @override
  final int? segmentId;
  @override
  final String? segmentMonthDate;
  @override
  final String? courseSegmentName;
  @override
  final bool? newGetFlag;
  @override
  final int? newGetClassId;

  @override
  String toString() {
    return 'YearCourseImage(subjectType: $subjectType, image: $image, lock: $lock, buy: $buy, buyType: $buyType, buyImage: $buyImage, buyRoute: $buyRoute, segmentId: $segmentId, segmentMonthDate: $segmentMonthDate, courseSegmentName: $courseSegmentName, newGetFlag: $newGetFlag, newGetClassId: $newGetClassId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_YearCourseImage &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.lock, lock) || other.lock == lock) &&
            (identical(other.buy, buy) || other.buy == buy) &&
            (identical(other.buyType, buyType) || other.buyType == buyType) &&
            (identical(other.buyImage, buyImage) ||
                other.buyImage == buyImage) &&
            (identical(other.buyRoute, buyRoute) ||
                other.buyRoute == buyRoute) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentMonthDate, segmentMonthDate) ||
                other.segmentMonthDate == segmentMonthDate) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.newGetFlag, newGetFlag) ||
                other.newGetFlag == newGetFlag) &&
            (identical(other.newGetClassId, newGetClassId) ||
                other.newGetClassId == newGetClassId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectType,
      image,
      lock,
      buy,
      buyType,
      buyImage,
      buyRoute,
      segmentId,
      segmentMonthDate,
      courseSegmentName,
      newGetFlag,
      newGetClassId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_YearCourseImageCopyWith<_$_YearCourseImage> get copyWith =>
      __$$_YearCourseImageCopyWithImpl<_$_YearCourseImage>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_YearCourseImageToJson(
      this,
    );
  }
}

abstract class _YearCourseImage implements YearCourseImage {
  const factory _YearCourseImage(
      {final int? subjectType,
      final String? image,
      final bool? lock,
      final bool? buy,
      final String? buyType,
      final String? buyImage,
      final String? buyRoute,
      final int? segmentId,
      final String? segmentMonthDate,
      final String? courseSegmentName,
      final bool? newGetFlag,
      final int? newGetClassId}) = _$_YearCourseImage;

  factory _YearCourseImage.fromJson(Map<String, dynamic> json) =
      _$_YearCourseImage.fromJson;

  @override
  int? get subjectType;
  @override
  String? get image;
  @override
  bool? get lock;
  @override
  bool? get buy;
  @override
  String? get buyType;
  @override
  String? get buyImage;
  @override
  String? get buyRoute;
  @override
  int? get segmentId;
  @override
  String? get segmentMonthDate;
  @override
  String? get courseSegmentName;
  @override
  bool? get newGetFlag;
  @override
  int? get newGetClassId;
  @override
  @JsonKey(ignore: true)
  _$$_YearCourseImageCopyWith<_$_YearCourseImage> get copyWith =>
      throw _privateConstructorUsedError;
}
