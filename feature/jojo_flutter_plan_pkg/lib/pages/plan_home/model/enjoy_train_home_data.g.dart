// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enjoy_train_home_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_EnjoyTrainHomeData _$$_EnjoyTrainHomeDataFromJson(
        Map<String, dynamic> json) =>
    _$_EnjoyTrainHomeData(
      subjectTabList: (json['subjectTabList'] as List<dynamic>?)
          ?.map((e) => SubjectTypeVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseScheduCardList: (json['courseScheduCardList'] as List<dynamic>?)
          ?.map((e) => ScheduleInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      takeCourse: json['takeCourse'] as int?,
      pureEnjoyTrain: json['pureEnjoyTrain'] as int?,
      contactRoute: json['contactRoute'] as String?,
      schedulePopupList: (json['schedulePopupList'] as List<dynamic>?)
          ?.map((e) => SchedulePopup.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseGuideList: (json['courseGuideList'] as List<dynamic>?)
          ?.map((e) => TrainCampTipData.fromJson(e as Map<String, dynamic>))
          .toList(),
      multipleClassTrialDataList:
          (json['multipleClassTrialDataList'] as List<dynamic>?)
              ?.map((e) =>
                  PlanAutoTransformPageData.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_EnjoyTrainHomeDataToJson(
        _$_EnjoyTrainHomeData instance) =>
    <String, dynamic>{
      'subjectTabList': instance.subjectTabList,
      'courseScheduCardList': instance.courseScheduCardList,
      'takeCourse': instance.takeCourse,
      'pureEnjoyTrain': instance.pureEnjoyTrain,
      'contactRoute': instance.contactRoute,
      'schedulePopupList': instance.schedulePopupList,
      'courseGuideList': instance.courseGuideList,
      'multipleClassTrialDataList': instance.multipleClassTrialDataList,
    };
