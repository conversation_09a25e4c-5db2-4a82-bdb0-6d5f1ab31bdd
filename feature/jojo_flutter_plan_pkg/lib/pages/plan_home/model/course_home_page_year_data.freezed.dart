// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_page_year_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomePageYearData _$CourseHomePageYearDataFromJson(
    Map<String, dynamic> json) {
  return _CourseHomePageYearData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomePageYearData {
  int? get year => throw _privateConstructorUsedError;
  List<SubjectTypeVoList>? get subjectTypeVoList =>
      throw _privateConstructorUsedError;
  List<MonthScheduleList>? get monthScheduleList =>
      throw _privateConstructorUsedError;
  List<LessonSummaryList>? get lessonSummaryList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomePageYearDataCopyWith<CourseHomePageYearData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomePageYearDataCopyWith<$Res> {
  factory $CourseHomePageYearDataCopyWith(CourseHomePageYearData value,
          $Res Function(CourseHomePageYearData) then) =
      _$CourseHomePageYearDataCopyWithImpl<$Res, CourseHomePageYearData>;
  @useResult
  $Res call(
      {int? year,
      List<SubjectTypeVoList>? subjectTypeVoList,
      List<MonthScheduleList>? monthScheduleList,
      List<LessonSummaryList>? lessonSummaryList});
}

/// @nodoc
class _$CourseHomePageYearDataCopyWithImpl<$Res,
        $Val extends CourseHomePageYearData>
    implements $CourseHomePageYearDataCopyWith<$Res> {
  _$CourseHomePageYearDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? subjectTypeVoList = freezed,
    Object? monthScheduleList = freezed,
    Object? lessonSummaryList = freezed,
  }) {
    return _then(_value.copyWith(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeVoList: freezed == subjectTypeVoList
          ? _value.subjectTypeVoList
          : subjectTypeVoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVoList>?,
      monthScheduleList: freezed == monthScheduleList
          ? _value.monthScheduleList
          : monthScheduleList // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleList>?,
      lessonSummaryList: freezed == lessonSummaryList
          ? _value.lessonSummaryList
          : lessonSummaryList // ignore: cast_nullable_to_non_nullable
              as List<LessonSummaryList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseHomePageYearDataCopyWith<$Res>
    implements $CourseHomePageYearDataCopyWith<$Res> {
  factory _$$_CourseHomePageYearDataCopyWith(_$_CourseHomePageYearData value,
          $Res Function(_$_CourseHomePageYearData) then) =
      __$$_CourseHomePageYearDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? year,
      List<SubjectTypeVoList>? subjectTypeVoList,
      List<MonthScheduleList>? monthScheduleList,
      List<LessonSummaryList>? lessonSummaryList});
}

/// @nodoc
class __$$_CourseHomePageYearDataCopyWithImpl<$Res>
    extends _$CourseHomePageYearDataCopyWithImpl<$Res,
        _$_CourseHomePageYearData>
    implements _$$_CourseHomePageYearDataCopyWith<$Res> {
  __$$_CourseHomePageYearDataCopyWithImpl(_$_CourseHomePageYearData _value,
      $Res Function(_$_CourseHomePageYearData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? subjectTypeVoList = freezed,
    Object? monthScheduleList = freezed,
    Object? lessonSummaryList = freezed,
  }) {
    return _then(_$_CourseHomePageYearData(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeVoList: freezed == subjectTypeVoList
          ? _value._subjectTypeVoList
          : subjectTypeVoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVoList>?,
      monthScheduleList: freezed == monthScheduleList
          ? _value._monthScheduleList
          : monthScheduleList // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleList>?,
      lessonSummaryList: freezed == lessonSummaryList
          ? _value._lessonSummaryList
          : lessonSummaryList // ignore: cast_nullable_to_non_nullable
              as List<LessonSummaryList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomePageYearData implements _CourseHomePageYearData {
  const _$_CourseHomePageYearData(
      {this.year,
      final List<SubjectTypeVoList>? subjectTypeVoList,
      final List<MonthScheduleList>? monthScheduleList,
      final List<LessonSummaryList>? lessonSummaryList})
      : _subjectTypeVoList = subjectTypeVoList,
        _monthScheduleList = monthScheduleList,
        _lessonSummaryList = lessonSummaryList;

  factory _$_CourseHomePageYearData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseHomePageYearDataFromJson(json);

  @override
  final int? year;
  final List<SubjectTypeVoList>? _subjectTypeVoList;
  @override
  List<SubjectTypeVoList>? get subjectTypeVoList {
    final value = _subjectTypeVoList;
    if (value == null) return null;
    if (_subjectTypeVoList is EqualUnmodifiableListView)
      return _subjectTypeVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<MonthScheduleList>? _monthScheduleList;
  @override
  List<MonthScheduleList>? get monthScheduleList {
    final value = _monthScheduleList;
    if (value == null) return null;
    if (_monthScheduleList is EqualUnmodifiableListView)
      return _monthScheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<LessonSummaryList>? _lessonSummaryList;
  @override
  List<LessonSummaryList>? get lessonSummaryList {
    final value = _lessonSummaryList;
    if (value == null) return null;
    if (_lessonSummaryList is EqualUnmodifiableListView)
      return _lessonSummaryList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseHomePageYearData(year: $year, subjectTypeVoList: $subjectTypeVoList, monthScheduleList: $monthScheduleList, lessonSummaryList: $lessonSummaryList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomePageYearData &&
            (identical(other.year, year) || other.year == year) &&
            const DeepCollectionEquality()
                .equals(other._subjectTypeVoList, _subjectTypeVoList) &&
            const DeepCollectionEquality()
                .equals(other._monthScheduleList, _monthScheduleList) &&
            const DeepCollectionEquality()
                .equals(other._lessonSummaryList, _lessonSummaryList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      year,
      const DeepCollectionEquality().hash(_subjectTypeVoList),
      const DeepCollectionEquality().hash(_monthScheduleList),
      const DeepCollectionEquality().hash(_lessonSummaryList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomePageYearDataCopyWith<_$_CourseHomePageYearData> get copyWith =>
      __$$_CourseHomePageYearDataCopyWithImpl<_$_CourseHomePageYearData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomePageYearDataToJson(
      this,
    );
  }
}

abstract class _CourseHomePageYearData implements CourseHomePageYearData {
  const factory _CourseHomePageYearData(
          {final int? year,
          final List<SubjectTypeVoList>? subjectTypeVoList,
          final List<MonthScheduleList>? monthScheduleList,
          final List<LessonSummaryList>? lessonSummaryList}) =
      _$_CourseHomePageYearData;

  factory _CourseHomePageYearData.fromJson(Map<String, dynamic> json) =
      _$_CourseHomePageYearData.fromJson;

  @override
  int? get year;
  @override
  List<SubjectTypeVoList>? get subjectTypeVoList;
  @override
  List<MonthScheduleList>? get monthScheduleList;
  @override
  List<LessonSummaryList>? get lessonSummaryList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomePageYearDataCopyWith<_$_CourseHomePageYearData> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonSummaryList _$LessonSummaryListFromJson(Map<String, dynamic> json) {
  return _LessonSummaryList.fromJson(json);
}

/// @nodoc
mixin _$LessonSummaryList {
  int? get subjectType => throw _privateConstructorUsedError;
  int? get unFinishedNum => throw _privateConstructorUsedError;
  int? get finishedNum => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonSummaryListCopyWith<LessonSummaryList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonSummaryListCopyWith<$Res> {
  factory $LessonSummaryListCopyWith(
          LessonSummaryList value, $Res Function(LessonSummaryList) then) =
      _$LessonSummaryListCopyWithImpl<$Res, LessonSummaryList>;
  @useResult
  $Res call({int? subjectType, int? unFinishedNum, int? finishedNum});
}

/// @nodoc
class _$LessonSummaryListCopyWithImpl<$Res, $Val extends LessonSummaryList>
    implements $LessonSummaryListCopyWith<$Res> {
  _$LessonSummaryListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonSummaryListCopyWith<$Res>
    implements $LessonSummaryListCopyWith<$Res> {
  factory _$$_LessonSummaryListCopyWith(_$_LessonSummaryList value,
          $Res Function(_$_LessonSummaryList) then) =
      __$$_LessonSummaryListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, int? unFinishedNum, int? finishedNum});
}

/// @nodoc
class __$$_LessonSummaryListCopyWithImpl<$Res>
    extends _$LessonSummaryListCopyWithImpl<$Res, _$_LessonSummaryList>
    implements _$$_LessonSummaryListCopyWith<$Res> {
  __$$_LessonSummaryListCopyWithImpl(
      _$_LessonSummaryList _value, $Res Function(_$_LessonSummaryList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
  }) {
    return _then(_$_LessonSummaryList(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonSummaryList implements _LessonSummaryList {
  const _$_LessonSummaryList(
      {this.subjectType, this.unFinishedNum, this.finishedNum});

  factory _$_LessonSummaryList.fromJson(Map<String, dynamic> json) =>
      _$$_LessonSummaryListFromJson(json);

  @override
  final int? subjectType;
  @override
  final int? unFinishedNum;
  @override
  final int? finishedNum;

  @override
  String toString() {
    return 'LessonSummaryList(subjectType: $subjectType, unFinishedNum: $unFinishedNum, finishedNum: $finishedNum)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonSummaryList &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.unFinishedNum, unFinishedNum) ||
                other.unFinishedNum == unFinishedNum) &&
            (identical(other.finishedNum, finishedNum) ||
                other.finishedNum == finishedNum));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, subjectType, unFinishedNum, finishedNum);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonSummaryListCopyWith<_$_LessonSummaryList> get copyWith =>
      __$$_LessonSummaryListCopyWithImpl<_$_LessonSummaryList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonSummaryListToJson(
      this,
    );
  }
}

abstract class _LessonSummaryList implements LessonSummaryList {
  const factory _LessonSummaryList(
      {final int? subjectType,
      final int? unFinishedNum,
      final int? finishedNum}) = _$_LessonSummaryList;

  factory _LessonSummaryList.fromJson(Map<String, dynamic> json) =
      _$_LessonSummaryList.fromJson;

  @override
  int? get subjectType;
  @override
  int? get unFinishedNum;
  @override
  int? get finishedNum;
  @override
  @JsonKey(ignore: true)
  _$$_LessonSummaryListCopyWith<_$_LessonSummaryList> get copyWith =>
      throw _privateConstructorUsedError;
}

MonthScheduleList _$MonthScheduleListFromJson(Map<String, dynamic> json) {
  return _MonthScheduleList.fromJson(json);
}

/// @nodoc
mixin _$MonthScheduleList {
  String? get month => throw _privateConstructorUsedError;
  int? get monthNum => throw _privateConstructorUsedError;
  bool? get currentMonth => throw _privateConstructorUsedError;
  List<ScheduleList>? get scheduleList => throw _privateConstructorUsedError;
  List<SubjectImageList>? get subjectImageList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonthScheduleListCopyWith<MonthScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonthScheduleListCopyWith<$Res> {
  factory $MonthScheduleListCopyWith(
          MonthScheduleList value, $Res Function(MonthScheduleList) then) =
      _$MonthScheduleListCopyWithImpl<$Res, MonthScheduleList>;
  @useResult
  $Res call(
      {String? month,
      int? monthNum,
      bool? currentMonth,
      List<ScheduleList>? scheduleList,
      List<SubjectImageList>? subjectImageList});
}

/// @nodoc
class _$MonthScheduleListCopyWithImpl<$Res, $Val extends MonthScheduleList>
    implements $MonthScheduleListCopyWith<$Res> {
  _$MonthScheduleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? currentMonth = freezed,
    Object? scheduleList = freezed,
    Object? subjectImageList = freezed,
  }) {
    return _then(_value.copyWith(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      currentMonth: freezed == currentMonth
          ? _value.currentMonth
          : currentMonth // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleList: freezed == scheduleList
          ? _value.scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleList>?,
      subjectImageList: freezed == subjectImageList
          ? _value.subjectImageList
          : subjectImageList // ignore: cast_nullable_to_non_nullable
              as List<SubjectImageList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MonthScheduleListCopyWith<$Res>
    implements $MonthScheduleListCopyWith<$Res> {
  factory _$$_MonthScheduleListCopyWith(_$_MonthScheduleList value,
          $Res Function(_$_MonthScheduleList) then) =
      __$$_MonthScheduleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? month,
      int? monthNum,
      bool? currentMonth,
      List<ScheduleList>? scheduleList,
      List<SubjectImageList>? subjectImageList});
}

/// @nodoc
class __$$_MonthScheduleListCopyWithImpl<$Res>
    extends _$MonthScheduleListCopyWithImpl<$Res, _$_MonthScheduleList>
    implements _$$_MonthScheduleListCopyWith<$Res> {
  __$$_MonthScheduleListCopyWithImpl(
      _$_MonthScheduleList _value, $Res Function(_$_MonthScheduleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? currentMonth = freezed,
    Object? scheduleList = freezed,
    Object? subjectImageList = freezed,
  }) {
    return _then(_$_MonthScheduleList(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      currentMonth: freezed == currentMonth
          ? _value.currentMonth
          : currentMonth // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleList: freezed == scheduleList
          ? _value._scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleList>?,
      subjectImageList: freezed == subjectImageList
          ? _value._subjectImageList
          : subjectImageList // ignore: cast_nullable_to_non_nullable
              as List<SubjectImageList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MonthScheduleList implements _MonthScheduleList {
  const _$_MonthScheduleList(
      {this.month,
      this.monthNum,
      this.currentMonth,
      final List<ScheduleList>? scheduleList,
      final List<SubjectImageList>? subjectImageList})
      : _scheduleList = scheduleList,
        _subjectImageList = subjectImageList;

  factory _$_MonthScheduleList.fromJson(Map<String, dynamic> json) =>
      _$$_MonthScheduleListFromJson(json);

  @override
  final String? month;
  @override
  final int? monthNum;
  @override
  final bool? currentMonth;
  final List<ScheduleList>? _scheduleList;
  @override
  List<ScheduleList>? get scheduleList {
    final value = _scheduleList;
    if (value == null) return null;
    if (_scheduleList is EqualUnmodifiableListView) return _scheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SubjectImageList>? _subjectImageList;
  @override
  List<SubjectImageList>? get subjectImageList {
    final value = _subjectImageList;
    if (value == null) return null;
    if (_subjectImageList is EqualUnmodifiableListView)
      return _subjectImageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MonthScheduleList(month: $month, monthNum: $monthNum, currentMonth: $currentMonth, scheduleList: $scheduleList, subjectImageList: $subjectImageList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MonthScheduleList &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.monthNum, monthNum) ||
                other.monthNum == monthNum) &&
            (identical(other.currentMonth, currentMonth) ||
                other.currentMonth == currentMonth) &&
            const DeepCollectionEquality()
                .equals(other._scheduleList, _scheduleList) &&
            const DeepCollectionEquality()
                .equals(other._subjectImageList, _subjectImageList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      month,
      monthNum,
      currentMonth,
      const DeepCollectionEquality().hash(_scheduleList),
      const DeepCollectionEquality().hash(_subjectImageList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MonthScheduleListCopyWith<_$_MonthScheduleList> get copyWith =>
      __$$_MonthScheduleListCopyWithImpl<_$_MonthScheduleList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MonthScheduleListToJson(
      this,
    );
  }
}

abstract class _MonthScheduleList implements MonthScheduleList {
  const factory _MonthScheduleList(
      {final String? month,
      final int? monthNum,
      final bool? currentMonth,
      final List<ScheduleList>? scheduleList,
      final List<SubjectImageList>? subjectImageList}) = _$_MonthScheduleList;

  factory _MonthScheduleList.fromJson(Map<String, dynamic> json) =
      _$_MonthScheduleList.fromJson;

  @override
  String? get month;
  @override
  int? get monthNum;
  @override
  bool? get currentMonth;
  @override
  List<ScheduleList>? get scheduleList;
  @override
  List<SubjectImageList>? get subjectImageList;
  @override
  @JsonKey(ignore: true)
  _$$_MonthScheduleListCopyWith<_$_MonthScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectImageList _$SubjectImageListFromJson(Map<String, dynamic> json) {
  return _SubjectImageList.fromJson(json);
}

/// @nodoc
mixin _$SubjectImageList {
  int? get subjectType => throw _privateConstructorUsedError;
  bool? get lock => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  bool? get buy => throw _privateConstructorUsedError; //是否购买 true:已购买 false:未购买
  bool? get subscribe =>
      throw _privateConstructorUsedError; //是否订阅 true:订阅 false:否
  int? get buyType => throw _privateConstructorUsedError;
  String? get buyImage => throw _privateConstructorUsedError; //购买展示图片
  String? get buyRoute => throw _privateConstructorUsedError; //购买路由
  String? get courseSegmentName => throw _privateConstructorUsedError; // 阶段名称
  int? get segmentId => throw _privateConstructorUsedError; //主题月id
  int? get segmentFirstStartTime =>
      throw _privateConstructorUsedError; //主题月第一课时开课时间
  bool? get newGetFlag => throw _privateConstructorUsedError; //是否是新获得
  int? get newGetClassId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectImageListCopyWith<SubjectImageList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectImageListCopyWith<$Res> {
  factory $SubjectImageListCopyWith(
          SubjectImageList value, $Res Function(SubjectImageList) then) =
      _$SubjectImageListCopyWithImpl<$Res, SubjectImageList>;
  @useResult
  $Res call(
      {int? subjectType,
      bool? lock,
      String? image,
      bool? buy,
      bool? subscribe,
      int? buyType,
      String? buyImage,
      String? buyRoute,
      String? courseSegmentName,
      int? segmentId,
      int? segmentFirstStartTime,
      bool? newGetFlag,
      int? newGetClassId});
}

/// @nodoc
class _$SubjectImageListCopyWithImpl<$Res, $Val extends SubjectImageList>
    implements $SubjectImageListCopyWith<$Res> {
  _$SubjectImageListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? lock = freezed,
    Object? image = freezed,
    Object? buy = freezed,
    Object? subscribe = freezed,
    Object? buyType = freezed,
    Object? buyImage = freezed,
    Object? buyRoute = freezed,
    Object? courseSegmentName = freezed,
    Object? segmentId = freezed,
    Object? segmentFirstStartTime = freezed,
    Object? newGetFlag = freezed,
    Object? newGetClassId = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      buy: freezed == buy
          ? _value.buy
          : buy // ignore: cast_nullable_to_non_nullable
              as bool?,
      subscribe: freezed == subscribe
          ? _value.subscribe
          : subscribe // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyType: freezed == buyType
          ? _value.buyType
          : buyType // ignore: cast_nullable_to_non_nullable
              as int?,
      buyImage: freezed == buyImage
          ? _value.buyImage
          : buyImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRoute: freezed == buyRoute
          ? _value.buyRoute
          : buyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentFirstStartTime: freezed == segmentFirstStartTime
          ? _value.segmentFirstStartTime
          : segmentFirstStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetClassId: freezed == newGetClassId
          ? _value.newGetClassId
          : newGetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectImageListCopyWith<$Res>
    implements $SubjectImageListCopyWith<$Res> {
  factory _$$_SubjectImageListCopyWith(
          _$_SubjectImageList value, $Res Function(_$_SubjectImageList) then) =
      __$$_SubjectImageListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      bool? lock,
      String? image,
      bool? buy,
      bool? subscribe,
      int? buyType,
      String? buyImage,
      String? buyRoute,
      String? courseSegmentName,
      int? segmentId,
      int? segmentFirstStartTime,
      bool? newGetFlag,
      int? newGetClassId});
}

/// @nodoc
class __$$_SubjectImageListCopyWithImpl<$Res>
    extends _$SubjectImageListCopyWithImpl<$Res, _$_SubjectImageList>
    implements _$$_SubjectImageListCopyWith<$Res> {
  __$$_SubjectImageListCopyWithImpl(
      _$_SubjectImageList _value, $Res Function(_$_SubjectImageList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? lock = freezed,
    Object? image = freezed,
    Object? buy = freezed,
    Object? subscribe = freezed,
    Object? buyType = freezed,
    Object? buyImage = freezed,
    Object? buyRoute = freezed,
    Object? courseSegmentName = freezed,
    Object? segmentId = freezed,
    Object? segmentFirstStartTime = freezed,
    Object? newGetFlag = freezed,
    Object? newGetClassId = freezed,
  }) {
    return _then(_$_SubjectImageList(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      lock: freezed == lock
          ? _value.lock
          : lock // ignore: cast_nullable_to_non_nullable
              as bool?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      buy: freezed == buy
          ? _value.buy
          : buy // ignore: cast_nullable_to_non_nullable
              as bool?,
      subscribe: freezed == subscribe
          ? _value.subscribe
          : subscribe // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyType: freezed == buyType
          ? _value.buyType
          : buyType // ignore: cast_nullable_to_non_nullable
              as int?,
      buyImage: freezed == buyImage
          ? _value.buyImage
          : buyImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRoute: freezed == buyRoute
          ? _value.buyRoute
          : buyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentFirstStartTime: freezed == segmentFirstStartTime
          ? _value.segmentFirstStartTime
          : segmentFirstStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetClassId: freezed == newGetClassId
          ? _value.newGetClassId
          : newGetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectImageList implements _SubjectImageList {
  const _$_SubjectImageList(
      {this.subjectType,
      this.lock,
      this.image,
      this.buy,
      this.subscribe,
      this.buyType,
      this.buyImage,
      this.buyRoute,
      this.courseSegmentName,
      this.segmentId,
      this.segmentFirstStartTime,
      this.newGetFlag,
      this.newGetClassId});

  factory _$_SubjectImageList.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectImageListFromJson(json);

  @override
  final int? subjectType;
  @override
  final bool? lock;
  @override
  final String? image;
  @override
  final bool? buy;
//是否购买 true:已购买 false:未购买
  @override
  final bool? subscribe;
//是否订阅 true:订阅 false:否
  @override
  final int? buyType;
  @override
  final String? buyImage;
//购买展示图片
  @override
  final String? buyRoute;
//购买路由
  @override
  final String? courseSegmentName;
// 阶段名称
  @override
  final int? segmentId;
//主题月id
  @override
  final int? segmentFirstStartTime;
//主题月第一课时开课时间
  @override
  final bool? newGetFlag;
//是否是新获得
  @override
  final int? newGetClassId;

  @override
  String toString() {
    return 'SubjectImageList(subjectType: $subjectType, lock: $lock, image: $image, buy: $buy, subscribe: $subscribe, buyType: $buyType, buyImage: $buyImage, buyRoute: $buyRoute, courseSegmentName: $courseSegmentName, segmentId: $segmentId, segmentFirstStartTime: $segmentFirstStartTime, newGetFlag: $newGetFlag, newGetClassId: $newGetClassId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectImageList &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.lock, lock) || other.lock == lock) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.buy, buy) || other.buy == buy) &&
            (identical(other.subscribe, subscribe) ||
                other.subscribe == subscribe) &&
            (identical(other.buyType, buyType) || other.buyType == buyType) &&
            (identical(other.buyImage, buyImage) ||
                other.buyImage == buyImage) &&
            (identical(other.buyRoute, buyRoute) ||
                other.buyRoute == buyRoute) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.segmentFirstStartTime, segmentFirstStartTime) ||
                other.segmentFirstStartTime == segmentFirstStartTime) &&
            (identical(other.newGetFlag, newGetFlag) ||
                other.newGetFlag == newGetFlag) &&
            (identical(other.newGetClassId, newGetClassId) ||
                other.newGetClassId == newGetClassId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectType,
      lock,
      image,
      buy,
      subscribe,
      buyType,
      buyImage,
      buyRoute,
      courseSegmentName,
      segmentId,
      segmentFirstStartTime,
      newGetFlag,
      newGetClassId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectImageListCopyWith<_$_SubjectImageList> get copyWith =>
      __$$_SubjectImageListCopyWithImpl<_$_SubjectImageList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectImageListToJson(
      this,
    );
  }
}

abstract class _SubjectImageList implements SubjectImageList {
  const factory _SubjectImageList(
      {final int? subjectType,
      final bool? lock,
      final String? image,
      final bool? buy,
      final bool? subscribe,
      final int? buyType,
      final String? buyImage,
      final String? buyRoute,
      final String? courseSegmentName,
      final int? segmentId,
      final int? segmentFirstStartTime,
      final bool? newGetFlag,
      final int? newGetClassId}) = _$_SubjectImageList;

  factory _SubjectImageList.fromJson(Map<String, dynamic> json) =
      _$_SubjectImageList.fromJson;

  @override
  int? get subjectType;
  @override
  bool? get lock;
  @override
  String? get image;
  @override
  bool? get buy;
  @override //是否购买 true:已购买 false:未购买
  bool? get subscribe;
  @override //是否订阅 true:订阅 false:否
  int? get buyType;
  @override
  String? get buyImage;
  @override //购买展示图片
  String? get buyRoute;
  @override //购买路由
  String? get courseSegmentName;
  @override // 阶段名称
  int? get segmentId;
  @override //主题月id
  int? get segmentFirstStartTime;
  @override //主题月第一课时开课时间
  bool? get newGetFlag;
  @override //是否是新获得
  int? get newGetClassId;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectImageListCopyWith<_$_SubjectImageList> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleList _$ScheduleListFromJson(Map<String, dynamic> json) {
  return _ScheduleList.fromJson(json);
}

/// @nodoc
mixin _$ScheduleList {
  List<SubjectScheduleStatus>? get subjectScheduleStatus =>
      throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get scheduleTaskList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleListCopyWith<ScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleListCopyWith<$Res> {
  factory $ScheduleListCopyWith(
          ScheduleList value, $Res Function(ScheduleList) then) =
      _$ScheduleListCopyWithImpl<$Res, ScheduleList>;
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      bool? today,
      List<Map<String, dynamic>>? scheduleTaskList});
}

/// @nodoc
class _$ScheduleListCopyWithImpl<$Res, $Val extends ScheduleList>
    implements $ScheduleListCopyWith<$Res> {
  _$ScheduleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? today = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value.subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value.scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleListCopyWith<$Res>
    implements $ScheduleListCopyWith<$Res> {
  factory _$$_ScheduleListCopyWith(
          _$_ScheduleList value, $Res Function(_$_ScheduleList) then) =
      __$$_ScheduleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      bool? today,
      List<Map<String, dynamic>>? scheduleTaskList});
}

/// @nodoc
class __$$_ScheduleListCopyWithImpl<$Res>
    extends _$ScheduleListCopyWithImpl<$Res, _$_ScheduleList>
    implements _$$_ScheduleListCopyWith<$Res> {
  __$$_ScheduleListCopyWithImpl(
      _$_ScheduleList _value, $Res Function(_$_ScheduleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? today = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_$_ScheduleList(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value._subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value._scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleList implements _ScheduleList {
  const _$_ScheduleList(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      this.showDateTime,
      this.today,
      final List<Map<String, dynamic>>? scheduleTaskList})
      : _subjectScheduleStatus = subjectScheduleStatus,
        _scheduleTaskList = scheduleTaskList;

  factory _$_ScheduleList.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleListFromJson(json);

  final List<SubjectScheduleStatus>? _subjectScheduleStatus;
  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus {
    final value = _subjectScheduleStatus;
    if (value == null) return null;
    if (_subjectScheduleStatus is EqualUnmodifiableListView)
      return _subjectScheduleStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? showDateTime;
  @override
  final bool? today;
  final List<Map<String, dynamic>>? _scheduleTaskList;
  @override
  List<Map<String, dynamic>>? get scheduleTaskList {
    final value = _scheduleTaskList;
    if (value == null) return null;
    if (_scheduleTaskList is EqualUnmodifiableListView)
      return _scheduleTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ScheduleList(subjectScheduleStatus: $subjectScheduleStatus, showDateTime: $showDateTime, today: $today, scheduleTaskList: $scheduleTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleList &&
            const DeepCollectionEquality()
                .equals(other._subjectScheduleStatus, _subjectScheduleStatus) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.today, today) || other.today == today) &&
            const DeepCollectionEquality()
                .equals(other._scheduleTaskList, _scheduleTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectScheduleStatus),
      showDateTime,
      today,
      const DeepCollectionEquality().hash(_scheduleTaskList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleListCopyWith<_$_ScheduleList> get copyWith =>
      __$$_ScheduleListCopyWithImpl<_$_ScheduleList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleListToJson(
      this,
    );
  }
}

abstract class _ScheduleList implements ScheduleList {
  const factory _ScheduleList(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      final int? showDateTime,
      final bool? today,
      final List<Map<String, dynamic>>? scheduleTaskList}) = _$_ScheduleList;

  factory _ScheduleList.fromJson(Map<String, dynamic> json) =
      _$_ScheduleList.fromJson;

  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus;
  @override
  int? get showDateTime;
  @override
  bool? get today;
  @override
  List<Map<String, dynamic>>? get scheduleTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleListCopyWith<_$_ScheduleList> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectScheduleStatus _$SubjectScheduleStatusFromJson(
    Map<String, dynamic> json) {
  return _SubjectScheduleStatus.fromJson(json);
}

/// @nodoc
mixin _$SubjectScheduleStatus {
  int? get subjectType => throw _privateConstructorUsedError;
  int? get scheduleStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectScheduleStatusCopyWith<SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectScheduleStatusCopyWith<$Res> {
  factory $SubjectScheduleStatusCopyWith(SubjectScheduleStatus value,
          $Res Function(SubjectScheduleStatus) then) =
      _$SubjectScheduleStatusCopyWithImpl<$Res, SubjectScheduleStatus>;
  @useResult
  $Res call({int? subjectType, int? scheduleStatus});
}

/// @nodoc
class _$SubjectScheduleStatusCopyWithImpl<$Res,
        $Val extends SubjectScheduleStatus>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  _$SubjectScheduleStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectScheduleStatusCopyWith<$Res>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  factory _$$_SubjectScheduleStatusCopyWith(_$_SubjectScheduleStatus value,
          $Res Function(_$_SubjectScheduleStatus) then) =
      __$$_SubjectScheduleStatusCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, int? scheduleStatus});
}

/// @nodoc
class __$$_SubjectScheduleStatusCopyWithImpl<$Res>
    extends _$SubjectScheduleStatusCopyWithImpl<$Res, _$_SubjectScheduleStatus>
    implements _$$_SubjectScheduleStatusCopyWith<$Res> {
  __$$_SubjectScheduleStatusCopyWithImpl(_$_SubjectScheduleStatus _value,
      $Res Function(_$_SubjectScheduleStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
  }) {
    return _then(_$_SubjectScheduleStatus(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectScheduleStatus implements _SubjectScheduleStatus {
  const _$_SubjectScheduleStatus({this.subjectType, this.scheduleStatus});

  factory _$_SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectScheduleStatusFromJson(json);

  @override
  final int? subjectType;
  @override
  final int? scheduleStatus;

  @override
  String toString() {
    return 'SubjectScheduleStatus(subjectType: $subjectType, scheduleStatus: $scheduleStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectScheduleStatus &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.scheduleStatus, scheduleStatus) ||
                other.scheduleStatus == scheduleStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, scheduleStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      __$$_SubjectScheduleStatusCopyWithImpl<_$_SubjectScheduleStatus>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectScheduleStatusToJson(
      this,
    );
  }
}

abstract class _SubjectScheduleStatus implements SubjectScheduleStatus {
  const factory _SubjectScheduleStatus(
      {final int? subjectType,
      final int? scheduleStatus}) = _$_SubjectScheduleStatus;

  factory _SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =
      _$_SubjectScheduleStatus.fromJson;

  @override
  int? get subjectType;
  @override
  int? get scheduleStatus;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectTypeVoList _$SubjectTypeVoListFromJson(Map<String, dynamic> json) {
  return _SubjectTypeVoList.fromJson(json);
}

/// @nodoc
mixin _$SubjectTypeVoList {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectTypeVoListCopyWith<SubjectTypeVoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectTypeVoListCopyWith<$Res> {
  factory $SubjectTypeVoListCopyWith(
          SubjectTypeVoList value, $Res Function(SubjectTypeVoList) then) =
      _$SubjectTypeVoListCopyWithImpl<$Res, SubjectTypeVoList>;
  @useResult
  $Res call({int? subjectType, String? subjectTypeDesc});
}

/// @nodoc
class _$SubjectTypeVoListCopyWithImpl<$Res, $Val extends SubjectTypeVoList>
    implements $SubjectTypeVoListCopyWith<$Res> {
  _$SubjectTypeVoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectTypeVoListCopyWith<$Res>
    implements $SubjectTypeVoListCopyWith<$Res> {
  factory _$$_SubjectTypeVoListCopyWith(_$_SubjectTypeVoList value,
          $Res Function(_$_SubjectTypeVoList) then) =
      __$$_SubjectTypeVoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, String? subjectTypeDesc});
}

/// @nodoc
class __$$_SubjectTypeVoListCopyWithImpl<$Res>
    extends _$SubjectTypeVoListCopyWithImpl<$Res, _$_SubjectTypeVoList>
    implements _$$_SubjectTypeVoListCopyWith<$Res> {
  __$$_SubjectTypeVoListCopyWithImpl(
      _$_SubjectTypeVoList _value, $Res Function(_$_SubjectTypeVoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
  }) {
    return _then(_$_SubjectTypeVoList(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectTypeVoList implements _SubjectTypeVoList {
  const _$_SubjectTypeVoList({this.subjectType, this.subjectTypeDesc});

  factory _$_SubjectTypeVoList.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectTypeVoListFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectTypeDesc;

  @override
  String toString() {
    return 'SubjectTypeVoList(subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectTypeVoList &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, subjectTypeDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectTypeVoListCopyWith<_$_SubjectTypeVoList> get copyWith =>
      __$$_SubjectTypeVoListCopyWithImpl<_$_SubjectTypeVoList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectTypeVoListToJson(
      this,
    );
  }
}

abstract class _SubjectTypeVoList implements SubjectTypeVoList {
  const factory _SubjectTypeVoList(
      {final int? subjectType,
      final String? subjectTypeDesc}) = _$_SubjectTypeVoList;

  factory _SubjectTypeVoList.fromJson(Map<String, dynamic> json) =
      _$_SubjectTypeVoList.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectTypeVoListCopyWith<_$_SubjectTypeVoList> get copyWith =>
      throw _privateConstructorUsedError;
}
