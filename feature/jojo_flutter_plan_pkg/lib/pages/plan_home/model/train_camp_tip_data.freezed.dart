// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'train_camp_tip_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TrainCampTipData _$TrainCampTipDataFromJson(Map<String, dynamic> json) {
  return _TrainCampTipData.fromJson(json);
}

/// @nodoc
mixin _$TrainCampTipData {
  int? get guideType =>
      throw _privateConstructorUsedError; // 引导类型。1-科目获得，2-课程收纳
  int? get order => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get voice => throw _privateConstructorUsedError;
  int? get displayDuration => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TrainCampTipDataCopyWith<TrainCampTipData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrainCampTipDataCopyWith<$Res> {
  factory $TrainCampTipDataCopyWith(
          TrainCampTipData value, $Res Function(TrainCampTipData) then) =
      _$TrainCampTipDataCopyWithImpl<$Res, TrainCampTipData>;
  @useResult
  $Res call(
      {int? guideType,
      int? order,
      String? content,
      String? voice,
      int? displayDuration,
      int? subjectType});
}

/// @nodoc
class _$TrainCampTipDataCopyWithImpl<$Res, $Val extends TrainCampTipData>
    implements $TrainCampTipDataCopyWith<$Res> {
  _$TrainCampTipDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guideType = freezed,
    Object? order = freezed,
    Object? content = freezed,
    Object? voice = freezed,
    Object? displayDuration = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_value.copyWith(
      guideType: freezed == guideType
          ? _value.guideType
          : guideType // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      displayDuration: freezed == displayDuration
          ? _value.displayDuration
          : displayDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TrainCampTipDataCopyWith<$Res>
    implements $TrainCampTipDataCopyWith<$Res> {
  factory _$$_TrainCampTipDataCopyWith(
          _$_TrainCampTipData value, $Res Function(_$_TrainCampTipData) then) =
      __$$_TrainCampTipDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? guideType,
      int? order,
      String? content,
      String? voice,
      int? displayDuration,
      int? subjectType});
}

/// @nodoc
class __$$_TrainCampTipDataCopyWithImpl<$Res>
    extends _$TrainCampTipDataCopyWithImpl<$Res, _$_TrainCampTipData>
    implements _$$_TrainCampTipDataCopyWith<$Res> {
  __$$_TrainCampTipDataCopyWithImpl(
      _$_TrainCampTipData _value, $Res Function(_$_TrainCampTipData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guideType = freezed,
    Object? order = freezed,
    Object? content = freezed,
    Object? voice = freezed,
    Object? displayDuration = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_$_TrainCampTipData(
      guideType: freezed == guideType
          ? _value.guideType
          : guideType // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      displayDuration: freezed == displayDuration
          ? _value.displayDuration
          : displayDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TrainCampTipData implements _TrainCampTipData {
  const _$_TrainCampTipData(
      {this.guideType,
      this.order,
      this.content,
      this.voice,
      this.displayDuration,
      this.subjectType});

  factory _$_TrainCampTipData.fromJson(Map<String, dynamic> json) =>
      _$$_TrainCampTipDataFromJson(json);

  @override
  final int? guideType;
// 引导类型。1-科目获得，2-课程收纳
  @override
  final int? order;
  @override
  final String? content;
  @override
  final String? voice;
  @override
  final int? displayDuration;
  @override
  final int? subjectType;

  @override
  String toString() {
    return 'TrainCampTipData(guideType: $guideType, order: $order, content: $content, voice: $voice, displayDuration: $displayDuration, subjectType: $subjectType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TrainCampTipData &&
            (identical(other.guideType, guideType) ||
                other.guideType == guideType) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.voice, voice) || other.voice == voice) &&
            (identical(other.displayDuration, displayDuration) ||
                other.displayDuration == displayDuration) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, guideType, order, content, voice,
      displayDuration, subjectType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TrainCampTipDataCopyWith<_$_TrainCampTipData> get copyWith =>
      __$$_TrainCampTipDataCopyWithImpl<_$_TrainCampTipData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TrainCampTipDataToJson(
      this,
    );
  }
}

abstract class _TrainCampTipData implements TrainCampTipData {
  const factory _TrainCampTipData(
      {final int? guideType,
      final int? order,
      final String? content,
      final String? voice,
      final int? displayDuration,
      final int? subjectType}) = _$_TrainCampTipData;

  factory _TrainCampTipData.fromJson(Map<String, dynamic> json) =
      _$_TrainCampTipData.fromJson;

  @override
  int? get guideType;
  @override // 引导类型。1-科目获得，2-课程收纳
  int? get order;
  @override
  String? get content;
  @override
  String? get voice;
  @override
  int? get displayDuration;
  @override
  int? get subjectType;
  @override
  @JsonKey(ignore: true)
  _$$_TrainCampTipDataCopyWith<_$_TrainCampTipData> get copyWith =>
      throw _privateConstructorUsedError;
}
