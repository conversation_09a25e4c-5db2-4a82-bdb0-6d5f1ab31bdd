// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_card_local.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LessonCardData _$$_LessonCardDataFromJson(Map<String, dynamic> json) =>
    _$_LessonCardData(
      courseKey: json['courseKey'] as String?,
      courseType: json['courseType'] as int?,
      icon: json['icon'] as String?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      bgImage: json['bgImage'] as String?,
      buttonText: json['buttonText'] as String?,
      unactivatedReason: json['unactivatedReason'] as String?,
      jumpRouter: json['jumpRouter'] as String?,
      bgColor: json['bgColor'] as String?,
      newClassIcon: json['newClassIcon'] as String?,
      statusIcon: json['statusIcon'] as String?,
      taskStatus: json['taskStatus'] as int?,
      reportPointed: json['reportPointed'] as bool?,
      newGetFlag: json['newGetFlag'] as bool?,
      subjectType: json['subjectType'] as int?,
      showDateTime: json['showDateTime'] as int?,
      lessonId: json['lessonId'] as int?,
      classId: json['classId'] as int?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      unlockType: json['unlockType'] as int?,
      videoInfo: json['videoInfo'] == null
          ? null
          : VideoInfo.fromJson(json['videoInfo'] as Map<String, dynamic>),
      finishInfo: json['finishInfo'] == null
          ? null
          : FinishInfo.fromJson(json['finishInfo'] as Map<String, dynamic>),
      newGetInfo: json['newGetInfo'] == null
          ? null
          : NewGetInfo.fromJson(json['newGetInfo'] as Map<String, dynamic>),
      sensorData: json['sensorData'] == null
          ? null
          : SensorData.fromJson(json['sensorData'] as Map<String, dynamic>),
      trialCourse: json['trialCourse'] as bool?,
      lessonServiceList: (json['lessonServiceList'] as List<dynamic>?)
          ?.map((e) => LessonServiceItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      startClassTime: json['startClassTime'] as int?,
      vipMessage: json['vipMessage'] as String?,
      vipJumpLink: json['vipJumpLink'] as String?,
      clientJumpUrl: json['clientJumpUrl'] as String?,
      clientCoverUrl: json['clientCoverUrl'] as String?,
      activateStatus: json['activateStatus'] as int?,
    );

Map<String, dynamic> _$$_LessonCardDataToJson(_$_LessonCardData instance) =>
    <String, dynamic>{
      'courseKey': instance.courseKey,
      'courseType': instance.courseType,
      'icon': instance.icon,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'bgImage': instance.bgImage,
      'buttonText': instance.buttonText,
      'unactivatedReason': instance.unactivatedReason,
      'jumpRouter': instance.jumpRouter,
      'bgColor': instance.bgColor,
      'newClassIcon': instance.newClassIcon,
      'statusIcon': instance.statusIcon,
      'taskStatus': instance.taskStatus,
      'reportPointed': instance.reportPointed,
      'newGetFlag': instance.newGetFlag,
      'subjectType': instance.subjectType,
      'showDateTime': instance.showDateTime,
      'lessonId': instance.lessonId,
      'classId': instance.classId,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'unlockType': instance.unlockType,
      'videoInfo': instance.videoInfo,
      'finishInfo': instance.finishInfo,
      'newGetInfo': instance.newGetInfo,
      'sensorData': instance.sensorData,
      'trialCourse': instance.trialCourse,
      'lessonServiceList': instance.lessonServiceList,
      'startClassTime': instance.startClassTime,
      'vipMessage': instance.vipMessage,
      'vipJumpLink': instance.vipJumpLink,
      'clientJumpUrl': instance.clientJumpUrl,
      'clientCoverUrl': instance.clientCoverUrl,
      'activateStatus': instance.activateStatus,
    };

_$_LessonServiceItem _$$_LessonServiceItemFromJson(Map<String, dynamic> json) =>
    _$_LessonServiceItem(
      key: json['key'] as String?,
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      desc: json['desc'] as String?,
      button: json['button'] as String?,
      redPoint: json['redPoint'] as bool?,
      route: json['route'] as String?,
      userCourseId: json['userCourseId'] as String?,
      parentVerify: json['parentVerify'] as int?,
      serviceExtra: json['serviceExtra'] == null
          ? null
          : CourseHomeTaskExtensionData.fromJson(
              json['serviceExtra'] as Map<String, dynamic>),
      toast: json['toast'] as String?,
      popupInfo: json['popupInfo'] == null
          ? null
          : PopInfo.fromJson(json['popupInfo'] as Map<String, dynamic>),
      userGifCourseList: (json['userGifCourseList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : UserGifCourse.fromJson(e as Map<String, dynamic>))
          .toList(),
      userGifLessonCardListData:
          (json['userGifLessonCardListData'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : LessonCardData.fromJson(e as Map<String, dynamic>))
              .toList(),
      sensorData: json['sensorData'] == null
          ? null
          : ServiceItemSensorData.fromJson(
              json['sensorData'] as Map<String, dynamic>),
      gifIcon: json['gifIcon'] as String?,
      playTime: json['playTime'] as int?,
      gifIconName: json['gifIconName'] as String?,
    );

Map<String, dynamic> _$$_LessonServiceItemToJson(
        _$_LessonServiceItem instance) =>
    <String, dynamic>{
      'key': instance.key,
      'icon': instance.icon,
      'name': instance.name,
      'desc': instance.desc,
      'button': instance.button,
      'redPoint': instance.redPoint,
      'route': instance.route,
      'userCourseId': instance.userCourseId,
      'parentVerify': instance.parentVerify,
      'serviceExtra': instance.serviceExtra,
      'toast': instance.toast,
      'popupInfo': instance.popupInfo,
      'userGifCourseList': instance.userGifCourseList,
      'userGifLessonCardListData': instance.userGifLessonCardListData,
      'sensorData': instance.sensorData,
      'gifIcon': instance.gifIcon,
      'playTime': instance.playTime,
      'gifIconName': instance.gifIconName,
    };

_$_SensorData _$$_SensorDataFromJson(Map<String, dynamic> json) =>
    _$_SensorData(
      pageName: json['pageName'] as String?,
      cardType: json['cardType'] as String?,
      elementType: json['elementType'] as String?,
      courseType: json['courseType'] as String?,
      courseStage: json['courseStage'] as String?,
      customState: json['customState'] as String?,
      businessType: json['businessType'] as String?,
      classId: json['classId'] as int?,
      trackExtendInfoList: (json['trackExtendInfoList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$_SensorDataToJson(_$_SensorData instance) =>
    <String, dynamic>{
      'pageName': instance.pageName,
      'cardType': instance.cardType,
      'elementType': instance.elementType,
      'courseType': instance.courseType,
      'courseStage': instance.courseStage,
      'customState': instance.customState,
      'businessType': instance.businessType,
      'classId': instance.classId,
      'trackExtendInfoList': instance.trackExtendInfoList,
    };

_$_ServiceItemSensorData _$$_ServiceItemSensorDataFromJson(
        Map<String, dynamic> json) =>
    _$_ServiceItemSensorData(
      pageName: json['pageName'] as String?,
      serviceType: json['serviceType'] as String?,
      courseType: json['courseType'] as String?,
      elementType: json['elementType'] as String?,
      courseStage: json['courseStage'] as String?,
      customState: json['customState'] as String?,
      businessType: json['businessType'] as String?,
      materialId: json['materialId'] as String?,
      materialName: json['materialName'] as String?,
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_ServiceItemSensorDataToJson(
        _$_ServiceItemSensorData instance) =>
    <String, dynamic>{
      'pageName': instance.pageName,
      'serviceType': instance.serviceType,
      'courseType': instance.courseType,
      'elementType': instance.elementType,
      'courseStage': instance.courseStage,
      'customState': instance.customState,
      'businessType': instance.businessType,
      'materialId': instance.materialId,
      'materialName': instance.materialName,
      'classId': instance.classId,
    };

_$_NewGetInfo _$$_NewGetInfoFromJson(Map<String, dynamic> json) =>
    _$_NewGetInfo(
      newGetFlag: json['newGetFlag'] as bool?,
      newGetGuideVoice: json['newGetGuideVoice'] as String?,
      newGetGuideIcon: json['newGetGuideIcon'] as String?,
      content: json['content'] as String?,
    );

Map<String, dynamic> _$$_NewGetInfoToJson(_$_NewGetInfo instance) =>
    <String, dynamic>{
      'newGetFlag': instance.newGetFlag,
      'newGetGuideVoice': instance.newGetGuideVoice,
      'newGetGuideIcon': instance.newGetGuideIcon,
      'content': instance.content,
    };

_$_VideoInfo _$$_VideoInfoFromJson(Map<String, dynamic> json) => _$_VideoInfo(
      showVideo: json['showVideo'] as bool?,
      videoUrl: json['videoUrl'] as String?,
      videoCover: json['videoCover'] as String?,
    );

Map<String, dynamic> _$$_VideoInfoToJson(_$_VideoInfo instance) =>
    <String, dynamic>{
      'showVideo': instance.showVideo,
      'videoUrl': instance.videoUrl,
      'videoCover': instance.videoCover,
    };

_$_FinishInfo _$$_FinishInfoFromJson(Map<String, dynamic> json) =>
    _$_FinishInfo(
      finishFlag: json['finishFlag'] as bool?,
      finishGuideIcon: json['finishGuideIcon'] as String?,
    );

Map<String, dynamic> _$$_FinishInfoToJson(_$_FinishInfo instance) =>
    <String, dynamic>{
      'finishFlag': instance.finishFlag,
      'finishGuideIcon': instance.finishGuideIcon,
    };
