// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_page_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomePageData _$CourseHomePageDataFromJson(Map<String, dynamic> json) {
  return _CourseHomePageData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomePageData {
  String? get contactRoute => throw _privateConstructorUsedError;
  List<SubjectTypeVo>? get subjectTypeVoList =>
      throw _privateConstructorUsedError;
  List<ScheduleInfo>? get scheduleList => throw _privateConstructorUsedError;
  List<LessonSummary>? get lessonSummaryList =>
      throw _privateConstructorUsedError;
  List<SchedulePopup>? get schedulePopupList =>
      throw _privateConstructorUsedError;
  List<ScheduleRemindBo>? get scheduleRemindList =>
      throw _privateConstructorUsedError;
  List<UserGifCourse?>? get userGiftCourseVoList =>
      throw _privateConstructorUsedError;
  RestResources? get restResources => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomePageDataCopyWith<CourseHomePageData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomePageDataCopyWith<$Res> {
  factory $CourseHomePageDataCopyWith(
          CourseHomePageData value, $Res Function(CourseHomePageData) then) =
      _$CourseHomePageDataCopyWithImpl<$Res, CourseHomePageData>;
  @useResult
  $Res call(
      {String? contactRoute,
      List<SubjectTypeVo>? subjectTypeVoList,
      List<ScheduleInfo>? scheduleList,
      List<LessonSummary>? lessonSummaryList,
      List<SchedulePopup>? schedulePopupList,
      List<ScheduleRemindBo>? scheduleRemindList,
      List<UserGifCourse?>? userGiftCourseVoList,
      RestResources? restResources});

  $RestResourcesCopyWith<$Res>? get restResources;
}

/// @nodoc
class _$CourseHomePageDataCopyWithImpl<$Res, $Val extends CourseHomePageData>
    implements $CourseHomePageDataCopyWith<$Res> {
  _$CourseHomePageDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactRoute = freezed,
    Object? subjectTypeVoList = freezed,
    Object? scheduleList = freezed,
    Object? lessonSummaryList = freezed,
    Object? schedulePopupList = freezed,
    Object? scheduleRemindList = freezed,
    Object? userGiftCourseVoList = freezed,
    Object? restResources = freezed,
  }) {
    return _then(_value.copyWith(
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeVoList: freezed == subjectTypeVoList
          ? _value.subjectTypeVoList
          : subjectTypeVoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVo>?,
      scheduleList: freezed == scheduleList
          ? _value.scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
      lessonSummaryList: freezed == lessonSummaryList
          ? _value.lessonSummaryList
          : lessonSummaryList // ignore: cast_nullable_to_non_nullable
              as List<LessonSummary>?,
      schedulePopupList: freezed == schedulePopupList
          ? _value.schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
      scheduleRemindList: freezed == scheduleRemindList
          ? _value.scheduleRemindList
          : scheduleRemindList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleRemindBo>?,
      userGiftCourseVoList: freezed == userGiftCourseVoList
          ? _value.userGiftCourseVoList
          : userGiftCourseVoList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      restResources: freezed == restResources
          ? _value.restResources
          : restResources // ignore: cast_nullable_to_non_nullable
              as RestResources?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RestResourcesCopyWith<$Res>? get restResources {
    if (_value.restResources == null) {
      return null;
    }

    return $RestResourcesCopyWith<$Res>(_value.restResources!, (value) {
      return _then(_value.copyWith(restResources: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseHomePageDataCopyWith<$Res>
    implements $CourseHomePageDataCopyWith<$Res> {
  factory _$$_CourseHomePageDataCopyWith(_$_CourseHomePageData value,
          $Res Function(_$_CourseHomePageData) then) =
      __$$_CourseHomePageDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? contactRoute,
      List<SubjectTypeVo>? subjectTypeVoList,
      List<ScheduleInfo>? scheduleList,
      List<LessonSummary>? lessonSummaryList,
      List<SchedulePopup>? schedulePopupList,
      List<ScheduleRemindBo>? scheduleRemindList,
      List<UserGifCourse?>? userGiftCourseVoList,
      RestResources? restResources});

  @override
  $RestResourcesCopyWith<$Res>? get restResources;
}

/// @nodoc
class __$$_CourseHomePageDataCopyWithImpl<$Res>
    extends _$CourseHomePageDataCopyWithImpl<$Res, _$_CourseHomePageData>
    implements _$$_CourseHomePageDataCopyWith<$Res> {
  __$$_CourseHomePageDataCopyWithImpl(
      _$_CourseHomePageData _value, $Res Function(_$_CourseHomePageData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactRoute = freezed,
    Object? subjectTypeVoList = freezed,
    Object? scheduleList = freezed,
    Object? lessonSummaryList = freezed,
    Object? schedulePopupList = freezed,
    Object? scheduleRemindList = freezed,
    Object? userGiftCourseVoList = freezed,
    Object? restResources = freezed,
  }) {
    return _then(_$_CourseHomePageData(
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeVoList: freezed == subjectTypeVoList
          ? _value._subjectTypeVoList
          : subjectTypeVoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVo>?,
      scheduleList: freezed == scheduleList
          ? _value._scheduleList
          : scheduleList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
      lessonSummaryList: freezed == lessonSummaryList
          ? _value._lessonSummaryList
          : lessonSummaryList // ignore: cast_nullable_to_non_nullable
              as List<LessonSummary>?,
      schedulePopupList: freezed == schedulePopupList
          ? _value._schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
      scheduleRemindList: freezed == scheduleRemindList
          ? _value._scheduleRemindList
          : scheduleRemindList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleRemindBo>?,
      userGiftCourseVoList: freezed == userGiftCourseVoList
          ? _value._userGiftCourseVoList
          : userGiftCourseVoList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      restResources: freezed == restResources
          ? _value.restResources
          : restResources // ignore: cast_nullable_to_non_nullable
              as RestResources?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomePageData implements _CourseHomePageData {
  const _$_CourseHomePageData(
      {this.contactRoute,
      final List<SubjectTypeVo>? subjectTypeVoList,
      final List<ScheduleInfo>? scheduleList,
      final List<LessonSummary>? lessonSummaryList,
      final List<SchedulePopup>? schedulePopupList,
      final List<ScheduleRemindBo>? scheduleRemindList,
      final List<UserGifCourse?>? userGiftCourseVoList,
      this.restResources})
      : _subjectTypeVoList = subjectTypeVoList,
        _scheduleList = scheduleList,
        _lessonSummaryList = lessonSummaryList,
        _schedulePopupList = schedulePopupList,
        _scheduleRemindList = scheduleRemindList,
        _userGiftCourseVoList = userGiftCourseVoList;

  factory _$_CourseHomePageData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseHomePageDataFromJson(json);

  @override
  final String? contactRoute;
  final List<SubjectTypeVo>? _subjectTypeVoList;
  @override
  List<SubjectTypeVo>? get subjectTypeVoList {
    final value = _subjectTypeVoList;
    if (value == null) return null;
    if (_subjectTypeVoList is EqualUnmodifiableListView)
      return _subjectTypeVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ScheduleInfo>? _scheduleList;
  @override
  List<ScheduleInfo>? get scheduleList {
    final value = _scheduleList;
    if (value == null) return null;
    if (_scheduleList is EqualUnmodifiableListView) return _scheduleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<LessonSummary>? _lessonSummaryList;
  @override
  List<LessonSummary>? get lessonSummaryList {
    final value = _lessonSummaryList;
    if (value == null) return null;
    if (_lessonSummaryList is EqualUnmodifiableListView)
      return _lessonSummaryList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SchedulePopup>? _schedulePopupList;
  @override
  List<SchedulePopup>? get schedulePopupList {
    final value = _schedulePopupList;
    if (value == null) return null;
    if (_schedulePopupList is EqualUnmodifiableListView)
      return _schedulePopupList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ScheduleRemindBo>? _scheduleRemindList;
  @override
  List<ScheduleRemindBo>? get scheduleRemindList {
    final value = _scheduleRemindList;
    if (value == null) return null;
    if (_scheduleRemindList is EqualUnmodifiableListView)
      return _scheduleRemindList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<UserGifCourse?>? _userGiftCourseVoList;
  @override
  List<UserGifCourse?>? get userGiftCourseVoList {
    final value = _userGiftCourseVoList;
    if (value == null) return null;
    if (_userGiftCourseVoList is EqualUnmodifiableListView)
      return _userGiftCourseVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final RestResources? restResources;

  @override
  String toString() {
    return 'CourseHomePageData(contactRoute: $contactRoute, subjectTypeVoList: $subjectTypeVoList, scheduleList: $scheduleList, lessonSummaryList: $lessonSummaryList, schedulePopupList: $schedulePopupList, scheduleRemindList: $scheduleRemindList, userGiftCourseVoList: $userGiftCourseVoList, restResources: $restResources)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomePageData &&
            (identical(other.contactRoute, contactRoute) ||
                other.contactRoute == contactRoute) &&
            const DeepCollectionEquality()
                .equals(other._subjectTypeVoList, _subjectTypeVoList) &&
            const DeepCollectionEquality()
                .equals(other._scheduleList, _scheduleList) &&
            const DeepCollectionEquality()
                .equals(other._lessonSummaryList, _lessonSummaryList) &&
            const DeepCollectionEquality()
                .equals(other._schedulePopupList, _schedulePopupList) &&
            const DeepCollectionEquality()
                .equals(other._scheduleRemindList, _scheduleRemindList) &&
            const DeepCollectionEquality()
                .equals(other._userGiftCourseVoList, _userGiftCourseVoList) &&
            (identical(other.restResources, restResources) ||
                other.restResources == restResources));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      contactRoute,
      const DeepCollectionEquality().hash(_subjectTypeVoList),
      const DeepCollectionEquality().hash(_scheduleList),
      const DeepCollectionEquality().hash(_lessonSummaryList),
      const DeepCollectionEquality().hash(_schedulePopupList),
      const DeepCollectionEquality().hash(_scheduleRemindList),
      const DeepCollectionEquality().hash(_userGiftCourseVoList),
      restResources);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomePageDataCopyWith<_$_CourseHomePageData> get copyWith =>
      __$$_CourseHomePageDataCopyWithImpl<_$_CourseHomePageData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomePageDataToJson(
      this,
    );
  }
}

abstract class _CourseHomePageData implements CourseHomePageData {
  const factory _CourseHomePageData(
      {final String? contactRoute,
      final List<SubjectTypeVo>? subjectTypeVoList,
      final List<ScheduleInfo>? scheduleList,
      final List<LessonSummary>? lessonSummaryList,
      final List<SchedulePopup>? schedulePopupList,
      final List<ScheduleRemindBo>? scheduleRemindList,
      final List<UserGifCourse?>? userGiftCourseVoList,
      final RestResources? restResources}) = _$_CourseHomePageData;

  factory _CourseHomePageData.fromJson(Map<String, dynamic> json) =
      _$_CourseHomePageData.fromJson;

  @override
  String? get contactRoute;
  @override
  List<SubjectTypeVo>? get subjectTypeVoList;
  @override
  List<ScheduleInfo>? get scheduleList;
  @override
  List<LessonSummary>? get lessonSummaryList;
  @override
  List<SchedulePopup>? get schedulePopupList;
  @override
  List<ScheduleRemindBo>? get scheduleRemindList;
  @override
  List<UserGifCourse?>? get userGiftCourseVoList;
  @override
  RestResources? get restResources;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomePageDataCopyWith<_$_CourseHomePageData> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonSummary _$LessonSummaryFromJson(Map<String, dynamic> json) {
  return _LessonSummary.fromJson(json);
}

/// @nodoc
mixin _$LessonSummary {
  int? get unFinishedNum => throw _privateConstructorUsedError;
  int? get finishedNum => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonSummaryCopyWith<LessonSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonSummaryCopyWith<$Res> {
  factory $LessonSummaryCopyWith(
          LessonSummary value, $Res Function(LessonSummary) then) =
      _$LessonSummaryCopyWithImpl<$Res, LessonSummary>;
  @useResult
  $Res call({int? unFinishedNum, int? finishedNum, int? subjectType});
}

/// @nodoc
class _$LessonSummaryCopyWithImpl<$Res, $Val extends LessonSummary>
    implements $LessonSummaryCopyWith<$Res> {
  _$LessonSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_value.copyWith(
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonSummaryCopyWith<$Res>
    implements $LessonSummaryCopyWith<$Res> {
  factory _$$_LessonSummaryCopyWith(
          _$_LessonSummary value, $Res Function(_$_LessonSummary) then) =
      __$$_LessonSummaryCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? unFinishedNum, int? finishedNum, int? subjectType});
}

/// @nodoc
class __$$_LessonSummaryCopyWithImpl<$Res>
    extends _$LessonSummaryCopyWithImpl<$Res, _$_LessonSummary>
    implements _$$_LessonSummaryCopyWith<$Res> {
  __$$_LessonSummaryCopyWithImpl(
      _$_LessonSummary _value, $Res Function(_$_LessonSummary) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_$_LessonSummary(
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonSummary implements _LessonSummary {
  const _$_LessonSummary(
      {this.unFinishedNum, this.finishedNum, this.subjectType});

  factory _$_LessonSummary.fromJson(Map<String, dynamic> json) =>
      _$$_LessonSummaryFromJson(json);

  @override
  final int? unFinishedNum;
  @override
  final int? finishedNum;
  @override
  final int? subjectType;

  @override
  String toString() {
    return 'LessonSummary(unFinishedNum: $unFinishedNum, finishedNum: $finishedNum, subjectType: $subjectType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonSummary &&
            (identical(other.unFinishedNum, unFinishedNum) ||
                other.unFinishedNum == unFinishedNum) &&
            (identical(other.finishedNum, finishedNum) ||
                other.finishedNum == finishedNum) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, unFinishedNum, finishedNum, subjectType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonSummaryCopyWith<_$_LessonSummary> get copyWith =>
      __$$_LessonSummaryCopyWithImpl<_$_LessonSummary>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonSummaryToJson(
      this,
    );
  }
}

abstract class _LessonSummary implements LessonSummary {
  const factory _LessonSummary(
      {final int? unFinishedNum,
      final int? finishedNum,
      final int? subjectType}) = _$_LessonSummary;

  factory _LessonSummary.fromJson(Map<String, dynamic> json) =
      _$_LessonSummary.fromJson;

  @override
  int? get unFinishedNum;
  @override
  int? get finishedNum;
  @override
  int? get subjectType;
  @override
  @JsonKey(ignore: true)
  _$$_LessonSummaryCopyWith<_$_LessonSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleRemindBo _$ScheduleRemindBoFromJson(Map<String, dynamic> json) {
  return _ScheduleRemindBo.fromJson(json);
}

/// @nodoc
mixin _$ScheduleRemindBo {
  int? get remindType =>
      throw _privateConstructorUsedError; //类型 1开课提醒 2开课，毕业日程说明
  int? get remindOrder => throw _privateConstructorUsedError; //客户端需要做排序操作
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  int? get remindTime =>
      throw _privateConstructorUsedError; //开课时间，0点时间戳 ，客户端需要根据这个时间做排序
  int? get classId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleRemindBoCopyWith<ScheduleRemindBo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleRemindBoCopyWith<$Res> {
  factory $ScheduleRemindBoCopyWith(
          ScheduleRemindBo value, $Res Function(ScheduleRemindBo) then) =
      _$ScheduleRemindBoCopyWithImpl<$Res, ScheduleRemindBo>;
  @useResult
  $Res call(
      {int? remindType,
      int? remindOrder,
      int? subjectType,
      String? subjectTypeDesc,
      int? order,
      int? remindTime,
      int? classId,
      String? title});
}

/// @nodoc
class _$ScheduleRemindBoCopyWithImpl<$Res, $Val extends ScheduleRemindBo>
    implements $ScheduleRemindBoCopyWith<$Res> {
  _$ScheduleRemindBoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remindType = freezed,
    Object? remindOrder = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? order = freezed,
    Object? remindTime = freezed,
    Object? classId = freezed,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      remindType: freezed == remindType
          ? _value.remindType
          : remindType // ignore: cast_nullable_to_non_nullable
              as int?,
      remindOrder: freezed == remindOrder
          ? _value.remindOrder
          : remindOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      remindTime: freezed == remindTime
          ? _value.remindTime
          : remindTime // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleRemindBoCopyWith<$Res>
    implements $ScheduleRemindBoCopyWith<$Res> {
  factory _$$_ScheduleRemindBoCopyWith(
          _$_ScheduleRemindBo value, $Res Function(_$_ScheduleRemindBo) then) =
      __$$_ScheduleRemindBoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? remindType,
      int? remindOrder,
      int? subjectType,
      String? subjectTypeDesc,
      int? order,
      int? remindTime,
      int? classId,
      String? title});
}

/// @nodoc
class __$$_ScheduleRemindBoCopyWithImpl<$Res>
    extends _$ScheduleRemindBoCopyWithImpl<$Res, _$_ScheduleRemindBo>
    implements _$$_ScheduleRemindBoCopyWith<$Res> {
  __$$_ScheduleRemindBoCopyWithImpl(
      _$_ScheduleRemindBo _value, $Res Function(_$_ScheduleRemindBo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? remindType = freezed,
    Object? remindOrder = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? order = freezed,
    Object? remindTime = freezed,
    Object? classId = freezed,
    Object? title = freezed,
  }) {
    return _then(_$_ScheduleRemindBo(
      remindType: freezed == remindType
          ? _value.remindType
          : remindType // ignore: cast_nullable_to_non_nullable
              as int?,
      remindOrder: freezed == remindOrder
          ? _value.remindOrder
          : remindOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      remindTime: freezed == remindTime
          ? _value.remindTime
          : remindTime // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleRemindBo implements _ScheduleRemindBo {
  const _$_ScheduleRemindBo(
      {this.remindType,
      this.remindOrder,
      this.subjectType,
      this.subjectTypeDesc,
      this.order,
      this.remindTime,
      this.classId,
      this.title});

  factory _$_ScheduleRemindBo.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleRemindBoFromJson(json);

  @override
  final int? remindType;
//类型 1开课提醒 2开课，毕业日程说明
  @override
  final int? remindOrder;
//客户端需要做排序操作
  @override
  final int? subjectType;
  @override
  final String? subjectTypeDesc;
  @override
  final int? order;
  @override
  final int? remindTime;
//开课时间，0点时间戳 ，客户端需要根据这个时间做排序
  @override
  final int? classId;
  @override
  final String? title;

  @override
  String toString() {
    return 'ScheduleRemindBo(remindType: $remindType, remindOrder: $remindOrder, subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, order: $order, remindTime: $remindTime, classId: $classId, title: $title)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleRemindBo &&
            (identical(other.remindType, remindType) ||
                other.remindType == remindType) &&
            (identical(other.remindOrder, remindOrder) ||
                other.remindOrder == remindOrder) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.remindTime, remindTime) ||
                other.remindTime == remindTime) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, remindType, remindOrder,
      subjectType, subjectTypeDesc, order, remindTime, classId, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleRemindBoCopyWith<_$_ScheduleRemindBo> get copyWith =>
      __$$_ScheduleRemindBoCopyWithImpl<_$_ScheduleRemindBo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleRemindBoToJson(
      this,
    );
  }
}

abstract class _ScheduleRemindBo implements ScheduleRemindBo {
  const factory _ScheduleRemindBo(
      {final int? remindType,
      final int? remindOrder,
      final int? subjectType,
      final String? subjectTypeDesc,
      final int? order,
      final int? remindTime,
      final int? classId,
      final String? title}) = _$_ScheduleRemindBo;

  factory _ScheduleRemindBo.fromJson(Map<String, dynamic> json) =
      _$_ScheduleRemindBo.fromJson;

  @override
  int? get remindType;
  @override //类型 1开课提醒 2开课，毕业日程说明
  int? get remindOrder;
  @override //客户端需要做排序操作
  int? get subjectType;
  @override
  String? get subjectTypeDesc;
  @override
  int? get order;
  @override
  int? get remindTime;
  @override //开课时间，0点时间戳 ，客户端需要根据这个时间做排序
  int? get classId;
  @override
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleRemindBoCopyWith<_$_ScheduleRemindBo> get copyWith =>
      throw _privateConstructorUsedError;
}

RestResources _$RestResourcesFromJson(Map<String, dynamic> json) {
  return _RestResources.fromJson(json);
}

/// @nodoc
mixin _$RestResources {
  String? get image => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RestResourcesCopyWith<RestResources> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RestResourcesCopyWith<$Res> {
  factory $RestResourcesCopyWith(
          RestResources value, $Res Function(RestResources) then) =
      _$RestResourcesCopyWithImpl<$Res, RestResources>;
  @useResult
  $Res call({String? image, String? title});
}

/// @nodoc
class _$RestResourcesCopyWithImpl<$Res, $Val extends RestResources>
    implements $RestResourcesCopyWith<$Res> {
  _$RestResourcesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RestResourcesCopyWith<$Res>
    implements $RestResourcesCopyWith<$Res> {
  factory _$$_RestResourcesCopyWith(
          _$_RestResources value, $Res Function(_$_RestResources) then) =
      __$$_RestResourcesCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? image, String? title});
}

/// @nodoc
class __$$_RestResourcesCopyWithImpl<$Res>
    extends _$RestResourcesCopyWithImpl<$Res, _$_RestResources>
    implements _$$_RestResourcesCopyWith<$Res> {
  __$$_RestResourcesCopyWithImpl(
      _$_RestResources _value, $Res Function(_$_RestResources) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? title = freezed,
  }) {
    return _then(_$_RestResources(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RestResources implements _RestResources {
  const _$_RestResources({this.image, this.title});

  factory _$_RestResources.fromJson(Map<String, dynamic> json) =>
      _$$_RestResourcesFromJson(json);

  @override
  final String? image;
  @override
  final String? title;

  @override
  String toString() {
    return 'RestResources(image: $image, title: $title)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RestResources &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, image, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RestResourcesCopyWith<_$_RestResources> get copyWith =>
      __$$_RestResourcesCopyWithImpl<_$_RestResources>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RestResourcesToJson(
      this,
    );
  }
}

abstract class _RestResources implements RestResources {
  const factory _RestResources({final String? image, final String? title}) =
      _$_RestResources;

  factory _RestResources.fromJson(Map<String, dynamic> json) =
      _$_RestResources.fromJson;

  @override
  String? get image;
  @override
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$_RestResourcesCopyWith<_$_RestResources> get copyWith =>
      throw _privateConstructorUsedError;
}

SchedulePopup _$SchedulePopupFromJson(Map<String, dynamic> json) {
  return _SchedulePopup.fromJson(json);
}

/// @nodoc
mixin _$SchedulePopup {
  int? get popupType => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get voice => throw _privateConstructorUsedError;
  int? get startClassTime => throw _privateConstructorUsedError;
  int? get firstLessonStartTime => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError; //老师昵称
  String? get teacherProfileUrl => throw _privateConstructorUsedError; //老师头像
  String? get icon => throw _privateConstructorUsedError;
  int? get template => throw _privateConstructorUsedError;
  int? get pattern => throw _privateConstructorUsedError;
  String? get addTeacherUrl => throw _privateConstructorUsedError; //加老师链接
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  String? get userCourseBusinessStatus => throw _privateConstructorUsedError;
  String? get guideImage => throw _privateConstructorUsedError;
  String? get addButton => throw _privateConstructorUsedError;
  String? get cancelButton => throw _privateConstructorUsedError;
  bool? get offlineBlockPopup =>
      throw _privateConstructorUsedError; //true 下线阻拦弹窗
  String? get buttonText => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  bool? get limitFlow => throw _privateConstructorUsedError;
  String? get businessTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SchedulePopupCopyWith<SchedulePopup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SchedulePopupCopyWith<$Res> {
  factory $SchedulePopupCopyWith(
          SchedulePopup value, $Res Function(SchedulePopup) then) =
      _$SchedulePopupCopyWithImpl<$Res, SchedulePopup>;
  @useResult
  $Res call(
      {int? popupType,
      int? subjectType,
      int? classId,
      String? courseKey,
      String? courseSegment,
      String? title,
      String? subTitle,
      String? image,
      String? voice,
      int? startClassTime,
      int? firstLessonStartTime,
      String? teacherName,
      String? teacherProfileUrl,
      String? icon,
      int? template,
      int? pattern,
      String? addTeacherUrl,
      String? subjectTypeDesc,
      String? segmentName,
      String? userCourseBusinessStatus,
      String? guideImage,
      String? addButton,
      String? cancelButton,
      bool? offlineBlockPopup,
      String? buttonText,
      List<String>? tags,
      bool? limitFlow,
      String? businessTypeDesc});
}

/// @nodoc
class _$SchedulePopupCopyWithImpl<$Res, $Val extends SchedulePopup>
    implements $SchedulePopupCopyWith<$Res> {
  _$SchedulePopupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupType = freezed,
    Object? subjectType = freezed,
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? image = freezed,
    Object? voice = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? teacherName = freezed,
    Object? teacherProfileUrl = freezed,
    Object? icon = freezed,
    Object? template = freezed,
    Object? pattern = freezed,
    Object? addTeacherUrl = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? guideImage = freezed,
    Object? addButton = freezed,
    Object? cancelButton = freezed,
    Object? offlineBlockPopup = freezed,
    Object? buttonText = freezed,
    Object? tags = freezed,
    Object? limitFlow = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      popupType: freezed == popupType
          ? _value.popupType
          : popupType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as int?,
      pattern: freezed == pattern
          ? _value.pattern
          : pattern // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      guideImage: freezed == guideImage
          ? _value.guideImage
          : guideImage // ignore: cast_nullable_to_non_nullable
              as String?,
      addButton: freezed == addButton
          ? _value.addButton
          : addButton // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButton: freezed == cancelButton
          ? _value.cancelButton
          : cancelButton // ignore: cast_nullable_to_non_nullable
              as String?,
      offlineBlockPopup: freezed == offlineBlockPopup
          ? _value.offlineBlockPopup
          : offlineBlockPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      limitFlow: freezed == limitFlow
          ? _value.limitFlow
          : limitFlow // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SchedulePopupCopyWith<$Res>
    implements $SchedulePopupCopyWith<$Res> {
  factory _$$_SchedulePopupCopyWith(
          _$_SchedulePopup value, $Res Function(_$_SchedulePopup) then) =
      __$$_SchedulePopupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? popupType,
      int? subjectType,
      int? classId,
      String? courseKey,
      String? courseSegment,
      String? title,
      String? subTitle,
      String? image,
      String? voice,
      int? startClassTime,
      int? firstLessonStartTime,
      String? teacherName,
      String? teacherProfileUrl,
      String? icon,
      int? template,
      int? pattern,
      String? addTeacherUrl,
      String? subjectTypeDesc,
      String? segmentName,
      String? userCourseBusinessStatus,
      String? guideImage,
      String? addButton,
      String? cancelButton,
      bool? offlineBlockPopup,
      String? buttonText,
      List<String>? tags,
      bool? limitFlow,
      String? businessTypeDesc});
}

/// @nodoc
class __$$_SchedulePopupCopyWithImpl<$Res>
    extends _$SchedulePopupCopyWithImpl<$Res, _$_SchedulePopup>
    implements _$$_SchedulePopupCopyWith<$Res> {
  __$$_SchedulePopupCopyWithImpl(
      _$_SchedulePopup _value, $Res Function(_$_SchedulePopup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupType = freezed,
    Object? subjectType = freezed,
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? image = freezed,
    Object? voice = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? teacherName = freezed,
    Object? teacherProfileUrl = freezed,
    Object? icon = freezed,
    Object? template = freezed,
    Object? pattern = freezed,
    Object? addTeacherUrl = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? guideImage = freezed,
    Object? addButton = freezed,
    Object? cancelButton = freezed,
    Object? offlineBlockPopup = freezed,
    Object? buttonText = freezed,
    Object? tags = freezed,
    Object? limitFlow = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_$_SchedulePopup(
      popupType: freezed == popupType
          ? _value.popupType
          : popupType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      voice: freezed == voice
          ? _value.voice
          : voice // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as int?,
      pattern: freezed == pattern
          ? _value.pattern
          : pattern // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      guideImage: freezed == guideImage
          ? _value.guideImage
          : guideImage // ignore: cast_nullable_to_non_nullable
              as String?,
      addButton: freezed == addButton
          ? _value.addButton
          : addButton // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButton: freezed == cancelButton
          ? _value.cancelButton
          : cancelButton // ignore: cast_nullable_to_non_nullable
              as String?,
      offlineBlockPopup: freezed == offlineBlockPopup
          ? _value.offlineBlockPopup
          : offlineBlockPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      limitFlow: freezed == limitFlow
          ? _value.limitFlow
          : limitFlow // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SchedulePopup implements _SchedulePopup {
  const _$_SchedulePopup(
      {this.popupType,
      this.subjectType,
      this.classId,
      this.courseKey,
      this.courseSegment,
      this.title,
      this.subTitle,
      this.image,
      this.voice,
      this.startClassTime,
      this.firstLessonStartTime,
      this.teacherName,
      this.teacherProfileUrl,
      this.icon,
      this.template,
      this.pattern,
      this.addTeacherUrl,
      this.subjectTypeDesc,
      this.segmentName,
      this.userCourseBusinessStatus,
      this.guideImage,
      this.addButton,
      this.cancelButton,
      this.offlineBlockPopup,
      this.buttonText,
      final List<String>? tags,
      this.limitFlow,
      this.businessTypeDesc})
      : _tags = tags;

  factory _$_SchedulePopup.fromJson(Map<String, dynamic> json) =>
      _$$_SchedulePopupFromJson(json);

  @override
  final int? popupType;
  @override
  final int? subjectType;
  @override
  final int? classId;
  @override
  final String? courseKey;
  @override
  final String? courseSegment;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final String? image;
  @override
  final String? voice;
  @override
  final int? startClassTime;
  @override
  final int? firstLessonStartTime;
  @override
  final String? teacherName;
//老师昵称
  @override
  final String? teacherProfileUrl;
//老师头像
  @override
  final String? icon;
  @override
  final int? template;
  @override
  final int? pattern;
  @override
  final String? addTeacherUrl;
//加老师链接
  @override
  final String? subjectTypeDesc;
  @override
  final String? segmentName;
  @override
  final String? userCourseBusinessStatus;
  @override
  final String? guideImage;
  @override
  final String? addButton;
  @override
  final String? cancelButton;
  @override
  final bool? offlineBlockPopup;
//true 下线阻拦弹窗
  @override
  final String? buttonText;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? limitFlow;
  @override
  final String? businessTypeDesc;

  @override
  String toString() {
    return 'SchedulePopup(popupType: $popupType, subjectType: $subjectType, classId: $classId, courseKey: $courseKey, courseSegment: $courseSegment, title: $title, subTitle: $subTitle, image: $image, voice: $voice, startClassTime: $startClassTime, firstLessonStartTime: $firstLessonStartTime, teacherName: $teacherName, teacherProfileUrl: $teacherProfileUrl, icon: $icon, template: $template, pattern: $pattern, addTeacherUrl: $addTeacherUrl, subjectTypeDesc: $subjectTypeDesc, segmentName: $segmentName, userCourseBusinessStatus: $userCourseBusinessStatus, guideImage: $guideImage, addButton: $addButton, cancelButton: $cancelButton, offlineBlockPopup: $offlineBlockPopup, buttonText: $buttonText, tags: $tags, limitFlow: $limitFlow, businessTypeDesc: $businessTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SchedulePopup &&
            (identical(other.popupType, popupType) ||
                other.popupType == popupType) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.voice, voice) || other.voice == voice) &&
            (identical(other.startClassTime, startClassTime) ||
                other.startClassTime == startClassTime) &&
            (identical(other.firstLessonStartTime, firstLessonStartTime) ||
                other.firstLessonStartTime == firstLessonStartTime) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.teacherProfileUrl, teacherProfileUrl) ||
                other.teacherProfileUrl == teacherProfileUrl) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.template, template) ||
                other.template == template) &&
            (identical(other.pattern, pattern) || other.pattern == pattern) &&
            (identical(other.addTeacherUrl, addTeacherUrl) ||
                other.addTeacherUrl == addTeacherUrl) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(
                    other.userCourseBusinessStatus, userCourseBusinessStatus) ||
                other.userCourseBusinessStatus == userCourseBusinessStatus) &&
            (identical(other.guideImage, guideImage) ||
                other.guideImage == guideImage) &&
            (identical(other.addButton, addButton) ||
                other.addButton == addButton) &&
            (identical(other.cancelButton, cancelButton) ||
                other.cancelButton == cancelButton) &&
            (identical(other.offlineBlockPopup, offlineBlockPopup) ||
                other.offlineBlockPopup == offlineBlockPopup) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.limitFlow, limitFlow) ||
                other.limitFlow == limitFlow) &&
            (identical(other.businessTypeDesc, businessTypeDesc) ||
                other.businessTypeDesc == businessTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        popupType,
        subjectType,
        classId,
        courseKey,
        courseSegment,
        title,
        subTitle,
        image,
        voice,
        startClassTime,
        firstLessonStartTime,
        teacherName,
        teacherProfileUrl,
        icon,
        template,
        pattern,
        addTeacherUrl,
        subjectTypeDesc,
        segmentName,
        userCourseBusinessStatus,
        guideImage,
        addButton,
        cancelButton,
        offlineBlockPopup,
        buttonText,
        const DeepCollectionEquality().hash(_tags),
        limitFlow,
        businessTypeDesc
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SchedulePopupCopyWith<_$_SchedulePopup> get copyWith =>
      __$$_SchedulePopupCopyWithImpl<_$_SchedulePopup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SchedulePopupToJson(
      this,
    );
  }
}

abstract class _SchedulePopup implements SchedulePopup {
  const factory _SchedulePopup(
      {final int? popupType,
      final int? subjectType,
      final int? classId,
      final String? courseKey,
      final String? courseSegment,
      final String? title,
      final String? subTitle,
      final String? image,
      final String? voice,
      final int? startClassTime,
      final int? firstLessonStartTime,
      final String? teacherName,
      final String? teacherProfileUrl,
      final String? icon,
      final int? template,
      final int? pattern,
      final String? addTeacherUrl,
      final String? subjectTypeDesc,
      final String? segmentName,
      final String? userCourseBusinessStatus,
      final String? guideImage,
      final String? addButton,
      final String? cancelButton,
      final bool? offlineBlockPopup,
      final String? buttonText,
      final List<String>? tags,
      final bool? limitFlow,
      final String? businessTypeDesc}) = _$_SchedulePopup;

  factory _SchedulePopup.fromJson(Map<String, dynamic> json) =
      _$_SchedulePopup.fromJson;

  @override
  int? get popupType;
  @override
  int? get subjectType;
  @override
  int? get classId;
  @override
  String? get courseKey;
  @override
  String? get courseSegment;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  String? get image;
  @override
  String? get voice;
  @override
  int? get startClassTime;
  @override
  int? get firstLessonStartTime;
  @override
  String? get teacherName;
  @override //老师昵称
  String? get teacherProfileUrl;
  @override //老师头像
  String? get icon;
  @override
  int? get template;
  @override
  int? get pattern;
  @override
  String? get addTeacherUrl;
  @override //加老师链接
  String? get subjectTypeDesc;
  @override
  String? get segmentName;
  @override
  String? get userCourseBusinessStatus;
  @override
  String? get guideImage;
  @override
  String? get addButton;
  @override
  String? get cancelButton;
  @override
  bool? get offlineBlockPopup;
  @override //true 下线阻拦弹窗
  String? get buttonText;
  @override
  List<String>? get tags;
  @override
  bool? get limitFlow;
  @override
  String? get businessTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_SchedulePopupCopyWith<_$_SchedulePopup> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleInfo _$ScheduleInfoFromJson(Map<String, dynamic> json) {
  return _ScheduleInfo.fromJson(json);
}

/// @nodoc
mixin _$ScheduleInfo {
  List<SubjectScheduleStatus>? get subjectScheduleStatus =>
      throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  String? get showDate => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;

  ///卡片类型 1 等待期卡片 2 课时卡片 3 结营卡片
  int? get cardType => throw _privateConstructorUsedError;

  /// 班期id
  int? get classId => throw _privateConstructorUsedError;

  /// 阶段名称
  String? get courseSegmentName => throw _privateConstructorUsedError;

  /// 班期状态名
  String? get cardTypeName => throw _privateConstructorUsedError;

  ///行课期
  String? get period => throw _privateConstructorUsedError;

  ///行课期排序
  int? get order => throw _privateConstructorUsedError;
  List<int>? get newGetSubjectTypeList => throw _privateConstructorUsedError;
  List<ScheduleTaskBo>? get scheduleTaskList =>
      throw _privateConstructorUsedError;
  EnjoyTrainMedal? get medal => throw _privateConstructorUsedError;

  /// 班期状态
  String? get classStatusName => throw _privateConstructorUsedError;
  bool? get trialCourse => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleInfoCopyWith<ScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleInfoCopyWith<$Res> {
  factory $ScheduleInfoCopyWith(
          ScheduleInfo value, $Res Function(ScheduleInfo) then) =
      _$ScheduleInfoCopyWithImpl<$Res, ScheduleInfo>;
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      String? showDate,
      bool? today,
      int? subjectType,
      int? cardType,
      int? classId,
      String? courseSegmentName,
      String? cardTypeName,
      String? period,
      int? order,
      List<int>? newGetSubjectTypeList,
      List<ScheduleTaskBo>? scheduleTaskList,
      EnjoyTrainMedal? medal,
      String? classStatusName,
      bool? trialCourse});

  $EnjoyTrainMedalCopyWith<$Res>? get medal;
}

/// @nodoc
class _$ScheduleInfoCopyWithImpl<$Res, $Val extends ScheduleInfo>
    implements $ScheduleInfoCopyWith<$Res> {
  _$ScheduleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? today = freezed,
    Object? subjectType = freezed,
    Object? cardType = freezed,
    Object? classId = freezed,
    Object? courseSegmentName = freezed,
    Object? cardTypeName = freezed,
    Object? period = freezed,
    Object? order = freezed,
    Object? newGetSubjectTypeList = freezed,
    Object? scheduleTaskList = freezed,
    Object? medal = freezed,
    Object? classStatusName = freezed,
    Object? trialCourse = freezed,
  }) {
    return _then(_value.copyWith(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value.subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardTypeName: freezed == cardTypeName
          ? _value.cardTypeName
          : cardTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      period: freezed == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetSubjectTypeList: freezed == newGetSubjectTypeList
          ? _value.newGetSubjectTypeList
          : newGetSubjectTypeList // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value.scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as EnjoyTrainMedal?,
      classStatusName: freezed == classStatusName
          ? _value.classStatusName
          : classStatusName // ignore: cast_nullable_to_non_nullable
              as String?,
      trialCourse: freezed == trialCourse
          ? _value.trialCourse
          : trialCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $EnjoyTrainMedalCopyWith<$Res>? get medal {
    if (_value.medal == null) {
      return null;
    }

    return $EnjoyTrainMedalCopyWith<$Res>(_value.medal!, (value) {
      return _then(_value.copyWith(medal: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ScheduleInfoCopyWith<$Res>
    implements $ScheduleInfoCopyWith<$Res> {
  factory _$$_ScheduleInfoCopyWith(
          _$_ScheduleInfo value, $Res Function(_$_ScheduleInfo) then) =
      __$$_ScheduleInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      String? showDate,
      bool? today,
      int? subjectType,
      int? cardType,
      int? classId,
      String? courseSegmentName,
      String? cardTypeName,
      String? period,
      int? order,
      List<int>? newGetSubjectTypeList,
      List<ScheduleTaskBo>? scheduleTaskList,
      EnjoyTrainMedal? medal,
      String? classStatusName,
      bool? trialCourse});

  @override
  $EnjoyTrainMedalCopyWith<$Res>? get medal;
}

/// @nodoc
class __$$_ScheduleInfoCopyWithImpl<$Res>
    extends _$ScheduleInfoCopyWithImpl<$Res, _$_ScheduleInfo>
    implements _$$_ScheduleInfoCopyWith<$Res> {
  __$$_ScheduleInfoCopyWithImpl(
      _$_ScheduleInfo _value, $Res Function(_$_ScheduleInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? today = freezed,
    Object? subjectType = freezed,
    Object? cardType = freezed,
    Object? classId = freezed,
    Object? courseSegmentName = freezed,
    Object? cardTypeName = freezed,
    Object? period = freezed,
    Object? order = freezed,
    Object? newGetSubjectTypeList = freezed,
    Object? scheduleTaskList = freezed,
    Object? medal = freezed,
    Object? classStatusName = freezed,
    Object? trialCourse = freezed,
  }) {
    return _then(_$_ScheduleInfo(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value._subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardTypeName: freezed == cardTypeName
          ? _value.cardTypeName
          : cardTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      period: freezed == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      newGetSubjectTypeList: freezed == newGetSubjectTypeList
          ? _value._newGetSubjectTypeList
          : newGetSubjectTypeList // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value._scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskBo>?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as EnjoyTrainMedal?,
      classStatusName: freezed == classStatusName
          ? _value.classStatusName
          : classStatusName // ignore: cast_nullable_to_non_nullable
              as String?,
      trialCourse: freezed == trialCourse
          ? _value.trialCourse
          : trialCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleInfo implements _ScheduleInfo {
  const _$_ScheduleInfo(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      this.showDateTime,
      this.showDate,
      this.today,
      this.subjectType,
      this.cardType,
      this.classId,
      this.courseSegmentName,
      this.cardTypeName,
      this.period,
      this.order,
      final List<int>? newGetSubjectTypeList,
      final List<ScheduleTaskBo>? scheduleTaskList,
      this.medal,
      this.classStatusName,
      this.trialCourse})
      : _subjectScheduleStatus = subjectScheduleStatus,
        _newGetSubjectTypeList = newGetSubjectTypeList,
        _scheduleTaskList = scheduleTaskList;

  factory _$_ScheduleInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleInfoFromJson(json);

  final List<SubjectScheduleStatus>? _subjectScheduleStatus;
  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus {
    final value = _subjectScheduleStatus;
    if (value == null) return null;
    if (_subjectScheduleStatus is EqualUnmodifiableListView)
      return _subjectScheduleStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? showDateTime;
  @override
  final String? showDate;
  @override
  final bool? today;
  @override
  final int? subjectType;

  ///卡片类型 1 等待期卡片 2 课时卡片 3 结营卡片
  @override
  final int? cardType;

  /// 班期id
  @override
  final int? classId;

  /// 阶段名称
  @override
  final String? courseSegmentName;

  /// 班期状态名
  @override
  final String? cardTypeName;

  ///行课期
  @override
  final String? period;

  ///行课期排序
  @override
  final int? order;
  final List<int>? _newGetSubjectTypeList;
  @override
  List<int>? get newGetSubjectTypeList {
    final value = _newGetSubjectTypeList;
    if (value == null) return null;
    if (_newGetSubjectTypeList is EqualUnmodifiableListView)
      return _newGetSubjectTypeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ScheduleTaskBo>? _scheduleTaskList;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList {
    final value = _scheduleTaskList;
    if (value == null) return null;
    if (_scheduleTaskList is EqualUnmodifiableListView)
      return _scheduleTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final EnjoyTrainMedal? medal;

  /// 班期状态
  @override
  final String? classStatusName;
  @override
  final bool? trialCourse;

  @override
  String toString() {
    return 'ScheduleInfo(subjectScheduleStatus: $subjectScheduleStatus, showDateTime: $showDateTime, showDate: $showDate, today: $today, subjectType: $subjectType, cardType: $cardType, classId: $classId, courseSegmentName: $courseSegmentName, cardTypeName: $cardTypeName, period: $period, order: $order, newGetSubjectTypeList: $newGetSubjectTypeList, scheduleTaskList: $scheduleTaskList, medal: $medal, classStatusName: $classStatusName, trialCourse: $trialCourse)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleInfo &&
            const DeepCollectionEquality()
                .equals(other._subjectScheduleStatus, _subjectScheduleStatus) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.showDate, showDate) ||
                other.showDate == showDate) &&
            (identical(other.today, today) || other.today == today) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.cardTypeName, cardTypeName) ||
                other.cardTypeName == cardTypeName) &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.order, order) || other.order == order) &&
            const DeepCollectionEquality()
                .equals(other._newGetSubjectTypeList, _newGetSubjectTypeList) &&
            const DeepCollectionEquality()
                .equals(other._scheduleTaskList, _scheduleTaskList) &&
            (identical(other.medal, medal) || other.medal == medal) &&
            (identical(other.classStatusName, classStatusName) ||
                other.classStatusName == classStatusName) &&
            (identical(other.trialCourse, trialCourse) ||
                other.trialCourse == trialCourse));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectScheduleStatus),
      showDateTime,
      showDate,
      today,
      subjectType,
      cardType,
      classId,
      courseSegmentName,
      cardTypeName,
      period,
      order,
      const DeepCollectionEquality().hash(_newGetSubjectTypeList),
      const DeepCollectionEquality().hash(_scheduleTaskList),
      medal,
      classStatusName,
      trialCourse);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleInfoCopyWith<_$_ScheduleInfo> get copyWith =>
      __$$_ScheduleInfoCopyWithImpl<_$_ScheduleInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleInfoToJson(
      this,
    );
  }
}

abstract class _ScheduleInfo implements ScheduleInfo {
  const factory _ScheduleInfo(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      final int? showDateTime,
      final String? showDate,
      final bool? today,
      final int? subjectType,
      final int? cardType,
      final int? classId,
      final String? courseSegmentName,
      final String? cardTypeName,
      final String? period,
      final int? order,
      final List<int>? newGetSubjectTypeList,
      final List<ScheduleTaskBo>? scheduleTaskList,
      final EnjoyTrainMedal? medal,
      final String? classStatusName,
      final bool? trialCourse}) = _$_ScheduleInfo;

  factory _ScheduleInfo.fromJson(Map<String, dynamic> json) =
      _$_ScheduleInfo.fromJson;

  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus;
  @override
  int? get showDateTime;
  @override
  String? get showDate;
  @override
  bool? get today;
  @override
  int? get subjectType;
  @override

  ///卡片类型 1 等待期卡片 2 课时卡片 3 结营卡片
  int? get cardType;
  @override

  /// 班期id
  int? get classId;
  @override

  /// 阶段名称
  String? get courseSegmentName;
  @override

  /// 班期状态名
  String? get cardTypeName;
  @override

  ///行课期
  String? get period;
  @override

  ///行课期排序
  int? get order;
  @override
  List<int>? get newGetSubjectTypeList;
  @override
  List<ScheduleTaskBo>? get scheduleTaskList;
  @override
  EnjoyTrainMedal? get medal;
  @override

  /// 班期状态
  String? get classStatusName;
  @override
  bool? get trialCourse;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleInfoCopyWith<_$_ScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleTaskBo _$ScheduleTaskBoFromJson(Map<String, dynamic> json) {
  return _ScheduleTaskBo.fromJson(json);
}

/// @nodoc
mixin _$ScheduleTaskBo {
// @Default(false)
// bool isExpanded,
  int? get taskStatus => throw _privateConstructorUsedError; // @Default(false)
// bool isExpanded,
  set taskStatus(int? value) => throw _privateConstructorUsedError;
  int? get materialId => throw _privateConstructorUsedError;
  set materialId(int? value) => throw _privateConstructorUsedError;
  int? get sortLessonOrder => throw _privateConstructorUsedError;
  set sortLessonOrder(int? value) => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  set materialName(String? value) => throw _privateConstructorUsedError;
  int? get configId => throw _privateConstructorUsedError;
  set configId(int? value) => throw _privateConstructorUsedError;
  String? get taskStatusDesc => throw _privateConstructorUsedError;
  set taskStatusDesc(String? value) => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  set showDateTime(int? value) => throw _privateConstructorUsedError;
  int? get startClassTime => throw _privateConstructorUsedError;
  set startClassTime(int? value) => throw _privateConstructorUsedError;
  int? get firstLessonStartTime => throw _privateConstructorUsedError;
  set firstLessonStartTime(int? value) => throw _privateConstructorUsedError;
  int? get joinClassTime => throw _privateConstructorUsedError;
  set joinClassTime(int? value) => throw _privateConstructorUsedError;
  int? get taskType => throw _privateConstructorUsedError;
  set taskType(int? value) => throw _privateConstructorUsedError;
  String? get taskTypeDesc => throw _privateConstructorUsedError;
  set taskTypeDesc(String? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  String? get scheduleTaskId => throw _privateConstructorUsedError;
  set scheduleTaskId(String? value) => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  set courseId(int? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  set lessonId(int? value) => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  set subjectType(int? value) => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  set subjectTypeDesc(String? value) => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  set segmentId(int? value) => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  set weekId(int? value) => throw _privateConstructorUsedError;
  int? get unlockType => throw _privateConstructorUsedError;
  set unlockType(int? value) => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  set subTitle(String? value) => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get lessonLabel => throw _privateConstructorUsedError;
  set lessonLabel(String? value) => throw _privateConstructorUsedError;
  String? get lessonOrder => throw _privateConstructorUsedError;
  set lessonOrder(String? value) => throw _privateConstructorUsedError;
  String? get lessonSubDesc => throw _privateConstructorUsedError;
  set lessonSubDesc(String? value) => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  set lessonName(String? value) => throw _privateConstructorUsedError;
  int? get classStatus => throw _privateConstructorUsedError;
  set classStatus(int? value) => throw _privateConstructorUsedError;
  String? get bannerImage => throw _privateConstructorUsedError;
  set bannerImage(String? value) => throw _privateConstructorUsedError;
  String? get padBannerImage => throw _privateConstructorUsedError;
  set padBannerImage(String? value) => throw _privateConstructorUsedError;
  int? get bannerType => throw _privateConstructorUsedError;
  set bannerType(int? value) => throw _privateConstructorUsedError;
  int? get bannerNum => throw _privateConstructorUsedError;
  set bannerNum(int? value) => throw _privateConstructorUsedError;
  String? get lessonCoverImage => throw _privateConstructorUsedError;
  set lessonCoverImage(String? value) => throw _privateConstructorUsedError;
  String? get courseCoverImage => throw _privateConstructorUsedError;
  set courseCoverImage(String? value) => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  set image(String? value) => throw _privateConstructorUsedError;
  String? get padImage => throw _privateConstructorUsedError;
  set padImage(String? value) => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  set courseType(int? value) => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  set courseSegmentName(String? value) => throw _privateConstructorUsedError;
  String? get userCourseBusinessStatus => throw _privateConstructorUsedError;
  set userCourseBusinessStatus(String? value) =>
      throw _privateConstructorUsedError;
  CourseHomePageLessonResourceData? get lessonResource =>
      throw _privateConstructorUsedError;
  set lessonResource(CourseHomePageLessonResourceData? value) =>
      throw _privateConstructorUsedError;
  CourseHomePageCourseResourceData? get courseResource =>
      throw _privateConstructorUsedError;
  set courseResource(CourseHomePageCourseResourceData? value) =>
      throw _privateConstructorUsedError;
  List<LessonServiceList>? get lessonServiceList =>
      throw _privateConstructorUsedError;
  set lessonServiceList(List<LessonServiceList>? value) =>
      throw _privateConstructorUsedError;
  List<ChildrenTaskList>? get trainScheduleTasks =>
      throw _privateConstructorUsedError;
  set trainScheduleTasks(List<ChildrenTaskList>? value) =>
      throw _privateConstructorUsedError;
  List<ChildrenTaskList>? get childrenTaskList =>
      throw _privateConstructorUsedError;
  set childrenTaskList(List<ChildrenTaskList>? value) =>
      throw _privateConstructorUsedError;
  List<LessonPopData>? get lessonPopList => throw _privateConstructorUsedError;
  set lessonPopList(List<LessonPopData>? value) =>
      throw _privateConstructorUsedError;
  TrainTookitVo? get trainToolkitVo => throw _privateConstructorUsedError;
  set trainToolkitVo(TrainTookitVo? value) =>
      throw _privateConstructorUsedError;
  bool? get newGetFlag => throw _privateConstructorUsedError;
  set newGetFlag(bool? value) => throw _privateConstructorUsedError;
  List<TrainCampTipData>? get trainNewGetGuideList =>
      throw _privateConstructorUsedError;
  set trainNewGetGuideList(List<TrainCampTipData>? value) =>
      throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  set btnText(String? value) => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  set pictureUrl(String? value) => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  set backgroundColor(String? value) => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  set linkUrl(String? value) => throw _privateConstructorUsedError;
  String? get missionMaterialType => throw _privateConstructorUsedError;
  set missionMaterialType(String? value) => throw _privateConstructorUsedError;
  String? get missionBusinessTypeName => throw _privateConstructorUsedError;
  set missionBusinessTypeName(String? value) =>
      throw _privateConstructorUsedError;
  bool? get expired => throw _privateConstructorUsedError;
  set expired(bool? value) => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  set description(String? value) => throw _privateConstructorUsedError;
  String? get titleColor => throw _privateConstructorUsedError;
  set titleColor(String? value) => throw _privateConstructorUsedError;
  String? get descriptionColor => throw _privateConstructorUsedError;
  set descriptionColor(String? value) => throw _privateConstructorUsedError;
  String? get missionMaterialTypeName => throw _privateConstructorUsedError;
  set missionMaterialTypeName(String? value) =>
      throw _privateConstructorUsedError;
  String? get missionTypeName => throw _privateConstructorUsedError;
  set missionTypeName(String? value) => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;
  set businessTagId(String? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfo>? get subjectInfoList => throw _privateConstructorUsedError;
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  set subjectInfoList(List<SubjectInfo>? value) =>
      throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  set gradeTitle(String? value) => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  set gradeKey(int? value) => throw _privateConstructorUsedError;
  bool? get lastDrainageCourseLessonTask => throw _privateConstructorUsedError;
  set lastDrainageCourseLessonTask(bool? value) =>
      throw _privateConstructorUsedError;
  DrainageCourseCardInfo? get courseInfo => throw _privateConstructorUsedError;
  set courseInfo(DrainageCourseCardInfo? value) =>
      throw _privateConstructorUsedError;
  bool? get needPop => throw _privateConstructorUsedError;
  set needPop(bool? value) => throw _privateConstructorUsedError;
  bool? get showNew => throw _privateConstructorUsedError;
  set showNew(bool? value) => throw _privateConstructorUsedError;
  AdvancedCourseInfo? get advancedCourseInfo =>
      throw _privateConstructorUsedError;
  set advancedCourseInfo(AdvancedCourseInfo? value) =>
      throw _privateConstructorUsedError;
  String? get buttonDescription => throw _privateConstructorUsedError;
  set buttonDescription(String? value) => throw _privateConstructorUsedError;
  String? get landscapePictureUrl => throw _privateConstructorUsedError;
  set landscapePictureUrl(String? value) => throw _privateConstructorUsedError;
  int? get auditId => throw _privateConstructorUsedError;
  set auditId(int? value) => throw _privateConstructorUsedError;
  String? get courseLabel => throw _privateConstructorUsedError;
  set courseLabel(String? value) => throw _privateConstructorUsedError;
  String? get startLessonDesc => throw _privateConstructorUsedError;
  set startLessonDesc(String? value) =>
      throw _privateConstructorUsedError; //背景图片
  String? get bgImage => throw _privateConstructorUsedError; //背景图片
  set bgImage(String? value) => throw _privateConstructorUsedError;
  String? get userCourseId => throw _privateConstructorUsedError;
  set userCourseId(String? value) => throw _privateConstructorUsedError;
  NewGetGuideInfo? get newGetGuideBo => throw _privateConstructorUsedError;
  set newGetGuideBo(NewGetGuideInfo? value) =>
      throw _privateConstructorUsedError;
  TaskTagBo? get taskTagBo => throw _privateConstructorUsedError;
  set taskTagBo(TaskTagBo? value) => throw _privateConstructorUsedError;

  ///训练营年课 统一的卡片数据处理 源自本地可供ui 直接显示 后续如果上课页 所有卡片
  ///都进行中转处理 LessonCardData 可以直接作为上课页卡片显示的ui 数据，不必放在ScheduleTaskBo
  LessonCardData? get lessonCardData => throw _privateConstructorUsedError;

  ///训练营年课 统一的卡片数据处理 源自本地可供ui 直接显示 后续如果上课页 所有卡片
  ///都进行中转处理 LessonCardData 可以直接作为上课页卡片显示的ui 数据，不必放在ScheduleTaskBo
  set lessonCardData(LessonCardData? value) =>
      throw _privateConstructorUsedError;

  ///未激活原因
  String? get unactivatedReason => throw _privateConstructorUsedError;

  ///未激活原因
  set unactivatedReason(String? value) => throw _privateConstructorUsedError;
  List<String>? get trackExtendInfoList => throw _privateConstructorUsedError;
  set trackExtendInfoList(List<String>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleTaskBoCopyWith<ScheduleTaskBo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleTaskBoCopyWith<$Res> {
  factory $ScheduleTaskBoCopyWith(
          ScheduleTaskBo value, $Res Function(ScheduleTaskBo) then) =
      _$ScheduleTaskBoCopyWithImpl<$Res, ScheduleTaskBo>;
  @useResult
  $Res call(
      {int? taskStatus,
      int? materialId,
      int? sortLessonOrder,
      String? materialName,
      int? configId,
      String? taskStatusDesc,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? joinClassTime,
      int? taskType,
      String? taskTypeDesc,
      int? classId,
      String? scheduleTaskId,
      int? courseId,
      String? courseKey,
      int? lessonId,
      int? subjectType,
      String? subjectTypeDesc,
      int? segmentId,
      int? weekId,
      int? unlockType,
      String? title,
      String? subTitle,
      String? route,
      String? icon,
      String? lessonLabel,
      String? lessonOrder,
      String? lessonSubDesc,
      String? lessonName,
      int? classStatus,
      String? bannerImage,
      String? padBannerImage,
      int? bannerType,
      int? bannerNum,
      String? lessonCoverImage,
      String? courseCoverImage,
      String? image,
      String? padImage,
      int? courseType,
      String? courseSegmentName,
      String? userCourseBusinessStatus,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<LessonServiceList>? lessonServiceList,
      List<ChildrenTaskList>? trainScheduleTasks,
      List<ChildrenTaskList>? childrenTaskList,
      List<LessonPopData>? lessonPopList,
      TrainTookitVo? trainToolkitVo,
      bool? newGetFlag,
      List<TrainCampTipData>? trainNewGetGuideList,
      String? btnText,
      String? pictureUrl,
      String? backgroundColor,
      String? linkUrl,
      String? missionMaterialType,
      String? missionBusinessTypeName,
      bool? expired,
      String? description,
      String? titleColor,
      String? descriptionColor,
      String? missionMaterialTypeName,
      String? missionTypeName,
      String? businessTagId,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      List<SubjectInfo>? subjectInfoList,
      String? gradeTitle,
      int? gradeKey,
      bool? lastDrainageCourseLessonTask,
      DrainageCourseCardInfo? courseInfo,
      bool? needPop,
      bool? showNew,
      AdvancedCourseInfo? advancedCourseInfo,
      String? buttonDescription,
      String? landscapePictureUrl,
      int? auditId,
      String? courseLabel,
      String? startLessonDesc,
      String? bgImage,
      String? userCourseId,
      NewGetGuideInfo? newGetGuideBo,
      TaskTagBo? taskTagBo,
      LessonCardData? lessonCardData,
      String? unactivatedReason,
      List<String>? trackExtendInfoList});

  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource;
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource;
  $TrainTookitVoCopyWith<$Res>? get trainToolkitVo;
  $DrainageCourseCardInfoCopyWith<$Res>? get courseInfo;
  $AdvancedCourseInfoCopyWith<$Res>? get advancedCourseInfo;
  $NewGetGuideInfoCopyWith<$Res>? get newGetGuideBo;
  $TaskTagBoCopyWith<$Res>? get taskTagBo;
  $LessonCardDataCopyWith<$Res>? get lessonCardData;
}

/// @nodoc
class _$ScheduleTaskBoCopyWithImpl<$Res, $Val extends ScheduleTaskBo>
    implements $ScheduleTaskBoCopyWith<$Res> {
  _$ScheduleTaskBoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? materialId = freezed,
    Object? sortLessonOrder = freezed,
    Object? materialName = freezed,
    Object? configId = freezed,
    Object? taskStatusDesc = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? joinClassTime = freezed,
    Object? taskType = freezed,
    Object? taskTypeDesc = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonOrder = freezed,
    Object? lessonSubDesc = freezed,
    Object? lessonName = freezed,
    Object? classStatus = freezed,
    Object? bannerImage = freezed,
    Object? padBannerImage = freezed,
    Object? bannerType = freezed,
    Object? bannerNum = freezed,
    Object? lessonCoverImage = freezed,
    Object? courseCoverImage = freezed,
    Object? image = freezed,
    Object? padImage = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? lessonResource = freezed,
    Object? courseResource = freezed,
    Object? lessonServiceList = freezed,
    Object? trainScheduleTasks = freezed,
    Object? childrenTaskList = freezed,
    Object? lessonPopList = freezed,
    Object? trainToolkitVo = freezed,
    Object? newGetFlag = freezed,
    Object? trainNewGetGuideList = freezed,
    Object? btnText = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? linkUrl = freezed,
    Object? missionMaterialType = freezed,
    Object? missionBusinessTypeName = freezed,
    Object? expired = freezed,
    Object? description = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? missionMaterialTypeName = freezed,
    Object? missionTypeName = freezed,
    Object? businessTagId = freezed,
    Object? subjectInfoList = freezed,
    Object? gradeTitle = freezed,
    Object? gradeKey = freezed,
    Object? lastDrainageCourseLessonTask = freezed,
    Object? courseInfo = freezed,
    Object? needPop = freezed,
    Object? showNew = freezed,
    Object? advancedCourseInfo = freezed,
    Object? buttonDescription = freezed,
    Object? landscapePictureUrl = freezed,
    Object? auditId = freezed,
    Object? courseLabel = freezed,
    Object? startLessonDesc = freezed,
    Object? bgImage = freezed,
    Object? userCourseId = freezed,
    Object? newGetGuideBo = freezed,
    Object? taskTagBo = freezed,
    Object? lessonCardData = freezed,
    Object? unactivatedReason = freezed,
    Object? trackExtendInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      sortLessonOrder: freezed == sortLessonOrder
          ? _value.sortLessonOrder
          : sortLessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatusDesc: freezed == taskStatusDesc
          ? _value.taskStatusDesc
          : taskStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      joinClassTime: freezed == joinClassTime
          ? _value.joinClassTime
          : joinClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskTypeDesc: freezed == taskTypeDesc
          ? _value.taskTypeDesc
          : taskTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubDesc: freezed == lessonSubDesc
          ? _value.lessonSubDesc
          : lessonSubDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      padBannerImage: freezed == padBannerImage
          ? _value.padBannerImage
          : padBannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerType: freezed == bannerType
          ? _value.bannerType
          : bannerType // ignore: cast_nullable_to_non_nullable
              as int?,
      bannerNum: freezed == bannerNum
          ? _value.bannerNum
          : bannerNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCoverImage: freezed == courseCoverImage
          ? _value.courseCoverImage
          : courseCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      padImage: freezed == padImage
          ? _value.padImage
          : padImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonResource: freezed == lessonResource
          ? _value.lessonResource
          : lessonResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageLessonResourceData?,
      courseResource: freezed == courseResource
          ? _value.courseResource
          : courseResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageCourseResourceData?,
      lessonServiceList: freezed == lessonServiceList
          ? _value.lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceList>?,
      trainScheduleTasks: freezed == trainScheduleTasks
          ? _value.trainScheduleTasks
          : trainScheduleTasks // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
      lessonPopList: freezed == lessonPopList
          ? _value.lessonPopList
          : lessonPopList // ignore: cast_nullable_to_non_nullable
              as List<LessonPopData>?,
      trainToolkitVo: freezed == trainToolkitVo
          ? _value.trainToolkitVo
          : trainToolkitVo // ignore: cast_nullable_to_non_nullable
              as TrainTookitVo?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      trainNewGetGuideList: freezed == trainNewGetGuideList
          ? _value.trainNewGetGuideList
          : trainNewGetGuideList // ignore: cast_nullable_to_non_nullable
              as List<TrainCampTipData>?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      missionBusinessTypeName: freezed == missionBusinessTypeName
          ? _value.missionBusinessTypeName
          : missionBusinessTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      expired: freezed == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialTypeName: freezed == missionMaterialTypeName
          ? _value.missionMaterialTypeName
          : missionMaterialTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      missionTypeName: freezed == missionTypeName
          ? _value.missionTypeName
          : missionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value.subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectInfo>?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      lastDrainageCourseLessonTask: freezed == lastDrainageCourseLessonTask
          ? _value.lastDrainageCourseLessonTask
          : lastDrainageCourseLessonTask // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as DrainageCourseCardInfo?,
      needPop: freezed == needPop
          ? _value.needPop
          : needPop // ignore: cast_nullable_to_non_nullable
              as bool?,
      showNew: freezed == showNew
          ? _value.showNew
          : showNew // ignore: cast_nullable_to_non_nullable
              as bool?,
      advancedCourseInfo: freezed == advancedCourseInfo
          ? _value.advancedCourseInfo
          : advancedCourseInfo // ignore: cast_nullable_to_non_nullable
              as AdvancedCourseInfo?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapePictureUrl: freezed == landscapePictureUrl
          ? _value.landscapePictureUrl
          : landscapePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      auditId: freezed == auditId
          ? _value.auditId
          : auditId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      startLessonDesc: freezed == startLessonDesc
          ? _value.startLessonDesc
          : startLessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideBo: freezed == newGetGuideBo
          ? _value.newGetGuideBo
          : newGetGuideBo // ignore: cast_nullable_to_non_nullable
              as NewGetGuideInfo?,
      taskTagBo: freezed == taskTagBo
          ? _value.taskTagBo
          : taskTagBo // ignore: cast_nullable_to_non_nullable
              as TaskTagBo?,
      lessonCardData: freezed == lessonCardData
          ? _value.lessonCardData
          : lessonCardData // ignore: cast_nullable_to_non_nullable
              as LessonCardData?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      trackExtendInfoList: freezed == trackExtendInfoList
          ? _value.trackExtendInfoList
          : trackExtendInfoList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource {
    if (_value.lessonResource == null) {
      return null;
    }

    return $CourseHomePageLessonResourceDataCopyWith<$Res>(
        _value.lessonResource!, (value) {
      return _then(_value.copyWith(lessonResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource {
    if (_value.courseResource == null) {
      return null;
    }

    return $CourseHomePageCourseResourceDataCopyWith<$Res>(
        _value.courseResource!, (value) {
      return _then(_value.copyWith(courseResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TrainTookitVoCopyWith<$Res>? get trainToolkitVo {
    if (_value.trainToolkitVo == null) {
      return null;
    }

    return $TrainTookitVoCopyWith<$Res>(_value.trainToolkitVo!, (value) {
      return _then(_value.copyWith(trainToolkitVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DrainageCourseCardInfoCopyWith<$Res>? get courseInfo {
    if (_value.courseInfo == null) {
      return null;
    }

    return $DrainageCourseCardInfoCopyWith<$Res>(_value.courseInfo!, (value) {
      return _then(_value.copyWith(courseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AdvancedCourseInfoCopyWith<$Res>? get advancedCourseInfo {
    if (_value.advancedCourseInfo == null) {
      return null;
    }

    return $AdvancedCourseInfoCopyWith<$Res>(_value.advancedCourseInfo!,
        (value) {
      return _then(_value.copyWith(advancedCourseInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $NewGetGuideInfoCopyWith<$Res>? get newGetGuideBo {
    if (_value.newGetGuideBo == null) {
      return null;
    }

    return $NewGetGuideInfoCopyWith<$Res>(_value.newGetGuideBo!, (value) {
      return _then(_value.copyWith(newGetGuideBo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TaskTagBoCopyWith<$Res>? get taskTagBo {
    if (_value.taskTagBo == null) {
      return null;
    }

    return $TaskTagBoCopyWith<$Res>(_value.taskTagBo!, (value) {
      return _then(_value.copyWith(taskTagBo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LessonCardDataCopyWith<$Res>? get lessonCardData {
    if (_value.lessonCardData == null) {
      return null;
    }

    return $LessonCardDataCopyWith<$Res>(_value.lessonCardData!, (value) {
      return _then(_value.copyWith(lessonCardData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ScheduleTaskBoCopyWith<$Res>
    implements $ScheduleTaskBoCopyWith<$Res> {
  factory _$$_ScheduleTaskBoCopyWith(
          _$_ScheduleTaskBo value, $Res Function(_$_ScheduleTaskBo) then) =
      __$$_ScheduleTaskBoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskStatus,
      int? materialId,
      int? sortLessonOrder,
      String? materialName,
      int? configId,
      String? taskStatusDesc,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? joinClassTime,
      int? taskType,
      String? taskTypeDesc,
      int? classId,
      String? scheduleTaskId,
      int? courseId,
      String? courseKey,
      int? lessonId,
      int? subjectType,
      String? subjectTypeDesc,
      int? segmentId,
      int? weekId,
      int? unlockType,
      String? title,
      String? subTitle,
      String? route,
      String? icon,
      String? lessonLabel,
      String? lessonOrder,
      String? lessonSubDesc,
      String? lessonName,
      int? classStatus,
      String? bannerImage,
      String? padBannerImage,
      int? bannerType,
      int? bannerNum,
      String? lessonCoverImage,
      String? courseCoverImage,
      String? image,
      String? padImage,
      int? courseType,
      String? courseSegmentName,
      String? userCourseBusinessStatus,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<LessonServiceList>? lessonServiceList,
      List<ChildrenTaskList>? trainScheduleTasks,
      List<ChildrenTaskList>? childrenTaskList,
      List<LessonPopData>? lessonPopList,
      TrainTookitVo? trainToolkitVo,
      bool? newGetFlag,
      List<TrainCampTipData>? trainNewGetGuideList,
      String? btnText,
      String? pictureUrl,
      String? backgroundColor,
      String? linkUrl,
      String? missionMaterialType,
      String? missionBusinessTypeName,
      bool? expired,
      String? description,
      String? titleColor,
      String? descriptionColor,
      String? missionMaterialTypeName,
      String? missionTypeName,
      String? businessTagId,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      List<SubjectInfo>? subjectInfoList,
      String? gradeTitle,
      int? gradeKey,
      bool? lastDrainageCourseLessonTask,
      DrainageCourseCardInfo? courseInfo,
      bool? needPop,
      bool? showNew,
      AdvancedCourseInfo? advancedCourseInfo,
      String? buttonDescription,
      String? landscapePictureUrl,
      int? auditId,
      String? courseLabel,
      String? startLessonDesc,
      String? bgImage,
      String? userCourseId,
      NewGetGuideInfo? newGetGuideBo,
      TaskTagBo? taskTagBo,
      LessonCardData? lessonCardData,
      String? unactivatedReason,
      List<String>? trackExtendInfoList});

  @override
  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource;
  @override
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource;
  @override
  $TrainTookitVoCopyWith<$Res>? get trainToolkitVo;
  @override
  $DrainageCourseCardInfoCopyWith<$Res>? get courseInfo;
  @override
  $AdvancedCourseInfoCopyWith<$Res>? get advancedCourseInfo;
  @override
  $NewGetGuideInfoCopyWith<$Res>? get newGetGuideBo;
  @override
  $TaskTagBoCopyWith<$Res>? get taskTagBo;
  @override
  $LessonCardDataCopyWith<$Res>? get lessonCardData;
}

/// @nodoc
class __$$_ScheduleTaskBoCopyWithImpl<$Res>
    extends _$ScheduleTaskBoCopyWithImpl<$Res, _$_ScheduleTaskBo>
    implements _$$_ScheduleTaskBoCopyWith<$Res> {
  __$$_ScheduleTaskBoCopyWithImpl(
      _$_ScheduleTaskBo _value, $Res Function(_$_ScheduleTaskBo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? materialId = freezed,
    Object? sortLessonOrder = freezed,
    Object? materialName = freezed,
    Object? configId = freezed,
    Object? taskStatusDesc = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? joinClassTime = freezed,
    Object? taskType = freezed,
    Object? taskTypeDesc = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonOrder = freezed,
    Object? lessonSubDesc = freezed,
    Object? lessonName = freezed,
    Object? classStatus = freezed,
    Object? bannerImage = freezed,
    Object? padBannerImage = freezed,
    Object? bannerType = freezed,
    Object? bannerNum = freezed,
    Object? lessonCoverImage = freezed,
    Object? courseCoverImage = freezed,
    Object? image = freezed,
    Object? padImage = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? lessonResource = freezed,
    Object? courseResource = freezed,
    Object? lessonServiceList = freezed,
    Object? trainScheduleTasks = freezed,
    Object? childrenTaskList = freezed,
    Object? lessonPopList = freezed,
    Object? trainToolkitVo = freezed,
    Object? newGetFlag = freezed,
    Object? trainNewGetGuideList = freezed,
    Object? btnText = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? linkUrl = freezed,
    Object? missionMaterialType = freezed,
    Object? missionBusinessTypeName = freezed,
    Object? expired = freezed,
    Object? description = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? missionMaterialTypeName = freezed,
    Object? missionTypeName = freezed,
    Object? businessTagId = freezed,
    Object? subjectInfoList = freezed,
    Object? gradeTitle = freezed,
    Object? gradeKey = freezed,
    Object? lastDrainageCourseLessonTask = freezed,
    Object? courseInfo = freezed,
    Object? needPop = freezed,
    Object? showNew = freezed,
    Object? advancedCourseInfo = freezed,
    Object? buttonDescription = freezed,
    Object? landscapePictureUrl = freezed,
    Object? auditId = freezed,
    Object? courseLabel = freezed,
    Object? startLessonDesc = freezed,
    Object? bgImage = freezed,
    Object? userCourseId = freezed,
    Object? newGetGuideBo = freezed,
    Object? taskTagBo = freezed,
    Object? lessonCardData = freezed,
    Object? unactivatedReason = freezed,
    Object? trackExtendInfoList = freezed,
  }) {
    return _then(_$_ScheduleTaskBo(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      sortLessonOrder: freezed == sortLessonOrder
          ? _value.sortLessonOrder
          : sortLessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatusDesc: freezed == taskStatusDesc
          ? _value.taskStatusDesc
          : taskStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      joinClassTime: freezed == joinClassTime
          ? _value.joinClassTime
          : joinClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskTypeDesc: freezed == taskTypeDesc
          ? _value.taskTypeDesc
          : taskTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubDesc: freezed == lessonSubDesc
          ? _value.lessonSubDesc
          : lessonSubDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      padBannerImage: freezed == padBannerImage
          ? _value.padBannerImage
          : padBannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerType: freezed == bannerType
          ? _value.bannerType
          : bannerType // ignore: cast_nullable_to_non_nullable
              as int?,
      bannerNum: freezed == bannerNum
          ? _value.bannerNum
          : bannerNum // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCoverImage: freezed == courseCoverImage
          ? _value.courseCoverImage
          : courseCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      padImage: freezed == padImage
          ? _value.padImage
          : padImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonResource: freezed == lessonResource
          ? _value.lessonResource
          : lessonResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageLessonResourceData?,
      courseResource: freezed == courseResource
          ? _value.courseResource
          : courseResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageCourseResourceData?,
      lessonServiceList: freezed == lessonServiceList
          ? _value.lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceList>?,
      trainScheduleTasks: freezed == trainScheduleTasks
          ? _value.trainScheduleTasks
          : trainScheduleTasks // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
      lessonPopList: freezed == lessonPopList
          ? _value.lessonPopList
          : lessonPopList // ignore: cast_nullable_to_non_nullable
              as List<LessonPopData>?,
      trainToolkitVo: freezed == trainToolkitVo
          ? _value.trainToolkitVo
          : trainToolkitVo // ignore: cast_nullable_to_non_nullable
              as TrainTookitVo?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      trainNewGetGuideList: freezed == trainNewGetGuideList
          ? _value.trainNewGetGuideList
          : trainNewGetGuideList // ignore: cast_nullable_to_non_nullable
              as List<TrainCampTipData>?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      missionBusinessTypeName: freezed == missionBusinessTypeName
          ? _value.missionBusinessTypeName
          : missionBusinessTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      expired: freezed == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialTypeName: freezed == missionMaterialTypeName
          ? _value.missionMaterialTypeName
          : missionMaterialTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      missionTypeName: freezed == missionTypeName
          ? _value.missionTypeName
          : missionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value.subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectInfo>?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      lastDrainageCourseLessonTask: freezed == lastDrainageCourseLessonTask
          ? _value.lastDrainageCourseLessonTask
          : lastDrainageCourseLessonTask // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseInfo: freezed == courseInfo
          ? _value.courseInfo
          : courseInfo // ignore: cast_nullable_to_non_nullable
              as DrainageCourseCardInfo?,
      needPop: freezed == needPop
          ? _value.needPop
          : needPop // ignore: cast_nullable_to_non_nullable
              as bool?,
      showNew: freezed == showNew
          ? _value.showNew
          : showNew // ignore: cast_nullable_to_non_nullable
              as bool?,
      advancedCourseInfo: freezed == advancedCourseInfo
          ? _value.advancedCourseInfo
          : advancedCourseInfo // ignore: cast_nullable_to_non_nullable
              as AdvancedCourseInfo?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapePictureUrl: freezed == landscapePictureUrl
          ? _value.landscapePictureUrl
          : landscapePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      auditId: freezed == auditId
          ? _value.auditId
          : auditId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      startLessonDesc: freezed == startLessonDesc
          ? _value.startLessonDesc
          : startLessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideBo: freezed == newGetGuideBo
          ? _value.newGetGuideBo
          : newGetGuideBo // ignore: cast_nullable_to_non_nullable
              as NewGetGuideInfo?,
      taskTagBo: freezed == taskTagBo
          ? _value.taskTagBo
          : taskTagBo // ignore: cast_nullable_to_non_nullable
              as TaskTagBo?,
      lessonCardData: freezed == lessonCardData
          ? _value.lessonCardData
          : lessonCardData // ignore: cast_nullable_to_non_nullable
              as LessonCardData?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      trackExtendInfoList: freezed == trackExtendInfoList
          ? _value.trackExtendInfoList
          : trackExtendInfoList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleTaskBo implements _ScheduleTaskBo {
  _$_ScheduleTaskBo(
      {this.taskStatus,
      this.materialId,
      this.sortLessonOrder,
      this.materialName,
      this.configId,
      this.taskStatusDesc,
      this.showDateTime,
      this.startClassTime,
      this.firstLessonStartTime,
      this.joinClassTime,
      this.taskType,
      this.taskTypeDesc,
      this.classId,
      this.scheduleTaskId,
      this.courseId,
      this.courseKey,
      this.lessonId,
      this.subjectType,
      this.subjectTypeDesc,
      this.segmentId,
      this.weekId,
      this.unlockType,
      this.title,
      this.subTitle,
      this.route,
      this.icon,
      this.lessonLabel,
      this.lessonOrder,
      this.lessonSubDesc,
      this.lessonName,
      this.classStatus,
      this.bannerImage,
      this.padBannerImage,
      this.bannerType,
      this.bannerNum,
      this.lessonCoverImage,
      this.courseCoverImage,
      this.image,
      this.padImage,
      this.courseType,
      this.courseSegmentName,
      this.userCourseBusinessStatus,
      this.lessonResource,
      this.courseResource,
      this.lessonServiceList,
      this.trainScheduleTasks,
      this.childrenTaskList,
      this.lessonPopList,
      this.trainToolkitVo,
      this.newGetFlag,
      this.trainNewGetGuideList,
      this.btnText,
      this.pictureUrl,
      this.backgroundColor,
      this.linkUrl,
      this.missionMaterialType,
      this.missionBusinessTypeName,
      this.expired,
      this.description,
      this.titleColor,
      this.descriptionColor,
      this.missionMaterialTypeName,
      this.missionTypeName,
      this.businessTagId,
      @JsonKey(name: "drainageCourseReceiveCardDataBo") this.subjectInfoList,
      this.gradeTitle,
      this.gradeKey,
      this.lastDrainageCourseLessonTask,
      this.courseInfo,
      this.needPop,
      this.showNew,
      this.advancedCourseInfo,
      this.buttonDescription,
      this.landscapePictureUrl,
      this.auditId,
      this.courseLabel,
      this.startLessonDesc,
      this.bgImage,
      this.userCourseId,
      this.newGetGuideBo,
      this.taskTagBo,
      this.lessonCardData,
      this.unactivatedReason,
      this.trackExtendInfoList});

  factory _$_ScheduleTaskBo.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleTaskBoFromJson(json);

// @Default(false)
// bool isExpanded,
  @override
  int? taskStatus;
  @override
  int? materialId;
  @override
  int? sortLessonOrder;
  @override
  String? materialName;
  @override
  int? configId;
  @override
  String? taskStatusDesc;
  @override
  int? showDateTime;
  @override
  int? startClassTime;
  @override
  int? firstLessonStartTime;
  @override
  int? joinClassTime;
  @override
  int? taskType;
  @override
  String? taskTypeDesc;
  @override
  int? classId;
  @override
  String? scheduleTaskId;
  @override
  int? courseId;
  @override
  String? courseKey;
  @override
  int? lessonId;
  @override
  int? subjectType;
  @override
  String? subjectTypeDesc;
  @override
  int? segmentId;
  @override
  int? weekId;
  @override
  int? unlockType;
  @override
  String? title;
  @override
  String? subTitle;
  @override
  String? route;
  @override
  String? icon;
  @override
  String? lessonLabel;
  @override
  String? lessonOrder;
  @override
  String? lessonSubDesc;
  @override
  String? lessonName;
  @override
  int? classStatus;
  @override
  String? bannerImage;
  @override
  String? padBannerImage;
  @override
  int? bannerType;
  @override
  int? bannerNum;
  @override
  String? lessonCoverImage;
  @override
  String? courseCoverImage;
  @override
  String? image;
  @override
  String? padImage;
  @override
  int? courseType;
  @override
  String? courseSegmentName;
  @override
  String? userCourseBusinessStatus;
  @override
  CourseHomePageLessonResourceData? lessonResource;
  @override
  CourseHomePageCourseResourceData? courseResource;
  @override
  List<LessonServiceList>? lessonServiceList;
  @override
  List<ChildrenTaskList>? trainScheduleTasks;
  @override
  List<ChildrenTaskList>? childrenTaskList;
  @override
  List<LessonPopData>? lessonPopList;
  @override
  TrainTookitVo? trainToolkitVo;
  @override
  bool? newGetFlag;
  @override
  List<TrainCampTipData>? trainNewGetGuideList;
  @override
  String? btnText;
  @override
  String? pictureUrl;
  @override
  String? backgroundColor;
  @override
  String? linkUrl;
  @override
  String? missionMaterialType;
  @override
  String? missionBusinessTypeName;
  @override
  bool? expired;
  @override
  String? description;
  @override
  String? titleColor;
  @override
  String? descriptionColor;
  @override
  String? missionMaterialTypeName;
  @override
  String? missionTypeName;
  @override
  String? businessTagId;
  @override
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfo>? subjectInfoList;
  @override
  String? gradeTitle;
  @override
  int? gradeKey;
  @override
  bool? lastDrainageCourseLessonTask;
  @override
  DrainageCourseCardInfo? courseInfo;
  @override
  bool? needPop;
  @override
  bool? showNew;
  @override
  AdvancedCourseInfo? advancedCourseInfo;
  @override
  String? buttonDescription;
  @override
  String? landscapePictureUrl;
  @override
  int? auditId;
  @override
  String? courseLabel;
  @override
  String? startLessonDesc;
//背景图片
  @override
  String? bgImage;
  @override
  String? userCourseId;
  @override
  NewGetGuideInfo? newGetGuideBo;
  @override
  TaskTagBo? taskTagBo;

  ///训练营年课 统一的卡片数据处理 源自本地可供ui 直接显示 后续如果上课页 所有卡片
  ///都进行中转处理 LessonCardData 可以直接作为上课页卡片显示的ui 数据，不必放在ScheduleTaskBo
  @override
  LessonCardData? lessonCardData;

  ///未激活原因
  @override
  String? unactivatedReason;
  @override
  List<String>? trackExtendInfoList;

  @override
  String toString() {
    return 'ScheduleTaskBo(taskStatus: $taskStatus, materialId: $materialId, sortLessonOrder: $sortLessonOrder, materialName: $materialName, configId: $configId, taskStatusDesc: $taskStatusDesc, showDateTime: $showDateTime, startClassTime: $startClassTime, firstLessonStartTime: $firstLessonStartTime, joinClassTime: $joinClassTime, taskType: $taskType, taskTypeDesc: $taskTypeDesc, classId: $classId, scheduleTaskId: $scheduleTaskId, courseId: $courseId, courseKey: $courseKey, lessonId: $lessonId, subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, segmentId: $segmentId, weekId: $weekId, unlockType: $unlockType, title: $title, subTitle: $subTitle, route: $route, icon: $icon, lessonLabel: $lessonLabel, lessonOrder: $lessonOrder, lessonSubDesc: $lessonSubDesc, lessonName: $lessonName, classStatus: $classStatus, bannerImage: $bannerImage, padBannerImage: $padBannerImage, bannerType: $bannerType, bannerNum: $bannerNum, lessonCoverImage: $lessonCoverImage, courseCoverImage: $courseCoverImage, image: $image, padImage: $padImage, courseType: $courseType, courseSegmentName: $courseSegmentName, userCourseBusinessStatus: $userCourseBusinessStatus, lessonResource: $lessonResource, courseResource: $courseResource, lessonServiceList: $lessonServiceList, trainScheduleTasks: $trainScheduleTasks, childrenTaskList: $childrenTaskList, lessonPopList: $lessonPopList, trainToolkitVo: $trainToolkitVo, newGetFlag: $newGetFlag, trainNewGetGuideList: $trainNewGetGuideList, btnText: $btnText, pictureUrl: $pictureUrl, backgroundColor: $backgroundColor, linkUrl: $linkUrl, missionMaterialType: $missionMaterialType, missionBusinessTypeName: $missionBusinessTypeName, expired: $expired, description: $description, titleColor: $titleColor, descriptionColor: $descriptionColor, missionMaterialTypeName: $missionMaterialTypeName, missionTypeName: $missionTypeName, businessTagId: $businessTagId, subjectInfoList: $subjectInfoList, gradeTitle: $gradeTitle, gradeKey: $gradeKey, lastDrainageCourseLessonTask: $lastDrainageCourseLessonTask, courseInfo: $courseInfo, needPop: $needPop, showNew: $showNew, advancedCourseInfo: $advancedCourseInfo, buttonDescription: $buttonDescription, landscapePictureUrl: $landscapePictureUrl, auditId: $auditId, courseLabel: $courseLabel, startLessonDesc: $startLessonDesc, bgImage: $bgImage, userCourseId: $userCourseId, newGetGuideBo: $newGetGuideBo, taskTagBo: $taskTagBo, lessonCardData: $lessonCardData, unactivatedReason: $unactivatedReason, trackExtendInfoList: $trackExtendInfoList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleTaskBoCopyWith<_$_ScheduleTaskBo> get copyWith =>
      __$$_ScheduleTaskBoCopyWithImpl<_$_ScheduleTaskBo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleTaskBoToJson(
      this,
    );
  }
}

abstract class _ScheduleTaskBo implements ScheduleTaskBo {
  factory _ScheduleTaskBo(
      {int? taskStatus,
      int? materialId,
      int? sortLessonOrder,
      String? materialName,
      int? configId,
      String? taskStatusDesc,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? joinClassTime,
      int? taskType,
      String? taskTypeDesc,
      int? classId,
      String? scheduleTaskId,
      int? courseId,
      String? courseKey,
      int? lessonId,
      int? subjectType,
      String? subjectTypeDesc,
      int? segmentId,
      int? weekId,
      int? unlockType,
      String? title,
      String? subTitle,
      String? route,
      String? icon,
      String? lessonLabel,
      String? lessonOrder,
      String? lessonSubDesc,
      String? lessonName,
      int? classStatus,
      String? bannerImage,
      String? padBannerImage,
      int? bannerType,
      int? bannerNum,
      String? lessonCoverImage,
      String? courseCoverImage,
      String? image,
      String? padImage,
      int? courseType,
      String? courseSegmentName,
      String? userCourseBusinessStatus,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<LessonServiceList>? lessonServiceList,
      List<ChildrenTaskList>? trainScheduleTasks,
      List<ChildrenTaskList>? childrenTaskList,
      List<LessonPopData>? lessonPopList,
      TrainTookitVo? trainToolkitVo,
      bool? newGetFlag,
      List<TrainCampTipData>? trainNewGetGuideList,
      String? btnText,
      String? pictureUrl,
      String? backgroundColor,
      String? linkUrl,
      String? missionMaterialType,
      String? missionBusinessTypeName,
      bool? expired,
      String? description,
      String? titleColor,
      String? descriptionColor,
      String? missionMaterialTypeName,
      String? missionTypeName,
      String? businessTagId,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      List<SubjectInfo>? subjectInfoList,
      String? gradeTitle,
      int? gradeKey,
      bool? lastDrainageCourseLessonTask,
      DrainageCourseCardInfo? courseInfo,
      bool? needPop,
      bool? showNew,
      AdvancedCourseInfo? advancedCourseInfo,
      String? buttonDescription,
      String? landscapePictureUrl,
      int? auditId,
      String? courseLabel,
      String? startLessonDesc,
      String? bgImage,
      String? userCourseId,
      NewGetGuideInfo? newGetGuideBo,
      TaskTagBo? taskTagBo,
      LessonCardData? lessonCardData,
      String? unactivatedReason,
      List<String>? trackExtendInfoList}) = _$_ScheduleTaskBo;

  factory _ScheduleTaskBo.fromJson(Map<String, dynamic> json) =
      _$_ScheduleTaskBo.fromJson;

  @override // @Default(false)
// bool isExpanded,
  int? get taskStatus; // @Default(false)
// bool isExpanded,
  set taskStatus(int? value);
  @override
  int? get materialId;
  set materialId(int? value);
  @override
  int? get sortLessonOrder;
  set sortLessonOrder(int? value);
  @override
  String? get materialName;
  set materialName(String? value);
  @override
  int? get configId;
  set configId(int? value);
  @override
  String? get taskStatusDesc;
  set taskStatusDesc(String? value);
  @override
  int? get showDateTime;
  set showDateTime(int? value);
  @override
  int? get startClassTime;
  set startClassTime(int? value);
  @override
  int? get firstLessonStartTime;
  set firstLessonStartTime(int? value);
  @override
  int? get joinClassTime;
  set joinClassTime(int? value);
  @override
  int? get taskType;
  set taskType(int? value);
  @override
  String? get taskTypeDesc;
  set taskTypeDesc(String? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  String? get scheduleTaskId;
  set scheduleTaskId(String? value);
  @override
  int? get courseId;
  set courseId(int? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override
  int? get lessonId;
  set lessonId(int? value);
  @override
  int? get subjectType;
  set subjectType(int? value);
  @override
  String? get subjectTypeDesc;
  set subjectTypeDesc(String? value);
  @override
  int? get segmentId;
  set segmentId(int? value);
  @override
  int? get weekId;
  set weekId(int? value);
  @override
  int? get unlockType;
  set unlockType(int? value);
  @override
  String? get title;
  set title(String? value);
  @override
  String? get subTitle;
  set subTitle(String? value);
  @override
  String? get route;
  set route(String? value);
  @override
  String? get icon;
  set icon(String? value);
  @override
  String? get lessonLabel;
  set lessonLabel(String? value);
  @override
  String? get lessonOrder;
  set lessonOrder(String? value);
  @override
  String? get lessonSubDesc;
  set lessonSubDesc(String? value);
  @override
  String? get lessonName;
  set lessonName(String? value);
  @override
  int? get classStatus;
  set classStatus(int? value);
  @override
  String? get bannerImage;
  set bannerImage(String? value);
  @override
  String? get padBannerImage;
  set padBannerImage(String? value);
  @override
  int? get bannerType;
  set bannerType(int? value);
  @override
  int? get bannerNum;
  set bannerNum(int? value);
  @override
  String? get lessonCoverImage;
  set lessonCoverImage(String? value);
  @override
  String? get courseCoverImage;
  set courseCoverImage(String? value);
  @override
  String? get image;
  set image(String? value);
  @override
  String? get padImage;
  set padImage(String? value);
  @override
  int? get courseType;
  set courseType(int? value);
  @override
  String? get courseSegmentName;
  set courseSegmentName(String? value);
  @override
  String? get userCourseBusinessStatus;
  set userCourseBusinessStatus(String? value);
  @override
  CourseHomePageLessonResourceData? get lessonResource;
  set lessonResource(CourseHomePageLessonResourceData? value);
  @override
  CourseHomePageCourseResourceData? get courseResource;
  set courseResource(CourseHomePageCourseResourceData? value);
  @override
  List<LessonServiceList>? get lessonServiceList;
  set lessonServiceList(List<LessonServiceList>? value);
  @override
  List<ChildrenTaskList>? get trainScheduleTasks;
  set trainScheduleTasks(List<ChildrenTaskList>? value);
  @override
  List<ChildrenTaskList>? get childrenTaskList;
  set childrenTaskList(List<ChildrenTaskList>? value);
  @override
  List<LessonPopData>? get lessonPopList;
  set lessonPopList(List<LessonPopData>? value);
  @override
  TrainTookitVo? get trainToolkitVo;
  set trainToolkitVo(TrainTookitVo? value);
  @override
  bool? get newGetFlag;
  set newGetFlag(bool? value);
  @override
  List<TrainCampTipData>? get trainNewGetGuideList;
  set trainNewGetGuideList(List<TrainCampTipData>? value);
  @override
  String? get btnText;
  set btnText(String? value);
  @override
  String? get pictureUrl;
  set pictureUrl(String? value);
  @override
  String? get backgroundColor;
  set backgroundColor(String? value);
  @override
  String? get linkUrl;
  set linkUrl(String? value);
  @override
  String? get missionMaterialType;
  set missionMaterialType(String? value);
  @override
  String? get missionBusinessTypeName;
  set missionBusinessTypeName(String? value);
  @override
  bool? get expired;
  set expired(bool? value);
  @override
  String? get description;
  set description(String? value);
  @override
  String? get titleColor;
  set titleColor(String? value);
  @override
  String? get descriptionColor;
  set descriptionColor(String? value);
  @override
  String? get missionMaterialTypeName;
  set missionMaterialTypeName(String? value);
  @override
  String? get missionTypeName;
  set missionTypeName(String? value);
  @override
  String? get businessTagId;
  set businessTagId(String? value);
  @override
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfo>? get subjectInfoList;
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  set subjectInfoList(List<SubjectInfo>? value);
  @override
  String? get gradeTitle;
  set gradeTitle(String? value);
  @override
  int? get gradeKey;
  set gradeKey(int? value);
  @override
  bool? get lastDrainageCourseLessonTask;
  set lastDrainageCourseLessonTask(bool? value);
  @override
  DrainageCourseCardInfo? get courseInfo;
  set courseInfo(DrainageCourseCardInfo? value);
  @override
  bool? get needPop;
  set needPop(bool? value);
  @override
  bool? get showNew;
  set showNew(bool? value);
  @override
  AdvancedCourseInfo? get advancedCourseInfo;
  set advancedCourseInfo(AdvancedCourseInfo? value);
  @override
  String? get buttonDescription;
  set buttonDescription(String? value);
  @override
  String? get landscapePictureUrl;
  set landscapePictureUrl(String? value);
  @override
  int? get auditId;
  set auditId(int? value);
  @override
  String? get courseLabel;
  set courseLabel(String? value);
  @override
  String? get startLessonDesc;
  set startLessonDesc(String? value);
  @override //背景图片
  String? get bgImage; //背景图片
  set bgImage(String? value);
  @override
  String? get userCourseId;
  set userCourseId(String? value);
  @override
  NewGetGuideInfo? get newGetGuideBo;
  set newGetGuideBo(NewGetGuideInfo? value);
  @override
  TaskTagBo? get taskTagBo;
  set taskTagBo(TaskTagBo? value);
  @override

  ///训练营年课 统一的卡片数据处理 源自本地可供ui 直接显示 后续如果上课页 所有卡片
  ///都进行中转处理 LessonCardData 可以直接作为上课页卡片显示的ui 数据，不必放在ScheduleTaskBo
  LessonCardData? get lessonCardData;

  ///训练营年课 统一的卡片数据处理 源自本地可供ui 直接显示 后续如果上课页 所有卡片
  ///都进行中转处理 LessonCardData 可以直接作为上课页卡片显示的ui 数据，不必放在ScheduleTaskBo
  set lessonCardData(LessonCardData? value);
  @override

  ///未激活原因
  String? get unactivatedReason;

  ///未激活原因
  set unactivatedReason(String? value);
  @override
  List<String>? get trackExtendInfoList;
  set trackExtendInfoList(List<String>? value);
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleTaskBoCopyWith<_$_ScheduleTaskBo> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonPopData _$LessonPopDataFromJson(Map<String, dynamic> json) {
  return _LessonPopData.fromJson(json);
}

/// @nodoc
mixin _$LessonPopData {
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get buttonDesc => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonPopDataCopyWith<LessonPopData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonPopDataCopyWith<$Res> {
  factory $LessonPopDataCopyWith(
          LessonPopData value, $Res Function(LessonPopData) then) =
      _$LessonPopDataCopyWithImpl<$Res, LessonPopData>;
  @useResult
  $Res call(
      {String? title,
      String? subTitle,
      String? icon,
      String? route,
      String? buttonDesc,
      int? type});
}

/// @nodoc
class _$LessonPopDataCopyWithImpl<$Res, $Val extends LessonPopData>
    implements $LessonPopDataCopyWith<$Res> {
  _$LessonPopDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? buttonDesc = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDesc: freezed == buttonDesc
          ? _value.buttonDesc
          : buttonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonPopDataCopyWith<$Res>
    implements $LessonPopDataCopyWith<$Res> {
  factory _$$_LessonPopDataCopyWith(
          _$_LessonPopData value, $Res Function(_$_LessonPopData) then) =
      __$$_LessonPopDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? subTitle,
      String? icon,
      String? route,
      String? buttonDesc,
      int? type});
}

/// @nodoc
class __$$_LessonPopDataCopyWithImpl<$Res>
    extends _$LessonPopDataCopyWithImpl<$Res, _$_LessonPopData>
    implements _$$_LessonPopDataCopyWith<$Res> {
  __$$_LessonPopDataCopyWithImpl(
      _$_LessonPopData _value, $Res Function(_$_LessonPopData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? buttonDesc = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_LessonPopData(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDesc: freezed == buttonDesc
          ? _value.buttonDesc
          : buttonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonPopData implements _LessonPopData {
  const _$_LessonPopData(
      {this.title,
      this.subTitle,
      this.icon,
      this.route,
      this.buttonDesc,
      this.type});

  factory _$_LessonPopData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonPopDataFromJson(json);

  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final String? icon;
  @override
  final String? route;
  @override
  final String? buttonDesc;
  @override
  final int? type;

  @override
  String toString() {
    return 'LessonPopData(title: $title, subTitle: $subTitle, icon: $icon, route: $route, buttonDesc: $buttonDesc, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonPopData &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.buttonDesc, buttonDesc) ||
                other.buttonDesc == buttonDesc) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, title, subTitle, icon, route, buttonDesc, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonPopDataCopyWith<_$_LessonPopData> get copyWith =>
      __$$_LessonPopDataCopyWithImpl<_$_LessonPopData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonPopDataToJson(
      this,
    );
  }
}

abstract class _LessonPopData implements LessonPopData {
  const factory _LessonPopData(
      {final String? title,
      final String? subTitle,
      final String? icon,
      final String? route,
      final String? buttonDesc,
      final int? type}) = _$_LessonPopData;

  factory _LessonPopData.fromJson(Map<String, dynamic> json) =
      _$_LessonPopData.fromJson;

  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  String? get icon;
  @override
  String? get route;
  @override
  String? get buttonDesc;
  @override
  int? get type;
  @override
  @JsonKey(ignore: true)
  _$$_LessonPopDataCopyWith<_$_LessonPopData> get copyWith =>
      throw _privateConstructorUsedError;
}

DrainageCourseCardInfo _$DrainageCourseCardInfoFromJson(
    Map<String, dynamic> json) {
  return _DrainageCourseCardInfo.fromJson(json);
}

/// @nodoc
mixin _$DrainageCourseCardInfo {
  String? get courseSegmentDesc => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;
  int? get countdownDeadlineTime => throw _privateConstructorUsedError;
  String? get courseDesc => throw _privateConstructorUsedError;
  String? get courseId => throw _privateConstructorUsedError;
  String? get courseTag => throw _privateConstructorUsedError;
  int? get courseTagKey => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseTypeDesc => throw _privateConstructorUsedError;
  String? get courseStageDesc => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  List<DrainageCourseLessonInfo>? get lessonInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DrainageCourseCardInfoCopyWith<DrainageCourseCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DrainageCourseCardInfoCopyWith<$Res> {
  factory $DrainageCourseCardInfoCopyWith(DrainageCourseCardInfo value,
          $Res Function(DrainageCourseCardInfo) then) =
      _$DrainageCourseCardInfoCopyWithImpl<$Res, DrainageCourseCardInfo>;
  @useResult
  $Res call(
      {String? courseSegmentDesc,
      int? courseSegmentCode,
      int? countdownDeadlineTime,
      String? courseDesc,
      String? courseId,
      String? courseTag,
      int? courseTagKey,
      int? courseType,
      String? courseTypeDesc,
      String? courseStageDesc,
      String? skuId,
      String? skuName,
      List<DrainageCourseLessonInfo>? lessonInfoList});
}

/// @nodoc
class _$DrainageCourseCardInfoCopyWithImpl<$Res,
        $Val extends DrainageCourseCardInfo>
    implements $DrainageCourseCardInfoCopyWith<$Res> {
  _$DrainageCourseCardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentDesc = freezed,
    Object? courseSegmentCode = freezed,
    Object? countdownDeadlineTime = freezed,
    Object? courseDesc = freezed,
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? courseType = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? lessonInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      courseSegmentDesc: freezed == courseSegmentDesc
          ? _value.courseSegmentDesc
          : courseSegmentDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      countdownDeadlineTime: freezed == countdownDeadlineTime
          ? _value.countdownDeadlineTime
          : countdownDeadlineTime // ignore: cast_nullable_to_non_nullable
              as int?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonInfoList: freezed == lessonInfoList
          ? _value.lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageCourseLessonInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DrainageCourseCardInfoCopyWith<$Res>
    implements $DrainageCourseCardInfoCopyWith<$Res> {
  factory _$$_DrainageCourseCardInfoCopyWith(_$_DrainageCourseCardInfo value,
          $Res Function(_$_DrainageCourseCardInfo) then) =
      __$$_DrainageCourseCardInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseSegmentDesc,
      int? courseSegmentCode,
      int? countdownDeadlineTime,
      String? courseDesc,
      String? courseId,
      String? courseTag,
      int? courseTagKey,
      int? courseType,
      String? courseTypeDesc,
      String? courseStageDesc,
      String? skuId,
      String? skuName,
      List<DrainageCourseLessonInfo>? lessonInfoList});
}

/// @nodoc
class __$$_DrainageCourseCardInfoCopyWithImpl<$Res>
    extends _$DrainageCourseCardInfoCopyWithImpl<$Res,
        _$_DrainageCourseCardInfo>
    implements _$$_DrainageCourseCardInfoCopyWith<$Res> {
  __$$_DrainageCourseCardInfoCopyWithImpl(_$_DrainageCourseCardInfo _value,
      $Res Function(_$_DrainageCourseCardInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentDesc = freezed,
    Object? courseSegmentCode = freezed,
    Object? countdownDeadlineTime = freezed,
    Object? courseDesc = freezed,
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? courseType = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? lessonInfoList = freezed,
  }) {
    return _then(_$_DrainageCourseCardInfo(
      courseSegmentDesc: freezed == courseSegmentDesc
          ? _value.courseSegmentDesc
          : courseSegmentDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      countdownDeadlineTime: freezed == countdownDeadlineTime
          ? _value.countdownDeadlineTime
          : countdownDeadlineTime // ignore: cast_nullable_to_non_nullable
              as int?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonInfoList: freezed == lessonInfoList
          ? _value._lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageCourseLessonInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DrainageCourseCardInfo implements _DrainageCourseCardInfo {
  const _$_DrainageCourseCardInfo(
      {this.courseSegmentDesc,
      this.courseSegmentCode,
      this.countdownDeadlineTime,
      this.courseDesc,
      this.courseId,
      this.courseTag,
      this.courseTagKey,
      this.courseType,
      this.courseTypeDesc,
      this.courseStageDesc,
      this.skuId,
      this.skuName,
      final List<DrainageCourseLessonInfo>? lessonInfoList})
      : _lessonInfoList = lessonInfoList;

  factory _$_DrainageCourseCardInfo.fromJson(Map<String, dynamic> json) =>
      _$$_DrainageCourseCardInfoFromJson(json);

  @override
  final String? courseSegmentDesc;
  @override
  final int? courseSegmentCode;
  @override
  final int? countdownDeadlineTime;
  @override
  final String? courseDesc;
  @override
  final String? courseId;
  @override
  final String? courseTag;
  @override
  final int? courseTagKey;
  @override
  final int? courseType;
  @override
  final String? courseTypeDesc;
  @override
  final String? courseStageDesc;
  @override
  final String? skuId;
  @override
  final String? skuName;
  final List<DrainageCourseLessonInfo>? _lessonInfoList;
  @override
  List<DrainageCourseLessonInfo>? get lessonInfoList {
    final value = _lessonInfoList;
    if (value == null) return null;
    if (_lessonInfoList is EqualUnmodifiableListView) return _lessonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DrainageCourseCardInfo(courseSegmentDesc: $courseSegmentDesc, courseSegmentCode: $courseSegmentCode, countdownDeadlineTime: $countdownDeadlineTime, courseDesc: $courseDesc, courseId: $courseId, courseTag: $courseTag, courseTagKey: $courseTagKey, courseType: $courseType, courseTypeDesc: $courseTypeDesc, courseStageDesc: $courseStageDesc, skuId: $skuId, skuName: $skuName, lessonInfoList: $lessonInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DrainageCourseCardInfo &&
            (identical(other.courseSegmentDesc, courseSegmentDesc) ||
                other.courseSegmentDesc == courseSegmentDesc) &&
            (identical(other.courseSegmentCode, courseSegmentCode) ||
                other.courseSegmentCode == courseSegmentCode) &&
            (identical(other.countdownDeadlineTime, countdownDeadlineTime) ||
                other.countdownDeadlineTime == countdownDeadlineTime) &&
            (identical(other.courseDesc, courseDesc) ||
                other.courseDesc == courseDesc) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseTag, courseTag) ||
                other.courseTag == courseTag) &&
            (identical(other.courseTagKey, courseTagKey) ||
                other.courseTagKey == courseTagKey) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseTypeDesc, courseTypeDesc) ||
                other.courseTypeDesc == courseTypeDesc) &&
            (identical(other.courseStageDesc, courseStageDesc) ||
                other.courseStageDesc == courseStageDesc) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            const DeepCollectionEquality()
                .equals(other._lessonInfoList, _lessonInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseSegmentDesc,
      courseSegmentCode,
      countdownDeadlineTime,
      courseDesc,
      courseId,
      courseTag,
      courseTagKey,
      courseType,
      courseTypeDesc,
      courseStageDesc,
      skuId,
      skuName,
      const DeepCollectionEquality().hash(_lessonInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DrainageCourseCardInfoCopyWith<_$_DrainageCourseCardInfo> get copyWith =>
      __$$_DrainageCourseCardInfoCopyWithImpl<_$_DrainageCourseCardInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DrainageCourseCardInfoToJson(
      this,
    );
  }
}

abstract class _DrainageCourseCardInfo implements DrainageCourseCardInfo {
  const factory _DrainageCourseCardInfo(
          {final String? courseSegmentDesc,
          final int? courseSegmentCode,
          final int? countdownDeadlineTime,
          final String? courseDesc,
          final String? courseId,
          final String? courseTag,
          final int? courseTagKey,
          final int? courseType,
          final String? courseTypeDesc,
          final String? courseStageDesc,
          final String? skuId,
          final String? skuName,
          final List<DrainageCourseLessonInfo>? lessonInfoList}) =
      _$_DrainageCourseCardInfo;

  factory _DrainageCourseCardInfo.fromJson(Map<String, dynamic> json) =
      _$_DrainageCourseCardInfo.fromJson;

  @override
  String? get courseSegmentDesc;
  @override
  int? get courseSegmentCode;
  @override
  int? get countdownDeadlineTime;
  @override
  String? get courseDesc;
  @override
  String? get courseId;
  @override
  String? get courseTag;
  @override
  int? get courseTagKey;
  @override
  int? get courseType;
  @override
  String? get courseTypeDesc;
  @override
  String? get courseStageDesc;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  List<DrainageCourseLessonInfo>? get lessonInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_DrainageCourseCardInfoCopyWith<_$_DrainageCourseCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

DrainageCourseLessonInfo _$DrainageCourseLessonInfoFromJson(
    Map<String, dynamic> json) {
  return _DrainageCourseLessonInfo.fromJson(json);
}

/// @nodoc
mixin _$DrainageCourseLessonInfo {
  String? get coverPicUrl => throw _privateConstructorUsedError;
  String? get lessonId => throw _privateConstructorUsedError;
  String? get lessonOrderDesc => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  List<StepInfo>? get stepInfoList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DrainageCourseLessonInfoCopyWith<DrainageCourseLessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DrainageCourseLessonInfoCopyWith<$Res> {
  factory $DrainageCourseLessonInfoCopyWith(DrainageCourseLessonInfo value,
          $Res Function(DrainageCourseLessonInfo) then) =
      _$DrainageCourseLessonInfoCopyWithImpl<$Res, DrainageCourseLessonInfo>;
  @useResult
  $Res call(
      {String? coverPicUrl,
      String? lessonId,
      String? lessonOrderDesc,
      String? lessonName,
      List<StepInfo>? stepInfoList});
}

/// @nodoc
class _$DrainageCourseLessonInfoCopyWithImpl<$Res,
        $Val extends DrainageCourseLessonInfo>
    implements $DrainageCourseLessonInfoCopyWith<$Res> {
  _$DrainageCourseLessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coverPicUrl = freezed,
    Object? lessonId = freezed,
    Object? lessonOrderDesc = freezed,
    Object? lessonName = freezed,
    Object? stepInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      coverPicUrl: freezed == coverPicUrl
          ? _value.coverPicUrl
          : coverPicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrderDesc: freezed == lessonOrderDesc
          ? _value.lessonOrderDesc
          : lessonOrderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      stepInfoList: freezed == stepInfoList
          ? _value.stepInfoList
          : stepInfoList // ignore: cast_nullable_to_non_nullable
              as List<StepInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DrainageCourseLessonInfoCopyWith<$Res>
    implements $DrainageCourseLessonInfoCopyWith<$Res> {
  factory _$$_DrainageCourseLessonInfoCopyWith(
          _$_DrainageCourseLessonInfo value,
          $Res Function(_$_DrainageCourseLessonInfo) then) =
      __$$_DrainageCourseLessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? coverPicUrl,
      String? lessonId,
      String? lessonOrderDesc,
      String? lessonName,
      List<StepInfo>? stepInfoList});
}

/// @nodoc
class __$$_DrainageCourseLessonInfoCopyWithImpl<$Res>
    extends _$DrainageCourseLessonInfoCopyWithImpl<$Res,
        _$_DrainageCourseLessonInfo>
    implements _$$_DrainageCourseLessonInfoCopyWith<$Res> {
  __$$_DrainageCourseLessonInfoCopyWithImpl(_$_DrainageCourseLessonInfo _value,
      $Res Function(_$_DrainageCourseLessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coverPicUrl = freezed,
    Object? lessonId = freezed,
    Object? lessonOrderDesc = freezed,
    Object? lessonName = freezed,
    Object? stepInfoList = freezed,
  }) {
    return _then(_$_DrainageCourseLessonInfo(
      coverPicUrl: freezed == coverPicUrl
          ? _value.coverPicUrl
          : coverPicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrderDesc: freezed == lessonOrderDesc
          ? _value.lessonOrderDesc
          : lessonOrderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      stepInfoList: freezed == stepInfoList
          ? _value._stepInfoList
          : stepInfoList // ignore: cast_nullable_to_non_nullable
              as List<StepInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DrainageCourseLessonInfo implements _DrainageCourseLessonInfo {
  const _$_DrainageCourseLessonInfo(
      {this.coverPicUrl,
      this.lessonId,
      this.lessonOrderDesc,
      this.lessonName,
      final List<StepInfo>? stepInfoList})
      : _stepInfoList = stepInfoList;

  factory _$_DrainageCourseLessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_DrainageCourseLessonInfoFromJson(json);

  @override
  final String? coverPicUrl;
  @override
  final String? lessonId;
  @override
  final String? lessonOrderDesc;
  @override
  final String? lessonName;
  final List<StepInfo>? _stepInfoList;
  @override
  List<StepInfo>? get stepInfoList {
    final value = _stepInfoList;
    if (value == null) return null;
    if (_stepInfoList is EqualUnmodifiableListView) return _stepInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DrainageCourseLessonInfo(coverPicUrl: $coverPicUrl, lessonId: $lessonId, lessonOrderDesc: $lessonOrderDesc, lessonName: $lessonName, stepInfoList: $stepInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DrainageCourseLessonInfo &&
            (identical(other.coverPicUrl, coverPicUrl) ||
                other.coverPicUrl == coverPicUrl) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonOrderDesc, lessonOrderDesc) ||
                other.lessonOrderDesc == lessonOrderDesc) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            const DeepCollectionEquality()
                .equals(other._stepInfoList, _stepInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      coverPicUrl,
      lessonId,
      lessonOrderDesc,
      lessonName,
      const DeepCollectionEquality().hash(_stepInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DrainageCourseLessonInfoCopyWith<_$_DrainageCourseLessonInfo>
      get copyWith => __$$_DrainageCourseLessonInfoCopyWithImpl<
          _$_DrainageCourseLessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DrainageCourseLessonInfoToJson(
      this,
    );
  }
}

abstract class _DrainageCourseLessonInfo implements DrainageCourseLessonInfo {
  const factory _DrainageCourseLessonInfo(
      {final String? coverPicUrl,
      final String? lessonId,
      final String? lessonOrderDesc,
      final String? lessonName,
      final List<StepInfo>? stepInfoList}) = _$_DrainageCourseLessonInfo;

  factory _DrainageCourseLessonInfo.fromJson(Map<String, dynamic> json) =
      _$_DrainageCourseLessonInfo.fromJson;

  @override
  String? get coverPicUrl;
  @override
  String? get lessonId;
  @override
  String? get lessonOrderDesc;
  @override
  String? get lessonName;
  @override
  List<StepInfo>? get stepInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_DrainageCourseLessonInfoCopyWith<_$_DrainageCourseLessonInfo>
      get copyWith => throw _privateConstructorUsedError;
}

StepInfo _$StepInfoFromJson(Map<String, dynamic> json) {
  return _StepInfo.fromJson(json);
}

/// @nodoc
mixin _$StepInfo {
  String? get stepId => throw _privateConstructorUsedError;
  String? get lessonStepIcon => throw _privateConstructorUsedError;
  String? get lessonStepName => throw _privateConstructorUsedError;
  int? get finishStatus => throw _privateConstructorUsedError;
  String? get stepRouter => throw _privateConstructorUsedError;
  String? get classId => throw _privateConstructorUsedError;
  String? get lessonId => throw _privateConstructorUsedError;
  int? get stepType => throw _privateConstructorUsedError;
  String? get surplusStepTypes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StepInfoCopyWith<StepInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StepInfoCopyWith<$Res> {
  factory $StepInfoCopyWith(StepInfo value, $Res Function(StepInfo) then) =
      _$StepInfoCopyWithImpl<$Res, StepInfo>;
  @useResult
  $Res call(
      {String? stepId,
      String? lessonStepIcon,
      String? lessonStepName,
      int? finishStatus,
      String? stepRouter,
      String? classId,
      String? lessonId,
      int? stepType,
      String? surplusStepTypes});
}

/// @nodoc
class _$StepInfoCopyWithImpl<$Res, $Val extends StepInfo>
    implements $StepInfoCopyWith<$Res> {
  _$StepInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stepId = freezed,
    Object? lessonStepIcon = freezed,
    Object? lessonStepName = freezed,
    Object? finishStatus = freezed,
    Object? stepRouter = freezed,
    Object? classId = freezed,
    Object? lessonId = freezed,
    Object? stepType = freezed,
    Object? surplusStepTypes = freezed,
  }) {
    return _then(_value.copyWith(
      stepId: freezed == stepId
          ? _value.stepId
          : stepId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStepIcon: freezed == lessonStepIcon
          ? _value.lessonStepIcon
          : lessonStepIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStepName: freezed == lessonStepName
          ? _value.lessonStepName
          : lessonStepName // ignore: cast_nullable_to_non_nullable
              as String?,
      finishStatus: freezed == finishStatus
          ? _value.finishStatus
          : finishStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      stepRouter: freezed == stepRouter
          ? _value.stepRouter
          : stepRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      stepType: freezed == stepType
          ? _value.stepType
          : stepType // ignore: cast_nullable_to_non_nullable
              as int?,
      surplusStepTypes: freezed == surplusStepTypes
          ? _value.surplusStepTypes
          : surplusStepTypes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StepInfoCopyWith<$Res> implements $StepInfoCopyWith<$Res> {
  factory _$$_StepInfoCopyWith(
          _$_StepInfo value, $Res Function(_$_StepInfo) then) =
      __$$_StepInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? stepId,
      String? lessonStepIcon,
      String? lessonStepName,
      int? finishStatus,
      String? stepRouter,
      String? classId,
      String? lessonId,
      int? stepType,
      String? surplusStepTypes});
}

/// @nodoc
class __$$_StepInfoCopyWithImpl<$Res>
    extends _$StepInfoCopyWithImpl<$Res, _$_StepInfo>
    implements _$$_StepInfoCopyWith<$Res> {
  __$$_StepInfoCopyWithImpl(
      _$_StepInfo _value, $Res Function(_$_StepInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stepId = freezed,
    Object? lessonStepIcon = freezed,
    Object? lessonStepName = freezed,
    Object? finishStatus = freezed,
    Object? stepRouter = freezed,
    Object? classId = freezed,
    Object? lessonId = freezed,
    Object? stepType = freezed,
    Object? surplusStepTypes = freezed,
  }) {
    return _then(_$_StepInfo(
      stepId: freezed == stepId
          ? _value.stepId
          : stepId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStepIcon: freezed == lessonStepIcon
          ? _value.lessonStepIcon
          : lessonStepIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStepName: freezed == lessonStepName
          ? _value.lessonStepName
          : lessonStepName // ignore: cast_nullable_to_non_nullable
              as String?,
      finishStatus: freezed == finishStatus
          ? _value.finishStatus
          : finishStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      stepRouter: freezed == stepRouter
          ? _value.stepRouter
          : stepRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      stepType: freezed == stepType
          ? _value.stepType
          : stepType // ignore: cast_nullable_to_non_nullable
              as int?,
      surplusStepTypes: freezed == surplusStepTypes
          ? _value.surplusStepTypes
          : surplusStepTypes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StepInfo implements _StepInfo {
  const _$_StepInfo(
      {this.stepId,
      this.lessonStepIcon,
      this.lessonStepName,
      this.finishStatus,
      this.stepRouter,
      this.classId,
      this.lessonId,
      this.stepType,
      this.surplusStepTypes});

  factory _$_StepInfo.fromJson(Map<String, dynamic> json) =>
      _$$_StepInfoFromJson(json);

  @override
  final String? stepId;
  @override
  final String? lessonStepIcon;
  @override
  final String? lessonStepName;
  @override
  final int? finishStatus;
  @override
  final String? stepRouter;
  @override
  final String? classId;
  @override
  final String? lessonId;
  @override
  final int? stepType;
  @override
  final String? surplusStepTypes;

  @override
  String toString() {
    return 'StepInfo(stepId: $stepId, lessonStepIcon: $lessonStepIcon, lessonStepName: $lessonStepName, finishStatus: $finishStatus, stepRouter: $stepRouter, classId: $classId, lessonId: $lessonId, stepType: $stepType, surplusStepTypes: $surplusStepTypes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StepInfo &&
            (identical(other.stepId, stepId) || other.stepId == stepId) &&
            (identical(other.lessonStepIcon, lessonStepIcon) ||
                other.lessonStepIcon == lessonStepIcon) &&
            (identical(other.lessonStepName, lessonStepName) ||
                other.lessonStepName == lessonStepName) &&
            (identical(other.finishStatus, finishStatus) ||
                other.finishStatus == finishStatus) &&
            (identical(other.stepRouter, stepRouter) ||
                other.stepRouter == stepRouter) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.stepType, stepType) ||
                other.stepType == stepType) &&
            (identical(other.surplusStepTypes, surplusStepTypes) ||
                other.surplusStepTypes == surplusStepTypes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      stepId,
      lessonStepIcon,
      lessonStepName,
      finishStatus,
      stepRouter,
      classId,
      lessonId,
      stepType,
      surplusStepTypes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StepInfoCopyWith<_$_StepInfo> get copyWith =>
      __$$_StepInfoCopyWithImpl<_$_StepInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StepInfoToJson(
      this,
    );
  }
}

abstract class _StepInfo implements StepInfo {
  const factory _StepInfo(
      {final String? stepId,
      final String? lessonStepIcon,
      final String? lessonStepName,
      final int? finishStatus,
      final String? stepRouter,
      final String? classId,
      final String? lessonId,
      final int? stepType,
      final String? surplusStepTypes}) = _$_StepInfo;

  factory _StepInfo.fromJson(Map<String, dynamic> json) = _$_StepInfo.fromJson;

  @override
  String? get stepId;
  @override
  String? get lessonStepIcon;
  @override
  String? get lessonStepName;
  @override
  int? get finishStatus;
  @override
  String? get stepRouter;
  @override
  String? get classId;
  @override
  String? get lessonId;
  @override
  int? get stepType;
  @override
  String? get surplusStepTypes;
  @override
  @JsonKey(ignore: true)
  _$$_StepInfoCopyWith<_$_StepInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

AdvancedCourseInfo _$AdvancedCourseInfoFromJson(Map<String, dynamic> json) {
  return _AdvancedCourseInfo.fromJson(json);
}

/// @nodoc
mixin _$AdvancedCourseInfo {
  String? get title => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get courseId => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  List<DrainageCourseLessonInfo>? get lessonInfoList =>
      throw _privateConstructorUsedError;
  List<DrainageBtnInfo>? get btnInfoList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdvancedCourseInfoCopyWith<AdvancedCourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvancedCourseInfoCopyWith<$Res> {
  factory $AdvancedCourseInfoCopyWith(
          AdvancedCourseInfo value, $Res Function(AdvancedCourseInfo) then) =
      _$AdvancedCourseInfoCopyWithImpl<$Res, AdvancedCourseInfo>;
  @useResult
  $Res call(
      {String? title,
      String? tip,
      String? subTitle,
      String? courseId,
      String? skuId,
      String? skuName,
      List<DrainageCourseLessonInfo>? lessonInfoList,
      List<DrainageBtnInfo>? btnInfoList});
}

/// @nodoc
class _$AdvancedCourseInfoCopyWithImpl<$Res, $Val extends AdvancedCourseInfo>
    implements $AdvancedCourseInfoCopyWith<$Res> {
  _$AdvancedCourseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? tip = freezed,
    Object? subTitle = freezed,
    Object? courseId = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? lessonInfoList = freezed,
    Object? btnInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonInfoList: freezed == lessonInfoList
          ? _value.lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageCourseLessonInfo>?,
      btnInfoList: freezed == btnInfoList
          ? _value.btnInfoList
          : btnInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageBtnInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdvancedCourseInfoCopyWith<$Res>
    implements $AdvancedCourseInfoCopyWith<$Res> {
  factory _$$_AdvancedCourseInfoCopyWith(_$_AdvancedCourseInfo value,
          $Res Function(_$_AdvancedCourseInfo) then) =
      __$$_AdvancedCourseInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? tip,
      String? subTitle,
      String? courseId,
      String? skuId,
      String? skuName,
      List<DrainageCourseLessonInfo>? lessonInfoList,
      List<DrainageBtnInfo>? btnInfoList});
}

/// @nodoc
class __$$_AdvancedCourseInfoCopyWithImpl<$Res>
    extends _$AdvancedCourseInfoCopyWithImpl<$Res, _$_AdvancedCourseInfo>
    implements _$$_AdvancedCourseInfoCopyWith<$Res> {
  __$$_AdvancedCourseInfoCopyWithImpl(
      _$_AdvancedCourseInfo _value, $Res Function(_$_AdvancedCourseInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? tip = freezed,
    Object? subTitle = freezed,
    Object? courseId = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? lessonInfoList = freezed,
    Object? btnInfoList = freezed,
  }) {
    return _then(_$_AdvancedCourseInfo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonInfoList: freezed == lessonInfoList
          ? _value._lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageCourseLessonInfo>?,
      btnInfoList: freezed == btnInfoList
          ? _value._btnInfoList
          : btnInfoList // ignore: cast_nullable_to_non_nullable
              as List<DrainageBtnInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdvancedCourseInfo implements _AdvancedCourseInfo {
  const _$_AdvancedCourseInfo(
      {this.title,
      this.tip,
      this.subTitle,
      this.courseId,
      this.skuId,
      this.skuName,
      final List<DrainageCourseLessonInfo>? lessonInfoList,
      final List<DrainageBtnInfo>? btnInfoList})
      : _lessonInfoList = lessonInfoList,
        _btnInfoList = btnInfoList;

  factory _$_AdvancedCourseInfo.fromJson(Map<String, dynamic> json) =>
      _$$_AdvancedCourseInfoFromJson(json);

  @override
  final String? title;
  @override
  final String? tip;
  @override
  final String? subTitle;
  @override
  final String? courseId;
  @override
  final String? skuId;
  @override
  final String? skuName;
  final List<DrainageCourseLessonInfo>? _lessonInfoList;
  @override
  List<DrainageCourseLessonInfo>? get lessonInfoList {
    final value = _lessonInfoList;
    if (value == null) return null;
    if (_lessonInfoList is EqualUnmodifiableListView) return _lessonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DrainageBtnInfo>? _btnInfoList;
  @override
  List<DrainageBtnInfo>? get btnInfoList {
    final value = _btnInfoList;
    if (value == null) return null;
    if (_btnInfoList is EqualUnmodifiableListView) return _btnInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AdvancedCourseInfo(title: $title, tip: $tip, subTitle: $subTitle, courseId: $courseId, skuId: $skuId, skuName: $skuName, lessonInfoList: $lessonInfoList, btnInfoList: $btnInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdvancedCourseInfo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            const DeepCollectionEquality()
                .equals(other._lessonInfoList, _lessonInfoList) &&
            const DeepCollectionEquality()
                .equals(other._btnInfoList, _btnInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      tip,
      subTitle,
      courseId,
      skuId,
      skuName,
      const DeepCollectionEquality().hash(_lessonInfoList),
      const DeepCollectionEquality().hash(_btnInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdvancedCourseInfoCopyWith<_$_AdvancedCourseInfo> get copyWith =>
      __$$_AdvancedCourseInfoCopyWithImpl<_$_AdvancedCourseInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdvancedCourseInfoToJson(
      this,
    );
  }
}

abstract class _AdvancedCourseInfo implements AdvancedCourseInfo {
  const factory _AdvancedCourseInfo(
      {final String? title,
      final String? tip,
      final String? subTitle,
      final String? courseId,
      final String? skuId,
      final String? skuName,
      final List<DrainageCourseLessonInfo>? lessonInfoList,
      final List<DrainageBtnInfo>? btnInfoList}) = _$_AdvancedCourseInfo;

  factory _AdvancedCourseInfo.fromJson(Map<String, dynamic> json) =
      _$_AdvancedCourseInfo.fromJson;

  @override
  String? get title;
  @override
  String? get tip;
  @override
  String? get subTitle;
  @override
  String? get courseId;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  List<DrainageCourseLessonInfo>? get lessonInfoList;
  @override
  List<DrainageBtnInfo>? get btnInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_AdvancedCourseInfoCopyWith<_$_AdvancedCourseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

DrainageBtnInfo _$DrainageBtnInfoFromJson(Map<String, dynamic> json) {
  return _DrainageBtnInfo.fromJson(json);
}

/// @nodoc
mixin _$DrainageBtnInfo {
  int? get type => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get bubbleText => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DrainageBtnInfoCopyWith<DrainageBtnInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DrainageBtnInfoCopyWith<$Res> {
  factory $DrainageBtnInfoCopyWith(
          DrainageBtnInfo value, $Res Function(DrainageBtnInfo) then) =
      _$DrainageBtnInfoCopyWithImpl<$Res, DrainageBtnInfo>;
  @useResult
  $Res call({int? type, String? content, String? bubbleText, String? router});
}

/// @nodoc
class _$DrainageBtnInfoCopyWithImpl<$Res, $Val extends DrainageBtnInfo>
    implements $DrainageBtnInfoCopyWith<$Res> {
  _$DrainageBtnInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? content = freezed,
    Object? bubbleText = freezed,
    Object? router = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      bubbleText: freezed == bubbleText
          ? _value.bubbleText
          : bubbleText // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DrainageBtnInfoCopyWith<$Res>
    implements $DrainageBtnInfoCopyWith<$Res> {
  factory _$$_DrainageBtnInfoCopyWith(
          _$_DrainageBtnInfo value, $Res Function(_$_DrainageBtnInfo) then) =
      __$$_DrainageBtnInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? content, String? bubbleText, String? router});
}

/// @nodoc
class __$$_DrainageBtnInfoCopyWithImpl<$Res>
    extends _$DrainageBtnInfoCopyWithImpl<$Res, _$_DrainageBtnInfo>
    implements _$$_DrainageBtnInfoCopyWith<$Res> {
  __$$_DrainageBtnInfoCopyWithImpl(
      _$_DrainageBtnInfo _value, $Res Function(_$_DrainageBtnInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? content = freezed,
    Object? bubbleText = freezed,
    Object? router = freezed,
  }) {
    return _then(_$_DrainageBtnInfo(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      bubbleText: freezed == bubbleText
          ? _value.bubbleText
          : bubbleText // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DrainageBtnInfo implements _DrainageBtnInfo {
  const _$_DrainageBtnInfo(
      {this.type, this.content, this.bubbleText, this.router});

  factory _$_DrainageBtnInfo.fromJson(Map<String, dynamic> json) =>
      _$$_DrainageBtnInfoFromJson(json);

  @override
  final int? type;
  @override
  final String? content;
  @override
  final String? bubbleText;
  @override
  final String? router;

  @override
  String toString() {
    return 'DrainageBtnInfo(type: $type, content: $content, bubbleText: $bubbleText, router: $router)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DrainageBtnInfo &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.bubbleText, bubbleText) ||
                other.bubbleText == bubbleText) &&
            (identical(other.router, router) || other.router == router));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, content, bubbleText, router);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DrainageBtnInfoCopyWith<_$_DrainageBtnInfo> get copyWith =>
      __$$_DrainageBtnInfoCopyWithImpl<_$_DrainageBtnInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DrainageBtnInfoToJson(
      this,
    );
  }
}

abstract class _DrainageBtnInfo implements DrainageBtnInfo {
  const factory _DrainageBtnInfo(
      {final int? type,
      final String? content,
      final String? bubbleText,
      final String? router}) = _$_DrainageBtnInfo;

  factory _DrainageBtnInfo.fromJson(Map<String, dynamic> json) =
      _$_DrainageBtnInfo.fromJson;

  @override
  int? get type;
  @override
  String? get content;
  @override
  String? get bubbleText;
  @override
  String? get router;
  @override
  @JsonKey(ignore: true)
  _$$_DrainageBtnInfoCopyWith<_$_DrainageBtnInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ChildrenTaskList _$ChildrenTaskListFromJson(Map<String, dynamic> json) {
  return _ChildrenTaskList.fromJson(json);
}

/// @nodoc
mixin _$ChildrenTaskList {
  int? get taskStatus => throw _privateConstructorUsedError;
  set taskStatus(int? value) => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  set showDateTime(int? value) => throw _privateConstructorUsedError;
  int? get startClassTime => throw _privateConstructorUsedError;
  set startClassTime(int? value) => throw _privateConstructorUsedError;
  int? get firstLessonStartTime => throw _privateConstructorUsedError;
  set firstLessonStartTime(int? value) => throw _privateConstructorUsedError;
  int? get taskType => throw _privateConstructorUsedError;
  set taskType(int? value) => throw _privateConstructorUsedError;
  String? get taskTypeDesc => throw _privateConstructorUsedError;
  set taskTypeDesc(String? value) => throw _privateConstructorUsedError;
  String? get simpleTitle => throw _privateConstructorUsedError;
  set simpleTitle(String? value) => throw _privateConstructorUsedError;
  String? get scheduleTaskId => throw _privateConstructorUsedError;
  set scheduleTaskId(String? value) => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  set courseType(int? value) => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  set courseSegmentName(String? value) => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  set subjectType(int? value) => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  set subTitle(String? value) => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  set btnText(String? value) => throw _privateConstructorUsedError;
  String? get buttonDesc => throw _privateConstructorUsedError;
  set buttonDesc(String? value) => throw _privateConstructorUsedError;
  String? get userCourseBusinessStatus => throw _privateConstructorUsedError;
  set userCourseBusinessStatus(String? value) =>
      throw _privateConstructorUsedError;
  CourseHomeTaskExtensionData? get taskExtension =>
      throw _privateConstructorUsedError;
  set taskExtension(CourseHomeTaskExtensionData? value) =>
      throw _privateConstructorUsedError;
  CourseHomePageLessonResourceData? get lessonResource =>
      throw _privateConstructorUsedError;
  set lessonResource(CourseHomePageLessonResourceData? value) =>
      throw _privateConstructorUsedError;
  CourseHomePageCourseResourceData? get courseResource =>
      throw _privateConstructorUsedError;
  set courseResource(CourseHomePageCourseResourceData? value) =>
      throw _privateConstructorUsedError;
  List<dynamic>? get childrenTaskList => throw _privateConstructorUsedError;
  set childrenTaskList(List<dynamic>? value) =>
      throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  set tip(String? value) => throw _privateConstructorUsedError;
  bool? get showTip => throw _privateConstructorUsedError;
  set showTip(bool? value) => throw _privateConstructorUsedError;
  ContactTeacherInfo? get contactTeacherInfo =>
      throw _privateConstructorUsedError;
  set contactTeacherInfo(ContactTeacherInfo? value) =>
      throw _privateConstructorUsedError;
  int? get parentVerify => throw _privateConstructorUsedError;
  set parentVerify(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChildrenTaskListCopyWith<ChildrenTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChildrenTaskListCopyWith<$Res> {
  factory $ChildrenTaskListCopyWith(
          ChildrenTaskList value, $Res Function(ChildrenTaskList) then) =
      _$ChildrenTaskListCopyWithImpl<$Res, ChildrenTaskList>;
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? taskType,
      String? taskTypeDesc,
      String? simpleTitle,
      String? scheduleTaskId,
      int? courseType,
      String? courseSegmentName,
      int? subjectType,
      String? title,
      String? subTitle,
      String? icon,
      String? route,
      String? btnText,
      String? buttonDesc,
      String? userCourseBusinessStatus,
      CourseHomeTaskExtensionData? taskExtension,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<dynamic>? childrenTaskList,
      String? tip,
      bool? showTip,
      ContactTeacherInfo? contactTeacherInfo,
      int? parentVerify});

  $CourseHomeTaskExtensionDataCopyWith<$Res>? get taskExtension;
  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource;
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource;
  $ContactTeacherInfoCopyWith<$Res>? get contactTeacherInfo;
}

/// @nodoc
class _$ChildrenTaskListCopyWithImpl<$Res, $Val extends ChildrenTaskList>
    implements $ChildrenTaskListCopyWith<$Res> {
  _$ChildrenTaskListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? taskType = freezed,
    Object? taskTypeDesc = freezed,
    Object? simpleTitle = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? subjectType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? btnText = freezed,
    Object? buttonDesc = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? taskExtension = freezed,
    Object? lessonResource = freezed,
    Object? courseResource = freezed,
    Object? childrenTaskList = freezed,
    Object? tip = freezed,
    Object? showTip = freezed,
    Object? contactTeacherInfo = freezed,
    Object? parentVerify = freezed,
  }) {
    return _then(_value.copyWith(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskTypeDesc: freezed == taskTypeDesc
          ? _value.taskTypeDesc
          : taskTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      simpleTitle: freezed == simpleTitle
          ? _value.simpleTitle
          : simpleTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDesc: freezed == buttonDesc
          ? _value.buttonDesc
          : buttonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      taskExtension: freezed == taskExtension
          ? _value.taskExtension
          : taskExtension // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      lessonResource: freezed == lessonResource
          ? _value.lessonResource
          : lessonResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageLessonResourceData?,
      courseResource: freezed == courseResource
          ? _value.courseResource
          : courseResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageCourseResourceData?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      showTip: freezed == showTip
          ? _value.showTip
          : showTip // ignore: cast_nullable_to_non_nullable
              as bool?,
      contactTeacherInfo: freezed == contactTeacherInfo
          ? _value.contactTeacherInfo
          : contactTeacherInfo // ignore: cast_nullable_to_non_nullable
              as ContactTeacherInfo?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get taskExtension {
    if (_value.taskExtension == null) {
      return null;
    }

    return $CourseHomeTaskExtensionDataCopyWith<$Res>(_value.taskExtension!,
        (value) {
      return _then(_value.copyWith(taskExtension: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource {
    if (_value.lessonResource == null) {
      return null;
    }

    return $CourseHomePageLessonResourceDataCopyWith<$Res>(
        _value.lessonResource!, (value) {
      return _then(_value.copyWith(lessonResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource {
    if (_value.courseResource == null) {
      return null;
    }

    return $CourseHomePageCourseResourceDataCopyWith<$Res>(
        _value.courseResource!, (value) {
      return _then(_value.copyWith(courseResource: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ContactTeacherInfoCopyWith<$Res>? get contactTeacherInfo {
    if (_value.contactTeacherInfo == null) {
      return null;
    }

    return $ContactTeacherInfoCopyWith<$Res>(_value.contactTeacherInfo!,
        (value) {
      return _then(_value.copyWith(contactTeacherInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ChildrenTaskListCopyWith<$Res>
    implements $ChildrenTaskListCopyWith<$Res> {
  factory _$$_ChildrenTaskListCopyWith(
          _$_ChildrenTaskList value, $Res Function(_$_ChildrenTaskList) then) =
      __$$_ChildrenTaskListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? taskType,
      String? taskTypeDesc,
      String? simpleTitle,
      String? scheduleTaskId,
      int? courseType,
      String? courseSegmentName,
      int? subjectType,
      String? title,
      String? subTitle,
      String? icon,
      String? route,
      String? btnText,
      String? buttonDesc,
      String? userCourseBusinessStatus,
      CourseHomeTaskExtensionData? taskExtension,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<dynamic>? childrenTaskList,
      String? tip,
      bool? showTip,
      ContactTeacherInfo? contactTeacherInfo,
      int? parentVerify});

  @override
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get taskExtension;
  @override
  $CourseHomePageLessonResourceDataCopyWith<$Res>? get lessonResource;
  @override
  $CourseHomePageCourseResourceDataCopyWith<$Res>? get courseResource;
  @override
  $ContactTeacherInfoCopyWith<$Res>? get contactTeacherInfo;
}

/// @nodoc
class __$$_ChildrenTaskListCopyWithImpl<$Res>
    extends _$ChildrenTaskListCopyWithImpl<$Res, _$_ChildrenTaskList>
    implements _$$_ChildrenTaskListCopyWith<$Res> {
  __$$_ChildrenTaskListCopyWithImpl(
      _$_ChildrenTaskList _value, $Res Function(_$_ChildrenTaskList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? taskType = freezed,
    Object? taskTypeDesc = freezed,
    Object? simpleTitle = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? subjectType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? btnText = freezed,
    Object? buttonDesc = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? taskExtension = freezed,
    Object? lessonResource = freezed,
    Object? courseResource = freezed,
    Object? childrenTaskList = freezed,
    Object? tip = freezed,
    Object? showTip = freezed,
    Object? contactTeacherInfo = freezed,
    Object? parentVerify = freezed,
  }) {
    return _then(_$_ChildrenTaskList(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskTypeDesc: freezed == taskTypeDesc
          ? _value.taskTypeDesc
          : taskTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      simpleTitle: freezed == simpleTitle
          ? _value.simpleTitle
          : simpleTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDesc: freezed == buttonDesc
          ? _value.buttonDesc
          : buttonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      taskExtension: freezed == taskExtension
          ? _value.taskExtension
          : taskExtension // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      lessonResource: freezed == lessonResource
          ? _value.lessonResource
          : lessonResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageLessonResourceData?,
      courseResource: freezed == courseResource
          ? _value.courseResource
          : courseResource // ignore: cast_nullable_to_non_nullable
              as CourseHomePageCourseResourceData?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      showTip: freezed == showTip
          ? _value.showTip
          : showTip // ignore: cast_nullable_to_non_nullable
              as bool?,
      contactTeacherInfo: freezed == contactTeacherInfo
          ? _value.contactTeacherInfo
          : contactTeacherInfo // ignore: cast_nullable_to_non_nullable
              as ContactTeacherInfo?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ChildrenTaskList implements _ChildrenTaskList {
  _$_ChildrenTaskList(
      {this.taskStatus,
      this.showDateTime,
      this.startClassTime,
      this.firstLessonStartTime,
      this.taskType,
      this.taskTypeDesc,
      this.simpleTitle,
      this.scheduleTaskId,
      this.courseType,
      this.courseSegmentName,
      this.subjectType,
      this.title,
      this.subTitle,
      this.icon,
      this.route,
      this.btnText,
      this.buttonDesc,
      this.userCourseBusinessStatus,
      this.taskExtension,
      this.lessonResource,
      this.courseResource,
      this.childrenTaskList,
      this.tip,
      this.showTip,
      this.contactTeacherInfo,
      this.parentVerify});

  factory _$_ChildrenTaskList.fromJson(Map<String, dynamic> json) =>
      _$$_ChildrenTaskListFromJson(json);

  @override
  int? taskStatus;
  @override
  int? showDateTime;
  @override
  int? startClassTime;
  @override
  int? firstLessonStartTime;
  @override
  int? taskType;
  @override
  String? taskTypeDesc;
  @override
  String? simpleTitle;
  @override
  String? scheduleTaskId;
  @override
  int? courseType;
  @override
  String? courseSegmentName;
  @override
  int? subjectType;
  @override
  String? title;
  @override
  String? subTitle;
  @override
  String? icon;
  @override
  String? route;
  @override
  String? btnText;
  @override
  String? buttonDesc;
  @override
  String? userCourseBusinessStatus;
  @override
  CourseHomeTaskExtensionData? taskExtension;
  @override
  CourseHomePageLessonResourceData? lessonResource;
  @override
  CourseHomePageCourseResourceData? courseResource;
  @override
  List<dynamic>? childrenTaskList;
  @override
  String? tip;
  @override
  bool? showTip;
  @override
  ContactTeacherInfo? contactTeacherInfo;
  @override
  int? parentVerify;

  @override
  String toString() {
    return 'ChildrenTaskList(taskStatus: $taskStatus, showDateTime: $showDateTime, startClassTime: $startClassTime, firstLessonStartTime: $firstLessonStartTime, taskType: $taskType, taskTypeDesc: $taskTypeDesc, simpleTitle: $simpleTitle, scheduleTaskId: $scheduleTaskId, courseType: $courseType, courseSegmentName: $courseSegmentName, subjectType: $subjectType, title: $title, subTitle: $subTitle, icon: $icon, route: $route, btnText: $btnText, buttonDesc: $buttonDesc, userCourseBusinessStatus: $userCourseBusinessStatus, taskExtension: $taskExtension, lessonResource: $lessonResource, courseResource: $courseResource, childrenTaskList: $childrenTaskList, tip: $tip, showTip: $showTip, contactTeacherInfo: $contactTeacherInfo, parentVerify: $parentVerify)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChildrenTaskListCopyWith<_$_ChildrenTaskList> get copyWith =>
      __$$_ChildrenTaskListCopyWithImpl<_$_ChildrenTaskList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ChildrenTaskListToJson(
      this,
    );
  }
}

abstract class _ChildrenTaskList implements ChildrenTaskList {
  factory _ChildrenTaskList(
      {int? taskStatus,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? taskType,
      String? taskTypeDesc,
      String? simpleTitle,
      String? scheduleTaskId,
      int? courseType,
      String? courseSegmentName,
      int? subjectType,
      String? title,
      String? subTitle,
      String? icon,
      String? route,
      String? btnText,
      String? buttonDesc,
      String? userCourseBusinessStatus,
      CourseHomeTaskExtensionData? taskExtension,
      CourseHomePageLessonResourceData? lessonResource,
      CourseHomePageCourseResourceData? courseResource,
      List<dynamic>? childrenTaskList,
      String? tip,
      bool? showTip,
      ContactTeacherInfo? contactTeacherInfo,
      int? parentVerify}) = _$_ChildrenTaskList;

  factory _ChildrenTaskList.fromJson(Map<String, dynamic> json) =
      _$_ChildrenTaskList.fromJson;

  @override
  int? get taskStatus;
  set taskStatus(int? value);
  @override
  int? get showDateTime;
  set showDateTime(int? value);
  @override
  int? get startClassTime;
  set startClassTime(int? value);
  @override
  int? get firstLessonStartTime;
  set firstLessonStartTime(int? value);
  @override
  int? get taskType;
  set taskType(int? value);
  @override
  String? get taskTypeDesc;
  set taskTypeDesc(String? value);
  @override
  String? get simpleTitle;
  set simpleTitle(String? value);
  @override
  String? get scheduleTaskId;
  set scheduleTaskId(String? value);
  @override
  int? get courseType;
  set courseType(int? value);
  @override
  String? get courseSegmentName;
  set courseSegmentName(String? value);
  @override
  int? get subjectType;
  set subjectType(int? value);
  @override
  String? get title;
  set title(String? value);
  @override
  String? get subTitle;
  set subTitle(String? value);
  @override
  String? get icon;
  set icon(String? value);
  @override
  String? get route;
  set route(String? value);
  @override
  String? get btnText;
  set btnText(String? value);
  @override
  String? get buttonDesc;
  set buttonDesc(String? value);
  @override
  String? get userCourseBusinessStatus;
  set userCourseBusinessStatus(String? value);
  @override
  CourseHomeTaskExtensionData? get taskExtension;
  set taskExtension(CourseHomeTaskExtensionData? value);
  @override
  CourseHomePageLessonResourceData? get lessonResource;
  set lessonResource(CourseHomePageLessonResourceData? value);
  @override
  CourseHomePageCourseResourceData? get courseResource;
  set courseResource(CourseHomePageCourseResourceData? value);
  @override
  List<dynamic>? get childrenTaskList;
  set childrenTaskList(List<dynamic>? value);
  @override
  String? get tip;
  set tip(String? value);
  @override
  bool? get showTip;
  set showTip(bool? value);
  @override
  ContactTeacherInfo? get contactTeacherInfo;
  set contactTeacherInfo(ContactTeacherInfo? value);
  @override
  int? get parentVerify;
  set parentVerify(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_ChildrenTaskListCopyWith<_$_ChildrenTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonServiceList _$LessonServiceListFromJson(Map<String, dynamic> json) {
  return _LessonServiceList.fromJson(json);
}

/// @nodoc
mixin _$LessonServiceList {
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  bool? get redPoint => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get key => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get button => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  int? get parentVerify => throw _privateConstructorUsedError;
  String? get toast => throw _privateConstructorUsedError;
  int? get taskStatus => throw _privateConstructorUsedError;
  CourseHomeTaskExtensionData? get serviceExtra =>
      throw _privateConstructorUsedError;
  List<String>? get serviceUpdateBubbleList =>
      throw _privateConstructorUsedError;
  PopInfo? get popupInfo => throw _privateConstructorUsedError;
  String? get gifIcon => throw _privateConstructorUsedError;
  int? get playTime => throw _privateConstructorUsedError;
  String? get gifIconName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonServiceListCopyWith<LessonServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonServiceListCopyWith<$Res> {
  factory $LessonServiceListCopyWith(
          LessonServiceList value, $Res Function(LessonServiceList) then) =
      _$LessonServiceListCopyWithImpl<$Res, LessonServiceList>;
  @useResult
  $Res call(
      {String? icon,
      String? name,
      bool? redPoint,
      String? route,
      String? key,
      String? title,
      String? subTitle,
      String? button,
      int? order,
      String? desc,
      int? parentVerify,
      String? toast,
      int? taskStatus,
      CourseHomeTaskExtensionData? serviceExtra,
      List<String>? serviceUpdateBubbleList,
      PopInfo? popupInfo,
      String? gifIcon,
      int? playTime,
      String? gifIconName});

  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra;
  $PopInfoCopyWith<$Res>? get popupInfo;
}

/// @nodoc
class _$LessonServiceListCopyWithImpl<$Res, $Val extends LessonServiceList>
    implements $LessonServiceListCopyWith<$Res> {
  _$LessonServiceListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
    Object? key = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? button = freezed,
    Object? order = freezed,
    Object? desc = freezed,
    Object? parentVerify = freezed,
    Object? toast = freezed,
    Object? taskStatus = freezed,
    Object? serviceExtra = freezed,
    Object? serviceUpdateBubbleList = freezed,
    Object? popupInfo = freezed,
    Object? gifIcon = freezed,
    Object? playTime = freezed,
    Object? gifIconName = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceExtra: freezed == serviceExtra
          ? _value.serviceExtra
          : serviceExtra // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      serviceUpdateBubbleList: freezed == serviceUpdateBubbleList
          ? _value.serviceUpdateBubbleList
          : serviceUpdateBubbleList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopInfo?,
      gifIcon: freezed == gifIcon
          ? _value.gifIcon
          : gifIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      playTime: freezed == playTime
          ? _value.playTime
          : playTime // ignore: cast_nullable_to_non_nullable
              as int?,
      gifIconName: freezed == gifIconName
          ? _value.gifIconName
          : gifIconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra {
    if (_value.serviceExtra == null) {
      return null;
    }

    return $CourseHomeTaskExtensionDataCopyWith<$Res>(_value.serviceExtra!,
        (value) {
      return _then(_value.copyWith(serviceExtra: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PopInfoCopyWith<$Res>? get popupInfo {
    if (_value.popupInfo == null) {
      return null;
    }

    return $PopInfoCopyWith<$Res>(_value.popupInfo!, (value) {
      return _then(_value.copyWith(popupInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LessonServiceListCopyWith<$Res>
    implements $LessonServiceListCopyWith<$Res> {
  factory _$$_LessonServiceListCopyWith(_$_LessonServiceList value,
          $Res Function(_$_LessonServiceList) then) =
      __$$_LessonServiceListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? icon,
      String? name,
      bool? redPoint,
      String? route,
      String? key,
      String? title,
      String? subTitle,
      String? button,
      int? order,
      String? desc,
      int? parentVerify,
      String? toast,
      int? taskStatus,
      CourseHomeTaskExtensionData? serviceExtra,
      List<String>? serviceUpdateBubbleList,
      PopInfo? popupInfo,
      String? gifIcon,
      int? playTime,
      String? gifIconName});

  @override
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra;
  @override
  $PopInfoCopyWith<$Res>? get popupInfo;
}

/// @nodoc
class __$$_LessonServiceListCopyWithImpl<$Res>
    extends _$LessonServiceListCopyWithImpl<$Res, _$_LessonServiceList>
    implements _$$_LessonServiceListCopyWith<$Res> {
  __$$_LessonServiceListCopyWithImpl(
      _$_LessonServiceList _value, $Res Function(_$_LessonServiceList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
    Object? key = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? button = freezed,
    Object? order = freezed,
    Object? desc = freezed,
    Object? parentVerify = freezed,
    Object? toast = freezed,
    Object? taskStatus = freezed,
    Object? serviceExtra = freezed,
    Object? serviceUpdateBubbleList = freezed,
    Object? popupInfo = freezed,
    Object? gifIcon = freezed,
    Object? playTime = freezed,
    Object? gifIconName = freezed,
  }) {
    return _then(_$_LessonServiceList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceExtra: freezed == serviceExtra
          ? _value.serviceExtra
          : serviceExtra // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      serviceUpdateBubbleList: freezed == serviceUpdateBubbleList
          ? _value._serviceUpdateBubbleList
          : serviceUpdateBubbleList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopInfo?,
      gifIcon: freezed == gifIcon
          ? _value.gifIcon
          : gifIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      playTime: freezed == playTime
          ? _value.playTime
          : playTime // ignore: cast_nullable_to_non_nullable
              as int?,
      gifIconName: freezed == gifIconName
          ? _value.gifIconName
          : gifIconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonServiceList implements _LessonServiceList {
  const _$_LessonServiceList(
      {this.icon,
      this.name,
      this.redPoint,
      this.route,
      this.key,
      this.title,
      this.subTitle,
      this.button,
      this.order,
      this.desc,
      this.parentVerify,
      this.toast,
      this.taskStatus,
      this.serviceExtra,
      final List<String>? serviceUpdateBubbleList,
      this.popupInfo,
      this.gifIcon,
      this.playTime,
      this.gifIconName})
      : _serviceUpdateBubbleList = serviceUpdateBubbleList;

  factory _$_LessonServiceList.fromJson(Map<String, dynamic> json) =>
      _$$_LessonServiceListFromJson(json);

  @override
  final String? icon;
  @override
  final String? name;
  @override
  final bool? redPoint;
  @override
  final String? route;
  @override
  final String? key;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final String? button;
  @override
  final int? order;
  @override
  final String? desc;
  @override
  final int? parentVerify;
  @override
  final String? toast;
  @override
  final int? taskStatus;
  @override
  final CourseHomeTaskExtensionData? serviceExtra;
  final List<String>? _serviceUpdateBubbleList;
  @override
  List<String>? get serviceUpdateBubbleList {
    final value = _serviceUpdateBubbleList;
    if (value == null) return null;
    if (_serviceUpdateBubbleList is EqualUnmodifiableListView)
      return _serviceUpdateBubbleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final PopInfo? popupInfo;
  @override
  final String? gifIcon;
  @override
  final int? playTime;
  @override
  final String? gifIconName;

  @override
  String toString() {
    return 'LessonServiceList(icon: $icon, name: $name, redPoint: $redPoint, route: $route, key: $key, title: $title, subTitle: $subTitle, button: $button, order: $order, desc: $desc, parentVerify: $parentVerify, toast: $toast, taskStatus: $taskStatus, serviceExtra: $serviceExtra, serviceUpdateBubbleList: $serviceUpdateBubbleList, popupInfo: $popupInfo, gifIcon: $gifIcon, playTime: $playTime, gifIconName: $gifIconName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonServiceList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.redPoint, redPoint) ||
                other.redPoint == redPoint) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.button, button) || other.button == button) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.parentVerify, parentVerify) ||
                other.parentVerify == parentVerify) &&
            (identical(other.toast, toast) || other.toast == toast) &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.serviceExtra, serviceExtra) ||
                other.serviceExtra == serviceExtra) &&
            const DeepCollectionEquality().equals(
                other._serviceUpdateBubbleList, _serviceUpdateBubbleList) &&
            (identical(other.popupInfo, popupInfo) ||
                other.popupInfo == popupInfo) &&
            (identical(other.gifIcon, gifIcon) || other.gifIcon == gifIcon) &&
            (identical(other.playTime, playTime) ||
                other.playTime == playTime) &&
            (identical(other.gifIconName, gifIconName) ||
                other.gifIconName == gifIconName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        icon,
        name,
        redPoint,
        route,
        key,
        title,
        subTitle,
        button,
        order,
        desc,
        parentVerify,
        toast,
        taskStatus,
        serviceExtra,
        const DeepCollectionEquality().hash(_serviceUpdateBubbleList),
        popupInfo,
        gifIcon,
        playTime,
        gifIconName
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonServiceListCopyWith<_$_LessonServiceList> get copyWith =>
      __$$_LessonServiceListCopyWithImpl<_$_LessonServiceList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonServiceListToJson(
      this,
    );
  }
}

abstract class _LessonServiceList implements LessonServiceList {
  const factory _LessonServiceList(
      {final String? icon,
      final String? name,
      final bool? redPoint,
      final String? route,
      final String? key,
      final String? title,
      final String? subTitle,
      final String? button,
      final int? order,
      final String? desc,
      final int? parentVerify,
      final String? toast,
      final int? taskStatus,
      final CourseHomeTaskExtensionData? serviceExtra,
      final List<String>? serviceUpdateBubbleList,
      final PopInfo? popupInfo,
      final String? gifIcon,
      final int? playTime,
      final String? gifIconName}) = _$_LessonServiceList;

  factory _LessonServiceList.fromJson(Map<String, dynamic> json) =
      _$_LessonServiceList.fromJson;

  @override
  String? get icon;
  @override
  String? get name;
  @override
  bool? get redPoint;
  @override
  String? get route;
  @override
  String? get key;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  String? get button;
  @override
  int? get order;
  @override
  String? get desc;
  @override
  int? get parentVerify;
  @override
  String? get toast;
  @override
  int? get taskStatus;
  @override
  CourseHomeTaskExtensionData? get serviceExtra;
  @override
  List<String>? get serviceUpdateBubbleList;
  @override
  PopInfo? get popupInfo;
  @override
  String? get gifIcon;
  @override
  int? get playTime;
  @override
  String? get gifIconName;
  @override
  @JsonKey(ignore: true)
  _$$_LessonServiceListCopyWith<_$_LessonServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectScheduleStatus _$SubjectScheduleStatusFromJson(
    Map<String, dynamic> json) {
  return _SubjectScheduleStatus.fromJson(json);
}

/// @nodoc
mixin _$SubjectScheduleStatus {
  int? get subjectType => throw _privateConstructorUsedError;
  int? get scheduleStatus => throw _privateConstructorUsedError;
  int? get monthKeyStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectScheduleStatusCopyWith<SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectScheduleStatusCopyWith<$Res> {
  factory $SubjectScheduleStatusCopyWith(SubjectScheduleStatus value,
          $Res Function(SubjectScheduleStatus) then) =
      _$SubjectScheduleStatusCopyWithImpl<$Res, SubjectScheduleStatus>;
  @useResult
  $Res call({int? subjectType, int? scheduleStatus, int? monthKeyStatus});
}

/// @nodoc
class _$SubjectScheduleStatusCopyWithImpl<$Res,
        $Val extends SubjectScheduleStatus>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  _$SubjectScheduleStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
    Object? monthKeyStatus = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      monthKeyStatus: freezed == monthKeyStatus
          ? _value.monthKeyStatus
          : monthKeyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectScheduleStatusCopyWith<$Res>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  factory _$$_SubjectScheduleStatusCopyWith(_$_SubjectScheduleStatus value,
          $Res Function(_$_SubjectScheduleStatus) then) =
      __$$_SubjectScheduleStatusCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, int? scheduleStatus, int? monthKeyStatus});
}

/// @nodoc
class __$$_SubjectScheduleStatusCopyWithImpl<$Res>
    extends _$SubjectScheduleStatusCopyWithImpl<$Res, _$_SubjectScheduleStatus>
    implements _$$_SubjectScheduleStatusCopyWith<$Res> {
  __$$_SubjectScheduleStatusCopyWithImpl(_$_SubjectScheduleStatus _value,
      $Res Function(_$_SubjectScheduleStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
    Object? monthKeyStatus = freezed,
  }) {
    return _then(_$_SubjectScheduleStatus(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      monthKeyStatus: freezed == monthKeyStatus
          ? _value.monthKeyStatus
          : monthKeyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectScheduleStatus implements _SubjectScheduleStatus {
  const _$_SubjectScheduleStatus(
      {this.subjectType, this.scheduleStatus, this.monthKeyStatus});

  factory _$_SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectScheduleStatusFromJson(json);

  @override
  final int? subjectType;
  @override
  final int? scheduleStatus;
  @override
  final int? monthKeyStatus;

  @override
  String toString() {
    return 'SubjectScheduleStatus(subjectType: $subjectType, scheduleStatus: $scheduleStatus, monthKeyStatus: $monthKeyStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectScheduleStatus &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.scheduleStatus, scheduleStatus) ||
                other.scheduleStatus == scheduleStatus) &&
            (identical(other.monthKeyStatus, monthKeyStatus) ||
                other.monthKeyStatus == monthKeyStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, subjectType, scheduleStatus, monthKeyStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      __$$_SubjectScheduleStatusCopyWithImpl<_$_SubjectScheduleStatus>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectScheduleStatusToJson(
      this,
    );
  }
}

abstract class _SubjectScheduleStatus implements SubjectScheduleStatus {
  const factory _SubjectScheduleStatus(
      {final int? subjectType,
      final int? scheduleStatus,
      final int? monthKeyStatus}) = _$_SubjectScheduleStatus;

  factory _SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =
      _$_SubjectScheduleStatus.fromJson;

  @override
  int? get subjectType;
  @override
  int? get scheduleStatus;
  @override
  int? get monthKeyStatus;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectTypeVo _$SubjectTypeVoFromJson(Map<String, dynamic> json) {
  return _SubjectTypeVo.fromJson(json);
}

/// @nodoc
mixin _$SubjectTypeVo {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;

  /// 是否定位当前科目
  bool? get isPosition => throw _privateConstructorUsedError; // 是否获得新科目
  bool? get subjectNewlyObtained => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectTypeVoCopyWith<SubjectTypeVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectTypeVoCopyWith<$Res> {
  factory $SubjectTypeVoCopyWith(
          SubjectTypeVo value, $Res Function(SubjectTypeVo) then) =
      _$SubjectTypeVoCopyWithImpl<$Res, SubjectTypeVo>;
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectTypeDesc,
      bool? isPosition,
      bool? subjectNewlyObtained,
      int? classId});
}

/// @nodoc
class _$SubjectTypeVoCopyWithImpl<$Res, $Val extends SubjectTypeVo>
    implements $SubjectTypeVoCopyWith<$Res> {
  _$SubjectTypeVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? isPosition = freezed,
    Object? subjectNewlyObtained = freezed,
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectNewlyObtained: freezed == subjectNewlyObtained
          ? _value.subjectNewlyObtained
          : subjectNewlyObtained // ignore: cast_nullable_to_non_nullable
              as bool?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectTypeVoCopyWith<$Res>
    implements $SubjectTypeVoCopyWith<$Res> {
  factory _$$_SubjectTypeVoCopyWith(
          _$_SubjectTypeVo value, $Res Function(_$_SubjectTypeVo) then) =
      __$$_SubjectTypeVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectTypeDesc,
      bool? isPosition,
      bool? subjectNewlyObtained,
      int? classId});
}

/// @nodoc
class __$$_SubjectTypeVoCopyWithImpl<$Res>
    extends _$SubjectTypeVoCopyWithImpl<$Res, _$_SubjectTypeVo>
    implements _$$_SubjectTypeVoCopyWith<$Res> {
  __$$_SubjectTypeVoCopyWithImpl(
      _$_SubjectTypeVo _value, $Res Function(_$_SubjectTypeVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? isPosition = freezed,
    Object? subjectNewlyObtained = freezed,
    Object? classId = freezed,
  }) {
    return _then(_$_SubjectTypeVo(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      isPosition: freezed == isPosition
          ? _value.isPosition
          : isPosition // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectNewlyObtained: freezed == subjectNewlyObtained
          ? _value.subjectNewlyObtained
          : subjectNewlyObtained // ignore: cast_nullable_to_non_nullable
              as bool?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectTypeVo implements _SubjectTypeVo {
  const _$_SubjectTypeVo(
      {this.subjectType,
      this.subjectTypeDesc,
      this.isPosition,
      this.subjectNewlyObtained,
      this.classId});

  factory _$_SubjectTypeVo.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectTypeVoFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectTypeDesc;

  /// 是否定位当前科目
  @override
  final bool? isPosition;
// 是否获得新科目
  @override
  final bool? subjectNewlyObtained;
  @override
  final int? classId;

  @override
  String toString() {
    return 'SubjectTypeVo(subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, isPosition: $isPosition, subjectNewlyObtained: $subjectNewlyObtained, classId: $classId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectTypeVo &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.isPosition, isPosition) ||
                other.isPosition == isPosition) &&
            (identical(other.subjectNewlyObtained, subjectNewlyObtained) ||
                other.subjectNewlyObtained == subjectNewlyObtained) &&
            (identical(other.classId, classId) || other.classId == classId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, subjectTypeDesc,
      isPosition, subjectNewlyObtained, classId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectTypeVoCopyWith<_$_SubjectTypeVo> get copyWith =>
      __$$_SubjectTypeVoCopyWithImpl<_$_SubjectTypeVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectTypeVoToJson(
      this,
    );
  }
}

abstract class _SubjectTypeVo implements SubjectTypeVo {
  const factory _SubjectTypeVo(
      {final int? subjectType,
      final String? subjectTypeDesc,
      final bool? isPosition,
      final bool? subjectNewlyObtained,
      final int? classId}) = _$_SubjectTypeVo;

  factory _SubjectTypeVo.fromJson(Map<String, dynamic> json) =
      _$_SubjectTypeVo.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectTypeDesc;
  @override

  /// 是否定位当前科目
  bool? get isPosition;
  @override // 是否获得新科目
  bool? get subjectNewlyObtained;
  @override
  int? get classId;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectTypeVoCopyWith<_$_SubjectTypeVo> get copyWith =>
      throw _privateConstructorUsedError;
}

TrainTookitVo _$TrainTookitVoFromJson(Map<String, dynamic> json) {
  return _TrainTookitVo.fromJson(json);
}

/// @nodoc
mixin _$TrainTookitVo {
  String? get background => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  List<ToolkitServiceBo>? get toolkitServiceList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TrainTookitVoCopyWith<TrainTookitVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrainTookitVoCopyWith<$Res> {
  factory $TrainTookitVoCopyWith(
          TrainTookitVo value, $Res Function(TrainTookitVo) then) =
      _$TrainTookitVoCopyWithImpl<$Res, TrainTookitVo>;
  @useResult
  $Res call(
      {String? background,
      String? title,
      String? desc,
      List<ToolkitServiceBo>? toolkitServiceList});
}

/// @nodoc
class _$TrainTookitVoCopyWithImpl<$Res, $Val extends TrainTookitVo>
    implements $TrainTookitVoCopyWith<$Res> {
  _$TrainTookitVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? background = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? toolkitServiceList = freezed,
  }) {
    return _then(_value.copyWith(
      background: freezed == background
          ? _value.background
          : background // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      toolkitServiceList: freezed == toolkitServiceList
          ? _value.toolkitServiceList
          : toolkitServiceList // ignore: cast_nullable_to_non_nullable
              as List<ToolkitServiceBo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TrainTookitVoCopyWith<$Res>
    implements $TrainTookitVoCopyWith<$Res> {
  factory _$$_TrainTookitVoCopyWith(
          _$_TrainTookitVo value, $Res Function(_$_TrainTookitVo) then) =
      __$$_TrainTookitVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? background,
      String? title,
      String? desc,
      List<ToolkitServiceBo>? toolkitServiceList});
}

/// @nodoc
class __$$_TrainTookitVoCopyWithImpl<$Res>
    extends _$TrainTookitVoCopyWithImpl<$Res, _$_TrainTookitVo>
    implements _$$_TrainTookitVoCopyWith<$Res> {
  __$$_TrainTookitVoCopyWithImpl(
      _$_TrainTookitVo _value, $Res Function(_$_TrainTookitVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? background = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? toolkitServiceList = freezed,
  }) {
    return _then(_$_TrainTookitVo(
      background: freezed == background
          ? _value.background
          : background // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      toolkitServiceList: freezed == toolkitServiceList
          ? _value._toolkitServiceList
          : toolkitServiceList // ignore: cast_nullable_to_non_nullable
              as List<ToolkitServiceBo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TrainTookitVo implements _TrainTookitVo {
  const _$_TrainTookitVo(
      {this.background,
      this.title,
      this.desc,
      final List<ToolkitServiceBo>? toolkitServiceList})
      : _toolkitServiceList = toolkitServiceList;

  factory _$_TrainTookitVo.fromJson(Map<String, dynamic> json) =>
      _$$_TrainTookitVoFromJson(json);

  @override
  final String? background;
  @override
  final String? title;
  @override
  final String? desc;
  final List<ToolkitServiceBo>? _toolkitServiceList;
  @override
  List<ToolkitServiceBo>? get toolkitServiceList {
    final value = _toolkitServiceList;
    if (value == null) return null;
    if (_toolkitServiceList is EqualUnmodifiableListView)
      return _toolkitServiceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TrainTookitVo(background: $background, title: $title, desc: $desc, toolkitServiceList: $toolkitServiceList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TrainTookitVo &&
            (identical(other.background, background) ||
                other.background == background) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            const DeepCollectionEquality()
                .equals(other._toolkitServiceList, _toolkitServiceList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, background, title, desc,
      const DeepCollectionEquality().hash(_toolkitServiceList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TrainTookitVoCopyWith<_$_TrainTookitVo> get copyWith =>
      __$$_TrainTookitVoCopyWithImpl<_$_TrainTookitVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TrainTookitVoToJson(
      this,
    );
  }
}

abstract class _TrainTookitVo implements TrainTookitVo {
  const factory _TrainTookitVo(
      {final String? background,
      final String? title,
      final String? desc,
      final List<ToolkitServiceBo>? toolkitServiceList}) = _$_TrainTookitVo;

  factory _TrainTookitVo.fromJson(Map<String, dynamic> json) =
      _$_TrainTookitVo.fromJson;

  @override
  String? get background;
  @override
  String? get title;
  @override
  String? get desc;
  @override
  List<ToolkitServiceBo>? get toolkitServiceList;
  @override
  @JsonKey(ignore: true)
  _$$_TrainTookitVoCopyWith<_$_TrainTookitVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ToolkitServiceBo _$ToolkitServiceBoFromJson(Map<String, dynamic> json) {
  return _ToolkitServiceBo.fromJson(json);
}

/// @nodoc
mixin _$ToolkitServiceBo {
  int? get type => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get button => throw _privateConstructorUsedError;
  String? get toast => throw _privateConstructorUsedError;
  int? get redDot => throw _privateConstructorUsedError; // 红点(0:不显示，1：显示)
  int? get parentVerify => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ToolkitServiceBoCopyWith<ToolkitServiceBo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ToolkitServiceBoCopyWith<$Res> {
  factory $ToolkitServiceBoCopyWith(
          ToolkitServiceBo value, $Res Function(ToolkitServiceBo) then) =
      _$ToolkitServiceBoCopyWithImpl<$Res, ToolkitServiceBo>;
  @useResult
  $Res call(
      {int? type,
      int? order,
      String? icon,
      String? title,
      String? desc,
      String? route,
      String? button,
      String? toast,
      int? redDot,
      int? parentVerify});
}

/// @nodoc
class _$ToolkitServiceBoCopyWithImpl<$Res, $Val extends ToolkitServiceBo>
    implements $ToolkitServiceBoCopyWith<$Res> {
  _$ToolkitServiceBoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? order = freezed,
    Object? icon = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? route = freezed,
    Object? button = freezed,
    Object? toast = freezed,
    Object? redDot = freezed,
    Object? parentVerify = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      redDot: freezed == redDot
          ? _value.redDot
          : redDot // ignore: cast_nullable_to_non_nullable
              as int?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ToolkitServiceBoCopyWith<$Res>
    implements $ToolkitServiceBoCopyWith<$Res> {
  factory _$$_ToolkitServiceBoCopyWith(
          _$_ToolkitServiceBo value, $Res Function(_$_ToolkitServiceBo) then) =
      __$$_ToolkitServiceBoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? type,
      int? order,
      String? icon,
      String? title,
      String? desc,
      String? route,
      String? button,
      String? toast,
      int? redDot,
      int? parentVerify});
}

/// @nodoc
class __$$_ToolkitServiceBoCopyWithImpl<$Res>
    extends _$ToolkitServiceBoCopyWithImpl<$Res, _$_ToolkitServiceBo>
    implements _$$_ToolkitServiceBoCopyWith<$Res> {
  __$$_ToolkitServiceBoCopyWithImpl(
      _$_ToolkitServiceBo _value, $Res Function(_$_ToolkitServiceBo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? order = freezed,
    Object? icon = freezed,
    Object? title = freezed,
    Object? desc = freezed,
    Object? route = freezed,
    Object? button = freezed,
    Object? toast = freezed,
    Object? redDot = freezed,
    Object? parentVerify = freezed,
  }) {
    return _then(_$_ToolkitServiceBo(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      redDot: freezed == redDot
          ? _value.redDot
          : redDot // ignore: cast_nullable_to_non_nullable
              as int?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ToolkitServiceBo implements _ToolkitServiceBo {
  const _$_ToolkitServiceBo(
      {this.type,
      this.order,
      this.icon,
      this.title,
      this.desc,
      this.route,
      this.button,
      this.toast,
      this.redDot,
      this.parentVerify});

  factory _$_ToolkitServiceBo.fromJson(Map<String, dynamic> json) =>
      _$$_ToolkitServiceBoFromJson(json);

  @override
  final int? type;
  @override
  final int? order;
  @override
  final String? icon;
  @override
  final String? title;
  @override
  final String? desc;
  @override
  final String? route;
  @override
  final String? button;
  @override
  final String? toast;
  @override
  final int? redDot;
// 红点(0:不显示，1：显示)
  @override
  final int? parentVerify;

  @override
  String toString() {
    return 'ToolkitServiceBo(type: $type, order: $order, icon: $icon, title: $title, desc: $desc, route: $route, button: $button, toast: $toast, redDot: $redDot, parentVerify: $parentVerify)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ToolkitServiceBo &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.button, button) || other.button == button) &&
            (identical(other.toast, toast) || other.toast == toast) &&
            (identical(other.redDot, redDot) || other.redDot == redDot) &&
            (identical(other.parentVerify, parentVerify) ||
                other.parentVerify == parentVerify));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, order, icon, title, desc,
      route, button, toast, redDot, parentVerify);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ToolkitServiceBoCopyWith<_$_ToolkitServiceBo> get copyWith =>
      __$$_ToolkitServiceBoCopyWithImpl<_$_ToolkitServiceBo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ToolkitServiceBoToJson(
      this,
    );
  }
}

abstract class _ToolkitServiceBo implements ToolkitServiceBo {
  const factory _ToolkitServiceBo(
      {final int? type,
      final int? order,
      final String? icon,
      final String? title,
      final String? desc,
      final String? route,
      final String? button,
      final String? toast,
      final int? redDot,
      final int? parentVerify}) = _$_ToolkitServiceBo;

  factory _ToolkitServiceBo.fromJson(Map<String, dynamic> json) =
      _$_ToolkitServiceBo.fromJson;

  @override
  int? get type;
  @override
  int? get order;
  @override
  String? get icon;
  @override
  String? get title;
  @override
  String? get desc;
  @override
  String? get route;
  @override
  String? get button;
  @override
  String? get toast;
  @override
  int? get redDot;
  @override // 红点(0:不显示，1：显示)
  int? get parentVerify;
  @override
  @JsonKey(ignore: true)
  _$$_ToolkitServiceBoCopyWith<_$_ToolkitServiceBo> get copyWith =>
      throw _privateConstructorUsedError;
}

ContactTeacherInfo _$ContactTeacherInfoFromJson(Map<String, dynamic> json) {
  return _ContactTeacherInfo.fromJson(json);
}

/// @nodoc
mixin _$ContactTeacherInfo {
  String? get nickname => throw _privateConstructorUsedError; //指导师名称
  String? get text => throw _privateConstructorUsedError; //指导师文案
  String? get profileUrl => throw _privateConstructorUsedError; //指导师头像
  String? get contactRoute => throw _privateConstructorUsedError; //指导师路由
  String? get addButton => throw _privateConstructorUsedError; //指导师按钮名称
  String? get cancelButton => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactTeacherInfoCopyWith<ContactTeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactTeacherInfoCopyWith<$Res> {
  factory $ContactTeacherInfoCopyWith(
          ContactTeacherInfo value, $Res Function(ContactTeacherInfo) then) =
      _$ContactTeacherInfoCopyWithImpl<$Res, ContactTeacherInfo>;
  @useResult
  $Res call(
      {String? nickname,
      String? text,
      String? profileUrl,
      String? contactRoute,
      String? addButton,
      String? cancelButton});
}

/// @nodoc
class _$ContactTeacherInfoCopyWithImpl<$Res, $Val extends ContactTeacherInfo>
    implements $ContactTeacherInfoCopyWith<$Res> {
  _$ContactTeacherInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? text = freezed,
    Object? profileUrl = freezed,
    Object? contactRoute = freezed,
    Object? addButton = freezed,
    Object? cancelButton = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      addButton: freezed == addButton
          ? _value.addButton
          : addButton // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButton: freezed == cancelButton
          ? _value.cancelButton
          : cancelButton // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContactTeacherInfoCopyWith<$Res>
    implements $ContactTeacherInfoCopyWith<$Res> {
  factory _$$_ContactTeacherInfoCopyWith(_$_ContactTeacherInfo value,
          $Res Function(_$_ContactTeacherInfo) then) =
      __$$_ContactTeacherInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      String? text,
      String? profileUrl,
      String? contactRoute,
      String? addButton,
      String? cancelButton});
}

/// @nodoc
class __$$_ContactTeacherInfoCopyWithImpl<$Res>
    extends _$ContactTeacherInfoCopyWithImpl<$Res, _$_ContactTeacherInfo>
    implements _$$_ContactTeacherInfoCopyWith<$Res> {
  __$$_ContactTeacherInfoCopyWithImpl(
      _$_ContactTeacherInfo _value, $Res Function(_$_ContactTeacherInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? text = freezed,
    Object? profileUrl = freezed,
    Object? contactRoute = freezed,
    Object? addButton = freezed,
    Object? cancelButton = freezed,
  }) {
    return _then(_$_ContactTeacherInfo(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      addButton: freezed == addButton
          ? _value.addButton
          : addButton // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButton: freezed == cancelButton
          ? _value.cancelButton
          : cancelButton // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContactTeacherInfo implements _ContactTeacherInfo {
  const _$_ContactTeacherInfo(
      {this.nickname,
      this.text,
      this.profileUrl,
      this.contactRoute,
      this.addButton,
      this.cancelButton});

  factory _$_ContactTeacherInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ContactTeacherInfoFromJson(json);

  @override
  final String? nickname;
//指导师名称
  @override
  final String? text;
//指导师文案
  @override
  final String? profileUrl;
//指导师头像
  @override
  final String? contactRoute;
//指导师路由
  @override
  final String? addButton;
//指导师按钮名称
  @override
  final String? cancelButton;

  @override
  String toString() {
    return 'ContactTeacherInfo(nickname: $nickname, text: $text, profileUrl: $profileUrl, contactRoute: $contactRoute, addButton: $addButton, cancelButton: $cancelButton)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContactTeacherInfo &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.profileUrl, profileUrl) ||
                other.profileUrl == profileUrl) &&
            (identical(other.contactRoute, contactRoute) ||
                other.contactRoute == contactRoute) &&
            (identical(other.addButton, addButton) ||
                other.addButton == addButton) &&
            (identical(other.cancelButton, cancelButton) ||
                other.cancelButton == cancelButton));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickname, text, profileUrl,
      contactRoute, addButton, cancelButton);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContactTeacherInfoCopyWith<_$_ContactTeacherInfo> get copyWith =>
      __$$_ContactTeacherInfoCopyWithImpl<_$_ContactTeacherInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContactTeacherInfoToJson(
      this,
    );
  }
}

abstract class _ContactTeacherInfo implements ContactTeacherInfo {
  const factory _ContactTeacherInfo(
      {final String? nickname,
      final String? text,
      final String? profileUrl,
      final String? contactRoute,
      final String? addButton,
      final String? cancelButton}) = _$_ContactTeacherInfo;

  factory _ContactTeacherInfo.fromJson(Map<String, dynamic> json) =
      _$_ContactTeacherInfo.fromJson;

  @override
  String? get nickname;
  @override //指导师名称
  String? get text;
  @override //指导师文案
  String? get profileUrl;
  @override //指导师头像
  String? get contactRoute;
  @override //指导师路由
  String? get addButton;
  @override //指导师按钮名称
  String? get cancelButton;
  @override
  @JsonKey(ignore: true)
  _$$_ContactTeacherInfoCopyWith<_$_ContactTeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

PopInfo _$PopInfoFromJson(Map<String, dynamic> json) {
  return _PopInfo.fromJson(json);
}

/// @nodoc
mixin _$PopInfo {
  String? get popupTitle => throw _privateConstructorUsedError;
  List<String?>? get introduceImageList => throw _privateConstructorUsedError;
  String? get buttonRoute => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get closedText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PopInfoCopyWith<PopInfo> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PopInfoCopyWith<$Res> {
  factory $PopInfoCopyWith(PopInfo value, $Res Function(PopInfo) then) =
      _$PopInfoCopyWithImpl<$Res, PopInfo>;
  @useResult
  $Res call(
      {String? popupTitle,
      List<String?>? introduceImageList,
      String? buttonRoute,
      String? buttonText,
      String? closedText});
}

/// @nodoc
class _$PopInfoCopyWithImpl<$Res, $Val extends PopInfo>
    implements $PopInfoCopyWith<$Res> {
  _$PopInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupTitle = freezed,
    Object? introduceImageList = freezed,
    Object? buttonRoute = freezed,
    Object? buttonText = freezed,
    Object? closedText = freezed,
  }) {
    return _then(_value.copyWith(
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      introduceImageList: freezed == introduceImageList
          ? _value.introduceImageList
          : introduceImageList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PopInfoCopyWith<$Res> implements $PopInfoCopyWith<$Res> {
  factory _$$_PopInfoCopyWith(
          _$_PopInfo value, $Res Function(_$_PopInfo) then) =
      __$$_PopInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? popupTitle,
      List<String?>? introduceImageList,
      String? buttonRoute,
      String? buttonText,
      String? closedText});
}

/// @nodoc
class __$$_PopInfoCopyWithImpl<$Res>
    extends _$PopInfoCopyWithImpl<$Res, _$_PopInfo>
    implements _$$_PopInfoCopyWith<$Res> {
  __$$_PopInfoCopyWithImpl(_$_PopInfo _value, $Res Function(_$_PopInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupTitle = freezed,
    Object? introduceImageList = freezed,
    Object? buttonRoute = freezed,
    Object? buttonText = freezed,
    Object? closedText = freezed,
  }) {
    return _then(_$_PopInfo(
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      introduceImageList: freezed == introduceImageList
          ? _value._introduceImageList
          : introduceImageList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PopInfo implements _PopInfo {
  const _$_PopInfo(
      {this.popupTitle,
      final List<String?>? introduceImageList,
      this.buttonRoute,
      this.buttonText,
      this.closedText})
      : _introduceImageList = introduceImageList;

  factory _$_PopInfo.fromJson(Map<String, dynamic> json) =>
      _$$_PopInfoFromJson(json);

  @override
  final String? popupTitle;
  final List<String?>? _introduceImageList;
  @override
  List<String?>? get introduceImageList {
    final value = _introduceImageList;
    if (value == null) return null;
    if (_introduceImageList is EqualUnmodifiableListView)
      return _introduceImageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? buttonRoute;
  @override
  final String? buttonText;
  @override
  final String? closedText;

  @override
  String toString() {
    return 'PopInfo(popupTitle: $popupTitle, introduceImageList: $introduceImageList, buttonRoute: $buttonRoute, buttonText: $buttonText, closedText: $closedText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PopInfo &&
            (identical(other.popupTitle, popupTitle) ||
                other.popupTitle == popupTitle) &&
            const DeepCollectionEquality()
                .equals(other._introduceImageList, _introduceImageList) &&
            (identical(other.buttonRoute, buttonRoute) ||
                other.buttonRoute == buttonRoute) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.closedText, closedText) ||
                other.closedText == closedText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      popupTitle,
      const DeepCollectionEquality().hash(_introduceImageList),
      buttonRoute,
      buttonText,
      closedText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PopInfoCopyWith<_$_PopInfo> get copyWith =>
      __$$_PopInfoCopyWithImpl<_$_PopInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PopInfoToJson(
      this,
    );
  }
}

abstract class _PopInfo implements PopInfo {
  const factory _PopInfo(
      {final String? popupTitle,
      final List<String?>? introduceImageList,
      final String? buttonRoute,
      final String? buttonText,
      final String? closedText}) = _$_PopInfo;

  factory _PopInfo.fromJson(Map<String, dynamic> json) = _$_PopInfo.fromJson;

  @override
  String? get popupTitle;
  @override
  List<String?>? get introduceImageList;
  @override
  String? get buttonRoute;
  @override
  String? get buttonText;
  @override
  String? get closedText;
  @override
  @JsonKey(ignore: true)
  _$$_PopInfoCopyWith<_$_PopInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

UserGifCourse _$UserGifCourseFromJson(Map<String, dynamic> json) {
  return _UserGifCourse.fromJson(json);
}

/// @nodoc
mixin _$UserGifCourse {
  String? get parentUserCourseId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  String? get courseCoverImage => throw _privateConstructorUsedError;
  int? get activateStatus => throw _privateConstructorUsedError;
  String? get unactivatedReason => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get bgImage => throw _privateConstructorUsedError;
  String? get courseLabel => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  int? get taskStatus => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserGifCourseCopyWith<UserGifCourse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserGifCourseCopyWith<$Res> {
  factory $UserGifCourseCopyWith(
          UserGifCourse value, $Res Function(UserGifCourse) then) =
      _$UserGifCourseCopyWithImpl<$Res, UserGifCourse>;
  @useResult
  $Res call(
      {String? parentUserCourseId,
      int? classId,
      String? courseKey,
      String? courseName,
      int? courseType,
      String? courseSegmentName,
      String? courseCoverImage,
      int? activateStatus,
      String? unactivatedReason,
      String? buttonText,
      String? bgImage,
      String? courseLabel,
      int? lessonId,
      int? subjectType,
      int? taskStatus,
      int? segmentId,
      int? weekId,
      String? route});
}

/// @nodoc
class _$UserGifCourseCopyWithImpl<$Res, $Val extends UserGifCourse>
    implements $UserGifCourseCopyWith<$Res> {
  _$UserGifCourseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? parentUserCourseId = freezed,
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? courseCoverImage = freezed,
    Object? activateStatus = freezed,
    Object? unactivatedReason = freezed,
    Object? buttonText = freezed,
    Object? bgImage = freezed,
    Object? courseLabel = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? taskStatus = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      parentUserCourseId: freezed == parentUserCourseId
          ? _value.parentUserCourseId
          : parentUserCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCoverImage: freezed == courseCoverImage
          ? _value.courseCoverImage
          : courseCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserGifCourseCopyWith<$Res>
    implements $UserGifCourseCopyWith<$Res> {
  factory _$$_UserGifCourseCopyWith(
          _$_UserGifCourse value, $Res Function(_$_UserGifCourse) then) =
      __$$_UserGifCourseCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? parentUserCourseId,
      int? classId,
      String? courseKey,
      String? courseName,
      int? courseType,
      String? courseSegmentName,
      String? courseCoverImage,
      int? activateStatus,
      String? unactivatedReason,
      String? buttonText,
      String? bgImage,
      String? courseLabel,
      int? lessonId,
      int? subjectType,
      int? taskStatus,
      int? segmentId,
      int? weekId,
      String? route});
}

/// @nodoc
class __$$_UserGifCourseCopyWithImpl<$Res>
    extends _$UserGifCourseCopyWithImpl<$Res, _$_UserGifCourse>
    implements _$$_UserGifCourseCopyWith<$Res> {
  __$$_UserGifCourseCopyWithImpl(
      _$_UserGifCourse _value, $Res Function(_$_UserGifCourse) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? parentUserCourseId = freezed,
    Object? classId = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? courseCoverImage = freezed,
    Object? activateStatus = freezed,
    Object? unactivatedReason = freezed,
    Object? buttonText = freezed,
    Object? bgImage = freezed,
    Object? courseLabel = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? taskStatus = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_UserGifCourse(
      parentUserCourseId: freezed == parentUserCourseId
          ? _value.parentUserCourseId
          : parentUserCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCoverImage: freezed == courseCoverImage
          ? _value.courseCoverImage
          : courseCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserGifCourse implements _UserGifCourse {
  const _$_UserGifCourse(
      {this.parentUserCourseId,
      this.classId,
      this.courseKey,
      this.courseName,
      this.courseType,
      this.courseSegmentName,
      this.courseCoverImage,
      this.activateStatus,
      this.unactivatedReason,
      this.buttonText,
      this.bgImage,
      this.courseLabel,
      this.lessonId,
      this.subjectType,
      this.taskStatus,
      this.segmentId,
      this.weekId,
      this.route});

  factory _$_UserGifCourse.fromJson(Map<String, dynamic> json) =>
      _$$_UserGifCourseFromJson(json);

  @override
  final String? parentUserCourseId;
  @override
  final int? classId;
  @override
  final String? courseKey;
  @override
  final String? courseName;
  @override
  final int? courseType;
  @override
  final String? courseSegmentName;
  @override
  final String? courseCoverImage;
  @override
  final int? activateStatus;
  @override
  final String? unactivatedReason;
  @override
  final String? buttonText;
  @override
  final String? bgImage;
  @override
  final String? courseLabel;
  @override
  final int? lessonId;
  @override
  final int? subjectType;
  @override
  final int? taskStatus;
  @override
  final int? segmentId;
  @override
  final int? weekId;
  @override
  final String? route;

  @override
  String toString() {
    return 'UserGifCourse(parentUserCourseId: $parentUserCourseId, classId: $classId, courseKey: $courseKey, courseName: $courseName, courseType: $courseType, courseSegmentName: $courseSegmentName, courseCoverImage: $courseCoverImage, activateStatus: $activateStatus, unactivatedReason: $unactivatedReason, buttonText: $buttonText, bgImage: $bgImage, courseLabel: $courseLabel, lessonId: $lessonId, subjectType: $subjectType, taskStatus: $taskStatus, segmentId: $segmentId, weekId: $weekId, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserGifCourse &&
            (identical(other.parentUserCourseId, parentUserCourseId) ||
                other.parentUserCourseId == parentUserCourseId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.courseCoverImage, courseCoverImage) ||
                other.courseCoverImage == courseCoverImage) &&
            (identical(other.activateStatus, activateStatus) ||
                other.activateStatus == activateStatus) &&
            (identical(other.unactivatedReason, unactivatedReason) ||
                other.unactivatedReason == unactivatedReason) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.bgImage, bgImage) || other.bgImage == bgImage) &&
            (identical(other.courseLabel, courseLabel) ||
                other.courseLabel == courseLabel) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      parentUserCourseId,
      classId,
      courseKey,
      courseName,
      courseType,
      courseSegmentName,
      courseCoverImage,
      activateStatus,
      unactivatedReason,
      buttonText,
      bgImage,
      courseLabel,
      lessonId,
      subjectType,
      taskStatus,
      segmentId,
      weekId,
      route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserGifCourseCopyWith<_$_UserGifCourse> get copyWith =>
      __$$_UserGifCourseCopyWithImpl<_$_UserGifCourse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserGifCourseToJson(
      this,
    );
  }
}

abstract class _UserGifCourse implements UserGifCourse {
  const factory _UserGifCourse(
      {final String? parentUserCourseId,
      final int? classId,
      final String? courseKey,
      final String? courseName,
      final int? courseType,
      final String? courseSegmentName,
      final String? courseCoverImage,
      final int? activateStatus,
      final String? unactivatedReason,
      final String? buttonText,
      final String? bgImage,
      final String? courseLabel,
      final int? lessonId,
      final int? subjectType,
      final int? taskStatus,
      final int? segmentId,
      final int? weekId,
      final String? route}) = _$_UserGifCourse;

  factory _UserGifCourse.fromJson(Map<String, dynamic> json) =
      _$_UserGifCourse.fromJson;

  @override
  String? get parentUserCourseId;
  @override
  int? get classId;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override
  int? get courseType;
  @override
  String? get courseSegmentName;
  @override
  String? get courseCoverImage;
  @override
  int? get activateStatus;
  @override
  String? get unactivatedReason;
  @override
  String? get buttonText;
  @override
  String? get bgImage;
  @override
  String? get courseLabel;
  @override
  int? get lessonId;
  @override
  int? get subjectType;
  @override
  int? get taskStatus;
  @override
  int? get segmentId;
  @override
  int? get weekId;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_UserGifCourseCopyWith<_$_UserGifCourse> get copyWith =>
      throw _privateConstructorUsedError;
}

NewGetGuideInfo _$NewGetGuideInfoFromJson(Map<String, dynamic> json) {
  return _NewGetGuideInfo.fromJson(json);
}

/// @nodoc
mixin _$NewGetGuideInfo {
  String? get content => throw _privateConstructorUsedError;
  String? get newGetGuideVoice => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NewGetGuideInfoCopyWith<NewGetGuideInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NewGetGuideInfoCopyWith<$Res> {
  factory $NewGetGuideInfoCopyWith(
          NewGetGuideInfo value, $Res Function(NewGetGuideInfo) then) =
      _$NewGetGuideInfoCopyWithImpl<$Res, NewGetGuideInfo>;
  @useResult
  $Res call({String? content, String? newGetGuideVoice});
}

/// @nodoc
class _$NewGetGuideInfoCopyWithImpl<$Res, $Val extends NewGetGuideInfo>
    implements $NewGetGuideInfoCopyWith<$Res> {
  _$NewGetGuideInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? newGetGuideVoice = freezed,
  }) {
    return _then(_value.copyWith(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideVoice: freezed == newGetGuideVoice
          ? _value.newGetGuideVoice
          : newGetGuideVoice // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_NewGetGuideInfoCopyWith<$Res>
    implements $NewGetGuideInfoCopyWith<$Res> {
  factory _$$_NewGetGuideInfoCopyWith(
          _$_NewGetGuideInfo value, $Res Function(_$_NewGetGuideInfo) then) =
      __$$_NewGetGuideInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? content, String? newGetGuideVoice});
}

/// @nodoc
class __$$_NewGetGuideInfoCopyWithImpl<$Res>
    extends _$NewGetGuideInfoCopyWithImpl<$Res, _$_NewGetGuideInfo>
    implements _$$_NewGetGuideInfoCopyWith<$Res> {
  __$$_NewGetGuideInfoCopyWithImpl(
      _$_NewGetGuideInfo _value, $Res Function(_$_NewGetGuideInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? newGetGuideVoice = freezed,
  }) {
    return _then(_$_NewGetGuideInfo(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideVoice: freezed == newGetGuideVoice
          ? _value.newGetGuideVoice
          : newGetGuideVoice // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_NewGetGuideInfo implements _NewGetGuideInfo {
  const _$_NewGetGuideInfo({this.content, this.newGetGuideVoice});

  factory _$_NewGetGuideInfo.fromJson(Map<String, dynamic> json) =>
      _$$_NewGetGuideInfoFromJson(json);

  @override
  final String? content;
  @override
  final String? newGetGuideVoice;

  @override
  String toString() {
    return 'NewGetGuideInfo(content: $content, newGetGuideVoice: $newGetGuideVoice)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_NewGetGuideInfo &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.newGetGuideVoice, newGetGuideVoice) ||
                other.newGetGuideVoice == newGetGuideVoice));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, content, newGetGuideVoice);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_NewGetGuideInfoCopyWith<_$_NewGetGuideInfo> get copyWith =>
      __$$_NewGetGuideInfoCopyWithImpl<_$_NewGetGuideInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_NewGetGuideInfoToJson(
      this,
    );
  }
}

abstract class _NewGetGuideInfo implements NewGetGuideInfo {
  const factory _NewGetGuideInfo(
      {final String? content,
      final String? newGetGuideVoice}) = _$_NewGetGuideInfo;

  factory _NewGetGuideInfo.fromJson(Map<String, dynamic> json) =
      _$_NewGetGuideInfo.fromJson;

  @override
  String? get content;
  @override
  String? get newGetGuideVoice;
  @override
  @JsonKey(ignore: true)
  _$$_NewGetGuideInfoCopyWith<_$_NewGetGuideInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
