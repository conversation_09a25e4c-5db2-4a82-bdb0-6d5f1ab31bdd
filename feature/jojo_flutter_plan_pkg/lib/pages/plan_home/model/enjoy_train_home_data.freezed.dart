// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enjoy_train_home_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

EnjoyTrainHomeData _$EnjoyTrainHomeDataFromJson(Map<String, dynamic> json) {
  return _EnjoyTrainHomeData.fromJson(json);
}

/// @nodoc
mixin _$EnjoyTrainHomeData {
  List<SubjectTypeVo>? get subjectTabList => throw _privateConstructorUsedError;
  set subjectTabList(List<SubjectTypeVo>? value) =>
      throw _privateConstructorUsedError;

  /// 课程卡片列表 同后端 对齐，纯享版本 内部的scheduleTaskList 长度 最多为1 ，有多个时 会返回多个 ScheduleInfo
  List<ScheduleInfo>? get courseScheduCardList =>
      throw _privateConstructorUsedError;

  /// 课程卡片列表 同后端 对齐，纯享版本 内部的scheduleTaskList 长度 最多为1 ，有多个时 会返回多个 ScheduleInfo
  set courseScheduCardList(List<ScheduleInfo>? value) =>
      throw _privateConstructorUsedError;

  /// 是否可获得新课 0 不可获得， 1 可获得
  int? get takeCourse => throw _privateConstructorUsedError;

  /// 是否可获得新课 0 不可获得， 1 可获得
  set takeCourse(int? value) => throw _privateConstructorUsedError;

  /// 是否是纯享版  0非纯享， 1 纯享版
  int? get pureEnjoyTrain => throw _privateConstructorUsedError;

  /// 是否是纯享版  0非纯享， 1 纯享版
  set pureEnjoyTrain(int? value) => throw _privateConstructorUsedError;

  /// 客服路由
  String? get contactRoute => throw _privateConstructorUsedError;

  /// 客服路由
  set contactRoute(String? value) => throw _privateConstructorUsedError;
  List<SchedulePopup>? get schedulePopupList =>
      throw _privateConstructorUsedError;
  set schedulePopupList(List<SchedulePopup>? value) =>
      throw _privateConstructorUsedError;
  List<TrainCampTipData>? get courseGuideList =>
      throw _privateConstructorUsedError;
  set courseGuideList(List<TrainCampTipData>? value) =>
      throw _privateConstructorUsedError;

  /// 使用自转化对象
  List<PlanAutoTransformPageData>? get multipleClassTrialDataList =>
      throw _privateConstructorUsedError;

  /// 使用自转化对象
  set multipleClassTrialDataList(List<PlanAutoTransformPageData>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EnjoyTrainHomeDataCopyWith<EnjoyTrainHomeData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnjoyTrainHomeDataCopyWith<$Res> {
  factory $EnjoyTrainHomeDataCopyWith(
          EnjoyTrainHomeData value, $Res Function(EnjoyTrainHomeData) then) =
      _$EnjoyTrainHomeDataCopyWithImpl<$Res, EnjoyTrainHomeData>;
  @useResult
  $Res call(
      {List<SubjectTypeVo>? subjectTabList,
      List<ScheduleInfo>? courseScheduCardList,
      int? takeCourse,
      int? pureEnjoyTrain,
      String? contactRoute,
      List<SchedulePopup>? schedulePopupList,
      List<TrainCampTipData>? courseGuideList,
      List<PlanAutoTransformPageData>? multipleClassTrialDataList});
}

/// @nodoc
class _$EnjoyTrainHomeDataCopyWithImpl<$Res, $Val extends EnjoyTrainHomeData>
    implements $EnjoyTrainHomeDataCopyWith<$Res> {
  _$EnjoyTrainHomeDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? courseScheduCardList = freezed,
    Object? takeCourse = freezed,
    Object? pureEnjoyTrain = freezed,
    Object? contactRoute = freezed,
    Object? schedulePopupList = freezed,
    Object? courseGuideList = freezed,
    Object? multipleClassTrialDataList = freezed,
  }) {
    return _then(_value.copyWith(
      subjectTabList: freezed == subjectTabList
          ? _value.subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVo>?,
      courseScheduCardList: freezed == courseScheduCardList
          ? _value.courseScheduCardList
          : courseScheduCardList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
      takeCourse: freezed == takeCourse
          ? _value.takeCourse
          : takeCourse // ignore: cast_nullable_to_non_nullable
              as int?,
      pureEnjoyTrain: freezed == pureEnjoyTrain
          ? _value.pureEnjoyTrain
          : pureEnjoyTrain // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      schedulePopupList: freezed == schedulePopupList
          ? _value.schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
      courseGuideList: freezed == courseGuideList
          ? _value.courseGuideList
          : courseGuideList // ignore: cast_nullable_to_non_nullable
              as List<TrainCampTipData>?,
      multipleClassTrialDataList: freezed == multipleClassTrialDataList
          ? _value.multipleClassTrialDataList
          : multipleClassTrialDataList // ignore: cast_nullable_to_non_nullable
              as List<PlanAutoTransformPageData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EnjoyTrainHomeDataCopyWith<$Res>
    implements $EnjoyTrainHomeDataCopyWith<$Res> {
  factory _$$_EnjoyTrainHomeDataCopyWith(_$_EnjoyTrainHomeData value,
          $Res Function(_$_EnjoyTrainHomeData) then) =
      __$$_EnjoyTrainHomeDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectTypeVo>? subjectTabList,
      List<ScheduleInfo>? courseScheduCardList,
      int? takeCourse,
      int? pureEnjoyTrain,
      String? contactRoute,
      List<SchedulePopup>? schedulePopupList,
      List<TrainCampTipData>? courseGuideList,
      List<PlanAutoTransformPageData>? multipleClassTrialDataList});
}

/// @nodoc
class __$$_EnjoyTrainHomeDataCopyWithImpl<$Res>
    extends _$EnjoyTrainHomeDataCopyWithImpl<$Res, _$_EnjoyTrainHomeData>
    implements _$$_EnjoyTrainHomeDataCopyWith<$Res> {
  __$$_EnjoyTrainHomeDataCopyWithImpl(
      _$_EnjoyTrainHomeData _value, $Res Function(_$_EnjoyTrainHomeData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectTabList = freezed,
    Object? courseScheduCardList = freezed,
    Object? takeCourse = freezed,
    Object? pureEnjoyTrain = freezed,
    Object? contactRoute = freezed,
    Object? schedulePopupList = freezed,
    Object? courseGuideList = freezed,
    Object? multipleClassTrialDataList = freezed,
  }) {
    return _then(_$_EnjoyTrainHomeData(
      subjectTabList: freezed == subjectTabList
          ? _value.subjectTabList
          : subjectTabList // ignore: cast_nullable_to_non_nullable
              as List<SubjectTypeVo>?,
      courseScheduCardList: freezed == courseScheduCardList
          ? _value.courseScheduCardList
          : courseScheduCardList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
      takeCourse: freezed == takeCourse
          ? _value.takeCourse
          : takeCourse // ignore: cast_nullable_to_non_nullable
              as int?,
      pureEnjoyTrain: freezed == pureEnjoyTrain
          ? _value.pureEnjoyTrain
          : pureEnjoyTrain // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      schedulePopupList: freezed == schedulePopupList
          ? _value.schedulePopupList
          : schedulePopupList // ignore: cast_nullable_to_non_nullable
              as List<SchedulePopup>?,
      courseGuideList: freezed == courseGuideList
          ? _value.courseGuideList
          : courseGuideList // ignore: cast_nullable_to_non_nullable
              as List<TrainCampTipData>?,
      multipleClassTrialDataList: freezed == multipleClassTrialDataList
          ? _value.multipleClassTrialDataList
          : multipleClassTrialDataList // ignore: cast_nullable_to_non_nullable
              as List<PlanAutoTransformPageData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EnjoyTrainHomeData implements _EnjoyTrainHomeData {
  _$_EnjoyTrainHomeData(
      {this.subjectTabList,
      this.courseScheduCardList,
      this.takeCourse,
      this.pureEnjoyTrain,
      this.contactRoute,
      this.schedulePopupList,
      this.courseGuideList,
      this.multipleClassTrialDataList});

  factory _$_EnjoyTrainHomeData.fromJson(Map<String, dynamic> json) =>
      _$$_EnjoyTrainHomeDataFromJson(json);

  @override
  List<SubjectTypeVo>? subjectTabList;

  /// 课程卡片列表 同后端 对齐，纯享版本 内部的scheduleTaskList 长度 最多为1 ，有多个时 会返回多个 ScheduleInfo
  @override
  List<ScheduleInfo>? courseScheduCardList;

  /// 是否可获得新课 0 不可获得， 1 可获得
  @override
  int? takeCourse;

  /// 是否是纯享版  0非纯享， 1 纯享版
  @override
  int? pureEnjoyTrain;

  /// 客服路由
  @override
  String? contactRoute;
  @override
  List<SchedulePopup>? schedulePopupList;
  @override
  List<TrainCampTipData>? courseGuideList;

  /// 使用自转化对象
  @override
  List<PlanAutoTransformPageData>? multipleClassTrialDataList;

  @override
  String toString() {
    return 'EnjoyTrainHomeData(subjectTabList: $subjectTabList, courseScheduCardList: $courseScheduCardList, takeCourse: $takeCourse, pureEnjoyTrain: $pureEnjoyTrain, contactRoute: $contactRoute, schedulePopupList: $schedulePopupList, courseGuideList: $courseGuideList, multipleClassTrialDataList: $multipleClassTrialDataList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EnjoyTrainHomeDataCopyWith<_$_EnjoyTrainHomeData> get copyWith =>
      __$$_EnjoyTrainHomeDataCopyWithImpl<_$_EnjoyTrainHomeData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EnjoyTrainHomeDataToJson(
      this,
    );
  }
}

abstract class _EnjoyTrainHomeData implements EnjoyTrainHomeData {
  factory _EnjoyTrainHomeData(
          {List<SubjectTypeVo>? subjectTabList,
          List<ScheduleInfo>? courseScheduCardList,
          int? takeCourse,
          int? pureEnjoyTrain,
          String? contactRoute,
          List<SchedulePopup>? schedulePopupList,
          List<TrainCampTipData>? courseGuideList,
          List<PlanAutoTransformPageData>? multipleClassTrialDataList}) =
      _$_EnjoyTrainHomeData;

  factory _EnjoyTrainHomeData.fromJson(Map<String, dynamic> json) =
      _$_EnjoyTrainHomeData.fromJson;

  @override
  List<SubjectTypeVo>? get subjectTabList;
  set subjectTabList(List<SubjectTypeVo>? value);
  @override

  /// 课程卡片列表 同后端 对齐，纯享版本 内部的scheduleTaskList 长度 最多为1 ，有多个时 会返回多个 ScheduleInfo
  List<ScheduleInfo>? get courseScheduCardList;

  /// 课程卡片列表 同后端 对齐，纯享版本 内部的scheduleTaskList 长度 最多为1 ，有多个时 会返回多个 ScheduleInfo
  set courseScheduCardList(List<ScheduleInfo>? value);
  @override

  /// 是否可获得新课 0 不可获得， 1 可获得
  int? get takeCourse;

  /// 是否可获得新课 0 不可获得， 1 可获得
  set takeCourse(int? value);
  @override

  /// 是否是纯享版  0非纯享， 1 纯享版
  int? get pureEnjoyTrain;

  /// 是否是纯享版  0非纯享， 1 纯享版
  set pureEnjoyTrain(int? value);
  @override

  /// 客服路由
  String? get contactRoute;

  /// 客服路由
  set contactRoute(String? value);
  @override
  List<SchedulePopup>? get schedulePopupList;
  set schedulePopupList(List<SchedulePopup>? value);
  @override
  List<TrainCampTipData>? get courseGuideList;
  set courseGuideList(List<TrainCampTipData>? value);
  @override

  /// 使用自转化对象
  List<PlanAutoTransformPageData>? get multipleClassTrialDataList;

  /// 使用自转化对象
  set multipleClassTrialDataList(List<PlanAutoTransformPageData>? value);
  @override
  @JsonKey(ignore: true)
  _$$_EnjoyTrainHomeDataCopyWith<_$_EnjoyTrainHomeData> get copyWith =>
      throw _privateConstructorUsedError;
}
