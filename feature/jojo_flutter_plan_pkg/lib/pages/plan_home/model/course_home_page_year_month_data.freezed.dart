// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_page_year_month_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomePageYearMonthData _$CourseHomePageYearMonthDataFromJson(
    Map<String, dynamic> json) {
  return _CourseHomePageYearMonthData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomePageYearMonthData {
  List<MonthScheduleInfo>? get monthScheduleInfo =>
      throw _privateConstructorUsedError;
  LessonSummary? get lessonSummary => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomePageYearMonthDataCopyWith<CourseHomePageYearMonthData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomePageYearMonthDataCopyWith<$Res> {
  factory $CourseHomePageYearMonthDataCopyWith(
          CourseHomePageYearMonthData value,
          $Res Function(CourseHomePageYearMonthData) then) =
      _$CourseHomePageYearMonthDataCopyWithImpl<$Res,
          CourseHomePageYearMonthData>;
  @useResult
  $Res call(
      {List<MonthScheduleInfo>? monthScheduleInfo,
      LessonSummary? lessonSummary});

  $LessonSummaryCopyWith<$Res>? get lessonSummary;
}

/// @nodoc
class _$CourseHomePageYearMonthDataCopyWithImpl<$Res,
        $Val extends CourseHomePageYearMonthData>
    implements $CourseHomePageYearMonthDataCopyWith<$Res> {
  _$CourseHomePageYearMonthDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthScheduleInfo = freezed,
    Object? lessonSummary = freezed,
  }) {
    return _then(_value.copyWith(
      monthScheduleInfo: freezed == monthScheduleInfo
          ? _value.monthScheduleInfo
          : monthScheduleInfo // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleInfo>?,
      lessonSummary: freezed == lessonSummary
          ? _value.lessonSummary
          : lessonSummary // ignore: cast_nullable_to_non_nullable
              as LessonSummary?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LessonSummaryCopyWith<$Res>? get lessonSummary {
    if (_value.lessonSummary == null) {
      return null;
    }

    return $LessonSummaryCopyWith<$Res>(_value.lessonSummary!, (value) {
      return _then(_value.copyWith(lessonSummary: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseHomePageYearMonthDataCopyWith<$Res>
    implements $CourseHomePageYearMonthDataCopyWith<$Res> {
  factory _$$_CourseHomePageYearMonthDataCopyWith(
          _$_CourseHomePageYearMonthData value,
          $Res Function(_$_CourseHomePageYearMonthData) then) =
      __$$_CourseHomePageYearMonthDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MonthScheduleInfo>? monthScheduleInfo,
      LessonSummary? lessonSummary});

  @override
  $LessonSummaryCopyWith<$Res>? get lessonSummary;
}

/// @nodoc
class __$$_CourseHomePageYearMonthDataCopyWithImpl<$Res>
    extends _$CourseHomePageYearMonthDataCopyWithImpl<$Res,
        _$_CourseHomePageYearMonthData>
    implements _$$_CourseHomePageYearMonthDataCopyWith<$Res> {
  __$$_CourseHomePageYearMonthDataCopyWithImpl(
      _$_CourseHomePageYearMonthData _value,
      $Res Function(_$_CourseHomePageYearMonthData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthScheduleInfo = freezed,
    Object? lessonSummary = freezed,
  }) {
    return _then(_$_CourseHomePageYearMonthData(
      monthScheduleInfo: freezed == monthScheduleInfo
          ? _value._monthScheduleInfo
          : monthScheduleInfo // ignore: cast_nullable_to_non_nullable
              as List<MonthScheduleInfo>?,
      lessonSummary: freezed == lessonSummary
          ? _value.lessonSummary
          : lessonSummary // ignore: cast_nullable_to_non_nullable
              as LessonSummary?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomePageYearMonthData implements _CourseHomePageYearMonthData {
  const _$_CourseHomePageYearMonthData(
      {final List<MonthScheduleInfo>? monthScheduleInfo, this.lessonSummary})
      : _monthScheduleInfo = monthScheduleInfo;

  factory _$_CourseHomePageYearMonthData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseHomePageYearMonthDataFromJson(json);

  final List<MonthScheduleInfo>? _monthScheduleInfo;
  @override
  List<MonthScheduleInfo>? get monthScheduleInfo {
    final value = _monthScheduleInfo;
    if (value == null) return null;
    if (_monthScheduleInfo is EqualUnmodifiableListView)
      return _monthScheduleInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final LessonSummary? lessonSummary;

  @override
  String toString() {
    return 'CourseHomePageYearMonthData(monthScheduleInfo: $monthScheduleInfo, lessonSummary: $lessonSummary)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomePageYearMonthData &&
            const DeepCollectionEquality()
                .equals(other._monthScheduleInfo, _monthScheduleInfo) &&
            (identical(other.lessonSummary, lessonSummary) ||
                other.lessonSummary == lessonSummary));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_monthScheduleInfo), lessonSummary);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomePageYearMonthDataCopyWith<_$_CourseHomePageYearMonthData>
      get copyWith => __$$_CourseHomePageYearMonthDataCopyWithImpl<
          _$_CourseHomePageYearMonthData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomePageYearMonthDataToJson(
      this,
    );
  }
}

abstract class _CourseHomePageYearMonthData
    implements CourseHomePageYearMonthData {
  const factory _CourseHomePageYearMonthData(
      {final List<MonthScheduleInfo>? monthScheduleInfo,
      final LessonSummary? lessonSummary}) = _$_CourseHomePageYearMonthData;

  factory _CourseHomePageYearMonthData.fromJson(Map<String, dynamic> json) =
      _$_CourseHomePageYearMonthData.fromJson;

  @override
  List<MonthScheduleInfo>? get monthScheduleInfo;
  @override
  LessonSummary? get lessonSummary;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomePageYearMonthDataCopyWith<_$_CourseHomePageYearMonthData>
      get copyWith => throw _privateConstructorUsedError;
}

LessonSummary _$LessonSummaryFromJson(Map<String, dynamic> json) {
  return _LessonSummary.fromJson(json);
}

/// @nodoc
mixin _$LessonSummary {
  String? get unFinishedNum => throw _privateConstructorUsedError;
  String? get finishedNum => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonSummaryCopyWith<LessonSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonSummaryCopyWith<$Res> {
  factory $LessonSummaryCopyWith(
          LessonSummary value, $Res Function(LessonSummary) then) =
      _$LessonSummaryCopyWithImpl<$Res, LessonSummary>;
  @useResult
  $Res call({String? unFinishedNum, String? finishedNum});
}

/// @nodoc
class _$LessonSummaryCopyWithImpl<$Res, $Val extends LessonSummary>
    implements $LessonSummaryCopyWith<$Res> {
  _$LessonSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
  }) {
    return _then(_value.copyWith(
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as String?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonSummaryCopyWith<$Res>
    implements $LessonSummaryCopyWith<$Res> {
  factory _$$_LessonSummaryCopyWith(
          _$_LessonSummary value, $Res Function(_$_LessonSummary) then) =
      __$$_LessonSummaryCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? unFinishedNum, String? finishedNum});
}

/// @nodoc
class __$$_LessonSummaryCopyWithImpl<$Res>
    extends _$LessonSummaryCopyWithImpl<$Res, _$_LessonSummary>
    implements _$$_LessonSummaryCopyWith<$Res> {
  __$$_LessonSummaryCopyWithImpl(
      _$_LessonSummary _value, $Res Function(_$_LessonSummary) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unFinishedNum = freezed,
    Object? finishedNum = freezed,
  }) {
    return _then(_$_LessonSummary(
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as String?,
      finishedNum: freezed == finishedNum
          ? _value.finishedNum
          : finishedNum // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonSummary implements _LessonSummary {
  const _$_LessonSummary({this.unFinishedNum, this.finishedNum});

  factory _$_LessonSummary.fromJson(Map<String, dynamic> json) =>
      _$$_LessonSummaryFromJson(json);

  @override
  final String? unFinishedNum;
  @override
  final String? finishedNum;

  @override
  String toString() {
    return 'LessonSummary(unFinishedNum: $unFinishedNum, finishedNum: $finishedNum)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonSummary &&
            (identical(other.unFinishedNum, unFinishedNum) ||
                other.unFinishedNum == unFinishedNum) &&
            (identical(other.finishedNum, finishedNum) ||
                other.finishedNum == finishedNum));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, unFinishedNum, finishedNum);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonSummaryCopyWith<_$_LessonSummary> get copyWith =>
      __$$_LessonSummaryCopyWithImpl<_$_LessonSummary>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonSummaryToJson(
      this,
    );
  }
}

abstract class _LessonSummary implements LessonSummary {
  const factory _LessonSummary(
      {final String? unFinishedNum,
      final String? finishedNum}) = _$_LessonSummary;

  factory _LessonSummary.fromJson(Map<String, dynamic> json) =
      _$_LessonSummary.fromJson;

  @override
  String? get unFinishedNum;
  @override
  String? get finishedNum;
  @override
  @JsonKey(ignore: true)
  _$$_LessonSummaryCopyWith<_$_LessonSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

MonthScheduleInfo _$MonthScheduleInfoFromJson(Map<String, dynamic> json) {
  return _MonthScheduleInfo.fromJson(json);
}

/// @nodoc
mixin _$MonthScheduleInfo {
  String? get month => throw _privateConstructorUsedError;
  int? get monthNum => throw _privateConstructorUsedError;
  List<ScheduleInfo>? get scheduleInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MonthScheduleInfoCopyWith<MonthScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonthScheduleInfoCopyWith<$Res> {
  factory $MonthScheduleInfoCopyWith(
          MonthScheduleInfo value, $Res Function(MonthScheduleInfo) then) =
      _$MonthScheduleInfoCopyWithImpl<$Res, MonthScheduleInfo>;
  @useResult
  $Res call({String? month, int? monthNum, List<ScheduleInfo>? scheduleInfo});
}

/// @nodoc
class _$MonthScheduleInfoCopyWithImpl<$Res, $Val extends MonthScheduleInfo>
    implements $MonthScheduleInfoCopyWith<$Res> {
  _$MonthScheduleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? scheduleInfo = freezed,
  }) {
    return _then(_value.copyWith(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleInfo: freezed == scheduleInfo
          ? _value.scheduleInfo
          : scheduleInfo // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MonthScheduleInfoCopyWith<$Res>
    implements $MonthScheduleInfoCopyWith<$Res> {
  factory _$$_MonthScheduleInfoCopyWith(_$_MonthScheduleInfo value,
          $Res Function(_$_MonthScheduleInfo) then) =
      __$$_MonthScheduleInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? month, int? monthNum, List<ScheduleInfo>? scheduleInfo});
}

/// @nodoc
class __$$_MonthScheduleInfoCopyWithImpl<$Res>
    extends _$MonthScheduleInfoCopyWithImpl<$Res, _$_MonthScheduleInfo>
    implements _$$_MonthScheduleInfoCopyWith<$Res> {
  __$$_MonthScheduleInfoCopyWithImpl(
      _$_MonthScheduleInfo _value, $Res Function(_$_MonthScheduleInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? month = freezed,
    Object? monthNum = freezed,
    Object? scheduleInfo = freezed,
  }) {
    return _then(_$_MonthScheduleInfo(
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as String?,
      monthNum: freezed == monthNum
          ? _value.monthNum
          : monthNum // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleInfo: freezed == scheduleInfo
          ? _value._scheduleInfo
          : scheduleInfo // ignore: cast_nullable_to_non_nullable
              as List<ScheduleInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MonthScheduleInfo implements _MonthScheduleInfo {
  const _$_MonthScheduleInfo(
      {this.month, this.monthNum, final List<ScheduleInfo>? scheduleInfo})
      : _scheduleInfo = scheduleInfo;

  factory _$_MonthScheduleInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MonthScheduleInfoFromJson(json);

  @override
  final String? month;
  @override
  final int? monthNum;
  final List<ScheduleInfo>? _scheduleInfo;
  @override
  List<ScheduleInfo>? get scheduleInfo {
    final value = _scheduleInfo;
    if (value == null) return null;
    if (_scheduleInfo is EqualUnmodifiableListView) return _scheduleInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MonthScheduleInfo(month: $month, monthNum: $monthNum, scheduleInfo: $scheduleInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MonthScheduleInfo &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.monthNum, monthNum) ||
                other.monthNum == monthNum) &&
            const DeepCollectionEquality()
                .equals(other._scheduleInfo, _scheduleInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, month, monthNum,
      const DeepCollectionEquality().hash(_scheduleInfo));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MonthScheduleInfoCopyWith<_$_MonthScheduleInfo> get copyWith =>
      __$$_MonthScheduleInfoCopyWithImpl<_$_MonthScheduleInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MonthScheduleInfoToJson(
      this,
    );
  }
}

abstract class _MonthScheduleInfo implements MonthScheduleInfo {
  const factory _MonthScheduleInfo(
      {final String? month,
      final int? monthNum,
      final List<ScheduleInfo>? scheduleInfo}) = _$_MonthScheduleInfo;

  factory _MonthScheduleInfo.fromJson(Map<String, dynamic> json) =
      _$_MonthScheduleInfo.fromJson;

  @override
  String? get month;
  @override
  int? get monthNum;
  @override
  List<ScheduleInfo>? get scheduleInfo;
  @override
  @JsonKey(ignore: true)
  _$$_MonthScheduleInfoCopyWith<_$_MonthScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleInfo _$ScheduleInfoFromJson(Map<String, dynamic> json) {
  return _ScheduleInfo.fromJson(json);
}

/// @nodoc
mixin _$ScheduleInfo {
  List<SubjectScheduleStatus>? get subjectScheduleStatus =>
      throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  List<ScheduleTaskList>? get scheduleTaskList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleInfoCopyWith<ScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleInfoCopyWith<$Res> {
  factory $ScheduleInfoCopyWith(
          ScheduleInfo value, $Res Function(ScheduleInfo) then) =
      _$ScheduleInfoCopyWithImpl<$Res, ScheduleInfo>;
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      bool? today,
      List<ScheduleTaskList>? scheduleTaskList});
}

/// @nodoc
class _$ScheduleInfoCopyWithImpl<$Res, $Val extends ScheduleInfo>
    implements $ScheduleInfoCopyWith<$Res> {
  _$ScheduleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? today = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value.subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value.scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleInfoCopyWith<$Res>
    implements $ScheduleInfoCopyWith<$Res> {
  factory _$$_ScheduleInfoCopyWith(
          _$_ScheduleInfo value, $Res Function(_$_ScheduleInfo) then) =
      __$$_ScheduleInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectScheduleStatus>? subjectScheduleStatus,
      int? showDateTime,
      bool? today,
      List<ScheduleTaskList>? scheduleTaskList});
}

/// @nodoc
class __$$_ScheduleInfoCopyWithImpl<$Res>
    extends _$ScheduleInfoCopyWithImpl<$Res, _$_ScheduleInfo>
    implements _$$_ScheduleInfoCopyWith<$Res> {
  __$$_ScheduleInfoCopyWithImpl(
      _$_ScheduleInfo _value, $Res Function(_$_ScheduleInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectScheduleStatus = freezed,
    Object? showDateTime = freezed,
    Object? today = freezed,
    Object? scheduleTaskList = freezed,
  }) {
    return _then(_$_ScheduleInfo(
      subjectScheduleStatus: freezed == subjectScheduleStatus
          ? _value._subjectScheduleStatus
          : subjectScheduleStatus // ignore: cast_nullable_to_non_nullable
              as List<SubjectScheduleStatus>?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      scheduleTaskList: freezed == scheduleTaskList
          ? _value._scheduleTaskList
          : scheduleTaskList // ignore: cast_nullable_to_non_nullable
              as List<ScheduleTaskList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleInfo implements _ScheduleInfo {
  const _$_ScheduleInfo(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      this.showDateTime,
      this.today,
      final List<ScheduleTaskList>? scheduleTaskList})
      : _subjectScheduleStatus = subjectScheduleStatus,
        _scheduleTaskList = scheduleTaskList;

  factory _$_ScheduleInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleInfoFromJson(json);

  final List<SubjectScheduleStatus>? _subjectScheduleStatus;
  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus {
    final value = _subjectScheduleStatus;
    if (value == null) return null;
    if (_subjectScheduleStatus is EqualUnmodifiableListView)
      return _subjectScheduleStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? showDateTime;
  @override
  final bool? today;
  final List<ScheduleTaskList>? _scheduleTaskList;
  @override
  List<ScheduleTaskList>? get scheduleTaskList {
    final value = _scheduleTaskList;
    if (value == null) return null;
    if (_scheduleTaskList is EqualUnmodifiableListView)
      return _scheduleTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ScheduleInfo(subjectScheduleStatus: $subjectScheduleStatus, showDateTime: $showDateTime, today: $today, scheduleTaskList: $scheduleTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleInfo &&
            const DeepCollectionEquality()
                .equals(other._subjectScheduleStatus, _subjectScheduleStatus) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.today, today) || other.today == today) &&
            const DeepCollectionEquality()
                .equals(other._scheduleTaskList, _scheduleTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectScheduleStatus),
      showDateTime,
      today,
      const DeepCollectionEquality().hash(_scheduleTaskList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleInfoCopyWith<_$_ScheduleInfo> get copyWith =>
      __$$_ScheduleInfoCopyWithImpl<_$_ScheduleInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleInfoToJson(
      this,
    );
  }
}

abstract class _ScheduleInfo implements ScheduleInfo {
  const factory _ScheduleInfo(
      {final List<SubjectScheduleStatus>? subjectScheduleStatus,
      final int? showDateTime,
      final bool? today,
      final List<ScheduleTaskList>? scheduleTaskList}) = _$_ScheduleInfo;

  factory _ScheduleInfo.fromJson(Map<String, dynamic> json) =
      _$_ScheduleInfo.fromJson;

  @override
  List<SubjectScheduleStatus>? get subjectScheduleStatus;
  @override
  int? get showDateTime;
  @override
  bool? get today;
  @override
  List<ScheduleTaskList>? get scheduleTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleInfoCopyWith<_$_ScheduleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleTaskList _$ScheduleTaskListFromJson(Map<String, dynamic> json) {
  return _ScheduleTaskList.fromJson(json);
}

/// @nodoc
mixin _$ScheduleTaskList {
  int? get taskStatus => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  int? get taskType => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get scheduleTaskId => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  dynamic get route => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get lessonLabel => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String? get lessonCoverImage => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  List<LessonServiceList>? get lessonServiceList =>
      throw _privateConstructorUsedError;
  List<ChildrenTaskList>? get childrenTaskList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleTaskListCopyWith<ScheduleTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleTaskListCopyWith<$Res> {
  factory $ScheduleTaskListCopyWith(
          ScheduleTaskList value, $Res Function(ScheduleTaskList) then) =
      _$ScheduleTaskListCopyWithImpl<$Res, ScheduleTaskList>;
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? taskType,
      int? classId,
      int? scheduleTaskId,
      int? courseId,
      int? lessonId,
      int? subjectType,
      int? segmentId,
      int? weekId,
      String? title,
      String? subTitle,
      dynamic route,
      String? icon,
      String? lessonLabel,
      String? lessonName,
      String? lessonCoverImage,
      int? courseType,
      String? courseSegmentName,
      List<LessonServiceList>? lessonServiceList,
      List<ChildrenTaskList>? childrenTaskList});
}

/// @nodoc
class _$ScheduleTaskListCopyWithImpl<$Res, $Val extends ScheduleTaskList>
    implements $ScheduleTaskListCopyWith<$Res> {
  _$ScheduleTaskListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? taskType = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonName = freezed,
    Object? lessonCoverImage = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? lessonServiceList = freezed,
    Object? childrenTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as dynamic,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonServiceList: freezed == lessonServiceList
          ? _value.lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceList>?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleTaskListCopyWith<$Res>
    implements $ScheduleTaskListCopyWith<$Res> {
  factory _$$_ScheduleTaskListCopyWith(
          _$_ScheduleTaskList value, $Res Function(_$_ScheduleTaskList) then) =
      __$$_ScheduleTaskListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? taskType,
      int? classId,
      int? scheduleTaskId,
      int? courseId,
      int? lessonId,
      int? subjectType,
      int? segmentId,
      int? weekId,
      String? title,
      String? subTitle,
      dynamic route,
      String? icon,
      String? lessonLabel,
      String? lessonName,
      String? lessonCoverImage,
      int? courseType,
      String? courseSegmentName,
      List<LessonServiceList>? lessonServiceList,
      List<ChildrenTaskList>? childrenTaskList});
}

/// @nodoc
class __$$_ScheduleTaskListCopyWithImpl<$Res>
    extends _$ScheduleTaskListCopyWithImpl<$Res, _$_ScheduleTaskList>
    implements _$$_ScheduleTaskListCopyWith<$Res> {
  __$$_ScheduleTaskListCopyWithImpl(
      _$_ScheduleTaskList _value, $Res Function(_$_ScheduleTaskList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? taskType = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonName = freezed,
    Object? lessonCoverImage = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? lessonServiceList = freezed,
    Object? childrenTaskList = freezed,
  }) {
    return _then(_$_ScheduleTaskList(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as dynamic,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonServiceList: freezed == lessonServiceList
          ? _value._lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceList>?,
      childrenTaskList: freezed == childrenTaskList
          ? _value._childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<ChildrenTaskList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleTaskList implements _ScheduleTaskList {
  const _$_ScheduleTaskList(
      {this.taskStatus,
      this.showDateTime,
      this.taskType,
      this.classId,
      this.scheduleTaskId,
      this.courseId,
      this.lessonId,
      this.subjectType,
      this.segmentId,
      this.weekId,
      this.title,
      this.subTitle,
      this.route,
      this.icon,
      this.lessonLabel,
      this.lessonName,
      this.lessonCoverImage,
      this.courseType,
      this.courseSegmentName,
      final List<LessonServiceList>? lessonServiceList,
      final List<ChildrenTaskList>? childrenTaskList})
      : _lessonServiceList = lessonServiceList,
        _childrenTaskList = childrenTaskList;

  factory _$_ScheduleTaskList.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleTaskListFromJson(json);

  @override
  final int? taskStatus;
  @override
  final int? showDateTime;
  @override
  final int? taskType;
  @override
  final int? classId;
  @override
  final int? scheduleTaskId;
  @override
  final int? courseId;
  @override
  final int? lessonId;
  @override
  final int? subjectType;
  @override
  final int? segmentId;
  @override
  final int? weekId;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final dynamic route;
  @override
  final String? icon;
  @override
  final String? lessonLabel;
  @override
  final String? lessonName;
  @override
  final String? lessonCoverImage;
  @override
  final int? courseType;
  @override
  final String? courseSegmentName;
  final List<LessonServiceList>? _lessonServiceList;
  @override
  List<LessonServiceList>? get lessonServiceList {
    final value = _lessonServiceList;
    if (value == null) return null;
    if (_lessonServiceList is EqualUnmodifiableListView)
      return _lessonServiceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ChildrenTaskList>? _childrenTaskList;
  @override
  List<ChildrenTaskList>? get childrenTaskList {
    final value = _childrenTaskList;
    if (value == null) return null;
    if (_childrenTaskList is EqualUnmodifiableListView)
      return _childrenTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ScheduleTaskList(taskStatus: $taskStatus, showDateTime: $showDateTime, taskType: $taskType, classId: $classId, scheduleTaskId: $scheduleTaskId, courseId: $courseId, lessonId: $lessonId, subjectType: $subjectType, segmentId: $segmentId, weekId: $weekId, title: $title, subTitle: $subTitle, route: $route, icon: $icon, lessonLabel: $lessonLabel, lessonName: $lessonName, lessonCoverImage: $lessonCoverImage, courseType: $courseType, courseSegmentName: $courseSegmentName, lessonServiceList: $lessonServiceList, childrenTaskList: $childrenTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleTaskList &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.taskType, taskType) ||
                other.taskType == taskType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.scheduleTaskId, scheduleTaskId) ||
                other.scheduleTaskId == scheduleTaskId) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            const DeepCollectionEquality().equals(other.route, route) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.lessonLabel, lessonLabel) ||
                other.lessonLabel == lessonLabel) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonCoverImage, lessonCoverImage) ||
                other.lessonCoverImage == lessonCoverImage) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            const DeepCollectionEquality()
                .equals(other._lessonServiceList, _lessonServiceList) &&
            const DeepCollectionEquality()
                .equals(other._childrenTaskList, _childrenTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        taskStatus,
        showDateTime,
        taskType,
        classId,
        scheduleTaskId,
        courseId,
        lessonId,
        subjectType,
        segmentId,
        weekId,
        title,
        subTitle,
        const DeepCollectionEquality().hash(route),
        icon,
        lessonLabel,
        lessonName,
        lessonCoverImage,
        courseType,
        courseSegmentName,
        const DeepCollectionEquality().hash(_lessonServiceList),
        const DeepCollectionEquality().hash(_childrenTaskList)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleTaskListCopyWith<_$_ScheduleTaskList> get copyWith =>
      __$$_ScheduleTaskListCopyWithImpl<_$_ScheduleTaskList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleTaskListToJson(
      this,
    );
  }
}

abstract class _ScheduleTaskList implements ScheduleTaskList {
  const factory _ScheduleTaskList(
      {final int? taskStatus,
      final int? showDateTime,
      final int? taskType,
      final int? classId,
      final int? scheduleTaskId,
      final int? courseId,
      final int? lessonId,
      final int? subjectType,
      final int? segmentId,
      final int? weekId,
      final String? title,
      final String? subTitle,
      final dynamic route,
      final String? icon,
      final String? lessonLabel,
      final String? lessonName,
      final String? lessonCoverImage,
      final int? courseType,
      final String? courseSegmentName,
      final List<LessonServiceList>? lessonServiceList,
      final List<ChildrenTaskList>? childrenTaskList}) = _$_ScheduleTaskList;

  factory _ScheduleTaskList.fromJson(Map<String, dynamic> json) =
      _$_ScheduleTaskList.fromJson;

  @override
  int? get taskStatus;
  @override
  int? get showDateTime;
  @override
  int? get taskType;
  @override
  int? get classId;
  @override
  int? get scheduleTaskId;
  @override
  int? get courseId;
  @override
  int? get lessonId;
  @override
  int? get subjectType;
  @override
  int? get segmentId;
  @override
  int? get weekId;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  dynamic get route;
  @override
  String? get icon;
  @override
  String? get lessonLabel;
  @override
  String? get lessonName;
  @override
  String? get lessonCoverImage;
  @override
  int? get courseType;
  @override
  String? get courseSegmentName;
  @override
  List<LessonServiceList>? get lessonServiceList;
  @override
  List<ChildrenTaskList>? get childrenTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleTaskListCopyWith<_$_ScheduleTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

ChildrenTaskList _$ChildrenTaskListFromJson(Map<String, dynamic> json) {
  return _ChildrenTaskList.fromJson(json);
}

/// @nodoc
mixin _$ChildrenTaskList {
  int? get taskStatus => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  int? get taskType => throw _privateConstructorUsedError;
  int? get scheduleTaskId => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  List<dynamic>? get childrenTaskList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChildrenTaskListCopyWith<ChildrenTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChildrenTaskListCopyWith<$Res> {
  factory $ChildrenTaskListCopyWith(
          ChildrenTaskList value, $Res Function(ChildrenTaskList) then) =
      _$ChildrenTaskListCopyWithImpl<$Res, ChildrenTaskList>;
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? taskType,
      int? scheduleTaskId,
      int? courseType,
      String? courseSegmentName,
      int? subjectType,
      String? title,
      String? subTitle,
      String? icon,
      String? route,
      List<dynamic>? childrenTaskList});
}

/// @nodoc
class _$ChildrenTaskListCopyWithImpl<$Res, $Val extends ChildrenTaskList>
    implements $ChildrenTaskListCopyWith<$Res> {
  _$ChildrenTaskListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? taskType = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? subjectType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? childrenTaskList = freezed,
  }) {
    return _then(_value.copyWith(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      childrenTaskList: freezed == childrenTaskList
          ? _value.childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ChildrenTaskListCopyWith<$Res>
    implements $ChildrenTaskListCopyWith<$Res> {
  factory _$$_ChildrenTaskListCopyWith(
          _$_ChildrenTaskList value, $Res Function(_$_ChildrenTaskList) then) =
      __$$_ChildrenTaskListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskStatus,
      int? showDateTime,
      int? taskType,
      int? scheduleTaskId,
      int? courseType,
      String? courseSegmentName,
      int? subjectType,
      String? title,
      String? subTitle,
      String? icon,
      String? route,
      List<dynamic>? childrenTaskList});
}

/// @nodoc
class __$$_ChildrenTaskListCopyWithImpl<$Res>
    extends _$ChildrenTaskListCopyWithImpl<$Res, _$_ChildrenTaskList>
    implements _$$_ChildrenTaskListCopyWith<$Res> {
  __$$_ChildrenTaskListCopyWithImpl(
      _$_ChildrenTaskList _value, $Res Function(_$_ChildrenTaskList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? showDateTime = freezed,
    Object? taskType = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? subjectType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? childrenTaskList = freezed,
  }) {
    return _then(_$_ChildrenTaskList(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      childrenTaskList: freezed == childrenTaskList
          ? _value._childrenTaskList
          : childrenTaskList // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ChildrenTaskList implements _ChildrenTaskList {
  const _$_ChildrenTaskList(
      {this.taskStatus,
      this.showDateTime,
      this.taskType,
      this.scheduleTaskId,
      this.courseType,
      this.courseSegmentName,
      this.subjectType,
      this.title,
      this.subTitle,
      this.icon,
      this.route,
      final List<dynamic>? childrenTaskList})
      : _childrenTaskList = childrenTaskList;

  factory _$_ChildrenTaskList.fromJson(Map<String, dynamic> json) =>
      _$$_ChildrenTaskListFromJson(json);

  @override
  final int? taskStatus;
  @override
  final int? showDateTime;
  @override
  final int? taskType;
  @override
  final int? scheduleTaskId;
  @override
  final int? courseType;
  @override
  final String? courseSegmentName;
  @override
  final int? subjectType;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final String? icon;
  @override
  final String? route;
  final List<dynamic>? _childrenTaskList;
  @override
  List<dynamic>? get childrenTaskList {
    final value = _childrenTaskList;
    if (value == null) return null;
    if (_childrenTaskList is EqualUnmodifiableListView)
      return _childrenTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ChildrenTaskList(taskStatus: $taskStatus, showDateTime: $showDateTime, taskType: $taskType, scheduleTaskId: $scheduleTaskId, courseType: $courseType, courseSegmentName: $courseSegmentName, subjectType: $subjectType, title: $title, subTitle: $subTitle, icon: $icon, route: $route, childrenTaskList: $childrenTaskList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChildrenTaskList &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.taskType, taskType) ||
                other.taskType == taskType) &&
            (identical(other.scheduleTaskId, scheduleTaskId) ||
                other.scheduleTaskId == scheduleTaskId) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality()
                .equals(other._childrenTaskList, _childrenTaskList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskStatus,
      showDateTime,
      taskType,
      scheduleTaskId,
      courseType,
      courseSegmentName,
      subjectType,
      title,
      subTitle,
      icon,
      route,
      const DeepCollectionEquality().hash(_childrenTaskList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChildrenTaskListCopyWith<_$_ChildrenTaskList> get copyWith =>
      __$$_ChildrenTaskListCopyWithImpl<_$_ChildrenTaskList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ChildrenTaskListToJson(
      this,
    );
  }
}

abstract class _ChildrenTaskList implements ChildrenTaskList {
  const factory _ChildrenTaskList(
      {final int? taskStatus,
      final int? showDateTime,
      final int? taskType,
      final int? scheduleTaskId,
      final int? courseType,
      final String? courseSegmentName,
      final int? subjectType,
      final String? title,
      final String? subTitle,
      final String? icon,
      final String? route,
      final List<dynamic>? childrenTaskList}) = _$_ChildrenTaskList;

  factory _ChildrenTaskList.fromJson(Map<String, dynamic> json) =
      _$_ChildrenTaskList.fromJson;

  @override
  int? get taskStatus;
  @override
  int? get showDateTime;
  @override
  int? get taskType;
  @override
  int? get scheduleTaskId;
  @override
  int? get courseType;
  @override
  String? get courseSegmentName;
  @override
  int? get subjectType;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  String? get icon;
  @override
  String? get route;
  @override
  List<dynamic>? get childrenTaskList;
  @override
  @JsonKey(ignore: true)
  _$$_ChildrenTaskListCopyWith<_$_ChildrenTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonServiceList _$LessonServiceListFromJson(Map<String, dynamic> json) {
  return _LessonServiceList.fromJson(json);
}

/// @nodoc
mixin _$LessonServiceList {
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  bool? get redPoint => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonServiceListCopyWith<LessonServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonServiceListCopyWith<$Res> {
  factory $LessonServiceListCopyWith(
          LessonServiceList value, $Res Function(LessonServiceList) then) =
      _$LessonServiceListCopyWithImpl<$Res, LessonServiceList>;
  @useResult
  $Res call({String? icon, String? name, bool? redPoint, String? route});
}

/// @nodoc
class _$LessonServiceListCopyWithImpl<$Res, $Val extends LessonServiceList>
    implements $LessonServiceListCopyWith<$Res> {
  _$LessonServiceListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonServiceListCopyWith<$Res>
    implements $LessonServiceListCopyWith<$Res> {
  factory _$$_LessonServiceListCopyWith(_$_LessonServiceList value,
          $Res Function(_$_LessonServiceList) then) =
      __$$_LessonServiceListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, String? name, bool? redPoint, String? route});
}

/// @nodoc
class __$$_LessonServiceListCopyWithImpl<$Res>
    extends _$LessonServiceListCopyWithImpl<$Res, _$_LessonServiceList>
    implements _$$_LessonServiceListCopyWith<$Res> {
  __$$_LessonServiceListCopyWithImpl(
      _$_LessonServiceList _value, $Res Function(_$_LessonServiceList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? name = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_LessonServiceList(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonServiceList implements _LessonServiceList {
  const _$_LessonServiceList({this.icon, this.name, this.redPoint, this.route});

  factory _$_LessonServiceList.fromJson(Map<String, dynamic> json) =>
      _$$_LessonServiceListFromJson(json);

  @override
  final String? icon;
  @override
  final String? name;
  @override
  final bool? redPoint;
  @override
  final String? route;

  @override
  String toString() {
    return 'LessonServiceList(icon: $icon, name: $name, redPoint: $redPoint, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonServiceList &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.redPoint, redPoint) ||
                other.redPoint == redPoint) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, icon, name, redPoint, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonServiceListCopyWith<_$_LessonServiceList> get copyWith =>
      __$$_LessonServiceListCopyWithImpl<_$_LessonServiceList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonServiceListToJson(
      this,
    );
  }
}

abstract class _LessonServiceList implements LessonServiceList {
  const factory _LessonServiceList(
      {final String? icon,
      final String? name,
      final bool? redPoint,
      final String? route}) = _$_LessonServiceList;

  factory _LessonServiceList.fromJson(Map<String, dynamic> json) =
      _$_LessonServiceList.fromJson;

  @override
  String? get icon;
  @override
  String? get name;
  @override
  bool? get redPoint;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_LessonServiceListCopyWith<_$_LessonServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectScheduleStatus _$SubjectScheduleStatusFromJson(
    Map<String, dynamic> json) {
  return _SubjectScheduleStatus.fromJson(json);
}

/// @nodoc
mixin _$SubjectScheduleStatus {
  String? get subjectType => throw _privateConstructorUsedError;
  String? get scheduleStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectScheduleStatusCopyWith<SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectScheduleStatusCopyWith<$Res> {
  factory $SubjectScheduleStatusCopyWith(SubjectScheduleStatus value,
          $Res Function(SubjectScheduleStatus) then) =
      _$SubjectScheduleStatusCopyWithImpl<$Res, SubjectScheduleStatus>;
  @useResult
  $Res call({String? subjectType, String? scheduleStatus});
}

/// @nodoc
class _$SubjectScheduleStatusCopyWithImpl<$Res,
        $Val extends SubjectScheduleStatus>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  _$SubjectScheduleStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectScheduleStatusCopyWith<$Res>
    implements $SubjectScheduleStatusCopyWith<$Res> {
  factory _$$_SubjectScheduleStatusCopyWith(_$_SubjectScheduleStatus value,
          $Res Function(_$_SubjectScheduleStatus) then) =
      __$$_SubjectScheduleStatusCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? subjectType, String? scheduleStatus});
}

/// @nodoc
class __$$_SubjectScheduleStatusCopyWithImpl<$Res>
    extends _$SubjectScheduleStatusCopyWithImpl<$Res, _$_SubjectScheduleStatus>
    implements _$$_SubjectScheduleStatusCopyWith<$Res> {
  __$$_SubjectScheduleStatusCopyWithImpl(_$_SubjectScheduleStatus _value,
      $Res Function(_$_SubjectScheduleStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? scheduleStatus = freezed,
  }) {
    return _then(_$_SubjectScheduleStatus(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as String?,
      scheduleStatus: freezed == scheduleStatus
          ? _value.scheduleStatus
          : scheduleStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectScheduleStatus implements _SubjectScheduleStatus {
  const _$_SubjectScheduleStatus({this.subjectType, this.scheduleStatus});

  factory _$_SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectScheduleStatusFromJson(json);

  @override
  final String? subjectType;
  @override
  final String? scheduleStatus;

  @override
  String toString() {
    return 'SubjectScheduleStatus(subjectType: $subjectType, scheduleStatus: $scheduleStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectScheduleStatus &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.scheduleStatus, scheduleStatus) ||
                other.scheduleStatus == scheduleStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, scheduleStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      __$$_SubjectScheduleStatusCopyWithImpl<_$_SubjectScheduleStatus>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectScheduleStatusToJson(
      this,
    );
  }
}

abstract class _SubjectScheduleStatus implements SubjectScheduleStatus {
  const factory _SubjectScheduleStatus(
      {final String? subjectType,
      final String? scheduleStatus}) = _$_SubjectScheduleStatus;

  factory _SubjectScheduleStatus.fromJson(Map<String, dynamic> json) =
      _$_SubjectScheduleStatus.fromJson;

  @override
  String? get subjectType;
  @override
  String? get scheduleStatus;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectScheduleStatusCopyWith<_$_SubjectScheduleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}
