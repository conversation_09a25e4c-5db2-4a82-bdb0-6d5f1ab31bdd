// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_card_route_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonCardRouteData _$LessonCardRouteDataFromJson(Map<String, dynamic> json) {
  return _LessonCardRouteData.fromJson(json);
}

/// @nodoc
mixin _$LessonCardRouteData {
  String get app => throw _privateConstructorUsedError;
  String get miniprogram => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonCardRouteDataCopyWith<LessonCardRouteData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonCardRouteDataCopyWith<$Res> {
  factory $LessonCardRouteDataCopyWith(
          LessonCardRouteData value, $Res Function(LessonCardRouteData) then) =
      _$LessonCardRouteDataCopyWithImpl<$Res, LessonCardRouteData>;
  @useResult
  $Res call({String app, String miniprogram});
}

/// @nodoc
class _$LessonCardRouteDataCopyWithImpl<$Res, $Val extends LessonCardRouteData>
    implements $LessonCardRouteDataCopyWith<$Res> {
  _$LessonCardRouteDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? miniprogram = null,
  }) {
    return _then(_value.copyWith(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as String,
      miniprogram: null == miniprogram
          ? _value.miniprogram
          : miniprogram // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonCardRouteDataCopyWith<$Res>
    implements $LessonCardRouteDataCopyWith<$Res> {
  factory _$$_LessonCardRouteDataCopyWith(_$_LessonCardRouteData value,
          $Res Function(_$_LessonCardRouteData) then) =
      __$$_LessonCardRouteDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String app, String miniprogram});
}

/// @nodoc
class __$$_LessonCardRouteDataCopyWithImpl<$Res>
    extends _$LessonCardRouteDataCopyWithImpl<$Res, _$_LessonCardRouteData>
    implements _$$_LessonCardRouteDataCopyWith<$Res> {
  __$$_LessonCardRouteDataCopyWithImpl(_$_LessonCardRouteData _value,
      $Res Function(_$_LessonCardRouteData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? app = null,
    Object? miniprogram = null,
  }) {
    return _then(_$_LessonCardRouteData(
      app: null == app
          ? _value.app
          : app // ignore: cast_nullable_to_non_nullable
              as String,
      miniprogram: null == miniprogram
          ? _value.miniprogram
          : miniprogram // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonCardRouteData implements _LessonCardRouteData {
  const _$_LessonCardRouteData({required this.app, required this.miniprogram});

  factory _$_LessonCardRouteData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonCardRouteDataFromJson(json);

  @override
  final String app;
  @override
  final String miniprogram;

  @override
  String toString() {
    return 'LessonCardRouteData(app: $app, miniprogram: $miniprogram)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonCardRouteData &&
            (identical(other.app, app) || other.app == app) &&
            (identical(other.miniprogram, miniprogram) ||
                other.miniprogram == miniprogram));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, app, miniprogram);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonCardRouteDataCopyWith<_$_LessonCardRouteData> get copyWith =>
      __$$_LessonCardRouteDataCopyWithImpl<_$_LessonCardRouteData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonCardRouteDataToJson(
      this,
    );
  }
}

abstract class _LessonCardRouteData implements LessonCardRouteData {
  const factory _LessonCardRouteData(
      {required final String app,
      required final String miniprogram}) = _$_LessonCardRouteData;

  factory _LessonCardRouteData.fromJson(Map<String, dynamic> json) =
      _$_LessonCardRouteData.fromJson;

  @override
  String get app;
  @override
  String get miniprogram;
  @override
  @JsonKey(ignore: true)
  _$$_LessonCardRouteDataCopyWith<_$_LessonCardRouteData> get copyWith =>
      throw _privateConstructorUsedError;
}
