// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_card_local.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonCardData _$LessonCardDataFromJson(Map<String, dynamic> json) {
  return _LessonCardData.fromJson(json);
}

/// @nodoc
mixin _$LessonCardData {
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;

  ///卡片类型 TRAINING(1, "训练营"),
  /// EXPERIENCE(4, "体验课"),
  /// SINGLE(2, "单课"),
  /// YEAR(3, "年课");
  int? get courseType => throw _privateConstructorUsedError;

  ///卡片类型 TRAINING(1, "训练营"),
  /// EXPERIENCE(4, "体验课"),
  /// SINGLE(2, "单课"),
  /// YEAR(3, "年课");
  set courseType(int? value) => throw _privateConstructorUsedError;

  ///标题图片
  String? get icon => throw _privateConstructorUsedError;

  ///标题图片
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  set subTitle(String? value) => throw _privateConstructorUsedError;
  String? get bgImage => throw _privateConstructorUsedError;
  set bgImage(String? value) => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  set buttonText(String? value) => throw _privateConstructorUsedError;

  ///未激活原因
  String? get unactivatedReason => throw _privateConstructorUsedError;

  ///未激活原因
  set unactivatedReason(String? value) => throw _privateConstructorUsedError;

  ///跳转路由
  String? get jumpRouter => throw _privateConstructorUsedError;

  ///跳转路由
  set jumpRouter(String? value) => throw _privateConstructorUsedError;

  ///卡片背景颜色
  String? get bgColor => throw _privateConstructorUsedError;

  ///卡片背景颜色
  set bgColor(String? value) => throw _privateConstructorUsedError;

  ///新课图标
  String? get newClassIcon => throw _privateConstructorUsedError;

  ///新课图标
  set newClassIcon(String? value) => throw _privateConstructorUsedError;

  ///已完成，未完成 图标
  String? get statusIcon => throw _privateConstructorUsedError;

  ///已完成，未完成 图标
  set statusIcon(String? value) => throw _privateConstructorUsedError;
  int? get taskStatus => throw _privateConstructorUsedError;
  set taskStatus(int? value) => throw _privateConstructorUsedError;

  ///是否已经上报过埋点,避免多次上报
  bool? get reportPointed => throw _privateConstructorUsedError;

  ///是否已经上报过埋点,避免多次上报
  set reportPointed(bool? value) => throw _privateConstructorUsedError;

  ///新课
  bool? get newGetFlag => throw _privateConstructorUsedError;

  ///新课
  set newGetFlag(bool? value) => throw _privateConstructorUsedError;

  /// 课程类型
  int? get subjectType => throw _privateConstructorUsedError;

  /// 课程类型
  set subjectType(int? value) => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  set showDateTime(int? value) => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  set lessonId(int? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  set segmentId(int? value) => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  set weekId(int? value) => throw _privateConstructorUsedError;
  int? get unlockType => throw _privateConstructorUsedError;
  set unlockType(int? value) => throw _privateConstructorUsedError;

  ///视频信息 有则显示视频封面
  VideoInfo? get videoInfo => throw _privateConstructorUsedError;

  ///视频信息 有则显示视频封面
  set videoInfo(VideoInfo? value) => throw _privateConstructorUsedError;

  ///完课信息 有则显示完课 状态
  FinishInfo? get finishInfo => throw _privateConstructorUsedError;

  ///完课信息 有则显示完课 状态
  set finishInfo(FinishInfo? value) => throw _privateConstructorUsedError;

  ///新课信息 有则显示新课提醒
  NewGetInfo? get newGetInfo => throw _privateConstructorUsedError;

  ///新课信息 有则显示新课提醒
  set newGetInfo(NewGetInfo? value) => throw _privateConstructorUsedError;

  ///埋点数据
  SensorData? get sensorData => throw _privateConstructorUsedError;

  ///埋点数据
  set sensorData(SensorData? value) => throw _privateConstructorUsedError;

  ///自转化
  bool? get trialCourse => throw _privateConstructorUsedError;

  ///自转化
  set trialCourse(bool? value) => throw _privateConstructorUsedError;

  ///服务列表
  List<LessonServiceItem>? get lessonServiceList =>
      throw _privateConstructorUsedError;

  ///服务列表
  set lessonServiceList(List<LessonServiceItem>? value) =>
      throw _privateConstructorUsedError;

  /// 开课时间
  int? get startClassTime => throw _privateConstructorUsedError;

  /// 开课时间
  set startClassTime(int? value) => throw _privateConstructorUsedError;
  String? get vipMessage => throw _privateConstructorUsedError;
  set vipMessage(String? value) => throw _privateConstructorUsedError;
  String? get vipJumpLink => throw _privateConstructorUsedError;
  set vipJumpLink(String? value) => throw _privateConstructorUsedError;

  /// 客服入口
  String? get clientJumpUrl => throw _privateConstructorUsedError;

  /// 客服入口
  set clientJumpUrl(String? value) => throw _privateConstructorUsedError;
  String? get clientCoverUrl => throw _privateConstructorUsedError;
  set clientCoverUrl(String? value) => throw _privateConstructorUsedError;
  int? get activateStatus => throw _privateConstructorUsedError;
  set activateStatus(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonCardDataCopyWith<LessonCardData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonCardDataCopyWith<$Res> {
  factory $LessonCardDataCopyWith(
          LessonCardData value, $Res Function(LessonCardData) then) =
      _$LessonCardDataCopyWithImpl<$Res, LessonCardData>;
  @useResult
  $Res call(
      {String? courseKey,
      int? courseType,
      String? icon,
      String? title,
      String? subTitle,
      String? bgImage,
      String? buttonText,
      String? unactivatedReason,
      String? jumpRouter,
      String? bgColor,
      String? newClassIcon,
      String? statusIcon,
      int? taskStatus,
      bool? reportPointed,
      bool? newGetFlag,
      int? subjectType,
      int? showDateTime,
      int? lessonId,
      int? classId,
      int? segmentId,
      int? weekId,
      int? unlockType,
      VideoInfo? videoInfo,
      FinishInfo? finishInfo,
      NewGetInfo? newGetInfo,
      SensorData? sensorData,
      bool? trialCourse,
      List<LessonServiceItem>? lessonServiceList,
      int? startClassTime,
      String? vipMessage,
      String? vipJumpLink,
      String? clientJumpUrl,
      String? clientCoverUrl,
      int? activateStatus});

  $VideoInfoCopyWith<$Res>? get videoInfo;
  $FinishInfoCopyWith<$Res>? get finishInfo;
  $NewGetInfoCopyWith<$Res>? get newGetInfo;
  $SensorDataCopyWith<$Res>? get sensorData;
}

/// @nodoc
class _$LessonCardDataCopyWithImpl<$Res, $Val extends LessonCardData>
    implements $LessonCardDataCopyWith<$Res> {
  _$LessonCardDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseKey = freezed,
    Object? courseType = freezed,
    Object? icon = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? bgImage = freezed,
    Object? buttonText = freezed,
    Object? unactivatedReason = freezed,
    Object? jumpRouter = freezed,
    Object? bgColor = freezed,
    Object? newClassIcon = freezed,
    Object? statusIcon = freezed,
    Object? taskStatus = freezed,
    Object? reportPointed = freezed,
    Object? newGetFlag = freezed,
    Object? subjectType = freezed,
    Object? showDateTime = freezed,
    Object? lessonId = freezed,
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? videoInfo = freezed,
    Object? finishInfo = freezed,
    Object? newGetInfo = freezed,
    Object? sensorData = freezed,
    Object? trialCourse = freezed,
    Object? lessonServiceList = freezed,
    Object? startClassTime = freezed,
    Object? vipMessage = freezed,
    Object? vipJumpLink = freezed,
    Object? clientJumpUrl = freezed,
    Object? clientCoverUrl = freezed,
    Object? activateStatus = freezed,
  }) {
    return _then(_value.copyWith(
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRouter: freezed == jumpRouter
          ? _value.jumpRouter
          : jumpRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      newClassIcon: freezed == newClassIcon
          ? _value.newClassIcon
          : newClassIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      statusIcon: freezed == statusIcon
          ? _value.statusIcon
          : statusIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      reportPointed: freezed == reportPointed
          ? _value.reportPointed
          : reportPointed // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      videoInfo: freezed == videoInfo
          ? _value.videoInfo
          : videoInfo // ignore: cast_nullable_to_non_nullable
              as VideoInfo?,
      finishInfo: freezed == finishInfo
          ? _value.finishInfo
          : finishInfo // ignore: cast_nullable_to_non_nullable
              as FinishInfo?,
      newGetInfo: freezed == newGetInfo
          ? _value.newGetInfo
          : newGetInfo // ignore: cast_nullable_to_non_nullable
              as NewGetInfo?,
      sensorData: freezed == sensorData
          ? _value.sensorData
          : sensorData // ignore: cast_nullable_to_non_nullable
              as SensorData?,
      trialCourse: freezed == trialCourse
          ? _value.trialCourse
          : trialCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonServiceList: freezed == lessonServiceList
          ? _value.lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceItem>?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      vipMessage: freezed == vipMessage
          ? _value.vipMessage
          : vipMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      vipJumpLink: freezed == vipJumpLink
          ? _value.vipJumpLink
          : vipJumpLink // ignore: cast_nullable_to_non_nullable
              as String?,
      clientJumpUrl: freezed == clientJumpUrl
          ? _value.clientJumpUrl
          : clientJumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      clientCoverUrl: freezed == clientCoverUrl
          ? _value.clientCoverUrl
          : clientCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VideoInfoCopyWith<$Res>? get videoInfo {
    if (_value.videoInfo == null) {
      return null;
    }

    return $VideoInfoCopyWith<$Res>(_value.videoInfo!, (value) {
      return _then(_value.copyWith(videoInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $FinishInfoCopyWith<$Res>? get finishInfo {
    if (_value.finishInfo == null) {
      return null;
    }

    return $FinishInfoCopyWith<$Res>(_value.finishInfo!, (value) {
      return _then(_value.copyWith(finishInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $NewGetInfoCopyWith<$Res>? get newGetInfo {
    if (_value.newGetInfo == null) {
      return null;
    }

    return $NewGetInfoCopyWith<$Res>(_value.newGetInfo!, (value) {
      return _then(_value.copyWith(newGetInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SensorDataCopyWith<$Res>? get sensorData {
    if (_value.sensorData == null) {
      return null;
    }

    return $SensorDataCopyWith<$Res>(_value.sensorData!, (value) {
      return _then(_value.copyWith(sensorData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LessonCardDataCopyWith<$Res>
    implements $LessonCardDataCopyWith<$Res> {
  factory _$$_LessonCardDataCopyWith(
          _$_LessonCardData value, $Res Function(_$_LessonCardData) then) =
      __$$_LessonCardDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseKey,
      int? courseType,
      String? icon,
      String? title,
      String? subTitle,
      String? bgImage,
      String? buttonText,
      String? unactivatedReason,
      String? jumpRouter,
      String? bgColor,
      String? newClassIcon,
      String? statusIcon,
      int? taskStatus,
      bool? reportPointed,
      bool? newGetFlag,
      int? subjectType,
      int? showDateTime,
      int? lessonId,
      int? classId,
      int? segmentId,
      int? weekId,
      int? unlockType,
      VideoInfo? videoInfo,
      FinishInfo? finishInfo,
      NewGetInfo? newGetInfo,
      SensorData? sensorData,
      bool? trialCourse,
      List<LessonServiceItem>? lessonServiceList,
      int? startClassTime,
      String? vipMessage,
      String? vipJumpLink,
      String? clientJumpUrl,
      String? clientCoverUrl,
      int? activateStatus});

  @override
  $VideoInfoCopyWith<$Res>? get videoInfo;
  @override
  $FinishInfoCopyWith<$Res>? get finishInfo;
  @override
  $NewGetInfoCopyWith<$Res>? get newGetInfo;
  @override
  $SensorDataCopyWith<$Res>? get sensorData;
}

/// @nodoc
class __$$_LessonCardDataCopyWithImpl<$Res>
    extends _$LessonCardDataCopyWithImpl<$Res, _$_LessonCardData>
    implements _$$_LessonCardDataCopyWith<$Res> {
  __$$_LessonCardDataCopyWithImpl(
      _$_LessonCardData _value, $Res Function(_$_LessonCardData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseKey = freezed,
    Object? courseType = freezed,
    Object? icon = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? bgImage = freezed,
    Object? buttonText = freezed,
    Object? unactivatedReason = freezed,
    Object? jumpRouter = freezed,
    Object? bgColor = freezed,
    Object? newClassIcon = freezed,
    Object? statusIcon = freezed,
    Object? taskStatus = freezed,
    Object? reportPointed = freezed,
    Object? newGetFlag = freezed,
    Object? subjectType = freezed,
    Object? showDateTime = freezed,
    Object? lessonId = freezed,
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? videoInfo = freezed,
    Object? finishInfo = freezed,
    Object? newGetInfo = freezed,
    Object? sensorData = freezed,
    Object? trialCourse = freezed,
    Object? lessonServiceList = freezed,
    Object? startClassTime = freezed,
    Object? vipMessage = freezed,
    Object? vipJumpLink = freezed,
    Object? clientJumpUrl = freezed,
    Object? clientCoverUrl = freezed,
    Object? activateStatus = freezed,
  }) {
    return _then(_$_LessonCardData(
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImage: freezed == bgImage
          ? _value.bgImage
          : bgImage // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRouter: freezed == jumpRouter
          ? _value.jumpRouter
          : jumpRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      newClassIcon: freezed == newClassIcon
          ? _value.newClassIcon
          : newClassIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      statusIcon: freezed == statusIcon
          ? _value.statusIcon
          : statusIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      reportPointed: freezed == reportPointed
          ? _value.reportPointed
          : reportPointed // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      videoInfo: freezed == videoInfo
          ? _value.videoInfo
          : videoInfo // ignore: cast_nullable_to_non_nullable
              as VideoInfo?,
      finishInfo: freezed == finishInfo
          ? _value.finishInfo
          : finishInfo // ignore: cast_nullable_to_non_nullable
              as FinishInfo?,
      newGetInfo: freezed == newGetInfo
          ? _value.newGetInfo
          : newGetInfo // ignore: cast_nullable_to_non_nullable
              as NewGetInfo?,
      sensorData: freezed == sensorData
          ? _value.sensorData
          : sensorData // ignore: cast_nullable_to_non_nullable
              as SensorData?,
      trialCourse: freezed == trialCourse
          ? _value.trialCourse
          : trialCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonServiceList: freezed == lessonServiceList
          ? _value.lessonServiceList
          : lessonServiceList // ignore: cast_nullable_to_non_nullable
              as List<LessonServiceItem>?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      vipMessage: freezed == vipMessage
          ? _value.vipMessage
          : vipMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      vipJumpLink: freezed == vipJumpLink
          ? _value.vipJumpLink
          : vipJumpLink // ignore: cast_nullable_to_non_nullable
              as String?,
      clientJumpUrl: freezed == clientJumpUrl
          ? _value.clientJumpUrl
          : clientJumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      clientCoverUrl: freezed == clientCoverUrl
          ? _value.clientCoverUrl
          : clientCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonCardData implements _LessonCardData {
  _$_LessonCardData(
      {this.courseKey,
      this.courseType,
      this.icon,
      this.title,
      this.subTitle,
      this.bgImage,
      this.buttonText,
      this.unactivatedReason,
      this.jumpRouter,
      this.bgColor,
      this.newClassIcon,
      this.statusIcon,
      this.taskStatus,
      this.reportPointed,
      this.newGetFlag,
      this.subjectType,
      this.showDateTime,
      this.lessonId,
      this.classId,
      this.segmentId,
      this.weekId,
      this.unlockType,
      this.videoInfo,
      this.finishInfo,
      this.newGetInfo,
      this.sensorData,
      this.trialCourse,
      this.lessonServiceList,
      this.startClassTime,
      this.vipMessage,
      this.vipJumpLink,
      this.clientJumpUrl,
      this.clientCoverUrl,
      this.activateStatus});

  factory _$_LessonCardData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonCardDataFromJson(json);

  @override
  String? courseKey;

  ///卡片类型 TRAINING(1, "训练营"),
  /// EXPERIENCE(4, "体验课"),
  /// SINGLE(2, "单课"),
  /// YEAR(3, "年课");
  @override
  int? courseType;

  ///标题图片
  @override
  String? icon;
  @override
  String? title;
  @override
  String? subTitle;
  @override
  String? bgImage;
  @override
  String? buttonText;

  ///未激活原因
  @override
  String? unactivatedReason;

  ///跳转路由
  @override
  String? jumpRouter;

  ///卡片背景颜色
  @override
  String? bgColor;

  ///新课图标
  @override
  String? newClassIcon;

  ///已完成，未完成 图标
  @override
  String? statusIcon;
  @override
  int? taskStatus;

  ///是否已经上报过埋点,避免多次上报
  @override
  bool? reportPointed;

  ///新课
  @override
  bool? newGetFlag;

  /// 课程类型
  @override
  int? subjectType;
  @override
  int? showDateTime;
  @override
  int? lessonId;
  @override
  int? classId;
  @override
  int? segmentId;
  @override
  int? weekId;
  @override
  int? unlockType;

  ///视频信息 有则显示视频封面
  @override
  VideoInfo? videoInfo;

  ///完课信息 有则显示完课 状态
  @override
  FinishInfo? finishInfo;

  ///新课信息 有则显示新课提醒
  @override
  NewGetInfo? newGetInfo;

  ///埋点数据
  @override
  SensorData? sensorData;

  ///自转化
  @override
  bool? trialCourse;

  ///服务列表
  @override
  List<LessonServiceItem>? lessonServiceList;

  /// 开课时间
  @override
  int? startClassTime;
  @override
  String? vipMessage;
  @override
  String? vipJumpLink;

  /// 客服入口
  @override
  String? clientJumpUrl;
  @override
  String? clientCoverUrl;
  @override
  int? activateStatus;

  @override
  String toString() {
    return 'LessonCardData(courseKey: $courseKey, courseType: $courseType, icon: $icon, title: $title, subTitle: $subTitle, bgImage: $bgImage, buttonText: $buttonText, unactivatedReason: $unactivatedReason, jumpRouter: $jumpRouter, bgColor: $bgColor, newClassIcon: $newClassIcon, statusIcon: $statusIcon, taskStatus: $taskStatus, reportPointed: $reportPointed, newGetFlag: $newGetFlag, subjectType: $subjectType, showDateTime: $showDateTime, lessonId: $lessonId, classId: $classId, segmentId: $segmentId, weekId: $weekId, unlockType: $unlockType, videoInfo: $videoInfo, finishInfo: $finishInfo, newGetInfo: $newGetInfo, sensorData: $sensorData, trialCourse: $trialCourse, lessonServiceList: $lessonServiceList, startClassTime: $startClassTime, vipMessage: $vipMessage, vipJumpLink: $vipJumpLink, clientJumpUrl: $clientJumpUrl, clientCoverUrl: $clientCoverUrl, activateStatus: $activateStatus)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonCardDataCopyWith<_$_LessonCardData> get copyWith =>
      __$$_LessonCardDataCopyWithImpl<_$_LessonCardData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonCardDataToJson(
      this,
    );
  }
}

abstract class _LessonCardData implements LessonCardData {
  factory _LessonCardData(
      {String? courseKey,
      int? courseType,
      String? icon,
      String? title,
      String? subTitle,
      String? bgImage,
      String? buttonText,
      String? unactivatedReason,
      String? jumpRouter,
      String? bgColor,
      String? newClassIcon,
      String? statusIcon,
      int? taskStatus,
      bool? reportPointed,
      bool? newGetFlag,
      int? subjectType,
      int? showDateTime,
      int? lessonId,
      int? classId,
      int? segmentId,
      int? weekId,
      int? unlockType,
      VideoInfo? videoInfo,
      FinishInfo? finishInfo,
      NewGetInfo? newGetInfo,
      SensorData? sensorData,
      bool? trialCourse,
      List<LessonServiceItem>? lessonServiceList,
      int? startClassTime,
      String? vipMessage,
      String? vipJumpLink,
      String? clientJumpUrl,
      String? clientCoverUrl,
      int? activateStatus}) = _$_LessonCardData;

  factory _LessonCardData.fromJson(Map<String, dynamic> json) =
      _$_LessonCardData.fromJson;

  @override
  String? get courseKey;
  set courseKey(String? value);
  @override

  ///卡片类型 TRAINING(1, "训练营"),
  /// EXPERIENCE(4, "体验课"),
  /// SINGLE(2, "单课"),
  /// YEAR(3, "年课");
  int? get courseType;

  ///卡片类型 TRAINING(1, "训练营"),
  /// EXPERIENCE(4, "体验课"),
  /// SINGLE(2, "单课"),
  /// YEAR(3, "年课");
  set courseType(int? value);
  @override

  ///标题图片
  String? get icon;

  ///标题图片
  set icon(String? value);
  @override
  String? get title;
  set title(String? value);
  @override
  String? get subTitle;
  set subTitle(String? value);
  @override
  String? get bgImage;
  set bgImage(String? value);
  @override
  String? get buttonText;
  set buttonText(String? value);
  @override

  ///未激活原因
  String? get unactivatedReason;

  ///未激活原因
  set unactivatedReason(String? value);
  @override

  ///跳转路由
  String? get jumpRouter;

  ///跳转路由
  set jumpRouter(String? value);
  @override

  ///卡片背景颜色
  String? get bgColor;

  ///卡片背景颜色
  set bgColor(String? value);
  @override

  ///新课图标
  String? get newClassIcon;

  ///新课图标
  set newClassIcon(String? value);
  @override

  ///已完成，未完成 图标
  String? get statusIcon;

  ///已完成，未完成 图标
  set statusIcon(String? value);
  @override
  int? get taskStatus;
  set taskStatus(int? value);
  @override

  ///是否已经上报过埋点,避免多次上报
  bool? get reportPointed;

  ///是否已经上报过埋点,避免多次上报
  set reportPointed(bool? value);
  @override

  ///新课
  bool? get newGetFlag;

  ///新课
  set newGetFlag(bool? value);
  @override

  /// 课程类型
  int? get subjectType;

  /// 课程类型
  set subjectType(int? value);
  @override
  int? get showDateTime;
  set showDateTime(int? value);
  @override
  int? get lessonId;
  set lessonId(int? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  int? get segmentId;
  set segmentId(int? value);
  @override
  int? get weekId;
  set weekId(int? value);
  @override
  int? get unlockType;
  set unlockType(int? value);
  @override

  ///视频信息 有则显示视频封面
  VideoInfo? get videoInfo;

  ///视频信息 有则显示视频封面
  set videoInfo(VideoInfo? value);
  @override

  ///完课信息 有则显示完课 状态
  FinishInfo? get finishInfo;

  ///完课信息 有则显示完课 状态
  set finishInfo(FinishInfo? value);
  @override

  ///新课信息 有则显示新课提醒
  NewGetInfo? get newGetInfo;

  ///新课信息 有则显示新课提醒
  set newGetInfo(NewGetInfo? value);
  @override

  ///埋点数据
  SensorData? get sensorData;

  ///埋点数据
  set sensorData(SensorData? value);
  @override

  ///自转化
  bool? get trialCourse;

  ///自转化
  set trialCourse(bool? value);
  @override

  ///服务列表
  List<LessonServiceItem>? get lessonServiceList;

  ///服务列表
  set lessonServiceList(List<LessonServiceItem>? value);
  @override

  /// 开课时间
  int? get startClassTime;

  /// 开课时间
  set startClassTime(int? value);
  @override
  String? get vipMessage;
  set vipMessage(String? value);
  @override
  String? get vipJumpLink;
  set vipJumpLink(String? value);
  @override

  /// 客服入口
  String? get clientJumpUrl;

  /// 客服入口
  set clientJumpUrl(String? value);
  @override
  String? get clientCoverUrl;
  set clientCoverUrl(String? value);
  @override
  int? get activateStatus;
  set activateStatus(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_LessonCardDataCopyWith<_$_LessonCardData> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonServiceItem _$LessonServiceItemFromJson(Map<String, dynamic> json) {
  return _LessonServiceItem.fromJson(json);
}

/// @nodoc
mixin _$LessonServiceItem {
  ///服务唯一标记 用于类型识别
  String? get key => throw _privateConstructorUsedError;

  ///服务唯一标记 用于类型识别
  set key(String? value) => throw _privateConstructorUsedError;

  ///图标
  String? get icon => throw _privateConstructorUsedError;

  ///图标
  set icon(String? value) => throw _privateConstructorUsedError;

  ///名称
  String? get name => throw _privateConstructorUsedError;

  ///名称
  set name(String? value) => throw _privateConstructorUsedError;

  ///描述
  String? get desc => throw _privateConstructorUsedError;

  ///描述
  set desc(String? value) => throw _privateConstructorUsedError; //按钮
  String? get button => throw _privateConstructorUsedError; //按钮
  set button(String? value) => throw _privateConstructorUsedError;

  ///红点
  bool? get redPoint => throw _privateConstructorUsedError;

  ///红点
  set redPoint(bool? value) => throw _privateConstructorUsedError;

  ///路由
  String? get route => throw _privateConstructorUsedError;

  ///路由
  set route(String? value) => throw _privateConstructorUsedError;

  ///用于找到对应的赠课信息列表
  String? get userCourseId => throw _privateConstructorUsedError;

  ///用于找到对应的赠课信息列表
  set userCourseId(String? value) => throw _privateConstructorUsedError;

  ///家长验证
  int? get parentVerify => throw _privateConstructorUsedError;

  ///家长验证
  set parentVerify(int? value) => throw _privateConstructorUsedError;

  ///任务扩展信息
  CourseHomeTaskExtensionData? get serviceExtra =>
      throw _privateConstructorUsedError;

  ///任务扩展信息
  set serviceExtra(CourseHomeTaskExtensionData? value) =>
      throw _privateConstructorUsedError;

  ///toast 提示,点击时 若哟，则显示toast 不进行跳转
  String? get toast => throw _privateConstructorUsedError;

  ///toast 提示,点击时 若哟，则显示toast 不进行跳转
  set toast(String? value) => throw _privateConstructorUsedError;
  PopInfo? get popupInfo => throw _privateConstructorUsedError;
  set popupInfo(PopInfo? value) => throw _privateConstructorUsedError;
  List<UserGifCourse?>? get userGifCourseList =>
      throw _privateConstructorUsedError;
  set userGifCourseList(List<UserGifCourse?>? value) =>
      throw _privateConstructorUsedError;
  List<LessonCardData?>? get userGifLessonCardListData =>
      throw _privateConstructorUsedError;
  set userGifLessonCardListData(List<LessonCardData?>? value) =>
      throw _privateConstructorUsedError;

  ///埋点数据
  ServiceItemSensorData? get sensorData => throw _privateConstructorUsedError;

  ///埋点数据
  set sensorData(ServiceItemSensorData? value) =>
      throw _privateConstructorUsedError;
  String? get gifIcon => throw _privateConstructorUsedError;
  set gifIcon(String? value) => throw _privateConstructorUsedError;
  int? get playTime => throw _privateConstructorUsedError;
  set playTime(int? value) => throw _privateConstructorUsedError;
  String? get gifIconName => throw _privateConstructorUsedError;
  set gifIconName(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonServiceItemCopyWith<LessonServiceItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonServiceItemCopyWith<$Res> {
  factory $LessonServiceItemCopyWith(
          LessonServiceItem value, $Res Function(LessonServiceItem) then) =
      _$LessonServiceItemCopyWithImpl<$Res, LessonServiceItem>;
  @useResult
  $Res call(
      {String? key,
      String? icon,
      String? name,
      String? desc,
      String? button,
      bool? redPoint,
      String? route,
      String? userCourseId,
      int? parentVerify,
      CourseHomeTaskExtensionData? serviceExtra,
      String? toast,
      PopInfo? popupInfo,
      List<UserGifCourse?>? userGifCourseList,
      List<LessonCardData?>? userGifLessonCardListData,
      ServiceItemSensorData? sensorData,
      String? gifIcon,
      int? playTime,
      String? gifIconName});

  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra;
  $PopInfoCopyWith<$Res>? get popupInfo;
  $ServiceItemSensorDataCopyWith<$Res>? get sensorData;
}

/// @nodoc
class _$LessonServiceItemCopyWithImpl<$Res, $Val extends LessonServiceItem>
    implements $LessonServiceItemCopyWith<$Res> {
  _$LessonServiceItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? button = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
    Object? userCourseId = freezed,
    Object? parentVerify = freezed,
    Object? serviceExtra = freezed,
    Object? toast = freezed,
    Object? popupInfo = freezed,
    Object? userGifCourseList = freezed,
    Object? userGifLessonCardListData = freezed,
    Object? sensorData = freezed,
    Object? gifIcon = freezed,
    Object? playTime = freezed,
    Object? gifIconName = freezed,
  }) {
    return _then(_value.copyWith(
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceExtra: freezed == serviceExtra
          ? _value.serviceExtra
          : serviceExtra // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopInfo?,
      userGifCourseList: freezed == userGifCourseList
          ? _value.userGifCourseList
          : userGifCourseList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      userGifLessonCardListData: freezed == userGifLessonCardListData
          ? _value.userGifLessonCardListData
          : userGifLessonCardListData // ignore: cast_nullable_to_non_nullable
              as List<LessonCardData?>?,
      sensorData: freezed == sensorData
          ? _value.sensorData
          : sensorData // ignore: cast_nullable_to_non_nullable
              as ServiceItemSensorData?,
      gifIcon: freezed == gifIcon
          ? _value.gifIcon
          : gifIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      playTime: freezed == playTime
          ? _value.playTime
          : playTime // ignore: cast_nullable_to_non_nullable
              as int?,
      gifIconName: freezed == gifIconName
          ? _value.gifIconName
          : gifIconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra {
    if (_value.serviceExtra == null) {
      return null;
    }

    return $CourseHomeTaskExtensionDataCopyWith<$Res>(_value.serviceExtra!,
        (value) {
      return _then(_value.copyWith(serviceExtra: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PopInfoCopyWith<$Res>? get popupInfo {
    if (_value.popupInfo == null) {
      return null;
    }

    return $PopInfoCopyWith<$Res>(_value.popupInfo!, (value) {
      return _then(_value.copyWith(popupInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ServiceItemSensorDataCopyWith<$Res>? get sensorData {
    if (_value.sensorData == null) {
      return null;
    }

    return $ServiceItemSensorDataCopyWith<$Res>(_value.sensorData!, (value) {
      return _then(_value.copyWith(sensorData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LessonServiceItemCopyWith<$Res>
    implements $LessonServiceItemCopyWith<$Res> {
  factory _$$_LessonServiceItemCopyWith(_$_LessonServiceItem value,
          $Res Function(_$_LessonServiceItem) then) =
      __$$_LessonServiceItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? key,
      String? icon,
      String? name,
      String? desc,
      String? button,
      bool? redPoint,
      String? route,
      String? userCourseId,
      int? parentVerify,
      CourseHomeTaskExtensionData? serviceExtra,
      String? toast,
      PopInfo? popupInfo,
      List<UserGifCourse?>? userGifCourseList,
      List<LessonCardData?>? userGifLessonCardListData,
      ServiceItemSensorData? sensorData,
      String? gifIcon,
      int? playTime,
      String? gifIconName});

  @override
  $CourseHomeTaskExtensionDataCopyWith<$Res>? get serviceExtra;
  @override
  $PopInfoCopyWith<$Res>? get popupInfo;
  @override
  $ServiceItemSensorDataCopyWith<$Res>? get sensorData;
}

/// @nodoc
class __$$_LessonServiceItemCopyWithImpl<$Res>
    extends _$LessonServiceItemCopyWithImpl<$Res, _$_LessonServiceItem>
    implements _$$_LessonServiceItemCopyWith<$Res> {
  __$$_LessonServiceItemCopyWithImpl(
      _$_LessonServiceItem _value, $Res Function(_$_LessonServiceItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? button = freezed,
    Object? redPoint = freezed,
    Object? route = freezed,
    Object? userCourseId = freezed,
    Object? parentVerify = freezed,
    Object? serviceExtra = freezed,
    Object? toast = freezed,
    Object? popupInfo = freezed,
    Object? userGifCourseList = freezed,
    Object? userGifLessonCardListData = freezed,
    Object? sensorData = freezed,
    Object? gifIcon = freezed,
    Object? playTime = freezed,
    Object? gifIconName = freezed,
  }) {
    return _then(_$_LessonServiceItem(
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      button: freezed == button
          ? _value.button
          : button // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as bool?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseId: freezed == userCourseId
          ? _value.userCourseId
          : userCourseId // ignore: cast_nullable_to_non_nullable
              as String?,
      parentVerify: freezed == parentVerify
          ? _value.parentVerify
          : parentVerify // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceExtra: freezed == serviceExtra
          ? _value.serviceExtra
          : serviceExtra // ignore: cast_nullable_to_non_nullable
              as CourseHomeTaskExtensionData?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopInfo?,
      userGifCourseList: freezed == userGifCourseList
          ? _value.userGifCourseList
          : userGifCourseList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      userGifLessonCardListData: freezed == userGifLessonCardListData
          ? _value.userGifLessonCardListData
          : userGifLessonCardListData // ignore: cast_nullable_to_non_nullable
              as List<LessonCardData?>?,
      sensorData: freezed == sensorData
          ? _value.sensorData
          : sensorData // ignore: cast_nullable_to_non_nullable
              as ServiceItemSensorData?,
      gifIcon: freezed == gifIcon
          ? _value.gifIcon
          : gifIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      playTime: freezed == playTime
          ? _value.playTime
          : playTime // ignore: cast_nullable_to_non_nullable
              as int?,
      gifIconName: freezed == gifIconName
          ? _value.gifIconName
          : gifIconName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonServiceItem implements _LessonServiceItem {
  _$_LessonServiceItem(
      {this.key,
      this.icon,
      this.name,
      this.desc,
      this.button,
      this.redPoint,
      this.route,
      this.userCourseId,
      this.parentVerify,
      this.serviceExtra,
      this.toast,
      this.popupInfo,
      this.userGifCourseList,
      this.userGifLessonCardListData,
      this.sensorData,
      this.gifIcon,
      this.playTime,
      this.gifIconName});

  factory _$_LessonServiceItem.fromJson(Map<String, dynamic> json) =>
      _$$_LessonServiceItemFromJson(json);

  ///服务唯一标记 用于类型识别
  @override
  String? key;

  ///图标
  @override
  String? icon;

  ///名称
  @override
  String? name;

  ///描述
  @override
  String? desc;
//按钮
  @override
  String? button;

  ///红点
  @override
  bool? redPoint;

  ///路由
  @override
  String? route;

  ///用于找到对应的赠课信息列表
  @override
  String? userCourseId;

  ///家长验证
  @override
  int? parentVerify;

  ///任务扩展信息
  @override
  CourseHomeTaskExtensionData? serviceExtra;

  ///toast 提示,点击时 若哟，则显示toast 不进行跳转
  @override
  String? toast;
  @override
  PopInfo? popupInfo;
  @override
  List<UserGifCourse?>? userGifCourseList;
  @override
  List<LessonCardData?>? userGifLessonCardListData;

  ///埋点数据
  @override
  ServiceItemSensorData? sensorData;
  @override
  String? gifIcon;
  @override
  int? playTime;
  @override
  String? gifIconName;

  @override
  String toString() {
    return 'LessonServiceItem(key: $key, icon: $icon, name: $name, desc: $desc, button: $button, redPoint: $redPoint, route: $route, userCourseId: $userCourseId, parentVerify: $parentVerify, serviceExtra: $serviceExtra, toast: $toast, popupInfo: $popupInfo, userGifCourseList: $userGifCourseList, userGifLessonCardListData: $userGifLessonCardListData, sensorData: $sensorData, gifIcon: $gifIcon, playTime: $playTime, gifIconName: $gifIconName)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonServiceItemCopyWith<_$_LessonServiceItem> get copyWith =>
      __$$_LessonServiceItemCopyWithImpl<_$_LessonServiceItem>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonServiceItemToJson(
      this,
    );
  }
}

abstract class _LessonServiceItem implements LessonServiceItem {
  factory _LessonServiceItem(
      {String? key,
      String? icon,
      String? name,
      String? desc,
      String? button,
      bool? redPoint,
      String? route,
      String? userCourseId,
      int? parentVerify,
      CourseHomeTaskExtensionData? serviceExtra,
      String? toast,
      PopInfo? popupInfo,
      List<UserGifCourse?>? userGifCourseList,
      List<LessonCardData?>? userGifLessonCardListData,
      ServiceItemSensorData? sensorData,
      String? gifIcon,
      int? playTime,
      String? gifIconName}) = _$_LessonServiceItem;

  factory _LessonServiceItem.fromJson(Map<String, dynamic> json) =
      _$_LessonServiceItem.fromJson;

  @override

  ///服务唯一标记 用于类型识别
  String? get key;

  ///服务唯一标记 用于类型识别
  set key(String? value);
  @override

  ///图标
  String? get icon;

  ///图标
  set icon(String? value);
  @override

  ///名称
  String? get name;

  ///名称
  set name(String? value);
  @override

  ///描述
  String? get desc;

  ///描述
  set desc(String? value);
  @override //按钮
  String? get button; //按钮
  set button(String? value);
  @override

  ///红点
  bool? get redPoint;

  ///红点
  set redPoint(bool? value);
  @override

  ///路由
  String? get route;

  ///路由
  set route(String? value);
  @override

  ///用于找到对应的赠课信息列表
  String? get userCourseId;

  ///用于找到对应的赠课信息列表
  set userCourseId(String? value);
  @override

  ///家长验证
  int? get parentVerify;

  ///家长验证
  set parentVerify(int? value);
  @override

  ///任务扩展信息
  CourseHomeTaskExtensionData? get serviceExtra;

  ///任务扩展信息
  set serviceExtra(CourseHomeTaskExtensionData? value);
  @override

  ///toast 提示,点击时 若哟，则显示toast 不进行跳转
  String? get toast;

  ///toast 提示,点击时 若哟，则显示toast 不进行跳转
  set toast(String? value);
  @override
  PopInfo? get popupInfo;
  set popupInfo(PopInfo? value);
  @override
  List<UserGifCourse?>? get userGifCourseList;
  set userGifCourseList(List<UserGifCourse?>? value);
  @override
  List<LessonCardData?>? get userGifLessonCardListData;
  set userGifLessonCardListData(List<LessonCardData?>? value);
  @override

  ///埋点数据
  ServiceItemSensorData? get sensorData;

  ///埋点数据
  set sensorData(ServiceItemSensorData? value);
  @override
  String? get gifIcon;
  set gifIcon(String? value);
  @override
  int? get playTime;
  set playTime(int? value);
  @override
  String? get gifIconName;
  set gifIconName(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_LessonServiceItemCopyWith<_$_LessonServiceItem> get copyWith =>
      throw _privateConstructorUsedError;
}

SensorData _$SensorDataFromJson(Map<String, dynamic> json) {
  return _SensorData.fromJson(json);
}

/// @nodoc
mixin _$SensorData {
  String? get pageName => throw _privateConstructorUsedError; //页面名称
  String? get cardType => throw _privateConstructorUsedError; //卡片类型
  String? get elementType => throw _privateConstructorUsedError; //元素类型
  String? get courseType => throw _privateConstructorUsedError; //课程类型
  String? get courseStage => throw _privateConstructorUsedError; //课程阶段
  String? get customState => throw _privateConstructorUsedError; //业务自定义状态
  String? get businessType => throw _privateConstructorUsedError; //业务类型
  int? get classId => throw _privateConstructorUsedError; //班级id
  List<String>? get trackExtendInfoList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SensorDataCopyWith<SensorData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SensorDataCopyWith<$Res> {
  factory $SensorDataCopyWith(
          SensorData value, $Res Function(SensorData) then) =
      _$SensorDataCopyWithImpl<$Res, SensorData>;
  @useResult
  $Res call(
      {String? pageName,
      String? cardType,
      String? elementType,
      String? courseType,
      String? courseStage,
      String? customState,
      String? businessType,
      int? classId,
      List<String>? trackExtendInfoList});
}

/// @nodoc
class _$SensorDataCopyWithImpl<$Res, $Val extends SensorData>
    implements $SensorDataCopyWith<$Res> {
  _$SensorDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageName = freezed,
    Object? cardType = freezed,
    Object? elementType = freezed,
    Object? courseType = freezed,
    Object? courseStage = freezed,
    Object? customState = freezed,
    Object? businessType = freezed,
    Object? classId = freezed,
    Object? trackExtendInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      trackExtendInfoList: freezed == trackExtendInfoList
          ? _value.trackExtendInfoList
          : trackExtendInfoList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SensorDataCopyWith<$Res>
    implements $SensorDataCopyWith<$Res> {
  factory _$$_SensorDataCopyWith(
          _$_SensorData value, $Res Function(_$_SensorData) then) =
      __$$_SensorDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? pageName,
      String? cardType,
      String? elementType,
      String? courseType,
      String? courseStage,
      String? customState,
      String? businessType,
      int? classId,
      List<String>? trackExtendInfoList});
}

/// @nodoc
class __$$_SensorDataCopyWithImpl<$Res>
    extends _$SensorDataCopyWithImpl<$Res, _$_SensorData>
    implements _$$_SensorDataCopyWith<$Res> {
  __$$_SensorDataCopyWithImpl(
      _$_SensorData _value, $Res Function(_$_SensorData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageName = freezed,
    Object? cardType = freezed,
    Object? elementType = freezed,
    Object? courseType = freezed,
    Object? courseStage = freezed,
    Object? customState = freezed,
    Object? businessType = freezed,
    Object? classId = freezed,
    Object? trackExtendInfoList = freezed,
  }) {
    return _then(_$_SensorData(
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      trackExtendInfoList: freezed == trackExtendInfoList
          ? _value._trackExtendInfoList
          : trackExtendInfoList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SensorData implements _SensorData {
  const _$_SensorData(
      {this.pageName,
      this.cardType,
      this.elementType,
      this.courseType,
      this.courseStage,
      this.customState,
      this.businessType,
      this.classId,
      final List<String>? trackExtendInfoList})
      : _trackExtendInfoList = trackExtendInfoList;

  factory _$_SensorData.fromJson(Map<String, dynamic> json) =>
      _$$_SensorDataFromJson(json);

  @override
  final String? pageName;
//页面名称
  @override
  final String? cardType;
//卡片类型
  @override
  final String? elementType;
//元素类型
  @override
  final String? courseType;
//课程类型
  @override
  final String? courseStage;
//课程阶段
  @override
  final String? customState;
//业务自定义状态
  @override
  final String? businessType;
//业务类型
  @override
  final int? classId;
//班级id
  final List<String>? _trackExtendInfoList;
//班级id
  @override
  List<String>? get trackExtendInfoList {
    final value = _trackExtendInfoList;
    if (value == null) return null;
    if (_trackExtendInfoList is EqualUnmodifiableListView)
      return _trackExtendInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SensorData(pageName: $pageName, cardType: $cardType, elementType: $elementType, courseType: $courseType, courseStage: $courseStage, customState: $customState, businessType: $businessType, classId: $classId, trackExtendInfoList: $trackExtendInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SensorData &&
            (identical(other.pageName, pageName) ||
                other.pageName == pageName) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.elementType, elementType) ||
                other.elementType == elementType) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseStage, courseStage) ||
                other.courseStage == courseStage) &&
            (identical(other.customState, customState) ||
                other.customState == customState) &&
            (identical(other.businessType, businessType) ||
                other.businessType == businessType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            const DeepCollectionEquality()
                .equals(other._trackExtendInfoList, _trackExtendInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      pageName,
      cardType,
      elementType,
      courseType,
      courseStage,
      customState,
      businessType,
      classId,
      const DeepCollectionEquality().hash(_trackExtendInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SensorDataCopyWith<_$_SensorData> get copyWith =>
      __$$_SensorDataCopyWithImpl<_$_SensorData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SensorDataToJson(
      this,
    );
  }
}

abstract class _SensorData implements SensorData {
  const factory _SensorData(
      {final String? pageName,
      final String? cardType,
      final String? elementType,
      final String? courseType,
      final String? courseStage,
      final String? customState,
      final String? businessType,
      final int? classId,
      final List<String>? trackExtendInfoList}) = _$_SensorData;

  factory _SensorData.fromJson(Map<String, dynamic> json) =
      _$_SensorData.fromJson;

  @override
  String? get pageName;
  @override //页面名称
  String? get cardType;
  @override //卡片类型
  String? get elementType;
  @override //元素类型
  String? get courseType;
  @override //课程类型
  String? get courseStage;
  @override //课程阶段
  String? get customState;
  @override //业务自定义状态
  String? get businessType;
  @override //业务类型
  int? get classId;
  @override //班级id
  List<String>? get trackExtendInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_SensorDataCopyWith<_$_SensorData> get copyWith =>
      throw _privateConstructorUsedError;
}

ServiceItemSensorData _$ServiceItemSensorDataFromJson(
    Map<String, dynamic> json) {
  return _ServiceItemSensorData.fromJson(json);
}

/// @nodoc
mixin _$ServiceItemSensorData {
  String? get pageName => throw _privateConstructorUsedError; //页面名称
  String? get serviceType => throw _privateConstructorUsedError; //服务卡片类型
  String? get courseType => throw _privateConstructorUsedError; //课程类型
  String? get elementType => throw _privateConstructorUsedError; //元素类型
  String? get courseStage => throw _privateConstructorUsedError; //课程阶段
  String? get customState => throw _privateConstructorUsedError; //业务自定义状态
  String? get businessType => throw _privateConstructorUsedError; //业务类型
  String? get materialId => throw _privateConstructorUsedError; //素材唯一标识
  String? get materialName => throw _privateConstructorUsedError; //素材名称
  int? get classId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ServiceItemSensorDataCopyWith<ServiceItemSensorData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServiceItemSensorDataCopyWith<$Res> {
  factory $ServiceItemSensorDataCopyWith(ServiceItemSensorData value,
          $Res Function(ServiceItemSensorData) then) =
      _$ServiceItemSensorDataCopyWithImpl<$Res, ServiceItemSensorData>;
  @useResult
  $Res call(
      {String? pageName,
      String? serviceType,
      String? courseType,
      String? elementType,
      String? courseStage,
      String? customState,
      String? businessType,
      String? materialId,
      String? materialName,
      int? classId});
}

/// @nodoc
class _$ServiceItemSensorDataCopyWithImpl<$Res,
        $Val extends ServiceItemSensorData>
    implements $ServiceItemSensorDataCopyWith<$Res> {
  _$ServiceItemSensorDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageName = freezed,
    Object? serviceType = freezed,
    Object? courseType = freezed,
    Object? elementType = freezed,
    Object? courseStage = freezed,
    Object? customState = freezed,
    Object? businessType = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceType: freezed == serviceType
          ? _value.serviceType
          : serviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ServiceItemSensorDataCopyWith<$Res>
    implements $ServiceItemSensorDataCopyWith<$Res> {
  factory _$$_ServiceItemSensorDataCopyWith(_$_ServiceItemSensorData value,
          $Res Function(_$_ServiceItemSensorData) then) =
      __$$_ServiceItemSensorDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? pageName,
      String? serviceType,
      String? courseType,
      String? elementType,
      String? courseStage,
      String? customState,
      String? businessType,
      String? materialId,
      String? materialName,
      int? classId});
}

/// @nodoc
class __$$_ServiceItemSensorDataCopyWithImpl<$Res>
    extends _$ServiceItemSensorDataCopyWithImpl<$Res, _$_ServiceItemSensorData>
    implements _$$_ServiceItemSensorDataCopyWith<$Res> {
  __$$_ServiceItemSensorDataCopyWithImpl(_$_ServiceItemSensorData _value,
      $Res Function(_$_ServiceItemSensorData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageName = freezed,
    Object? serviceType = freezed,
    Object? courseType = freezed,
    Object? elementType = freezed,
    Object? courseStage = freezed,
    Object? customState = freezed,
    Object? businessType = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? classId = freezed,
  }) {
    return _then(_$_ServiceItemSensorData(
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceType: freezed == serviceType
          ? _value.serviceType
          : serviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ServiceItemSensorData implements _ServiceItemSensorData {
  const _$_ServiceItemSensorData(
      {this.pageName,
      this.serviceType,
      this.courseType,
      this.elementType,
      this.courseStage,
      this.customState,
      this.businessType,
      this.materialId,
      this.materialName,
      this.classId});

  factory _$_ServiceItemSensorData.fromJson(Map<String, dynamic> json) =>
      _$$_ServiceItemSensorDataFromJson(json);

  @override
  final String? pageName;
//页面名称
  @override
  final String? serviceType;
//服务卡片类型
  @override
  final String? courseType;
//课程类型
  @override
  final String? elementType;
//元素类型
  @override
  final String? courseStage;
//课程阶段
  @override
  final String? customState;
//业务自定义状态
  @override
  final String? businessType;
//业务类型
  @override
  final String? materialId;
//素材唯一标识
  @override
  final String? materialName;
//素材名称
  @override
  final int? classId;

  @override
  String toString() {
    return 'ServiceItemSensorData(pageName: $pageName, serviceType: $serviceType, courseType: $courseType, elementType: $elementType, courseStage: $courseStage, customState: $customState, businessType: $businessType, materialId: $materialId, materialName: $materialName, classId: $classId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ServiceItemSensorData &&
            (identical(other.pageName, pageName) ||
                other.pageName == pageName) &&
            (identical(other.serviceType, serviceType) ||
                other.serviceType == serviceType) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.elementType, elementType) ||
                other.elementType == elementType) &&
            (identical(other.courseStage, courseStage) ||
                other.courseStage == courseStage) &&
            (identical(other.customState, customState) ||
                other.customState == customState) &&
            (identical(other.businessType, businessType) ||
                other.businessType == businessType) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.classId, classId) || other.classId == classId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      pageName,
      serviceType,
      courseType,
      elementType,
      courseStage,
      customState,
      businessType,
      materialId,
      materialName,
      classId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ServiceItemSensorDataCopyWith<_$_ServiceItemSensorData> get copyWith =>
      __$$_ServiceItemSensorDataCopyWithImpl<_$_ServiceItemSensorData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ServiceItemSensorDataToJson(
      this,
    );
  }
}

abstract class _ServiceItemSensorData implements ServiceItemSensorData {
  const factory _ServiceItemSensorData(
      {final String? pageName,
      final String? serviceType,
      final String? courseType,
      final String? elementType,
      final String? courseStage,
      final String? customState,
      final String? businessType,
      final String? materialId,
      final String? materialName,
      final int? classId}) = _$_ServiceItemSensorData;

  factory _ServiceItemSensorData.fromJson(Map<String, dynamic> json) =
      _$_ServiceItemSensorData.fromJson;

  @override
  String? get pageName;
  @override //页面名称
  String? get serviceType;
  @override //服务卡片类型
  String? get courseType;
  @override //课程类型
  String? get elementType;
  @override //元素类型
  String? get courseStage;
  @override //课程阶段
  String? get customState;
  @override //业务自定义状态
  String? get businessType;
  @override //业务类型
  String? get materialId;
  @override //素材唯一标识
  String? get materialName;
  @override //素材名称
  int? get classId;
  @override
  @JsonKey(ignore: true)
  _$$_ServiceItemSensorDataCopyWith<_$_ServiceItemSensorData> get copyWith =>
      throw _privateConstructorUsedError;
}

NewGetInfo _$NewGetInfoFromJson(Map<String, dynamic> json) {
  return _NewGetInfo.fromJson(json);
}

/// @nodoc
mixin _$NewGetInfo {
  bool? get newGetFlag => throw _privateConstructorUsedError;
  String? get newGetGuideVoice => throw _privateConstructorUsedError;
  String? get newGetGuideIcon => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NewGetInfoCopyWith<NewGetInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NewGetInfoCopyWith<$Res> {
  factory $NewGetInfoCopyWith(
          NewGetInfo value, $Res Function(NewGetInfo) then) =
      _$NewGetInfoCopyWithImpl<$Res, NewGetInfo>;
  @useResult
  $Res call(
      {bool? newGetFlag,
      String? newGetGuideVoice,
      String? newGetGuideIcon,
      String? content});
}

/// @nodoc
class _$NewGetInfoCopyWithImpl<$Res, $Val extends NewGetInfo>
    implements $NewGetInfoCopyWith<$Res> {
  _$NewGetInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newGetFlag = freezed,
    Object? newGetGuideVoice = freezed,
    Object? newGetGuideIcon = freezed,
    Object? content = freezed,
  }) {
    return _then(_value.copyWith(
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetGuideVoice: freezed == newGetGuideVoice
          ? _value.newGetGuideVoice
          : newGetGuideVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideIcon: freezed == newGetGuideIcon
          ? _value.newGetGuideIcon
          : newGetGuideIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_NewGetInfoCopyWith<$Res>
    implements $NewGetInfoCopyWith<$Res> {
  factory _$$_NewGetInfoCopyWith(
          _$_NewGetInfo value, $Res Function(_$_NewGetInfo) then) =
      __$$_NewGetInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? newGetFlag,
      String? newGetGuideVoice,
      String? newGetGuideIcon,
      String? content});
}

/// @nodoc
class __$$_NewGetInfoCopyWithImpl<$Res>
    extends _$NewGetInfoCopyWithImpl<$Res, _$_NewGetInfo>
    implements _$$_NewGetInfoCopyWith<$Res> {
  __$$_NewGetInfoCopyWithImpl(
      _$_NewGetInfo _value, $Res Function(_$_NewGetInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newGetFlag = freezed,
    Object? newGetGuideVoice = freezed,
    Object? newGetGuideIcon = freezed,
    Object? content = freezed,
  }) {
    return _then(_$_NewGetInfo(
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      newGetGuideVoice: freezed == newGetGuideVoice
          ? _value.newGetGuideVoice
          : newGetGuideVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetGuideIcon: freezed == newGetGuideIcon
          ? _value.newGetGuideIcon
          : newGetGuideIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_NewGetInfo implements _NewGetInfo {
  const _$_NewGetInfo(
      {this.newGetFlag,
      this.newGetGuideVoice,
      this.newGetGuideIcon,
      this.content});

  factory _$_NewGetInfo.fromJson(Map<String, dynamic> json) =>
      _$$_NewGetInfoFromJson(json);

  @override
  final bool? newGetFlag;
  @override
  final String? newGetGuideVoice;
  @override
  final String? newGetGuideIcon;
  @override
  final String? content;

  @override
  String toString() {
    return 'NewGetInfo(newGetFlag: $newGetFlag, newGetGuideVoice: $newGetGuideVoice, newGetGuideIcon: $newGetGuideIcon, content: $content)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_NewGetInfo &&
            (identical(other.newGetFlag, newGetFlag) ||
                other.newGetFlag == newGetFlag) &&
            (identical(other.newGetGuideVoice, newGetGuideVoice) ||
                other.newGetGuideVoice == newGetGuideVoice) &&
            (identical(other.newGetGuideIcon, newGetGuideIcon) ||
                other.newGetGuideIcon == newGetGuideIcon) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, newGetFlag, newGetGuideVoice, newGetGuideIcon, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_NewGetInfoCopyWith<_$_NewGetInfo> get copyWith =>
      __$$_NewGetInfoCopyWithImpl<_$_NewGetInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_NewGetInfoToJson(
      this,
    );
  }
}

abstract class _NewGetInfo implements NewGetInfo {
  const factory _NewGetInfo(
      {final bool? newGetFlag,
      final String? newGetGuideVoice,
      final String? newGetGuideIcon,
      final String? content}) = _$_NewGetInfo;

  factory _NewGetInfo.fromJson(Map<String, dynamic> json) =
      _$_NewGetInfo.fromJson;

  @override
  bool? get newGetFlag;
  @override
  String? get newGetGuideVoice;
  @override
  String? get newGetGuideIcon;
  @override
  String? get content;
  @override
  @JsonKey(ignore: true)
  _$$_NewGetInfoCopyWith<_$_NewGetInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

VideoInfo _$VideoInfoFromJson(Map<String, dynamic> json) {
  return _VideoInfo.fromJson(json);
}

/// @nodoc
mixin _$VideoInfo {
  bool? get showVideo => throw _privateConstructorUsedError;
  String? get videoUrl => throw _privateConstructorUsedError;
  String? get videoCover => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VideoInfoCopyWith<VideoInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VideoInfoCopyWith<$Res> {
  factory $VideoInfoCopyWith(VideoInfo value, $Res Function(VideoInfo) then) =
      _$VideoInfoCopyWithImpl<$Res, VideoInfo>;
  @useResult
  $Res call({bool? showVideo, String? videoUrl, String? videoCover});
}

/// @nodoc
class _$VideoInfoCopyWithImpl<$Res, $Val extends VideoInfo>
    implements $VideoInfoCopyWith<$Res> {
  _$VideoInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showVideo = freezed,
    Object? videoUrl = freezed,
    Object? videoCover = freezed,
  }) {
    return _then(_value.copyWith(
      showVideo: freezed == showVideo
          ? _value.showVideo
          : showVideo // ignore: cast_nullable_to_non_nullable
              as bool?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCover: freezed == videoCover
          ? _value.videoCover
          : videoCover // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_VideoInfoCopyWith<$Res> implements $VideoInfoCopyWith<$Res> {
  factory _$$_VideoInfoCopyWith(
          _$_VideoInfo value, $Res Function(_$_VideoInfo) then) =
      __$$_VideoInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? showVideo, String? videoUrl, String? videoCover});
}

/// @nodoc
class __$$_VideoInfoCopyWithImpl<$Res>
    extends _$VideoInfoCopyWithImpl<$Res, _$_VideoInfo>
    implements _$$_VideoInfoCopyWith<$Res> {
  __$$_VideoInfoCopyWithImpl(
      _$_VideoInfo _value, $Res Function(_$_VideoInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showVideo = freezed,
    Object? videoUrl = freezed,
    Object? videoCover = freezed,
  }) {
    return _then(_$_VideoInfo(
      showVideo: freezed == showVideo
          ? _value.showVideo
          : showVideo // ignore: cast_nullable_to_non_nullable
              as bool?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCover: freezed == videoCover
          ? _value.videoCover
          : videoCover // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_VideoInfo implements _VideoInfo {
  const _$_VideoInfo({this.showVideo, this.videoUrl, this.videoCover});

  factory _$_VideoInfo.fromJson(Map<String, dynamic> json) =>
      _$$_VideoInfoFromJson(json);

  @override
  final bool? showVideo;
  @override
  final String? videoUrl;
  @override
  final String? videoCover;

  @override
  String toString() {
    return 'VideoInfo(showVideo: $showVideo, videoUrl: $videoUrl, videoCover: $videoCover)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_VideoInfo &&
            (identical(other.showVideo, showVideo) ||
                other.showVideo == showVideo) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.videoCover, videoCover) ||
                other.videoCover == videoCover));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, showVideo, videoUrl, videoCover);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_VideoInfoCopyWith<_$_VideoInfo> get copyWith =>
      __$$_VideoInfoCopyWithImpl<_$_VideoInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_VideoInfoToJson(
      this,
    );
  }
}

abstract class _VideoInfo implements VideoInfo {
  const factory _VideoInfo(
      {final bool? showVideo,
      final String? videoUrl,
      final String? videoCover}) = _$_VideoInfo;

  factory _VideoInfo.fromJson(Map<String, dynamic> json) =
      _$_VideoInfo.fromJson;

  @override
  bool? get showVideo;
  @override
  String? get videoUrl;
  @override
  String? get videoCover;
  @override
  @JsonKey(ignore: true)
  _$$_VideoInfoCopyWith<_$_VideoInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

FinishInfo _$FinishInfoFromJson(Map<String, dynamic> json) {
  return _FinishInfo.fromJson(json);
}

/// @nodoc
mixin _$FinishInfo {
  bool? get finishFlag => throw _privateConstructorUsedError;
  String? get finishGuideIcon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FinishInfoCopyWith<FinishInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FinishInfoCopyWith<$Res> {
  factory $FinishInfoCopyWith(
          FinishInfo value, $Res Function(FinishInfo) then) =
      _$FinishInfoCopyWithImpl<$Res, FinishInfo>;
  @useResult
  $Res call({bool? finishFlag, String? finishGuideIcon});
}

/// @nodoc
class _$FinishInfoCopyWithImpl<$Res, $Val extends FinishInfo>
    implements $FinishInfoCopyWith<$Res> {
  _$FinishInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? finishFlag = freezed,
    Object? finishGuideIcon = freezed,
  }) {
    return _then(_value.copyWith(
      finishFlag: freezed == finishFlag
          ? _value.finishFlag
          : finishFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      finishGuideIcon: freezed == finishGuideIcon
          ? _value.finishGuideIcon
          : finishGuideIcon // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FinishInfoCopyWith<$Res>
    implements $FinishInfoCopyWith<$Res> {
  factory _$$_FinishInfoCopyWith(
          _$_FinishInfo value, $Res Function(_$_FinishInfo) then) =
      __$$_FinishInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? finishFlag, String? finishGuideIcon});
}

/// @nodoc
class __$$_FinishInfoCopyWithImpl<$Res>
    extends _$FinishInfoCopyWithImpl<$Res, _$_FinishInfo>
    implements _$$_FinishInfoCopyWith<$Res> {
  __$$_FinishInfoCopyWithImpl(
      _$_FinishInfo _value, $Res Function(_$_FinishInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? finishFlag = freezed,
    Object? finishGuideIcon = freezed,
  }) {
    return _then(_$_FinishInfo(
      finishFlag: freezed == finishFlag
          ? _value.finishFlag
          : finishFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      finishGuideIcon: freezed == finishGuideIcon
          ? _value.finishGuideIcon
          : finishGuideIcon // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FinishInfo implements _FinishInfo {
  const _$_FinishInfo({this.finishFlag, this.finishGuideIcon});

  factory _$_FinishInfo.fromJson(Map<String, dynamic> json) =>
      _$$_FinishInfoFromJson(json);

  @override
  final bool? finishFlag;
  @override
  final String? finishGuideIcon;

  @override
  String toString() {
    return 'FinishInfo(finishFlag: $finishFlag, finishGuideIcon: $finishGuideIcon)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FinishInfo &&
            (identical(other.finishFlag, finishFlag) ||
                other.finishFlag == finishFlag) &&
            (identical(other.finishGuideIcon, finishGuideIcon) ||
                other.finishGuideIcon == finishGuideIcon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, finishFlag, finishGuideIcon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FinishInfoCopyWith<_$_FinishInfo> get copyWith =>
      __$$_FinishInfoCopyWithImpl<_$_FinishInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FinishInfoToJson(
      this,
    );
  }
}

abstract class _FinishInfo implements FinishInfo {
  const factory _FinishInfo(
      {final bool? finishFlag, final String? finishGuideIcon}) = _$_FinishInfo;

  factory _FinishInfo.fromJson(Map<String, dynamic> json) =
      _$_FinishInfo.fromJson;

  @override
  bool? get finishFlag;
  @override
  String? get finishGuideIcon;
  @override
  @JsonKey(ignore: true)
  _$$_FinishInfoCopyWith<_$_FinishInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
