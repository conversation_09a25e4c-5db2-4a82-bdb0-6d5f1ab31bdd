// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enjoy_train_medal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

EnjoyTrainMedal _$EnjoyTrainMedalFromJson(Map<String, dynamic> json) {
  return _EnjoyTrainMedal.fromJson(json);
}

/// @nodoc
mixin _$EnjoyTrainMedal {
  String? get lockImage => throw _privateConstructorUsedError;
  String? get unlockImage => throw _privateConstructorUsedError;
  String? get resourceUrl => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  int? get medalId => throw _privateConstructorUsedError;
  int? get getTime => throw _privateConstructorUsedError;
  bool? get finished => throw _privateConstructorUsedError;
  String? get medalName => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get remark => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get guideMedalTitleImage => throw _privateConstructorUsedError;
  String? get guideMedalDesc => throw _privateConstructorUsedError;
  String? get guideMedalButton => throw _privateConstructorUsedError;
  String? get medalFinishedImage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EnjoyTrainMedalCopyWith<EnjoyTrainMedal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnjoyTrainMedalCopyWith<$Res> {
  factory $EnjoyTrainMedalCopyWith(
          EnjoyTrainMedal value, $Res Function(EnjoyTrainMedal) then) =
      _$EnjoyTrainMedalCopyWithImpl<$Res, EnjoyTrainMedal>;
  @useResult
  $Res call(
      {String? lockImage,
      String? unlockImage,
      String? resourceUrl,
      int? lessonId,
      int? medalId,
      int? getTime,
      bool? finished,
      String? medalName,
      String? title,
      String? remark,
      String? route,
      String? guideMedalTitleImage,
      String? guideMedalDesc,
      String? guideMedalButton,
      String? medalFinishedImage});
}

/// @nodoc
class _$EnjoyTrainMedalCopyWithImpl<$Res, $Val extends EnjoyTrainMedal>
    implements $EnjoyTrainMedalCopyWith<$Res> {
  _$EnjoyTrainMedalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceUrl = freezed,
    Object? lessonId = freezed,
    Object? medalId = freezed,
    Object? getTime = freezed,
    Object? finished = freezed,
    Object? medalName = freezed,
    Object? title = freezed,
    Object? remark = freezed,
    Object? route = freezed,
    Object? guideMedalTitleImage = freezed,
    Object? guideMedalDesc = freezed,
    Object? guideMedalButton = freezed,
    Object? medalFinishedImage = freezed,
  }) {
    return _then(_value.copyWith(
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      medalId: freezed == medalId
          ? _value.medalId
          : medalId // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      medalName: freezed == medalName
          ? _value.medalName
          : medalName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalTitleImage: freezed == guideMedalTitleImage
          ? _value.guideMedalTitleImage
          : guideMedalTitleImage // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalDesc: freezed == guideMedalDesc
          ? _value.guideMedalDesc
          : guideMedalDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalButton: freezed == guideMedalButton
          ? _value.guideMedalButton
          : guideMedalButton // ignore: cast_nullable_to_non_nullable
              as String?,
      medalFinishedImage: freezed == medalFinishedImage
          ? _value.medalFinishedImage
          : medalFinishedImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EnjoyTrainMedalCopyWith<$Res>
    implements $EnjoyTrainMedalCopyWith<$Res> {
  factory _$$_EnjoyTrainMedalCopyWith(
          _$_EnjoyTrainMedal value, $Res Function(_$_EnjoyTrainMedal) then) =
      __$$_EnjoyTrainMedalCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lockImage,
      String? unlockImage,
      String? resourceUrl,
      int? lessonId,
      int? medalId,
      int? getTime,
      bool? finished,
      String? medalName,
      String? title,
      String? remark,
      String? route,
      String? guideMedalTitleImage,
      String? guideMedalDesc,
      String? guideMedalButton,
      String? medalFinishedImage});
}

/// @nodoc
class __$$_EnjoyTrainMedalCopyWithImpl<$Res>
    extends _$EnjoyTrainMedalCopyWithImpl<$Res, _$_EnjoyTrainMedal>
    implements _$$_EnjoyTrainMedalCopyWith<$Res> {
  __$$_EnjoyTrainMedalCopyWithImpl(
      _$_EnjoyTrainMedal _value, $Res Function(_$_EnjoyTrainMedal) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceUrl = freezed,
    Object? lessonId = freezed,
    Object? medalId = freezed,
    Object? getTime = freezed,
    Object? finished = freezed,
    Object? medalName = freezed,
    Object? title = freezed,
    Object? remark = freezed,
    Object? route = freezed,
    Object? guideMedalTitleImage = freezed,
    Object? guideMedalDesc = freezed,
    Object? guideMedalButton = freezed,
    Object? medalFinishedImage = freezed,
  }) {
    return _then(_$_EnjoyTrainMedal(
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceUrl: freezed == resourceUrl
          ? _value.resourceUrl
          : resourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      medalId: freezed == medalId
          ? _value.medalId
          : medalId // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      finished: freezed == finished
          ? _value.finished
          : finished // ignore: cast_nullable_to_non_nullable
              as bool?,
      medalName: freezed == medalName
          ? _value.medalName
          : medalName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalTitleImage: freezed == guideMedalTitleImage
          ? _value.guideMedalTitleImage
          : guideMedalTitleImage // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalDesc: freezed == guideMedalDesc
          ? _value.guideMedalDesc
          : guideMedalDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      guideMedalButton: freezed == guideMedalButton
          ? _value.guideMedalButton
          : guideMedalButton // ignore: cast_nullable_to_non_nullable
              as String?,
      medalFinishedImage: freezed == medalFinishedImage
          ? _value.medalFinishedImage
          : medalFinishedImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EnjoyTrainMedal implements _EnjoyTrainMedal {
  const _$_EnjoyTrainMedal(
      {this.lockImage,
      this.unlockImage,
      this.resourceUrl,
      this.lessonId,
      this.medalId,
      this.getTime,
      this.finished,
      this.medalName,
      this.title,
      this.remark,
      this.route,
      this.guideMedalTitleImage,
      this.guideMedalDesc,
      this.guideMedalButton,
      this.medalFinishedImage});

  factory _$_EnjoyTrainMedal.fromJson(Map<String, dynamic> json) =>
      _$$_EnjoyTrainMedalFromJson(json);

  @override
  final String? lockImage;
  @override
  final String? unlockImage;
  @override
  final String? resourceUrl;
  @override
  final int? lessonId;
  @override
  final int? medalId;
  @override
  final int? getTime;
  @override
  final bool? finished;
  @override
  final String? medalName;
  @override
  final String? title;
  @override
  final String? remark;
  @override
  final String? route;
  @override
  final String? guideMedalTitleImage;
  @override
  final String? guideMedalDesc;
  @override
  final String? guideMedalButton;
  @override
  final String? medalFinishedImage;

  @override
  String toString() {
    return 'EnjoyTrainMedal(lockImage: $lockImage, unlockImage: $unlockImage, resourceUrl: $resourceUrl, lessonId: $lessonId, medalId: $medalId, getTime: $getTime, finished: $finished, medalName: $medalName, title: $title, remark: $remark, route: $route, guideMedalTitleImage: $guideMedalTitleImage, guideMedalDesc: $guideMedalDesc, guideMedalButton: $guideMedalButton, medalFinishedImage: $medalFinishedImage)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EnjoyTrainMedal &&
            (identical(other.lockImage, lockImage) ||
                other.lockImage == lockImage) &&
            (identical(other.unlockImage, unlockImage) ||
                other.unlockImage == unlockImage) &&
            (identical(other.resourceUrl, resourceUrl) ||
                other.resourceUrl == resourceUrl) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.medalId, medalId) || other.medalId == medalId) &&
            (identical(other.getTime, getTime) || other.getTime == getTime) &&
            (identical(other.finished, finished) ||
                other.finished == finished) &&
            (identical(other.medalName, medalName) ||
                other.medalName == medalName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.guideMedalTitleImage, guideMedalTitleImage) ||
                other.guideMedalTitleImage == guideMedalTitleImage) &&
            (identical(other.guideMedalDesc, guideMedalDesc) ||
                other.guideMedalDesc == guideMedalDesc) &&
            (identical(other.guideMedalButton, guideMedalButton) ||
                other.guideMedalButton == guideMedalButton) &&
            (identical(other.medalFinishedImage, medalFinishedImage) ||
                other.medalFinishedImage == medalFinishedImage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      lockImage,
      unlockImage,
      resourceUrl,
      lessonId,
      medalId,
      getTime,
      finished,
      medalName,
      title,
      remark,
      route,
      guideMedalTitleImage,
      guideMedalDesc,
      guideMedalButton,
      medalFinishedImage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EnjoyTrainMedalCopyWith<_$_EnjoyTrainMedal> get copyWith =>
      __$$_EnjoyTrainMedalCopyWithImpl<_$_EnjoyTrainMedal>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EnjoyTrainMedalToJson(
      this,
    );
  }
}

abstract class _EnjoyTrainMedal implements EnjoyTrainMedal {
  const factory _EnjoyTrainMedal(
      {final String? lockImage,
      final String? unlockImage,
      final String? resourceUrl,
      final int? lessonId,
      final int? medalId,
      final int? getTime,
      final bool? finished,
      final String? medalName,
      final String? title,
      final String? remark,
      final String? route,
      final String? guideMedalTitleImage,
      final String? guideMedalDesc,
      final String? guideMedalButton,
      final String? medalFinishedImage}) = _$_EnjoyTrainMedal;

  factory _EnjoyTrainMedal.fromJson(Map<String, dynamic> json) =
      _$_EnjoyTrainMedal.fromJson;

  @override
  String? get lockImage;
  @override
  String? get unlockImage;
  @override
  String? get resourceUrl;
  @override
  int? get lessonId;
  @override
  int? get medalId;
  @override
  int? get getTime;
  @override
  bool? get finished;
  @override
  String? get medalName;
  @override
  String? get title;
  @override
  String? get remark;
  @override
  String? get route;
  @override
  String? get guideMedalTitleImage;
  @override
  String? get guideMedalDesc;
  @override
  String? get guideMedalButton;
  @override
  String? get medalFinishedImage;
  @override
  @JsonKey(ignore: true)
  _$$_EnjoyTrainMedalCopyWith<_$_EnjoyTrainMedal> get copyWith =>
      throw _privateConstructorUsedError;
}
