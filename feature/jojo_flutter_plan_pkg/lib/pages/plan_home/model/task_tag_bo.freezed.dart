// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_tag_bo.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TaskTagBo _$TaskTagBoFromJson(Map<String, dynamic> json) {
  return _TaskTagBo.fromJson(json);
}

/// @nodoc
mixin _$TaskTagBo {
  String? get tagImage => throw _privateConstructorUsedError;

  /// 1.已完成标签 2.新获得标签
  int? get tagType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TaskTagBoCopyWith<TaskTagBo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskTagBoCopyWith<$Res> {
  factory $TaskTagBoCopyWith(TaskTagBo value, $Res Function(TaskTagBo) then) =
      _$TaskTagBoCopyWithImpl<$Res, TaskTagBo>;
  @useResult
  $Res call({String? tagImage, int? tagType});
}

/// @nodoc
class _$TaskTagBoCopyWithImpl<$Res, $Val extends TaskTagBo>
    implements $TaskTagBoCopyWith<$Res> {
  _$TaskTagBoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tagImage = freezed,
    Object? tagType = freezed,
  }) {
    return _then(_value.copyWith(
      tagImage: freezed == tagImage
          ? _value.tagImage
          : tagImage // ignore: cast_nullable_to_non_nullable
              as String?,
      tagType: freezed == tagType
          ? _value.tagType
          : tagType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TaskTagBoCopyWith<$Res> implements $TaskTagBoCopyWith<$Res> {
  factory _$$_TaskTagBoCopyWith(
          _$_TaskTagBo value, $Res Function(_$_TaskTagBo) then) =
      __$$_TaskTagBoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? tagImage, int? tagType});
}

/// @nodoc
class __$$_TaskTagBoCopyWithImpl<$Res>
    extends _$TaskTagBoCopyWithImpl<$Res, _$_TaskTagBo>
    implements _$$_TaskTagBoCopyWith<$Res> {
  __$$_TaskTagBoCopyWithImpl(
      _$_TaskTagBo _value, $Res Function(_$_TaskTagBo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tagImage = freezed,
    Object? tagType = freezed,
  }) {
    return _then(_$_TaskTagBo(
      tagImage: freezed == tagImage
          ? _value.tagImage
          : tagImage // ignore: cast_nullable_to_non_nullable
              as String?,
      tagType: freezed == tagType
          ? _value.tagType
          : tagType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TaskTagBo implements _TaskTagBo {
  const _$_TaskTagBo({this.tagImage, this.tagType});

  factory _$_TaskTagBo.fromJson(Map<String, dynamic> json) =>
      _$$_TaskTagBoFromJson(json);

  @override
  final String? tagImage;

  /// 1.已完成标签 2.新获得标签
  @override
  final int? tagType;

  @override
  String toString() {
    return 'TaskTagBo(tagImage: $tagImage, tagType: $tagType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TaskTagBo &&
            (identical(other.tagImage, tagImage) ||
                other.tagImage == tagImage) &&
            (identical(other.tagType, tagType) || other.tagType == tagType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tagImage, tagType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TaskTagBoCopyWith<_$_TaskTagBo> get copyWith =>
      __$$_TaskTagBoCopyWithImpl<_$_TaskTagBo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TaskTagBoToJson(
      this,
    );
  }
}

abstract class _TaskTagBo implements TaskTagBo {
  const factory _TaskTagBo({final String? tagImage, final int? tagType}) =
      _$_TaskTagBo;

  factory _TaskTagBo.fromJson(Map<String, dynamic> json) =
      _$_TaskTagBo.fromJson;

  @override
  String? get tagImage;
  @override

  /// 1.已完成标签 2.新获得标签
  int? get tagType;
  @override
  @JsonKey(ignore: true)
  _$$_TaskTagBoCopyWith<_$_TaskTagBo> get copyWith =>
      throw _privateConstructorUsedError;
}
