// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_home_page_year_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseHomePageYearData _$$_CourseHomePageYearDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseHomePageYearData(
      year: json['year'] as int?,
      subjectTypeVoList: (json['subjectTypeVoList'] as List<dynamic>?)
          ?.map((e) => SubjectTypeVoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      monthScheduleList: (json['monthScheduleList'] as List<dynamic>?)
          ?.map((e) => MonthScheduleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonSummaryList: (json['lessonSummaryList'] as List<dynamic>?)
          ?.map((e) => LessonSummaryList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseHomePageYearDataToJson(
        _$_CourseHomePageYearData instance) =>
    <String, dynamic>{
      'year': instance.year,
      'subjectTypeVoList': instance.subjectTypeVoList,
      'monthScheduleList': instance.monthScheduleList,
      'lessonSummaryList': instance.lessonSummaryList,
    };

_$_LessonSummaryList _$$_LessonSummaryListFromJson(Map<String, dynamic> json) =>
    _$_LessonSummaryList(
      subjectType: json['subjectType'] as int?,
      unFinishedNum: json['unFinishedNum'] as int?,
      finishedNum: json['finishedNum'] as int?,
    );

Map<String, dynamic> _$$_LessonSummaryListToJson(
        _$_LessonSummaryList instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'unFinishedNum': instance.unFinishedNum,
      'finishedNum': instance.finishedNum,
    };

_$_MonthScheduleList _$$_MonthScheduleListFromJson(Map<String, dynamic> json) =>
    _$_MonthScheduleList(
      month: json['month'] as String?,
      monthNum: json['monthNum'] as int?,
      currentMonth: json['currentMonth'] as bool?,
      scheduleList: (json['scheduleList'] as List<dynamic>?)
          ?.map((e) => ScheduleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      subjectImageList: (json['subjectImageList'] as List<dynamic>?)
          ?.map((e) => SubjectImageList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MonthScheduleListToJson(
        _$_MonthScheduleList instance) =>
    <String, dynamic>{
      'month': instance.month,
      'monthNum': instance.monthNum,
      'currentMonth': instance.currentMonth,
      'scheduleList': instance.scheduleList,
      'subjectImageList': instance.subjectImageList,
    };

_$_SubjectImageList _$$_SubjectImageListFromJson(Map<String, dynamic> json) =>
    _$_SubjectImageList(
      subjectType: json['subjectType'] as int?,
      lock: json['lock'] as bool?,
      image: json['image'] as String?,
      buy: json['buy'] as bool?,
      subscribe: json['subscribe'] as bool?,
      buyType: json['buyType'] as int?,
      buyImage: json['buyImage'] as String?,
      buyRoute: json['buyRoute'] as String?,
      courseSegmentName: json['courseSegmentName'] as String?,
      segmentId: json['segmentId'] as int?,
      segmentFirstStartTime: json['segmentFirstStartTime'] as int?,
      newGetFlag: json['newGetFlag'] as bool?,
      newGetClassId: json['newGetClassId'] as int?,
    );

Map<String, dynamic> _$$_SubjectImageListToJson(_$_SubjectImageList instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'lock': instance.lock,
      'image': instance.image,
      'buy': instance.buy,
      'subscribe': instance.subscribe,
      'buyType': instance.buyType,
      'buyImage': instance.buyImage,
      'buyRoute': instance.buyRoute,
      'courseSegmentName': instance.courseSegmentName,
      'segmentId': instance.segmentId,
      'segmentFirstStartTime': instance.segmentFirstStartTime,
      'newGetFlag': instance.newGetFlag,
      'newGetClassId': instance.newGetClassId,
    };

_$_ScheduleList _$$_ScheduleListFromJson(Map<String, dynamic> json) =>
    _$_ScheduleList(
      subjectScheduleStatus: (json['subjectScheduleStatus'] as List<dynamic>?)
          ?.map(
              (e) => SubjectScheduleStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      showDateTime: json['showDateTime'] as int?,
      today: json['today'] as bool?,
      scheduleTaskList: (json['scheduleTaskList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$$_ScheduleListToJson(_$_ScheduleList instance) =>
    <String, dynamic>{
      'subjectScheduleStatus': instance.subjectScheduleStatus,
      'showDateTime': instance.showDateTime,
      'today': instance.today,
      'scheduleTaskList': instance.scheduleTaskList,
    };

_$_SubjectScheduleStatus _$$_SubjectScheduleStatusFromJson(
        Map<String, dynamic> json) =>
    _$_SubjectScheduleStatus(
      subjectType: json['subjectType'] as int?,
      scheduleStatus: json['scheduleStatus'] as int?,
    );

Map<String, dynamic> _$$_SubjectScheduleStatusToJson(
        _$_SubjectScheduleStatus instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'scheduleStatus': instance.scheduleStatus,
    };

_$_SubjectTypeVoList _$$_SubjectTypeVoListFromJson(Map<String, dynamic> json) =>
    _$_SubjectTypeVoList(
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_SubjectTypeVoListToJson(
        _$_SubjectTypeVoList instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
    };
