// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_page_course_resource_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomePageCourseResourceData _$CourseHomePageCourseResourceDataFromJson(
    Map<String, dynamic> json) {
  return _CourseHomePageCourseResourceData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomePageCourseResourceData {
  String? get courseGuideCoverUrl => throw _privateConstructorUsedError;
  String? get courseGuideVideoUrl => throw _privateConstructorUsedError;
  String? get courseResourceRoute => throw _privateConstructorUsedError;
  String? get videoDescription => throw _privateConstructorUsedError;
  String? get courseTypeDesc => throw _privateConstructorUsedError;
  String? get courseStageDesc => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomePageCourseResourceDataCopyWith<CourseHomePageCourseResourceData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomePageCourseResourceDataCopyWith<$Res> {
  factory $CourseHomePageCourseResourceDataCopyWith(
          CourseHomePageCourseResourceData value,
          $Res Function(CourseHomePageCourseResourceData) then) =
      _$CourseHomePageCourseResourceDataCopyWithImpl<$Res,
          CourseHomePageCourseResourceData>;
  @useResult
  $Res call(
      {String? courseGuideCoverUrl,
      String? courseGuideVideoUrl,
      String? courseResourceRoute,
      String? videoDescription,
      String? courseTypeDesc,
      String? courseStageDesc,
      String? skuId,
      String? skuName});
}

/// @nodoc
class _$CourseHomePageCourseResourceDataCopyWithImpl<$Res,
        $Val extends CourseHomePageCourseResourceData>
    implements $CourseHomePageCourseResourceDataCopyWith<$Res> {
  _$CourseHomePageCourseResourceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseGuideCoverUrl = freezed,
    Object? courseGuideVideoUrl = freezed,
    Object? courseResourceRoute = freezed,
    Object? videoDescription = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
  }) {
    return _then(_value.copyWith(
      courseGuideCoverUrl: freezed == courseGuideCoverUrl
          ? _value.courseGuideCoverUrl
          : courseGuideCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseGuideVideoUrl: freezed == courseGuideVideoUrl
          ? _value.courseGuideVideoUrl
          : courseGuideVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseResourceRoute: freezed == courseResourceRoute
          ? _value.courseResourceRoute
          : courseResourceRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      videoDescription: freezed == videoDescription
          ? _value.videoDescription
          : videoDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseHomePageCourseResourceDataCopyWith<$Res>
    implements $CourseHomePageCourseResourceDataCopyWith<$Res> {
  factory _$$_CourseHomePageCourseResourceDataCopyWith(
          _$_CourseHomePageCourseResourceData value,
          $Res Function(_$_CourseHomePageCourseResourceData) then) =
      __$$_CourseHomePageCourseResourceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseGuideCoverUrl,
      String? courseGuideVideoUrl,
      String? courseResourceRoute,
      String? videoDescription,
      String? courseTypeDesc,
      String? courseStageDesc,
      String? skuId,
      String? skuName});
}

/// @nodoc
class __$$_CourseHomePageCourseResourceDataCopyWithImpl<$Res>
    extends _$CourseHomePageCourseResourceDataCopyWithImpl<$Res,
        _$_CourseHomePageCourseResourceData>
    implements _$$_CourseHomePageCourseResourceDataCopyWith<$Res> {
  __$$_CourseHomePageCourseResourceDataCopyWithImpl(
      _$_CourseHomePageCourseResourceData _value,
      $Res Function(_$_CourseHomePageCourseResourceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseGuideCoverUrl = freezed,
    Object? courseGuideVideoUrl = freezed,
    Object? courseResourceRoute = freezed,
    Object? videoDescription = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
  }) {
    return _then(_$_CourseHomePageCourseResourceData(
      courseGuideCoverUrl: freezed == courseGuideCoverUrl
          ? _value.courseGuideCoverUrl
          : courseGuideCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseGuideVideoUrl: freezed == courseGuideVideoUrl
          ? _value.courseGuideVideoUrl
          : courseGuideVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseResourceRoute: freezed == courseResourceRoute
          ? _value.courseResourceRoute
          : courseResourceRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      videoDescription: freezed == videoDescription
          ? _value.videoDescription
          : videoDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomePageCourseResourceData
    implements _CourseHomePageCourseResourceData {
  const _$_CourseHomePageCourseResourceData(
      {this.courseGuideCoverUrl,
      this.courseGuideVideoUrl,
      this.courseResourceRoute,
      this.videoDescription,
      this.courseTypeDesc,
      this.courseStageDesc,
      this.skuId,
      this.skuName});

  factory _$_CourseHomePageCourseResourceData.fromJson(
          Map<String, dynamic> json) =>
      _$$_CourseHomePageCourseResourceDataFromJson(json);

  @override
  final String? courseGuideCoverUrl;
  @override
  final String? courseGuideVideoUrl;
  @override
  final String? courseResourceRoute;
  @override
  final String? videoDescription;
  @override
  final String? courseTypeDesc;
  @override
  final String? courseStageDesc;
  @override
  final String? skuId;
  @override
  final String? skuName;

  @override
  String toString() {
    return 'CourseHomePageCourseResourceData(courseGuideCoverUrl: $courseGuideCoverUrl, courseGuideVideoUrl: $courseGuideVideoUrl, courseResourceRoute: $courseResourceRoute, videoDescription: $videoDescription, courseTypeDesc: $courseTypeDesc, courseStageDesc: $courseStageDesc, skuId: $skuId, skuName: $skuName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomePageCourseResourceData &&
            (identical(other.courseGuideCoverUrl, courseGuideCoverUrl) ||
                other.courseGuideCoverUrl == courseGuideCoverUrl) &&
            (identical(other.courseGuideVideoUrl, courseGuideVideoUrl) ||
                other.courseGuideVideoUrl == courseGuideVideoUrl) &&
            (identical(other.courseResourceRoute, courseResourceRoute) ||
                other.courseResourceRoute == courseResourceRoute) &&
            (identical(other.videoDescription, videoDescription) ||
                other.videoDescription == videoDescription) &&
            (identical(other.courseTypeDesc, courseTypeDesc) ||
                other.courseTypeDesc == courseTypeDesc) &&
            (identical(other.courseStageDesc, courseStageDesc) ||
                other.courseStageDesc == courseStageDesc) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseGuideCoverUrl,
      courseGuideVideoUrl,
      courseResourceRoute,
      videoDescription,
      courseTypeDesc,
      courseStageDesc,
      skuId,
      skuName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomePageCourseResourceDataCopyWith<
          _$_CourseHomePageCourseResourceData>
      get copyWith => __$$_CourseHomePageCourseResourceDataCopyWithImpl<
          _$_CourseHomePageCourseResourceData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomePageCourseResourceDataToJson(
      this,
    );
  }
}

abstract class _CourseHomePageCourseResourceData
    implements CourseHomePageCourseResourceData {
  const factory _CourseHomePageCourseResourceData(
      {final String? courseGuideCoverUrl,
      final String? courseGuideVideoUrl,
      final String? courseResourceRoute,
      final String? videoDescription,
      final String? courseTypeDesc,
      final String? courseStageDesc,
      final String? skuId,
      final String? skuName}) = _$_CourseHomePageCourseResourceData;

  factory _CourseHomePageCourseResourceData.fromJson(
      Map<String, dynamic> json) = _$_CourseHomePageCourseResourceData.fromJson;

  @override
  String? get courseGuideCoverUrl;
  @override
  String? get courseGuideVideoUrl;
  @override
  String? get courseResourceRoute;
  @override
  String? get videoDescription;
  @override
  String? get courseTypeDesc;
  @override
  String? get courseStageDesc;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomePageCourseResourceDataCopyWith<
          _$_CourseHomePageCourseResourceData>
      get copyWith => throw _privateConstructorUsedError;
}
