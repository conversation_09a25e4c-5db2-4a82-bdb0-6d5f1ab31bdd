// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_home_task_extension_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseHomeTaskExtensionData _$$_CourseHomeTaskExtensionDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseHomeTaskExtensionData(
      forestActivityExtra: json['forestActivityExtra'] == null
          ? null
          : ForestActivityExtra.fromJson(
              json['forestActivityExtra'] as Map<String, dynamic>),
      evaluateExtra: json['evaluateExtra'] == null
          ? null
          : EvaluateExtra.fromJson(
              json['evaluateExtra'] as Map<String, dynamic>),
      tipsExtra: json['tipsExtra'] == null
          ? null
          : TipsExtra.fromJson(json['tipsExtra'] as Map<String, dynamic>),
      externalRouteExtra: json['externalRouteExtra'] == null
          ? null
          : ExternalRouteExtra.fromJson(
              json['externalRouteExtra'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseHomeTaskExtensionDataToJson(
        _$_CourseHomeTaskExtensionData instance) =>
    <String, dynamic>{
      'forestActivityExtra': instance.forestActivityExtra,
      'evaluateExtra': instance.evaluateExtra,
      'tipsExtra': instance.tipsExtra,
      'externalRouteExtra': instance.externalRouteExtra,
    };

_$_ForestActivityExtra _$$_ForestActivityExtraFromJson(
        Map<String, dynamic> json) =>
    _$_ForestActivityExtra(
      dripStatus: json['dripStatus'] as String?,
      plantUsedDrips: json['plantUsedDrips'] as int?,
      plantNeedDrips: json['plantNeedDrips'] as int?,
    );

Map<String, dynamic> _$$_ForestActivityExtraToJson(
        _$_ForestActivityExtra instance) =>
    <String, dynamic>{
      'dripStatus': instance.dripStatus,
      'plantUsedDrips': instance.plantUsedDrips,
      'plantNeedDrips': instance.plantNeedDrips,
    };

_$_EvaluateExtra _$$_EvaluateExtraFromJson(Map<String, dynamic> json) =>
    _$_EvaluateExtra(
      evaluateType: json['evaluateType'] as int?,
    );

Map<String, dynamic> _$$_EvaluateExtraToJson(_$_EvaluateExtra instance) =>
    <String, dynamic>{
      'evaluateType': instance.evaluateType,
    };

_$_TipsExtra _$$_TipsExtraFromJson(Map<String, dynamic> json) => _$_TipsExtra(
      tipSource: json['tipSource'] as int?,
    );

Map<String, dynamic> _$$_TipsExtraToJson(_$_TipsExtra instance) =>
    <String, dynamic>{
      'tipSource': instance.tipSource,
    };

_$_ExternalRouteExtra _$$_ExternalRouteExtraFromJson(
        Map<String, dynamic> json) =>
    _$_ExternalRouteExtra(
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_ExternalRouteExtraToJson(
        _$_ExternalRouteExtra instance) =>
    <String, dynamic>{
      'route': instance.route,
    };
