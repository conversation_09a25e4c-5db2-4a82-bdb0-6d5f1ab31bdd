// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_task_extension_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomeTaskExtensionData _$CourseHomeTaskExtensionDataFromJson(
    Map<String, dynamic> json) {
  return _CourseHomeTaskExtensionData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomeTaskExtensionData {
  ForestActivityExtra? get forestActivityExtra =>
      throw _privateConstructorUsedError;
  EvaluateExtra? get evaluateExtra => throw _privateConstructorUsedError;
  TipsExtra? get tipsExtra => throw _privateConstructorUsedError;
  ExternalRouteExtra? get externalRouteExtra =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomeTaskExtensionDataCopyWith<CourseHomeTaskExtensionData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomeTaskExtensionDataCopyWith<$Res> {
  factory $CourseHomeTaskExtensionDataCopyWith(
          CourseHomeTaskExtensionData value,
          $Res Function(CourseHomeTaskExtensionData) then) =
      _$CourseHomeTaskExtensionDataCopyWithImpl<$Res,
          CourseHomeTaskExtensionData>;
  @useResult
  $Res call(
      {ForestActivityExtra? forestActivityExtra,
      EvaluateExtra? evaluateExtra,
      TipsExtra? tipsExtra,
      ExternalRouteExtra? externalRouteExtra});

  $ForestActivityExtraCopyWith<$Res>? get forestActivityExtra;
  $EvaluateExtraCopyWith<$Res>? get evaluateExtra;
  $TipsExtraCopyWith<$Res>? get tipsExtra;
  $ExternalRouteExtraCopyWith<$Res>? get externalRouteExtra;
}

/// @nodoc
class _$CourseHomeTaskExtensionDataCopyWithImpl<$Res,
        $Val extends CourseHomeTaskExtensionData>
    implements $CourseHomeTaskExtensionDataCopyWith<$Res> {
  _$CourseHomeTaskExtensionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forestActivityExtra = freezed,
    Object? evaluateExtra = freezed,
    Object? tipsExtra = freezed,
    Object? externalRouteExtra = freezed,
  }) {
    return _then(_value.copyWith(
      forestActivityExtra: freezed == forestActivityExtra
          ? _value.forestActivityExtra
          : forestActivityExtra // ignore: cast_nullable_to_non_nullable
              as ForestActivityExtra?,
      evaluateExtra: freezed == evaluateExtra
          ? _value.evaluateExtra
          : evaluateExtra // ignore: cast_nullable_to_non_nullable
              as EvaluateExtra?,
      tipsExtra: freezed == tipsExtra
          ? _value.tipsExtra
          : tipsExtra // ignore: cast_nullable_to_non_nullable
              as TipsExtra?,
      externalRouteExtra: freezed == externalRouteExtra
          ? _value.externalRouteExtra
          : externalRouteExtra // ignore: cast_nullable_to_non_nullable
              as ExternalRouteExtra?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ForestActivityExtraCopyWith<$Res>? get forestActivityExtra {
    if (_value.forestActivityExtra == null) {
      return null;
    }

    return $ForestActivityExtraCopyWith<$Res>(_value.forestActivityExtra!,
        (value) {
      return _then(_value.copyWith(forestActivityExtra: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $EvaluateExtraCopyWith<$Res>? get evaluateExtra {
    if (_value.evaluateExtra == null) {
      return null;
    }

    return $EvaluateExtraCopyWith<$Res>(_value.evaluateExtra!, (value) {
      return _then(_value.copyWith(evaluateExtra: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TipsExtraCopyWith<$Res>? get tipsExtra {
    if (_value.tipsExtra == null) {
      return null;
    }

    return $TipsExtraCopyWith<$Res>(_value.tipsExtra!, (value) {
      return _then(_value.copyWith(tipsExtra: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ExternalRouteExtraCopyWith<$Res>? get externalRouteExtra {
    if (_value.externalRouteExtra == null) {
      return null;
    }

    return $ExternalRouteExtraCopyWith<$Res>(_value.externalRouteExtra!,
        (value) {
      return _then(_value.copyWith(externalRouteExtra: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseHomeTaskExtensionDataCopyWith<$Res>
    implements $CourseHomeTaskExtensionDataCopyWith<$Res> {
  factory _$$_CourseHomeTaskExtensionDataCopyWith(
          _$_CourseHomeTaskExtensionData value,
          $Res Function(_$_CourseHomeTaskExtensionData) then) =
      __$$_CourseHomeTaskExtensionDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ForestActivityExtra? forestActivityExtra,
      EvaluateExtra? evaluateExtra,
      TipsExtra? tipsExtra,
      ExternalRouteExtra? externalRouteExtra});

  @override
  $ForestActivityExtraCopyWith<$Res>? get forestActivityExtra;
  @override
  $EvaluateExtraCopyWith<$Res>? get evaluateExtra;
  @override
  $TipsExtraCopyWith<$Res>? get tipsExtra;
  @override
  $ExternalRouteExtraCopyWith<$Res>? get externalRouteExtra;
}

/// @nodoc
class __$$_CourseHomeTaskExtensionDataCopyWithImpl<$Res>
    extends _$CourseHomeTaskExtensionDataCopyWithImpl<$Res,
        _$_CourseHomeTaskExtensionData>
    implements _$$_CourseHomeTaskExtensionDataCopyWith<$Res> {
  __$$_CourseHomeTaskExtensionDataCopyWithImpl(
      _$_CourseHomeTaskExtensionData _value,
      $Res Function(_$_CourseHomeTaskExtensionData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forestActivityExtra = freezed,
    Object? evaluateExtra = freezed,
    Object? tipsExtra = freezed,
    Object? externalRouteExtra = freezed,
  }) {
    return _then(_$_CourseHomeTaskExtensionData(
      forestActivityExtra: freezed == forestActivityExtra
          ? _value.forestActivityExtra
          : forestActivityExtra // ignore: cast_nullable_to_non_nullable
              as ForestActivityExtra?,
      evaluateExtra: freezed == evaluateExtra
          ? _value.evaluateExtra
          : evaluateExtra // ignore: cast_nullable_to_non_nullable
              as EvaluateExtra?,
      tipsExtra: freezed == tipsExtra
          ? _value.tipsExtra
          : tipsExtra // ignore: cast_nullable_to_non_nullable
              as TipsExtra?,
      externalRouteExtra: freezed == externalRouteExtra
          ? _value.externalRouteExtra
          : externalRouteExtra // ignore: cast_nullable_to_non_nullable
              as ExternalRouteExtra?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomeTaskExtensionData implements _CourseHomeTaskExtensionData {
  const _$_CourseHomeTaskExtensionData(
      {this.forestActivityExtra,
      this.evaluateExtra,
      this.tipsExtra,
      this.externalRouteExtra});

  factory _$_CourseHomeTaskExtensionData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseHomeTaskExtensionDataFromJson(json);

  @override
  final ForestActivityExtra? forestActivityExtra;
  @override
  final EvaluateExtra? evaluateExtra;
  @override
  final TipsExtra? tipsExtra;
  @override
  final ExternalRouteExtra? externalRouteExtra;

  @override
  String toString() {
    return 'CourseHomeTaskExtensionData(forestActivityExtra: $forestActivityExtra, evaluateExtra: $evaluateExtra, tipsExtra: $tipsExtra, externalRouteExtra: $externalRouteExtra)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomeTaskExtensionData &&
            (identical(other.forestActivityExtra, forestActivityExtra) ||
                other.forestActivityExtra == forestActivityExtra) &&
            (identical(other.evaluateExtra, evaluateExtra) ||
                other.evaluateExtra == evaluateExtra) &&
            (identical(other.tipsExtra, tipsExtra) ||
                other.tipsExtra == tipsExtra) &&
            (identical(other.externalRouteExtra, externalRouteExtra) ||
                other.externalRouteExtra == externalRouteExtra));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, forestActivityExtra,
      evaluateExtra, tipsExtra, externalRouteExtra);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomeTaskExtensionDataCopyWith<_$_CourseHomeTaskExtensionData>
      get copyWith => __$$_CourseHomeTaskExtensionDataCopyWithImpl<
          _$_CourseHomeTaskExtensionData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomeTaskExtensionDataToJson(
      this,
    );
  }
}

abstract class _CourseHomeTaskExtensionData
    implements CourseHomeTaskExtensionData {
  const factory _CourseHomeTaskExtensionData(
          {final ForestActivityExtra? forestActivityExtra,
          final EvaluateExtra? evaluateExtra,
          final TipsExtra? tipsExtra,
          final ExternalRouteExtra? externalRouteExtra}) =
      _$_CourseHomeTaskExtensionData;

  factory _CourseHomeTaskExtensionData.fromJson(Map<String, dynamic> json) =
      _$_CourseHomeTaskExtensionData.fromJson;

  @override
  ForestActivityExtra? get forestActivityExtra;
  @override
  EvaluateExtra? get evaluateExtra;
  @override
  TipsExtra? get tipsExtra;
  @override
  ExternalRouteExtra? get externalRouteExtra;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomeTaskExtensionDataCopyWith<_$_CourseHomeTaskExtensionData>
      get copyWith => throw _privateConstructorUsedError;
}

ForestActivityExtra _$ForestActivityExtraFromJson(Map<String, dynamic> json) {
  return _ForestActivityExtra.fromJson(json);
}

/// @nodoc
mixin _$ForestActivityExtra {
  String? get dripStatus => throw _privateConstructorUsedError;
  int? get plantUsedDrips => throw _privateConstructorUsedError;
  int? get plantNeedDrips => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ForestActivityExtraCopyWith<ForestActivityExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForestActivityExtraCopyWith<$Res> {
  factory $ForestActivityExtraCopyWith(
          ForestActivityExtra value, $Res Function(ForestActivityExtra) then) =
      _$ForestActivityExtraCopyWithImpl<$Res, ForestActivityExtra>;
  @useResult
  $Res call({String? dripStatus, int? plantUsedDrips, int? plantNeedDrips});
}

/// @nodoc
class _$ForestActivityExtraCopyWithImpl<$Res, $Val extends ForestActivityExtra>
    implements $ForestActivityExtraCopyWith<$Res> {
  _$ForestActivityExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dripStatus = freezed,
    Object? plantUsedDrips = freezed,
    Object? plantNeedDrips = freezed,
  }) {
    return _then(_value.copyWith(
      dripStatus: freezed == dripStatus
          ? _value.dripStatus
          : dripStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      plantUsedDrips: freezed == plantUsedDrips
          ? _value.plantUsedDrips
          : plantUsedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantNeedDrips: freezed == plantNeedDrips
          ? _value.plantNeedDrips
          : plantNeedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ForestActivityExtraCopyWith<$Res>
    implements $ForestActivityExtraCopyWith<$Res> {
  factory _$$_ForestActivityExtraCopyWith(_$_ForestActivityExtra value,
          $Res Function(_$_ForestActivityExtra) then) =
      __$$_ForestActivityExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? dripStatus, int? plantUsedDrips, int? plantNeedDrips});
}

/// @nodoc
class __$$_ForestActivityExtraCopyWithImpl<$Res>
    extends _$ForestActivityExtraCopyWithImpl<$Res, _$_ForestActivityExtra>
    implements _$$_ForestActivityExtraCopyWith<$Res> {
  __$$_ForestActivityExtraCopyWithImpl(_$_ForestActivityExtra _value,
      $Res Function(_$_ForestActivityExtra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dripStatus = freezed,
    Object? plantUsedDrips = freezed,
    Object? plantNeedDrips = freezed,
  }) {
    return _then(_$_ForestActivityExtra(
      dripStatus: freezed == dripStatus
          ? _value.dripStatus
          : dripStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      plantUsedDrips: freezed == plantUsedDrips
          ? _value.plantUsedDrips
          : plantUsedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
      plantNeedDrips: freezed == plantNeedDrips
          ? _value.plantNeedDrips
          : plantNeedDrips // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ForestActivityExtra implements _ForestActivityExtra {
  const _$_ForestActivityExtra(
      {this.dripStatus, this.plantUsedDrips, this.plantNeedDrips});

  factory _$_ForestActivityExtra.fromJson(Map<String, dynamic> json) =>
      _$$_ForestActivityExtraFromJson(json);

  @override
  final String? dripStatus;
  @override
  final int? plantUsedDrips;
  @override
  final int? plantNeedDrips;

  @override
  String toString() {
    return 'ForestActivityExtra(dripStatus: $dripStatus, plantUsedDrips: $plantUsedDrips, plantNeedDrips: $plantNeedDrips)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ForestActivityExtra &&
            (identical(other.dripStatus, dripStatus) ||
                other.dripStatus == dripStatus) &&
            (identical(other.plantUsedDrips, plantUsedDrips) ||
                other.plantUsedDrips == plantUsedDrips) &&
            (identical(other.plantNeedDrips, plantNeedDrips) ||
                other.plantNeedDrips == plantNeedDrips));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, dripStatus, plantUsedDrips, plantNeedDrips);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ForestActivityExtraCopyWith<_$_ForestActivityExtra> get copyWith =>
      __$$_ForestActivityExtraCopyWithImpl<_$_ForestActivityExtra>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ForestActivityExtraToJson(
      this,
    );
  }
}

abstract class _ForestActivityExtra implements ForestActivityExtra {
  const factory _ForestActivityExtra(
      {final String? dripStatus,
      final int? plantUsedDrips,
      final int? plantNeedDrips}) = _$_ForestActivityExtra;

  factory _ForestActivityExtra.fromJson(Map<String, dynamic> json) =
      _$_ForestActivityExtra.fromJson;

  @override
  String? get dripStatus;
  @override
  int? get plantUsedDrips;
  @override
  int? get plantNeedDrips;
  @override
  @JsonKey(ignore: true)
  _$$_ForestActivityExtraCopyWith<_$_ForestActivityExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

EvaluateExtra _$EvaluateExtraFromJson(Map<String, dynamic> json) {
  return _EvaluateExtra.fromJson(json);
}

/// @nodoc
mixin _$EvaluateExtra {
  int? get evaluateType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EvaluateExtraCopyWith<EvaluateExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EvaluateExtraCopyWith<$Res> {
  factory $EvaluateExtraCopyWith(
          EvaluateExtra value, $Res Function(EvaluateExtra) then) =
      _$EvaluateExtraCopyWithImpl<$Res, EvaluateExtra>;
  @useResult
  $Res call({int? evaluateType});
}

/// @nodoc
class _$EvaluateExtraCopyWithImpl<$Res, $Val extends EvaluateExtra>
    implements $EvaluateExtraCopyWith<$Res> {
  _$EvaluateExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? evaluateType = freezed,
  }) {
    return _then(_value.copyWith(
      evaluateType: freezed == evaluateType
          ? _value.evaluateType
          : evaluateType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EvaluateExtraCopyWith<$Res>
    implements $EvaluateExtraCopyWith<$Res> {
  factory _$$_EvaluateExtraCopyWith(
          _$_EvaluateExtra value, $Res Function(_$_EvaluateExtra) then) =
      __$$_EvaluateExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? evaluateType});
}

/// @nodoc
class __$$_EvaluateExtraCopyWithImpl<$Res>
    extends _$EvaluateExtraCopyWithImpl<$Res, _$_EvaluateExtra>
    implements _$$_EvaluateExtraCopyWith<$Res> {
  __$$_EvaluateExtraCopyWithImpl(
      _$_EvaluateExtra _value, $Res Function(_$_EvaluateExtra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? evaluateType = freezed,
  }) {
    return _then(_$_EvaluateExtra(
      evaluateType: freezed == evaluateType
          ? _value.evaluateType
          : evaluateType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EvaluateExtra implements _EvaluateExtra {
  const _$_EvaluateExtra({this.evaluateType});

  factory _$_EvaluateExtra.fromJson(Map<String, dynamic> json) =>
      _$$_EvaluateExtraFromJson(json);

  @override
  final int? evaluateType;

  @override
  String toString() {
    return 'EvaluateExtra(evaluateType: $evaluateType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EvaluateExtra &&
            (identical(other.evaluateType, evaluateType) ||
                other.evaluateType == evaluateType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, evaluateType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EvaluateExtraCopyWith<_$_EvaluateExtra> get copyWith =>
      __$$_EvaluateExtraCopyWithImpl<_$_EvaluateExtra>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EvaluateExtraToJson(
      this,
    );
  }
}

abstract class _EvaluateExtra implements EvaluateExtra {
  const factory _EvaluateExtra({final int? evaluateType}) = _$_EvaluateExtra;

  factory _EvaluateExtra.fromJson(Map<String, dynamic> json) =
      _$_EvaluateExtra.fromJson;

  @override
  int? get evaluateType;
  @override
  @JsonKey(ignore: true)
  _$$_EvaluateExtraCopyWith<_$_EvaluateExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

TipsExtra _$TipsExtraFromJson(Map<String, dynamic> json) {
  return _TipsExtra.fromJson(json);
}

/// @nodoc
mixin _$TipsExtra {
  int? get tipSource => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TipsExtraCopyWith<TipsExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TipsExtraCopyWith<$Res> {
  factory $TipsExtraCopyWith(TipsExtra value, $Res Function(TipsExtra) then) =
      _$TipsExtraCopyWithImpl<$Res, TipsExtra>;
  @useResult
  $Res call({int? tipSource});
}

/// @nodoc
class _$TipsExtraCopyWithImpl<$Res, $Val extends TipsExtra>
    implements $TipsExtraCopyWith<$Res> {
  _$TipsExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipSource = freezed,
  }) {
    return _then(_value.copyWith(
      tipSource: freezed == tipSource
          ? _value.tipSource
          : tipSource // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TipsExtraCopyWith<$Res> implements $TipsExtraCopyWith<$Res> {
  factory _$$_TipsExtraCopyWith(
          _$_TipsExtra value, $Res Function(_$_TipsExtra) then) =
      __$$_TipsExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? tipSource});
}

/// @nodoc
class __$$_TipsExtraCopyWithImpl<$Res>
    extends _$TipsExtraCopyWithImpl<$Res, _$_TipsExtra>
    implements _$$_TipsExtraCopyWith<$Res> {
  __$$_TipsExtraCopyWithImpl(
      _$_TipsExtra _value, $Res Function(_$_TipsExtra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipSource = freezed,
  }) {
    return _then(_$_TipsExtra(
      tipSource: freezed == tipSource
          ? _value.tipSource
          : tipSource // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TipsExtra implements _TipsExtra {
  const _$_TipsExtra({this.tipSource});

  factory _$_TipsExtra.fromJson(Map<String, dynamic> json) =>
      _$$_TipsExtraFromJson(json);

  @override
  final int? tipSource;

  @override
  String toString() {
    return 'TipsExtra(tipSource: $tipSource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TipsExtra &&
            (identical(other.tipSource, tipSource) ||
                other.tipSource == tipSource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tipSource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TipsExtraCopyWith<_$_TipsExtra> get copyWith =>
      __$$_TipsExtraCopyWithImpl<_$_TipsExtra>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TipsExtraToJson(
      this,
    );
  }
}

abstract class _TipsExtra implements TipsExtra {
  const factory _TipsExtra({final int? tipSource}) = _$_TipsExtra;

  factory _TipsExtra.fromJson(Map<String, dynamic> json) =
      _$_TipsExtra.fromJson;

  @override
  int? get tipSource;
  @override
  @JsonKey(ignore: true)
  _$$_TipsExtraCopyWith<_$_TipsExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

ExternalRouteExtra _$ExternalRouteExtraFromJson(Map<String, dynamic> json) {
  return _ExternalRouteExtra.fromJson(json);
}

/// @nodoc
mixin _$ExternalRouteExtra {
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExternalRouteExtraCopyWith<ExternalRouteExtra> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExternalRouteExtraCopyWith<$Res> {
  factory $ExternalRouteExtraCopyWith(
          ExternalRouteExtra value, $Res Function(ExternalRouteExtra) then) =
      _$ExternalRouteExtraCopyWithImpl<$Res, ExternalRouteExtra>;
  @useResult
  $Res call({String? route});
}

/// @nodoc
class _$ExternalRouteExtraCopyWithImpl<$Res, $Val extends ExternalRouteExtra>
    implements $ExternalRouteExtraCopyWith<$Res> {
  _$ExternalRouteExtraCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExternalRouteExtraCopyWith<$Res>
    implements $ExternalRouteExtraCopyWith<$Res> {
  factory _$$_ExternalRouteExtraCopyWith(_$_ExternalRouteExtra value,
          $Res Function(_$_ExternalRouteExtra) then) =
      __$$_ExternalRouteExtraCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? route});
}

/// @nodoc
class __$$_ExternalRouteExtraCopyWithImpl<$Res>
    extends _$ExternalRouteExtraCopyWithImpl<$Res, _$_ExternalRouteExtra>
    implements _$$_ExternalRouteExtraCopyWith<$Res> {
  __$$_ExternalRouteExtraCopyWithImpl(
      _$_ExternalRouteExtra _value, $Res Function(_$_ExternalRouteExtra) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? route = freezed,
  }) {
    return _then(_$_ExternalRouteExtra(
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExternalRouteExtra implements _ExternalRouteExtra {
  const _$_ExternalRouteExtra({this.route});

  factory _$_ExternalRouteExtra.fromJson(Map<String, dynamic> json) =>
      _$$_ExternalRouteExtraFromJson(json);

  @override
  final String? route;

  @override
  String toString() {
    return 'ExternalRouteExtra(route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExternalRouteExtra &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExternalRouteExtraCopyWith<_$_ExternalRouteExtra> get copyWith =>
      __$$_ExternalRouteExtraCopyWithImpl<_$_ExternalRouteExtra>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExternalRouteExtraToJson(
      this,
    );
  }
}

abstract class _ExternalRouteExtra implements ExternalRouteExtra {
  const factory _ExternalRouteExtra({final String? route}) =
      _$_ExternalRouteExtra;

  factory _ExternalRouteExtra.fromJson(Map<String, dynamic> json) =
      _$_ExternalRouteExtra.fromJson;

  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_ExternalRouteExtraCopyWith<_$_ExternalRouteExtra> get copyWith =>
      throw _privateConstructorUsedError;
}
