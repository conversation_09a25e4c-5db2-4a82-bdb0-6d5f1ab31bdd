// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_home_page_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseHomePageData _$$_CourseHomePageDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseHomePageData(
      contactRoute: json['contactRoute'] as String?,
      subjectTypeVoList: (json['subjectTypeVoList'] as List<dynamic>?)
          ?.map((e) => SubjectTypeVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      scheduleList: (json['scheduleList'] as List<dynamic>?)
          ?.map((e) => ScheduleInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonSummaryList: (json['lessonSummaryList'] as List<dynamic>?)
          ?.map((e) => LessonSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      schedulePopupList: (json['schedulePopupList'] as List<dynamic>?)
          ?.map((e) => SchedulePopup.fromJson(e as Map<String, dynamic>))
          .toList(),
      scheduleRemindList: (json['scheduleRemindList'] as List<dynamic>?)
          ?.map((e) => ScheduleRemindBo.fromJson(e as Map<String, dynamic>))
          .toList(),
      userGiftCourseVoList: (json['userGiftCourseVoList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : UserGifCourse.fromJson(e as Map<String, dynamic>))
          .toList(),
      restResources: json['restResources'] == null
          ? null
          : RestResources.fromJson(
              json['restResources'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseHomePageDataToJson(
        _$_CourseHomePageData instance) =>
    <String, dynamic>{
      'contactRoute': instance.contactRoute,
      'subjectTypeVoList': instance.subjectTypeVoList,
      'scheduleList': instance.scheduleList,
      'lessonSummaryList': instance.lessonSummaryList,
      'schedulePopupList': instance.schedulePopupList,
      'scheduleRemindList': instance.scheduleRemindList,
      'userGiftCourseVoList': instance.userGiftCourseVoList,
      'restResources': instance.restResources,
    };

_$_LessonSummary _$$_LessonSummaryFromJson(Map<String, dynamic> json) =>
    _$_LessonSummary(
      unFinishedNum: json['unFinishedNum'] as int?,
      finishedNum: json['finishedNum'] as int?,
      subjectType: json['subjectType'] as int?,
    );

Map<String, dynamic> _$$_LessonSummaryToJson(_$_LessonSummary instance) =>
    <String, dynamic>{
      'unFinishedNum': instance.unFinishedNum,
      'finishedNum': instance.finishedNum,
      'subjectType': instance.subjectType,
    };

_$_ScheduleRemindBo _$$_ScheduleRemindBoFromJson(Map<String, dynamic> json) =>
    _$_ScheduleRemindBo(
      remindType: json['remindType'] as int?,
      remindOrder: json['remindOrder'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      order: json['order'] as int?,
      remindTime: json['remindTime'] as int?,
      classId: json['classId'] as int?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$_ScheduleRemindBoToJson(_$_ScheduleRemindBo instance) =>
    <String, dynamic>{
      'remindType': instance.remindType,
      'remindOrder': instance.remindOrder,
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'order': instance.order,
      'remindTime': instance.remindTime,
      'classId': instance.classId,
      'title': instance.title,
    };

_$_RestResources _$$_RestResourcesFromJson(Map<String, dynamic> json) =>
    _$_RestResources(
      image: json['image'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$_RestResourcesToJson(_$_RestResources instance) =>
    <String, dynamic>{
      'image': instance.image,
      'title': instance.title,
    };

_$_SchedulePopup _$$_SchedulePopupFromJson(Map<String, dynamic> json) =>
    _$_SchedulePopup(
      popupType: json['popupType'] as int?,
      subjectType: json['subjectType'] as int?,
      classId: json['classId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseSegment: json['courseSegment'] as String?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      image: json['image'] as String?,
      voice: json['voice'] as String?,
      startClassTime: json['startClassTime'] as int?,
      firstLessonStartTime: json['firstLessonStartTime'] as int?,
      teacherName: json['teacherName'] as String?,
      teacherProfileUrl: json['teacherProfileUrl'] as String?,
      icon: json['icon'] as String?,
      template: json['template'] as int?,
      pattern: json['pattern'] as int?,
      addTeacherUrl: json['addTeacherUrl'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      segmentName: json['segmentName'] as String?,
      userCourseBusinessStatus: json['userCourseBusinessStatus'] as String?,
      guideImage: json['guideImage'] as String?,
      addButton: json['addButton'] as String?,
      cancelButton: json['cancelButton'] as String?,
      offlineBlockPopup: json['offlineBlockPopup'] as bool?,
      buttonText: json['buttonText'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      limitFlow: json['limitFlow'] as bool?,
      businessTypeDesc: json['businessTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_SchedulePopupToJson(_$_SchedulePopup instance) =>
    <String, dynamic>{
      'popupType': instance.popupType,
      'subjectType': instance.subjectType,
      'classId': instance.classId,
      'courseKey': instance.courseKey,
      'courseSegment': instance.courseSegment,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'image': instance.image,
      'voice': instance.voice,
      'startClassTime': instance.startClassTime,
      'firstLessonStartTime': instance.firstLessonStartTime,
      'teacherName': instance.teacherName,
      'teacherProfileUrl': instance.teacherProfileUrl,
      'icon': instance.icon,
      'template': instance.template,
      'pattern': instance.pattern,
      'addTeacherUrl': instance.addTeacherUrl,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'segmentName': instance.segmentName,
      'userCourseBusinessStatus': instance.userCourseBusinessStatus,
      'guideImage': instance.guideImage,
      'addButton': instance.addButton,
      'cancelButton': instance.cancelButton,
      'offlineBlockPopup': instance.offlineBlockPopup,
      'buttonText': instance.buttonText,
      'tags': instance.tags,
      'limitFlow': instance.limitFlow,
      'businessTypeDesc': instance.businessTypeDesc,
    };

_$_ScheduleInfo _$$_ScheduleInfoFromJson(Map<String, dynamic> json) =>
    _$_ScheduleInfo(
      subjectScheduleStatus: (json['subjectScheduleStatus'] as List<dynamic>?)
          ?.map(
              (e) => SubjectScheduleStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      showDateTime: json['showDateTime'] as int?,
      showDate: json['showDate'] as String?,
      today: json['today'] as bool?,
      subjectType: json['subjectType'] as int?,
      cardType: json['cardType'] as int?,
      classId: json['classId'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      cardTypeName: json['cardTypeName'] as String?,
      period: json['period'] as String?,
      order: json['order'] as int?,
      newGetSubjectTypeList: (json['newGetSubjectTypeList'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      scheduleTaskList: (json['scheduleTaskList'] as List<dynamic>?)
          ?.map((e) => ScheduleTaskBo.fromJson(e as Map<String, dynamic>))
          .toList(),
      medal: json['medal'] == null
          ? null
          : EnjoyTrainMedal.fromJson(json['medal'] as Map<String, dynamic>),
      classStatusName: json['classStatusName'] as String?,
      trialCourse: json['trialCourse'] as bool?,
    );

Map<String, dynamic> _$$_ScheduleInfoToJson(_$_ScheduleInfo instance) =>
    <String, dynamic>{
      'subjectScheduleStatus': instance.subjectScheduleStatus,
      'showDateTime': instance.showDateTime,
      'showDate': instance.showDate,
      'today': instance.today,
      'subjectType': instance.subjectType,
      'cardType': instance.cardType,
      'classId': instance.classId,
      'courseSegmentName': instance.courseSegmentName,
      'cardTypeName': instance.cardTypeName,
      'period': instance.period,
      'order': instance.order,
      'newGetSubjectTypeList': instance.newGetSubjectTypeList,
      'scheduleTaskList': instance.scheduleTaskList,
      'medal': instance.medal,
      'classStatusName': instance.classStatusName,
      'trialCourse': instance.trialCourse,
    };

_$_ScheduleTaskBo _$$_ScheduleTaskBoFromJson(Map<String, dynamic> json) =>
    _$_ScheduleTaskBo(
      taskStatus: json['taskStatus'] as int?,
      materialId: json['materialId'] as int?,
      sortLessonOrder: json['sortLessonOrder'] as int?,
      materialName: json['materialName'] as String?,
      configId: json['configId'] as int?,
      taskStatusDesc: json['taskStatusDesc'] as String?,
      showDateTime: json['showDateTime'] as int?,
      startClassTime: json['startClassTime'] as int?,
      firstLessonStartTime: json['firstLessonStartTime'] as int?,
      joinClassTime: json['joinClassTime'] as int?,
      taskType: json['taskType'] as int?,
      taskTypeDesc: json['taskTypeDesc'] as String?,
      classId: json['classId'] as int?,
      scheduleTaskId: json['scheduleTaskId'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      lessonId: json['lessonId'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      unlockType: json['unlockType'] as int?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      route: json['route'] as String?,
      icon: json['icon'] as String?,
      lessonLabel: json['lessonLabel'] as String?,
      lessonOrder: json['lessonOrder'] as String?,
      lessonSubDesc: json['lessonSubDesc'] as String?,
      lessonName: json['lessonName'] as String?,
      classStatus: json['classStatus'] as int?,
      bannerImage: json['bannerImage'] as String?,
      padBannerImage: json['padBannerImage'] as String?,
      bannerType: json['bannerType'] as int?,
      bannerNum: json['bannerNum'] as int?,
      lessonCoverImage: json['lessonCoverImage'] as String?,
      courseCoverImage: json['courseCoverImage'] as String?,
      image: json['image'] as String?,
      padImage: json['padImage'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      userCourseBusinessStatus: json['userCourseBusinessStatus'] as String?,
      lessonResource: json['lessonResource'] == null
          ? null
          : CourseHomePageLessonResourceData.fromJson(
              json['lessonResource'] as Map<String, dynamic>),
      courseResource: json['courseResource'] == null
          ? null
          : CourseHomePageCourseResourceData.fromJson(
              json['courseResource'] as Map<String, dynamic>),
      lessonServiceList: (json['lessonServiceList'] as List<dynamic>?)
          ?.map((e) => LessonServiceList.fromJson(e as Map<String, dynamic>))
          .toList(),
      trainScheduleTasks: (json['trainScheduleTasks'] as List<dynamic>?)
          ?.map((e) => ChildrenTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
      childrenTaskList: (json['childrenTaskList'] as List<dynamic>?)
          ?.map((e) => ChildrenTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonPopList: (json['lessonPopList'] as List<dynamic>?)
          ?.map((e) => LessonPopData.fromJson(e as Map<String, dynamic>))
          .toList(),
      trainToolkitVo: json['trainToolkitVo'] == null
          ? null
          : TrainTookitVo.fromJson(
              json['trainToolkitVo'] as Map<String, dynamic>),
      newGetFlag: json['newGetFlag'] as bool?,
      trainNewGetGuideList: (json['trainNewGetGuideList'] as List<dynamic>?)
          ?.map((e) => TrainCampTipData.fromJson(e as Map<String, dynamic>))
          .toList(),
      btnText: json['btnText'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      linkUrl: json['linkUrl'] as String?,
      missionMaterialType: json['missionMaterialType'] as String?,
      missionBusinessTypeName: json['missionBusinessTypeName'] as String?,
      expired: json['expired'] as bool?,
      description: json['description'] as String?,
      titleColor: json['titleColor'] as String?,
      descriptionColor: json['descriptionColor'] as String?,
      missionMaterialTypeName: json['missionMaterialTypeName'] as String?,
      missionTypeName: json['missionTypeName'] as String?,
      businessTagId: json['businessTagId'] as String?,
      subjectInfoList:
          (json['drainageCourseReceiveCardDataBo'] as List<dynamic>?)
              ?.map((e) => SubjectInfo.fromJson(e as Map<String, dynamic>))
              .toList(),
      gradeTitle: json['gradeTitle'] as String?,
      gradeKey: json['gradeKey'] as int?,
      lastDrainageCourseLessonTask:
          json['lastDrainageCourseLessonTask'] as bool?,
      courseInfo: json['courseInfo'] == null
          ? null
          : DrainageCourseCardInfo.fromJson(
              json['courseInfo'] as Map<String, dynamic>),
      needPop: json['needPop'] as bool?,
      showNew: json['showNew'] as bool?,
      advancedCourseInfo: json['advancedCourseInfo'] == null
          ? null
          : AdvancedCourseInfo.fromJson(
              json['advancedCourseInfo'] as Map<String, dynamic>),
      buttonDescription: json['buttonDescription'] as String?,
      landscapePictureUrl: json['landscapePictureUrl'] as String?,
      auditId: json['auditId'] as int?,
      courseLabel: json['courseLabel'] as String?,
      startLessonDesc: json['startLessonDesc'] as String?,
      bgImage: json['bgImage'] as String?,
      userCourseId: json['userCourseId'] as String?,
      newGetGuideBo: json['newGetGuideBo'] == null
          ? null
          : NewGetGuideInfo.fromJson(
              json['newGetGuideBo'] as Map<String, dynamic>),
      taskTagBo: json['taskTagBo'] == null
          ? null
          : TaskTagBo.fromJson(json['taskTagBo'] as Map<String, dynamic>),
      lessonCardData: json['lessonCardData'] == null
          ? null
          : LessonCardData.fromJson(
              json['lessonCardData'] as Map<String, dynamic>),
      unactivatedReason: json['unactivatedReason'] as String?,
      trackExtendInfoList: (json['trackExtendInfoList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$_ScheduleTaskBoToJson(_$_ScheduleTaskBo instance) =>
    <String, dynamic>{
      'taskStatus': instance.taskStatus,
      'materialId': instance.materialId,
      'sortLessonOrder': instance.sortLessonOrder,
      'materialName': instance.materialName,
      'configId': instance.configId,
      'taskStatusDesc': instance.taskStatusDesc,
      'showDateTime': instance.showDateTime,
      'startClassTime': instance.startClassTime,
      'firstLessonStartTime': instance.firstLessonStartTime,
      'joinClassTime': instance.joinClassTime,
      'taskType': instance.taskType,
      'taskTypeDesc': instance.taskTypeDesc,
      'classId': instance.classId,
      'scheduleTaskId': instance.scheduleTaskId,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'lessonId': instance.lessonId,
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'unlockType': instance.unlockType,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'route': instance.route,
      'icon': instance.icon,
      'lessonLabel': instance.lessonLabel,
      'lessonOrder': instance.lessonOrder,
      'lessonSubDesc': instance.lessonSubDesc,
      'lessonName': instance.lessonName,
      'classStatus': instance.classStatus,
      'bannerImage': instance.bannerImage,
      'padBannerImage': instance.padBannerImage,
      'bannerType': instance.bannerType,
      'bannerNum': instance.bannerNum,
      'lessonCoverImage': instance.lessonCoverImage,
      'courseCoverImage': instance.courseCoverImage,
      'image': instance.image,
      'padImage': instance.padImage,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'userCourseBusinessStatus': instance.userCourseBusinessStatus,
      'lessonResource': instance.lessonResource,
      'courseResource': instance.courseResource,
      'lessonServiceList': instance.lessonServiceList,
      'trainScheduleTasks': instance.trainScheduleTasks,
      'childrenTaskList': instance.childrenTaskList,
      'lessonPopList': instance.lessonPopList,
      'trainToolkitVo': instance.trainToolkitVo,
      'newGetFlag': instance.newGetFlag,
      'trainNewGetGuideList': instance.trainNewGetGuideList,
      'btnText': instance.btnText,
      'pictureUrl': instance.pictureUrl,
      'backgroundColor': instance.backgroundColor,
      'linkUrl': instance.linkUrl,
      'missionMaterialType': instance.missionMaterialType,
      'missionBusinessTypeName': instance.missionBusinessTypeName,
      'expired': instance.expired,
      'description': instance.description,
      'titleColor': instance.titleColor,
      'descriptionColor': instance.descriptionColor,
      'missionMaterialTypeName': instance.missionMaterialTypeName,
      'missionTypeName': instance.missionTypeName,
      'businessTagId': instance.businessTagId,
      'drainageCourseReceiveCardDataBo': instance.subjectInfoList,
      'gradeTitle': instance.gradeTitle,
      'gradeKey': instance.gradeKey,
      'lastDrainageCourseLessonTask': instance.lastDrainageCourseLessonTask,
      'courseInfo': instance.courseInfo,
      'needPop': instance.needPop,
      'showNew': instance.showNew,
      'advancedCourseInfo': instance.advancedCourseInfo,
      'buttonDescription': instance.buttonDescription,
      'landscapePictureUrl': instance.landscapePictureUrl,
      'auditId': instance.auditId,
      'courseLabel': instance.courseLabel,
      'startLessonDesc': instance.startLessonDesc,
      'bgImage': instance.bgImage,
      'userCourseId': instance.userCourseId,
      'newGetGuideBo': instance.newGetGuideBo,
      'taskTagBo': instance.taskTagBo,
      'lessonCardData': instance.lessonCardData,
      'unactivatedReason': instance.unactivatedReason,
      'trackExtendInfoList': instance.trackExtendInfoList,
    };

_$_LessonPopData _$$_LessonPopDataFromJson(Map<String, dynamic> json) =>
    _$_LessonPopData(
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      buttonDesc: json['buttonDesc'] as String?,
      type: json['type'] as int?,
    );

Map<String, dynamic> _$$_LessonPopDataToJson(_$_LessonPopData instance) =>
    <String, dynamic>{
      'title': instance.title,
      'subTitle': instance.subTitle,
      'icon': instance.icon,
      'route': instance.route,
      'buttonDesc': instance.buttonDesc,
      'type': instance.type,
    };

_$_DrainageCourseCardInfo _$$_DrainageCourseCardInfoFromJson(
        Map<String, dynamic> json) =>
    _$_DrainageCourseCardInfo(
      courseSegmentDesc: json['courseSegmentDesc'] as String?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
      countdownDeadlineTime: json['countdownDeadlineTime'] as int?,
      courseDesc: json['courseDesc'] as String?,
      courseId: json['courseId'] as String?,
      courseTag: json['courseTag'] as String?,
      courseTagKey: json['courseTagKey'] as int?,
      courseType: json['courseType'] as int?,
      courseTypeDesc: json['courseTypeDesc'] as String?,
      courseStageDesc: json['courseStageDesc'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      lessonInfoList: (json['lessonInfoList'] as List<dynamic>?)
          ?.map((e) =>
              DrainageCourseLessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_DrainageCourseCardInfoToJson(
        _$_DrainageCourseCardInfo instance) =>
    <String, dynamic>{
      'courseSegmentDesc': instance.courseSegmentDesc,
      'courseSegmentCode': instance.courseSegmentCode,
      'countdownDeadlineTime': instance.countdownDeadlineTime,
      'courseDesc': instance.courseDesc,
      'courseId': instance.courseId,
      'courseTag': instance.courseTag,
      'courseTagKey': instance.courseTagKey,
      'courseType': instance.courseType,
      'courseTypeDesc': instance.courseTypeDesc,
      'courseStageDesc': instance.courseStageDesc,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'lessonInfoList': instance.lessonInfoList,
    };

_$_DrainageCourseLessonInfo _$$_DrainageCourseLessonInfoFromJson(
        Map<String, dynamic> json) =>
    _$_DrainageCourseLessonInfo(
      coverPicUrl: json['coverPicUrl'] as String?,
      lessonId: json['lessonId'] as String?,
      lessonOrderDesc: json['lessonOrderDesc'] as String?,
      lessonName: json['lessonName'] as String?,
      stepInfoList: (json['stepInfoList'] as List<dynamic>?)
          ?.map((e) => StepInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_DrainageCourseLessonInfoToJson(
        _$_DrainageCourseLessonInfo instance) =>
    <String, dynamic>{
      'coverPicUrl': instance.coverPicUrl,
      'lessonId': instance.lessonId,
      'lessonOrderDesc': instance.lessonOrderDesc,
      'lessonName': instance.lessonName,
      'stepInfoList': instance.stepInfoList,
    };

_$_StepInfo _$$_StepInfoFromJson(Map<String, dynamic> json) => _$_StepInfo(
      stepId: json['stepId'] as String?,
      lessonStepIcon: json['lessonStepIcon'] as String?,
      lessonStepName: json['lessonStepName'] as String?,
      finishStatus: json['finishStatus'] as int?,
      stepRouter: json['stepRouter'] as String?,
      classId: json['classId'] as String?,
      lessonId: json['lessonId'] as String?,
      stepType: json['stepType'] as int?,
      surplusStepTypes: json['surplusStepTypes'] as String?,
    );

Map<String, dynamic> _$$_StepInfoToJson(_$_StepInfo instance) =>
    <String, dynamic>{
      'stepId': instance.stepId,
      'lessonStepIcon': instance.lessonStepIcon,
      'lessonStepName': instance.lessonStepName,
      'finishStatus': instance.finishStatus,
      'stepRouter': instance.stepRouter,
      'classId': instance.classId,
      'lessonId': instance.lessonId,
      'stepType': instance.stepType,
      'surplusStepTypes': instance.surplusStepTypes,
    };

_$_AdvancedCourseInfo _$$_AdvancedCourseInfoFromJson(
        Map<String, dynamic> json) =>
    _$_AdvancedCourseInfo(
      title: json['title'] as String?,
      tip: json['tip'] as String?,
      subTitle: json['subTitle'] as String?,
      courseId: json['courseId'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      lessonInfoList: (json['lessonInfoList'] as List<dynamic>?)
          ?.map((e) =>
              DrainageCourseLessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      btnInfoList: (json['btnInfoList'] as List<dynamic>?)
          ?.map((e) => DrainageBtnInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AdvancedCourseInfoToJson(
        _$_AdvancedCourseInfo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'tip': instance.tip,
      'subTitle': instance.subTitle,
      'courseId': instance.courseId,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'lessonInfoList': instance.lessonInfoList,
      'btnInfoList': instance.btnInfoList,
    };

_$_DrainageBtnInfo _$$_DrainageBtnInfoFromJson(Map<String, dynamic> json) =>
    _$_DrainageBtnInfo(
      type: json['type'] as int?,
      content: json['content'] as String?,
      bubbleText: json['bubbleText'] as String?,
      router: json['router'] as String?,
    );

Map<String, dynamic> _$$_DrainageBtnInfoToJson(_$_DrainageBtnInfo instance) =>
    <String, dynamic>{
      'type': instance.type,
      'content': instance.content,
      'bubbleText': instance.bubbleText,
      'router': instance.router,
    };

_$_ChildrenTaskList _$$_ChildrenTaskListFromJson(Map<String, dynamic> json) =>
    _$_ChildrenTaskList(
      taskStatus: json['taskStatus'] as int?,
      showDateTime: json['showDateTime'] as int?,
      startClassTime: json['startClassTime'] as int?,
      firstLessonStartTime: json['firstLessonStartTime'] as int?,
      taskType: json['taskType'] as int?,
      taskTypeDesc: json['taskTypeDesc'] as String?,
      simpleTitle: json['simpleTitle'] as String?,
      scheduleTaskId: json['scheduleTaskId'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      subjectType: json['subjectType'] as int?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      btnText: json['btnText'] as String?,
      buttonDesc: json['buttonDesc'] as String?,
      userCourseBusinessStatus: json['userCourseBusinessStatus'] as String?,
      taskExtension: json['taskExtension'] == null
          ? null
          : CourseHomeTaskExtensionData.fromJson(
              json['taskExtension'] as Map<String, dynamic>),
      lessonResource: json['lessonResource'] == null
          ? null
          : CourseHomePageLessonResourceData.fromJson(
              json['lessonResource'] as Map<String, dynamic>),
      courseResource: json['courseResource'] == null
          ? null
          : CourseHomePageCourseResourceData.fromJson(
              json['courseResource'] as Map<String, dynamic>),
      childrenTaskList: json['childrenTaskList'] as List<dynamic>?,
      tip: json['tip'] as String?,
      showTip: json['showTip'] as bool?,
      contactTeacherInfo: json['contactTeacherInfo'] == null
          ? null
          : ContactTeacherInfo.fromJson(
              json['contactTeacherInfo'] as Map<String, dynamic>),
      parentVerify: json['parentVerify'] as int?,
    );

Map<String, dynamic> _$$_ChildrenTaskListToJson(_$_ChildrenTaskList instance) =>
    <String, dynamic>{
      'taskStatus': instance.taskStatus,
      'showDateTime': instance.showDateTime,
      'startClassTime': instance.startClassTime,
      'firstLessonStartTime': instance.firstLessonStartTime,
      'taskType': instance.taskType,
      'taskTypeDesc': instance.taskTypeDesc,
      'simpleTitle': instance.simpleTitle,
      'scheduleTaskId': instance.scheduleTaskId,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'subjectType': instance.subjectType,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'icon': instance.icon,
      'route': instance.route,
      'btnText': instance.btnText,
      'buttonDesc': instance.buttonDesc,
      'userCourseBusinessStatus': instance.userCourseBusinessStatus,
      'taskExtension': instance.taskExtension,
      'lessonResource': instance.lessonResource,
      'courseResource': instance.courseResource,
      'childrenTaskList': instance.childrenTaskList,
      'tip': instance.tip,
      'showTip': instance.showTip,
      'contactTeacherInfo': instance.contactTeacherInfo,
      'parentVerify': instance.parentVerify,
    };

_$_LessonServiceList _$$_LessonServiceListFromJson(Map<String, dynamic> json) =>
    _$_LessonServiceList(
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      redPoint: json['redPoint'] as bool?,
      route: json['route'] as String?,
      key: json['key'] as String?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      button: json['button'] as String?,
      order: json['order'] as int?,
      desc: json['desc'] as String?,
      parentVerify: json['parentVerify'] as int?,
      toast: json['toast'] as String?,
      taskStatus: json['taskStatus'] as int?,
      serviceExtra: json['serviceExtra'] == null
          ? null
          : CourseHomeTaskExtensionData.fromJson(
              json['serviceExtra'] as Map<String, dynamic>),
      serviceUpdateBubbleList:
          (json['serviceUpdateBubbleList'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      popupInfo: json['popupInfo'] == null
          ? null
          : PopInfo.fromJson(json['popupInfo'] as Map<String, dynamic>),
      gifIcon: json['gifIcon'] as String?,
      playTime: json['playTime'] as int?,
      gifIconName: json['gifIconName'] as String?,
    );

Map<String, dynamic> _$$_LessonServiceListToJson(
        _$_LessonServiceList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'name': instance.name,
      'redPoint': instance.redPoint,
      'route': instance.route,
      'key': instance.key,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'button': instance.button,
      'order': instance.order,
      'desc': instance.desc,
      'parentVerify': instance.parentVerify,
      'toast': instance.toast,
      'taskStatus': instance.taskStatus,
      'serviceExtra': instance.serviceExtra,
      'serviceUpdateBubbleList': instance.serviceUpdateBubbleList,
      'popupInfo': instance.popupInfo,
      'gifIcon': instance.gifIcon,
      'playTime': instance.playTime,
      'gifIconName': instance.gifIconName,
    };

_$_SubjectScheduleStatus _$$_SubjectScheduleStatusFromJson(
        Map<String, dynamic> json) =>
    _$_SubjectScheduleStatus(
      subjectType: json['subjectType'] as int?,
      scheduleStatus: json['scheduleStatus'] as int?,
      monthKeyStatus: json['monthKeyStatus'] as int?,
    );

Map<String, dynamic> _$$_SubjectScheduleStatusToJson(
        _$_SubjectScheduleStatus instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'scheduleStatus': instance.scheduleStatus,
      'monthKeyStatus': instance.monthKeyStatus,
    };

_$_SubjectTypeVo _$$_SubjectTypeVoFromJson(Map<String, dynamic> json) =>
    _$_SubjectTypeVo(
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      isPosition: json['isPosition'] as bool?,
      subjectNewlyObtained: json['subjectNewlyObtained'] as bool?,
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_SubjectTypeVoToJson(_$_SubjectTypeVo instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'isPosition': instance.isPosition,
      'subjectNewlyObtained': instance.subjectNewlyObtained,
      'classId': instance.classId,
    };

_$_TrainTookitVo _$$_TrainTookitVoFromJson(Map<String, dynamic> json) =>
    _$_TrainTookitVo(
      background: json['background'] as String?,
      title: json['title'] as String?,
      desc: json['desc'] as String?,
      toolkitServiceList: (json['toolkitServiceList'] as List<dynamic>?)
          ?.map((e) => ToolkitServiceBo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TrainTookitVoToJson(_$_TrainTookitVo instance) =>
    <String, dynamic>{
      'background': instance.background,
      'title': instance.title,
      'desc': instance.desc,
      'toolkitServiceList': instance.toolkitServiceList,
    };

_$_ToolkitServiceBo _$$_ToolkitServiceBoFromJson(Map<String, dynamic> json) =>
    _$_ToolkitServiceBo(
      type: json['type'] as int?,
      order: json['order'] as int?,
      icon: json['icon'] as String?,
      title: json['title'] as String?,
      desc: json['desc'] as String?,
      route: json['route'] as String?,
      button: json['button'] as String?,
      toast: json['toast'] as String?,
      redDot: json['redDot'] as int?,
      parentVerify: json['parentVerify'] as int?,
    );

Map<String, dynamic> _$$_ToolkitServiceBoToJson(_$_ToolkitServiceBo instance) =>
    <String, dynamic>{
      'type': instance.type,
      'order': instance.order,
      'icon': instance.icon,
      'title': instance.title,
      'desc': instance.desc,
      'route': instance.route,
      'button': instance.button,
      'toast': instance.toast,
      'redDot': instance.redDot,
      'parentVerify': instance.parentVerify,
    };

_$_ContactTeacherInfo _$$_ContactTeacherInfoFromJson(
        Map<String, dynamic> json) =>
    _$_ContactTeacherInfo(
      nickname: json['nickname'] as String?,
      text: json['text'] as String?,
      profileUrl: json['profileUrl'] as String?,
      contactRoute: json['contactRoute'] as String?,
      addButton: json['addButton'] as String?,
      cancelButton: json['cancelButton'] as String?,
    );

Map<String, dynamic> _$$_ContactTeacherInfoToJson(
        _$_ContactTeacherInfo instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'text': instance.text,
      'profileUrl': instance.profileUrl,
      'contactRoute': instance.contactRoute,
      'addButton': instance.addButton,
      'cancelButton': instance.cancelButton,
    };

_$_PopInfo _$$_PopInfoFromJson(Map<String, dynamic> json) => _$_PopInfo(
      popupTitle: json['popupTitle'] as String?,
      introduceImageList: (json['introduceImageList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      buttonRoute: json['buttonRoute'] as String?,
      buttonText: json['buttonText'] as String?,
      closedText: json['closedText'] as String?,
    );

Map<String, dynamic> _$$_PopInfoToJson(_$_PopInfo instance) =>
    <String, dynamic>{
      'popupTitle': instance.popupTitle,
      'introduceImageList': instance.introduceImageList,
      'buttonRoute': instance.buttonRoute,
      'buttonText': instance.buttonText,
      'closedText': instance.closedText,
    };

_$_UserGifCourse _$$_UserGifCourseFromJson(Map<String, dynamic> json) =>
    _$_UserGifCourse(
      parentUserCourseId: json['parentUserCourseId'] as String?,
      classId: json['classId'] as int?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      courseCoverImage: json['courseCoverImage'] as String?,
      activateStatus: json['activateStatus'] as int?,
      unactivatedReason: json['unactivatedReason'] as String?,
      buttonText: json['buttonText'] as String?,
      bgImage: json['bgImage'] as String?,
      courseLabel: json['courseLabel'] as String?,
      lessonId: json['lessonId'] as int?,
      subjectType: json['subjectType'] as int?,
      taskStatus: json['taskStatus'] as int?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_UserGifCourseToJson(_$_UserGifCourse instance) =>
    <String, dynamic>{
      'parentUserCourseId': instance.parentUserCourseId,
      'classId': instance.classId,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'courseCoverImage': instance.courseCoverImage,
      'activateStatus': instance.activateStatus,
      'unactivatedReason': instance.unactivatedReason,
      'buttonText': instance.buttonText,
      'bgImage': instance.bgImage,
      'courseLabel': instance.courseLabel,
      'lessonId': instance.lessonId,
      'subjectType': instance.subjectType,
      'taskStatus': instance.taskStatus,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'route': instance.route,
    };

_$_NewGetGuideInfo _$$_NewGetGuideInfoFromJson(Map<String, dynamic> json) =>
    _$_NewGetGuideInfo(
      content: json['content'] as String?,
      newGetGuideVoice: json['newGetGuideVoice'] as String?,
    );

Map<String, dynamic> _$$_NewGetGuideInfoToJson(_$_NewGetGuideInfo instance) =>
    <String, dynamic>{
      'content': instance.content,
      'newGetGuideVoice': instance.newGetGuideVoice,
    };
