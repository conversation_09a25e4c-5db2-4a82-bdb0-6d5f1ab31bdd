// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_home_page_year_month_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseHomePageYearMonthData _$$_CourseHomePageYearMonthDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseHomePageYearMonthData(
      monthScheduleInfo: (json['monthScheduleInfo'] as List<dynamic>?)
          ?.map((e) => MonthScheduleInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonSummary: json['lessonSummary'] == null
          ? null
          : LessonSummary.fromJson(
              json['lessonSummary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseHomePageYearMonthDataToJson(
        _$_CourseHomePageYearMonthData instance) =>
    <String, dynamic>{
      'monthScheduleInfo': instance.monthScheduleInfo,
      'lessonSummary': instance.lessonSummary,
    };

_$_LessonSummary _$$_LessonSummaryFromJson(Map<String, dynamic> json) =>
    _$_LessonSummary(
      unFinishedNum: json['unFinishedNum'] as String?,
      finishedNum: json['finishedNum'] as String?,
    );

Map<String, dynamic> _$$_LessonSummaryToJson(_$_LessonSummary instance) =>
    <String, dynamic>{
      'unFinishedNum': instance.unFinishedNum,
      'finishedNum': instance.finishedNum,
    };

_$_MonthScheduleInfo _$$_MonthScheduleInfoFromJson(Map<String, dynamic> json) =>
    _$_MonthScheduleInfo(
      month: json['month'] as String?,
      monthNum: json['monthNum'] as int?,
      scheduleInfo: (json['scheduleInfo'] as List<dynamic>?)
          ?.map((e) => ScheduleInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MonthScheduleInfoToJson(
        _$_MonthScheduleInfo instance) =>
    <String, dynamic>{
      'month': instance.month,
      'monthNum': instance.monthNum,
      'scheduleInfo': instance.scheduleInfo,
    };

_$_ScheduleInfo _$$_ScheduleInfoFromJson(Map<String, dynamic> json) =>
    _$_ScheduleInfo(
      subjectScheduleStatus: (json['subjectScheduleStatus'] as List<dynamic>?)
          ?.map(
              (e) => SubjectScheduleStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      showDateTime: json['showDateTime'] as int?,
      today: json['today'] as bool?,
      scheduleTaskList: (json['scheduleTaskList'] as List<dynamic>?)
          ?.map((e) => ScheduleTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ScheduleInfoToJson(_$_ScheduleInfo instance) =>
    <String, dynamic>{
      'subjectScheduleStatus': instance.subjectScheduleStatus,
      'showDateTime': instance.showDateTime,
      'today': instance.today,
      'scheduleTaskList': instance.scheduleTaskList,
    };

_$_ScheduleTaskList _$$_ScheduleTaskListFromJson(Map<String, dynamic> json) =>
    _$_ScheduleTaskList(
      taskStatus: json['taskStatus'] as int?,
      showDateTime: json['showDateTime'] as int?,
      taskType: json['taskType'] as int?,
      classId: json['classId'] as int?,
      scheduleTaskId: json['scheduleTaskId'] as int?,
      courseId: json['courseId'] as int?,
      lessonId: json['lessonId'] as int?,
      subjectType: json['subjectType'] as int?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      route: json['route'],
      icon: json['icon'] as String?,
      lessonLabel: json['lessonLabel'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonCoverImage: json['lessonCoverImage'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      lessonServiceList: (json['lessonServiceList'] as List<dynamic>?)
          ?.map((e) => LessonServiceList.fromJson(e as Map<String, dynamic>))
          .toList(),
      childrenTaskList: (json['childrenTaskList'] as List<dynamic>?)
          ?.map((e) => ChildrenTaskList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ScheduleTaskListToJson(_$_ScheduleTaskList instance) =>
    <String, dynamic>{
      'taskStatus': instance.taskStatus,
      'showDateTime': instance.showDateTime,
      'taskType': instance.taskType,
      'classId': instance.classId,
      'scheduleTaskId': instance.scheduleTaskId,
      'courseId': instance.courseId,
      'lessonId': instance.lessonId,
      'subjectType': instance.subjectType,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'route': instance.route,
      'icon': instance.icon,
      'lessonLabel': instance.lessonLabel,
      'lessonName': instance.lessonName,
      'lessonCoverImage': instance.lessonCoverImage,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'lessonServiceList': instance.lessonServiceList,
      'childrenTaskList': instance.childrenTaskList,
    };

_$_ChildrenTaskList _$$_ChildrenTaskListFromJson(Map<String, dynamic> json) =>
    _$_ChildrenTaskList(
      taskStatus: json['taskStatus'] as int?,
      showDateTime: json['showDateTime'] as int?,
      taskType: json['taskType'] as int?,
      scheduleTaskId: json['scheduleTaskId'] as int?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      subjectType: json['subjectType'] as int?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      childrenTaskList: json['childrenTaskList'] as List<dynamic>?,
    );

Map<String, dynamic> _$$_ChildrenTaskListToJson(_$_ChildrenTaskList instance) =>
    <String, dynamic>{
      'taskStatus': instance.taskStatus,
      'showDateTime': instance.showDateTime,
      'taskType': instance.taskType,
      'scheduleTaskId': instance.scheduleTaskId,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'subjectType': instance.subjectType,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'icon': instance.icon,
      'route': instance.route,
      'childrenTaskList': instance.childrenTaskList,
    };

_$_LessonServiceList _$$_LessonServiceListFromJson(Map<String, dynamic> json) =>
    _$_LessonServiceList(
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      redPoint: json['redPoint'] as bool?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_LessonServiceListToJson(
        _$_LessonServiceList instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'name': instance.name,
      'redPoint': instance.redPoint,
      'route': instance.route,
    };

_$_SubjectScheduleStatus _$$_SubjectScheduleStatusFromJson(
        Map<String, dynamic> json) =>
    _$_SubjectScheduleStatus(
      subjectType: json['subjectType'] as String?,
      scheduleStatus: json['scheduleStatus'] as String?,
    );

Map<String, dynamic> _$$_SubjectScheduleStatusToJson(
        _$_SubjectScheduleStatus instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'scheduleStatus': instance.scheduleStatus,
    };
