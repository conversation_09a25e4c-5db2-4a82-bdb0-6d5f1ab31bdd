// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_home_page_lesson_resource_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseHomePageLessonResourceData _$CourseHomePageLessonResourceDataFromJson(
    Map<String, dynamic> json) {
  return _CourseHomePageLessonResourceData.fromJson(json);
}

/// @nodoc
mixin _$CourseHomePageLessonResourceData {
  String? get lessonGuideCoverUrl => throw _privateConstructorUsedError;
  String? get lessonGuideVideoUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  String? get courseTypeDesc => throw _privateConstructorUsedError;
  String? get courseStageDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseHomePageLessonResourceDataCopyWith<CourseHomePageLessonResourceData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseHomePageLessonResourceDataCopyWith<$Res> {
  factory $CourseHomePageLessonResourceDataCopyWith(
          CourseHomePageLessonResourceData value,
          $Res Function(CourseHomePageLessonResourceData) then) =
      _$CourseHomePageLessonResourceDataCopyWithImpl<$Res,
          CourseHomePageLessonResourceData>;
  @useResult
  $Res call(
      {String? lessonGuideCoverUrl,
      String? lessonGuideVideoUrl,
      String? description,
      String? skuId,
      String? skuName,
      String? courseTypeDesc,
      String? courseStageDesc});
}

/// @nodoc
class _$CourseHomePageLessonResourceDataCopyWithImpl<$Res,
        $Val extends CourseHomePageLessonResourceData>
    implements $CourseHomePageLessonResourceDataCopyWith<$Res> {
  _$CourseHomePageLessonResourceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonGuideCoverUrl = freezed,
    Object? lessonGuideVideoUrl = freezed,
    Object? description = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
  }) {
    return _then(_value.copyWith(
      lessonGuideCoverUrl: freezed == lessonGuideCoverUrl
          ? _value.lessonGuideCoverUrl
          : lessonGuideCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGuideVideoUrl: freezed == lessonGuideVideoUrl
          ? _value.lessonGuideVideoUrl
          : lessonGuideVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseHomePageLessonResourceDataCopyWith<$Res>
    implements $CourseHomePageLessonResourceDataCopyWith<$Res> {
  factory _$$_CourseHomePageLessonResourceDataCopyWith(
          _$_CourseHomePageLessonResourceData value,
          $Res Function(_$_CourseHomePageLessonResourceData) then) =
      __$$_CourseHomePageLessonResourceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lessonGuideCoverUrl,
      String? lessonGuideVideoUrl,
      String? description,
      String? skuId,
      String? skuName,
      String? courseTypeDesc,
      String? courseStageDesc});
}

/// @nodoc
class __$$_CourseHomePageLessonResourceDataCopyWithImpl<$Res>
    extends _$CourseHomePageLessonResourceDataCopyWithImpl<$Res,
        _$_CourseHomePageLessonResourceData>
    implements _$$_CourseHomePageLessonResourceDataCopyWith<$Res> {
  __$$_CourseHomePageLessonResourceDataCopyWithImpl(
      _$_CourseHomePageLessonResourceData _value,
      $Res Function(_$_CourseHomePageLessonResourceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonGuideCoverUrl = freezed,
    Object? lessonGuideVideoUrl = freezed,
    Object? description = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? courseTypeDesc = freezed,
    Object? courseStageDesc = freezed,
  }) {
    return _then(_$_CourseHomePageLessonResourceData(
      lessonGuideCoverUrl: freezed == lessonGuideCoverUrl
          ? _value.lessonGuideCoverUrl
          : lessonGuideCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGuideVideoUrl: freezed == lessonGuideVideoUrl
          ? _value.lessonGuideVideoUrl
          : lessonGuideVideoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTypeDesc: freezed == courseTypeDesc
          ? _value.courseTypeDesc
          : courseTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStageDesc: freezed == courseStageDesc
          ? _value.courseStageDesc
          : courseStageDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseHomePageLessonResourceData
    implements _CourseHomePageLessonResourceData {
  const _$_CourseHomePageLessonResourceData(
      {this.lessonGuideCoverUrl,
      this.lessonGuideVideoUrl,
      this.description,
      this.skuId,
      this.skuName,
      this.courseTypeDesc,
      this.courseStageDesc});

  factory _$_CourseHomePageLessonResourceData.fromJson(
          Map<String, dynamic> json) =>
      _$$_CourseHomePageLessonResourceDataFromJson(json);

  @override
  final String? lessonGuideCoverUrl;
  @override
  final String? lessonGuideVideoUrl;
  @override
  final String? description;
  @override
  final String? skuId;
  @override
  final String? skuName;
  @override
  final String? courseTypeDesc;
  @override
  final String? courseStageDesc;

  @override
  String toString() {
    return 'CourseHomePageLessonResourceData(lessonGuideCoverUrl: $lessonGuideCoverUrl, lessonGuideVideoUrl: $lessonGuideVideoUrl, description: $description, skuId: $skuId, skuName: $skuName, courseTypeDesc: $courseTypeDesc, courseStageDesc: $courseStageDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseHomePageLessonResourceData &&
            (identical(other.lessonGuideCoverUrl, lessonGuideCoverUrl) ||
                other.lessonGuideCoverUrl == lessonGuideCoverUrl) &&
            (identical(other.lessonGuideVideoUrl, lessonGuideVideoUrl) ||
                other.lessonGuideVideoUrl == lessonGuideVideoUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.courseTypeDesc, courseTypeDesc) ||
                other.courseTypeDesc == courseTypeDesc) &&
            (identical(other.courseStageDesc, courseStageDesc) ||
                other.courseStageDesc == courseStageDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      lessonGuideCoverUrl,
      lessonGuideVideoUrl,
      description,
      skuId,
      skuName,
      courseTypeDesc,
      courseStageDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseHomePageLessonResourceDataCopyWith<
          _$_CourseHomePageLessonResourceData>
      get copyWith => __$$_CourseHomePageLessonResourceDataCopyWithImpl<
          _$_CourseHomePageLessonResourceData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseHomePageLessonResourceDataToJson(
      this,
    );
  }
}

abstract class _CourseHomePageLessonResourceData
    implements CourseHomePageLessonResourceData {
  const factory _CourseHomePageLessonResourceData(
      {final String? lessonGuideCoverUrl,
      final String? lessonGuideVideoUrl,
      final String? description,
      final String? skuId,
      final String? skuName,
      final String? courseTypeDesc,
      final String? courseStageDesc}) = _$_CourseHomePageLessonResourceData;

  factory _CourseHomePageLessonResourceData.fromJson(
      Map<String, dynamic> json) = _$_CourseHomePageLessonResourceData.fromJson;

  @override
  String? get lessonGuideCoverUrl;
  @override
  String? get lessonGuideVideoUrl;
  @override
  String? get description;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  String? get courseTypeDesc;
  @override
  String? get courseStageDesc;
  @override
  @JsonKey(ignore: true)
  _$$_CourseHomePageLessonResourceDataCopyWith<
          _$_CourseHomePageLessonResourceData>
      get copyWith => throw _privateConstructorUsedError;
}
