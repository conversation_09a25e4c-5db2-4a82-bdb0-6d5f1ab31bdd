// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_step_study_route_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonStepStudyRouteData _$LessonStepStudyRouteDataFromJson(
    Map<String, dynamic> json) {
  return _LessonStepStudyRouteData.fromJson(json);
}

/// @nodoc
mixin _$LessonStepStudyRouteData {
  String? get studyRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonStepStudyRouteDataCopyWith<LessonStepStudyRouteData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonStepStudyRouteDataCopyWith<$Res> {
  factory $LessonStepStudyRouteDataCopyWith(LessonStepStudyRouteData value,
          $Res Function(LessonStepStudyRouteData) then) =
      _$LessonStepStudyRouteDataCopyWithImpl<$Res, LessonStepStudyRouteData>;
  @useResult
  $Res call({String? studyRoute});
}

/// @nodoc
class _$LessonStepStudyRouteDataCopyWithImpl<$Res,
        $Val extends LessonStepStudyRouteData>
    implements $LessonStepStudyRouteDataCopyWith<$Res> {
  _$LessonStepStudyRouteDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? studyRoute = freezed,
  }) {
    return _then(_value.copyWith(
      studyRoute: freezed == studyRoute
          ? _value.studyRoute
          : studyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonStepStudyRouteDataCopyWith<$Res>
    implements $LessonStepStudyRouteDataCopyWith<$Res> {
  factory _$$_LessonStepStudyRouteDataCopyWith(
          _$_LessonStepStudyRouteData value,
          $Res Function(_$_LessonStepStudyRouteData) then) =
      __$$_LessonStepStudyRouteDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? studyRoute});
}

/// @nodoc
class __$$_LessonStepStudyRouteDataCopyWithImpl<$Res>
    extends _$LessonStepStudyRouteDataCopyWithImpl<$Res,
        _$_LessonStepStudyRouteData>
    implements _$$_LessonStepStudyRouteDataCopyWith<$Res> {
  __$$_LessonStepStudyRouteDataCopyWithImpl(_$_LessonStepStudyRouteData _value,
      $Res Function(_$_LessonStepStudyRouteData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? studyRoute = freezed,
  }) {
    return _then(_$_LessonStepStudyRouteData(
      studyRoute: freezed == studyRoute
          ? _value.studyRoute
          : studyRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonStepStudyRouteData implements _LessonStepStudyRouteData {
  const _$_LessonStepStudyRouteData({this.studyRoute});

  factory _$_LessonStepStudyRouteData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonStepStudyRouteDataFromJson(json);

  @override
  final String? studyRoute;

  @override
  String toString() {
    return 'LessonStepStudyRouteData(studyRoute: $studyRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonStepStudyRouteData &&
            (identical(other.studyRoute, studyRoute) ||
                other.studyRoute == studyRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, studyRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonStepStudyRouteDataCopyWith<_$_LessonStepStudyRouteData>
      get copyWith => __$$_LessonStepStudyRouteDataCopyWithImpl<
          _$_LessonStepStudyRouteData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonStepStudyRouteDataToJson(
      this,
    );
  }
}

abstract class _LessonStepStudyRouteData implements LessonStepStudyRouteData {
  const factory _LessonStepStudyRouteData({final String? studyRoute}) =
      _$_LessonStepStudyRouteData;

  factory _LessonStepStudyRouteData.fromJson(Map<String, dynamic> json) =
      _$_LessonStepStudyRouteData.fromJson;

  @override
  String? get studyRoute;
  @override
  @JsonKey(ignore: true)
  _$$_LessonStepStudyRouteDataCopyWith<_$_LessonStepStudyRouteData>
      get copyWith => throw _privateConstructorUsedError;
}
