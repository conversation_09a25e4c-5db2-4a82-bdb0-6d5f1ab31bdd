// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'expand_subject_banner_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ExpandSubjectBannerInfo _$ExpandSubjectBannerInfoFromJson(
    Map<String, dynamic> json) {
  return _ExpandSubjectBannerInfo.fromJson(json);
}

/// @nodoc
mixin _$ExpandSubjectBannerInfo {
  @JsonKey(name: 'recommendList')
  List<RecommendBannerInfo>? get recommendBannerList =>
      throw _privateConstructorUsedError;
  List<GrayList>? get grayList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExpandSubjectBannerInfoCopyWith<ExpandSubjectBannerInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExpandSubjectBannerInfoCopyWith<$Res> {
  factory $ExpandSubjectBannerInfoCopyWith(ExpandSubjectBannerInfo value,
          $Res Function(ExpandSubjectBannerInfo) then) =
      _$ExpandSubjectBannerInfoCopyWithImpl<$Res, ExpandSubjectBannerInfo>;
  @useResult
  $Res call(
      {@JsonKey(name: 'recommendList')
      List<RecommendBannerInfo>? recommendBannerList,
      List<GrayList>? grayList});
}

/// @nodoc
class _$ExpandSubjectBannerInfoCopyWithImpl<$Res,
        $Val extends ExpandSubjectBannerInfo>
    implements $ExpandSubjectBannerInfoCopyWith<$Res> {
  _$ExpandSubjectBannerInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recommendBannerList = freezed,
    Object? grayList = freezed,
  }) {
    return _then(_value.copyWith(
      recommendBannerList: freezed == recommendBannerList
          ? _value.recommendBannerList
          : recommendBannerList // ignore: cast_nullable_to_non_nullable
              as List<RecommendBannerInfo>?,
      grayList: freezed == grayList
          ? _value.grayList
          : grayList // ignore: cast_nullable_to_non_nullable
              as List<GrayList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExpandSubjectBannerInfoCopyWith<$Res>
    implements $ExpandSubjectBannerInfoCopyWith<$Res> {
  factory _$$_ExpandSubjectBannerInfoCopyWith(_$_ExpandSubjectBannerInfo value,
          $Res Function(_$_ExpandSubjectBannerInfo) then) =
      __$$_ExpandSubjectBannerInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'recommendList')
      List<RecommendBannerInfo>? recommendBannerList,
      List<GrayList>? grayList});
}

/// @nodoc
class __$$_ExpandSubjectBannerInfoCopyWithImpl<$Res>
    extends _$ExpandSubjectBannerInfoCopyWithImpl<$Res,
        _$_ExpandSubjectBannerInfo>
    implements _$$_ExpandSubjectBannerInfoCopyWith<$Res> {
  __$$_ExpandSubjectBannerInfoCopyWithImpl(_$_ExpandSubjectBannerInfo _value,
      $Res Function(_$_ExpandSubjectBannerInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recommendBannerList = freezed,
    Object? grayList = freezed,
  }) {
    return _then(_$_ExpandSubjectBannerInfo(
      recommendBannerList: freezed == recommendBannerList
          ? _value._recommendBannerList
          : recommendBannerList // ignore: cast_nullable_to_non_nullable
              as List<RecommendBannerInfo>?,
      grayList: freezed == grayList
          ? _value._grayList
          : grayList // ignore: cast_nullable_to_non_nullable
              as List<GrayList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExpandSubjectBannerInfo implements _ExpandSubjectBannerInfo {
  const _$_ExpandSubjectBannerInfo(
      {@JsonKey(name: 'recommendList')
      final List<RecommendBannerInfo>? recommendBannerList,
      final List<GrayList>? grayList})
      : _recommendBannerList = recommendBannerList,
        _grayList = grayList;

  factory _$_ExpandSubjectBannerInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ExpandSubjectBannerInfoFromJson(json);

  final List<RecommendBannerInfo>? _recommendBannerList;
  @override
  @JsonKey(name: 'recommendList')
  List<RecommendBannerInfo>? get recommendBannerList {
    final value = _recommendBannerList;
    if (value == null) return null;
    if (_recommendBannerList is EqualUnmodifiableListView)
      return _recommendBannerList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<GrayList>? _grayList;
  @override
  List<GrayList>? get grayList {
    final value = _grayList;
    if (value == null) return null;
    if (_grayList is EqualUnmodifiableListView) return _grayList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ExpandSubjectBannerInfo(recommendBannerList: $recommendBannerList, grayList: $grayList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExpandSubjectBannerInfo &&
            const DeepCollectionEquality()
                .equals(other._recommendBannerList, _recommendBannerList) &&
            const DeepCollectionEquality().equals(other._grayList, _grayList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_recommendBannerList),
      const DeepCollectionEquality().hash(_grayList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExpandSubjectBannerInfoCopyWith<_$_ExpandSubjectBannerInfo>
      get copyWith =>
          __$$_ExpandSubjectBannerInfoCopyWithImpl<_$_ExpandSubjectBannerInfo>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExpandSubjectBannerInfoToJson(
      this,
    );
  }
}

abstract class _ExpandSubjectBannerInfo implements ExpandSubjectBannerInfo {
  const factory _ExpandSubjectBannerInfo(
      {@JsonKey(name: 'recommendList')
      final List<RecommendBannerInfo>? recommendBannerList,
      final List<GrayList>? grayList}) = _$_ExpandSubjectBannerInfo;

  factory _ExpandSubjectBannerInfo.fromJson(Map<String, dynamic> json) =
      _$_ExpandSubjectBannerInfo.fromJson;

  @override
  @JsonKey(name: 'recommendList')
  List<RecommendBannerInfo>? get recommendBannerList;
  @override
  List<GrayList>? get grayList;
  @override
  @JsonKey(ignore: true)
  _$$_ExpandSubjectBannerInfoCopyWith<_$_ExpandSubjectBannerInfo>
      get copyWith => throw _privateConstructorUsedError;
}

GrayList _$GrayListFromJson(Map<String, dynamic> json) {
  return _GrayList.fromJson(json);
}

/// @nodoc
mixin _$GrayList {
  bool? get grayCover => throw _privateConstructorUsedError;
  bool? get grayHit => throw _privateConstructorUsedError;
  String? get grayScaleId => throw _privateConstructorUsedError;
  int? get testValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrayListCopyWith<GrayList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrayListCopyWith<$Res> {
  factory $GrayListCopyWith(GrayList value, $Res Function(GrayList) then) =
      _$GrayListCopyWithImpl<$Res, GrayList>;
  @useResult
  $Res call(
      {bool? grayCover, bool? grayHit, String? grayScaleId, int? testValue});
}

/// @nodoc
class _$GrayListCopyWithImpl<$Res, $Val extends GrayList>
    implements $GrayListCopyWith<$Res> {
  _$GrayListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_value.copyWith(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GrayListCopyWith<$Res> implements $GrayListCopyWith<$Res> {
  factory _$$_GrayListCopyWith(
          _$_GrayList value, $Res Function(_$_GrayList) then) =
      __$$_GrayListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? grayCover, bool? grayHit, String? grayScaleId, int? testValue});
}

/// @nodoc
class __$$_GrayListCopyWithImpl<$Res>
    extends _$GrayListCopyWithImpl<$Res, _$_GrayList>
    implements _$$_GrayListCopyWith<$Res> {
  __$$_GrayListCopyWithImpl(
      _$_GrayList _value, $Res Function(_$_GrayList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_$_GrayList(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GrayList implements _GrayList {
  const _$_GrayList(
      {this.grayCover, this.grayHit, this.grayScaleId, this.testValue});

  factory _$_GrayList.fromJson(Map<String, dynamic> json) =>
      _$$_GrayListFromJson(json);

  @override
  final bool? grayCover;
  @override
  final bool? grayHit;
  @override
  final String? grayScaleId;
  @override
  final int? testValue;

  @override
  String toString() {
    return 'GrayList(grayCover: $grayCover, grayHit: $grayHit, grayScaleId: $grayScaleId, testValue: $testValue)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GrayList &&
            (identical(other.grayCover, grayCover) ||
                other.grayCover == grayCover) &&
            (identical(other.grayHit, grayHit) || other.grayHit == grayHit) &&
            (identical(other.grayScaleId, grayScaleId) ||
                other.grayScaleId == grayScaleId) &&
            (identical(other.testValue, testValue) ||
                other.testValue == testValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, grayCover, grayHit, grayScaleId, testValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GrayListCopyWith<_$_GrayList> get copyWith =>
      __$$_GrayListCopyWithImpl<_$_GrayList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GrayListToJson(
      this,
    );
  }
}

abstract class _GrayList implements GrayList {
  const factory _GrayList(
      {final bool? grayCover,
      final bool? grayHit,
      final String? grayScaleId,
      final int? testValue}) = _$_GrayList;

  factory _GrayList.fromJson(Map<String, dynamic> json) = _$_GrayList.fromJson;

  @override
  bool? get grayCover;
  @override
  bool? get grayHit;
  @override
  String? get grayScaleId;
  @override
  int? get testValue;
  @override
  @JsonKey(ignore: true)
  _$$_GrayListCopyWith<_$_GrayList> get copyWith =>
      throw _privateConstructorUsedError;
}

RecommendBannerInfo _$RecommendBannerInfoFromJson(Map<String, dynamic> json) {
  return _RecommendBannerInfo.fromJson(json);
}

/// @nodoc
mixin _$RecommendBannerInfo {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get recommendConfigType => throw _privateConstructorUsedError;
  int? get recommendSubjectType => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  String? get titleColor => throw _privateConstructorUsedError;
  String? get descriptionColor => throw _privateConstructorUsedError;
  String? get buttonDescription => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get businessLabelId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecommendBannerInfoCopyWith<RecommendBannerInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecommendBannerInfoCopyWith<$Res> {
  factory $RecommendBannerInfoCopyWith(
          RecommendBannerInfo value, $Res Function(RecommendBannerInfo) then) =
      _$RecommendBannerInfoCopyWithImpl<$Res, RecommendBannerInfo>;
  @useResult
  $Res call(
      {int? id,
      String? name,
      int? recommendConfigType,
      int? recommendSubjectType,
      String? pictureUrl,
      String? title,
      String? description,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? buttonDescription,
      String? linkUrl,
      String? businessLabelId});
}

/// @nodoc
class _$RecommendBannerInfoCopyWithImpl<$Res, $Val extends RecommendBannerInfo>
    implements $RecommendBannerInfoCopyWith<$Res> {
  _$RecommendBannerInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? recommendConfigType = freezed,
    Object? recommendSubjectType = freezed,
    Object? pictureUrl = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? buttonDescription = freezed,
    Object? linkUrl = freezed,
    Object? businessLabelId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendConfigType: freezed == recommendConfigType
          ? _value.recommendConfigType
          : recommendConfigType // ignore: cast_nullable_to_non_nullable
              as int?,
      recommendSubjectType: freezed == recommendSubjectType
          ? _value.recommendSubjectType
          : recommendSubjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessLabelId: freezed == businessLabelId
          ? _value.businessLabelId
          : businessLabelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RecommendBannerInfoCopyWith<$Res>
    implements $RecommendBannerInfoCopyWith<$Res> {
  factory _$$_RecommendBannerInfoCopyWith(_$_RecommendBannerInfo value,
          $Res Function(_$_RecommendBannerInfo) then) =
      __$$_RecommendBannerInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? name,
      int? recommendConfigType,
      int? recommendSubjectType,
      String? pictureUrl,
      String? title,
      String? description,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? buttonDescription,
      String? linkUrl,
      String? businessLabelId});
}

/// @nodoc
class __$$_RecommendBannerInfoCopyWithImpl<$Res>
    extends _$RecommendBannerInfoCopyWithImpl<$Res, _$_RecommendBannerInfo>
    implements _$$_RecommendBannerInfoCopyWith<$Res> {
  __$$_RecommendBannerInfoCopyWithImpl(_$_RecommendBannerInfo _value,
      $Res Function(_$_RecommendBannerInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? recommendConfigType = freezed,
    Object? recommendSubjectType = freezed,
    Object? pictureUrl = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? buttonDescription = freezed,
    Object? linkUrl = freezed,
    Object? businessLabelId = freezed,
  }) {
    return _then(_$_RecommendBannerInfo(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendConfigType: freezed == recommendConfigType
          ? _value.recommendConfigType
          : recommendConfigType // ignore: cast_nullable_to_non_nullable
              as int?,
      recommendSubjectType: freezed == recommendSubjectType
          ? _value.recommendSubjectType
          : recommendSubjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      businessLabelId: freezed == businessLabelId
          ? _value.businessLabelId
          : businessLabelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RecommendBannerInfo implements _RecommendBannerInfo {
  const _$_RecommendBannerInfo(
      {this.id,
      this.name,
      this.recommendConfigType,
      this.recommendSubjectType,
      this.pictureUrl,
      this.title,
      this.description,
      this.backgroundColor,
      this.titleColor,
      this.descriptionColor,
      this.buttonDescription,
      this.linkUrl,
      this.businessLabelId});

  factory _$_RecommendBannerInfo.fromJson(Map<String, dynamic> json) =>
      _$$_RecommendBannerInfoFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  @override
  final int? recommendConfigType;
  @override
  final int? recommendSubjectType;
  @override
  final String? pictureUrl;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final String? backgroundColor;
  @override
  final String? titleColor;
  @override
  final String? descriptionColor;
  @override
  final String? buttonDescription;
  @override
  final String? linkUrl;
  @override
  final String? businessLabelId;

  @override
  String toString() {
    return 'RecommendBannerInfo(id: $id, name: $name, recommendConfigType: $recommendConfigType, recommendSubjectType: $recommendSubjectType, pictureUrl: $pictureUrl, title: $title, description: $description, backgroundColor: $backgroundColor, titleColor: $titleColor, descriptionColor: $descriptionColor, buttonDescription: $buttonDescription, linkUrl: $linkUrl, businessLabelId: $businessLabelId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RecommendBannerInfo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.recommendConfigType, recommendConfigType) ||
                other.recommendConfigType == recommendConfigType) &&
            (identical(other.recommendSubjectType, recommendSubjectType) ||
                other.recommendSubjectType == recommendSubjectType) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.titleColor, titleColor) ||
                other.titleColor == titleColor) &&
            (identical(other.descriptionColor, descriptionColor) ||
                other.descriptionColor == descriptionColor) &&
            (identical(other.buttonDescription, buttonDescription) ||
                other.buttonDescription == buttonDescription) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.businessLabelId, businessLabelId) ||
                other.businessLabelId == businessLabelId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      recommendConfigType,
      recommendSubjectType,
      pictureUrl,
      title,
      description,
      backgroundColor,
      titleColor,
      descriptionColor,
      buttonDescription,
      linkUrl,
      businessLabelId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RecommendBannerInfoCopyWith<_$_RecommendBannerInfo> get copyWith =>
      __$$_RecommendBannerInfoCopyWithImpl<_$_RecommendBannerInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RecommendBannerInfoToJson(
      this,
    );
  }
}

abstract class _RecommendBannerInfo implements RecommendBannerInfo {
  const factory _RecommendBannerInfo(
      {final int? id,
      final String? name,
      final int? recommendConfigType,
      final int? recommendSubjectType,
      final String? pictureUrl,
      final String? title,
      final String? description,
      final String? backgroundColor,
      final String? titleColor,
      final String? descriptionColor,
      final String? buttonDescription,
      final String? linkUrl,
      final String? businessLabelId}) = _$_RecommendBannerInfo;

  factory _RecommendBannerInfo.fromJson(Map<String, dynamic> json) =
      _$_RecommendBannerInfo.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  int? get recommendConfigType;
  @override
  int? get recommendSubjectType;
  @override
  String? get pictureUrl;
  @override
  String? get title;
  @override
  String? get description;
  @override
  String? get backgroundColor;
  @override
  String? get titleColor;
  @override
  String? get descriptionColor;
  @override
  String? get buttonDescription;
  @override
  String? get linkUrl;
  @override
  String? get businessLabelId;
  @override
  @JsonKey(ignore: true)
  _$$_RecommendBannerInfoCopyWith<_$_RecommendBannerInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
