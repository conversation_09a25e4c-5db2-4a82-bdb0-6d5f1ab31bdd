// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'space_entrance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SpaceEntrance _$$_SpaceEntranceFromJson(Map<String, dynamic> json) =>
    _$_SpaceEntrance(
      extracurricularServiceBaseInfoList:
          (json['extracurricularServiceBaseInfoList'] as List<dynamic>?)
              ?.map((e) => ExtracurricularServiceBaseInfoList.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_SpaceEntranceToJson(_$_SpaceEntrance instance) =>
    <String, dynamic>{
      'extracurricularServiceBaseInfoList':
          instance.extracurricularServiceBaseInfoList,
    };

_$_ExtracurricularServiceBaseInfoList
    _$$_ExtracurricularServiceBaseInfoListFromJson(Map<String, dynamic> json) =>
        _$_ExtracurricularServiceBaseInfoList(
          type: json['type'] as int?,
          name: json['name'] as String?,
          entranceImageUrl: json['entranceImageUrl'] as String?,
          route: json['route'] as String?,
        );

Map<String, dynamic> _$$_ExtracurricularServiceBaseInfoListToJson(
        _$_ExtracurricularServiceBaseInfoList instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'entranceImageUrl': instance.entranceImageUrl,
      'route': instance.route,
    };
