// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'space_entrance.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SpaceEntrance _$SpaceEntranceFromJson(Map<String, dynamic> json) {
  return _SpaceEntrance.fromJson(json);
}

/// @nodoc
mixin _$SpaceEntrance {
  List<ExtracurricularServiceBaseInfoList>?
      get extracurricularServiceBaseInfoList =>
          throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpaceEntranceCopyWith<SpaceEntrance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpaceEntranceCopyWith<$Res> {
  factory $SpaceEntranceCopyWith(
          SpaceEntrance value, $Res Function(SpaceEntrance) then) =
      _$SpaceEntranceCopyWithImpl<$Res, SpaceEntrance>;
  @useResult
  $Res call(
      {List<ExtracurricularServiceBaseInfoList>?
          extracurricularServiceBaseInfoList});
}

/// @nodoc
class _$SpaceEntranceCopyWithImpl<$Res, $Val extends SpaceEntrance>
    implements $SpaceEntranceCopyWith<$Res> {
  _$SpaceEntranceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extracurricularServiceBaseInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      extracurricularServiceBaseInfoList: freezed ==
              extracurricularServiceBaseInfoList
          ? _value.extracurricularServiceBaseInfoList
          : extracurricularServiceBaseInfoList // ignore: cast_nullable_to_non_nullable
              as List<ExtracurricularServiceBaseInfoList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpaceEntranceCopyWith<$Res>
    implements $SpaceEntranceCopyWith<$Res> {
  factory _$$_SpaceEntranceCopyWith(
          _$_SpaceEntrance value, $Res Function(_$_SpaceEntrance) then) =
      __$$_SpaceEntranceCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ExtracurricularServiceBaseInfoList>?
          extracurricularServiceBaseInfoList});
}

/// @nodoc
class __$$_SpaceEntranceCopyWithImpl<$Res>
    extends _$SpaceEntranceCopyWithImpl<$Res, _$_SpaceEntrance>
    implements _$$_SpaceEntranceCopyWith<$Res> {
  __$$_SpaceEntranceCopyWithImpl(
      _$_SpaceEntrance _value, $Res Function(_$_SpaceEntrance) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? extracurricularServiceBaseInfoList = freezed,
  }) {
    return _then(_$_SpaceEntrance(
      extracurricularServiceBaseInfoList: freezed ==
              extracurricularServiceBaseInfoList
          ? _value._extracurricularServiceBaseInfoList
          : extracurricularServiceBaseInfoList // ignore: cast_nullable_to_non_nullable
              as List<ExtracurricularServiceBaseInfoList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpaceEntrance implements _SpaceEntrance {
  const _$_SpaceEntrance(
      {final List<ExtracurricularServiceBaseInfoList>?
          extracurricularServiceBaseInfoList})
      : _extracurricularServiceBaseInfoList =
            extracurricularServiceBaseInfoList;

  factory _$_SpaceEntrance.fromJson(Map<String, dynamic> json) =>
      _$$_SpaceEntranceFromJson(json);

  final List<ExtracurricularServiceBaseInfoList>?
      _extracurricularServiceBaseInfoList;
  @override
  List<ExtracurricularServiceBaseInfoList>?
      get extracurricularServiceBaseInfoList {
    final value = _extracurricularServiceBaseInfoList;
    if (value == null) return null;
    if (_extracurricularServiceBaseInfoList is EqualUnmodifiableListView)
      return _extracurricularServiceBaseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SpaceEntrance(extracurricularServiceBaseInfoList: $extracurricularServiceBaseInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpaceEntrance &&
            const DeepCollectionEquality().equals(
                other._extracurricularServiceBaseInfoList,
                _extracurricularServiceBaseInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_extracurricularServiceBaseInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpaceEntranceCopyWith<_$_SpaceEntrance> get copyWith =>
      __$$_SpaceEntranceCopyWithImpl<_$_SpaceEntrance>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpaceEntranceToJson(
      this,
    );
  }
}

abstract class _SpaceEntrance implements SpaceEntrance {
  const factory _SpaceEntrance(
      {final List<ExtracurricularServiceBaseInfoList>?
          extracurricularServiceBaseInfoList}) = _$_SpaceEntrance;

  factory _SpaceEntrance.fromJson(Map<String, dynamic> json) =
      _$_SpaceEntrance.fromJson;

  @override
  List<ExtracurricularServiceBaseInfoList>?
      get extracurricularServiceBaseInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_SpaceEntranceCopyWith<_$_SpaceEntrance> get copyWith =>
      throw _privateConstructorUsedError;
}

ExtracurricularServiceBaseInfoList _$ExtracurricularServiceBaseInfoListFromJson(
    Map<String, dynamic> json) {
  return _ExtracurricularServiceBaseInfoList.fromJson(json);
}

/// @nodoc
mixin _$ExtracurricularServiceBaseInfoList {
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get entranceImageUrl => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtracurricularServiceBaseInfoListCopyWith<
          ExtracurricularServiceBaseInfoList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtracurricularServiceBaseInfoListCopyWith<$Res> {
  factory $ExtracurricularServiceBaseInfoListCopyWith(
          ExtracurricularServiceBaseInfoList value,
          $Res Function(ExtracurricularServiceBaseInfoList) then) =
      _$ExtracurricularServiceBaseInfoListCopyWithImpl<$Res,
          ExtracurricularServiceBaseInfoList>;
  @useResult
  $Res call({int? type, String? name, String? entranceImageUrl, String? route});
}

/// @nodoc
class _$ExtracurricularServiceBaseInfoListCopyWithImpl<$Res,
        $Val extends ExtracurricularServiceBaseInfoList>
    implements $ExtracurricularServiceBaseInfoListCopyWith<$Res> {
  _$ExtracurricularServiceBaseInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? entranceImageUrl = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImageUrl: freezed == entranceImageUrl
          ? _value.entranceImageUrl
          : entranceImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExtracurricularServiceBaseInfoListCopyWith<$Res>
    implements $ExtracurricularServiceBaseInfoListCopyWith<$Res> {
  factory _$$_ExtracurricularServiceBaseInfoListCopyWith(
          _$_ExtracurricularServiceBaseInfoList value,
          $Res Function(_$_ExtracurricularServiceBaseInfoList) then) =
      __$$_ExtracurricularServiceBaseInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? name, String? entranceImageUrl, String? route});
}

/// @nodoc
class __$$_ExtracurricularServiceBaseInfoListCopyWithImpl<$Res>
    extends _$ExtracurricularServiceBaseInfoListCopyWithImpl<$Res,
        _$_ExtracurricularServiceBaseInfoList>
    implements _$$_ExtracurricularServiceBaseInfoListCopyWith<$Res> {
  __$$_ExtracurricularServiceBaseInfoListCopyWithImpl(
      _$_ExtracurricularServiceBaseInfoList _value,
      $Res Function(_$_ExtracurricularServiceBaseInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? entranceImageUrl = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_ExtracurricularServiceBaseInfoList(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImageUrl: freezed == entranceImageUrl
          ? _value.entranceImageUrl
          : entranceImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExtracurricularServiceBaseInfoList
    implements _ExtracurricularServiceBaseInfoList {
  const _$_ExtracurricularServiceBaseInfoList(
      {this.type, this.name, this.entranceImageUrl, this.route});

  factory _$_ExtracurricularServiceBaseInfoList.fromJson(
          Map<String, dynamic> json) =>
      _$$_ExtracurricularServiceBaseInfoListFromJson(json);

  @override
  final int? type;
  @override
  final String? name;
  @override
  final String? entranceImageUrl;
  @override
  final String? route;

  @override
  String toString() {
    return 'ExtracurricularServiceBaseInfoList(type: $type, name: $name, entranceImageUrl: $entranceImageUrl, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExtracurricularServiceBaseInfoList &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.entranceImageUrl, entranceImageUrl) ||
                other.entranceImageUrl == entranceImageUrl) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, name, entranceImageUrl, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtracurricularServiceBaseInfoListCopyWith<
          _$_ExtracurricularServiceBaseInfoList>
      get copyWith => __$$_ExtracurricularServiceBaseInfoListCopyWithImpl<
          _$_ExtracurricularServiceBaseInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtracurricularServiceBaseInfoListToJson(
      this,
    );
  }
}

abstract class _ExtracurricularServiceBaseInfoList
    implements ExtracurricularServiceBaseInfoList {
  const factory _ExtracurricularServiceBaseInfoList(
      {final int? type,
      final String? name,
      final String? entranceImageUrl,
      final String? route}) = _$_ExtracurricularServiceBaseInfoList;

  factory _ExtracurricularServiceBaseInfoList.fromJson(
          Map<String, dynamic> json) =
      _$_ExtracurricularServiceBaseInfoList.fromJson;

  @override
  int? get type;
  @override
  String? get name;
  @override
  String? get entranceImageUrl;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_ExtracurricularServiceBaseInfoListCopyWith<
          _$_ExtracurricularServiceBaseInfoList>
      get copyWith => throw _privateConstructorUsedError;
}
