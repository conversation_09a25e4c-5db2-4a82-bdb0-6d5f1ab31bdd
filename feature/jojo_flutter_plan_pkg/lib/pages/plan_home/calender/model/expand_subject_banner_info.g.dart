// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expand_subject_banner_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ExpandSubjectBannerInfo _$$_ExpandSubjectBannerInfoFromJson(
        Map<String, dynamic> json) =>
    _$_ExpandSubjectBannerInfo(
      recommendBannerList: (json['recommendList'] as List<dynamic>?)
          ?.map((e) => RecommendBannerInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      grayList: (json['grayList'] as List<dynamic>?)
          ?.map((e) => GrayList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ExpandSubjectBannerInfoToJson(
        _$_ExpandSubjectBannerInfo instance) =>
    <String, dynamic>{
      'recommendList': instance.recommendBannerList,
      'grayList': instance.grayList,
    };

_$_GrayList _$$_GrayListFromJson(Map<String, dynamic> json) => _$_GrayList(
      grayCover: json['grayCover'] as bool?,
      grayHit: json['grayHit'] as bool?,
      grayScaleId: json['grayScaleId'] as String?,
      testValue: json['testValue'] as int?,
    );

Map<String, dynamic> _$$_GrayListToJson(_$_GrayList instance) =>
    <String, dynamic>{
      'grayCover': instance.grayCover,
      'grayHit': instance.grayHit,
      'grayScaleId': instance.grayScaleId,
      'testValue': instance.testValue,
    };

_$_RecommendBannerInfo _$$_RecommendBannerInfoFromJson(
        Map<String, dynamic> json) =>
    _$_RecommendBannerInfo(
      id: json['id'] as int?,
      name: json['name'] as String?,
      recommendConfigType: json['recommendConfigType'] as int?,
      recommendSubjectType: json['recommendSubjectType'] as int?,
      pictureUrl: json['pictureUrl'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      titleColor: json['titleColor'] as String?,
      descriptionColor: json['descriptionColor'] as String?,
      buttonDescription: json['buttonDescription'] as String?,
      linkUrl: json['linkUrl'] as String?,
      businessLabelId: json['businessLabelId'] as String?,
    );

Map<String, dynamic> _$$_RecommendBannerInfoToJson(
        _$_RecommendBannerInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'recommendConfigType': instance.recommendConfigType,
      'recommendSubjectType': instance.recommendSubjectType,
      'pictureUrl': instance.pictureUrl,
      'title': instance.title,
      'description': instance.description,
      'backgroundColor': instance.backgroundColor,
      'titleColor': instance.titleColor,
      'descriptionColor': instance.descriptionColor,
      'buttonDescription': instance.buttonDescription,
      'linkUrl': instance.linkUrl,
      'businessLabelId': instance.businessLabelId,
    };
