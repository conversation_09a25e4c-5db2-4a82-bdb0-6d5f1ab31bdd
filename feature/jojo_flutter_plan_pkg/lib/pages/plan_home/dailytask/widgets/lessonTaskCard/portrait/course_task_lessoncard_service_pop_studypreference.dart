import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../../../../plan_home_lesson/controller.dart';
import '../../../../../plan_home_lesson/model/card_course_theme_info.dart';
import '../../../../../plan_home_lesson/model/course_lesson_info.dart';
import '../../../../utils/ext.dart';
import 'course_task_lessoncard_base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/utils/course_map_utils.dart';

/// 学习偏好设置弹窗
class CourseTaskLessonServicePOPStudyPreference extends StatefulWidget {
  final bool? isLandscape;
  final BuildContext context;
  final PlanHomeLessonCtrl? ctrl;
  final CourseGuideService? serviceItem;

  const CourseTaskLessonServicePOPStudyPreference(
      this.ctrl, this.context, this.isLandscape, this.serviceItem,
      {super.key});

  // 显示学习偏好设置弹窗
  static void showPOPCourseTaskLessonServicePOPStudyPreference(
      BuildContext context,
      PlanHomeLessonCtrl? ctrl,
      bool? isLandscape,
      CourseGuideService? serviceItem) async {
    double bottomHeight = await getSafeBottom(context);
    if (isLandscape == false) {
      jojoNativeBridge.showHomePageTabs(show: false);
    }
    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': "学习偏好设置半弹窗",
      'course_type': serviceItem?.courseType?.getCourseTypeStr() ?? "",
      'business_type': serviceItem?.classStatus?.getClassStatusStr() ?? "",
      'course_stage': serviceItem?.courseSegment,
      'material_id': serviceItem?.subjectName,
      'class_id': serviceItem?.classId,
      'course_key': serviceItem?.courseKey
    });
    SmartDialog.show(
        animationTime: const Duration(milliseconds: 250),
        clickMaskDismiss: true,
        onDismiss: () {
          if (isLandscape == false) {
            jojoNativeBridge.showHomePageTabs(show: true);
          }
        },
        alignment: (isLandscape ?? false)
            ? Alignment.centerRight
            : Alignment.bottomCenter,
        maskColor: HexColor('#000000', 0.4),
        builder: (_) => CourseTaskLessonCardDialog(
            isLand: isLandscape == true,
            title: serviceItem?.popupInfo?.popupTitle ?? "",
            radius: null,
            bottomText: null,
            safeBottom: bottomHeight,
            closeCallback: () {
              if (isLandscape == false) {
                jojoNativeBridge.showHomePageTabs(show: true);
              }
              SmartDialog.dismiss();
            },
            bottomCallback: null,
            child: CourseTaskLessonServicePOPStudyPreference(
                ctrl, context, isLandscape, serviceItem)));
  }

  @override
  State<CourseTaskLessonServicePOPStudyPreference> createState() =>
      _CourseTaskLessonServicePOPStudyPreferenceState();
}

class _CourseTaskLessonServicePOPStudyPreferenceState
    extends State<CourseTaskLessonServicePOPStudyPreference> {
  int? selectedKey; // 当前选中的按钮的 navKey
  String? selectedContentImg = ''; // 当前选中的按钮的 contentImg

  @override
  void initState() {
    super.initState();
    List<Option>? options = widget.serviceItem?.popupInfo?.options ?? [];
    Option selectedOption = options.firstWhere(
      (option) => option.hasChoice == 1,
      orElse: () => const Option(), // 返回一个默认值
    );
    selectedKey = selectedOption.navKey ?? 0;
    selectedContentImg = selectedOption.contentImg ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
        width: double.infinity,
        decoration: BoxDecoration(
          color: HexColor("#ffffff"),
        ),
        child: _buildContentWidget());
  }

  // 正文内容
  Widget _buildContentWidget() {
    late double safeBottom = MediaQuery.of(context).padding.bottom;

    return Stack(
      children: [
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          SizedBox(
            height: safeBottom > 0 ? 40.rdp : 0.rdp,
          ),
          _buildTitle(),
          SizedBox(
            height: 10.rdp,
          ),
          _buildDescription(),
          SizedBox(
            height: 40.rdp,
          ),
          _buildWeekOptions(),
          SizedBox(
            height: 20.rdp,
          ),
          _buildWeekImage(),
          SizedBox(
            height: 20.rdp,
          ),
          _buildTpis(),
        ]),
        Positioned(
          bottom: RunEnv.isAndroid
              ? 50.rdp
              : (safeBottom > 0
                  ? safeBottom
                  : (widget.isLandscape == true ? 0 : 10.0.rdp)),
          child: _buildConfirmBtn(),
        )
      ],
    );
  }

  Widget _buildTitle() {
    //标题
    return Text(
      widget.serviceItem?.popupInfo?.actionTitle ?? "",
      style: TextStyle(
        fontSize: 20.rdp,
        color: HexColor("#404040"),
      ),
    );
  }

  Widget _buildDescription() {
    //描述
    return Text(
      widget.serviceItem?.popupInfo?.introduce ?? "",
      style: TextStyle(
        fontSize: 15.rdp,
        color: HexColor("#666666"),
      ),
    );
  }

  Widget _buildWeekOptions() {
    // 获取 options 列表
    List<Option>? options = widget.serviceItem?.popupInfo?.options ?? [];

    // 如果 options 为空，返回空容器
    if (options.isEmpty) {
      return Container();
    }

    List<Widget> widgetList = [];
    for (var element in (options)) {
      widgetList.add(_buildWeekItem(element));
    }
    return Wrap(
      spacing: 16.rdp, // 按钮之间的水平间距
      runSpacing: 16.rdp, // 按钮之间的垂直间距
      children: options.take(6).map((option) {
        // 最多显示 6 个按钮
        return _buildWeekItem(option);
      }).toList(),
    );
  }

  Widget _buildWeekItem(Option option) {
    double width = widget.isLandscape == true
        ? (375.rdp - 40.rdp - 36.rdp) / 3
        : (MediaQuery.of(context).size.width - 40.rdp - 36.rdp) / 3;
    // 判断当前按钮是否选中
    bool isSelected = selectedKey == option.navKey;

    //描述
    return JoJoBtn(
      width: width,
      height: 38.rdp,
      fontSize: 14.rdp,
      text: option.navTitle ?? "",
      color: isSelected ? HexColor("#FCDA00") : HexColor('#F6F7F8'),
      fontColor: isSelected ? HexColor("#544300") : HexColor('#2B323C'),
      tapHandle: () {
        RunEnv.sensorsTrack('\$AppClick', {
          '\$screen_name': "学习偏好设置半弹窗",
          '\$element_name': option.navTitle,
          'course_type':
              widget.serviceItem?.courseType?.getCourseTypeStr() ?? "",
          'business_type':
              widget.serviceItem?.classStatus?.getClassStatusStr() ?? "",
          'course_stage': widget.serviceItem?.courseSegment,
          'material_id': widget.serviceItem?.subjectName,
          'class_id': widget.serviceItem?.classId,
          'course_key': widget.serviceItem?.courseKey
        });
        // 更新选中状态
        setState(() {
          selectedKey = option.navKey;
          selectedContentImg = option.contentImg;
        });
      },
    );
  }

  Widget _buildWeekImage() {
    //星期的图片
    return ImageNetworkCached(
      imageUrl: selectedContentImg ?? "",
      fit: BoxFit.cover,
      width: widget.isLandscape == true
          ? 375.rdp
          : MediaQuery.of(context).size.width,
      placeholderWidget: Container(),
      errorWidget: Container(),
    );
  }

  Widget _buildTpis() {
    String? tip = selectedContentImg ?? '';
    //提示
    return Text(
      tip.isNotEmpty ? widget.serviceItem?.popupInfo?.actionTip ?? "" : '',
      style: TextStyle(
        fontSize: 12.rdp,
        color: HexColor("#B2B2B2"),
      ),
    );
  }

  Widget _buildConfirmBtn() {
    double width = widget.isLandscape == true
        ? 375.rdp - 40.rdp
        : MediaQuery.of(context).size.width - 40.rdp;
    //描述
    int confirmKey = selectedKey ?? 0;
    return JoJoBtn(
      width: width,
      color: confirmKey > 0 ? HexColor('#FCDA00') : HexColor("#F5F4F4"),
      fontColor: confirmKey > 0 ? HexColor('#544300') : HexColor("#B2B2B2"),
      height: 44.rdp,
      fontSize: 18.rdp,
      text: widget.serviceItem?.popupInfo?.buttonText ?? "",
      tapHandle: () {
        widget.ctrl?.requestStudyPreference(
            widget.context,
            selectedKey,
            widget.serviceItem?.classKey,
            widget.serviceItem?.popupInfo?.closedText);
        RunEnv.sensorsTrack('\$AppClick', {
          '\$screen_name': "学习偏好设置半弹窗",
          '\$element_name': widget.serviceItem?.popupInfo?.buttonText,
          'course_type':
              widget.serviceItem?.courseType?.getCourseTypeStr() ?? "",
          'business_type':
              widget.serviceItem?.classStatus?.getClassStatusStr() ?? "",
          'course_stage': widget.serviceItem?.courseSegment,
          'material_id': widget.serviceItem?.subjectName,
          'class_id': widget.serviceItem?.classId,
          'course_key': widget.serviceItem?.courseKey
        });
      },
    );
  }
}

// 