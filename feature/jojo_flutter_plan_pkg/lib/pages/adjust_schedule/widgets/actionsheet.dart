import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/resources/jojo_icons.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_base/widgets/app_rich_text.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/service/adjust_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

import '../controller.dart';
import '../model/adjust_plan_type.dart';
import '../state.dart';

/// 提前开启-规则说明
ruleExplain(BuildContext? context, String type) {
  if (context == null || !context.mounted) {
    return;
  }
  String? _content = "";
  String? _adjustTitle = "";
  if (type == AdjustType.later) {
    _adjustTitle = '延后开启计划说明';
    _content = context
        .read<AdjustScheduleCtrl>()
        .state
        .laterAdjustData
        .advanceStartExplain;
  } else {
    _adjustTitle = '提前开启计划说明';
    _content = context
        .read<AdjustScheduleCtrl>()
        .state
        .aHeadAdjustData
        .advanceStartExplain;
  }
  buildActionSheet(
    context,
    children: [
      Text(
        _adjustTitle,
        style: TextStyle(fontSize: pt(18), fontWeight: FontWeight.w500),
      ),
      // SizedBox(height: pt(16)),
      Flexible(
        child: SingleChildScrollView(
          child: JojoRichText(richHtml: _content ?? ''),
        ),
      ),
      SizedBox(height: pt(12)),
      JoJoBtn(
        text: '知道了',
        height: pt(44),
        width: pt(280),
        tapHandle: () {
          SmartDialog.dismiss();
        },
      )
    ],
  );
}

class RichTextItem {
  final String title;
  final String richText;

  RichTextItem({
    required this.title,
    required this.richText,
  });
}

/// 开启说明
Future<dynamic> startExplain(
  BuildContext context,
  TransClassPlanList? content,
  TargetClassInfo? item,
  String type,
) {
  String? adjustTitle = "";
  List<RichTextItem> richText = [];
  var isYearCourse =
      context.read<AdjustScheduleCtrl>().state.queryParams['courseType'] != '1';

  if (type == AdjustType.later) {
    adjustTitle = "延后开启计划说明";
    var _content = context.read<AdjustScheduleCtrl>().state.laterAdjustData;
    if (isYearCourse) {
      //年课
      richText = [
        RichTextItem(
          title: '物流确认',
          richText: _content.advanceStartConfirm ?? '',
        ),
        RichTextItem(
          title: '更换指导师',
          richText: _content.advanceStartAddTeacher ?? '',
        ),
      ];
    } else {
      //训练营
      richText = [
        RichTextItem(
          title: '数据清空',
          richText: _content.advanceStartClearData ?? '',
        ),
        RichTextItem(
          title: '更换指导师',
          richText: _content.advanceStartAddTeacher ?? '',
        ),
        RichTextItem(
          title: '物流确认',
          richText: _content.advanceStartConfirm ?? '',
        ),
      ];
    }
  } else {
    adjustTitle = "提前开启计划说明";
    var _content = context.read<AdjustScheduleCtrl>().state.aHeadAdjustData;

    if (isYearCourse) {
      //年课
      richText = [
        RichTextItem(
          title: '物流确认',
          richText: _content.advanceStartConfirm ?? '',
        ),
        RichTextItem(
          title: '更换指导师',
          richText: _content.advanceStartAddTeacher ?? '',
        ),
      ];
    } else {
      //训练营
      richText = [
        RichTextItem(
          title: '数据清空',
          richText: _content.advanceStartClearData ?? '',
        ),
        RichTextItem(
          title: '更换指导师',
          richText: _content.advanceStartAddTeacher ?? '',
        ),
        RichTextItem(
          title: '物流确认',
          richText: _content.advanceStartConfirm ?? '',
        ),
      ];
    }
  }

  _flowContainer(int index, String title, String content, bool showLine) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: double.infinity,
          clipBehavior: Clip.none,
          padding: EdgeInsets.only(left: pt(20), bottom: pt(0)),
          margin: EdgeInsets.only(left: pt(10)),
          decoration: BoxDecoration(
            border: showLine
                ? const Border(
                    left: BorderSide(color: Color(0xffD1D7E0), width: 1),
                  )
                : null,
          ),
          child: Transform.translate(
            offset: Offset(0, -pt(3)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: pt(18),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                // SizedBox(height: pt(7)),
                JojoRichText(richHtml: content)
              ],
            ),
          ),
        ),
        Positioned(
          left: pt(0),
          top: pt(0),
          child: Container(
            width: pt(20),
            height: pt(20),
            decoration: BoxDecoration(
              color: const Color(0xffD1D7E0),
              borderRadius: BorderRadius.circular(pt(20)),
            ),
            child: Center(
              child: Text(
                index.toString(),
                style: TextStyle(
                  fontSize: pt(12, isFontSize: true),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  var _status = StartStatus.timing;
  Timer _timer = Timer(const Duration(seconds: 1), () {});
  Timer _loopTimer = Timer(const Duration(seconds: 1), () {});
  bool timeStarted = false;

  /// 结果控制
  /// [reture] {bool}
  /// [false] 没有返回结果
  /// [true] 有返回结果，并处理结果
  Future<bool> resultCtrl(AdjustOperationResult operationData, setState) async {
    print('resultCtrl, ${operationData.actionStatus.toString()}');

    if (operationData.actionStatus == null) {
      setState(() {
        _status = StartStatus.fail;
      });
    }

    if (operationData.actionStatus == 'SUCCESS') {
      setState(() {
        _status = StartStatus.success;
      });

      /// 有老师服务跳转
      /// 无老师关闭弹窗并 toast
      _timer.cancel();
      _loopTimer.cancel();
      if ((operationData.addTeacherRoute ?? '') != '') {
        RunEnv.jumpLink(operationData.addTeacherRoute ?? '');
      } else {
        JoJoToast.showSuccess('调整成功，可在调整记录中查看~');
      }
      context.read<AdjustScheduleCtrl>().initState(loading: false);
      setState(() {
        _status = StartStatus.timing;
      });

      return true;
    }

    if (operationData.actionStatus == 'FAILED') {
      setState(() {
        _status = StartStatus.fail;
      });
      return true;
    }

    return false;
  }

  var timerRun = true;

  /// [轮训规则]： 轮训时长：10s 每2s一次
  loopFetch(actionId, setState) {
    int count = 0;
    _loopTimer = Timer.periodic(const Duration(seconds: 2), (timer) async {
      if (timerRun == false) return;
      try {
        if (count < 5) {
          print('Query the API count: $count');
          final operationData =
              await adjustOperationService.operationResult(actionId);

          /// 轮训有结果，取消轮训
          final res = await resultCtrl(operationData, setState);
          if (res) {
            timerRun = false;
            timer.cancel();
            if (_status != StartStatus.fail) {
              SmartDialog.dismiss();
            }
          }
          count += 1;
        } else {
          setState(() {
            _status = StartStatus.fail;
          });
          SmartDialog.dismiss();
          JoJoToast.showWarning('正在处理，请稍后在调整记录中确认');
          timer.cancel();
        }
      } catch (e) {
        count += 1;
        // Sentry.captureException('adjust loop fetch error', stackTrace: e);
      }
    });
  }

  adjustNow(TargetClassInfo? item, setState) async {
    setState(() {
      _status = StartStatus.fetching;
    });
    try {
      var sensorsData = {
        "course_type": isYearCourse ? '年课' : '训练营',
        "user_state": content?.userCourseStatusName,
        "custom_state": content?.subjectTypeName,
        "course_stage": content?.courseSegment,
        "course_name": content?.courseName,
        "\$element_content": item?.targetClassStartDate,
      };

      if (type == AdjustType.later) {
        RunEnv.sensorsTrack('\$AppClick', {
          '\$element_name': '延后开启_立即调整按钮',
          ...sensorsData,
        });
      } else {
        RunEnv.sensorsTrack('\$AppClick', {
          '\$element_name': '提前开启_立即调整按钮',
          ...sensorsData,
        });
      }
      final operationData = await adjustOperationService.operationPlan(
        type,
        (content?.baseSkuNo ?? '').toString(),
        (content?.courseKey ?? '').toString(),
        (content?.sourceClassId ?? '').toString(),
        (item?.targetClassId ?? '').toString(),
      );
      final res = await resultCtrl(operationData, setState);

      /// 没有返回结果，开始轮训
      if (operationData.actionId != null && !res) {
        loopFetch(operationData.actionId, setState);
      }
    } catch (e) {
      print(e);
      setState(() {
        _status = StartStatus.fail;
      });
    }
  }

  int _start = 5;

  void startTiming(setState) {
    timeStarted = true;
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (_start == 0) {
          timer.cancel();
          setState(() {
            _status = StartStatus.idle;
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  return buildActionSheet(
    context,
    children: [
      StatefulBuilder(builder: (context, setState) {
        if (!timeStarted) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
            startTiming(setState); // 开始倒计时
          });
        }
        final Map<StartStatus, Widget> mapBtn = {
          StartStatus.idle: JoJoBtn(
            text: '立即调整',
            height: pt(44),
            width: pt(280),
            tapHandle: () {
              adjustNow(item, setState);
            },
          ),
          StartStatus.timing: JoJoBtn(
            text: '立即调整 $_start' 's',
            height: pt(44),
            width: pt(280),
            color: const Color(0xffF5F4F4),
            fontColor: const Color(0xffb2b2b2),
            tapHandle: () {
              return;
            },
          ),
          StartStatus.fetching: Container(
            width: pt(279),
            padding: EdgeInsets.symmetric(vertical: pt(10), horizontal: pt(26)),
            decoration: BoxDecoration(
              color: const Color(0xffEBF9FF),
              borderRadius: BorderRadius.circular(pt(44)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ImageAssetWeb(
                  assetName: AssetsImg.LOADING,
                  width: pt(24),
                  height: pt(24),
                  package: Config.package,
                ),
                if (item?.hasTeacher == 1) SizedBox(width: pt(2)),
                if (item?.hasTeacher == 1)
                  Text(
                    '即将打开微信添加指导师',
                    style: TextStyle(
                      fontSize: pt(18),
                      fontWeight: FontWeight.bold,
                      color: const Color(0xff004161),
                    ),
                  )
              ],
            ),
          ),
          StartStatus.fail: Container(
            padding: EdgeInsets.symmetric(vertical: pt(10), horizontal: pt(26)),
            decoration: BoxDecoration(
              color: const Color(0xffFFF2F0),
              borderRadius: BorderRadius.circular(pt(44)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  JoJoDesignIcons.error,
                  size: pt(24),
                  color: const Color(0xffFF715C),
                ),
                SizedBox(width: pt(2)),
                Text(
                  '调整失败，可联系客服处理',
                  style: TextStyle(
                    fontSize: pt(18),
                    fontWeight: FontWeight.bold,
                    color: const Color(0xff781A0C),
                  ),
                )
              ],
            ),
          )
        };

        return Column(
          children: [
            Expanded(
              flex: 0,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              adjustTitle ?? "",
                              style: TextStyle(
                                fontSize: pt(18),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        _status != StartStatus.fetching
                            ? GestureDetector(
                                onTap: () {
                                  SmartDialog.dismiss();
                                  _timer.cancel();
                                },
                                child: Container(
                                  color: Colors.white,
                                  padding: EdgeInsets.all(pt(2)),
                                  child: Icon(
                                    JoJoDesignIcons.close2,
                                    size: pt(16),
                                  ),
                                ),
                              )
                            : SizedBox(
                                width: pt(16),
                              )
                      ],
                    ),
                    SizedBox(height: pt(20)),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        DefaultTextStyle(
                          style: TextStyle(
                              color: const Color(0xff666666),
                              fontSize: pt(14, isFontSize: true)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('计划名称：'),
                              SizedBox(height: pt(3)),
                              const Text('原开始时间：'),
                              SizedBox(height: pt(3)),
                              const Text('调整后时间：'),
                            ],
                          ),
                        ),
                        SizedBox(width: pt(3)),
                        Expanded(
                          child: DefaultTextStyle(
                            style: TextStyle(
                              color: const Color(0xff666666),
                              fontSize: pt(14, isFontSize: true),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  content?.courseName ?? '',
                                ),
                                SizedBox(height: pt(3)),
                                Text(content?.sourceClassStartDate ?? ''),
                                SizedBox(height: pt(3)),
                                Text(item?.targetClassStartDate ?? ''),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: pt(32)),
                    ...List.generate(richText.length, (index) {
                      var item = richText[index];
                      return _flowContainer(
                        index + 1,
                        item.title,
                        item.richText,
                        index < richText.length - 1,
                      );
                    })
                  ],
                ),
              ),
            ),
            SizedBox(height: pt(12)),
            mapBtn[_status] as Widget
          ],
        );
      })
    ],
  );
}

/// 基础弹出层
Future buildActionSheet(
  BuildContext context, {
  required List<Widget> children,
}) {
  return SmartDialog.show(
    clickMaskDismiss: false,
    alignment: Alignment.bottomCenter,
    builder: (_) => Container(
      constraints: BoxConstraints(maxHeight: screen.screenHeight * 0.9),
      width: double.infinity,
      padding: EdgeInsets.only(
        bottom: screen.paddingBottom + pt(20),
        top: pt(19),
        left: pt(20),
        right: pt(20),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(pt(20)),
          topRight: Radius.circular(pt(20)),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    ),
  );
}

///延后开启选择班期
ruleDelayAdjustTimeDialog(
  BuildContext delayContext,
  TransClassPlanList? items,
  Map<String, dynamic> sensorsData,
) {
  var length = items?.targetClassList?.length ?? 0;

  buildActionChooseSheet(
    delayContext,
    children: [
      Text(
        '选择延后开启时间',
        style: TextStyle(
          fontSize: pt(18),
          fontWeight: FontWeight.w700,
        ),
      ),
      SizedBox(
        height: pt(10),
      ),
      SizedBox(
        height: length == 1 ? pt(100) : length * pt(44),
        child: ListView.builder(
          padding: const EdgeInsets.only(top: 0),
          itemCount: length,
          itemExtent: length == 1 ? pt(100) : pt(44),
          itemBuilder: (BuildContext context, int index) {
            var item = items?.targetClassList?.elementAt(index);

            return GestureDetector(
              onTap: () {
                //展示延迟调整功能弹出
                SmartDialog.dismiss();

                RunEnv.sensorsTrack(
                  '\$AppClick',
                  {
                    '\$element_name': '延后开启_班期选择',
                    ...sensorsData,
                    '\$element_content': item?.targetClassStartDate,
                  },
                );

                startExplain(
                  delayContext,
                  items,
                  item,
                  AdjustType.later,
                );
              },
              child: Column(
                children: [
                  Container(
                    color: HexColor("#F5F4F4"),
                    height: 1,
                  ),
                  Flexible(
                    child: Center(
                      child: Text(
                        item?.targetClassStartDate ?? '',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: pt(16),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            );
          },
        ),
      ),
      Container(
        color: HexColor("#F8F8FA"),
        height: pt(12),
      ),
      Container(
        color: Colors.white,
        height: pt(12),
      ),
      GestureDetector(
        onTap: () {
          SmartDialog.dismiss();
        },
        child: SizedBox(
          height: pt(24),
          child: Center(
            child: Container(
              color: Colors.white,
              width: double.infinity,
              child: Text(
                "取消",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: pt(16, isFontSize: true),
                  color: HexColor("#41474F"),
                ),
              ),
            ),
          ),
        ),
      )
    ],
  );
}

/// 基础弹出层
Future buildActionChooseSheet(
  BuildContext context, {
  required List<Widget> children,
}) {
  return SmartDialog.show(
    clickMaskDismiss: false,
    alignment: Alignment.bottomCenter,
    builder: (_) => Container(
      constraints: BoxConstraints(maxHeight: screen.screenHeight * 0.7),
      width: double.infinity,
      padding: EdgeInsets.only(
        bottom: screen.paddingBottom + pt(10),
        top: pt(19),
        left: pt(0),
        right: pt(0),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(pt(20)),
          topRight: Radius.circular(pt(20)),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    ),
  );
}
