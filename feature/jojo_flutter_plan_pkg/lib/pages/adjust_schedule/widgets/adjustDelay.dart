import 'dart:async';
import 'package:jojo_flutter_base/resources/jojo_icons.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/state.dart';
import 'package:jojo_flutter_plan_pkg/utils/color_util.dart';
// import 'package:jojo_flutter_plan_pkg/utils/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/utils/pt.dart';
// import 'package:jojo_flutter_plan_pkg/widgets/common/btn.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/empty.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/inner_shadow.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';

import 'package:jojo_flutter_base/utils/pt.dart';

import '../controller.dart';
import 'actionsheet.dart';

class AdjustViewDelay extends StatelessWidget {
  final AdjustScheduleState state;

  const AdjustViewDelay(this.state, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var eventName = "";
    if (RunEnv.isWeb && !RunEnv.isApp) {
      eventName = 'autoTrack';
    } else {
      eventName = '\$AppViewScreen';
    }
    RunEnv.sensorsTrack(
      eventName,
      {
        '\$screen_name': '计划调整_延后开启',
        'custom_state':
            (state.laterAdjustData.transClassPlanList ?? []).isNotEmpty
                ? 'not empty'
                : 'empty',
        'material_custom': '${state.laterAdjustData.transNum ?? 0}',
        "course_type": state.queryParams['courseType'] == '1' ? '训练营' : '年课',
      },
    );
    var isShowAdjust = false;
    if (context.read<AdjustScheduleCtrl>().state.isShowTwo) {
      isShowAdjust = true;
    }
    final _adjustData =
        context.read<AdjustScheduleCtrl>().state.laterAdjustData;
    if (!context.read<AdjustScheduleCtrl>().state.isShowTwo) {
      Timer.periodic(const Duration(milliseconds: 300), (timer) {
        if (context == null || !context.mounted) {
          timer.cancel();
          return;
        }
        isShowAdjust = true;
        ruleExplain(context, AdjustType.later);
        context.read<AdjustScheduleCtrl>().state.isShowTwo = true;
        timer.cancel();
      });
    }
    final _transClassPlanList = _adjustData.transClassPlanList ?? [];
    Widget _empty = empty(
        tip: '暂无可调整的计划',
        otherChild: Column(
          children: [
            SizedBox(height: pt(20)),
            JoJoBtn(
              width: pt(132),
              height: pt(44),
              text: '查看规则',
              tapHandle: () {
                ruleExplain(context, AdjustType.later);
              },
            )
          ],
        ));
    return Container(
      padding: EdgeInsets.symmetric(vertical: pt(0), horizontal: pt(20)),
      child: DefaultTextStyle(
        style: TextStyle(fontSize: pt(14)),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(bottom: pt(20), top: pt(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        '以下计划可延后开启',
                        style: TextStyle(
                          fontSize: pt(14, isFontSize: true),
                          color: const Color(0xff666666),
                          textBaseline: TextBaseline.alphabetic,
                        ),
                      ),
                      SizedBox(width: pt(8)),
                      SizedBox(
                        child: GestureDetector(
                          onTap: () {
                            ruleExplain(context, AdjustType.later);
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.ideographic,
                            // crossAxisAlignment: RunEnv.isWeb
                            //     ? CrossAxisAlignment.start
                            //     : CrossAxisAlignment.end,
                            children: [
                              Transform.translate(
                                  offset: Offset(0, pt(1)),
                                  child: Icon(
                                    JoJoDesignIcons.help1,
                                    size: pt(17),
                                    color: const Color(0xffb2b2b2),
                                  )),
                              Text(
                                '查看规则',
                                style: TextStyle(
                                  fontSize: pt(14, isFontSize: true),
                                  color: const Color(0xffb2b2b2),
                                  textBaseline: TextBaseline.alphabetic,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                  GestureDetector(
                    onTap: () {
                      if (isShowAdjust) {
                        var _courseType = context
                            .read<AdjustScheduleCtrl>()
                            .state
                            .queryParams['courseType'];

                        JoJoRouter.push(
                          url:
                              "${AppPage.classAdjustRecord.path}?type=${AdjustType.later}&courseType=$_courseType",
                        );
                      }
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.ideographic,
                      // crossAxisAlignment: RunEnv.isWeb
                      //     ? CrossAxisAlignment.start
                      //     : CrossAxisAlignment.end,
                      children: [
                        Transform.translate(
                          offset: Offset(0, pt(1)),
                          child: Icon(
                            JoJoDesignIcons.record,
                            size: pt(17),
                            color: const Color(0xff6491D9),
                          ),
                        ),
                        Transform.translate(
                          offset: Offset(0, pt(1)),
                          child: Text(
                            '调整记录',
                            style: TextStyle(
                                fontSize: pt(14, isFontSize: true),
                                color: const Color(0xff6491D9),
                                textBaseline: TextBaseline.alphabetic),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: _transClassPlanList.isEmpty
                  ? _empty
                  : SingleChildScrollView(
                      child: Column(
                        children:
                            List.generate(_transClassPlanList.length, (index) {
                          final _item = _transClassPlanList[index];
                          final _colors = getColors(_item.subjectType ?? 2);
                          return InnerShadow(
                            blur: pt(6),
                            offset: const Offset(0, -6),
                            color: _colors[1].withOpacity(0.8),
                            child: Container(
                              width: dp(335),
                              margin: EdgeInsets.only(bottom: pt(20)),
                              padding: EdgeInsets.all(pt(16)),
                              decoration: BoxDecoration(
                                color: _colors[0],
                                borderRadius: BorderRadius.circular(pt(24)),
                              ),
                              child: Column(children: [
                                Text(
                                  _item.courseName ?? '',
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      fontSize: pt(16),
                                      color: _colors[3],
                                      fontWeight: FontWeight.w500),
                                ),
                                SizedBox(height: pt(12)),
                                Container(
                                  width: dp(303),
                                  padding: EdgeInsets.all(pt(16)),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius:
                                          BorderRadius.circular(pt(16)),
                                      boxShadow: [
                                        BoxShadow(
                                          spreadRadius: 0,
                                          blurRadius: pt(4),
                                          offset: Offset(0, pt(4)),
                                          color: Colors.white.withOpacity(0.2),
                                        )
                                      ]),
                                  child: Column(children: [
                                    Text.rich(
                                      TextSpan(
                                        text: _item.termExplain,
                                        style: TextStyle(
                                            fontSize: pt(20),
                                            color: _colors[3],
                                            fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                    SizedBox(height: pt(4)),
                                    Text.rich(
                                      TextSpan(children: [
                                        TextSpan(
                                          text: '原开始时间：',
                                          style: TextStyle(
                                            fontSize: pt(16),
                                            color: const Color(0xffb2b2b2),
                                          ),
                                        ),
                                        TextSpan(
                                          text: _item.sourceClassStartDate,
                                          style: TextStyle(
                                            fontSize: pt(16),
                                            color: const Color(0xffb2b2b2),
                                          ),
                                        ),
                                      ]),
                                    ),
                                    SizedBox(height: pt(8)),
                                    JoJoBtn(
                                        text: '延后开启',
                                        height: pt(44),
                                        fontSize: pt(18),
                                        tapHandle: () {
                                          var sensorsData = {
                                            "course_type": state.queryParams[
                                                        'courseType'] ==
                                                    '1'
                                                ? '训练营'
                                                : '年课',
                                            "user_state":
                                                _item.userCourseStatusName,
                                            "custom_state":
                                                _item.subjectTypeName,
                                            "course_stage": _item.courseSegment,
                                            "course_name": _item.courseName,
                                          };
                                          RunEnv.sensorsTrack(
                                            '\$AppClick',
                                            {
                                              '\$element_name': '延后开启按钮',
                                              ...sensorsData,
                                            },
                                          );

                                          RunEnv.sensorsTrack(
                                            'ElementView',
                                            {
                                              'c_element_name': '延后开启_班期选择',
                                              ...sensorsData,
                                            },
                                          );

                                          ruleDelayAdjustTimeDialog(
                                            context,
                                            _item,
                                            sensorsData,
                                          );
                                        })
                                  ]),
                                )
                              ]),
                            ),
                          );
                        }),
                      ),
                    ),
            )
          ],
        ),
      ),
    );
  }
}
