import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/view.dart';

import 'controller.dart';

class AdjustSchedulePageModel extends BasePage {
  final Map<String, String> queryParams;

  const AdjustSchedulePageModel({
    Key? key,
    required this.queryParams,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AdjustSchedulePageModelState();
}

class _AdjustSchedulePageModelState extends BaseState<AdjustSchedulePageModel>
    with BasicInitPage {
  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => AdjustScheduleCtrl(
        queryParams: widget.queryParams,
      ),
      child: BlocBuilder<AdjustScheduleCtrl, AdjustScheduleState>(
          builder: (context, state) {
        return AdjustScheduleView(
          state: state,
          pageType: widget.queryParams['pageType'],
        );
      }),
    );
  }
}
