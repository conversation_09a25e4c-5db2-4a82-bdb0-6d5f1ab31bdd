// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adjust_plan_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AdjustPlan _$$_AdjustPlanFromJson(Map<String, dynamic> json) =>
    _$_AdjustPlan(
      transClassPlanList: (json['transClassPlanList'] as List<dynamic>?)
          ?.map((e) => TransClassPlanList.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactCustomerContentList: (json['contactCustomerContentList']
              as List<dynamic>?)
          ?.map((e) =>
              ContactCustomerContentList.fromJson(e as Map<String, dynamic>))
          .toList(),
      advanceStartExplain: json['advanceStartExplain'] as String?,
      advanceStartConfirm: json['advanceStartConfirm'] as String?,
      advanceStartAddTeacher: json['advanceStartAddTeacher'] as String?,
      advanceStartClearData: json['advanceStartClearData'] as String?,
      transNum: json['transNum'] as int?,
    );

Map<String, dynamic> _$$_AdjustPlanToJson(_$_AdjustPlan instance) =>
    <String, dynamic>{
      'transClassPlanList': instance.transClassPlanList,
      'contactCustomerContentList': instance.contactCustomerContentList,
      'advanceStartExplain': instance.advanceStartExplain,
      'advanceStartConfirm': instance.advanceStartConfirm,
      'advanceStartAddTeacher': instance.advanceStartAddTeacher,
      'advanceStartClearData': instance.advanceStartClearData,
      'transNum': instance.transNum,
    };

_$_TargetClassInfo _$$_TargetClassInfoFromJson(Map<String, dynamic> json) =>
    _$_TargetClassInfo(
      targetClassId: json['targetClassId'] as int?,
      targetClassStartDate: json['targetClassStartDate'] as String?,
      hasTeacher: json['hasTeacher'] as int?,
    );

Map<String, dynamic> _$$_TargetClassInfoToJson(_$_TargetClassInfo instance) =>
    <String, dynamic>{
      'targetClassId': instance.targetClassId,
      'targetClassStartDate': instance.targetClassStartDate,
      'hasTeacher': instance.hasTeacher,
    };

_$_TransClassPlanList _$$_TransClassPlanListFromJson(
        Map<String, dynamic> json) =>
    _$_TransClassPlanList(
      baseSkuNo: json['baseSkuNo'] as String?,
      courseKey: json['courseKey'] as String?,
      courseName: json['courseName'] as String?,
      sourceClassId: json['sourceClassId'] as int?,
      sourceClassStartDate: json['sourceClassStartDate'] as String?,
      subjectType: json['subjectType'] as int?,
      termExplain: json['termExplain'] as String?,
      targetClassList: (json['targetClassList'] as List<dynamic>?)
          ?.map((e) => TargetClassInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      subjectTypeName: json['subjectTypeName'] as String?,
      userCourseStatusName: json['userCourseStatusName'] as String?,
      courseSegment: json['courseSegment'] as String?,
    );

Map<String, dynamic> _$$_TransClassPlanListToJson(
        _$_TransClassPlanList instance) =>
    <String, dynamic>{
      'baseSkuNo': instance.baseSkuNo,
      'courseKey': instance.courseKey,
      'courseName': instance.courseName,
      'sourceClassId': instance.sourceClassId,
      'sourceClassStartDate': instance.sourceClassStartDate,
      'subjectType': instance.subjectType,
      'termExplain': instance.termExplain,
      'targetClassList': instance.targetClassList,
      'subjectTypeName': instance.subjectTypeName,
      'userCourseStatusName': instance.userCourseStatusName,
      'courseSegment': instance.courseSegment,
    };

_$_AdjustOperationResult _$$_AdjustOperationResultFromJson(
        Map<String, dynamic> json) =>
    _$_AdjustOperationResult(
      actionId: json['actionId'] as String?,
      actionStatus: json['actionStatus'] as String?,
      addTeacherRoute: json['addTeacherRoute'] as String?,
    );

Map<String, dynamic> _$$_AdjustOperationResultToJson(
        _$_AdjustOperationResult instance) =>
    <String, dynamic>{
      'actionId': instance.actionId,
      'actionStatus': instance.actionStatus,
      'addTeacherRoute': instance.addTeacherRoute,
    };
