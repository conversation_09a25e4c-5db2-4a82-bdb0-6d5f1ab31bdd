import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/model/contact_content_type.dart';

part 'adjust_plan_type.freezed.dart';
part 'adjust_plan_type.g.dart';

@freezed
class AdjustPlan with _$AdjustPlan {
  const factory AdjustPlan({
    List<TransClassPlanList>? transClassPlanList,
    List<ContactCustomerContentList>? contactCustomerContentList,
    String? advanceStartExplain,
    String? advanceStartConfirm,
    String? advanceStartAddTeacher,
    String? advanceStartClearData,
    int? transNum,
  }) = _AdjustPlan;
  factory AdjustPlan.fromJson(Map<String, dynamic> json) =>
      _$AdjustPlanFromJson(json);
}

@freezed
class TargetClassInfo with _$TargetClassInfo {
  const factory TargetClassInfo({
    int? targetClassId,

    ///新班期Id
    String? targetClassStartDate,

    ///新班期开班时间
    int? hasTeacher,

    ///新班级是否有老师，1有老师，0无老师
  }) = _TargetClassInfo;
  factory TargetClassInfo.fromJson(Map<String, dynamic> json) =>
      _$TargetClassInfoFromJson(json);
}

@freezed
class TransClassPlanList with _$TransClassPlanList {
  const factory TransClassPlanList({
    /// 编码
    String? baseSkuNo,
    String? courseKey,
    String? courseName,

    /// 老班期ID
    int? sourceClassId,

    /// 老班期开课时间
    String? sourceClassStartDate,

    /// 科目类型
    int? subjectType,

    /// 延后开启获取文案
    String? termExplain,

    ///目标班期列表信息
    List<TargetClassInfo>? targetClassList,

    /// 科目类型名称
    String? subjectTypeName,

    /// 用户课程状态名称
    String? userCourseStatusName,

    /// 课程阶段
    String? courseSegment,
  }) = _TransClassPlanList;
  factory TransClassPlanList.fromJson(Map<String, dynamic> json) =>
      _$TransClassPlanListFromJson(json);
}

@freezed
class AdjustOperationResult with _$AdjustOperationResult {
  const factory AdjustOperationResult({
    String? actionId,

    /// 执行结果 WAIT:待执行, IN_PROGRESS:进行中, SUCCESS:执行成功, FAILED:执行失败
    String? actionStatus,
    String? addTeacherRoute,
  }) = _AdjustOperationResult;
  factory AdjustOperationResult.fromJson(Map<String, dynamic> json) =>
      _$AdjustOperationResultFromJson(json);
}
