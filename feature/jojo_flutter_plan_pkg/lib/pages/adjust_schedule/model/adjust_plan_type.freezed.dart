// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'adjust_plan_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AdjustPlan _$AdjustPlanFromJson(Map<String, dynamic> json) {
  return _AdjustPlan.fromJson(json);
}

/// @nodoc
mixin _$AdjustPlan {
  List<TransClassPlanList>? get transClassPlanList =>
      throw _privateConstructorUsedError;
  List<ContactCustomerContentList>? get contactCustomerContentList =>
      throw _privateConstructorUsedError;
  String? get advanceStartExplain => throw _privateConstructorUsedError;
  String? get advanceStartConfirm => throw _privateConstructorUsedError;
  String? get advanceStartAddTeacher => throw _privateConstructorUsedError;
  String? get advanceStartClearData => throw _privateConstructorUsedError;
  int? get transNum => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdjustPlanCopyWith<AdjustPlan> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdjustPlanCopyWith<$Res> {
  factory $AdjustPlanCopyWith(
          AdjustPlan value, $Res Function(AdjustPlan) then) =
      _$AdjustPlanCopyWithImpl<$Res, AdjustPlan>;
  @useResult
  $Res call(
      {List<TransClassPlanList>? transClassPlanList,
      List<ContactCustomerContentList>? contactCustomerContentList,
      String? advanceStartExplain,
      String? advanceStartConfirm,
      String? advanceStartAddTeacher,
      String? advanceStartClearData,
      int? transNum});
}

/// @nodoc
class _$AdjustPlanCopyWithImpl<$Res, $Val extends AdjustPlan>
    implements $AdjustPlanCopyWith<$Res> {
  _$AdjustPlanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transClassPlanList = freezed,
    Object? contactCustomerContentList = freezed,
    Object? advanceStartExplain = freezed,
    Object? advanceStartConfirm = freezed,
    Object? advanceStartAddTeacher = freezed,
    Object? advanceStartClearData = freezed,
    Object? transNum = freezed,
  }) {
    return _then(_value.copyWith(
      transClassPlanList: freezed == transClassPlanList
          ? _value.transClassPlanList
          : transClassPlanList // ignore: cast_nullable_to_non_nullable
              as List<TransClassPlanList>?,
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value.contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<ContactCustomerContentList>?,
      advanceStartExplain: freezed == advanceStartExplain
          ? _value.advanceStartExplain
          : advanceStartExplain // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartConfirm: freezed == advanceStartConfirm
          ? _value.advanceStartConfirm
          : advanceStartConfirm // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartAddTeacher: freezed == advanceStartAddTeacher
          ? _value.advanceStartAddTeacher
          : advanceStartAddTeacher // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartClearData: freezed == advanceStartClearData
          ? _value.advanceStartClearData
          : advanceStartClearData // ignore: cast_nullable_to_non_nullable
              as String?,
      transNum: freezed == transNum
          ? _value.transNum
          : transNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdjustPlanCopyWith<$Res>
    implements $AdjustPlanCopyWith<$Res> {
  factory _$$_AdjustPlanCopyWith(
          _$_AdjustPlan value, $Res Function(_$_AdjustPlan) then) =
      __$$_AdjustPlanCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TransClassPlanList>? transClassPlanList,
      List<ContactCustomerContentList>? contactCustomerContentList,
      String? advanceStartExplain,
      String? advanceStartConfirm,
      String? advanceStartAddTeacher,
      String? advanceStartClearData,
      int? transNum});
}

/// @nodoc
class __$$_AdjustPlanCopyWithImpl<$Res>
    extends _$AdjustPlanCopyWithImpl<$Res, _$_AdjustPlan>
    implements _$$_AdjustPlanCopyWith<$Res> {
  __$$_AdjustPlanCopyWithImpl(
      _$_AdjustPlan _value, $Res Function(_$_AdjustPlan) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transClassPlanList = freezed,
    Object? contactCustomerContentList = freezed,
    Object? advanceStartExplain = freezed,
    Object? advanceStartConfirm = freezed,
    Object? advanceStartAddTeacher = freezed,
    Object? advanceStartClearData = freezed,
    Object? transNum = freezed,
  }) {
    return _then(_$_AdjustPlan(
      transClassPlanList: freezed == transClassPlanList
          ? _value._transClassPlanList
          : transClassPlanList // ignore: cast_nullable_to_non_nullable
              as List<TransClassPlanList>?,
      contactCustomerContentList: freezed == contactCustomerContentList
          ? _value._contactCustomerContentList
          : contactCustomerContentList // ignore: cast_nullable_to_non_nullable
              as List<ContactCustomerContentList>?,
      advanceStartExplain: freezed == advanceStartExplain
          ? _value.advanceStartExplain
          : advanceStartExplain // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartConfirm: freezed == advanceStartConfirm
          ? _value.advanceStartConfirm
          : advanceStartConfirm // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartAddTeacher: freezed == advanceStartAddTeacher
          ? _value.advanceStartAddTeacher
          : advanceStartAddTeacher // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceStartClearData: freezed == advanceStartClearData
          ? _value.advanceStartClearData
          : advanceStartClearData // ignore: cast_nullable_to_non_nullable
              as String?,
      transNum: freezed == transNum
          ? _value.transNum
          : transNum // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdjustPlan with DiagnosticableTreeMixin implements _AdjustPlan {
  const _$_AdjustPlan(
      {final List<TransClassPlanList>? transClassPlanList,
      final List<ContactCustomerContentList>? contactCustomerContentList,
      this.advanceStartExplain,
      this.advanceStartConfirm,
      this.advanceStartAddTeacher,
      this.advanceStartClearData,
      this.transNum})
      : _transClassPlanList = transClassPlanList,
        _contactCustomerContentList = contactCustomerContentList;

  factory _$_AdjustPlan.fromJson(Map<String, dynamic> json) =>
      _$$_AdjustPlanFromJson(json);

  final List<TransClassPlanList>? _transClassPlanList;
  @override
  List<TransClassPlanList>? get transClassPlanList {
    final value = _transClassPlanList;
    if (value == null) return null;
    if (_transClassPlanList is EqualUnmodifiableListView)
      return _transClassPlanList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ContactCustomerContentList>? _contactCustomerContentList;
  @override
  List<ContactCustomerContentList>? get contactCustomerContentList {
    final value = _contactCustomerContentList;
    if (value == null) return null;
    if (_contactCustomerContentList is EqualUnmodifiableListView)
      return _contactCustomerContentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? advanceStartExplain;
  @override
  final String? advanceStartConfirm;
  @override
  final String? advanceStartAddTeacher;
  @override
  final String? advanceStartClearData;
  @override
  final int? transNum;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AdjustPlan(transClassPlanList: $transClassPlanList, contactCustomerContentList: $contactCustomerContentList, advanceStartExplain: $advanceStartExplain, advanceStartConfirm: $advanceStartConfirm, advanceStartAddTeacher: $advanceStartAddTeacher, advanceStartClearData: $advanceStartClearData, transNum: $transNum)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AdjustPlan'))
      ..add(DiagnosticsProperty('transClassPlanList', transClassPlanList))
      ..add(DiagnosticsProperty(
          'contactCustomerContentList', contactCustomerContentList))
      ..add(DiagnosticsProperty('advanceStartExplain', advanceStartExplain))
      ..add(DiagnosticsProperty('advanceStartConfirm', advanceStartConfirm))
      ..add(
          DiagnosticsProperty('advanceStartAddTeacher', advanceStartAddTeacher))
      ..add(DiagnosticsProperty('advanceStartClearData', advanceStartClearData))
      ..add(DiagnosticsProperty('transNum', transNum));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdjustPlan &&
            const DeepCollectionEquality()
                .equals(other._transClassPlanList, _transClassPlanList) &&
            const DeepCollectionEquality().equals(
                other._contactCustomerContentList,
                _contactCustomerContentList) &&
            (identical(other.advanceStartExplain, advanceStartExplain) ||
                other.advanceStartExplain == advanceStartExplain) &&
            (identical(other.advanceStartConfirm, advanceStartConfirm) ||
                other.advanceStartConfirm == advanceStartConfirm) &&
            (identical(other.advanceStartAddTeacher, advanceStartAddTeacher) ||
                other.advanceStartAddTeacher == advanceStartAddTeacher) &&
            (identical(other.advanceStartClearData, advanceStartClearData) ||
                other.advanceStartClearData == advanceStartClearData) &&
            (identical(other.transNum, transNum) ||
                other.transNum == transNum));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_transClassPlanList),
      const DeepCollectionEquality().hash(_contactCustomerContentList),
      advanceStartExplain,
      advanceStartConfirm,
      advanceStartAddTeacher,
      advanceStartClearData,
      transNum);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdjustPlanCopyWith<_$_AdjustPlan> get copyWith =>
      __$$_AdjustPlanCopyWithImpl<_$_AdjustPlan>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdjustPlanToJson(
      this,
    );
  }
}

abstract class _AdjustPlan implements AdjustPlan {
  const factory _AdjustPlan(
      {final List<TransClassPlanList>? transClassPlanList,
      final List<ContactCustomerContentList>? contactCustomerContentList,
      final String? advanceStartExplain,
      final String? advanceStartConfirm,
      final String? advanceStartAddTeacher,
      final String? advanceStartClearData,
      final int? transNum}) = _$_AdjustPlan;

  factory _AdjustPlan.fromJson(Map<String, dynamic> json) =
      _$_AdjustPlan.fromJson;

  @override
  List<TransClassPlanList>? get transClassPlanList;
  @override
  List<ContactCustomerContentList>? get contactCustomerContentList;
  @override
  String? get advanceStartExplain;
  @override
  String? get advanceStartConfirm;
  @override
  String? get advanceStartAddTeacher;
  @override
  String? get advanceStartClearData;
  @override
  int? get transNum;
  @override
  @JsonKey(ignore: true)
  _$$_AdjustPlanCopyWith<_$_AdjustPlan> get copyWith =>
      throw _privateConstructorUsedError;
}

TargetClassInfo _$TargetClassInfoFromJson(Map<String, dynamic> json) {
  return _TargetClassInfo.fromJson(json);
}

/// @nodoc
mixin _$TargetClassInfo {
  int? get targetClassId => throw _privateConstructorUsedError;

  ///新班期Id
  String? get targetClassStartDate => throw _privateConstructorUsedError;

  ///新班期开班时间
  int? get hasTeacher => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TargetClassInfoCopyWith<TargetClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TargetClassInfoCopyWith<$Res> {
  factory $TargetClassInfoCopyWith(
          TargetClassInfo value, $Res Function(TargetClassInfo) then) =
      _$TargetClassInfoCopyWithImpl<$Res, TargetClassInfo>;
  @useResult
  $Res call(
      {int? targetClassId, String? targetClassStartDate, int? hasTeacher});
}

/// @nodoc
class _$TargetClassInfoCopyWithImpl<$Res, $Val extends TargetClassInfo>
    implements $TargetClassInfoCopyWith<$Res> {
  _$TargetClassInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetClassId = freezed,
    Object? targetClassStartDate = freezed,
    Object? hasTeacher = freezed,
  }) {
    return _then(_value.copyWith(
      targetClassId: freezed == targetClassId
          ? _value.targetClassId
          : targetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      targetClassStartDate: freezed == targetClassStartDate
          ? _value.targetClassStartDate
          : targetClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      hasTeacher: freezed == hasTeacher
          ? _value.hasTeacher
          : hasTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TargetClassInfoCopyWith<$Res>
    implements $TargetClassInfoCopyWith<$Res> {
  factory _$$_TargetClassInfoCopyWith(
          _$_TargetClassInfo value, $Res Function(_$_TargetClassInfo) then) =
      __$$_TargetClassInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? targetClassId, String? targetClassStartDate, int? hasTeacher});
}

/// @nodoc
class __$$_TargetClassInfoCopyWithImpl<$Res>
    extends _$TargetClassInfoCopyWithImpl<$Res, _$_TargetClassInfo>
    implements _$$_TargetClassInfoCopyWith<$Res> {
  __$$_TargetClassInfoCopyWithImpl(
      _$_TargetClassInfo _value, $Res Function(_$_TargetClassInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetClassId = freezed,
    Object? targetClassStartDate = freezed,
    Object? hasTeacher = freezed,
  }) {
    return _then(_$_TargetClassInfo(
      targetClassId: freezed == targetClassId
          ? _value.targetClassId
          : targetClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      targetClassStartDate: freezed == targetClassStartDate
          ? _value.targetClassStartDate
          : targetClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      hasTeacher: freezed == hasTeacher
          ? _value.hasTeacher
          : hasTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TargetClassInfo
    with DiagnosticableTreeMixin
    implements _TargetClassInfo {
  const _$_TargetClassInfo(
      {this.targetClassId, this.targetClassStartDate, this.hasTeacher});

  factory _$_TargetClassInfo.fromJson(Map<String, dynamic> json) =>
      _$$_TargetClassInfoFromJson(json);

  @override
  final int? targetClassId;

  ///新班期Id
  @override
  final String? targetClassStartDate;

  ///新班期开班时间
  @override
  final int? hasTeacher;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TargetClassInfo(targetClassId: $targetClassId, targetClassStartDate: $targetClassStartDate, hasTeacher: $hasTeacher)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TargetClassInfo'))
      ..add(DiagnosticsProperty('targetClassId', targetClassId))
      ..add(DiagnosticsProperty('targetClassStartDate', targetClassStartDate))
      ..add(DiagnosticsProperty('hasTeacher', hasTeacher));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TargetClassInfo &&
            (identical(other.targetClassId, targetClassId) ||
                other.targetClassId == targetClassId) &&
            (identical(other.targetClassStartDate, targetClassStartDate) ||
                other.targetClassStartDate == targetClassStartDate) &&
            (identical(other.hasTeacher, hasTeacher) ||
                other.hasTeacher == hasTeacher));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, targetClassId, targetClassStartDate, hasTeacher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TargetClassInfoCopyWith<_$_TargetClassInfo> get copyWith =>
      __$$_TargetClassInfoCopyWithImpl<_$_TargetClassInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TargetClassInfoToJson(
      this,
    );
  }
}

abstract class _TargetClassInfo implements TargetClassInfo {
  const factory _TargetClassInfo(
      {final int? targetClassId,
      final String? targetClassStartDate,
      final int? hasTeacher}) = _$_TargetClassInfo;

  factory _TargetClassInfo.fromJson(Map<String, dynamic> json) =
      _$_TargetClassInfo.fromJson;

  @override
  int? get targetClassId;
  @override

  ///新班期Id
  String? get targetClassStartDate;
  @override

  ///新班期开班时间
  int? get hasTeacher;
  @override
  @JsonKey(ignore: true)
  _$$_TargetClassInfoCopyWith<_$_TargetClassInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

TransClassPlanList _$TransClassPlanListFromJson(Map<String, dynamic> json) {
  return _TransClassPlanList.fromJson(json);
}

/// @nodoc
mixin _$TransClassPlanList {
  /// 编码
  String? get baseSkuNo => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;

  /// 老班期ID
  int? get sourceClassId => throw _privateConstructorUsedError;

  /// 老班期开课时间
  String? get sourceClassStartDate => throw _privateConstructorUsedError;

  /// 科目类型
  int? get subjectType => throw _privateConstructorUsedError;

  /// 延后开启获取文案
  String? get termExplain => throw _privateConstructorUsedError;

  ///目标班期列表信息
  List<TargetClassInfo>? get targetClassList =>
      throw _privateConstructorUsedError;

  /// 科目类型名称
  String? get subjectTypeName => throw _privateConstructorUsedError;

  /// 用户课程状态名称
  String? get userCourseStatusName => throw _privateConstructorUsedError;

  /// 课程阶段
  String? get courseSegment => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TransClassPlanListCopyWith<TransClassPlanList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransClassPlanListCopyWith<$Res> {
  factory $TransClassPlanListCopyWith(
          TransClassPlanList value, $Res Function(TransClassPlanList) then) =
      _$TransClassPlanListCopyWithImpl<$Res, TransClassPlanList>;
  @useResult
  $Res call(
      {String? baseSkuNo,
      String? courseKey,
      String? courseName,
      int? sourceClassId,
      String? sourceClassStartDate,
      int? subjectType,
      String? termExplain,
      List<TargetClassInfo>? targetClassList,
      String? subjectTypeName,
      String? userCourseStatusName,
      String? courseSegment});
}

/// @nodoc
class _$TransClassPlanListCopyWithImpl<$Res, $Val extends TransClassPlanList>
    implements $TransClassPlanListCopyWith<$Res> {
  _$TransClassPlanListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseSkuNo = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? sourceClassId = freezed,
    Object? sourceClassStartDate = freezed,
    Object? subjectType = freezed,
    Object? termExplain = freezed,
    Object? targetClassList = freezed,
    Object? subjectTypeName = freezed,
    Object? userCourseStatusName = freezed,
    Object? courseSegment = freezed,
  }) {
    return _then(_value.copyWith(
      baseSkuNo: freezed == baseSkuNo
          ? _value.baseSkuNo
          : baseSkuNo // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceClassId: freezed == sourceClassId
          ? _value.sourceClassId
          : sourceClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      sourceClassStartDate: freezed == sourceClassStartDate
          ? _value.sourceClassStartDate
          : sourceClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      termExplain: freezed == termExplain
          ? _value.termExplain
          : termExplain // ignore: cast_nullable_to_non_nullable
              as String?,
      targetClassList: freezed == targetClassList
          ? _value.targetClassList
          : targetClassList // ignore: cast_nullable_to_non_nullable
              as List<TargetClassInfo>?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseStatusName: freezed == userCourseStatusName
          ? _value.userCourseStatusName
          : userCourseStatusName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TransClassPlanListCopyWith<$Res>
    implements $TransClassPlanListCopyWith<$Res> {
  factory _$$_TransClassPlanListCopyWith(_$_TransClassPlanList value,
          $Res Function(_$_TransClassPlanList) then) =
      __$$_TransClassPlanListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? baseSkuNo,
      String? courseKey,
      String? courseName,
      int? sourceClassId,
      String? sourceClassStartDate,
      int? subjectType,
      String? termExplain,
      List<TargetClassInfo>? targetClassList,
      String? subjectTypeName,
      String? userCourseStatusName,
      String? courseSegment});
}

/// @nodoc
class __$$_TransClassPlanListCopyWithImpl<$Res>
    extends _$TransClassPlanListCopyWithImpl<$Res, _$_TransClassPlanList>
    implements _$$_TransClassPlanListCopyWith<$Res> {
  __$$_TransClassPlanListCopyWithImpl(
      _$_TransClassPlanList _value, $Res Function(_$_TransClassPlanList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseSkuNo = freezed,
    Object? courseKey = freezed,
    Object? courseName = freezed,
    Object? sourceClassId = freezed,
    Object? sourceClassStartDate = freezed,
    Object? subjectType = freezed,
    Object? termExplain = freezed,
    Object? targetClassList = freezed,
    Object? subjectTypeName = freezed,
    Object? userCourseStatusName = freezed,
    Object? courseSegment = freezed,
  }) {
    return _then(_$_TransClassPlanList(
      baseSkuNo: freezed == baseSkuNo
          ? _value.baseSkuNo
          : baseSkuNo // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      sourceClassId: freezed == sourceClassId
          ? _value.sourceClassId
          : sourceClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      sourceClassStartDate: freezed == sourceClassStartDate
          ? _value.sourceClassStartDate
          : sourceClassStartDate // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      termExplain: freezed == termExplain
          ? _value.termExplain
          : termExplain // ignore: cast_nullable_to_non_nullable
              as String?,
      targetClassList: freezed == targetClassList
          ? _value._targetClassList
          : targetClassList // ignore: cast_nullable_to_non_nullable
              as List<TargetClassInfo>?,
      subjectTypeName: freezed == subjectTypeName
          ? _value.subjectTypeName
          : subjectTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseStatusName: freezed == userCourseStatusName
          ? _value.userCourseStatusName
          : userCourseStatusName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TransClassPlanList
    with DiagnosticableTreeMixin
    implements _TransClassPlanList {
  const _$_TransClassPlanList(
      {this.baseSkuNo,
      this.courseKey,
      this.courseName,
      this.sourceClassId,
      this.sourceClassStartDate,
      this.subjectType,
      this.termExplain,
      final List<TargetClassInfo>? targetClassList,
      this.subjectTypeName,
      this.userCourseStatusName,
      this.courseSegment})
      : _targetClassList = targetClassList;

  factory _$_TransClassPlanList.fromJson(Map<String, dynamic> json) =>
      _$$_TransClassPlanListFromJson(json);

  /// 编码
  @override
  final String? baseSkuNo;
  @override
  final String? courseKey;
  @override
  final String? courseName;

  /// 老班期ID
  @override
  final int? sourceClassId;

  /// 老班期开课时间
  @override
  final String? sourceClassStartDate;

  /// 科目类型
  @override
  final int? subjectType;

  /// 延后开启获取文案
  @override
  final String? termExplain;

  ///目标班期列表信息
  final List<TargetClassInfo>? _targetClassList;

  ///目标班期列表信息
  @override
  List<TargetClassInfo>? get targetClassList {
    final value = _targetClassList;
    if (value == null) return null;
    if (_targetClassList is EqualUnmodifiableListView) return _targetClassList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// 科目类型名称
  @override
  final String? subjectTypeName;

  /// 用户课程状态名称
  @override
  final String? userCourseStatusName;

  /// 课程阶段
  @override
  final String? courseSegment;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TransClassPlanList(baseSkuNo: $baseSkuNo, courseKey: $courseKey, courseName: $courseName, sourceClassId: $sourceClassId, sourceClassStartDate: $sourceClassStartDate, subjectType: $subjectType, termExplain: $termExplain, targetClassList: $targetClassList, subjectTypeName: $subjectTypeName, userCourseStatusName: $userCourseStatusName, courseSegment: $courseSegment)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TransClassPlanList'))
      ..add(DiagnosticsProperty('baseSkuNo', baseSkuNo))
      ..add(DiagnosticsProperty('courseKey', courseKey))
      ..add(DiagnosticsProperty('courseName', courseName))
      ..add(DiagnosticsProperty('sourceClassId', sourceClassId))
      ..add(DiagnosticsProperty('sourceClassStartDate', sourceClassStartDate))
      ..add(DiagnosticsProperty('subjectType', subjectType))
      ..add(DiagnosticsProperty('termExplain', termExplain))
      ..add(DiagnosticsProperty('targetClassList', targetClassList))
      ..add(DiagnosticsProperty('subjectTypeName', subjectTypeName))
      ..add(DiagnosticsProperty('userCourseStatusName', userCourseStatusName))
      ..add(DiagnosticsProperty('courseSegment', courseSegment));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TransClassPlanList &&
            (identical(other.baseSkuNo, baseSkuNo) ||
                other.baseSkuNo == baseSkuNo) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.sourceClassId, sourceClassId) ||
                other.sourceClassId == sourceClassId) &&
            (identical(other.sourceClassStartDate, sourceClassStartDate) ||
                other.sourceClassStartDate == sourceClassStartDate) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.termExplain, termExplain) ||
                other.termExplain == termExplain) &&
            const DeepCollectionEquality()
                .equals(other._targetClassList, _targetClassList) &&
            (identical(other.subjectTypeName, subjectTypeName) ||
                other.subjectTypeName == subjectTypeName) &&
            (identical(other.userCourseStatusName, userCourseStatusName) ||
                other.userCourseStatusName == userCourseStatusName) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      baseSkuNo,
      courseKey,
      courseName,
      sourceClassId,
      sourceClassStartDate,
      subjectType,
      termExplain,
      const DeepCollectionEquality().hash(_targetClassList),
      subjectTypeName,
      userCourseStatusName,
      courseSegment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TransClassPlanListCopyWith<_$_TransClassPlanList> get copyWith =>
      __$$_TransClassPlanListCopyWithImpl<_$_TransClassPlanList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TransClassPlanListToJson(
      this,
    );
  }
}

abstract class _TransClassPlanList implements TransClassPlanList {
  const factory _TransClassPlanList(
      {final String? baseSkuNo,
      final String? courseKey,
      final String? courseName,
      final int? sourceClassId,
      final String? sourceClassStartDate,
      final int? subjectType,
      final String? termExplain,
      final List<TargetClassInfo>? targetClassList,
      final String? subjectTypeName,
      final String? userCourseStatusName,
      final String? courseSegment}) = _$_TransClassPlanList;

  factory _TransClassPlanList.fromJson(Map<String, dynamic> json) =
      _$_TransClassPlanList.fromJson;

  @override

  /// 编码
  String? get baseSkuNo;
  @override
  String? get courseKey;
  @override
  String? get courseName;
  @override

  /// 老班期ID
  int? get sourceClassId;
  @override

  /// 老班期开课时间
  String? get sourceClassStartDate;
  @override

  /// 科目类型
  int? get subjectType;
  @override

  /// 延后开启获取文案
  String? get termExplain;
  @override

  ///目标班期列表信息
  List<TargetClassInfo>? get targetClassList;
  @override

  /// 科目类型名称
  String? get subjectTypeName;
  @override

  /// 用户课程状态名称
  String? get userCourseStatusName;
  @override

  /// 课程阶段
  String? get courseSegment;
  @override
  @JsonKey(ignore: true)
  _$$_TransClassPlanListCopyWith<_$_TransClassPlanList> get copyWith =>
      throw _privateConstructorUsedError;
}

AdjustOperationResult _$AdjustOperationResultFromJson(
    Map<String, dynamic> json) {
  return _AdjustOperationResult.fromJson(json);
}

/// @nodoc
mixin _$AdjustOperationResult {
  String? get actionId => throw _privateConstructorUsedError;

  /// 执行结果 WAIT:待执行, IN_PROGRESS:进行中, SUCCESS:执行成功, FAILED:执行失败
  String? get actionStatus => throw _privateConstructorUsedError;
  String? get addTeacherRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdjustOperationResultCopyWith<AdjustOperationResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdjustOperationResultCopyWith<$Res> {
  factory $AdjustOperationResultCopyWith(AdjustOperationResult value,
          $Res Function(AdjustOperationResult) then) =
      _$AdjustOperationResultCopyWithImpl<$Res, AdjustOperationResult>;
  @useResult
  $Res call({String? actionId, String? actionStatus, String? addTeacherRoute});
}

/// @nodoc
class _$AdjustOperationResultCopyWithImpl<$Res,
        $Val extends AdjustOperationResult>
    implements $AdjustOperationResultCopyWith<$Res> {
  _$AdjustOperationResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actionId = freezed,
    Object? actionStatus = freezed,
    Object? addTeacherRoute = freezed,
  }) {
    return _then(_value.copyWith(
      actionId: freezed == actionId
          ? _value.actionId
          : actionId // ignore: cast_nullable_to_non_nullable
              as String?,
      actionStatus: freezed == actionStatus
          ? _value.actionStatus
          : actionStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherRoute: freezed == addTeacherRoute
          ? _value.addTeacherRoute
          : addTeacherRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdjustOperationResultCopyWith<$Res>
    implements $AdjustOperationResultCopyWith<$Res> {
  factory _$$_AdjustOperationResultCopyWith(_$_AdjustOperationResult value,
          $Res Function(_$_AdjustOperationResult) then) =
      __$$_AdjustOperationResultCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? actionId, String? actionStatus, String? addTeacherRoute});
}

/// @nodoc
class __$$_AdjustOperationResultCopyWithImpl<$Res>
    extends _$AdjustOperationResultCopyWithImpl<$Res, _$_AdjustOperationResult>
    implements _$$_AdjustOperationResultCopyWith<$Res> {
  __$$_AdjustOperationResultCopyWithImpl(_$_AdjustOperationResult _value,
      $Res Function(_$_AdjustOperationResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? actionId = freezed,
    Object? actionStatus = freezed,
    Object? addTeacherRoute = freezed,
  }) {
    return _then(_$_AdjustOperationResult(
      actionId: freezed == actionId
          ? _value.actionId
          : actionId // ignore: cast_nullable_to_non_nullable
              as String?,
      actionStatus: freezed == actionStatus
          ? _value.actionStatus
          : actionStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherRoute: freezed == addTeacherRoute
          ? _value.addTeacherRoute
          : addTeacherRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdjustOperationResult
    with DiagnosticableTreeMixin
    implements _AdjustOperationResult {
  const _$_AdjustOperationResult(
      {this.actionId, this.actionStatus, this.addTeacherRoute});

  factory _$_AdjustOperationResult.fromJson(Map<String, dynamic> json) =>
      _$$_AdjustOperationResultFromJson(json);

  @override
  final String? actionId;

  /// 执行结果 WAIT:待执行, IN_PROGRESS:进行中, SUCCESS:执行成功, FAILED:执行失败
  @override
  final String? actionStatus;
  @override
  final String? addTeacherRoute;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AdjustOperationResult(actionId: $actionId, actionStatus: $actionStatus, addTeacherRoute: $addTeacherRoute)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AdjustOperationResult'))
      ..add(DiagnosticsProperty('actionId', actionId))
      ..add(DiagnosticsProperty('actionStatus', actionStatus))
      ..add(DiagnosticsProperty('addTeacherRoute', addTeacherRoute));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdjustOperationResult &&
            (identical(other.actionId, actionId) ||
                other.actionId == actionId) &&
            (identical(other.actionStatus, actionStatus) ||
                other.actionStatus == actionStatus) &&
            (identical(other.addTeacherRoute, addTeacherRoute) ||
                other.addTeacherRoute == addTeacherRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, actionId, actionStatus, addTeacherRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdjustOperationResultCopyWith<_$_AdjustOperationResult> get copyWith =>
      __$$_AdjustOperationResultCopyWithImpl<_$_AdjustOperationResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdjustOperationResultToJson(
      this,
    );
  }
}

abstract class _AdjustOperationResult implements AdjustOperationResult {
  const factory _AdjustOperationResult(
      {final String? actionId,
      final String? actionStatus,
      final String? addTeacherRoute}) = _$_AdjustOperationResult;

  factory _AdjustOperationResult.fromJson(Map<String, dynamic> json) =
      _$_AdjustOperationResult.fromJson;

  @override
  String? get actionId;
  @override

  /// 执行结果 WAIT:待执行, IN_PROGRESS:进行中, SUCCESS:执行成功, FAILED:执行失败
  String? get actionStatus;
  @override
  String? get addTeacherRoute;
  @override
  @JsonKey(ignore: true)
  _$$_AdjustOperationResultCopyWith<_$_AdjustOperationResult> get copyWith =>
      throw _privateConstructorUsedError;
}
