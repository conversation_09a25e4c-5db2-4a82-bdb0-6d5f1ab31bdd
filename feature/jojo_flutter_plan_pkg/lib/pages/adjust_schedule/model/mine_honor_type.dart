import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/base.dart';

part 'mine_honor_type.freezed.dart';
part 'mine_honor_type.g.dart';

@freezed
class MineHonorData with _$MineHonorData {
  const factory MineHonorData({
    int? certificateCount,
    String? certificateCountCopywriting,
    String? certificateCopywriting,
    String? certificateShareBtn,
    bool? sharedStatus,
    bool? isSelf,
    String? receiveCourseUrl,
    BabyInfo? babyInfo,
    CertificateWallShareContent? certificateWallShareContent,
    List<HonorCertificateList>? honorCertificateList,
  }) = _MineHonorData;
  factory MineHonorData.fromJson(Map<String, dynamic> json) =>
      _$MineHonorDataFromJson(json);
}

@freezed
class BabyInfo with _$BabyInfo {
  const factory BabyInfo({
    String? nickName,
    String? avatar,
    dynamic age,
  }) = _BabyInfo;
  factory BabyInfo.fromJson(Map<String, dynamic> json) =>
      _$BabyInfoFromJson(json);
}

@freezed
class CertificateWallShareContent with _$CertificateWallShareContent {
  const factory CertificateWallShareContent({
    String? obtainReasonCopywriting,
    String? obtainCountCopywriting,
    String? image,
    String? shareCopywriting,
    String? shareSubCopywriting,
    String? sharePage,
    String? shareImage,
  }) = _CertificateWallShareContent;
  factory CertificateWallShareContent.fromJson(Map<String, dynamic> json) =>
      _$CertificateWallShareContentFromJson(json);
}

@freezed
class HonorCertificateList with _$HonorCertificateList {
  const factory HonorCertificateList({
    String? courseName,
    String? courseSegment,
    String? segmentName,
    String? title,
    String? image,
    int? obtainCertificateTime,
    bool? readStatus,
    CertificateShareContent? certificateShareContent,
  }) = _HonorCertificateList;
  factory HonorCertificateList.fromJson(Map<String, dynamic> json) =>
      _$HonorCertificateListFromJson(json);
}

@freezed
class CertificateShareContent with _$CertificateShareContent {
  const factory CertificateShareContent({
    String? cardName,
    String? nickNameCopywriting,
    String? titleReasonCopywriting,
    String? titleCopywriting,
    String? obtainCertificateDate,
    String? praiseDetailSharePage,
    String? image,
    String? shareImage,
  }) = _CertificateShareContent;
  factory CertificateShareContent.fromJson(Map<String, dynamic> json) =>
      _$CertificateShareContentFromJson(json);
}
