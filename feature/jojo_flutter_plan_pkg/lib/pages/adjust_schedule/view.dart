import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/pt.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/model/contact_content_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/widgets/adjust.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/widgets/adjustDelay.dart';

import '../../widgets/common/jojo_undline_tab_indicator.dart';
import 'controller.dart';

class AdjustScheduleView extends HookWidget {
  final AdjustScheduleState state;
  final String? pageType;

  AdjustScheduleView({
    Key? key,
    required this.state,
    required this.pageType,
  }) : super(key: key);

  final tabs = ['提前开启', '延后开启'];

  var preCurrentIndex = 0;

  Widget tipsRender() {
    final List<ContactCustomerContentList> _contactCustomerContentList =
        state.aHeadAdjustData.contactCustomerContentList ?? [];
    return Container(
      color: Colors.white,
      child: SafeArea(
        top: false,
        child: Container(
          height: pt(70),
          width: screen.screenWidth,
          alignment: Alignment.center,
          child: RichText(
            text: TextSpan(
              children:
                  List.generate(_contactCustomerContentList.length, (index) {
                final _item = _contactCustomerContentList[index];
                return TextSpan(
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      if (_item.linkUrl?.isNotEmpty != true) {
                        return;
                      }
                      RunEnv.sensorsTrack('\$AppClick', {
                        '\$element_name': '物流_联系客服',
                        '\$title': '课程信息页',
                      });
                      RunEnv.jumpLink(_item.linkUrl ?? '');
                    },
                  text: "${_item.text}",
                  style: TextStyle(
                    fontSize: pt(14, isFontSize: true),
                    color: _item.color?.isNotEmpty == true
                        ? HexColor(_item.color ?? '')
                        : Colors.black,
                  ),
                );
              }),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final tabController = useTabController(initialLength: tabs.length);
    useEffect(() {
      if (state.aHeadAdjustData.advanceStartExplain != null &&
          RunEnv.isWeb &&
          !RunEnv.isApp) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          Timer.periodic(const Duration(seconds: 1), (timer) {
            switch (pageType) {
              case PageType.pageNative:
                RunEnv.setWebTitle('计划调整');
                break;
              case PageType.pageWebOpenAdvance:
                RunEnv.setWebTitle('提前开启');
                break;
              case PageType.pageWebOpenDelay:
                RunEnv.setWebTitle('延后开启');
                break;
              default:
                RunEnv.setWebTitle('计划调整');
            }
          });
        });
      }
      return null;
    }, [state.aHeadAdjustData.advanceStartExplain]);

    return Scaffold(
      primary: !JoJoRouter.isWindow,
      appBar: RunEnv.isWeb && !RunEnv.isApp
          ? null
          : const JoJoAppBar(
              title: '计划调整',
              backgroundColor: Colors.transparent,
              centerTitle: true,
            ),
      body: JoJoPageLoading(
        status: state.pageStatus,
        retry: () {
          context.read<AdjustScheduleCtrl>().initState(loading: true);
        },
        child: Column(
          children: [
            pageType != null && pageType != PageType.pageNative
                ? const SizedBox(width: 0, height: 0)
                : Container(
                    color: Colors.white,
                    child: TabBar(
                      controller: tabController,
                      isScrollable: false,
                      indicatorSize: TabBarIndicatorSize.tab,
                      indicatorWeight: 2,
                      indicator: const JoJoUnderlineTabIndicator(
                          width: 30,
                          borderSide:
                              BorderSide(color: Color(0xffFCDA00), width: 4)),
                      unselectedLabelColor: const Color(0xffB2B2B2),
                      labelColor: const Color(0xff404040),
                      labelStyle: const TextStyle(fontWeight: FontWeight.w600),
                      unselectedLabelStyle:
                          const TextStyle(fontWeight: FontWeight.normal),
                      tabs: List.generate(tabs.length, (index) {
                        return GestureDetector(
                          onTap: () {
                            tabController.animateTo(index);
                          },
                          child: Text(
                            tabs[index],
                            style:
                                TextStyle(fontSize: pt(16, isFontSize: true)),
                          ),
                        );
                      }),
                    ),
                  ),
            Expanded(
                child: pageType == PageType.pageWebOpenAdvance
                    ? AdjustView(state)
                    : pageType == PageType.pageWebOpenDelay
                        ? AdjustViewDelay(state)
                        : pageType == PageType.pageNative
                            ? contentWidget(state, tabController)
                            : contentWidget(state, tabController)),
            RunEnv.isWeb ? Container() : tipsRender()
          ],
        ),
      ),
    );
  }
}

Widget contentWidget(AdjustScheduleState state, TabController tabController) {
  return TabBarView(
    controller: tabController,
    children: [AdjustView(state), AdjustViewDelay(state)],
  );
}
