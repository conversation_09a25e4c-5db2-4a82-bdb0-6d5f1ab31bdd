import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import 'model/adjust_plan_type.dart';

enum StartStatus {
  /// 默认状态
  idle,

  /// 倒计时状态
  timing,
  fetching,
  success,
  fail,
  ;
}

class PageType {
  static const pageNative = "pageNative";

  /// flutter web 提前开启
  static const pageWebOpenAdvance = "pageWebOpenAdvance";

  ///flutter web 延后开启
  static const pageWebOpenDelay = "pageWebOpenDelay";
}

class AdjustType {
  /// 往前调期
  static const ahead = "AHEAD";

  /// 往后调期
  static const later = "LATER";
}

class AdjustScheduleState {
  PageStatus pageStatus;
  StartStatus startStatus;
  AdjustPlan aHeadAdjustData;
  AdjustPlan laterAdjustData;
  int count;
  bool isShowOne;
  bool isShowTwo;
  Map<String, String> queryParams;

  AdjustScheduleState({
    required this.pageStatus,
    required this.startStatus,
    required this.aHeadAdjustData,
    required this.laterAdjustData,
    required this.count,
    required this.isShowOne,
    required this.isShowTwo,
    required this.queryParams,
  });

  AdjustScheduleState copyWith() {
    return AdjustScheduleState(
      pageStatus: pageStatus,
      startStatus: startStatus,
      aHeadAdjustData: aHeadAdjustData,
      laterAdjustData: laterAdjustData,
      count: count,
      isShowOne: isShowOne,
      isShowTwo: isShowTwo,
      queryParams: queryParams,
    );
  }
}
