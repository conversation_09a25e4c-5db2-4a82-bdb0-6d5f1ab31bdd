import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/model/adjust_plan_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/state.dart';

import '../../service/adjust_api.dart';

class AdjustScheduleCtrl extends Cubit<AdjustScheduleState> {
  AdjustScheduleCtrl({required queryParams})
      : super(
          AdjustScheduleState(
            count: 0,
            pageStatus: PageStatus.loading,
            startStatus: StartStatus.timing,
            aHeadAdjustData: const AdjustPlan(),
            laterAdjustData: const AdjustPlan(),
            isShowOne: false,
            isShowTwo: false,
            queryParams: queryParams,
          ),
        ) {
    RunEnv.setWebTitle('计划调整');
    initState(loading: true);
  }

  initState({bool? loading = true}) async {
    if (loading == true) {
      emit(state.copyWith()..pageStatus = PageStatus.loading);
    }
    try {
      List<Future<AdjustPlan>> adjustList = [
        adjustPlanService.getAdjustPlan(
          AdjustType.ahead,
          state.queryParams['courseType'],
          state.queryParams['subjectType'],
        ),
        adjustPlanService.getAdjustPlan(
          AdjustType.later,
          state.queryParams['courseType'],
          state.queryParams['subjectType'],
        )
      ];
      final adjustData = await Future.wait(adjustList);
      emit(
        state.copyWith()
          ..aHeadAdjustData = adjustData[0]
          ..laterAdjustData = adjustData[1]
          ..pageStatus = PageStatus.success,
      );
    } catch (e) {
      emit(state.copyWith()..pageStatus = PageStatus.error);
    }
  }
}
