// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'math_lessons_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MathLessonsModel _$$_MathLessonsModelFromJson(Map<String, dynamic> json) =>
    _$_MathLessonsModel(
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => MathTagsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraData: json['extraData'] == null
          ? null
          : ExtraDataModel.fromJson(json['extraData'] as Map<String, dynamic>),
      weekList: (json['weekList'] as List<dynamic>?)
          ?.map((e) => MathWeekModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MathLessonsModelToJson(_$_MathLessonsModel instance) =>
    <String, dynamic>{
      'tags': instance.tags,
      'extraData': instance.extraData,
      'weekList': instance.weekList,
    };

_$_MathTagsModel _$$_MathTagsModelFromJson(Map<String, dynamic> json) =>
    _$_MathTagsModel(
      name: json['name'] as String?,
      code: json['code'] as int?,
      isSelect: json['isSelect'] as int?,
    );

Map<String, dynamic> _$$_MathTagsModelToJson(_$_MathTagsModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      'isSelect': instance.isSelect,
    };

_$_ExtraDataModel _$$_ExtraDataModelFromJson(Map<String, dynamic> json) =>
    _$_ExtraDataModel(
      lockVoice: json['lockVoice'] as String?,
      lockTips: json['lockTips'] as String?,
      bgColor: json['bgColor'] as String?,
      courseKey: json['courseKey'] as String?,
    );

Map<String, dynamic> _$$_ExtraDataModelToJson(_$_ExtraDataModel instance) =>
    <String, dynamic>{
      'lockVoice': instance.lockVoice,
      'lockTips': instance.lockTips,
      'bgColor': instance.bgColor,
      'courseKey': instance.courseKey,
    };

_$_WeekKey _$$_WeekKeyFromJson(Map<String, dynamic> json) => _$_WeekKey(
      weekId: json['weekId'] as int?,
      classId: json['classId'] as int?,
    );

Map<String, dynamic> _$$_WeekKeyToJson(_$_WeekKey instance) =>
    <String, dynamic>{
      'weekId': instance.weekId,
      'classId': instance.classId,
    };

_$_MathWeekModel _$$_MathWeekModelFromJson(Map<String, dynamic> json) =>
    _$_MathWeekModel(
      weekId: json['weekId'] as int?,
      classId: json['classId'] as int?,
      name: json['name'] as String?,
      weekTime: json['weekTime'] as int?,
      state: json['state'] as int?,
      current: json['current'] as int?,
      total: json['total'] as int?,
      isSelect: json['isSelect'] as int?,
      lessonList: (json['lessonList'] as List<dynamic>?)
          ?.map((e) => MathLessonModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MathWeekModelToJson(_$_MathWeekModel instance) =>
    <String, dynamic>{
      'weekId': instance.weekId,
      'classId': instance.classId,
      'name': instance.name,
      'weekTime': instance.weekTime,
      'state': instance.state,
      'current': instance.current,
      'total': instance.total,
      'isSelect': instance.isSelect,
      'lessonList': instance.lessonList,
    };

_$_MathLessonModel _$$_MathLessonModelFromJson(Map<String, dynamic> json) =>
    _$_MathLessonModel(
      lessonId: json['lessonId'] as int?,
      lessonName: json['lessonName'] as String?,
      lessonOrder: json['lessonOrder'] as int?,
      coverImageUrl: json['coverImageUrl'] as String?,
      state: json['state'] as int?,
      stateIcon: json['stateIcon'] as String?,
      lessonStartTime: json['lessonStartTime'] as int?,
      today: json['today'] as int?,
      jumpRoute: json['jumpRoute'] as String?,
      classId: json['classId'] as int?,
      segmentId: json['segmentId'] as int?,
      gif: json['gif'] as bool?,
    );

Map<String, dynamic> _$$_MathLessonModelToJson(_$_MathLessonModel instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'lessonName': instance.lessonName,
      'lessonOrder': instance.lessonOrder,
      'coverImageUrl': instance.coverImageUrl,
      'state': instance.state,
      'stateIcon': instance.stateIcon,
      'lessonStartTime': instance.lessonStartTime,
      'today': instance.today,
      'jumpRoute': instance.jumpRoute,
      'classId': instance.classId,
      'segmentId': instance.segmentId,
      'gif': instance.gif,
    };
