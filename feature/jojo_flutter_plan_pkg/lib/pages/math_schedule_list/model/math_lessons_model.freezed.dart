// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'math_lessons_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MathLessonsModel _$MathLessonsModelFromJson(Map<String, dynamic> json) {
  return _MathLessonsModel.fromJson(json);
}

/// @nodoc
mixin _$MathLessonsModel {
  /// 标签列表
  List<MathTagsModel>? get tags => throw _privateConstructorUsedError;

  /// 额外数据，例如锁住点击不能学的语音、背景颜色等
  ExtraDataModel? get extraData => throw _privateConstructorUsedError;

  /// 主题周列表
  List<MathWeekModel>? get weekList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MathLessonsModelCopyWith<MathLessonsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MathLessonsModelCopyWith<$Res> {
  factory $MathLessonsModelCopyWith(
          MathLessonsModel value, $Res Function(MathLessonsModel) then) =
      _$MathLessonsModelCopyWithImpl<$Res, MathLessonsModel>;
  @useResult
  $Res call(
      {List<MathTagsModel>? tags,
      ExtraDataModel? extraData,
      List<MathWeekModel>? weekList});

  $ExtraDataModelCopyWith<$Res>? get extraData;
}

/// @nodoc
class _$MathLessonsModelCopyWithImpl<$Res, $Val extends MathLessonsModel>
    implements $MathLessonsModelCopyWith<$Res> {
  _$MathLessonsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tags = freezed,
    Object? extraData = freezed,
    Object? weekList = freezed,
  }) {
    return _then(_value.copyWith(
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<MathTagsModel>?,
      extraData: freezed == extraData
          ? _value.extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as ExtraDataModel?,
      weekList: freezed == weekList
          ? _value.weekList
          : weekList // ignore: cast_nullable_to_non_nullable
              as List<MathWeekModel>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtraDataModelCopyWith<$Res>? get extraData {
    if (_value.extraData == null) {
      return null;
    }

    return $ExtraDataModelCopyWith<$Res>(_value.extraData!, (value) {
      return _then(_value.copyWith(extraData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MathLessonsModelCopyWith<$Res>
    implements $MathLessonsModelCopyWith<$Res> {
  factory _$$_MathLessonsModelCopyWith(
          _$_MathLessonsModel value, $Res Function(_$_MathLessonsModel) then) =
      __$$_MathLessonsModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MathTagsModel>? tags,
      ExtraDataModel? extraData,
      List<MathWeekModel>? weekList});

  @override
  $ExtraDataModelCopyWith<$Res>? get extraData;
}

/// @nodoc
class __$$_MathLessonsModelCopyWithImpl<$Res>
    extends _$MathLessonsModelCopyWithImpl<$Res, _$_MathLessonsModel>
    implements _$$_MathLessonsModelCopyWith<$Res> {
  __$$_MathLessonsModelCopyWithImpl(
      _$_MathLessonsModel _value, $Res Function(_$_MathLessonsModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tags = freezed,
    Object? extraData = freezed,
    Object? weekList = freezed,
  }) {
    return _then(_$_MathLessonsModel(
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<MathTagsModel>?,
      extraData: freezed == extraData
          ? _value.extraData
          : extraData // ignore: cast_nullable_to_non_nullable
              as ExtraDataModel?,
      weekList: freezed == weekList
          ? _value._weekList
          : weekList // ignore: cast_nullable_to_non_nullable
              as List<MathWeekModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MathLessonsModel implements _MathLessonsModel {
  _$_MathLessonsModel(
      {final List<MathTagsModel>? tags,
      this.extraData,
      final List<MathWeekModel>? weekList})
      : _tags = tags,
        _weekList = weekList;

  factory _$_MathLessonsModel.fromJson(Map<String, dynamic> json) =>
      _$$_MathLessonsModelFromJson(json);

  /// 标签列表
  final List<MathTagsModel>? _tags;

  /// 标签列表
  @override
  List<MathTagsModel>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// 额外数据，例如锁住点击不能学的语音、背景颜色等
  @override
  final ExtraDataModel? extraData;

  /// 主题周列表
  final List<MathWeekModel>? _weekList;

  /// 主题周列表
  @override
  List<MathWeekModel>? get weekList {
    final value = _weekList;
    if (value == null) return null;
    if (_weekList is EqualUnmodifiableListView) return _weekList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MathLessonsModel(tags: $tags, extraData: $extraData, weekList: $weekList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MathLessonsModel &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.extraData, extraData) ||
                other.extraData == extraData) &&
            const DeepCollectionEquality().equals(other._weekList, _weekList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_tags),
      extraData,
      const DeepCollectionEquality().hash(_weekList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MathLessonsModelCopyWith<_$_MathLessonsModel> get copyWith =>
      __$$_MathLessonsModelCopyWithImpl<_$_MathLessonsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MathLessonsModelToJson(
      this,
    );
  }
}

abstract class _MathLessonsModel implements MathLessonsModel {
  factory _MathLessonsModel(
      {final List<MathTagsModel>? tags,
      final ExtraDataModel? extraData,
      final List<MathWeekModel>? weekList}) = _$_MathLessonsModel;

  factory _MathLessonsModel.fromJson(Map<String, dynamic> json) =
      _$_MathLessonsModel.fromJson;

  @override

  /// 标签列表
  List<MathTagsModel>? get tags;
  @override

  /// 额外数据，例如锁住点击不能学的语音、背景颜色等
  ExtraDataModel? get extraData;
  @override

  /// 主题周列表
  List<MathWeekModel>? get weekList;
  @override
  @JsonKey(ignore: true)
  _$$_MathLessonsModelCopyWith<_$_MathLessonsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

MathTagsModel _$MathTagsModelFromJson(Map<String, dynamic> json) {
  return _MathTagsModel.fromJson(json);
}

/// @nodoc
mixin _$MathTagsModel {
  /// 标签名称
  String? get name => throw _privateConstructorUsedError;

  /// 标签码
  int? get code => throw _privateConstructorUsedError;

  /// 是否选择 (1 是, 0 否)
  int? get isSelect => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MathTagsModelCopyWith<MathTagsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MathTagsModelCopyWith<$Res> {
  factory $MathTagsModelCopyWith(
          MathTagsModel value, $Res Function(MathTagsModel) then) =
      _$MathTagsModelCopyWithImpl<$Res, MathTagsModel>;
  @useResult
  $Res call({String? name, int? code, int? isSelect});
}

/// @nodoc
class _$MathTagsModelCopyWithImpl<$Res, $Val extends MathTagsModel>
    implements $MathTagsModelCopyWith<$Res> {
  _$MathTagsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
    Object? isSelect = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelect: freezed == isSelect
          ? _value.isSelect
          : isSelect // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MathTagsModelCopyWith<$Res>
    implements $MathTagsModelCopyWith<$Res> {
  factory _$$_MathTagsModelCopyWith(
          _$_MathTagsModel value, $Res Function(_$_MathTagsModel) then) =
      __$$_MathTagsModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, int? code, int? isSelect});
}

/// @nodoc
class __$$_MathTagsModelCopyWithImpl<$Res>
    extends _$MathTagsModelCopyWithImpl<$Res, _$_MathTagsModel>
    implements _$$_MathTagsModelCopyWith<$Res> {
  __$$_MathTagsModelCopyWithImpl(
      _$_MathTagsModel _value, $Res Function(_$_MathTagsModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? code = freezed,
    Object? isSelect = freezed,
  }) {
    return _then(_$_MathTagsModel(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelect: freezed == isSelect
          ? _value.isSelect
          : isSelect // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MathTagsModel implements _MathTagsModel {
  _$_MathTagsModel({this.name, this.code, this.isSelect});

  factory _$_MathTagsModel.fromJson(Map<String, dynamic> json) =>
      _$$_MathTagsModelFromJson(json);

  /// 标签名称
  @override
  final String? name;

  /// 标签码
  @override
  final int? code;

  /// 是否选择 (1 是, 0 否)
  @override
  final int? isSelect;

  @override
  String toString() {
    return 'MathTagsModel(name: $name, code: $code, isSelect: $isSelect)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MathTagsModel &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.isSelect, isSelect) ||
                other.isSelect == isSelect));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, code, isSelect);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MathTagsModelCopyWith<_$_MathTagsModel> get copyWith =>
      __$$_MathTagsModelCopyWithImpl<_$_MathTagsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MathTagsModelToJson(
      this,
    );
  }
}

abstract class _MathTagsModel implements MathTagsModel {
  factory _MathTagsModel(
      {final String? name,
      final int? code,
      final int? isSelect}) = _$_MathTagsModel;

  factory _MathTagsModel.fromJson(Map<String, dynamic> json) =
      _$_MathTagsModel.fromJson;

  @override

  /// 标签名称
  String? get name;
  @override

  /// 标签码
  int? get code;
  @override

  /// 是否选择 (1 是, 0 否)
  int? get isSelect;
  @override
  @JsonKey(ignore: true)
  _$$_MathTagsModelCopyWith<_$_MathTagsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

ExtraDataModel _$ExtraDataModelFromJson(Map<String, dynamic> json) {
  return _ExtraDataModel.fromJson(json);
}

/// @nodoc
mixin _$ExtraDataModel {
  /// 锁住点击不能学的语音
  String? get lockVoice => throw _privateConstructorUsedError;
  String? get lockTips => throw _privateConstructorUsedError;

  /// 背景颜色
  String? get bgColor => throw _privateConstructorUsedError;

  /// 课程key
  String? get courseKey => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraDataModelCopyWith<ExtraDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraDataModelCopyWith<$Res> {
  factory $ExtraDataModelCopyWith(
          ExtraDataModel value, $Res Function(ExtraDataModel) then) =
      _$ExtraDataModelCopyWithImpl<$Res, ExtraDataModel>;
  @useResult
  $Res call(
      {String? lockVoice,
      String? lockTips,
      String? bgColor,
      String? courseKey});
}

/// @nodoc
class _$ExtraDataModelCopyWithImpl<$Res, $Val extends ExtraDataModel>
    implements $ExtraDataModelCopyWith<$Res> {
  _$ExtraDataModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lockVoice = freezed,
    Object? lockTips = freezed,
    Object? bgColor = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_value.copyWith(
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockTips: freezed == lockTips
          ? _value.lockTips
          : lockTips // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ExtraDataModelCopyWith<$Res>
    implements $ExtraDataModelCopyWith<$Res> {
  factory _$$_ExtraDataModelCopyWith(
          _$_ExtraDataModel value, $Res Function(_$_ExtraDataModel) then) =
      __$$_ExtraDataModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lockVoice,
      String? lockTips,
      String? bgColor,
      String? courseKey});
}

/// @nodoc
class __$$_ExtraDataModelCopyWithImpl<$Res>
    extends _$ExtraDataModelCopyWithImpl<$Res, _$_ExtraDataModel>
    implements _$$_ExtraDataModelCopyWith<$Res> {
  __$$_ExtraDataModelCopyWithImpl(
      _$_ExtraDataModel _value, $Res Function(_$_ExtraDataModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lockVoice = freezed,
    Object? lockTips = freezed,
    Object? bgColor = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_$_ExtraDataModel(
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockTips: freezed == lockTips
          ? _value.lockTips
          : lockTips // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ExtraDataModel implements _ExtraDataModel {
  _$_ExtraDataModel(
      {this.lockVoice, this.lockTips, this.bgColor, this.courseKey});

  factory _$_ExtraDataModel.fromJson(Map<String, dynamic> json) =>
      _$$_ExtraDataModelFromJson(json);

  /// 锁住点击不能学的语音
  @override
  final String? lockVoice;
  @override
  final String? lockTips;

  /// 背景颜色
  @override
  final String? bgColor;

  /// 课程key
  @override
  final String? courseKey;

  @override
  String toString() {
    return 'ExtraDataModel(lockVoice: $lockVoice, lockTips: $lockTips, bgColor: $bgColor, courseKey: $courseKey)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ExtraDataModel &&
            (identical(other.lockVoice, lockVoice) ||
                other.lockVoice == lockVoice) &&
            (identical(other.lockTips, lockTips) ||
                other.lockTips == lockTips) &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lockVoice, lockTips, bgColor, courseKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtraDataModelCopyWith<_$_ExtraDataModel> get copyWith =>
      __$$_ExtraDataModelCopyWithImpl<_$_ExtraDataModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtraDataModelToJson(
      this,
    );
  }
}

abstract class _ExtraDataModel implements ExtraDataModel {
  factory _ExtraDataModel(
      {final String? lockVoice,
      final String? lockTips,
      final String? bgColor,
      final String? courseKey}) = _$_ExtraDataModel;

  factory _ExtraDataModel.fromJson(Map<String, dynamic> json) =
      _$_ExtraDataModel.fromJson;

  @override

  /// 锁住点击不能学的语音
  String? get lockVoice;
  @override
  String? get lockTips;
  @override

  /// 背景颜色
  String? get bgColor;
  @override

  /// 课程key
  String? get courseKey;
  @override
  @JsonKey(ignore: true)
  _$$_ExtraDataModelCopyWith<_$_ExtraDataModel> get copyWith =>
      throw _privateConstructorUsedError;
}

WeekKey _$WeekKeyFromJson(Map<String, dynamic> json) {
  return _WeekKey.fromJson(json);
}

/// @nodoc
mixin _$WeekKey {
  int? get weekId => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WeekKeyCopyWith<WeekKey> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WeekKeyCopyWith<$Res> {
  factory $WeekKeyCopyWith(WeekKey value, $Res Function(WeekKey) then) =
      _$WeekKeyCopyWithImpl<$Res, WeekKey>;
  @useResult
  $Res call({int? weekId, int? classId});
}

/// @nodoc
class _$WeekKeyCopyWithImpl<$Res, $Val extends WeekKey>
    implements $WeekKeyCopyWith<$Res> {
  _$WeekKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_value.copyWith(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WeekKeyCopyWith<$Res> implements $WeekKeyCopyWith<$Res> {
  factory _$$_WeekKeyCopyWith(
          _$_WeekKey value, $Res Function(_$_WeekKey) then) =
      __$$_WeekKeyCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? weekId, int? classId});
}

/// @nodoc
class __$$_WeekKeyCopyWithImpl<$Res>
    extends _$WeekKeyCopyWithImpl<$Res, _$_WeekKey>
    implements _$$_WeekKeyCopyWith<$Res> {
  __$$_WeekKeyCopyWithImpl(_$_WeekKey _value, $Res Function(_$_WeekKey) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? classId = freezed,
  }) {
    return _then(_$_WeekKey(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_WeekKey implements _WeekKey {
  _$_WeekKey({this.weekId, this.classId});

  factory _$_WeekKey.fromJson(Map<String, dynamic> json) =>
      _$$_WeekKeyFromJson(json);

  @override
  final int? weekId;
  @override
  final int? classId;

  @override
  String toString() {
    return 'WeekKey(weekId: $weekId, classId: $classId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WeekKey &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.classId, classId) || other.classId == classId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, weekId, classId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WeekKeyCopyWith<_$_WeekKey> get copyWith =>
      __$$_WeekKeyCopyWithImpl<_$_WeekKey>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_WeekKeyToJson(
      this,
    );
  }
}

abstract class _WeekKey implements WeekKey {
  factory _WeekKey({final int? weekId, final int? classId}) = _$_WeekKey;

  factory _WeekKey.fromJson(Map<String, dynamic> json) = _$_WeekKey.fromJson;

  @override
  int? get weekId;
  @override
  int? get classId;
  @override
  @JsonKey(ignore: true)
  _$$_WeekKeyCopyWith<_$_WeekKey> get copyWith =>
      throw _privateConstructorUsedError;
}

MathWeekModel _$MathWeekModelFromJson(Map<String, dynamic> json) {
  return _MathWeekModel.fromJson(json);
}

/// @nodoc
mixin _$MathWeekModel {
  /// 周ID
  int? get weekId => throw _privateConstructorUsedError;

  /// 班级ID
  int? get classId => throw _privateConstructorUsedError;

  /// 主题周名称
  String? get name => throw _privateConstructorUsedError;

  /// 周的时间
  int? get weekTime => throw _privateConstructorUsedError;

  /// 状态: 0-未解锁、1-进行中、2-已完成
  int? get state => throw _privateConstructorUsedError;

  /// 当前完成数
  int? get current => throw _privateConstructorUsedError;

  /// 总数
  int? get total => throw _privateConstructorUsedError;

  /// 是否选择 (1 是, 0 否)
  int? get isSelect => throw _privateConstructorUsedError;

  /// 课时列表
  List<MathLessonModel>? get lessonList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MathWeekModelCopyWith<MathWeekModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MathWeekModelCopyWith<$Res> {
  factory $MathWeekModelCopyWith(
          MathWeekModel value, $Res Function(MathWeekModel) then) =
      _$MathWeekModelCopyWithImpl<$Res, MathWeekModel>;
  @useResult
  $Res call(
      {int? weekId,
      int? classId,
      String? name,
      int? weekTime,
      int? state,
      int? current,
      int? total,
      int? isSelect,
      List<MathLessonModel>? lessonList});
}

/// @nodoc
class _$MathWeekModelCopyWithImpl<$Res, $Val extends MathWeekModel>
    implements $MathWeekModelCopyWith<$Res> {
  _$MathWeekModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? classId = freezed,
    Object? name = freezed,
    Object? weekTime = freezed,
    Object? state = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isSelect = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_value.copyWith(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      weekTime: freezed == weekTime
          ? _value.weekTime
          : weekTime // ignore: cast_nullable_to_non_nullable
              as int?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelect: freezed == isSelect
          ? _value.isSelect
          : isSelect // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonList: freezed == lessonList
          ? _value.lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<MathLessonModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MathWeekModelCopyWith<$Res>
    implements $MathWeekModelCopyWith<$Res> {
  factory _$$_MathWeekModelCopyWith(
          _$_MathWeekModel value, $Res Function(_$_MathWeekModel) then) =
      __$$_MathWeekModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? weekId,
      int? classId,
      String? name,
      int? weekTime,
      int? state,
      int? current,
      int? total,
      int? isSelect,
      List<MathLessonModel>? lessonList});
}

/// @nodoc
class __$$_MathWeekModelCopyWithImpl<$Res>
    extends _$MathWeekModelCopyWithImpl<$Res, _$_MathWeekModel>
    implements _$$_MathWeekModelCopyWith<$Res> {
  __$$_MathWeekModelCopyWithImpl(
      _$_MathWeekModel _value, $Res Function(_$_MathWeekModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? weekId = freezed,
    Object? classId = freezed,
    Object? name = freezed,
    Object? weekTime = freezed,
    Object? state = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isSelect = freezed,
    Object? lessonList = freezed,
  }) {
    return _then(_$_MathWeekModel(
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      weekTime: freezed == weekTime
          ? _value.weekTime
          : weekTime // ignore: cast_nullable_to_non_nullable
              as int?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelect: freezed == isSelect
          ? _value.isSelect
          : isSelect // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonList: freezed == lessonList
          ? _value._lessonList
          : lessonList // ignore: cast_nullable_to_non_nullable
              as List<MathLessonModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MathWeekModel implements _MathWeekModel {
  _$_MathWeekModel(
      {this.weekId,
      this.classId,
      this.name,
      this.weekTime,
      this.state,
      this.current,
      this.total,
      this.isSelect,
      final List<MathLessonModel>? lessonList})
      : _lessonList = lessonList;

  factory _$_MathWeekModel.fromJson(Map<String, dynamic> json) =>
      _$$_MathWeekModelFromJson(json);

  /// 周ID
  @override
  final int? weekId;

  /// 班级ID
  @override
  final int? classId;

  /// 主题周名称
  @override
  final String? name;

  /// 周的时间
  @override
  final int? weekTime;

  /// 状态: 0-未解锁、1-进行中、2-已完成
  @override
  final int? state;

  /// 当前完成数
  @override
  final int? current;

  /// 总数
  @override
  final int? total;

  /// 是否选择 (1 是, 0 否)
  @override
  final int? isSelect;

  /// 课时列表
  final List<MathLessonModel>? _lessonList;

  /// 课时列表
  @override
  List<MathLessonModel>? get lessonList {
    final value = _lessonList;
    if (value == null) return null;
    if (_lessonList is EqualUnmodifiableListView) return _lessonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MathWeekModel(weekId: $weekId, classId: $classId, name: $name, weekTime: $weekTime, state: $state, current: $current, total: $total, isSelect: $isSelect, lessonList: $lessonList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MathWeekModel &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.weekTime, weekTime) ||
                other.weekTime == weekTime) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.isSelect, isSelect) ||
                other.isSelect == isSelect) &&
            const DeepCollectionEquality()
                .equals(other._lessonList, _lessonList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      weekId,
      classId,
      name,
      weekTime,
      state,
      current,
      total,
      isSelect,
      const DeepCollectionEquality().hash(_lessonList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MathWeekModelCopyWith<_$_MathWeekModel> get copyWith =>
      __$$_MathWeekModelCopyWithImpl<_$_MathWeekModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MathWeekModelToJson(
      this,
    );
  }
}

abstract class _MathWeekModel implements MathWeekModel {
  factory _MathWeekModel(
      {final int? weekId,
      final int? classId,
      final String? name,
      final int? weekTime,
      final int? state,
      final int? current,
      final int? total,
      final int? isSelect,
      final List<MathLessonModel>? lessonList}) = _$_MathWeekModel;

  factory _MathWeekModel.fromJson(Map<String, dynamic> json) =
      _$_MathWeekModel.fromJson;

  @override

  /// 周ID
  int? get weekId;
  @override

  /// 班级ID
  int? get classId;
  @override

  /// 主题周名称
  String? get name;
  @override

  /// 周的时间
  int? get weekTime;
  @override

  /// 状态: 0-未解锁、1-进行中、2-已完成
  int? get state;
  @override

  /// 当前完成数
  int? get current;
  @override

  /// 总数
  int? get total;
  @override

  /// 是否选择 (1 是, 0 否)
  int? get isSelect;
  @override

  /// 课时列表
  List<MathLessonModel>? get lessonList;
  @override
  @JsonKey(ignore: true)
  _$$_MathWeekModelCopyWith<_$_MathWeekModel> get copyWith =>
      throw _privateConstructorUsedError;
}

MathLessonModel _$MathLessonModelFromJson(Map<String, dynamic> json) {
  return _MathLessonModel.fromJson(json);
}

/// @nodoc
mixin _$MathLessonModel {
  /// 课时ID
  int? get lessonId => throw _privateConstructorUsedError;

  /// 课时名称
  String? get lessonName => throw _privateConstructorUsedError;

  /// 课时序号
  int? get lessonOrder => throw _privateConstructorUsedError;

  /// 封面图片 URL
  String? get coverImageUrl => throw _privateConstructorUsedError;

  /// 状态: 0 - 未解锁、1 - 已解锁未完成、2 - 已完成
  int? get state => throw _privateConstructorUsedError;

  /// 状态图标
  String? get stateIcon => throw _privateConstructorUsedError;

  /// 课时开始时间（时间戳）
  int? get lessonStartTime => throw _privateConstructorUsedError;

  /// 是否是今日正学 (1 是, 0 否)
  int? get today => throw _privateConstructorUsedError;

  /// 跳转路由
  String? get jumpRoute => throw _privateConstructorUsedError;

  /// 班级ID
  int? get classId => throw _privateConstructorUsedError;

  /// 阶段ID
  int? get segmentId => throw _privateConstructorUsedError;
  bool? get gif => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MathLessonModelCopyWith<MathLessonModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MathLessonModelCopyWith<$Res> {
  factory $MathLessonModelCopyWith(
          MathLessonModel value, $Res Function(MathLessonModel) then) =
      _$MathLessonModelCopyWithImpl<$Res, MathLessonModel>;
  @useResult
  $Res call(
      {int? lessonId,
      String? lessonName,
      int? lessonOrder,
      String? coverImageUrl,
      int? state,
      String? stateIcon,
      int? lessonStartTime,
      int? today,
      String? jumpRoute,
      int? classId,
      int? segmentId,
      bool? gif});
}

/// @nodoc
class _$MathLessonModelCopyWithImpl<$Res, $Val extends MathLessonModel>
    implements $MathLessonModelCopyWith<$Res> {
  _$MathLessonModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonOrder = freezed,
    Object? coverImageUrl = freezed,
    Object? state = freezed,
    Object? stateIcon = freezed,
    Object? lessonStartTime = freezed,
    Object? today = freezed,
    Object? jumpRoute = freezed,
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? gif = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      coverImageUrl: freezed == coverImageUrl
          ? _value.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      stateIcon: freezed == stateIcon
          ? _value.stateIcon
          : stateIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStartTime: freezed == lessonStartTime
          ? _value.lessonStartTime
          : lessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      gif: freezed == gif
          ? _value.gif
          : gif // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MathLessonModelCopyWith<$Res>
    implements $MathLessonModelCopyWith<$Res> {
  factory _$$_MathLessonModelCopyWith(
          _$_MathLessonModel value, $Res Function(_$_MathLessonModel) then) =
      __$$_MathLessonModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? lessonId,
      String? lessonName,
      int? lessonOrder,
      String? coverImageUrl,
      int? state,
      String? stateIcon,
      int? lessonStartTime,
      int? today,
      String? jumpRoute,
      int? classId,
      int? segmentId,
      bool? gif});
}

/// @nodoc
class __$$_MathLessonModelCopyWithImpl<$Res>
    extends _$MathLessonModelCopyWithImpl<$Res, _$_MathLessonModel>
    implements _$$_MathLessonModelCopyWith<$Res> {
  __$$_MathLessonModelCopyWithImpl(
      _$_MathLessonModel _value, $Res Function(_$_MathLessonModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonOrder = freezed,
    Object? coverImageUrl = freezed,
    Object? state = freezed,
    Object? stateIcon = freezed,
    Object? lessonStartTime = freezed,
    Object? today = freezed,
    Object? jumpRoute = freezed,
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? gif = freezed,
  }) {
    return _then(_$_MathLessonModel(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      coverImageUrl: freezed == coverImageUrl
          ? _value.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      stateIcon: freezed == stateIcon
          ? _value.stateIcon
          : stateIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonStartTime: freezed == lessonStartTime
          ? _value.lessonStartTime
          : lessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      gif: freezed == gif
          ? _value.gif
          : gif // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MathLessonModel implements _MathLessonModel {
  _$_MathLessonModel(
      {this.lessonId,
      this.lessonName,
      this.lessonOrder,
      this.coverImageUrl,
      this.state,
      this.stateIcon,
      this.lessonStartTime,
      this.today,
      this.jumpRoute,
      this.classId,
      this.segmentId,
      this.gif});

  factory _$_MathLessonModel.fromJson(Map<String, dynamic> json) =>
      _$$_MathLessonModelFromJson(json);

  /// 课时ID
  @override
  final int? lessonId;

  /// 课时名称
  @override
  final String? lessonName;

  /// 课时序号
  @override
  final int? lessonOrder;

  /// 封面图片 URL
  @override
  final String? coverImageUrl;

  /// 状态: 0 - 未解锁、1 - 已解锁未完成、2 - 已完成
  @override
  final int? state;

  /// 状态图标
  @override
  final String? stateIcon;

  /// 课时开始时间（时间戳）
  @override
  final int? lessonStartTime;

  /// 是否是今日正学 (1 是, 0 否)
  @override
  final int? today;

  /// 跳转路由
  @override
  final String? jumpRoute;

  /// 班级ID
  @override
  final int? classId;

  /// 阶段ID
  @override
  final int? segmentId;
  @override
  final bool? gif;

  @override
  String toString() {
    return 'MathLessonModel(lessonId: $lessonId, lessonName: $lessonName, lessonOrder: $lessonOrder, coverImageUrl: $coverImageUrl, state: $state, stateIcon: $stateIcon, lessonStartTime: $lessonStartTime, today: $today, jumpRoute: $jumpRoute, classId: $classId, segmentId: $segmentId, gif: $gif)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MathLessonModel &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.coverImageUrl, coverImageUrl) ||
                other.coverImageUrl == coverImageUrl) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.stateIcon, stateIcon) ||
                other.stateIcon == stateIcon) &&
            (identical(other.lessonStartTime, lessonStartTime) ||
                other.lessonStartTime == lessonStartTime) &&
            (identical(other.today, today) || other.today == today) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.gif, gif) || other.gif == gif));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      lessonId,
      lessonName,
      lessonOrder,
      coverImageUrl,
      state,
      stateIcon,
      lessonStartTime,
      today,
      jumpRoute,
      classId,
      segmentId,
      gif);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MathLessonModelCopyWith<_$_MathLessonModel> get copyWith =>
      __$$_MathLessonModelCopyWithImpl<_$_MathLessonModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MathLessonModelToJson(
      this,
    );
  }
}

abstract class _MathLessonModel implements MathLessonModel {
  factory _MathLessonModel(
      {final int? lessonId,
      final String? lessonName,
      final int? lessonOrder,
      final String? coverImageUrl,
      final int? state,
      final String? stateIcon,
      final int? lessonStartTime,
      final int? today,
      final String? jumpRoute,
      final int? classId,
      final int? segmentId,
      final bool? gif}) = _$_MathLessonModel;

  factory _MathLessonModel.fromJson(Map<String, dynamic> json) =
      _$_MathLessonModel.fromJson;

  @override

  /// 课时ID
  int? get lessonId;
  @override

  /// 课时名称
  String? get lessonName;
  @override

  /// 课时序号
  int? get lessonOrder;
  @override

  /// 封面图片 URL
  String? get coverImageUrl;
  @override

  /// 状态: 0 - 未解锁、1 - 已解锁未完成、2 - 已完成
  int? get state;
  @override

  /// 状态图标
  String? get stateIcon;
  @override

  /// 课时开始时间（时间戳）
  int? get lessonStartTime;
  @override

  /// 是否是今日正学 (1 是, 0 否)
  int? get today;
  @override

  /// 跳转路由
  String? get jumpRoute;
  @override

  /// 班级ID
  int? get classId;
  @override

  /// 阶段ID
  int? get segmentId;
  @override
  bool? get gif;
  @override
  @JsonKey(ignore: true)
  _$$_MathLessonModelCopyWith<_$_MathLessonModel> get copyWith =>
      throw _privateConstructorUsedError;
}
