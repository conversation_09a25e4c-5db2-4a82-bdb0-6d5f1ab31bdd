import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/spine_desc_impl.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/utils/milestion_show_strorge.dart';

mixin MilestionIncentiveMixin {
  static const String studyAbort = "study_abort"; //学习_学习中断
  static const String studyKeep = "study_keep"; //学习_学习保持
  static const String studyFirstFinish = "study_first_finish_kepp"; //学习_学习完成
  static const String studyFirstFinishAbort =
      "study_first_finish_abort"; //中断下的首次学习完成
  List<JoJoSpineAnimation> get boxAnimat => [
        JoJoSpineAnimation(
            animaitonName: "enter",
            trackIndex: 0,
            loop: false,
            listener: (AnimationEventType type) {
              if (type == AnimationEventType.complete) {
                boxAnimaOnEnd.call();
              }
            }),
      ];
  final jojoAnimaMap = {
    studyAbort: [
      JoJoSpineAnimation(animaitonName: "tranceLoop", trackIndex: 0, loop: true)
    ],
    studyKeep: [
      JoJoSpineAnimation(animaitonName: "fireLoop", trackIndex: 0, loop: true)
    ],
    studyFirstFinish: [
      JoJoSpineAnimation(animaitonName: "fireWin", trackIndex: 0, loop: false),
      JoJoSpineAnimation(animaitonName: "fireLoop", trackIndex: 0, loop: true)
    ],
    studyFirstFinishAbort: [
      JoJoSpineAnimation(
          animaitonName: "tranceToFire", trackIndex: 0, loop: false),
      JoJoSpineAnimation(animaitonName: "fireWin", trackIndex: 0, loop: false),
      JoJoSpineAnimation(
          animaitonName: "fireToTrance", trackIndex: 0, loop: false),
      JoJoSpineAnimation(
          animaitonName: "tranceLoop", trackIndex: 0, loop: true),
    ]
  };

  String get spineRes;

  SpineDescImpl get milesJoJoSpine => SpineDescImpl("top_ip", spineRes);

  SpineDescImpl get milesBoxSpine => SpineDescImpl("top_ip_box", spineRes);

  Function get boxAnimaOnEnd;

  String getStudyStateAnimatName(
      {required int status, required bool hasMilestone}) {
    if (hasMilestone) {
      if (status == 2) {
        return studyFirstFinish;
      } else {
        return studyFirstFinishAbort;
      }
    } else {
      if (status == 2) {
        return studyKeep;
      } else {
        return studyAbort;
      }
    }
  }

  ValueKey getReBuildSpineKey(SpineDescImpl spineDescImpl,
      int requestSubjectType, String progressText) {
    return ValueKey(
        '${spineDescImpl.atlasFile}-${spineDescImpl.skelFile}-$requestSubjectType-$progressText');
  }

  Future<bool> getFinishState(TopUserInfo? topUserInfo, int subjectType) async {
    final milestone = topUserInfo?.continuous?.milestone;
    if (milestone == null ||
        milestone.progressText == null ||
        milestone.progressText!.isEmpty) {
      l.i("里程碑动画", "milestone 信息 为空");
      return false;
    }
    final toDay = DateFormat("yyyy-MM-dd").format(DateTime.now());
    final bool canShow = await MilestionShowStrorge.instance
        .canShow(toDay: toDay, subjectType: subjectType);
    return canShow;
  }

  Future<void> saveFinishState(int subjectType) async {
    await MilestionShowStrorge.instance.saveSubject(
      DateFormat("yyyy-MM-dd").format(DateTime.now()),
      subjectType,
    );
  }

  Widget buildIconWidget();

  Widget buildSpineWidget(
    Size size,
    JoJoSpineAnimationController jojoController, {
    File? atlasFile,
    File? skelFile,
    Function? onInitialized,
    Key? key,
  }) {
    if (atlasFile == null || skelFile == null) {
      l.i("2025上课页科目动态资源",
          "里程碑动画顶部动效资源有问题,atlasFile:$atlasFile,skelFile:$skelFile");
      return buildIconWidget();
    }

    return SizedBox.fromSize(
      key: key,
      size: size,
      child: JoJoSpineAnimationWidget(
        atlasFile.path,
        skelFile.path,
        LoadMode.file,
        jojoController,
        onInitialized: (controller) {
          onInitialized?.call();
        },
      ),
    );
  }

  void reportClickTracking({required Map<String, dynamic> eventMap}) {
    RunEnv.sensorsTrack('\$AppClick', eventMap);
  }

  void startJoJoAnima({
    required bool isSubjectTypeEqual,
    required bool hasMilestone,
    required int animatStatus,
    required int subjectType,
    required JoJoSpineAnimationController spineController,
    TopUserInfo? topUserInfo,
  }) async {
    if (!isSubjectTypeEqual) {
      spineController.playAnimationList(jojoAnimaMap[
          getStudyStateAnimatName(status: animatStatus, hasMilestone: false)]!);
      return;
    }
    var canShow = await getFinishState(topUserInfo, subjectType);
    if (!canShow) {
      spineController.playAnimationList(jojoAnimaMap[
          getStudyStateAnimatName(status: animatStatus, hasMilestone: false)]!);
      return;
    }
    //只在有学习里程碑的时候才执行动画
    spineController.playAnimationList(jojoAnimaMap[getStudyStateAnimatName(
        status: animatStatus, hasMilestone: hasMilestone)]!);
  }
}
