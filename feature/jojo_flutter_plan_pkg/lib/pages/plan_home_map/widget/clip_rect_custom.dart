import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

// 自定义裁切控件,用于上课页的tab，只裁切下左右，不裁切上方
class CustomRectClipper extends CustomClipper<Rect> {
  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(0, -30.rdp, size.width, size.height);
  }

  @override
  bool shouldReclip(covariant CustomClipper<Rect> oldClipper) {
    return false; // 如果裁切的尺寸不随时间变化，返回 false
  }
}
