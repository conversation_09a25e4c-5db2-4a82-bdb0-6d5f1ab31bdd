import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:lottie/lottie.dart';

import '../../../../common/host_env/host_env.dart';
import '../../../../static/audio.dart';
import '../../../../static/lottie.dart';
import '../../../finish_course_settle_accounts/task_settle/widget/animations/animation_audio_mixin.dart';

class HomePersonalImageGuideDialog extends BaseDialogWidget {
  final bool canShow;
  final PersonnalImageGuideDialogParams? params;

  HomePersonalImageGuideDialog({
    super.key,
    required this.canShow,
    required super.onDialogShow,
    required this.params,
    super.clickBackDismiss = false,
  }) : super(
          pagePath: DialogsMessage.personalImageDialog.pagePath,
          dialogKey: DialogsMessage.personalImageDialog.key,
          dialogSort: DialogsMessage.personalImageDialog.sort,
          dialogType: DialogsMessage.personalImageDialog.dialogType,
        );

  @override
  Future<bool> canShowDialog() async {
    return canShow;
  }

  @override
  BaseDialogWidgetState<HomePersonalImageGuideDialog> createState() =>
      _HomePersonalImageGuideDialogState();
}

class _HomePersonalImageGuideDialogState
    extends BaseDialogWidgetState<HomePersonalImageGuideDialog> {
  @override
  Widget build(BuildContext context) {
    return _AnimatedGuideBoard(
      widget.params,
      completeCallback: () => widget.dismissDialog(),
    );
  }
}

class _AnimatedGuideBoard extends StatefulWidget {
  final PersonnalImageGuideDialogParams? params;
  final VoidCallback? completeCallback;
  const _AnimatedGuideBoard(
    this.params, {
    required this.completeCallback,
    Key? key,
  }) : super(key: key);
  @override
  State<_AnimatedGuideBoard> createState() => _AnimatedGuideBoardState();
}

class _AnimatedGuideBoardState extends State<_AnimatedGuideBoard>
    with
        SingleTickerProviderStateMixin,
        AnimationAudioPlayMixin,
        WidgetsBindingObserver {
  late AnimationController _controller;
  late Animation<double> _bgOpacity;
  late Animation<double> _bgWidth;
  late Animation<double> _textOpacity;
  late Animation<double> _textPosition;

  late double _kBoxWidth;
  late double _kBoxHeight;
  late double _scale;
  late bool _isLandscape;

  bool _showFingerGuide = false;
  bool _isOnDismiss = false;
  bool _wasPlayingBeforePause = false;

  final AudioPlayer _audioPlayer = AudioPlayer();
  StreamSubscription? _playerStateSubscription;

  @override
  void initState() {
    super.initState();

    _isLandscape = widget.params?.personalImageRect?.isLandSpace ?? false;
    _scale = _isLandscape ? 1.25 : 1;
    _kBoxWidth = 347 * _scale;
    _kBoxHeight = 81 * _scale;

    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 320));
    _bgOpacity = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 1, curve: Curves.linear)));
    _bgWidth = Tween<double>(begin: _kBoxWidth / 2.0, end: _kBoxWidth).animate(
        CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 1, curve: Curves.linear)));
    _textOpacity = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
        parent: _controller,
        curve: const Interval(0, 1.0, curve: Curves.linear)));
    _textPosition = Tween<double>(begin: 150.rdp, end: 28.rdp).animate(
        CurvedAnimation(
            parent: _controller,
            curve: const Interval(0, 1.0, curve: Curves.linear)));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.forward().whenComplete(() {
        Future.delayed(const Duration(milliseconds: 100), () {
          setState(() {
            _showFingerGuide = true;
          });
          playAudio(
              audioPlayer: _audioPlayer,
              path: AssetsAudio.PLAN_HOME_PERSONAL_GUIDE);
        });
        Future.delayed(const Duration(milliseconds: 5000), () {
          _playDismissAnimation();
        });
      });
    });

    WidgetsBinding.instance.addObserver(this);

    _playerStateSubscription =
        _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      if (!mounted) return;

      if ((state == PlayerState.completed || state == PlayerState.stopped) &&
          !_isOnDismiss) {
        _playDismissAnimation();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _audioPlayer.stop();
    _playerStateSubscription?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _playDismissAnimation() {
    if (_isOnDismiss || !mounted) return;

    try {
      setState(() {
        _showFingerGuide = false;
      });
      _audioPlayer.stop();
      _controller.reverse().whenCompleteOrCancel(() {
        if (mounted) {
          widget.completeCallback?.call();
        }
      });
    } catch (e) {
      l.i('个人形象引导', "_playDismissAnimation 错误: $e");
      // 如果动画出错，直接调用回调
      if (mounted) {
        widget.completeCallback?.call();
      }
    } finally {
      _isOnDismiss = true;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      if (_audioPlayer.state == PlayerState.playing) {
        _wasPlayingBeforePause = true;
        _audioPlayer.pause();
      }
    } else if (state == AppLifecycleState.resumed) {
      if (_wasPlayingBeforePause && !_isOnDismiss) {
        _wasPlayingBeforePause = true;
        _audioPlayer.resume();
      }
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    final rect = widget.params?.personalImageRect?.rect;
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          children: [
            Container(
                color:
                    Colors.white.withAlpha((_bgOpacity.value * 179).toInt())),
            SizedBox(
              child: topWidget(context, rect),
            ),
          ],
        );
      },
    );
  }

  Opacity topWidget(BuildContext context, Rect? rect) {
    final offset = rect?.topLeft ?? Offset.zero;
    final bottomOffset = widget.params?.personalImageRect?.bottomOffset ?? 0;
    final size = rect?.size ?? Size.zero;
    final heightFactor = (1 - (bottomOffset.abs()  + 1) / size.height);
    final topOffset = -(1 - heightFactor) * size.height + 1;
    final top = offset.dy + size.height + topOffset - _kBoxHeight;
    final iPadLeft = offset.dx - _kBoxWidth + size.width - context.dimensions.mediumSpacing;

    final screenWidth = MediaQuery.of(context).size.width;
    final iPhoneLeft =
        screenWidth - _kBoxWidth - context.dimensions.mediumSpacing;
    final left = _isLandscape ? iPadLeft : iPhoneLeft;

    return Opacity(
      opacity: _bgOpacity.value,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: top,
            left: left + (_kBoxWidth - _bgWidth.value),
            child: Container(
              width: _bgWidth.value,
              height: _kBoxHeight,
              decoration: BoxDecoration(
                color: context.appColors.jColorYellow3,
                borderRadius: BorderRadius.circular(context.dimensions.largeCornerRadius),
                border: Border.all(
                    color: context.appColors.jColorOrange4, width: 1.rdp),
              ),
              child: Stack(
                children: [
                  Positioned(
                    left: _textPosition.value,
                    top: 10 * _scale,
                    child: Opacity(
                      opacity: _textOpacity.value,
                      child: Image.asset(
                        AssetsImg.PLAN_IMAGE_PLAN_HOME_PERSONAL_GUIDE,
                        width: 144 * _scale,
                        height: 60 * _scale,
                        fit: BoxFit.contain,
                        package: RunEnv.package,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: rect?.top ?? 74.rdp,
            left: rect?.left ?? 230.rdp,
            child: ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: heightFactor,
                  child: GuidePersonalImageWidget(
                    params: widget.params,
                    rect: rect,
                  ),
                ),
              ),
          ),
          if (_showFingerGuide) _fingerGuideWidget(rect, top),
        ],
      ),
    );
  }

  Widget _fingerGuideWidget(Rect? rect, double top) {
    final width = 91.rdp * 1.25;
    final size = rect?.size ?? Size.zero;
    final offset = rect?.topLeft ?? Offset.zero;
    return Positioned(
      left: offset.dx + size.width / 2 - width / 3,
      top: top,
      child: Transform(
        transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0), // 仅左右翻转
        alignment: Alignment.center,
        child: IgnorePointer(
          ignoring: true,
          child: SizedBox(
              width: width,
              height: width,
              child: Lottie.asset(
                AssetsLottie.YEAR_FINGER_POINT,
                package: RunEnv.package,
                repeat: true,
              )),
        ),
      ),
    );
  }
}

class GuidePersonalImageWidget extends StatefulWidget {
  final PersonnalImageGuideDialogParams? params;
  final Rect? rect;

  const GuidePersonalImageWidget({
    Key? key,
    this.params,
    required this.rect,
  }) : super(key: key);

  @override
  State<GuidePersonalImageWidget> createState() => _GuidePersonalImageWidgetState();
}

class _GuidePersonalImageWidgetState extends State<GuidePersonalImageWidget> {
  Uint8List? _capturedImageWidget;

  Future<Uint8List?> captureWidgetImage(GlobalKey key) async {
    try {
      final boundary = key.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return null;

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      l.i('个人形象引导', "截图失败：$e");
      return null; // 捕获失败返回 null
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = widget.rect?.size ?? Size.zero;
    if (_capturedImageWidget == null &&
        widget.rect != null &&
        widget.params?.personalImageRect?.personImageKey != null) {
      final personImageKey = widget.params?.personalImageRect?.personImageKey;
      if (personImageKey != null) {
        captureWidgetImage(personImageKey)
            .then((imageBytes) {
          if (mounted && imageBytes != null) {
            setState(() {
              _capturedImageWidget = imageBytes;
            });
          }
        });
      }
    }

    return Container(
      height: size.height,
      width: size.width,
      color: Colors.transparent,
      child: Stack(
        children: [
          _capturedImageWidget != null
              ? Image.memory(_capturedImageWidget!)
              : _childWidget(size.height),
        ],
      ),
    );
  }

  Widget _childWidget(double itemHeight) {
        String localPersonalImage = widget.params?.localPersonalImagePath ?? '';

    bool showNormalImage = (widget.params?.activityPersonalImage ?? "").isEmpty;
        Widget childWidget;
    if (showNormalImage) {
      childWidget = _buildNormalPersonalImageWidget(
          localPersonalImage, itemHeight, widget.params?.dressImgUrl);
    } else {
      childWidget = _buildActivityPersonalImageWidget(itemHeight);
    }
    return childWidget;
  }

  Widget _buildNormalPersonalImageWidget(
      String localPersonalImage, double itemHeight, String? localDressImg) {
    return localPersonalImage.isNotEmpty
        ? Image.file(
            File(localPersonalImage),
            fit: BoxFit.contain,
            height: itemHeight,
            errorBuilder: (context, error, stackTrace) {
              return ImageNetworkCached(
                imageUrl: localDressImg ?? '',
                fit: BoxFit.contain,
                height: itemHeight,
              );
            },
          )
        : ImageNetworkCached(
            imageUrl: localDressImg ?? '',
            fit: BoxFit.contain,
            height: itemHeight,
            placeholderWidget: const SizedBox.shrink(),
          );
  }

  Widget _buildActivityPersonalImageWidget(double itemHeight) {
    return ImageNetworkCached(
      imageUrl: widget.params?.activityPersonalImage ?? '',
      useOldImageOnUrlChange: true,
      fit: BoxFit.contain,
      height: itemHeight,
      placeholderWidget: Container(),
    );
  }
}

class PersonnalImageGuideDialogParams {
  final PersonalImageRectEvent? personalImageRect;
  final String? activityPersonalImage;
  final String? localPersonalImagePath;
  final String? dressImgUrl;

  PersonnalImageGuideDialogParams({
    this.localPersonalImagePath,
    this.activityPersonalImage,
    this.dressImgUrl,
    this.personalImageRect,
  });
}

// 定义事件类
class PersonalImageRectEvent {
  final Rect rect;
  final double? bottomOffset;
  final bool? isLandSpace;
  final GlobalKey? personImageKey;
  PersonalImageRectEvent(this.rect,
      {this.bottomOffset, this.isLandSpace, this.personImageKey});
}
