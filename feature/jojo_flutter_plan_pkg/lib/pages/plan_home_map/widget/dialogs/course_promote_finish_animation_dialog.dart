
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/animation/spine_animation_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_bubble.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_madel_get.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';

/// 促完课动画执行弹窗
class CoursePromoteFinishAnimationDialog extends BaseDialogWidget  {

  final bool canShow;
  final PromoteLessonFinishModel model;
  final String? subjectColor;

  CoursePromoteFinishAnimationDialog({
    super.key,
    required this.model,
    required this.canShow,
    required this.subjectColor,
    required super.onDialogDismiss,
    required super.onDialogShow,
    super.clickBackDismiss = false,
  }): super(
    pagePath: DialogsMessage.promoteFinishAnimationDialog.pagePath,
    dialogKey: DialogsMessage.promoteFinishAnimationDialog.key,
    dialogSort: DialogsMessage.promoteFinishAnimationDialog.sort,
    dialogType: DialogsMessage.promoteFinishAnimationDialog.dialogType,
  ); 

  @override
  Future<bool> canShowDialog() async {
    return canShow;
  }

  @override
  BaseDialogWidgetState<BaseDialogWidget> createState() {
    return _CoursePromoteFinishAnimationDialogDialogState();
  }
}

class _CoursePromoteFinishAnimationDialogDialogState extends BaseDialogWidgetState<CoursePromoteFinishAnimationDialog> {

  final _gotSpineController = JoJoSpineAnimationController();
  late Timer _timer; // 如果规定时间内没有确定是否需要显示弹窗，则不显示该弹窗，避免弹窗阻塞用户操作
  Timer? _starFlyTimer;  // 飞星倒计时（到了时间，需要执行进度条动画）
  StreamSubscription? _promoteFinishAniEventBus;
  StreamSubscription? _dialogShowEventBus;
  StreamSubscription? _lifeCycleEventBus;
  AnimationType _animationType = AnimationType.none;
  bool _hasActivityCard = false;  // 是否存在顶部活动卡片，如果不存在，那么就不应该弹窗当前弹窗
  double _starFlyTopSpace = 350.rdp;
  double _progressItemWidth = 0.0;
  double _nextNodeLeftSpace = 0.0;
  bool _isLandSpace = false;
  bool _isShowedProgressAnimation = false;  // 是否已经执行了进度条动画
  AudioPlayer? audioPlayer;  // 音频管理

  //监听促完课动画事件
  void _listenPromoteFinishAnimation() {
    // 动画通知
    _promoteFinishAniEventBus = jojoEventBus.on<EventBusPromoteFinishAnimationDialogData>().listen((event) async {
      if (event.animationType == AnimationType.none) {
        // 无动画时弹窗消失，继续走后续弹窗逻辑
        widget.dismissDialog();
      } else if (event.animationType == AnimationType.starFly) {
        // 执行飞星动画
        _playFlyStarAnimation();
      } else if (event.animationType == AnimationType.badgeShowInCenter) {
        _showMadelGetWidget();
      } else if (event.animationType == AnimationType.madelPreview || event.animationType == AnimationType.nodeMadelGet) {
        List<LessonInProgressGiftModel> giftList =  widget.model.lessonInProgress?.giftList ?? [];
        bool isShouldShowBubble = await CoursePromoteFinishAnimationDialogBubbleWidget.isShouldShowBubble(widget.model.lessonInProgress?.nextGiftModel, giftList);
        if (event.animationType == AnimationType.madelPreview && isShouldShowBubble) {
          _showBubbleWidget();
        } else if (event.animationType == AnimationType.nodeMadelGet) {
          _showMadelGetWidget();
        } else {
          widget.dismissDialog();
        }
      }
    });
  }

  // 执行飞星动画
  void _playFlyStarAnimation() {
    _animationType = AnimationType.starFly;
    _playStarFlyAudio();
    _hasActivityCard = false;
    setState(() {});
  }

  // 展示预告气泡
  void _showBubbleWidget() {
    _animationType = AnimationType.madelPreview;
    setState(() {});
  }

  // 展示奖励获得弹窗
  void _showMadelGetWidget() {
    LessonInProgressGiftModel? giftModel;
    if (widget.model.lessonInProgress?.completeLessonCount == widget.model.lessonInProgress?.lessonCount) {
      giftModel = widget.model.lessonInProgress?.allFinishGiftModel;
    } else {
      giftModel = widget.model.lessonInProgress?.currentGiftModel;
    }
    widget.dismissDialog();
    jojoNativeBridge.showHomePageTabs(show: false);
    // 关闭当前动画执行弹窗,弹出奖励页
    SmartDialog.show(
        clickMaskDismiss: true,
        useAnimation: false,
        onDismiss: () {
          jojoNativeBridge.showHomePageTabs(show: true);
        },
        alignment: Alignment.center,
        builder: (_) => CoursePromoteFinishAnimationDialogMadelGetWidget(
            model: widget.model,
            giftModel: giftModel,
            isFromClick: false,
            subjectColor: widget.subjectColor,
            isAllLessonFirstGet: true,
            disMissCallBack: () {
              SmartDialog.dismiss();
            }));
  }

  // 创建音频播放器
  Future<void> _createAudioPlayerAndPlay(String filePath) async {
    if (filePath.isEmpty) {
      return;
    }
    try {
      if (audioPlayer != null) {
        audioPlayer!.stop();
        audioPlayer!.dispose();
      }
      audioPlayer = AudioPlayer();
      audioPlayer!.audioCache.prefix = '';
      await audioPlayer!.play(DeviceFileSource(filePath));
    } catch (e) {
      print(e);
    }
  }

  // 暂停并销毁音频播放器
  void _disposeAudioPlayer() {
    if (audioPlayer != null) {
      audioPlayer?.stop();
      audioPlayer?.dispose();
      audioPlayer = null;
    }
  }

  void _listenLifeCycleEventBus() {
    // 生命周期
    _lifeCycleEventBus = jojoEventBus.on<EventBusPromoteFinishLifecycleData>().listen((event) async {
      // 页面不可见时，非奖章获得页面，需要关闭弹窗
      if (event.lifecycleType == LifecycleType.paused && _animationType != AnimationType.badgePage) {
        widget.dismissDialog();
      }
      // 页面不可见时干掉音频逻辑
      if (event.lifecycleType == LifecycleType.paused) {
        _disposeAudioPlayer();
        // 退后台后,弹窗会关闭,这里需要强制更新进度数以及进度条
        int completeLessonCount = widget.model.lessonInProgress?.completeLessonCount ?? 0;
        jojoEventBus.fire(EventBusPromoteDialogShowData(AnimationDialogType.updateCompleteCountNoAnimation, 0, 0, 0, completeLessonCount));
      }
    });
  }

  // 飞星音频播放
  void _playStarFlyAudio() {
    if (widget.model.lessonInProgress == null || widget.model.lessonInProgress!.spineResourceInfo == null) return;
    PromoteLessonFinishSpineResourceInfo spineResourceInfo = widget.model.lessonInProgress!.spineResourceInfo!;
    String audioFilePath = spineResourceInfo.starFlyResource.getFilePath("audio_event_star_second_play.mp3");
    _createAudioPlayerAndPlay(audioFilePath);
  }

  //监听促完课弹窗显示事件（如果没收到通知，则倒计时结束会判断是否应该显示弹窗）
  void _listenPromoteFinishShow() {
      _dialogShowEventBus = jojoEventBus.on<EventBusPromoteDialogShowData>().listen((event) async {
        if (event.dialogType == AnimationDialogType.hasActivityCard) {
          _starFlyTopSpace = event.topSpace;
          _nextNodeLeftSpace = event.leftSpace;
          _progressItemWidth = event.progressItemWidth;
          _hasActivityCard = true;
        }
        // 卡片上收集物动画执行完毕，这里可以继续执行飞星动画
        if (event.dialogType == AnimationDialogType.animationEnd && _hasActivityCard) {
          _timer.cancel();
          _playFlyStarAnimation();
        }
      });
  }

  // 开始倒计时的方法
  void _startTimer() {
    // 发送通知
    jojoEventBus.fire(EventBusPromoteDialogShowData(AnimationDialogType.request, 0, 0, 0, 0));
    // 需要等待收集物动画执行完成，如果在规定时间内没有收到通知，则不显示当前弹窗（需要等待收集物动画执行，这里设置1秒，如果一秒内没有收到通知，则判断是否有活动卡片，如果有，则继续执行动画)
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _timer.cancel();
      // 如果数据是空的，则直接关闭动画执行弹窗
      if (widget.model.lessonInProgress?.spineResourceInfo == null) {
        widget.dismissDialog();
        return;
      }
      if (_hasActivityCard) {
        // 倒计时结束，满足弹窗显示条件，执行飞星动画
        _playFlyStarAnimation();
      } else {
        // 倒计时结束，不满足弹窗显示条件，不显示弹窗
        widget.dismissDialog();
      }
    });
  }

  void _startStarFlyTimer(double seconds) {
    _starFlyTimer = Timer.periodic(Duration(milliseconds: (seconds * 1000).round()), (timer) {
      _starFlyTimer?.cancel();
      _starFlyTimer = null;
      _isShowedProgressAnimation = true;
      jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.progress));
    });
  }

  @override
  void initState() {
    super.initState();
    _listenPromoteFinishAnimation();
    _listenPromoteFinishShow();
    _listenLifeCycleEventBus();
    _startTimer();
  }

  @override
  void dispose() {
    _promoteFinishAniEventBus?.cancel();
    _promoteFinishAniEventBus = null;
    _dialogShowEventBus?.cancel();
    _dialogShowEventBus = null;
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _timer.cancel();
    _starFlyTimer?.cancel();
    _starFlyTimer = null;
    _gotSpineController.dispose();
    _disposeAudioPlayer();
    super.dispose();
  }

  // 动效动画完成回调
  _spineAnimationEvent(AnimationEventType type) {
    if(AnimationEventType.complete == type) {
      if (_animationType == AnimationType.starFly && !_isShowedProgressAnimation) {
        jojoEventBus.fire(EventBusPromoteFinishAnimationDialogData(AnimationType.progress));
      }
    }
  }

  // 飞星动画
  Widget _buildStarFlyAnimationWidget(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    double itemHeight = screenHeight - _starFlyTopSpace;
    PromoteLessonFinishSpineResourceInfo spineResourceInfo = widget.model.lessonInProgress!.spineResourceInfo!;
    String? atlasFilePath = spineResourceInfo.starFlyResource.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = spineResourceInfo.starFlyResource.getFilePath(SpineResourceConstants.SPINE_SKL);
    return Stack(
        children: [
          Positioned(
          top: _starFlyTopSpace - itemHeight*ConfigSize.starFlyDySpace/868,
          left: -(750.rdp - screenWidth)/2.0,
          child: SizedBox(
            width: 750.rdp,
            height: itemHeight,
            child: CourseSpineAnimationWidget(
              animationController: _gotSpineController,
              atlasFilePath: atlasFilePath,
              skelFilePath: skelFilePath,
              animationName: SpineAnimationConstants.twStarAnimationPlay,
              animationDurationName: SpineAnimationConstants.twProgressAnimationPlay,
              playAnimation: true,
              scale: itemHeight/868, loop: false, callBack: (type) {
                _spineAnimationEvent(type);
              }, initializedCallBack: (duration) {
              if (duration > 0) {
                _startStarFlyTimer(duration);
              }
            },)
          ),
        ),
        ]
    );
  }

  Widget _buildContentView() {
    if (_animationType == AnimationType.starFly) {
      // 飞星
      return _buildStarFlyAnimationWidget(context);
    } else if (_animationType == AnimationType.badgeShowInCenter || _animationType == AnimationType.badgePage) {
      // 奖章获得
      return CoursePromoteFinishAnimationDialogMadelGetWidget(
          model: widget.model,
          isFromClick: false,
          giftModel: widget.model.lessonInProgress?.allFinishGiftModel,
          subjectColor: widget.subjectColor,
          isAllLessonFirstGet: true,
          disMissCallBack: () {
            widget.dismissDialog();
          });
    } else if (_animationType == AnimationType.madelPreview) {
      // 奖章预告
      return CoursePromoteFinishAnimationDialogBubbleWidget(
          giftModel: widget.model.lessonInProgress?.nextGiftModel,
          isOnClick: false,
          topSpace: _starFlyTopSpace,
          leftSpace: _nextNodeLeftSpace > 0 ? _nextNodeLeftSpace : null,
          isLandSpace: _isLandSpace,
          progressWidth: _progressItemWidth,
          disMissCallBack: () {
            widget.dismissDialog();
          }
      );
    } else if (_animationType == AnimationType.nodeMadelGet) {
      // 阶段奖励获得
      return CoursePromoteFinishAnimationDialogMadelGetWidget(
          model: widget.model,
          isFromClick: false,
          giftModel: widget.model.lessonInProgress?.currentGiftModel,
          subjectColor: widget.subjectColor,
          isAllLessonFirstGet: true,
          disMissCallBack: () {
            widget.dismissDialog();
          });
    }
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveOrientationLayout(
      portrait: (BuildContext context) {
        _isLandSpace = false;
        return _buildContentView();
    }, landscape: (BuildContext context) {
        _isLandSpace = true;
        return _buildContentView();
    });
  }
}
