
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_guide_landscape_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_guide_protrait_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';

import '../../../../service/home_map_page_api.dart';


/// 促完课行课期引导弹窗
class CoursePromoteFinishGuideWidgetDialog extends BaseDialogWidget  {

  final bool canShow;
  final PromoteLessonFinishPopVo? popupInfo;

  CoursePromoteFinishGuideWidgetDialog({
    super.key,
    required this.canShow,
    required this.popupInfo,
    required super.onDialogDismiss,
    required super.onDialogShow,
    super.clickBackDismiss = false,
  }): super(
    pagePath: DialogsMessage.promoteFinishGuideDialog.pagePath,
    dialogKey: DialogsMessage.promoteFinishGuideDialog.key,
    dialogSort: DialogsMessage.promoteFinishGuideDialog.sort,
    dialogType: DialogsMessage.promoteFinishGuideDialog.dialogType,
  );

  @override
  Future<bool> canShowDialog() async {
    return canShow;
  }

  @override
  BaseDialogWidgetState<BaseDialogWidget> createState() {
    return _CoursePromoteFinishGuideWidgetDialogState();
  }

  // 获取是否应该显示弹窗
  static Future<bool> shouldShowPOPGuide(PromoteLessonFinishPopVo? pagePopupInfo, int activityId) async {
    if (pagePopupInfo == null) return false;
    try {
      String key;
      if (pagePopupInfo.type == PromoteLessonFinishPopType.guide) {
        key = "kPlanHomeClassCoursePromoteFinish_activityGuide_${activityId}_${BaseConfig.share.userInfo?.uid}";
      } else {
        key = "kPlanHomeClassCoursePromoteFinish_firstFinishGuide_${activityId}_${pagePopupInfo.taskId}_${BaseConfig.share.userInfo?.uid}";
      }
      NativeValue? mp = await jojoNativeBridge.operationNativeValueGet(key: key).then((value) => value.data);
      String value = mp?.value ?? '';
      return value.isEmpty;
    } catch (e) {
      l.e("2025上课页促完课弹窗", "获取是否应该显示弹窗的逻辑判断异常");
      return false;
    }
  }
}

class _CoursePromoteFinishGuideWidgetDialogState extends BaseDialogWidgetState<CoursePromoteFinishGuideWidgetDialog> {

  AudioPlayer? audioPlayer;  // 音频管理
  StreamSubscription? _lifeCycleEventBus;

  Future<void> _setGuidePOPStatus() async {
    try {
      if (widget.popupInfo == null) return;
      if (!mounted) return;
      String key;
      if (widget.popupInfo?.type == PromoteLessonFinishPopType.guide) {
        key = "kPlanHomeClassCoursePromoteFinish_activityGuide_${widget.popupInfo?.activityId}_${BaseConfig.share.userInfo?.uid}";
        _report(76, widget.popupInfo?.classId, "${widget.popupInfo?.activityId}");
      } else {
        key = "kPlanHomeClassCoursePromoteFinish_firstFinishGuide_${widget.popupInfo?.activityId}_${widget.popupInfo?.taskId}_${BaseConfig.share.userInfo?.uid}";
        _report(77, widget.popupInfo?.classId, widget.popupInfo?.bizId);
      }
      await jojoNativeBridge.operationNativeValueSet(key: key, value: key);
      setState(() {});
    } catch (e) {
      l.e("2025上课页新开课弹窗", "设置弹窗状态的逻辑判断异常");
    }
  }

  _report(int type, int? classId, String? bizId) {
    homeMapPageApiService.requestTopUserInfoDialogCallback(
        ["$bizId"], type, "${BaseConfig.share.userInfo?.uid}", classId);
  }

  // 埋点
  void _dealWithViewBuried() {
    if (widget.popupInfo?.activityId != null) {
      int activityId = widget.popupInfo?.activityId ?? 0;
      int status = widget.popupInfo?.status ?? 0;
      JoJoPromoteFinishBuriedUtils.ActivityPopViewScreen(activityId, status, widget.popupInfo?.lessonInfo);
    }
  }

  void _listenLifeCycleEventBus() {
    // 生命周期
    _lifeCycleEventBus = jojoEventBus.on<EventBusPromoteFinishLifecycleData>().listen((event) async {
      // 页面不可见时干掉音频逻辑
      if (event.lifecycleType == LifecycleType.paused) {
        _disposeAudioPlayer();
      }
      // 页面可见时，回来如果弹窗还在，这里需要重新播放音频
      if (event.lifecycleType == LifecycleType.resumed && mounted && widget.popupInfo?.type == PromoteLessonFinishPopType.guide) {
        _createAudioPlayerAndPlay(widget.popupInfo?.audioUrl ?? '');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveOrientationLayout(portrait: _portrait, landscape: _landscape);
  }

  Widget _portrait(BuildContext context) {
    jojoNativeBridge.showHomePageTabs(show: false);
    return CoursePromoteFinishGuideProtraitWidget(
      popupInfo: widget.popupInfo, 
      dismissAction: () {
        jojoNativeBridge.showHomePageTabs(show: true);
        _disposeAudioPlayer();
        widget.dismissDialog();
      },);
  }

  Widget _landscape(BuildContext context) {
    return CoursePromoteFinishGuideLandScapeWidget(
      popupInfo: widget.popupInfo, 
      dismissAction: () {
        _disposeAudioPlayer();
        widget.dismissDialog();
      },);
  }

  @override
  void initState() {
    super.initState();
    _setGuidePOPStatus();
    _dealWithViewBuried();
    _listenLifeCycleEventBus();
    // 播放语音
    if (widget.popupInfo?.type == PromoteLessonFinishPopType.guide) {
      _createAudioPlayerAndPlay(widget.popupInfo?.audioUrl ?? '');
    }
  }

  @override
  void dispose() {
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _disposeAudioPlayer();
    super.dispose();
  }

  // 创建音频播放器
  Future<void> _createAudioPlayerAndPlay(String filePath) async {
    if (filePath.isEmpty) {
      return;
    }
    try {
      if (audioPlayer != null) {
        audioPlayer!.stop();
        audioPlayer!.dispose();
      }
      audioPlayer = AudioPlayer();
      audioPlayer!.audioCache.prefix = '';
      await audioPlayer!.play(UrlSource(filePath));
    } catch (e) {
      print(e);
    }
  }

  // 暂停并销毁音频播放器
  void _disposeAudioPlayer() {
    if (audioPlayer != null) {
      audioPlayer?.stop();
      audioPlayer?.dispose();
      audioPlayer = null;
    }
  }
}
