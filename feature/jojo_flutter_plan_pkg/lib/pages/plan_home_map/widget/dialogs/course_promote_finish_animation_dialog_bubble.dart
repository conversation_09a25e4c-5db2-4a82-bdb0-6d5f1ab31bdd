
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/animation/spine_animation_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class CoursePromoteFinishAnimationDialogBubbleWidget extends StatefulWidget {
  final bool isLandSpace;
  final bool isOnClick;  // 是否是主动点击的
  final double topSpace;
  final double? leftSpace;
  final double progressWidth;  // 进度条宽度
  final LessonInProgressGiftModel? giftModel;
  final Function()? disMissCallBack;

  static Future<bool> isShouldShowBubble(LessonInProgressGiftModel? model, List<LessonInProgressGiftModel> giftList) async {
    if (model == null || giftList.isEmpty) return false;
    // 从本地标记中判断是否显示过气泡
    String key = "kPlanHomeClassCoursePromoteFinish_bubble_${model.activityId}_${model.taskId}_${BaseConfig.share.userInfo?.uid}";
    NativeValue? mp = await jojoNativeBridge.operationNativeValueGet(key: key).then((value) => value.data);
    String value = mp?.value ?? '';
    if (value.isNotEmpty) return false;
    if (!hasAnimationResource(model)) return false;  // 没有动效资源，同样不弹出气泡，避免资源配置有问题时卡住

    // 判断动效资源是否下载成功
    for (var element in giftList) {
      if (element.spineResourceVo?.resourceUrl == model.giftSpineUrl) {
        return element.spineResourceVo?.isDowned == true;
      }
    }
    return true;
  }

  static bool hasAnimationResource(LessonInProgressGiftModel? model) {
    // 没有动效资源也不能弹出气泡
    String? atlasFilePath = model?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = model?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_SKL);
    if ((atlasFilePath ?? "").isEmpty || (skelFilePath ?? "").isEmpty) {
      return false;
    }
    return true;
  }

  const CoursePromoteFinishAnimationDialogBubbleWidget({
    Key? key,
    required this.giftModel,
    required this.topSpace,
    required this.isOnClick,
    required this.leftSpace,
    required this.isLandSpace,
    required this.progressWidth,
    required this.disMissCallBack
  }) : super(key: key);

  @override
  CoursePromoteFinishAnimationDialogBubbleWidgetState createState() => CoursePromoteFinishAnimationDialogBubbleWidgetState();
}

class CoursePromoteFinishAnimationDialogBubbleWidgetState extends State<CoursePromoteFinishAnimationDialogBubbleWidget> with SingleTickerProviderStateMixin {

  StreamSubscription? _lifeCycleEventBus;
  AudioPlayer? _audioPlayerDubbing;  // 配音音频管理
  AudioPlayer? _audioPlayerSoundEffects;  // 音效音频管理
  final _spineController = JoJoSpineAnimationController();
  bool _playedAudio = false;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    //播放配音
    String? audioDubbingPath = widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventDubbingLoop);
    if ((audioDubbingPath ?? "").isNotEmpty) {
      _audioPlayerDubbing = AudioPlayer();
    }
    _createAudioPlayerAndPlay(audioDubbingPath ?? "", _audioPlayerDubbing);
    //播放音效
    String? audioSoundEffectsPath = widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventSoundEffectsLoop);
    if ((widget.giftModel?.rewardList ?? []).length == 1) {
      audioSoundEffectsPath = widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventSoundEffectsOldLoop);
    }
    if ((audioSoundEffectsPath ?? "").isNotEmpty) {
      _audioPlayerSoundEffects = AudioPlayer();
    }
    _createAudioPlayerAndPlay(audioSoundEffectsPath ?? "", _audioPlayerSoundEffects);

    _listenLifeCycleEventBus();

    _dealWithAnimation();

    // 标记已弹出过
    if (widget.isOnClick != true) {
      String taskId = widget.giftModel?.taskId?.toString() ?? '';
      String key = "kPlanHomeClassCoursePromoteFinish_bubble_${widget.giftModel?.activityId}_${taskId}_${BaseConfig.share.userInfo?.uid}";
      jojoNativeBridge.operationNativeValueSet(key: key, value: key);
    }
  }

  @override
  void dispose() {
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _disposeAudioPlayer();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.giftModel == null) {
      return Container();
    }
    double space = widget.progressWidth * widget.giftModel!.index * 1.0 / widget.giftModel!.lessonCount - ConfigSize.madelPreViewBubbleWidth/2.0;
    if (widget.leftSpace != null) {
      space = widget.leftSpace ?? 0;
    }
    double start_x = ConfigSize.contentPadding + ConfigSize.lrSpace;
    if (widget.isLandSpace) {
      // 气泡的位置需要兼容平板
      double screenWidth = MediaQuery.of(context).size.width;
      start_x += (screenWidth - ConfigSize.landSpaceContentWidth)/2.0 + ConfigSize.landSpaceProgressLeftSpace;
    }
    String? atlasFilePath = widget.giftModel?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = widget.giftModel?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_SKL);
    return Stack(
        children: [
          Positioned(
            top: widget.topSpace - ConfigSize.madelPreViewCardTopSpace,
            left: start_x + space,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Opacity(
                  opacity: _opacityAnimation.value,
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    alignment: Alignment.bottomCenter, // 缩放中心点设置为底部中心
                    child: SizedBox(
                        width: ConfigSize.madelPreViewBubbleWidth,
                        height: ConfigSize.madelPreViewBubbleHeight,
                        child: Stack(
                          children: [
                            ImageAssetWeb(
                              assetName: AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_NODE_BUBBLE_ICON,
                              fit: BoxFit.cover,
                              package: Config.package,
                            ),
                            Positioned(
                                top: 9.rdp,
                                left: 9.rdp,
                                child: SizedBox(
                                  width: ConfigSize.madelPreViewBubbleSpineWidth,
                                  height: ConfigSize.madelPreViewBubbleSpineHeight,
                                  child: CourseSpineAnimationWidget(
                                    animationController: _spineController,
                                    atlasFilePath: atlasFilePath,
                                    skelFilePath: skelFilePath,
                                    animationName: SpineAnimationConstants.bubbleAnimationLoop,
                                    playAnimation: true,
                                    scale: ConfigSize.madelPreViewBubbleSpineWidth/ConfigSize.medalSpineUIWidth, loop: true, callBack: (type) {
                                    if (type == AnimationEventType.complete && !_playedAudio) {
                                      // 动画播放完了，并且没有播放语音，则播完动画后就隐藏气泡并且关闭弹窗
                                      disMissBubble();
                                    }
                                  }, initializedCallBack: (duration) {
                                    if (_spineController.spineController?.skeletonData.findAnimation(SpineAnimationConstants.bubbleAnimationLoop) == null) {
                                      // 动画不存在，直接关闭弹窗，避免因为配置问题产生卡死的BUG
                                      l.i("促完课活动", "中间奖励动效资源中缺少loop动画");
                                      widget.disMissCallBack?.call();
                                      return;
                                    }
                                  },),
                                )
                            )
                          ],
                        )
                    ),
                  ),
                );
              },
            ),
          ),
        ]
    );
  }

  void _listenLifeCycleEventBus() {
    // 生命周期
    _lifeCycleEventBus = jojoEventBus.on<EventBusPromoteFinishLifecycleData>().listen((event) async {
      // 页面不可见时干掉音频逻辑
      if (event.lifecycleType == LifecycleType.paused) {
        _disposeAudioPlayer();
        disMissBubble(); // 不可见时需要销毁气泡，避免卡程序
      }
    });
  }

  // 创建音频播放器
  Future<void> _createAudioPlayerAndPlay(String filePath, AudioPlayer? audioPlayer) async {
    if (filePath.isEmpty || audioPlayer == null) {
      return;
    }
    try {
      audioPlayer.audioCache.prefix = '';
      await audioPlayer.play(DeviceFileSource(filePath));
      if (audioPlayer == _audioPlayerDubbing) {
        _playedAudio = true;
      }
      audioPlayer.onPlayerStateChanged.listen((event) {
        if (event == PlayerState.completed && audioPlayer == _audioPlayerDubbing) {
          disMissBubble();
        }
      });
    } catch (e) {
      print(e);
    }
  }

  void disMissBubble() {
    // 播放完语音后，直接隐藏气泡，关闭弹窗
    widget.disMissCallBack?.call();
  }

  // 暂停并销毁音频播放器
  void _disposeAudioPlayer() {
    if (_audioPlayerDubbing != null) {
      _audioPlayerDubbing?.stop();
      _audioPlayerDubbing?.dispose();
      _audioPlayerDubbing = null;
    }
    if (_audioPlayerSoundEffects != null) {
      _audioPlayerSoundEffects?.stop();
      _audioPlayerSoundEffects?.dispose();
      _audioPlayerSoundEffects = null;
    }
  }

  void _dealWithAnimation() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 550),
      vsync: this,
    );

    // 缩放动画：0.0 -> 1.15 -> 1.0
    _scaleAnimation = TweenSequence([
      TweenSequenceItem(
        tween: Tween(begin: 0.0, end: 1.15)
            .chain(CurveTween(curve: Curves.easeOutBack)),
        weight: 58,
      ),
      TweenSequenceItem(
        tween: Tween(begin: 1.15, end: 1.0)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 42,
      ),
    ]).animate(_controller);

    // 透明度动画：0 -> 1
    _opacityAnimation = Tween(begin: 0.0, end: 1.0)
        .animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.58, curve: Curves.easeOut),
      ),
    );

    // 启动动画
    _controller.forward();
  }
}
