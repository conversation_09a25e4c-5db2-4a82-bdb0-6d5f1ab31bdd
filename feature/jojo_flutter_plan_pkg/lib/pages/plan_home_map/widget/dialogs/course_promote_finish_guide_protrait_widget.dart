import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';

import '../dialog_safe_bottom_widget.dart';

class CoursePromoteFinishGuideProtraitWidget extends StatefulWidget {
  final PromoteLessonFinishPopVo? popupInfo;
  final Function()? dismissAction;

  const CoursePromoteFinishGuideProtraitWidget(
      {super.key, required this.popupInfo, required this.dismissAction});

  @override
  State<StatefulWidget> createState() {
    return CoursePromoteFinishGuideProtraitWidgetState();
  }
}

class CoursePromoteFinishGuideProtraitWidgetState
    extends State<CoursePromoteFinishGuideProtraitWidget> {
  @override
  Widget build(BuildContext context) {
    return DialogSafeBottom(
      child: Container(
        alignment: Alignment.bottomCenter,
        margin: EdgeInsets.zero,
        color: HexColor('#000000', 70), // 起始颜色
        child: _buildDialogWidget(context),
      ),
    );
  }

  Widget _buildDialogWidget(BuildContext context) {
    return Container(
        width: double.infinity,
        height: 394.rdp,
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned(
              left: 0.rdp,
              right: 0.rdp,
              bottom: 0.rdp,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(32.rdp),
                    topLeft: Radius.circular(32.rdp)),
                child: Container(
                  width: double.infinity,
                  height: 324.rdp,
                  decoration: BoxDecoration(
                    color: HexColor("#ffffff"),
                  ),
                  child: _buildContentWidget(context),
                ),
              ),
            ),
            Positioned(
                top: 0.rdp,
                left: 0.rdp,
                right: 0.rdp,
                child: SizedBox(
                  width: double.infinity,
                  height: 240.rdp,
                  child: ImageNetworkCached(
                    imageUrl: widget.popupInfo?.icon ?? "",
                    fit: BoxFit.contain,
                  ),
                ))
          ],
        ));
  }

  Widget _buildTitleWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
      height: 30.rdp,
      child: Text(
        widget.popupInfo?.title ?? "",
        style: TextStyle(
          fontSize: 20.rdp,
          fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
          color: context.appColors.jColorGray6,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildSubTitleWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
      height: 27.rdp,
      child: Text(
        widget.popupInfo?.subTitle ?? "",
        style: TextStyle(
          fontSize: 18.rdp,
          fontWeight: FontWeight.w400,
          color: context.appColors.jColorGray5,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildCommitWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.dismissAction?.call();
        if (widget.popupInfo?.type == PromoteLessonFinishPopType.guide &&
            widget.popupInfo?.videoUrl != null) {
          // 需要把埋点参数编码后传过去
          String dataString = jsonEncode(widget.popupInfo?.properties ?? {});
          RunEnv.jumpLink(
              "tinman-router://cn.tinman.jojoread/flutter/plan/planHomeVideoIntroducePage?url=${widget.popupInfo?.videoUrl}&dataString=${Uri.encodeComponent(dataString)}");
        }
      },
      child: Container(
        decoration: BoxDecoration(
            color: HexColor('#FCDA00'),
            borderRadius: BorderRadius.circular(20.rdp)),
        height: 40.rdp,
        width: 280.rdp,
        alignment: Alignment.center,
        child: Center(
          child: Text(
            widget.popupInfo?.btnText ?? "",
            style: TextStyle(
              fontSize: 14.rdp,
              color: HexColor('#544300'),
              fontWeight: FontWeight.w400,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWidget(BuildContext context) {
    return SizedBox(
        child: Column(
      children: [
        SizedBox(height: 175.rdp),
        _buildTitleWidget(context),
        _buildSubTitleWidget(context),
        SizedBox(height: 20.rdp),
        _buildCommitWidget(context)
      ],
    ));
  }
}
