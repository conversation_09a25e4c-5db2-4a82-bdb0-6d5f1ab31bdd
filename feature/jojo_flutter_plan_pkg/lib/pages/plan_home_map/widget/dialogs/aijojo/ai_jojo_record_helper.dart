import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_controller.dart';

class AiJoJoRecordHelper {
  static const String _key = 'ai_jojo_phone_dialog_show_today';

  static void markToadyShow(int finisMilestoneRewardTime) async {
    UserInfo? userInfo =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
    DateTime now = DateTime.now();
    String today = '${now.year}-${now.month}-${now.day}';
    jojoNativeBridge.operationNativeValueSet(
        key: _key,
        value: "${userInfo?.uid}_${today}_$finisMilestoneRewardTime");
  }

  static Future<bool> canShowToday() async {
    UserInfo? userInfo =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
    NativeValue? value = await jojoNativeBridge
        .operationNativeValueGet(key: _key)
        .then((value) => value.data);
    String? result = value?.value;
    if (result == null || result.isEmpty) {
      return true;
    }
    List<String> valueList = result.split('_');
    if (valueList.length != 3){
      return true;
    }
    DateTime now = DateTime.now();
    String today = '${now.year}-${now.month}-${now.day}';
    if (valueList[0] != userInfo?.uid) {
      return true;
    }
    if (valueList[1] != today) {
      return true;
    }
    return false;
  }

  static Future<bool> canShowFinisMilestoneRewardTime(
      int? finisMilestoneRewardTime) async {
    if (finisMilestoneRewardTime == null || finisMilestoneRewardTime == 0) {
      l.i(AiJoJoController.tag, "finisMilestoneRewardTime 无效 不展示 $finisMilestoneRewardTime");
      return false;
    }
    if(!await canShowToday()){
      l.i(AiJoJoController.tag, "今天已经展示过");
      return false;
    }
    UserInfo? userInfo =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
    NativeValue? value = await jojoNativeBridge
        .operationNativeValueGet(key: _key)
        .then((value) => value.data);
    String? result = value?.value;
    if (result == null || result.isEmpty) {
      l.i(AiJoJoController.tag, "无存储结果 可以展示");
      return true;
    }
    List<String> valueList = result.split('_');
    if (valueList.length != 3) {
      l.i(AiJoJoController.tag, "数据错误 可以展示");
      return true;
    }
    if (valueList[0] != userInfo?.uid) {
      l.i(AiJoJoController.tag, "用户切换 可以展示");
      return true;
    }
    try {
      int localFinisMilestoneRewardTime = int.parse(valueList[2]);
      if (localFinisMilestoneRewardTime < finisMilestoneRewardTime) {
        l.i(AiJoJoController.tag, "次数更新 可以展示");
        return true;
      }
    } catch (e) {
      l.e(AiJoJoController.tag, 'canShowFinisMilestoneRewardTime error $e');
      return true;
    }
    l.i(AiJoJoController.tag, "次数未更新 不可以展示");
    return false;
  }
}
