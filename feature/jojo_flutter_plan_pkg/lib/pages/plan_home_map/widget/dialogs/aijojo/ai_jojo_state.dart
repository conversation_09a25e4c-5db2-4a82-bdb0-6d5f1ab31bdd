import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/ai_frequency.dart';

class AiJoJoState {
  EncouragementConversationParamsVo? encouragementConversationParamsVo;
  AiFrequency? aiFrequency;
  bool canShow = false;

  AiJoJoState();

  AiJoJoState copyWith({
    EncouragementConversationParamsVo? encouragementConversationParamsVo,
    AiFrequency? aiFrequency,
    bool? canShow,
  }) {
    return AiJoJoState()
      ..canShow = canShow ?? this.canShow
      ..aiFrequency = aiFrequency?.copyWith()
      ..encouragementConversationParamsVo = encouragementConversationParamsVo?.copyWith();
  }
}