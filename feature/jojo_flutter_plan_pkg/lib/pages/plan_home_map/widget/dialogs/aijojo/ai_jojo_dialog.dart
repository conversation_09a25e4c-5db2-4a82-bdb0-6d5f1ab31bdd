import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as cacheManager;
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/customization/landscape_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/ai_frequency.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_dialog_dismiss_reason.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/ui_dialog_dialog_key.dart';
import 'package:jojo_flutter_plan_pkg/service/ai_jiao_jiao_service_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class AiJoJoDialog extends StatefulWidget {
  const AiJoJoDialog({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AiJoJoDialogState();
  }
}

class _AiJoJoDialogState extends State<AiJoJoDialog>
    with WidgetsBindingObserver {
  AiJoJoController? _controller;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        _controller ??= AiJoJoController(AiJoJoState(), aiJiaoJiaoServiceApi,cacheManager.DefaultCacheManager());
        return _controller!;
      },
      child: BlocBuilder<AiJoJoController, AiJoJoState>(
        builder: (context, state) {
          l.i(AiJoJoController.tag,
              "builder canShow ${state.canShow}  ${state.encouragementConversationParamsVo != null}");
          return state.canShow &&
                  state.encouragementConversationParamsVo != null
              ? AiJoJoDialogWidget(
                  encouragementConversationParamsVo:
                      state.encouragementConversationParamsVo!,
                  aiJoJoState: state,
                  aiJoJoController: _controller,
                  dismiss: _dismiss,
                )
              : Container();
        },
      ),
    );
  }

  void _dismiss() {
    _controller?.dismiss();
  }

  void _showDialog() async {
    _controller?.resume = true;
    _controller?.refresh();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    l.i(AiJoJoController.tag, "生命周期变化 $state");
    if (state == AppLifecycleState.resumed) {
      _showDialog();
      // 恢复
    } else if (state == AppLifecycleState.paused) {
      // 处于暂停状态
      _controller?.resume = false;
    }
  }
}

class AiJoJoDialogWidget extends StatefulWidget {
  final EncouragementConversationParamsVo encouragementConversationParamsVo;
  final AiJoJoState aiJoJoState;
  final Function dismiss;
  final AiJoJoController? aiJoJoController;

  const AiJoJoDialogWidget(
      {super.key,
      required this.encouragementConversationParamsVo,
      required this.dismiss,
      required this.aiJoJoController,
      required this.aiJoJoState});

  @override
  State<StatefulWidget> createState() {
    return AiJoJoDialogWidgetState();
  }
}

class AiJoJoDialogWidgetState extends State<AiJoJoDialogWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final AudioPlayer _bgmPlayer = AudioPlayer();
  late AnimationController _animationController;
  bool _callStop = false;
  AppLifecycleState? _lastAppLifecycleState;

  static int initCount = 0;

  @override
  void initState() {
    initCount++;
    super.initState();
    l.i(AiJoJoController.tag, "initState 初始化");
    WidgetsBinding.instance.addObserver(this);
    playAudios();
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _animationController.addStatusListener(_animationStateChangeListener);
    _animationController.forward();
    l.i(AiJoJoController.tag, "动画计时 开始");
    RunEnv.sensorsTrack('ElementView', {
      '\$screen_name': "AI激励",
      'learn_segments': widget.aiJoJoState.aiFrequency?.segmentCodeList ?? [],
      'class_ids': widget.aiJoJoState.aiFrequency?.classIds ?? [],
      "custom_state": widget.aiJoJoState.aiFrequency?.customState
    });
  }

  void _animationStateChangeListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      widget.aiJoJoController?.reportAiDialogDismiss(
          AiDialogDismissReason.timeout,
          widget.encouragementConversationParamsVo.roomTaskId);
      _dismiss(AiDialogDismissReason.timeout);
      l.i(AiJoJoController.tag, "动画计时 结束");
    }
  }

  @override
  void dispose() {
    l.i(AiJoJoController.tag, "dispose 销毁");
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _dismiss(AiDialogDismissReason.timeout);
    jojoNativeBridge.notifyAlertManageAlertDidDismiss(
        pagePath: AppPage.planHomeMapPage.path,
        dialogKey: UiDialogDialogKey.aiPhoneCallDialog.dialogKey);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    l.i(AiJoJoController.tag, "组建 didChangeAppLifecycleState $state");
    if (state == AppLifecycleState.paused &&
        _lastAppLifecycleState != AppLifecycleState.paused) {
      // 处于暂停状态 停止播放音频
      _dismiss(AiDialogDismissReason.pageStatChange);
      widget.aiJoJoController?.reportAiDialogDismiss(
          AiDialogDismissReason.timeout,
          widget.encouragementConversationParamsVo.roomTaskId);
    }
    _lastAppLifecycleState = state;
  }

  void playAudios() async {
    l.i(AiJoJoController.tag, "第 $initCount 播放音频开始");
    try {
      String? localAudioUrl = widget.encouragementConversationParamsVo
          .localEncouragementConversationVoice;
      String? audioUrl = widget
          .encouragementConversationParamsVo.encouragementConversationVoice;
      if (localAudioUrl.isNotNullOrEmpty() == true) {
        await _audioPlayer.play(DeviceFileSource(localAudioUrl!));
      } else if (audioUrl.isNotNullOrEmpty() == true) {
        await _audioPlayer.play(UrlSource(audioUrl!));
      }
    } catch (e) {
      l.e(AiJoJoController.tag, "播放音频失败 $e");
    }
    _bgmPlayer.setReleaseMode(ReleaseMode.loop);
    try {
      String? localBgmUrl = widget
          .encouragementConversationParamsVo.localEncouragementConversationBgm;
      String? bgmUrl =
          widget.encouragementConversationParamsVo.encouragementConversationBgm;
      if (localBgmUrl.isNotNullOrEmpty() == true) {
        await _bgmPlayer.play(DeviceFileSource(localBgmUrl!));
      } else if (bgmUrl.isNotNullOrEmpty() == true) {
        await _bgmPlayer.play(UrlSource(bgmUrl!));
      }
    } catch (e) {
      l.e(AiJoJoController.tag, "播放背景音乐失败 $e");
    }
    l.i(AiJoJoController.tag, "第 $initCount 播放音频完成");
    if (_callStop) {
      //解决 音频播放过程中 停止播放失效，声音会一直存在的问题
      l.i(AiJoJoController.tag, "_callStop 后音频播放才完成，重新暂停 ");
      _stopAllAudios();
    }
  }

  void _dismiss(AiDialogDismissReason reason) async {
    if (!_callStop) {
      _callStop = true;
      _animationController.removeStatusListener(_animationStateChangeListener);
      _animationController.stop();
      _animationController.dispose();

      widget.dismiss();
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': reason.reason,
        'learn_segments': widget.aiJoJoState.aiFrequency?.segmentCodeList ?? [],
        'class_ids': widget.aiJoJoState.aiFrequency?.classIds ?? [],
        "custom_state": widget.aiJoJoState.aiFrequency?.customState
      });
    }
    await _stopAllAudios();
  }

  Future<void> _stopAllAudios() async {
    l.i(AiJoJoController.tag, "暂停第 $initCount 次音频开始");
    await _audioPlayer.stop();
    await _audioPlayer.dispose();
    await _bgmPlayer.stop();
    await _bgmPlayer.dispose();
    l.i(AiJoJoController.tag, "暂停第 $initCount 次音频结束");
  }

  void _callAiJoJo() async {
    _dismiss(AiDialogDismissReason.callStart);
    String? router = widget
        .encouragementConversationParamsVo.encouragementConversationRouter;
    if (router == null || router.isEmpty) {
      l.e(AiJoJoController.tag, "跳转失败，路由为空");
      return;
    }
    RunEnv.jumpLink(router);
  }

  @override
  Widget build(BuildContext context) {
    if (LandscapeUtils.isLandscape()) {
      return _buildLandScapeWidget();
    } else {
      return _buildPortraitWidget();
    }
  }

  Widget _buildLandScapeWidget() {
    return Align(
      alignment: Alignment.topCenter,
      child: GestureDetector(
        onTap: () {},
        child: Container(
          width: 780.rdp,
          height: 180.rdp,
          margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.rdp),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5), // 灰色阴影
                  spreadRadius: 2.rdp, // 阴影扩散范围
                  blurRadius: 8.rdp, // 模糊半径
                  offset: const Offset(0, 3), // 阴影偏移量 (x, y)
                ),
              ]),
          child: Row(children: [
            SizedBox(
              width: 80.rdp,
            ),
            Expanded(
                child: Row(
              children: [
                ImageNetworkCached(
                    imageUrl: widget.encouragementConversationParamsVo
                            .encouragementConversationIcon ??
                        "",
                    width: 69.rdp,
                    height: 69.rdp,
                    fit: BoxFit.cover),
                SizedBox(
                  width: 12.rdp,
                ),
                Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                      Text(
                        widget.encouragementConversationParamsVo
                                .encouragementConversationTitle ??
                            "",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 18.rdp,
                            fontWeight: FontWeight.w600,
                            color: context.appColors.jColorGray6),
                      ),
                      Text(
                        widget.encouragementConversationParamsVo
                                .encouragementConversationSubTitle ??
                            "",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 14.rdp,
                            fontWeight: FontWeight.w400,
                            color: context.appColors.jColorGray5),
                      ),
                    ])),
                SizedBox(
                  width: 12.rdp,
                ),
                GestureDetector(
                    onTap: () {
                      _dismiss(AiDialogDismissReason.userCanceled);
                      widget.aiJoJoController?.reportAiDialogDismiss(
                          AiDialogDismissReason.userCanceled,
                          widget.encouragementConversationParamsVo.roomTaskId);
                    },
                    child: SvgAssetWeb(
                      assetName: AssetsImg.AI_JIAOJIAO_AI_JIAO_JIAO_REJECT,
                      width: 112.rdp,
                      height: 54.rdp,
                      fit: BoxFit.cover,
                      package: Config.package,
                    )),
                SizedBox(
                  width: 12.rdp,
                ),
                GestureDetector(
                  onTap: () {
                    _callAiJoJo();
                  },
                  child: SvgAssetWeb(
                    assetName: AssetsImg.AI_JIAOJIAO_AI_JIAO_JIAO_CALL,
                    width: 180.rdp,
                    height: 54.rdp,
                    package: Config.package,
                  ),
                ),
              ],
            )),
            SizedBox(
              width: 80.rdp,
            ),
          ]),
        ),
      ),
    );
  }

  Widget _buildPortraitWidget() {
    return Container(
      margin: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top, left: 16.rdp, right: 16.rdp),
      width: MediaQuery.of(context).size.width,
      child: GestureDetector(
        onTap: () {},
        child: Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24.rdp),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5), // 灰色阴影
                  spreadRadius: 2.rdp, // 阴影扩散范围
                  blurRadius: 8.rdp, // 模糊半径
                  offset: const Offset(0, 3), // 阴影偏移量 (x, y)
                ),
              ]),
          child: GestureDetector(
            onTap: () {},
            child: Container(
              padding:
                  EdgeInsets.symmetric(vertical: 22.rdp, horizontal: 16.rdp),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                Row(children: [
                  ImageNetworkCached(
                    imageUrl: widget.encouragementConversationParamsVo
                            .encouragementConversationIcon ??
                        "",
                    width: 69.rdp,
                    height: 69.rdp,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(
                    width: 12.rdp,
                  ),
                  Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                        Text(
                          widget.encouragementConversationParamsVo
                                  .encouragementConversationTitle ??
                              "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 18.rdp,
                              fontWeight: FontWeight.w600,
                              color: context.appColors.jColorGray6),
                        ),
                        SizedBox(
                          height: 4.rdp,
                        ),
                        Text(
                          widget.encouragementConversationParamsVo
                                  .encouragementConversationSubTitle ??
                              "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 14.rdp,
                              fontWeight: FontWeight.w400,
                              color: context.appColors.jColorGray5),
                        ),
                      ]))
                ]),
                SizedBox(
                  height: 12.rdp,
                ),
                Row(children: [
                  GestureDetector(
                      onTap: () {
                        _dismiss(AiDialogDismissReason.userCanceled);
                        widget.aiJoJoController?.reportAiDialogDismiss(
                            AiDialogDismissReason.userCanceled,
                            widget
                                .encouragementConversationParamsVo.roomTaskId);
                      },
                      child: SvgAssetWeb(
                        assetName: AssetsImg.AI_JIAOJIAO_AI_JIAO_JIAO_REJECT,
                        width: 112.rdp,
                        height: 54.rdp,
                        fit: BoxFit.cover,
                        package: Config.package,
                      )),
                  Expanded(child: Container()),
                  GestureDetector(
                    onTap: () {
                      _callAiJoJo();
                    },
                    child: SvgAssetWeb(
                      assetName: AssetsImg.AI_JIAOJIAO_AI_JIAO_JIAO_CALL,
                      width: 180.rdp,
                      height: 54.rdp,
                      package: Config.package,
                    ),
                  ),
                ])
              ]),
            ),
          ),
        ),
      ),
    );
  }
}
