import 'dart:async';

import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/ai_frequency.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_dialog_dismiss_reason.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/ui_dialog_dialog_key.dart';
import 'package:jojo_flutter_plan_pkg/service/ai_jiao_jiao_service_api.dart';

class AiJoJoController extends Cubit<AiJoJoState> {
  final AIJiaoJiaoServiceApi jiaoJiaoServiceApi;
  final CacheManager cacheManager;
  int encouragementCalledType = 1;

  AiJoJoController(super.initialState, this.jiaoJiaoServiceApi,this.cacheManager) {
    refresh();
  }

  static String tag = 'Ai叫叫电话激励弹窗';

  bool resume = true;

  Future<void> refresh() async {
    AiFrequency? aiFrequency;
    try {
      jojoNativeBridge.removePubcliAbV2GrayScale(grayScaleId: 'ai_encourage');
      aiFrequency = await jiaoJiaoServiceApi.getFrequency(encouragementCalledType);
      String? grayScaleId = aiFrequency.grayInfo?.grayScaleId;
      int? testValue = aiFrequency.grayInfo?.testValue;
      if (grayScaleId != null &&
          grayScaleId.isNotEmpty == true &&
          testValue != null) {
        jojoNativeBridge.addPubcliAbV2GrayScale(
            grayScaleId: grayScaleId, testValue: testValue);
      }
      l.i(tag, "接口请求 完毕");
    } catch (e) {
      l.e(tag, "接口请求异常 $e");
      return;
    }
    try {
      await downIcon(aiFrequency);
      await downLoadAudios(aiFrequency);
      AiJoJoState newState = state.copyWith(
        canShow: aiFrequency.encouragementConversationParamsVo?.canPopupAiCall == true,
        encouragementConversationParamsVo:
            aiFrequency.encouragementConversationParamsVo,
          aiFrequency: aiFrequency,
      );
      if (aiFrequency.encouragementConversationParamsVo?.canPopupAiCall != true) {
        l.i(tag, "接口请求 完毕  不需要展示ai 激励弹窗");
        return;
      }
      l.i(tag, "开始请求ui 管理弹窗");
      NativeValue? nativeValue = await jojoNativeBridge
          .showAlertInManager(
              pagePath: AppPage.planHomeMapPage.path,
              dialogKey: UiDialogDialogKey.aiPhoneCallDialog.dialogKey)
          .then((value) => value.data);
      String? result = nativeValue?.value;
      l.i(tag,
          "数据处理完成 UI框架结果 $result, 页面状态 $resume newState ${newState.canShow}  ${newState.encouragementConversationParamsVo == null}");
      if (result == "1" && resume) {
        //非页面可见状态 不更新ui 弹窗
        safeEmit(newState);
      }
    } catch (e) {
      l.e(tag, "showAlertInManager 异常 $e");
    }
  }

  Future<void> downIcon(AiFrequency aiFrequency) async {
    try {
      String? icon = aiFrequency
          .encouragementConversationParamsVo?.encouragementConversationIcon;
      if (icon?.isNotNullOrEmpty() == true) {
        await cacheManager.getFileFromCache(icon!);
      }
    } catch (e) {
      l.e(tag,
          "下载icon异常 icon:${aiFrequency.encouragementConversationParamsVo?.encouragementConversationIcon} $e");
    }
  }

  Future<void> downLoadAudios(AiFrequency aiFrequency) async {
    List<String> urls = collectNeedDownAudios(aiFrequency);
    Completer completer = Completer();

    void checkAndComplete() {
      if (!completer.isCompleted) {
        completer.complete();
      }
    }

    l.i(tag, "准备开始下载");
    if (urls.isNotEmpty) {
      JoJoResourceManager().downloadUrl(urls,
          successListener: (Map<String, String> downResult) {
        updateAiFrequencyLocalAudios(aiFrequency, downResult);
        checkAndComplete();
      }, failListener: (UnifiedExceptionData failUrl) {
        checkAndComplete();
      });
    } else {
      checkAndComplete();
    }
    l.i(tag, "等待下载");
    await completer.future;
    l.i(tag, "下载完成");
  }

  void updateAiFrequencyLocalAudios(AiFrequency aiFrequency, Map<String, String> downResult) {
    aiFrequency.encouragementConversationParamsVo
        ?.localEncouragementConversationVoice = downResult[aiFrequency.encouragementConversationParamsVo?.encouragementConversationVoice];
    aiFrequency.encouragementConversationParamsVo
        ?.localEncouragementConversationBgm = downResult[aiFrequency.encouragementConversationParamsVo?.encouragementConversationBgm];
  }

  void safeEmit(AiJoJoState newState) {
    if (isClosed) {
      return;
    }
    emit(newState);
  }

  Future<void> dismiss() async {
    AiJoJoState newState = state.copyWith(canShow: false);
    safeEmit(newState);
  }

  List<String> collectNeedDownAudios(AiFrequency aiFrequency) {
    List<String> urls = [];
    String? bgm = aiFrequency
        .encouragementConversationParamsVo?.encouragementConversationBgm;
    if (bgm?.isNotNullOrEmpty() == true) {
      urls.add(bgm!);
    }
    String? voice = aiFrequency
        .encouragementConversationParamsVo?.encouragementConversationVoice;
    if (voice?.isNotNullOrEmpty() == true) {
      urls.add(voice!);
    }
    return urls;
  }

  Future<void> reportAiDialogDismiss(AiDialogDismissReason dismissReason,String? roomTaskId) async {
    try {
      await jiaoJiaoServiceApi.deductFrequency({
        "scene": encouragementCalledType,
        "aiCallStatus": dismissReason.aiCallStatus,
        "roomTaskId": roomTaskId
      });
    } catch (e) {
      l.e(tag,"上报AI激励弹窗关闭异常 $e");
    }
  }
}
