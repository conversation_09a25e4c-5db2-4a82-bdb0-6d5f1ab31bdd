import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_half.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_helper.dart';

class FindStudyPartnerPhoneDialog extends StatefulWidget {
  final ModuleHalfDialogData data;
  const FindStudyPartnerPhoneDialog({super.key, required this.data});

  @override
  State<FindStudyPartnerPhoneDialog> createState() =>
      _FindStudyPartnerPhoneDialogState();
}

class _FindStudyPartnerPhoneDialogState
    extends State<FindStudyPartnerPhoneDialog> {
  @override
  void initState() {
    show();
    super.initState();
  }

  void show() {
    ModuleDialogHelper.getInstance().getModuleHalfDialog(widget.data).show();
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}
