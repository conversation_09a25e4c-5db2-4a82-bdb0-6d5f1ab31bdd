import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';

class CoursePromoteFinishGuideLandScapeWidget extends StatefulWidget {

  final PromoteLessonFinishPopVo? popupInfo;
  final Function()? dismissAction;

  const CoursePromoteFinishGuideLandScapeWidget({super.key, required this.popupInfo, required this.dismissAction});

  @override
  State<StatefulWidget> createState() {
    return CoursePromoteFinishGuideLandScapeWidgetState();
  }
}

class CoursePromoteFinishGuideLandScapeWidgetState extends State<CoursePromoteFinishGuideLandScapeWidget>{

Widget _buildTitleWidget(BuildContext context) {
    return Container(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            height: 30.rdp,
            child: Text(
        widget.popupInfo?.title ?? "",
        style: TextStyle(
          fontSize: 20.rdp,
          fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
          color: context.appColors.jColorGray6,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
          );
  }

  Widget _buildSubTitleWidget(BuildContext context) {
    return Container(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            height: 27.rdp,
            child: Text(
        widget.popupInfo?.subTitle ?? "",
        style: TextStyle(
          fontSize: 18.rdp,
          fontWeight: FontWeight.w400,
          color: context.appColors.jColorGray5,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
          );
  }

  // 埋点
  void _dealWithAppClickBuried() {
    if (widget.popupInfo?.activityId != null) {
      int activityId = widget.popupInfo?.activityId ?? 0;
      int status = widget.popupInfo?.status ?? 0;
      JoJoPromoteFinishBuriedUtils.appClickActivityPopView(activityId, status, widget.popupInfo?.lessonInfo);
    }
  }

  Widget _buildCommitWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.dismissAction?.call();
        _dealWithAppClickBuried();
        if (widget.popupInfo?.type == PromoteLessonFinishPopType.guide && widget.popupInfo?.videoUrl != null) {
          // 需要把埋点参数编码后传过去
          String dataString = jsonEncode(widget.popupInfo?.properties ?? {});
          RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/planHomeVideoIntroducePage?windowType=window&url=${widget.popupInfo?.videoUrl}&dataString=${Uri.encodeComponent(dataString)}");
        }
      },
      child: Container(
      decoration: BoxDecoration(
          color: HexColor('#FCDA00'),
          borderRadius: BorderRadius.circular(20.rdp)),
      height: 40.rdp,
      width: 280.rdp,
      alignment: Alignment.center,
      child: Center(
        child: Text(
          widget.popupInfo?.btnText ?? "",
          style: TextStyle(
            fontSize: 14.rdp,
            color: HexColor('#544300'),
            fontWeight: FontWeight.w400,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildContentWidget(BuildContext context) {
    return SizedBox(
      child : Column(
        children: [
          SizedBox(height: 175.rdp),
          _buildTitleWidget(context),
          SizedBox(height: 10.rdp),
          _buildSubTitleWidget(context),
          SizedBox(height: 20.rdp),
          _buildCommitWidget(context)
        ],
      )
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment:Alignment.center,
      color: HexColor('#000000', 70), // 起始颜色
      child: ClipRRect(
      borderRadius: BorderRadius.all(Radius.circular(32.rdp)),
      child: SizedBox(
          width: 375.rdp,
          height: 395.rdp,
          child: Stack(
            children: [
              Positioned(
                left: 0.rdp,
                right: 0.rdp,
                bottom: 0.rdp,
                child: ClipRRect(
      borderRadius: BorderRadius.only(topRight: Radius.circular(32.rdp), topLeft: Radius.circular(32.rdp)),
      child: Container(
          width: double.infinity,
          height: 325.rdp,
          decoration: BoxDecoration(
            color: HexColor("#ffffff"),
          ),
          child: _buildContentWidget(context),
      ),
      ),),
      Positioned(
        top: 0.rdp,
        left: 0.rdp,
        right: 0.rdp,
        child: SizedBox(
          width: double.infinity,
          height: 236.rdp,
          child: ImageNetworkCached(
              imageUrl: widget.popupInfo?.icon ?? "",
              fit: BoxFit.cover,
            ),
        ))
            ],
          )),
    ),
    );
  }
}