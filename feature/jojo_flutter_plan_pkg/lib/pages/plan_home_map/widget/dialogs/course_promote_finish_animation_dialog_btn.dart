
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';

class CoursePromoteFinishAnimationDialogBtnWidget extends StatefulWidget {
  final int? medalId;
  final bool isLandSpace;
  final bool isFirstGet;  // 是否是首次获得
  final PromoteLessonFinishModel? model;
  final LessonInProgressGiftModel? giftModel;
  final LessonInProgressGiftRewardModel? currentRewardModel;
  final Function(bool shouldClosePopup)? onclickCallBack;
  final Function()? disMissCallBack;

  const CoursePromoteFinishAnimationDialogBtnWidget({
    Key? key,
    required this.medalId,
    required this.model,
    required this.giftModel,
    required this.isLandSpace,
    required this.isFirstGet,
    required this.currentRewardModel,
    required this.onclickCallBack,
    required this.disMissCallBack
  }) : super(key: key);

  @override
  CoursePromoteFinishAnimationDialogBtnWidgetState createState() => CoursePromoteFinishAnimationDialogBtnWidgetState();
}

class CoursePromoteFinishAnimationDialogBtnWidgetState extends State<CoursePromoteFinishAnimationDialogBtnWidget> {

  void buriedData(String? kElementName) {
    int activityId = widget.model?.activityId ?? 0;
    int status = widget.model?.status ?? 0;
    int? taskType = widget.currentRewardModel?.type;
    String elementName = PromoteLessonFinishModel.getGiftBtnName(context, widget.currentRewardModel?.type, widget.currentRewardModel?.isGet ?? false);
    if (kElementName != null) {
      elementName = kElementName;
    }
    JoJoPromoteFinishBuriedUtils.appClickActivityMedalGit(activityId, status, widget.model?.lessonInfo, elementName, taskType);
  }

  void shareBtnOnClick() {
    buriedData(null);
    //跳转分享页
    if (widget.currentRewardModel?.medalId != null) {
      widget.onclickCallBack?.call(false);
      RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/shareAchievement?windowType=window&medalId=${widget.currentRewardModel?.medalId}");
    } else {
      l.e("促完课执行弹窗", "奖章分享页缺少奖章ID");
      widget.disMissCallBack?.call();
    }
  }

  void nodeBtnOnClick() {
    // 点击虚拟奖励，直接关闭弹窗
    String route = widget.currentRewardModel?.route ?? "";
    buriedData(null);
    if (widget.currentRewardModel?.type != PromoteLessonRewardsType.virtual &&
        widget.currentRewardModel?.type != PromoteLessonRewardsType.dress) {
      if (route.isNotEmpty) {
        widget.onclickCallBack?.call(false);
        RunEnv.jumpLink(route);  // 跳转过去后不关闭当前弹窗
      } else {
        l.e("促完课执行弹窗", "奖励详情地址为空");
        widget.disMissCallBack?.call();
      }
    } else {
      widget.onclickCallBack?.call(true);
    }
  }

  Widget _buildShareBtnWidget(BuildContext context) {
    int? taskType = widget.currentRewardModel?.type;
    bool isGit = widget.currentRewardModel?.isGet ?? false;
    String title = PromoteLessonFinishModel.getGiftBtnName(context, taskType, isGit);
    return GestureDetector(
      onTap: () {
        if (widget.currentRewardModel?.type == PromoteLessonRewardsType.normal) {
          shareBtnOnClick();
        } else {
          nodeBtnOnClick();
        }
      },
      child: Container(
      decoration: BoxDecoration(
          color: HexColor('#FCDA00'),
          borderRadius: BorderRadius.circular(ConfigSize.madelGetPageBtnHeight/2.0)),
      width: ConfigSize.landSpaceMadelGetPageBtnWidth,
      height: ConfigSize.madelGetPageBtnHeight,
      alignment: Alignment.center,
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            fontSize: 18.rdp,
            color: HexColor('#544300'),
            fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    ),
    );
  }

  Widget _buildCancelWidget(BuildContext context) {
    double btnWidth = 100.rdp;
    double btnHeight = 27.rdp;
    bool showGetButton = widget.isFirstGet && widget.currentRewardModel?.type == PromoteLessonRewardsType.normal;
    if (!showGetButton) {
      // 占位，避免UI显示下移
      return Container(
        width: btnWidth,
        height: btnHeight,
        color: Colors.transparent,
      );
    }
    return GestureDetector(
      onTap: () {
        buriedData("直接收下");
        widget.onclickCallBack?.call(true);
      },
      child: Container(
        width: btnWidth,
        height: btnHeight,
        alignment: Alignment.center,
        child: Center(
          child: Text(
            S.of(context).takeIt,
            style: TextStyle(
              fontSize: 18.rdp,
              color: HexColor('#DBAF00'),
              fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [
        _buildShareBtnWidget(context),
        SizedBox(height: 15.rdp,),
          _buildCancelWidget(context)
       ],
      ),
    );
  }
}
