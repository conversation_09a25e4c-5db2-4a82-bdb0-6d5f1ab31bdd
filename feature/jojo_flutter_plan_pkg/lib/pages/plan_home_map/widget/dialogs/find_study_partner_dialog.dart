import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_half.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_helper.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/find_study_partner_phone_dialog.dart';

typedef DataCallback = Future<bool> Function();
typedef DialogActionCallback = Function();

class FindStudyPartnerDialog extends BaseDialogWidget {
  final String coverImgUrl;
  final String router;
  final DataCallback dataCallback;


  FindStudyPartnerDialog(
      {super.key,
      required this.coverImgUrl,
      required this.router,
      required this.dataCallback,
      required super.onDialogDismiss,
        required super.onDialogShow,})
      : super(
          pagePath: DialogsMessage.studyPartnerDialog.pagePath,
          dialogKey: DialogsMessage.studyPartnerDialog.key,
          dialogSort: DialogsMessage.studyPartnerDialog.sort,
          dialogType: DialogsMessage.studyPartnerDialog.dialogType,

        );

  @override
  BaseDialogWidgetState<FindStudyPartnerDialog> createState() =>
      _FindStudyPartnerDialogState();

  @override
  Future<bool> canShowDialog() async {
    bool canShow = await dataCallback.call();
    return canShow;
  }
}

class _FindStudyPartnerDialogState
    extends BaseDialogWidgetState<FindStudyPartnerDialog> {
  late CachedNetworkImageProvider imageProvider;
  late Future<ImageInfo?> _imageInfoFuture;

  @override
  void initState() {
    l.d("FindStudyPartnerDialog", "引导弹窗：：学伴");
    RunEnv.sensorsTrack('\$AppViewScreen', {"\$screen_name": "我的学伴_新手弹窗浏览"});
    imageProvider = CachedNetworkImageProvider(widget.coverImgUrl);
    _imageInfoFuture = _loadImage();
    super.initState();
  }

  Future<ImageInfo?> _loadImage() async {
    final completer = Completer<ImageInfo?>();
    try {
      final imageStream = imageProvider.resolve(ImageConfiguration.empty);

      final listener = ImageStreamListener(
        (ImageInfo imageInfo, bool _) {
          if (!completer.isCompleted) {
            completer.complete(imageInfo);
          }
        },
        onError: (dynamic exception, StackTrace? stackTrace) {
          l.e('图片加载失败', exception.toString());
          if (!completer.isCompleted) {
            completer.complete(null);
          }
        },
      );

      imageStream.addListener(listener);

      // 确保监听器最终会被移除
      completer.future.then((_) {
        imageStream.removeListener(listener);
      }).catchError((_) {
        imageStream.removeListener(listener);
        widget.dismissDialog();
      });
    } catch (e) {
      l.e('图片加载失败', e.toString());
      if (!completer.isCompleted) {
        completer.complete(null);
        widget.dismissDialog();
      }
    }
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ImageInfo?>(
        future: _imageInfoFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const SizedBox.shrink();
          }

          if (!snapshot.hasData || snapshot.data == null) {
            return const SizedBox.shrink();
          }
          return _buildBody();
        });
  }

  Widget _buildBody() {
    return AdaptiveOrientationLayout(
      portrait: (context) => FindStudyPartnerPhoneDialog(
        data: generaterModuleHalfDialogData(context, false),
      ),
      landscape: (context) => FindStudyPartnerPhoneDialog(
        data: generaterModuleHalfDialogData(context, true),
      ),
    );
  }

  ModuleHalfDialogData generaterModuleHalfDialogData(
      BuildContext context, bool isLand) {
    ModuleHalfDialogData data = ModuleHalfDialogData(
        cancelCallback: cancelCallback,
        sureCallback: sureCallback,
        cancelText: S.of(context).eyeProtectionNextTime,
        sureText: S.of(context).goAndSee,
        isLand: isLand,
        outImage: true,
        onDismiss: () {
          widget.dismissDialog();
        },
        topImageWidget: CachedNetworkImage(imageUrl: widget.coverImgUrl))
      ..tag = widget.dialogKey;
    return data;
  }

  void sureCallback() async {
    RunEnv.sensorsTrack('\$AppClick', {"\$element_name": "我的学伴_新手弹窗浏览_去看看"});
    if (widget.router.isEmpty) {
      return;
    }
    await ModuleDialogHelper.getInstance().dismissWithTag();
    RunEnv.jumpLink(widget.router);
  }

  void cancelCallback() {
    RunEnv.sensorsTrack('\$AppClick', {"\$element_name": "我的学伴_新手弹窗浏览_下次再说"});
    ModuleDialogHelper.getInstance().dismissWithTag();
  }
}
