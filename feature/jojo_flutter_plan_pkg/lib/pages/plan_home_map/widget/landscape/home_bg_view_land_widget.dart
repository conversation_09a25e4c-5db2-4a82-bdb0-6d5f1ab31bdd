import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import '../../model/course_continue_study_stream_data.dart';

class HomeBgViewLandWidget extends StatefulWidget {
  final int? topUserAnimatorDuring;
  final double? currentBgProcess;
  final Color? currentBgColor;
  final Color? targetBgColor;
  final int? currentTabSubject;
  final StreamController<CourseContinueStudyStreamData>? scrollerController;
  final double? height;
  final String? localPersonalImagePath;
  final String? activityHeader;

  const HomeBgViewLandWidget(
      {Key? key,
      this.height,
      this.topUserAnimatorDuring,
      this.currentBgProcess,
      this.currentBgColor,
      this.targetBgColor,
      this.currentTabSubject,
      this.localPersonalImagePath,
      required this.scrollerController,
      required this.activityHeader})
      : super(key: key);

  @override
  HomeBgViewLandWidgetState createState() => HomeBgViewLandWidgetState();
}

class HomeBgViewLandWidgetState extends State<HomeBgViewLandWidget> {
  double? currentBgProcess;
  Color? currentBgColor;
  Color? targetBgColor;
  int? currentTabSubject;
  StreamSubscription? _streamSubscription;
  String? localDressImg = '';

  @override
  void initState() {
    super.initState();
    currentBgColor = widget.currentBgColor;
    currentBgProcess = widget.currentBgProcess;
    targetBgColor = widget.targetBgColor;
    currentTabSubject = widget.currentTabSubject;

    _streamSubscription = widget.scrollerController?.stream.listen((data) {
      print('Received data: $data');
      setState(() {
        currentBgColor = data.currentColor;
        targetBgColor = data.targetColor;
        currentBgProcess = data.scrollPercentage;
      });
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant HomeBgViewLandWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    currentBgColor = widget.currentBgColor;
    currentBgProcess = widget.currentBgProcess;
    targetBgColor = widget.targetBgColor;
    currentTabSubject = widget.currentTabSubject;
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Container(
      height: widget.height,
      width: screenWidth,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            getColorTransition(currentBgColor ?? Colors.transparent,
                    targetBgColor ?? Colors.transparent, currentBgProcess ?? 0)
                .withOpacity(1.0),
            getColorTransition(currentBgColor ?? Colors.transparent,
                    targetBgColor ?? Colors.transparent, currentBgProcess ?? 0)
                .withOpacity(1.0),
            getColorTransition(currentBgColor ?? Colors.transparent,
                    targetBgColor ?? Colors.transparent, currentBgProcess ?? 0)
                .withOpacity(0.5),
            getColorTransition(currentBgColor ?? Colors.transparent,
                    targetBgColor ?? Colors.transparent, currentBgProcess ?? 0)
                .withOpacity(0.0),
          ],
        ),
      ),
    );
  }
}

Color hexToColor(String hexString) {
  final buffer = StringBuffer();
  if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
  buffer.write(hexString.replaceFirst('#', ''));
  return Color(int.parse(buffer.toString(), radix: 16));
}

Color getColorTransition(
    Color currentColor, Color targetColor, double progress) {
  if (progress < 0) return currentColor;
  if (progress > 1) return targetColor;

  return Color.lerp(currentColor, targetColor, progress)!;
}
