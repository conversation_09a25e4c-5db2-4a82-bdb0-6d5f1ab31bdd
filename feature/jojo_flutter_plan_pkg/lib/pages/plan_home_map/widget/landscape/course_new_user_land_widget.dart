import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';

import '../../controller.dart';

class CourseNewUserLandWidget extends StatefulWidget {
  final int? tabSubject;

  const CourseNewUserLandWidget({super.key, this.tabSubject});

  @override
  State<CourseNewUserLandWidget> createState() =>
      _CourseNewUserLandWidgetState();
}

class _CourseNewUserLandWidgetState extends State<CourseNewUserLandWidget> {
  final _gotSpineController = JoJoSpineAnimationController();

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    String recoursePath =
        pageController?.tabAnimationFilePathMap?["${widget.tabSubject}_path"] ??
            "";
    return SizedBox(
      width: screenWidth,
      height: 100,
      child: Center(
        child: SizedBox(
          width: 780.rdp,
          height: 100,
          child: Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
              width: 100,
              height: 100,
              child: _buildSpineWidget(recoursePath, 'loop'),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpineWidget(String spineResource, String assetName) {
    File? atlasFile;
    File? skelFile;
    try {
      atlasFile = findFilesByName(spineResource, "welcome_icon.atlas.txt");
      skelFile = findFilesByName(spineResource, "welcome_icon.skel.bytes");
    } catch (e) {
      l.e("2025上课页科目动态资源", "竖版纯新用户资源包异常，$e");
    }
    if (atlasFile == null || skelFile == null) {
      l.e("2025上课页科目动态资源",
          "横版纯新用户资源包异常,atlasFile = $atlasFile,skelFile = $skelFile");
      return const SizedBox();
    }
    Key spineKey = ValueKey('$atlasFile-$skelFile');

    return Container(
      key: spineKey,
      child: JoJoSpineAnimationWidget(
        atlasFile.path,
        skelFile.path,
        LoadMode.file,
        useRootBoneAlign: true,
        fit: BoxFit.none,
        _gotSpineController,
        onInitialized: (controller) {
          controller.skeleton
            ..setScaleX(1)
            ..setScaleY(1);
          _gotSpineController.playAnimation(JoJoSpineAnimation(
              animaitonName: assetName, trackIndex: 0, loop: true, delay: 0));
        },
      ),
    );
  }

  PlanHomeWithMapviewCtrl? get pageController =>
      mounted ? context.read<PlanHomeWithMapviewCtrl>() : null;
}
