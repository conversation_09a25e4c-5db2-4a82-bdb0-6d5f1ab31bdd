import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';

/// spine播放组件
class CourseSpineAnimationWidget extends StatefulWidget {
  final JoJoSpineAnimationController animationController;
  final String? atlasFilePath;
  final String? skelFilePath;
  final String? animationName;
  final String? animationDurationName;
  final double? scale;
  final bool? loop;
  final bool? playAnimation;
  final Function(AnimationEventType type)? callBack;
  final Function(double animationDuration)? initializedCallBack;

  const CourseSpineAnimationWidget({
    Key? key,
    required this.animationController,
    required this.atlasFilePath,
    required this.skelFilePath,
    required this.animationName,
    required this.scale,
    required this.loop,
    required this.callBack,
    required this.playAnimation,
    this.initializedCallBack,
    this.animationDurationName
  }) : super(key: key);

  @override
  CourseSpineAnimationWidgetState createState() => CourseSpineAnimationWidgetState();
}

class CourseSpineAnimationWidgetState extends State<CourseSpineAnimationWidget> {

  void animationPlay() {
    if (widget.playAnimation ?? true) {
      widget.animationController.playAnimation(JoJoSpineAnimation(
        animaitonName: widget.animationName ?? "",
        trackIndex: 0,
        loop: widget.loop ?? false,
        delay: 0, listener: _spineAnimationEvent));
    }
  }

  /// 动效动画完成回调
  _spineAnimationEvent(AnimationEventType type) {
    widget.callBack?.call(type);
  }

  @override
  Widget build(BuildContext context) {
    return JoJoSpineAnimationWidget(
      widget.atlasFilePath ?? "",
      widget.skelFilePath ?? "",
      LoadMode.file,
      useRootBoneAlign: true,
      fit: BoxFit.none,
      widget.animationController,
      onInitialized: (controller) {
        if (mounted) {
          controller.skeleton
        ..setScaleX(widget.scale ?? 1)
        ..setScaleY(widget.scale ?? 1);
          animationPlay();
          if (widget.initializedCallBack != null && widget.animationDurationName != null) {
            double duration = controller.skeletonData
                .findAnimation(widget.animationDurationName ?? "")?.getDuration() ?? 0.0;
            widget.initializedCallBack?.call(duration);
          } else {
            widget.initializedCallBack?.call(0.0);
          }
        }
      },
    );
  }
}
