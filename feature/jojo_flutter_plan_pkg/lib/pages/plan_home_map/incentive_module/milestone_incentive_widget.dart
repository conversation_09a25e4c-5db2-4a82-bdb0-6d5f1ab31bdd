import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/milestone_incentive_landspac_item_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'milestone_incentive_item_widget.dart';

class MilestoneIncentiveWidget extends StatefulWidget {
  final bool isLand;
  final int subjectType;
  final Color subjectColor;
  final TopUserInfo? topUserInfo;
  final int? topUserAnimatorDuring;
  final bool isPlayAnimator;

  const MilestoneIncentiveWidget(
      {super.key,
      required this.isLand,
      required this.subjectType,
      required this.subjectColor,
      this.topUserInfo,
      this.topUserAnimatorDuring,
      this.isPlayAnimator = false});

  @override
  State<MilestoneIncentiveWidget> createState() =>
      _MilestoneIncentiveWidgetState();
}

class _MilestoneIncentiveWidgetState extends State<MilestoneIncentiveWidget> {
  final phoneSize = Size(210.rdp, 130.rdp);
  final padSize = Size(440.rdp, 110.rdp);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  get _currentSize => widget.isLand ? padSize : phoneSize;

  bool get _isLand => widget.isLand;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: _currentSize.width,
      height: _currentSize.height,
      padding: EdgeInsets.only(bottom: _isLand ? 0.rdp : 46.rdp),
      child: _isLand
          ? MilestoneIncentiveLandspacItemWidget(
              subjectColor: widget.subjectColor,
              subjectType: widget.subjectType,
              topUserInfo: widget.topUserInfo,
              requestSubjectType:
                  pageController?.state.topUserRequestSubjectType ?? 0,
              topUserAnimatorDuring: widget.topUserAnimatorDuring,
            )
          : MilestoneIncentiveItemWidget(
              subjectColor: widget.subjectColor,
              subjectType: widget.subjectType,
              requestSubjectType:
                  pageController?.state.topUserRequestSubjectType ?? 0,
              topUserInfo: widget.topUserInfo,
              topUserAnimatorDuring: widget.topUserAnimatorDuring,
            ),
    );
  }

  PlanHomeWithMapviewCtrl? get pageController =>
      mounted ? context.read<PlanHomeWithMapviewCtrl>() : null;
}
