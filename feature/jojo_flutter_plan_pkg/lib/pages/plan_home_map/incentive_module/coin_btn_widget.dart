import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/resources/jojo_colors.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/after_layout.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/animated_flip_counter.dart';
import 'package:path/path.dart';

/// 按钮类型枚举
enum CoinBtnType {
  badge,
  coin;

  String get elementName =>
      this == CoinBtnType.badge ? "学习主页_成就奖章入口" : "学习主页_货币入口";

  int get maxNumber => this == CoinBtnType.badge ? 999 : 99999;
}

/// 按钮样式配置
class CoinBtnWidgetStyle {
  final Size iconSize;
  final double fontSize;
  final int animatorDuring;

  const CoinBtnWidgetStyle(
      {required this.iconSize,
      required this.fontSize,
      required this.animatorDuring});
}

class CoinBtnWidget extends StatelessWidget {
  final String router;
  final bool isHide;
  final int number;
  final CoinBtnType type; // 使用枚举替代字符串
  final int rewardType;
  final String icon;
  final CoinBtnWidgetStyle style;
  final int subjectType;
  final Color subjectColor;

  const CoinBtnWidget(
      {super.key,
      required this.router,
      required this.number,
      required this.type,
      required this.rewardType,
      required this.isHide,
      required this.subjectType,
      required this.icon,
      required this.subjectColor,
      required this.style});

  @override
  Widget build(BuildContext context) {
    if (isHide) {
      return Container();
    }
    final String text = number > type.maxNumber ? '...' : number.toString();
    final bool isNumber = text != '...';

    return AfterLayout(
      callback: (value) => CourseHelper().rewardMap[rewardType] = value.offset,
      child: isNumber
          ? _buildCoinNumber(context, int.parse(text))
          : _buildCoinMore(context, text),
    );
  }

  /// 处理点击事件
  void _handleTap() {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': type.elementName,
      'custom_state': subjectType,
    });
    RunEnv.jumpLink(router);
  }

  /// 构建带数字动画的按钮
  Widget _buildCoinNumber(BuildContext context, int value) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedFlipCounter(
        duration: Duration(milliseconds: style.animatorDuring),
        icon: _buildIcon(context.appColors.colorVariant5(subjectColor)),
        value: value,
        textStyle: _getTextStyle(context),
      ),
    );
  }

  /// 构建显示省略号的按钮
  Widget _buildCoinMore(BuildContext context, String text) {
    return JoJoBtn(
      icon: _buildIcon(context.appColors.colorVariant5(subjectColor)),
      text: text,
      fontSize: style.fontSize,
      fontColor: context.appColors.colorVariant6(subjectColor).withAlpha(178),
      color: Colors.transparent,
      tapHandle: _handleTap,
    );
  }

  /// 构建图标
  Widget _buildIcon(Color color) => ImageNetworkCached(
        imageUrl: icon,
        fit: BoxFit.contain,
        height: style.iconSize.height,
        width: style.iconSize.width,
      );

  /// 获取文本样式
  TextStyle _getTextStyle(BuildContext context) => TextStyle(
        fontSize: style.fontSize,
        color: context.appColors.colorVariant6(subjectColor).withAlpha(178),
        fontFamily: 'MohrRounded_Bold',
        letterSpacing: -1.0,
        package: 'jojo_flutter_base',
      );
}
