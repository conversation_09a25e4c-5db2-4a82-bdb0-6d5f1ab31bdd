import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/subject_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/test_dialog.dart';
import 'package:jojo_flutter_plan_pkg/utils/num_to_image.dart';

import '../mixins/milestion_incentive_mixin.dart';
import 'coin_btn_widget.dart';

class MilestoneIncentiveItemWidget extends StatefulWidget {
  final TopUserInfo? topUserInfo;
  final int subjectType;
  final Color subjectColor;
  final int? topUserAnimatorDuring;

  final int requestSubjectType;

  const MilestoneIncentiveItemWidget(
      {super.key,
      required this.subjectType,
      required this.subjectColor,
      required this.requestSubjectType,
      this.topUserInfo,
      this.topUserAnimatorDuring});

  @override
  State<MilestoneIncentiveItemWidget> createState() =>
      _MilestoneIncentiveItemWidgetState();
}

class _MilestoneIncentiveItemWidgetState
    extends State<MilestoneIncentiveItemWidget>
    with MilestionIncentiveMixin, TickerProviderStateMixin {
  final jojoSpineController = JoJoSpineAnimationController();
  final jojoBoxSpineController = JoJoSpineAnimationController();
  late AnimationController sceneChangeController;
  late AnimationController tipIntoController;
  late Animation<double> opacityValue;
  late Animation<double> tipAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    tipIntoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    sceneChangeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // 创建动画
    opacityValue = sceneChangeController.drive(
      Tween<double>(begin: 0.0, end: 1.0),
    );

    tipAnimation = tipIntoController.drive(
      Tween<double>(begin: 0.0, end: 1.0),
    );
  }

  @override
  void dispose() {
    tipIntoController.dispose();
    sceneChangeController.dispose();
    jojoSpineController.dispose();
    jojoBoxSpineController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant MilestoneIncentiveItemWidget oldWidget) {
    l.d("里程碑动画", "切换场景${sceneChangeController.value}");
    if (oldWidget.requestSubjectType != widget.requestSubjectType) {
      sceneChangeController
          .reset(); // 用户可能不会等 宝箱动画播放完成，切换 tab 后，宝箱动画会被中断无法正常重置，所以需要重置场景动画
    }
    super.didUpdateWidget(oldWidget);
  }

  Widget _buildNumberWidget() {
    int continuousNum = widget.topUserInfo?.continuous?.days ?? 0;
    int coinIconNum = widget.topUserInfo?.learnBean?.amount ?? 0;
    int coinRewardTypeType =
        widget.topUserInfo?.learnBean?.type ?? -1; //激励右边icon类型
    int badgeIconNum = widget.topUserInfo?.medal?.amount ?? 0;
    int badgeRewardTypeType =
        widget.topUserInfo?.medal?.type ?? -1; //激励左边icon类型
    int isStart = widget.topUserInfo?.continuous?.isStart ?? 0;
    String notStartText = widget.topUserInfo?.continuous?.notStartText ?? "";
    return Container(
      padding: EdgeInsets.only(top: 2.rdp),
      height: 88.rdp,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          isStart == 1
              ? Row(
                  children: buildNumberImages(
                    continuousNum,
                    36.rdp,
                    9999,
                  ),
                )
              : Text(notStartText,
                  style: TextStyle(
                      fontSize: 14.rdp,
                      fontWeight: FontWeight.w500,
                      color: HexColor("#E35B00"))),
          Row(
            children: [
              CoinBtnWidget(
                router: widget.topUserInfo?.medal?.jumpRoute ?? "",
                number: badgeIconNum,
                type: CoinBtnType.badge,
                isHide: widget.topUserInfo?.medal?.isShow != 1,
                rewardType: badgeRewardTypeType,
                icon: widget.topUserInfo?.medal?.icon ?? "",
                subjectType: widget.subjectType,
                subjectColor: widget.subjectColor,
                style: CoinBtnWidgetStyle(
                    iconSize: Size(16.rdp, 16.rdp),
                    fontSize: 17.rdp,
                    animatorDuring: widget.topUserAnimatorDuring ?? 0),
              ),
              // buildJoJoBtn(badgeIconNum, 'badge', badgeRewardTypeType),
              SizedBox(
                width: widget.topUserInfo?.medal?.isShow != 1 ? 0.rdp : 8.rdp,
              ),
              CoinBtnWidget(
                router: widget.topUserInfo?.learnBean?.jumpRoute ?? "",
                number: coinIconNum,
                type: CoinBtnType.coin,
                isHide: widget.topUserInfo?.learnBean?.isShow != 1,
                rewardType: coinRewardTypeType,
                icon: widget.topUserInfo?.learnBean?.icon ?? "",
                subjectType: widget.subjectType,
                subjectColor: widget.subjectColor,
                style: CoinBtnWidgetStyle(
                    iconSize: Size(16.rdp, 16.rdp),
                    fontSize: 17.rdp,
                    animatorDuring: widget.topUserAnimatorDuring ?? 0),
              ),
              SizedBox(
                width: 16.rdp,
              )
            ],
          )
        ],
      ),
    );
  }

  String get _progressText =>
      widget.topUserInfo?.continuous?.milestone?.progressText ?? "";

  int get _studyStaus => widget.topUserInfo?.continuous?.status ?? 1;

  bool get _hasMilestone =>
      widget.topUserInfo?.continuous?.milestone?.progressText?.isNotEmpty ??
      false;

  void _startAnima(TopUserInfo? toUserInfo) async {
    if (widget.subjectType != widget.requestSubjectType) {
      return;
    }

    var canShow =
        await getFinishState(widget.topUserInfo, widget.requestSubjectType);
    if (!canShow) {
      return;
    }
    saveFinishState(widget.subjectType);
    l.i("里程碑动画", "动画执行");
    if (mounted) {
      sceneChangeController.value = 1.0;
      tipIntoController.reset();
      tipIntoController.forward();
      jojoBoxSpineController.playAnimationList(boxAnimat);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints.expand(),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                reportClickTracking(eventMap: {
                  '\$element_name': "学习主页_连续学入口",
                  'custom_state': getSubjectName(widget.subjectType),
                });
                RunEnv.jumpLink(
                    widget.topUserInfo?.continuous?.jumpRoute ?? "");
              },
              child: buildSpineWidget(
                  Size(88.rdp, 88.rdp),
                  key: getReBuildSpineKey(
                      milesJoJoSpine, widget.requestSubjectType, _progressText),
                  jojoSpineController,
                  atlasFile: milesJoJoSpine.atlasFile,
                  skelFile: milesJoJoSpine.skelFile, onInitialized: () {
                startJoJoAnima(
                    isSubjectTypeEqual:
                        widget.subjectType == widget.requestSubjectType,
                    hasMilestone: _hasMilestone,
                    animatStatus: _studyStaus,
                    subjectType: widget.subjectType,
                    spineController: jojoSpineController,
                    topUserInfo: widget.topUserInfo);
              }),
            ),
            Expanded(
              flex: 1,
              child: Container(
                  key: getReBuildSpineKey(
                      milesBoxSpine, widget.requestSubjectType, _progressText),
                  child: _buildChangeAnimatWidget(sceneChangeController,
                      opacityValue, tipIntoController, tipAnimation)),
            )
          ]),
    );
  }

  Widget _buildTreasuresBox(
      AnimationController tipIntoController, Animation<double> tipAnimation) {
    return SizedBox(
      height: 88.rdp,
      width: 88.rdp,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: AlignmentDirectional.bottomStart,
        children: [
          if (milesBoxSpine.atlasFile != null && milesBoxSpine.skelFile != null)
            SizedBox(
              width: 88.rdp,
              height: 88.rdp,
              child: Stack(
                clipBehavior: Clip.none,
                alignment: AlignmentDirectional.bottomCenter,
                children: [
                  GestureDetector(
                    onTap: () {
                      reportClickTracking(eventMap: {
                        '\$element_name': "学习页_里程碑入口",
                        'custom_state': getSubjectName(widget.subjectType),
                      });
                      RunEnv.jumpLink(
                          widget.topUserInfo?.continuous?.jumpRoute ?? "");
                    },
                    child: buildSpineWidget(
                        Size(88.rdp, 88.rdp), jojoBoxSpineController,
                        atlasFile: milesBoxSpine.atlasFile,
                        skelFile: milesBoxSpine.skelFile, onInitialized: () {
                      _startAnima(widget.topUserInfo);
                    }),
                  ),
                  _buildTipAnimateWidget(tipIntoController, tipAnimation)
                ],
              ),
            ),
        ],
      ),
    );
  }

  Positioned _buildTipAnimateWidget(
      AnimationController tipIntoController, Animation<double> tipAnimation) {
    return Positioned(
        bottom: 5.rdp,
        child: AnimatedBuilder(
          animation: tipIntoController,
          builder: (context, child) {
            final progressText =
                widget.topUserInfo?.continuous?.milestone?.progressText ?? "";
            return progressText.isEmpty
                ? Container()
                : Opacity(
                    opacity: tipAnimation.value,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 8.rdp, vertical: 1.rdp),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(75.rdp)),
                      child: Text(
                        progressText,
                        style: context.textstyles.bodyText.pf.copyWith(
                            fontSize: 12.rdp,
                            fontWeight: FontWeight.w500,
                            color: HexColor("#B76024")),
                      ),
                    ),
                  );
          },
        ));
  }

  Widget _buildChangeAnimatWidget(
      AnimationController controller,
      Animation<double> opacityValue,
      AnimationController tipIntoController,
      Animation<double> tipAnimation) {
    return AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          return Stack(
            alignment: AlignmentDirectional.centerStart,
            children: [
              Offstage(
                offstage: opacityValue.value == 0,
                child: Opacity(
                  opacity: opacityValue.value,
                  child: _buildTreasuresBox(tipIntoController, tipAnimation),
                ),
              ),
              Offstage(
                offstage: 1 - opacityValue.value == 0,
                child: Opacity(
                  opacity: 1 - opacityValue.value,
                  child: GestureDetector(
                    onTap: () {
                      l.d("sensorsTrack", "学习页_连续学入口_连续天数");
                      reportClickTracking(eventMap: {
                        '\$element_name': "学习页_连续学入口_连续天数",
                        'custom_state': getSubjectName(widget.subjectType),
                      });
                      RunEnv.jumpLink(
                          widget.topUserInfo?.continuous?.jumpRoute ?? "");
                    },
                    child: _buildNumberWidget(),
                  ),
                ),
              )
            ],
          );
        });
  }

  @override
  String get spineRes =>
      pageController
          ?.tabAnimationFilePathMap?["${widget.requestSubjectType}_path"] ??
      "";

  @override
  Function get boxAnimaOnEnd => () {
        if (mounted) {
          sceneChangeController.reverse();
        }
      };

  PlanHomeWithMapviewCtrl? get pageController =>
      mounted ? context.read<PlanHomeWithMapviewCtrl>() : null;

  @override
  Widget buildIconWidget() {
    return SizedBox(width: 88.rdp, height: 88.rdp);
  }
}
