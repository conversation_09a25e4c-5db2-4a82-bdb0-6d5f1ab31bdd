import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/subject_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/coin_btn_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/mixins/milestion_incentive_mixin.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'package:jojo_flutter_plan_pkg/utils/num_to_image.dart';

class MilestoneIncentiveLandspacItemWidget extends StatefulWidget {
  final int subjectType;
  final Color subjectColor;
  final TopUserInfo? topUserInfo;
  final int? topUserAnimatorDuring;
  final int requestSubjectType;

  const MilestoneIncentiveLandspacItemWidget({
    super.key,
    required this.subjectType,
    required this.subjectColor,
    required this.requestSubjectType,
    this.topUserInfo,
    this.topUserAnimatorDuring,
  });

  @override
  State<MilestoneIncentiveLandspacItemWidget> createState() =>
      _MilestoneIncentiveLandspacItemWidgetState();
}

class _MilestoneIncentiveLandspacItemWidgetState
    extends State<MilestoneIncentiveLandspacItemWidget>
    with TickerProviderStateMixin, MilestionIncentiveMixin {
  final jojoSpineController = JoJoSpineAnimationController();
  final jojoBoxSpineController = JoJoSpineAnimationController();
  late AnimationController sceneChangeController;
  late AnimationController tipIntoController;
  late Animation<double> opacityValue;
  late Animation<double> tipAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    tipIntoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    sceneChangeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    opacityValue = sceneChangeController.drive(
      Tween<double>(begin: 0.0, end: 1.0),
    );

    tipAnimation = tipIntoController.drive(
      Tween<double>(begin: 0.0, end: 1.0),
    );
  }

  @override
  void dispose() {
    jojoSpineController.dispose();
    jojoBoxSpineController.dispose();
    tipIntoController.dispose();
    sceneChangeController.dispose();
    super.dispose();
  }

  String get _progressText =>
      widget.topUserInfo?.continuous?.milestone?.progressText ?? "";

  int get _studyStaus => widget.topUserInfo?.continuous?.status ?? 1;

  bool get _hasMilestone =>
      widget.topUserInfo?.continuous?.milestone?.progressText?.isNotEmpty ??
      false;

  void _startAnima(TopUserInfo? toUserInfo) async {
    if (widget.subjectType != widget.requestSubjectType) {
      return;
    }
    var canShow =
        await getFinishState(widget.topUserInfo, widget.requestSubjectType);
    if (!canShow) {
      return;
    }
    saveFinishState(widget.requestSubjectType);
    l.i("里程碑动画", "动画执行");
    if (mounted) {
      sceneChangeController.value = 1.0;
      tipIntoController.reset();
      tipIntoController.forward();
      jojoBoxSpineController.playAnimationList(boxAnimat);
    }
  }

  Widget _buildNumberWidget() {
    int continuousNum = widget.topUserInfo?.continuous?.days ?? 0;
    int coinIconNum = widget.topUserInfo?.learnBean?.amount ?? 0;
    int coinRewardTypeType =
        widget.topUserInfo?.learnBean?.type ?? -1; //激励右边icon类型
    int badgeIconNum = widget.topUserInfo?.medal?.amount ?? 0;
    int badgeRewardTypeType =
        widget.topUserInfo?.medal?.type ?? -1; //激励左边icon类型
    int isStart = widget.topUserInfo?.continuous?.isStart ?? 0;
    String notStartText = widget.topUserInfo?.continuous?.notStartText ?? "";
    return SizedBox(
      height: 110.rdp,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 18.rdp,
          ),
          Container(
            height: 78.rdp,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(top: 10.rdp),
            child: isStart == 1
                ? Row(
                    children: buildNumberImages(
                      continuousNum,
                      46.rdp,
                      9999,
                    ),
                  )
                : Container(
                    padding: EdgeInsets.only(top: 10.rdp),
                    child: Text(notStartText,
                        style: TextStyle(
                            fontSize: 20.rdp,
                            fontWeight: FontWeight.w500,
                            color: HexColor("#E35B00"))),
                  ),
          ),
          SizedBox(
            width: 30.rdp,
          ),
          SizedBox(
            width: 10.rdp,
          ),
          if (widget.topUserInfo?.medal?.isShow == 1)
            Container(
              padding: EdgeInsets.only(top: 20.rdp),
              child: CoinBtnWidget(
                router: widget.topUserInfo?.medal?.jumpRoute ?? "",
                number: badgeIconNum,
                type: CoinBtnType.badge,
                isHide: widget.topUserInfo?.medal?.isShow != 1,
                rewardType: badgeRewardTypeType,
                icon: widget.topUserInfo?.medal?.icon ?? "",
                subjectType: widget.subjectType,
                subjectColor: widget.subjectColor,
                style: CoinBtnWidgetStyle(
                    iconSize: Size(40.rdp, 40.rdp),
                    fontSize: 24.rdp,
                    animatorDuring: widget.topUserAnimatorDuring ?? 0),
              ),
            ),
          if (widget.topUserInfo?.medal?.isShow == 1)
            SizedBox(
              width: 20.rdp,
            ),
          if (widget.topUserInfo?.learnBean?.isShow == 1)
            Padding(
                padding: EdgeInsets.only(top: 20.rdp),
                child: CoinBtnWidget(
                  router: widget.topUserInfo?.learnBean?.jumpRoute ?? "",
                  number: coinIconNum,
                  type: CoinBtnType.coin,
                  isHide: widget.topUserInfo?.learnBean?.isShow != 1,
                  rewardType: coinRewardTypeType,
                  icon: widget.topUserInfo?.learnBean?.icon ?? "",
                  subjectType: widget.subjectType,
                  subjectColor: widget.subjectColor,
                  style: CoinBtnWidgetStyle(
                      iconSize: Size(40.rdp, 40.rdp),
                      fontSize: 24.rdp,
                      animatorDuring: widget.topUserAnimatorDuring ?? 0),
                ))
        ],
      ),
    );
  }

  Widget _buildChangeAnimatWidget(
      AnimationController controller,
      Animation<double> opacityValue,
      AnimationController tipIntoController,
      Animation<double> tipAnimation) {
    return AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          return Stack(
            alignment: AlignmentDirectional.centerStart,
            children: [
              Offstage(
                offstage: opacityValue.value == 0,
                child: Opacity(
                  opacity: opacityValue.value,
                  child: _buildTreasuresBox(tipIntoController, tipAnimation),
                ),
              ),
              Offstage(
                offstage: 1 - opacityValue.value == 0,
                child: Opacity(
                  opacity: 1 - opacityValue.value,
                  child: GestureDetector(
                    onTap: () {
                      l.d("sensorsTrack", "学习页_连续学入口_连续天数");
                      reportClickTracking(eventMap: {
                        '\$element_name': "学习页_连续学入口_连续天数",
                        'custom_state': getSubjectName(widget.subjectType),
                      });
                      RunEnv.jumpLink(
                          widget.topUserInfo?.continuous?.jumpRoute ?? "");
                    },
                    child: _buildNumberWidget(),
                  ),
                ),
              )
            ],
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 110.rdp,
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          Positioned(
            left: 8.rdp,
            child: GestureDetector(
              onTap: () {
                RunEnv.sensorsTrack('\$AppClick', {
                  '\$element_name': "学习主页_连续学入口",
                  'custom_state': getSubjectName(widget.subjectType),
                });
                RunEnv.jumpLink(
                    widget.topUserInfo?.continuous?.jumpRoute ?? "");
              },
              child: buildSpineWidget(
                  Size(110.rdp, 110.rdp),
                  key: getReBuildSpineKey(
                      milesJoJoSpine, widget.requestSubjectType, _progressText),
                  jojoSpineController,
                  atlasFile: milesJoJoSpine.atlasFile,
                  skelFile: milesJoJoSpine.skelFile, onInitialized: () {
                startJoJoAnima(
                    isSubjectTypeEqual:
                        widget.subjectType == widget.requestSubjectType,
                    hasMilestone: _hasMilestone,
                    animatStatus: _studyStaus,
                    subjectType: widget.subjectType,
                    spineController: jojoSpineController,
                    topUserInfo: widget.topUserInfo);
              }),
            ),
          ),
          Positioned(
              left: 100.rdp,
              child: Container(
                key: getReBuildSpineKey(
                    milesBoxSpine, widget.requestSubjectType, _progressText),
                child: _buildChangeAnimatWidget(sceneChangeController,
                    opacityValue, tipIntoController, tipAnimation),
              ))
        ],
      ),
    );
  }

  Widget _buildTreasuresBox(
      AnimationController tipIntoController, Animation<double> tipAnimation) {
    return SizedBox(
      height: 110.rdp,
      child: Stack(
        clipBehavior: Clip.none,
        alignment: AlignmentDirectional.bottomStart,
        children: [
          Positioned(
              left: 76.rdp,
              bottom: 30.rdp,
              child: _buildTipAnimateWidget(tipIntoController, tipAnimation)),
          Positioned(
              child: GestureDetector(
            onTap: () {
              reportClickTracking(eventMap: {
                '\$element_name': "学习页_里程碑入口",
                'custom_state': getSubjectName(widget.subjectType),
              });
              RunEnv.jumpLink(widget.topUserInfo?.continuous?.jumpRoute ?? "");
            },
            child: buildSpineWidget(
                Size(110.rdp, 110.rdp), jojoBoxSpineController,
                atlasFile: milesBoxSpine.atlasFile,
                skelFile: milesBoxSpine.skelFile, onInitialized: () {
              _startAnima(widget.topUserInfo);
            }),
          )),
        ],
      ),
    );
  }

  Widget _buildTipAnimateWidget(
      AnimationController tipIntoController, Animation<double> tipAnimation) {
    return AnimatedBuilder(
      animation: tipIntoController,
      builder: (context, child) {
        final progressText =
            widget.topUserInfo?.continuous?.milestone?.progressText ?? "";
        return progressText.isEmpty
            ? Container()
            : Opacity(
                opacity: tipAnimation.value,
                child: Container(
                  padding: EdgeInsets.only(
                      left: 24.rdp, right: 20.rdp, top: 4.rdp, bottom: 4.rdp),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.horizontal(
                          right: Radius.circular(20.rdp))),
                  child: Text(
                    progressText,
                    style: context.textstyles.bodyText.pf.copyWith(
                        fontSize: 20.rdp,
                        fontWeight: FontWeight.w500,
                        color: HexColor("#B76024")),
                  ),
                ),
              );
      },
    );
  }

  @override
  void didUpdateWidget(
      covariant MilestoneIncentiveLandspacItemWidget oldWidget) {
    l.d("里程碑动画", "切换场景${sceneChangeController.value}");
    if (oldWidget.requestSubjectType != widget.requestSubjectType) {
      sceneChangeController
          .reset(); // 用户可能不会等 宝箱动画播放完成，切换 tab 后，宝箱动画会被中断无法正常重置，所以需要重置场景动画
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  String get spineRes =>
      pageController
          ?.tabAnimationFilePathMap?["${widget.requestSubjectType}_path"] ??
      "";

  @override
  Function get boxAnimaOnEnd => () {
        if (mounted) {
          sceneChangeController.reverse();
        }
      };

  @override
  Widget buildIconWidget() {
    return SizedBox(width: 110.rdp, height: 110.rdp);
  }

  PlanHomeWithMapviewCtrl? get pageController =>
      mounted ? context.read<PlanHomeWithMapviewCtrl>() : null;
}
