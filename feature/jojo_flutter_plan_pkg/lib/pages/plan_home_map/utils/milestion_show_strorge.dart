import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/milestion_save_data.dart';

class MilestionShowStrorge {
  final String _queryKey = 'milestion_show';

  static MilestionShowStrorge? _instance;
  // Avoid self instance
  MilestionShowStrorge._();
  static MilestionShowStrorge get instance =>
      _instance ??= MilestionShowStrorge._();

  Future<bool> canShow(
      {required String toDay, required int subjectType}) async {
    final MilestionSaveData storageData = await _getStorageData();
    // 如果数据为空，保存并返回true
    if (storageData.isEmpty) {
      l.i("里程碑动画", "未查询到信息缓存信息 storageData == null");
      return true;
    }
    // 如果日期不是当天，更新日期和科目并返回true
    if (storageData.date != toDay) {
      l.i("里程碑动画", "查询到信息缓存信息 ${storageData.subjects}");
      return true;
    }
    // 如果当天没有对应科目，添加科目并返回true
    if (!storageData.hasSubject(subjectType)) {
      l.i("里程碑动画", "查询到信息缓存信息 ${storageData.subjects}");
      return true;
    }
    l.i("里程碑动画", "当天已展示过该科目，不再展示");
    // 当天已展示过该科目，不再展示
    return false;
  }

  /// 保存日期和科目信息
  /// [date] 日期
  /// [subjectType] 科目类型

  Future<void> saveSubject(
    String date,
    int subjectType,
  ) async {
    // 获取当前存储数据
    final MilestionSaveData storageData = await _getStorageData();
    bool isNewDay = storageData.isEmpty || storageData.date != date;
    // 如果是新的一天，创建新的数据对象，只包含当前科目
    if (isNewDay) {
      final newData = MilestionSaveData(date: date, subjects: [subjectType]);
      await _saveStorageData(newData);
      return;
    }

    // 如果是当天的新科目，添加到现有列表
    storageData.date = date;
    storageData.addSubject(subjectType);

    // 保存到存储
    await _saveStorageData(storageData);
  }

  /// 获取存储数据
  Future<MilestionSaveData> _getStorageData() async {
    final jojoResponse =
        await jojoNativeBridge.operationNativeValueGet(key: _queryKey);
    final NativeValue? value = jojoResponse.data;

    if (value == null) {
      l.i("里程碑动画", "未查询到信息缓存信息");
      return MilestionSaveData.empty();
    }

    try {
      return MilestionSaveData.fromJson(value.value);
    } catch (e) {
      l.i("里程碑动画", "解析失败返回空数据");
      // 解析失败返回空数据
      return MilestionSaveData.empty();
    }
  }

  /// 保存数据到存储
  Future<void> _saveStorageData(MilestionSaveData data) async {
    final jsonString = data.toJson();
    await jojoNativeBridge.operationNativeValueSet(
      key: _queryKey,
      value: jsonString,
    );
  }
}
