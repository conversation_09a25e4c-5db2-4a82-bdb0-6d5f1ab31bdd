// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_dialog_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AiDialogData _$AiDialogDataFromJson(Map<String, dynamic> json) {
  return _AiDialogData.fromJson(json);
}

/// @nodoc
mixin _$AiDialogData {
  int? get jojoCallTime => throw _privateConstructorUsedError;
  set jojoCallTime(int? value) => throw _privateConstructorUsedError;
  String? get jojoCallIcon => throw _privateConstructorUsedError;
  set jojoCallIcon(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallTitle => throw _privateConstructorUsedError;
  set jojoCallTitle(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallSubTitle => throw _privateConstructorUsedError;
  set jojoCallSubTitle(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallBgm => throw _privateConstructorUsedError;
  set jojoCallBgm(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallVoice => throw _privateConstructorUsedError;
  set jojoCallVoice(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallRouter => throw _privateConstructorUsedError;
  set jojoCallRouter(String? value) => throw _privateConstructorUsedError;
  String? get jojoCallBgImgLocalPath => throw _privateConstructorUsedError;
  set jojoCallBgImgLocalPath(String? value) =>
      throw _privateConstructorUsedError;
  String? get jojoCallVoiceLocalPath => throw _privateConstructorUsedError;
  set jojoCallVoiceLocalPath(String? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AiDialogDataCopyWith<AiDialogData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiDialogDataCopyWith<$Res> {
  factory $AiDialogDataCopyWith(
          AiDialogData value, $Res Function(AiDialogData) then) =
      _$AiDialogDataCopyWithImpl<$Res, AiDialogData>;
  @useResult
  $Res call(
      {int? jojoCallTime,
      String? jojoCallIcon,
      String? jojoCallTitle,
      String? jojoCallSubTitle,
      String? jojoCallBgm,
      String? jojoCallVoice,
      String? jojoCallRouter,
      String? jojoCallBgImgLocalPath,
      String? jojoCallVoiceLocalPath});
}

/// @nodoc
class _$AiDialogDataCopyWithImpl<$Res, $Val extends AiDialogData>
    implements $AiDialogDataCopyWith<$Res> {
  _$AiDialogDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jojoCallTime = freezed,
    Object? jojoCallIcon = freezed,
    Object? jojoCallTitle = freezed,
    Object? jojoCallSubTitle = freezed,
    Object? jojoCallBgm = freezed,
    Object? jojoCallVoice = freezed,
    Object? jojoCallRouter = freezed,
    Object? jojoCallBgImgLocalPath = freezed,
    Object? jojoCallVoiceLocalPath = freezed,
  }) {
    return _then(_value.copyWith(
      jojoCallTime: freezed == jojoCallTime
          ? _value.jojoCallTime
          : jojoCallTime // ignore: cast_nullable_to_non_nullable
              as int?,
      jojoCallIcon: freezed == jojoCallIcon
          ? _value.jojoCallIcon
          : jojoCallIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallTitle: freezed == jojoCallTitle
          ? _value.jojoCallTitle
          : jojoCallTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallSubTitle: freezed == jojoCallSubTitle
          ? _value.jojoCallSubTitle
          : jojoCallSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallBgm: freezed == jojoCallBgm
          ? _value.jojoCallBgm
          : jojoCallBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallVoice: freezed == jojoCallVoice
          ? _value.jojoCallVoice
          : jojoCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallRouter: freezed == jojoCallRouter
          ? _value.jojoCallRouter
          : jojoCallRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallBgImgLocalPath: freezed == jojoCallBgImgLocalPath
          ? _value.jojoCallBgImgLocalPath
          : jojoCallBgImgLocalPath // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallVoiceLocalPath: freezed == jojoCallVoiceLocalPath
          ? _value.jojoCallVoiceLocalPath
          : jojoCallVoiceLocalPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AiDialogDataCopyWith<$Res>
    implements $AiDialogDataCopyWith<$Res> {
  factory _$$_AiDialogDataCopyWith(
          _$_AiDialogData value, $Res Function(_$_AiDialogData) then) =
      __$$_AiDialogDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? jojoCallTime,
      String? jojoCallIcon,
      String? jojoCallTitle,
      String? jojoCallSubTitle,
      String? jojoCallBgm,
      String? jojoCallVoice,
      String? jojoCallRouter,
      String? jojoCallBgImgLocalPath,
      String? jojoCallVoiceLocalPath});
}

/// @nodoc
class __$$_AiDialogDataCopyWithImpl<$Res>
    extends _$AiDialogDataCopyWithImpl<$Res, _$_AiDialogData>
    implements _$$_AiDialogDataCopyWith<$Res> {
  __$$_AiDialogDataCopyWithImpl(
      _$_AiDialogData _value, $Res Function(_$_AiDialogData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jojoCallTime = freezed,
    Object? jojoCallIcon = freezed,
    Object? jojoCallTitle = freezed,
    Object? jojoCallSubTitle = freezed,
    Object? jojoCallBgm = freezed,
    Object? jojoCallVoice = freezed,
    Object? jojoCallRouter = freezed,
    Object? jojoCallBgImgLocalPath = freezed,
    Object? jojoCallVoiceLocalPath = freezed,
  }) {
    return _then(_$_AiDialogData(
      jojoCallTime: freezed == jojoCallTime
          ? _value.jojoCallTime
          : jojoCallTime // ignore: cast_nullable_to_non_nullable
              as int?,
      jojoCallIcon: freezed == jojoCallIcon
          ? _value.jojoCallIcon
          : jojoCallIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallTitle: freezed == jojoCallTitle
          ? _value.jojoCallTitle
          : jojoCallTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallSubTitle: freezed == jojoCallSubTitle
          ? _value.jojoCallSubTitle
          : jojoCallSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallBgm: freezed == jojoCallBgm
          ? _value.jojoCallBgm
          : jojoCallBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallVoice: freezed == jojoCallVoice
          ? _value.jojoCallVoice
          : jojoCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallRouter: freezed == jojoCallRouter
          ? _value.jojoCallRouter
          : jojoCallRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallBgImgLocalPath: freezed == jojoCallBgImgLocalPath
          ? _value.jojoCallBgImgLocalPath
          : jojoCallBgImgLocalPath // ignore: cast_nullable_to_non_nullable
              as String?,
      jojoCallVoiceLocalPath: freezed == jojoCallVoiceLocalPath
          ? _value.jojoCallVoiceLocalPath
          : jojoCallVoiceLocalPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AiDialogData implements _AiDialogData {
  _$_AiDialogData(
      {this.jojoCallTime,
      this.jojoCallIcon,
      this.jojoCallTitle,
      this.jojoCallSubTitle,
      this.jojoCallBgm,
      this.jojoCallVoice,
      this.jojoCallRouter,
      this.jojoCallBgImgLocalPath,
      this.jojoCallVoiceLocalPath});

  factory _$_AiDialogData.fromJson(Map<String, dynamic> json) =>
      _$$_AiDialogDataFromJson(json);

  @override
  int? jojoCallTime;
  @override
  String? jojoCallIcon;
  @override
  String? jojoCallTitle;
  @override
  String? jojoCallSubTitle;
  @override
  String? jojoCallBgm;
  @override
  String? jojoCallVoice;
  @override
  String? jojoCallRouter;
  @override
  String? jojoCallBgImgLocalPath;
  @override
  String? jojoCallVoiceLocalPath;

  @override
  String toString() {
    return 'AiDialogData(jojoCallTime: $jojoCallTime, jojoCallIcon: $jojoCallIcon, jojoCallTitle: $jojoCallTitle, jojoCallSubTitle: $jojoCallSubTitle, jojoCallBgm: $jojoCallBgm, jojoCallVoice: $jojoCallVoice, jojoCallRouter: $jojoCallRouter, jojoCallBgImgLocalPath: $jojoCallBgImgLocalPath, jojoCallVoiceLocalPath: $jojoCallVoiceLocalPath)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AiDialogDataCopyWith<_$_AiDialogData> get copyWith =>
      __$$_AiDialogDataCopyWithImpl<_$_AiDialogData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AiDialogDataToJson(
      this,
    );
  }
}

abstract class _AiDialogData implements AiDialogData {
  factory _AiDialogData(
      {int? jojoCallTime,
      String? jojoCallIcon,
      String? jojoCallTitle,
      String? jojoCallSubTitle,
      String? jojoCallBgm,
      String? jojoCallVoice,
      String? jojoCallRouter,
      String? jojoCallBgImgLocalPath,
      String? jojoCallVoiceLocalPath}) = _$_AiDialogData;

  factory _AiDialogData.fromJson(Map<String, dynamic> json) =
      _$_AiDialogData.fromJson;

  @override
  int? get jojoCallTime;
  set jojoCallTime(int? value);
  @override
  String? get jojoCallIcon;
  set jojoCallIcon(String? value);
  @override
  String? get jojoCallTitle;
  set jojoCallTitle(String? value);
  @override
  String? get jojoCallSubTitle;
  set jojoCallSubTitle(String? value);
  @override
  String? get jojoCallBgm;
  set jojoCallBgm(String? value);
  @override
  String? get jojoCallVoice;
  set jojoCallVoice(String? value);
  @override
  String? get jojoCallRouter;
  set jojoCallRouter(String? value);
  @override
  String? get jojoCallBgImgLocalPath;
  set jojoCallBgImgLocalPath(String? value);
  @override
  String? get jojoCallVoiceLocalPath;
  set jojoCallVoiceLocalPath(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_AiDialogDataCopyWith<_$_AiDialogData> get copyWith =>
      throw _privateConstructorUsedError;
}
