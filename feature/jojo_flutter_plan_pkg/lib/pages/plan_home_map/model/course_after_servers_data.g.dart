// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_after_servers_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseAfterServersData _$$_CourseAfterServersDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseAfterServersData(
      tabTitle: json['tabTitle'] as String?,
      title: json['title'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      configKey: json['configKey'] as String?,
      toastList: (json['toastList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      subjectColor: json['subjectColor'] as String?,
      subjectName: json['subjectName'] as String?,
      classServerList: (json['classServerList'] as List<dynamic>?)
          ?.map((e) => ClassServerList.fromJson(e as Map<String, dynamic>))
          .toList(),
      popupList: (json['popupList'] as List<dynamic>?)
          ?.map((e) => ClassPagePopupList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseAfterServersDataToJson(
        _$_CourseAfterServersData instance) =>
    <String, dynamic>{
      'tabTitle': instance.tabTitle,
      'title': instance.title,
      'icon': instance.icon,
      'route': instance.route,
      'configKey': instance.configKey,
      'toastList': instance.toastList,
      'subjectColor': instance.subjectColor,
      'subjectName': instance.subjectName,
      'classServerList': instance.classServerList,
      'popupList': instance.popupList,
    };

_$_ClassServerList _$$_ClassServerListFromJson(Map<String, dynamic> json) =>
    _$_ClassServerList(
      endClass: json['endClass'] as bool?,
      classTitle: json['classTitle'] as String?,
      classId: json['classId'] as int?,
      classStatus: json['classStatus'] as int?,
      courseId: json['courseId'] as int?,
      courseType: json['courseType'] as int?,
      courseKey: json['courseKey'] as String?,
      courseSegment: json['courseSegment'] as String?,
      classTeacher: json['classTeacher'] == null
          ? null
          : ClassTeacher.fromJson(json['classTeacher'] as Map<String, dynamic>),
      serverInfoList: (json['serverInfoList'] as List<dynamic>?)
          ?.map((e) => ServerInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ClassServerListToJson(_$_ClassServerList instance) =>
    <String, dynamic>{
      'endClass': instance.endClass,
      'classTitle': instance.classTitle,
      'classId': instance.classId,
      'classStatus': instance.classStatus,
      'courseId': instance.courseId,
      'courseType': instance.courseType,
      'courseKey': instance.courseKey,
      'courseSegment': instance.courseSegment,
      'classTeacher': instance.classTeacher,
      'serverInfoList': instance.serverInfoList,
    };

_$_ClassTeacher _$$_ClassTeacherFromJson(Map<String, dynamic> json) =>
    _$_ClassTeacher(
      teacherId: json['teacherId'] as int?,
      teacherTitle: json['teacherTitle'] as String?,
      teacherAvatar: json['teacherAvatar'] as String?,
      add: json['add'] as bool?,
      toast: json['toast'] as String?,
      route: json['route'] as String?,
    );

Map<String, dynamic> _$$_ClassTeacherToJson(_$_ClassTeacher instance) =>
    <String, dynamic>{
      'teacherId': instance.teacherId,
      'teacherTitle': instance.teacherTitle,
      'teacherAvatar': instance.teacherAvatar,
      'add': instance.add,
      'toast': instance.toast,
      'route': instance.route,
    };

_$_ServerInfoList _$$_ServerInfoListFromJson(Map<String, dynamic> json) =>
    _$_ServerInfoList(
      isBuried: json['isBuried'] as bool?,
      status: json['status'] as int?,
      updateNum: json['updateNum'] as int?,
      title: json['title'] as String?,
      key: json['key'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      toast: json['toast'] as String?,
    );

Map<String, dynamic> _$$_ServerInfoListToJson(_$_ServerInfoList instance) =>
    <String, dynamic>{
      'isBuried': instance.isBuried,
      'status': instance.status,
      'updateNum': instance.updateNum,
      'title': instance.title,
      'key': instance.key,
      'icon': instance.icon,
      'route': instance.route,
      'toast': instance.toast,
    };
