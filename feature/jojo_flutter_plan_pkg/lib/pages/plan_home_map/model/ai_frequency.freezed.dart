// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_frequency.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AiFrequency _$AiFrequencyFromJson(Map<String, dynamic> json) {
  return _AiFrequency.fromJson(json);
}

/// @nodoc
mixin _$AiFrequency {
  String? get callVoice => throw _privateConstructorUsedError; // 拨打电话等待提示音
  String? get localCallVoice =>
      throw _privateConstructorUsedError; // 拨打电话等待提示音 本地文件
  int? get dayCallFrequency => throw _privateConstructorUsedError; // 会话总次数
  String? get ableCallVoice => throw _privateConstructorUsedError; // 可拨打时语音提示音
  String? get localAbleCallVoice =>
      throw _privateConstructorUsedError; // 可拨打时语音提示音 本地文件
  String? get ableCallTip => throw _privateConstructorUsedError; // 可拨打时提示文案
  String? get disableCallVoice =>
      throw _privateConstructorUsedError; // 不可拨打时语音提示音
  String? get localDisableCallVoice =>
      throw _privateConstructorUsedError; // 不可拨打时语音提示音 本地文件
  String? get disableCallTip => throw _privateConstructorUsedError; // 不可拨打时提示文案
  int? get remaining => throw _privateConstructorUsedError; // 剩余次数
  int? get callDuration => throw _privateConstructorUsedError; // 最大通话分钟数
  String? get hangupCallVoice => throw _privateConstructorUsedError; // 挂断语音提示音频
  String? get localHangupCallVoice =>
      throw _privateConstructorUsedError; // 挂断语音提示音频 本地文件
  int? get callSecondDuration =>
      throw _privateConstructorUsedError; // app版本 2.1.0 开始支持，最大通话时长 单位：秒
  List<String>? get segmentCodeList => throw _privateConstructorUsedError;
  List<int>? get classIds => throw _privateConstructorUsedError;
  String? get customState => throw _privateConstructorUsedError;
  GrayInfo? get grayInfo => throw _privateConstructorUsedError;
  EncouragementConversationParamsVo? get encouragementConversationParamsVo =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AiFrequencyCopyWith<AiFrequency> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiFrequencyCopyWith<$Res> {
  factory $AiFrequencyCopyWith(
          AiFrequency value, $Res Function(AiFrequency) then) =
      _$AiFrequencyCopyWithImpl<$Res, AiFrequency>;
  @useResult
  $Res call(
      {String? callVoice,
      String? localCallVoice,
      int? dayCallFrequency,
      String? ableCallVoice,
      String? localAbleCallVoice,
      String? ableCallTip,
      String? disableCallVoice,
      String? localDisableCallVoice,
      String? disableCallTip,
      int? remaining,
      int? callDuration,
      String? hangupCallVoice,
      String? localHangupCallVoice,
      int? callSecondDuration,
      List<String>? segmentCodeList,
      List<int>? classIds,
      String? customState,
      GrayInfo? grayInfo,
      EncouragementConversationParamsVo? encouragementConversationParamsVo});

  $GrayInfoCopyWith<$Res>? get grayInfo;
  $EncouragementConversationParamsVoCopyWith<$Res>?
      get encouragementConversationParamsVo;
}

/// @nodoc
class _$AiFrequencyCopyWithImpl<$Res, $Val extends AiFrequency>
    implements $AiFrequencyCopyWith<$Res> {
  _$AiFrequencyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callVoice = freezed,
    Object? localCallVoice = freezed,
    Object? dayCallFrequency = freezed,
    Object? ableCallVoice = freezed,
    Object? localAbleCallVoice = freezed,
    Object? ableCallTip = freezed,
    Object? disableCallVoice = freezed,
    Object? localDisableCallVoice = freezed,
    Object? disableCallTip = freezed,
    Object? remaining = freezed,
    Object? callDuration = freezed,
    Object? hangupCallVoice = freezed,
    Object? localHangupCallVoice = freezed,
    Object? callSecondDuration = freezed,
    Object? segmentCodeList = freezed,
    Object? classIds = freezed,
    Object? customState = freezed,
    Object? grayInfo = freezed,
    Object? encouragementConversationParamsVo = freezed,
  }) {
    return _then(_value.copyWith(
      callVoice: freezed == callVoice
          ? _value.callVoice
          : callVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localCallVoice: freezed == localCallVoice
          ? _value.localCallVoice
          : localCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCallFrequency: freezed == dayCallFrequency
          ? _value.dayCallFrequency
          : dayCallFrequency // ignore: cast_nullable_to_non_nullable
              as int?,
      ableCallVoice: freezed == ableCallVoice
          ? _value.ableCallVoice
          : ableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localAbleCallVoice: freezed == localAbleCallVoice
          ? _value.localAbleCallVoice
          : localAbleCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      ableCallTip: freezed == ableCallTip
          ? _value.ableCallTip
          : ableCallTip // ignore: cast_nullable_to_non_nullable
              as String?,
      disableCallVoice: freezed == disableCallVoice
          ? _value.disableCallVoice
          : disableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localDisableCallVoice: freezed == localDisableCallVoice
          ? _value.localDisableCallVoice
          : localDisableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      disableCallTip: freezed == disableCallTip
          ? _value.disableCallTip
          : disableCallTip // ignore: cast_nullable_to_non_nullable
              as String?,
      remaining: freezed == remaining
          ? _value.remaining
          : remaining // ignore: cast_nullable_to_non_nullable
              as int?,
      callDuration: freezed == callDuration
          ? _value.callDuration
          : callDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      hangupCallVoice: freezed == hangupCallVoice
          ? _value.hangupCallVoice
          : hangupCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localHangupCallVoice: freezed == localHangupCallVoice
          ? _value.localHangupCallVoice
          : localHangupCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      callSecondDuration: freezed == callSecondDuration
          ? _value.callSecondDuration
          : callSecondDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentCodeList: freezed == segmentCodeList
          ? _value.segmentCodeList
          : segmentCodeList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      classIds: freezed == classIds
          ? _value.classIds
          : classIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      grayInfo: freezed == grayInfo
          ? _value.grayInfo
          : grayInfo // ignore: cast_nullable_to_non_nullable
              as GrayInfo?,
      encouragementConversationParamsVo: freezed ==
              encouragementConversationParamsVo
          ? _value.encouragementConversationParamsVo
          : encouragementConversationParamsVo // ignore: cast_nullable_to_non_nullable
              as EncouragementConversationParamsVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GrayInfoCopyWith<$Res>? get grayInfo {
    if (_value.grayInfo == null) {
      return null;
    }

    return $GrayInfoCopyWith<$Res>(_value.grayInfo!, (value) {
      return _then(_value.copyWith(grayInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $EncouragementConversationParamsVoCopyWith<$Res>?
      get encouragementConversationParamsVo {
    if (_value.encouragementConversationParamsVo == null) {
      return null;
    }

    return $EncouragementConversationParamsVoCopyWith<$Res>(
        _value.encouragementConversationParamsVo!, (value) {
      return _then(
          _value.copyWith(encouragementConversationParamsVo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AiFrequencyCopyWith<$Res>
    implements $AiFrequencyCopyWith<$Res> {
  factory _$$_AiFrequencyCopyWith(
          _$_AiFrequency value, $Res Function(_$_AiFrequency) then) =
      __$$_AiFrequencyCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? callVoice,
      String? localCallVoice,
      int? dayCallFrequency,
      String? ableCallVoice,
      String? localAbleCallVoice,
      String? ableCallTip,
      String? disableCallVoice,
      String? localDisableCallVoice,
      String? disableCallTip,
      int? remaining,
      int? callDuration,
      String? hangupCallVoice,
      String? localHangupCallVoice,
      int? callSecondDuration,
      List<String>? segmentCodeList,
      List<int>? classIds,
      String? customState,
      GrayInfo? grayInfo,
      EncouragementConversationParamsVo? encouragementConversationParamsVo});

  @override
  $GrayInfoCopyWith<$Res>? get grayInfo;
  @override
  $EncouragementConversationParamsVoCopyWith<$Res>?
      get encouragementConversationParamsVo;
}

/// @nodoc
class __$$_AiFrequencyCopyWithImpl<$Res>
    extends _$AiFrequencyCopyWithImpl<$Res, _$_AiFrequency>
    implements _$$_AiFrequencyCopyWith<$Res> {
  __$$_AiFrequencyCopyWithImpl(
      _$_AiFrequency _value, $Res Function(_$_AiFrequency) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callVoice = freezed,
    Object? localCallVoice = freezed,
    Object? dayCallFrequency = freezed,
    Object? ableCallVoice = freezed,
    Object? localAbleCallVoice = freezed,
    Object? ableCallTip = freezed,
    Object? disableCallVoice = freezed,
    Object? localDisableCallVoice = freezed,
    Object? disableCallTip = freezed,
    Object? remaining = freezed,
    Object? callDuration = freezed,
    Object? hangupCallVoice = freezed,
    Object? localHangupCallVoice = freezed,
    Object? callSecondDuration = freezed,
    Object? segmentCodeList = freezed,
    Object? classIds = freezed,
    Object? customState = freezed,
    Object? grayInfo = freezed,
    Object? encouragementConversationParamsVo = freezed,
  }) {
    return _then(_$_AiFrequency(
      callVoice: freezed == callVoice
          ? _value.callVoice
          : callVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localCallVoice: freezed == localCallVoice
          ? _value.localCallVoice
          : localCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCallFrequency: freezed == dayCallFrequency
          ? _value.dayCallFrequency
          : dayCallFrequency // ignore: cast_nullable_to_non_nullable
              as int?,
      ableCallVoice: freezed == ableCallVoice
          ? _value.ableCallVoice
          : ableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localAbleCallVoice: freezed == localAbleCallVoice
          ? _value.localAbleCallVoice
          : localAbleCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      ableCallTip: freezed == ableCallTip
          ? _value.ableCallTip
          : ableCallTip // ignore: cast_nullable_to_non_nullable
              as String?,
      disableCallVoice: freezed == disableCallVoice
          ? _value.disableCallVoice
          : disableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localDisableCallVoice: freezed == localDisableCallVoice
          ? _value.localDisableCallVoice
          : localDisableCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      disableCallTip: freezed == disableCallTip
          ? _value.disableCallTip
          : disableCallTip // ignore: cast_nullable_to_non_nullable
              as String?,
      remaining: freezed == remaining
          ? _value.remaining
          : remaining // ignore: cast_nullable_to_non_nullable
              as int?,
      callDuration: freezed == callDuration
          ? _value.callDuration
          : callDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      hangupCallVoice: freezed == hangupCallVoice
          ? _value.hangupCallVoice
          : hangupCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      localHangupCallVoice: freezed == localHangupCallVoice
          ? _value.localHangupCallVoice
          : localHangupCallVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      callSecondDuration: freezed == callSecondDuration
          ? _value.callSecondDuration
          : callSecondDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentCodeList: freezed == segmentCodeList
          ? _value._segmentCodeList
          : segmentCodeList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      classIds: freezed == classIds
          ? _value._classIds
          : classIds // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
      grayInfo: freezed == grayInfo
          ? _value.grayInfo
          : grayInfo // ignore: cast_nullable_to_non_nullable
              as GrayInfo?,
      encouragementConversationParamsVo: freezed ==
              encouragementConversationParamsVo
          ? _value.encouragementConversationParamsVo
          : encouragementConversationParamsVo // ignore: cast_nullable_to_non_nullable
              as EncouragementConversationParamsVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AiFrequency implements _AiFrequency {
  const _$_AiFrequency(
      {this.callVoice,
      this.localCallVoice,
      this.dayCallFrequency,
      this.ableCallVoice,
      this.localAbleCallVoice,
      this.ableCallTip,
      this.disableCallVoice,
      this.localDisableCallVoice,
      this.disableCallTip,
      this.remaining,
      this.callDuration,
      this.hangupCallVoice,
      this.localHangupCallVoice,
      this.callSecondDuration,
      final List<String>? segmentCodeList,
      final List<int>? classIds,
      this.customState,
      this.grayInfo,
      this.encouragementConversationParamsVo})
      : _segmentCodeList = segmentCodeList,
        _classIds = classIds;

  factory _$_AiFrequency.fromJson(Map<String, dynamic> json) =>
      _$$_AiFrequencyFromJson(json);

  @override
  final String? callVoice;
// 拨打电话等待提示音
  @override
  final String? localCallVoice;
// 拨打电话等待提示音 本地文件
  @override
  final int? dayCallFrequency;
// 会话总次数
  @override
  final String? ableCallVoice;
// 可拨打时语音提示音
  @override
  final String? localAbleCallVoice;
// 可拨打时语音提示音 本地文件
  @override
  final String? ableCallTip;
// 可拨打时提示文案
  @override
  final String? disableCallVoice;
// 不可拨打时语音提示音
  @override
  final String? localDisableCallVoice;
// 不可拨打时语音提示音 本地文件
  @override
  final String? disableCallTip;
// 不可拨打时提示文案
  @override
  final int? remaining;
// 剩余次数
  @override
  final int? callDuration;
// 最大通话分钟数
  @override
  final String? hangupCallVoice;
// 挂断语音提示音频
  @override
  final String? localHangupCallVoice;
// 挂断语音提示音频 本地文件
  @override
  final int? callSecondDuration;
// app版本 2.1.0 开始支持，最大通话时长 单位：秒
  final List<String>? _segmentCodeList;
// app版本 2.1.0 开始支持，最大通话时长 单位：秒
  @override
  List<String>? get segmentCodeList {
    final value = _segmentCodeList;
    if (value == null) return null;
    if (_segmentCodeList is EqualUnmodifiableListView) return _segmentCodeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int>? _classIds;
  @override
  List<int>? get classIds {
    final value = _classIds;
    if (value == null) return null;
    if (_classIds is EqualUnmodifiableListView) return _classIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? customState;
  @override
  final GrayInfo? grayInfo;
  @override
  final EncouragementConversationParamsVo? encouragementConversationParamsVo;

  @override
  String toString() {
    return 'AiFrequency(callVoice: $callVoice, localCallVoice: $localCallVoice, dayCallFrequency: $dayCallFrequency, ableCallVoice: $ableCallVoice, localAbleCallVoice: $localAbleCallVoice, ableCallTip: $ableCallTip, disableCallVoice: $disableCallVoice, localDisableCallVoice: $localDisableCallVoice, disableCallTip: $disableCallTip, remaining: $remaining, callDuration: $callDuration, hangupCallVoice: $hangupCallVoice, localHangupCallVoice: $localHangupCallVoice, callSecondDuration: $callSecondDuration, segmentCodeList: $segmentCodeList, classIds: $classIds, customState: $customState, grayInfo: $grayInfo, encouragementConversationParamsVo: $encouragementConversationParamsVo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AiFrequency &&
            (identical(other.callVoice, callVoice) ||
                other.callVoice == callVoice) &&
            (identical(other.localCallVoice, localCallVoice) ||
                other.localCallVoice == localCallVoice) &&
            (identical(other.dayCallFrequency, dayCallFrequency) ||
                other.dayCallFrequency == dayCallFrequency) &&
            (identical(other.ableCallVoice, ableCallVoice) ||
                other.ableCallVoice == ableCallVoice) &&
            (identical(other.localAbleCallVoice, localAbleCallVoice) ||
                other.localAbleCallVoice == localAbleCallVoice) &&
            (identical(other.ableCallTip, ableCallTip) ||
                other.ableCallTip == ableCallTip) &&
            (identical(other.disableCallVoice, disableCallVoice) ||
                other.disableCallVoice == disableCallVoice) &&
            (identical(other.localDisableCallVoice, localDisableCallVoice) ||
                other.localDisableCallVoice == localDisableCallVoice) &&
            (identical(other.disableCallTip, disableCallTip) ||
                other.disableCallTip == disableCallTip) &&
            (identical(other.remaining, remaining) ||
                other.remaining == remaining) &&
            (identical(other.callDuration, callDuration) ||
                other.callDuration == callDuration) &&
            (identical(other.hangupCallVoice, hangupCallVoice) ||
                other.hangupCallVoice == hangupCallVoice) &&
            (identical(other.localHangupCallVoice, localHangupCallVoice) ||
                other.localHangupCallVoice == localHangupCallVoice) &&
            (identical(other.callSecondDuration, callSecondDuration) ||
                other.callSecondDuration == callSecondDuration) &&
            const DeepCollectionEquality()
                .equals(other._segmentCodeList, _segmentCodeList) &&
            const DeepCollectionEquality().equals(other._classIds, _classIds) &&
            (identical(other.customState, customState) ||
                other.customState == customState) &&
            (identical(other.grayInfo, grayInfo) ||
                other.grayInfo == grayInfo) &&
            (identical(other.encouragementConversationParamsVo,
                    encouragementConversationParamsVo) ||
                other.encouragementConversationParamsVo ==
                    encouragementConversationParamsVo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        callVoice,
        localCallVoice,
        dayCallFrequency,
        ableCallVoice,
        localAbleCallVoice,
        ableCallTip,
        disableCallVoice,
        localDisableCallVoice,
        disableCallTip,
        remaining,
        callDuration,
        hangupCallVoice,
        localHangupCallVoice,
        callSecondDuration,
        const DeepCollectionEquality().hash(_segmentCodeList),
        const DeepCollectionEquality().hash(_classIds),
        customState,
        grayInfo,
        encouragementConversationParamsVo
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AiFrequencyCopyWith<_$_AiFrequency> get copyWith =>
      __$$_AiFrequencyCopyWithImpl<_$_AiFrequency>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AiFrequencyToJson(
      this,
    );
  }
}

abstract class _AiFrequency implements AiFrequency {
  const factory _AiFrequency(
      {final String? callVoice,
      final String? localCallVoice,
      final int? dayCallFrequency,
      final String? ableCallVoice,
      final String? localAbleCallVoice,
      final String? ableCallTip,
      final String? disableCallVoice,
      final String? localDisableCallVoice,
      final String? disableCallTip,
      final int? remaining,
      final int? callDuration,
      final String? hangupCallVoice,
      final String? localHangupCallVoice,
      final int? callSecondDuration,
      final List<String>? segmentCodeList,
      final List<int>? classIds,
      final String? customState,
      final GrayInfo? grayInfo,
      final EncouragementConversationParamsVo?
          encouragementConversationParamsVo}) = _$_AiFrequency;

  factory _AiFrequency.fromJson(Map<String, dynamic> json) =
      _$_AiFrequency.fromJson;

  @override
  String? get callVoice;
  @override // 拨打电话等待提示音
  String? get localCallVoice;
  @override // 拨打电话等待提示音 本地文件
  int? get dayCallFrequency;
  @override // 会话总次数
  String? get ableCallVoice;
  @override // 可拨打时语音提示音
  String? get localAbleCallVoice;
  @override // 可拨打时语音提示音 本地文件
  String? get ableCallTip;
  @override // 可拨打时提示文案
  String? get disableCallVoice;
  @override // 不可拨打时语音提示音
  String? get localDisableCallVoice;
  @override // 不可拨打时语音提示音 本地文件
  String? get disableCallTip;
  @override // 不可拨打时提示文案
  int? get remaining;
  @override // 剩余次数
  int? get callDuration;
  @override // 最大通话分钟数
  String? get hangupCallVoice;
  @override // 挂断语音提示音频
  String? get localHangupCallVoice;
  @override // 挂断语音提示音频 本地文件
  int? get callSecondDuration;
  @override // app版本 2.1.0 开始支持，最大通话时长 单位：秒
  List<String>? get segmentCodeList;
  @override
  List<int>? get classIds;
  @override
  String? get customState;
  @override
  GrayInfo? get grayInfo;
  @override
  EncouragementConversationParamsVo? get encouragementConversationParamsVo;
  @override
  @JsonKey(ignore: true)
  _$$_AiFrequencyCopyWith<_$_AiFrequency> get copyWith =>
      throw _privateConstructorUsedError;
}

EncouragementConversationParamsVo _$EncouragementConversationParamsVoFromJson(
    Map<String, dynamic> json) {
  return _EncouragementConversationParamsVo.fromJson(json);
}

/// @nodoc
mixin _$EncouragementConversationParamsVo {
  bool? get canPopupAiCall => throw _privateConstructorUsedError;
  set canPopupAiCall(bool? value) =>
      throw _privateConstructorUsedError; //完成里程碑奖励的最近时间
  String? get encouragementConversationIcon =>
      throw _privateConstructorUsedError; //完成里程碑奖励的最近时间
  set encouragementConversationIcon(String? value) =>
      throw _privateConstructorUsedError; //AI学伴激励电话Icon
  String? get encouragementConversationTitle =>
      throw _privateConstructorUsedError; //AI学伴激励电话Icon
  set encouragementConversationTitle(String? value) =>
      throw _privateConstructorUsedError; //AI学伴激励电话页面标题
  String? get encouragementConversationSubTitle =>
      throw _privateConstructorUsedError; //AI学伴激励电话页面标题
  set encouragementConversationSubTitle(String? value) =>
      throw _privateConstructorUsedError; //AI学伴激励电话页面副标题
  String? get encouragementConversationBgm =>
      throw _privateConstructorUsedError; //AI学伴激励电话页面副标题
  set encouragementConversationBgm(String? value) =>
      throw _privateConstructorUsedError; //AI学伴激励电话背景音
  String? get encouragementConversationVoice =>
      throw _privateConstructorUsedError; //AI学伴激励电话背景音
  set encouragementConversationVoice(String? value) =>
      throw _privateConstructorUsedError; //AI学伴激励电话呼叫语音
  String? get encouragementConversationRouter =>
      throw _privateConstructorUsedError; //AI学伴激励电话呼叫语音
  set encouragementConversationRouter(String? value) =>
      throw _privateConstructorUsedError; //AI学伴电话路由
  String? get localEncouragementConversationBgm =>
      throw _privateConstructorUsedError; //AI学伴电话路由
  set localEncouragementConversationBgm(String? value) =>
      throw _privateConstructorUsedError;
  String? get localEncouragementConversationVoice =>
      throw _privateConstructorUsedError;
  set localEncouragementConversationVoice(String? value) =>
      throw _privateConstructorUsedError;
  String? get roomTaskId => throw _privateConstructorUsedError;
  set roomTaskId(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EncouragementConversationParamsVoCopyWith<EncouragementConversationParamsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EncouragementConversationParamsVoCopyWith<$Res> {
  factory $EncouragementConversationParamsVoCopyWith(
          EncouragementConversationParamsVo value,
          $Res Function(EncouragementConversationParamsVo) then) =
      _$EncouragementConversationParamsVoCopyWithImpl<$Res,
          EncouragementConversationParamsVo>;
  @useResult
  $Res call(
      {bool? canPopupAiCall,
      String? encouragementConversationIcon,
      String? encouragementConversationTitle,
      String? encouragementConversationSubTitle,
      String? encouragementConversationBgm,
      String? encouragementConversationVoice,
      String? encouragementConversationRouter,
      String? localEncouragementConversationBgm,
      String? localEncouragementConversationVoice,
      String? roomTaskId});
}

/// @nodoc
class _$EncouragementConversationParamsVoCopyWithImpl<$Res,
        $Val extends EncouragementConversationParamsVo>
    implements $EncouragementConversationParamsVoCopyWith<$Res> {
  _$EncouragementConversationParamsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canPopupAiCall = freezed,
    Object? encouragementConversationIcon = freezed,
    Object? encouragementConversationTitle = freezed,
    Object? encouragementConversationSubTitle = freezed,
    Object? encouragementConversationBgm = freezed,
    Object? encouragementConversationVoice = freezed,
    Object? encouragementConversationRouter = freezed,
    Object? localEncouragementConversationBgm = freezed,
    Object? localEncouragementConversationVoice = freezed,
    Object? roomTaskId = freezed,
  }) {
    return _then(_value.copyWith(
      canPopupAiCall: freezed == canPopupAiCall
          ? _value.canPopupAiCall
          : canPopupAiCall // ignore: cast_nullable_to_non_nullable
              as bool?,
      encouragementConversationIcon: freezed == encouragementConversationIcon
          ? _value.encouragementConversationIcon
          : encouragementConversationIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationTitle: freezed == encouragementConversationTitle
          ? _value.encouragementConversationTitle
          : encouragementConversationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationSubTitle: freezed ==
              encouragementConversationSubTitle
          ? _value.encouragementConversationSubTitle
          : encouragementConversationSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationBgm: freezed == encouragementConversationBgm
          ? _value.encouragementConversationBgm
          : encouragementConversationBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationVoice: freezed == encouragementConversationVoice
          ? _value.encouragementConversationVoice
          : encouragementConversationVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationRouter: freezed ==
              encouragementConversationRouter
          ? _value.encouragementConversationRouter
          : encouragementConversationRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      localEncouragementConversationBgm: freezed ==
              localEncouragementConversationBgm
          ? _value.localEncouragementConversationBgm
          : localEncouragementConversationBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      localEncouragementConversationVoice: freezed ==
              localEncouragementConversationVoice
          ? _value.localEncouragementConversationVoice
          : localEncouragementConversationVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      roomTaskId: freezed == roomTaskId
          ? _value.roomTaskId
          : roomTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EncouragementConversationParamsVoCopyWith<$Res>
    implements $EncouragementConversationParamsVoCopyWith<$Res> {
  factory _$$_EncouragementConversationParamsVoCopyWith(
          _$_EncouragementConversationParamsVo value,
          $Res Function(_$_EncouragementConversationParamsVo) then) =
      __$$_EncouragementConversationParamsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? canPopupAiCall,
      String? encouragementConversationIcon,
      String? encouragementConversationTitle,
      String? encouragementConversationSubTitle,
      String? encouragementConversationBgm,
      String? encouragementConversationVoice,
      String? encouragementConversationRouter,
      String? localEncouragementConversationBgm,
      String? localEncouragementConversationVoice,
      String? roomTaskId});
}

/// @nodoc
class __$$_EncouragementConversationParamsVoCopyWithImpl<$Res>
    extends _$EncouragementConversationParamsVoCopyWithImpl<$Res,
        _$_EncouragementConversationParamsVo>
    implements _$$_EncouragementConversationParamsVoCopyWith<$Res> {
  __$$_EncouragementConversationParamsVoCopyWithImpl(
      _$_EncouragementConversationParamsVo _value,
      $Res Function(_$_EncouragementConversationParamsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canPopupAiCall = freezed,
    Object? encouragementConversationIcon = freezed,
    Object? encouragementConversationTitle = freezed,
    Object? encouragementConversationSubTitle = freezed,
    Object? encouragementConversationBgm = freezed,
    Object? encouragementConversationVoice = freezed,
    Object? encouragementConversationRouter = freezed,
    Object? localEncouragementConversationBgm = freezed,
    Object? localEncouragementConversationVoice = freezed,
    Object? roomTaskId = freezed,
  }) {
    return _then(_$_EncouragementConversationParamsVo(
      canPopupAiCall: freezed == canPopupAiCall
          ? _value.canPopupAiCall
          : canPopupAiCall // ignore: cast_nullable_to_non_nullable
              as bool?,
      encouragementConversationIcon: freezed == encouragementConversationIcon
          ? _value.encouragementConversationIcon
          : encouragementConversationIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationTitle: freezed == encouragementConversationTitle
          ? _value.encouragementConversationTitle
          : encouragementConversationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationSubTitle: freezed ==
              encouragementConversationSubTitle
          ? _value.encouragementConversationSubTitle
          : encouragementConversationSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationBgm: freezed == encouragementConversationBgm
          ? _value.encouragementConversationBgm
          : encouragementConversationBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationVoice: freezed == encouragementConversationVoice
          ? _value.encouragementConversationVoice
          : encouragementConversationVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      encouragementConversationRouter: freezed ==
              encouragementConversationRouter
          ? _value.encouragementConversationRouter
          : encouragementConversationRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      localEncouragementConversationBgm: freezed ==
              localEncouragementConversationBgm
          ? _value.localEncouragementConversationBgm
          : localEncouragementConversationBgm // ignore: cast_nullable_to_non_nullable
              as String?,
      localEncouragementConversationVoice: freezed ==
              localEncouragementConversationVoice
          ? _value.localEncouragementConversationVoice
          : localEncouragementConversationVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      roomTaskId: freezed == roomTaskId
          ? _value.roomTaskId
          : roomTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EncouragementConversationParamsVo
    implements _EncouragementConversationParamsVo {
  _$_EncouragementConversationParamsVo(
      {this.canPopupAiCall,
      this.encouragementConversationIcon,
      this.encouragementConversationTitle,
      this.encouragementConversationSubTitle,
      this.encouragementConversationBgm,
      this.encouragementConversationVoice,
      this.encouragementConversationRouter,
      this.localEncouragementConversationBgm,
      this.localEncouragementConversationVoice,
      this.roomTaskId});

  factory _$_EncouragementConversationParamsVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_EncouragementConversationParamsVoFromJson(json);

  @override
  bool? canPopupAiCall;
//完成里程碑奖励的最近时间
  @override
  String? encouragementConversationIcon;
//AI学伴激励电话Icon
  @override
  String? encouragementConversationTitle;
//AI学伴激励电话页面标题
  @override
  String? encouragementConversationSubTitle;
//AI学伴激励电话页面副标题
  @override
  String? encouragementConversationBgm;
//AI学伴激励电话背景音
  @override
  String? encouragementConversationVoice;
//AI学伴激励电话呼叫语音
  @override
  String? encouragementConversationRouter;
//AI学伴电话路由
  @override
  String? localEncouragementConversationBgm;
  @override
  String? localEncouragementConversationVoice;
  @override
  String? roomTaskId;

  @override
  String toString() {
    return 'EncouragementConversationParamsVo(canPopupAiCall: $canPopupAiCall, encouragementConversationIcon: $encouragementConversationIcon, encouragementConversationTitle: $encouragementConversationTitle, encouragementConversationSubTitle: $encouragementConversationSubTitle, encouragementConversationBgm: $encouragementConversationBgm, encouragementConversationVoice: $encouragementConversationVoice, encouragementConversationRouter: $encouragementConversationRouter, localEncouragementConversationBgm: $localEncouragementConversationBgm, localEncouragementConversationVoice: $localEncouragementConversationVoice, roomTaskId: $roomTaskId)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EncouragementConversationParamsVoCopyWith<
          _$_EncouragementConversationParamsVo>
      get copyWith => __$$_EncouragementConversationParamsVoCopyWithImpl<
          _$_EncouragementConversationParamsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EncouragementConversationParamsVoToJson(
      this,
    );
  }
}

abstract class _EncouragementConversationParamsVo
    implements EncouragementConversationParamsVo {
  factory _EncouragementConversationParamsVo(
      {bool? canPopupAiCall,
      String? encouragementConversationIcon,
      String? encouragementConversationTitle,
      String? encouragementConversationSubTitle,
      String? encouragementConversationBgm,
      String? encouragementConversationVoice,
      String? encouragementConversationRouter,
      String? localEncouragementConversationBgm,
      String? localEncouragementConversationVoice,
      String? roomTaskId}) = _$_EncouragementConversationParamsVo;

  factory _EncouragementConversationParamsVo.fromJson(
          Map<String, dynamic> json) =
      _$_EncouragementConversationParamsVo.fromJson;

  @override
  bool? get canPopupAiCall;
  set canPopupAiCall(bool? value);
  @override //完成里程碑奖励的最近时间
  String? get encouragementConversationIcon; //完成里程碑奖励的最近时间
  set encouragementConversationIcon(String? value);
  @override //AI学伴激励电话Icon
  String? get encouragementConversationTitle; //AI学伴激励电话Icon
  set encouragementConversationTitle(String? value);
  @override //AI学伴激励电话页面标题
  String? get encouragementConversationSubTitle; //AI学伴激励电话页面标题
  set encouragementConversationSubTitle(String? value);
  @override //AI学伴激励电话页面副标题
  String? get encouragementConversationBgm; //AI学伴激励电话页面副标题
  set encouragementConversationBgm(String? value);
  @override //AI学伴激励电话背景音
  String? get encouragementConversationVoice; //AI学伴激励电话背景音
  set encouragementConversationVoice(String? value);
  @override //AI学伴激励电话呼叫语音
  String? get encouragementConversationRouter; //AI学伴激励电话呼叫语音
  set encouragementConversationRouter(String? value);
  @override //AI学伴电话路由
  String? get localEncouragementConversationBgm; //AI学伴电话路由
  set localEncouragementConversationBgm(String? value);
  @override
  String? get localEncouragementConversationVoice;
  set localEncouragementConversationVoice(String? value);
  @override
  String? get roomTaskId;
  set roomTaskId(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_EncouragementConversationParamsVoCopyWith<
          _$_EncouragementConversationParamsVo>
      get copyWith => throw _privateConstructorUsedError;
}

GrayInfo _$GrayInfoFromJson(Map<String, dynamic> json) {
  return _GrayInfo.fromJson(json);
}

/// @nodoc
mixin _$GrayInfo {
  String? get grayScaleId => throw _privateConstructorUsedError;
  int? get testValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrayInfoCopyWith<GrayInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrayInfoCopyWith<$Res> {
  factory $GrayInfoCopyWith(GrayInfo value, $Res Function(GrayInfo) then) =
      _$GrayInfoCopyWithImpl<$Res, GrayInfo>;
  @useResult
  $Res call({String? grayScaleId, int? testValue});
}

/// @nodoc
class _$GrayInfoCopyWithImpl<$Res, $Val extends GrayInfo>
    implements $GrayInfoCopyWith<$Res> {
  _$GrayInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_value.copyWith(
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GrayInfoCopyWith<$Res> implements $GrayInfoCopyWith<$Res> {
  factory _$$_GrayInfoCopyWith(
          _$_GrayInfo value, $Res Function(_$_GrayInfo) then) =
      __$$_GrayInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? grayScaleId, int? testValue});
}

/// @nodoc
class __$$_GrayInfoCopyWithImpl<$Res>
    extends _$GrayInfoCopyWithImpl<$Res, _$_GrayInfo>
    implements _$$_GrayInfoCopyWith<$Res> {
  __$$_GrayInfoCopyWithImpl(
      _$_GrayInfo _value, $Res Function(_$_GrayInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_$_GrayInfo(
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GrayInfo implements _GrayInfo {
  const _$_GrayInfo({this.grayScaleId, this.testValue});

  factory _$_GrayInfo.fromJson(Map<String, dynamic> json) =>
      _$$_GrayInfoFromJson(json);

  @override
  final String? grayScaleId;
  @override
  final int? testValue;

  @override
  String toString() {
    return 'GrayInfo(grayScaleId: $grayScaleId, testValue: $testValue)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GrayInfo &&
            (identical(other.grayScaleId, grayScaleId) ||
                other.grayScaleId == grayScaleId) &&
            (identical(other.testValue, testValue) ||
                other.testValue == testValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, grayScaleId, testValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GrayInfoCopyWith<_$_GrayInfo> get copyWith =>
      __$$_GrayInfoCopyWithImpl<_$_GrayInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GrayInfoToJson(
      this,
    );
  }
}

abstract class _GrayInfo implements GrayInfo {
  const factory _GrayInfo({final String? grayScaleId, final int? testValue}) =
      _$_GrayInfo;

  factory _GrayInfo.fromJson(Map<String, dynamic> json) = _$_GrayInfo.fromJson;

  @override
  String? get grayScaleId;
  @override
  int? get testValue;
  @override
  @JsonKey(ignore: true)
  _$$_GrayInfoCopyWith<_$_GrayInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
