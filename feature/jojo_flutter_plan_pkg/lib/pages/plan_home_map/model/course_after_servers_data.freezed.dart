// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_after_servers_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseAfterServersData _$CourseAfterServersDataFromJson(
    Map<String, dynamic> json) {
  return _CourseAfterServersData.fromJson(json);
}

/// @nodoc
mixin _$CourseAfterServersData {
  String? get tabTitle => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get configKey => throw _privateConstructorUsedError;
  List<String?>? get toastList => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  List<ClassServerList>? get classServerList =>
      throw _privateConstructorUsedError;
  List<ClassPagePopupList>? get popupList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseAfterServersDataCopyWith<CourseAfterServersData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseAfterServersDataCopyWith<$Res> {
  factory $CourseAfterServersDataCopyWith(CourseAfterServersData value,
          $Res Function(CourseAfterServersData) then) =
      _$CourseAfterServersDataCopyWithImpl<$Res, CourseAfterServersData>;
  @useResult
  $Res call(
      {String? tabTitle,
      String? title,
      String? icon,
      String? route,
      String? configKey,
      List<String?>? toastList,
      String? subjectColor,
      String? subjectName,
      List<ClassServerList>? classServerList,
      List<ClassPagePopupList>? popupList});
}

/// @nodoc
class _$CourseAfterServersDataCopyWithImpl<$Res,
        $Val extends CourseAfterServersData>
    implements $CourseAfterServersDataCopyWith<$Res> {
  _$CourseAfterServersDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? configKey = freezed,
    Object? toastList = freezed,
    Object? subjectColor = freezed,
    Object? subjectName = freezed,
    Object? classServerList = freezed,
    Object? popupList = freezed,
  }) {
    return _then(_value.copyWith(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value.toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      classServerList: freezed == classServerList
          ? _value.classServerList
          : classServerList // ignore: cast_nullable_to_non_nullable
              as List<ClassServerList>?,
      popupList: freezed == popupList
          ? _value.popupList
          : popupList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseAfterServersDataCopyWith<$Res>
    implements $CourseAfterServersDataCopyWith<$Res> {
  factory _$$_CourseAfterServersDataCopyWith(_$_CourseAfterServersData value,
          $Res Function(_$_CourseAfterServersData) then) =
      __$$_CourseAfterServersDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tabTitle,
      String? title,
      String? icon,
      String? route,
      String? configKey,
      List<String?>? toastList,
      String? subjectColor,
      String? subjectName,
      List<ClassServerList>? classServerList,
      List<ClassPagePopupList>? popupList});
}

/// @nodoc
class __$$_CourseAfterServersDataCopyWithImpl<$Res>
    extends _$CourseAfterServersDataCopyWithImpl<$Res,
        _$_CourseAfterServersData>
    implements _$$_CourseAfterServersDataCopyWith<$Res> {
  __$$_CourseAfterServersDataCopyWithImpl(_$_CourseAfterServersData _value,
      $Res Function(_$_CourseAfterServersData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? configKey = freezed,
    Object? toastList = freezed,
    Object? subjectColor = freezed,
    Object? subjectName = freezed,
    Object? classServerList = freezed,
    Object? popupList = freezed,
  }) {
    return _then(_$_CourseAfterServersData(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value._toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      classServerList: freezed == classServerList
          ? _value._classServerList
          : classServerList // ignore: cast_nullable_to_non_nullable
              as List<ClassServerList>?,
      popupList: freezed == popupList
          ? _value._popupList
          : popupList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseAfterServersData implements _CourseAfterServersData {
  const _$_CourseAfterServersData(
      {this.tabTitle,
      this.title,
      this.icon,
      this.route,
      this.configKey,
      final List<String?>? toastList,
      this.subjectColor,
      this.subjectName,
      final List<ClassServerList>? classServerList,
      final List<ClassPagePopupList>? popupList})
      : _toastList = toastList,
        _classServerList = classServerList,
        _popupList = popupList;

  factory _$_CourseAfterServersData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseAfterServersDataFromJson(json);

  @override
  final String? tabTitle;
  @override
  final String? title;
  @override
  final String? icon;
  @override
  final String? route;
  @override
  final String? configKey;
  final List<String?>? _toastList;
  @override
  List<String?>? get toastList {
    final value = _toastList;
    if (value == null) return null;
    if (_toastList is EqualUnmodifiableListView) return _toastList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? subjectColor;
  @override
  final String? subjectName;
  final List<ClassServerList>? _classServerList;
  @override
  List<ClassServerList>? get classServerList {
    final value = _classServerList;
    if (value == null) return null;
    if (_classServerList is EqualUnmodifiableListView) return _classServerList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ClassPagePopupList>? _popupList;
  @override
  List<ClassPagePopupList>? get popupList {
    final value = _popupList;
    if (value == null) return null;
    if (_popupList is EqualUnmodifiableListView) return _popupList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseAfterServersData(tabTitle: $tabTitle, title: $title, icon: $icon, route: $route, configKey: $configKey, toastList: $toastList, subjectColor: $subjectColor, subjectName: $subjectName, classServerList: $classServerList, popupList: $popupList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseAfterServersData &&
            (identical(other.tabTitle, tabTitle) ||
                other.tabTitle == tabTitle) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.configKey, configKey) ||
                other.configKey == configKey) &&
            const DeepCollectionEquality()
                .equals(other._toastList, _toastList) &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            const DeepCollectionEquality()
                .equals(other._classServerList, _classServerList) &&
            const DeepCollectionEquality()
                .equals(other._popupList, _popupList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      tabTitle,
      title,
      icon,
      route,
      configKey,
      const DeepCollectionEquality().hash(_toastList),
      subjectColor,
      subjectName,
      const DeepCollectionEquality().hash(_classServerList),
      const DeepCollectionEquality().hash(_popupList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseAfterServersDataCopyWith<_$_CourseAfterServersData> get copyWith =>
      __$$_CourseAfterServersDataCopyWithImpl<_$_CourseAfterServersData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseAfterServersDataToJson(
      this,
    );
  }
}

abstract class _CourseAfterServersData implements CourseAfterServersData {
  const factory _CourseAfterServersData(
      {final String? tabTitle,
      final String? title,
      final String? icon,
      final String? route,
      final String? configKey,
      final List<String?>? toastList,
      final String? subjectColor,
      final String? subjectName,
      final List<ClassServerList>? classServerList,
      final List<ClassPagePopupList>? popupList}) = _$_CourseAfterServersData;

  factory _CourseAfterServersData.fromJson(Map<String, dynamic> json) =
      _$_CourseAfterServersData.fromJson;

  @override
  String? get tabTitle;
  @override
  String? get title;
  @override
  String? get icon;
  @override
  String? get route;
  @override
  String? get configKey;
  @override
  List<String?>? get toastList;
  @override
  String? get subjectColor;
  @override
  String? get subjectName;
  @override
  List<ClassServerList>? get classServerList;
  @override
  List<ClassPagePopupList>? get popupList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseAfterServersDataCopyWith<_$_CourseAfterServersData> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassServerList _$ClassServerListFromJson(Map<String, dynamic> json) {
  return _ClassServerList.fromJson(json);
}

/// @nodoc
mixin _$ClassServerList {
  bool? get endClass => throw _privateConstructorUsedError;
  String? get classTitle => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get classStatus => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  ClassTeacher? get classTeacher => throw _privateConstructorUsedError;
  List<ServerInfoList>? get serverInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassServerListCopyWith<ClassServerList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassServerListCopyWith<$Res> {
  factory $ClassServerListCopyWith(
          ClassServerList value, $Res Function(ClassServerList) then) =
      _$ClassServerListCopyWithImpl<$Res, ClassServerList>;
  @useResult
  $Res call(
      {bool? endClass,
      String? classTitle,
      int? classId,
      int? classStatus,
      int? courseId,
      int? courseType,
      String? courseKey,
      String? courseSegment,
      ClassTeacher? classTeacher,
      List<ServerInfoList>? serverInfoList});

  $ClassTeacherCopyWith<$Res>? get classTeacher;
}

/// @nodoc
class _$ClassServerListCopyWithImpl<$Res, $Val extends ClassServerList>
    implements $ClassServerListCopyWith<$Res> {
  _$ClassServerListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endClass = freezed,
    Object? classTitle = freezed,
    Object? classId = freezed,
    Object? classStatus = freezed,
    Object? courseId = freezed,
    Object? courseType = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? classTeacher = freezed,
    Object? serverInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      endClass: freezed == endClass
          ? _value.endClass
          : endClass // ignore: cast_nullable_to_non_nullable
              as bool?,
      classTitle: freezed == classTitle
          ? _value.classTitle
          : classTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacher: freezed == classTeacher
          ? _value.classTeacher
          : classTeacher // ignore: cast_nullable_to_non_nullable
              as ClassTeacher?,
      serverInfoList: freezed == serverInfoList
          ? _value.serverInfoList
          : serverInfoList // ignore: cast_nullable_to_non_nullable
              as List<ServerInfoList>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassTeacherCopyWith<$Res>? get classTeacher {
    if (_value.classTeacher == null) {
      return null;
    }

    return $ClassTeacherCopyWith<$Res>(_value.classTeacher!, (value) {
      return _then(_value.copyWith(classTeacher: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ClassServerListCopyWith<$Res>
    implements $ClassServerListCopyWith<$Res> {
  factory _$$_ClassServerListCopyWith(
          _$_ClassServerList value, $Res Function(_$_ClassServerList) then) =
      __$$_ClassServerListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? endClass,
      String? classTitle,
      int? classId,
      int? classStatus,
      int? courseId,
      int? courseType,
      String? courseKey,
      String? courseSegment,
      ClassTeacher? classTeacher,
      List<ServerInfoList>? serverInfoList});

  @override
  $ClassTeacherCopyWith<$Res>? get classTeacher;
}

/// @nodoc
class __$$_ClassServerListCopyWithImpl<$Res>
    extends _$ClassServerListCopyWithImpl<$Res, _$_ClassServerList>
    implements _$$_ClassServerListCopyWith<$Res> {
  __$$_ClassServerListCopyWithImpl(
      _$_ClassServerList _value, $Res Function(_$_ClassServerList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endClass = freezed,
    Object? classTitle = freezed,
    Object? classId = freezed,
    Object? classStatus = freezed,
    Object? courseId = freezed,
    Object? courseType = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? classTeacher = freezed,
    Object? serverInfoList = freezed,
  }) {
    return _then(_$_ClassServerList(
      endClass: freezed == endClass
          ? _value.endClass
          : endClass // ignore: cast_nullable_to_non_nullable
              as bool?,
      classTitle: freezed == classTitle
          ? _value.classTitle
          : classTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classTeacher: freezed == classTeacher
          ? _value.classTeacher
          : classTeacher // ignore: cast_nullable_to_non_nullable
              as ClassTeacher?,
      serverInfoList: freezed == serverInfoList
          ? _value._serverInfoList
          : serverInfoList // ignore: cast_nullable_to_non_nullable
              as List<ServerInfoList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassServerList implements _ClassServerList {
  const _$_ClassServerList(
      {this.endClass,
      this.classTitle,
      this.classId,
      this.classStatus,
      this.courseId,
      this.courseType,
      this.courseKey,
      this.courseSegment,
      this.classTeacher,
      final List<ServerInfoList>? serverInfoList})
      : _serverInfoList = serverInfoList;

  factory _$_ClassServerList.fromJson(Map<String, dynamic> json) =>
      _$$_ClassServerListFromJson(json);

  @override
  final bool? endClass;
  @override
  final String? classTitle;
  @override
  final int? classId;
  @override
  final int? classStatus;
  @override
  final int? courseId;
  @override
  final int? courseType;
  @override
  final String? courseKey;
  @override
  final String? courseSegment;
  @override
  final ClassTeacher? classTeacher;
  final List<ServerInfoList>? _serverInfoList;
  @override
  List<ServerInfoList>? get serverInfoList {
    final value = _serverInfoList;
    if (value == null) return null;
    if (_serverInfoList is EqualUnmodifiableListView) return _serverInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ClassServerList(endClass: $endClass, classTitle: $classTitle, classId: $classId, classStatus: $classStatus, courseId: $courseId, courseType: $courseType, courseKey: $courseKey, courseSegment: $courseSegment, classTeacher: $classTeacher, serverInfoList: $serverInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassServerList &&
            (identical(other.endClass, endClass) ||
                other.endClass == endClass) &&
            (identical(other.classTitle, classTitle) ||
                other.classTitle == classTitle) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classStatus, classStatus) ||
                other.classStatus == classStatus) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.classTeacher, classTeacher) ||
                other.classTeacher == classTeacher) &&
            const DeepCollectionEquality()
                .equals(other._serverInfoList, _serverInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      endClass,
      classTitle,
      classId,
      classStatus,
      courseId,
      courseType,
      courseKey,
      courseSegment,
      classTeacher,
      const DeepCollectionEquality().hash(_serverInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassServerListCopyWith<_$_ClassServerList> get copyWith =>
      __$$_ClassServerListCopyWithImpl<_$_ClassServerList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassServerListToJson(
      this,
    );
  }
}

abstract class _ClassServerList implements ClassServerList {
  const factory _ClassServerList(
      {final bool? endClass,
      final String? classTitle,
      final int? classId,
      final int? classStatus,
      final int? courseId,
      final int? courseType,
      final String? courseKey,
      final String? courseSegment,
      final ClassTeacher? classTeacher,
      final List<ServerInfoList>? serverInfoList}) = _$_ClassServerList;

  factory _ClassServerList.fromJson(Map<String, dynamic> json) =
      _$_ClassServerList.fromJson;

  @override
  bool? get endClass;
  @override
  String? get classTitle;
  @override
  int? get classId;
  @override
  int? get classStatus;
  @override
  int? get courseId;
  @override
  int? get courseType;
  @override
  String? get courseKey;
  @override
  String? get courseSegment;
  @override
  ClassTeacher? get classTeacher;
  @override
  List<ServerInfoList>? get serverInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_ClassServerListCopyWith<_$_ClassServerList> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassTeacher _$ClassTeacherFromJson(Map<String, dynamic> json) {
  return _ClassTeacher.fromJson(json);
}

/// @nodoc
mixin _$ClassTeacher {
  int? get teacherId => throw _privateConstructorUsedError;
  String? get teacherTitle => throw _privateConstructorUsedError;
  String? get teacherAvatar => throw _privateConstructorUsedError;
  bool? get add => throw _privateConstructorUsedError;
  String? get toast => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassTeacherCopyWith<ClassTeacher> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassTeacherCopyWith<$Res> {
  factory $ClassTeacherCopyWith(
          ClassTeacher value, $Res Function(ClassTeacher) then) =
      _$ClassTeacherCopyWithImpl<$Res, ClassTeacher>;
  @useResult
  $Res call(
      {int? teacherId,
      String? teacherTitle,
      String? teacherAvatar,
      bool? add,
      String? toast,
      String? route});
}

/// @nodoc
class _$ClassTeacherCopyWithImpl<$Res, $Val extends ClassTeacher>
    implements $ClassTeacherCopyWith<$Res> {
  _$ClassTeacherCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherTitle = freezed,
    Object? teacherAvatar = freezed,
    Object? add = freezed,
    Object? toast = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherTitle: freezed == teacherTitle
          ? _value.teacherTitle
          : teacherTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      add: freezed == add
          ? _value.add
          : add // ignore: cast_nullable_to_non_nullable
              as bool?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassTeacherCopyWith<$Res>
    implements $ClassTeacherCopyWith<$Res> {
  factory _$$_ClassTeacherCopyWith(
          _$_ClassTeacher value, $Res Function(_$_ClassTeacher) then) =
      __$$_ClassTeacherCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teacherId,
      String? teacherTitle,
      String? teacherAvatar,
      bool? add,
      String? toast,
      String? route});
}

/// @nodoc
class __$$_ClassTeacherCopyWithImpl<$Res>
    extends _$ClassTeacherCopyWithImpl<$Res, _$_ClassTeacher>
    implements _$$_ClassTeacherCopyWith<$Res> {
  __$$_ClassTeacherCopyWithImpl(
      _$_ClassTeacher _value, $Res Function(_$_ClassTeacher) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherId = freezed,
    Object? teacherTitle = freezed,
    Object? teacherAvatar = freezed,
    Object? add = freezed,
    Object? toast = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_ClassTeacher(
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherTitle: freezed == teacherTitle
          ? _value.teacherTitle
          : teacherTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAvatar: freezed == teacherAvatar
          ? _value.teacherAvatar
          : teacherAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      add: freezed == add
          ? _value.add
          : add // ignore: cast_nullable_to_non_nullable
              as bool?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassTeacher implements _ClassTeacher {
  const _$_ClassTeacher(
      {this.teacherId,
      this.teacherTitle,
      this.teacherAvatar,
      this.add,
      this.toast,
      this.route});

  factory _$_ClassTeacher.fromJson(Map<String, dynamic> json) =>
      _$$_ClassTeacherFromJson(json);

  @override
  final int? teacherId;
  @override
  final String? teacherTitle;
  @override
  final String? teacherAvatar;
  @override
  final bool? add;
  @override
  final String? toast;
  @override
  final String? route;

  @override
  String toString() {
    return 'ClassTeacher(teacherId: $teacherId, teacherTitle: $teacherTitle, teacherAvatar: $teacherAvatar, add: $add, toast: $toast, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassTeacher &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherTitle, teacherTitle) ||
                other.teacherTitle == teacherTitle) &&
            (identical(other.teacherAvatar, teacherAvatar) ||
                other.teacherAvatar == teacherAvatar) &&
            (identical(other.add, add) || other.add == add) &&
            (identical(other.toast, toast) || other.toast == toast) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, teacherId, teacherTitle, teacherAvatar, add, toast, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassTeacherCopyWith<_$_ClassTeacher> get copyWith =>
      __$$_ClassTeacherCopyWithImpl<_$_ClassTeacher>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassTeacherToJson(
      this,
    );
  }
}

abstract class _ClassTeacher implements ClassTeacher {
  const factory _ClassTeacher(
      {final int? teacherId,
      final String? teacherTitle,
      final String? teacherAvatar,
      final bool? add,
      final String? toast,
      final String? route}) = _$_ClassTeacher;

  factory _ClassTeacher.fromJson(Map<String, dynamic> json) =
      _$_ClassTeacher.fromJson;

  @override
  int? get teacherId;
  @override
  String? get teacherTitle;
  @override
  String? get teacherAvatar;
  @override
  bool? get add;
  @override
  String? get toast;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_ClassTeacherCopyWith<_$_ClassTeacher> get copyWith =>
      throw _privateConstructorUsedError;
}

ServerInfoList _$ServerInfoListFromJson(Map<String, dynamic> json) {
  return _ServerInfoList.fromJson(json);
}

/// @nodoc
mixin _$ServerInfoList {
  bool? get isBuried => throw _privateConstructorUsedError;
  set isBuried(bool? value) => throw _privateConstructorUsedError; // 是否埋过点
  int? get status => throw _privateConstructorUsedError; // 是否埋过点
  set status(int? value) =>
      throw _privateConstructorUsedError; // 1. 可点击  0.不可点击
  int? get updateNum => throw _privateConstructorUsedError; // 1. 可点击  0.不可点击
  set updateNum(int? value) => throw _privateConstructorUsedError; // 更新数目
  String? get title => throw _privateConstructorUsedError; // 更新数目
  set title(String? value) => throw _privateConstructorUsedError;
  String? get key => throw _privateConstructorUsedError;
  set key(String? value) => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  set route(String? value) => throw _privateConstructorUsedError;
  String? get toast => throw _privateConstructorUsedError;
  set toast(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ServerInfoListCopyWith<ServerInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServerInfoListCopyWith<$Res> {
  factory $ServerInfoListCopyWith(
          ServerInfoList value, $Res Function(ServerInfoList) then) =
      _$ServerInfoListCopyWithImpl<$Res, ServerInfoList>;
  @useResult
  $Res call(
      {bool? isBuried,
      int? status,
      int? updateNum,
      String? title,
      String? key,
      String? icon,
      String? route,
      String? toast});
}

/// @nodoc
class _$ServerInfoListCopyWithImpl<$Res, $Val extends ServerInfoList>
    implements $ServerInfoListCopyWith<$Res> {
  _$ServerInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isBuried = freezed,
    Object? status = freezed,
    Object? updateNum = freezed,
    Object? title = freezed,
    Object? key = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toast = freezed,
  }) {
    return _then(_value.copyWith(
      isBuried: freezed == isBuried
          ? _value.isBuried
          : isBuried // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      updateNum: freezed == updateNum
          ? _value.updateNum
          : updateNum // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ServerInfoListCopyWith<$Res>
    implements $ServerInfoListCopyWith<$Res> {
  factory _$$_ServerInfoListCopyWith(
          _$_ServerInfoList value, $Res Function(_$_ServerInfoList) then) =
      __$$_ServerInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isBuried,
      int? status,
      int? updateNum,
      String? title,
      String? key,
      String? icon,
      String? route,
      String? toast});
}

/// @nodoc
class __$$_ServerInfoListCopyWithImpl<$Res>
    extends _$ServerInfoListCopyWithImpl<$Res, _$_ServerInfoList>
    implements _$$_ServerInfoListCopyWith<$Res> {
  __$$_ServerInfoListCopyWithImpl(
      _$_ServerInfoList _value, $Res Function(_$_ServerInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isBuried = freezed,
    Object? status = freezed,
    Object? updateNum = freezed,
    Object? title = freezed,
    Object? key = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toast = freezed,
  }) {
    return _then(_$_ServerInfoList(
      isBuried: freezed == isBuried
          ? _value.isBuried
          : isBuried // ignore: cast_nullable_to_non_nullable
              as bool?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      updateNum: freezed == updateNum
          ? _value.updateNum
          : updateNum // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ServerInfoList implements _ServerInfoList {
  _$_ServerInfoList(
      {this.isBuried,
      this.status,
      this.updateNum,
      this.title,
      this.key,
      this.icon,
      this.route,
      this.toast});

  factory _$_ServerInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_ServerInfoListFromJson(json);

  @override
  bool? isBuried;
// 是否埋过点
  @override
  int? status;
// 1. 可点击  0.不可点击
  @override
  int? updateNum;
// 更新数目
  @override
  String? title;
  @override
  String? key;
  @override
  String? icon;
  @override
  String? route;
  @override
  String? toast;

  @override
  String toString() {
    return 'ServerInfoList(isBuried: $isBuried, status: $status, updateNum: $updateNum, title: $title, key: $key, icon: $icon, route: $route, toast: $toast)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ServerInfoListCopyWith<_$_ServerInfoList> get copyWith =>
      __$$_ServerInfoListCopyWithImpl<_$_ServerInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ServerInfoListToJson(
      this,
    );
  }
}

abstract class _ServerInfoList implements ServerInfoList {
  factory _ServerInfoList(
      {bool? isBuried,
      int? status,
      int? updateNum,
      String? title,
      String? key,
      String? icon,
      String? route,
      String? toast}) = _$_ServerInfoList;

  factory _ServerInfoList.fromJson(Map<String, dynamic> json) =
      _$_ServerInfoList.fromJson;

  @override
  bool? get isBuried;
  set isBuried(bool? value);
  @override // 是否埋过点
  int? get status; // 是否埋过点
  set status(int? value);
  @override // 1. 可点击  0.不可点击
  int? get updateNum; // 1. 可点击  0.不可点击
  set updateNum(int? value);
  @override // 更新数目
  String? get title; // 更新数目
  set title(String? value);
  @override
  String? get key;
  set key(String? value);
  @override
  String? get icon;
  set icon(String? value);
  @override
  String? get route;
  set route(String? value);
  @override
  String? get toast;
  set toast(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_ServerInfoListCopyWith<_$_ServerInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}
