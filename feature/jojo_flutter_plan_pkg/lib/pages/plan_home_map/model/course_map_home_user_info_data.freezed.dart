// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_map_home_user_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TopUserInfo _$TopUserInfoFromJson(Map<String, dynamic> json) {
  return _TopUserInfo.fromJson(json);
}

/// @nodoc
mixin _$TopUserInfo {
  Dress? get dress => throw _privateConstructorUsedError; //装扮图
  PartnerGuide? get partnerGuide => throw _privateConstructorUsedError; // 学伴引导
  Continuous? get continuous => throw _privateConstructorUsedError; //连续学
  LearnBean? get learnBean => throw _privateConstructorUsedError; //学豆
  LearnBean? get medal => throw _privateConstructorUsedError; //徽章
  NewDressUpNotice? get newDressUpNotice => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TopUserInfoCopyWith<TopUserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TopUserInfoCopyWith<$Res> {
  factory $TopUserInfoCopyWith(
          TopUserInfo value, $Res Function(TopUserInfo) then) =
      _$TopUserInfoCopyWithImpl<$Res, TopUserInfo>;
  @useResult
  $Res call(
      {Dress? dress,
      PartnerGuide? partnerGuide,
      Continuous? continuous,
      LearnBean? learnBean,
      LearnBean? medal,
      NewDressUpNotice? newDressUpNotice});

  $DressCopyWith<$Res>? get dress;
  $PartnerGuideCopyWith<$Res>? get partnerGuide;
  $ContinuousCopyWith<$Res>? get continuous;
  $LearnBeanCopyWith<$Res>? get learnBean;
  $LearnBeanCopyWith<$Res>? get medal;
  $NewDressUpNoticeCopyWith<$Res>? get newDressUpNotice;
}

/// @nodoc
class _$TopUserInfoCopyWithImpl<$Res, $Val extends TopUserInfo>
    implements $TopUserInfoCopyWith<$Res> {
  _$TopUserInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dress = freezed,
    Object? partnerGuide = freezed,
    Object? continuous = freezed,
    Object? learnBean = freezed,
    Object? medal = freezed,
    Object? newDressUpNotice = freezed,
  }) {
    return _then(_value.copyWith(
      dress: freezed == dress
          ? _value.dress
          : dress // ignore: cast_nullable_to_non_nullable
              as Dress?,
      partnerGuide: freezed == partnerGuide
          ? _value.partnerGuide
          : partnerGuide // ignore: cast_nullable_to_non_nullable
              as PartnerGuide?,
      continuous: freezed == continuous
          ? _value.continuous
          : continuous // ignore: cast_nullable_to_non_nullable
              as Continuous?,
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      newDressUpNotice: freezed == newDressUpNotice
          ? _value.newDressUpNotice
          : newDressUpNotice // ignore: cast_nullable_to_non_nullable
              as NewDressUpNotice?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DressCopyWith<$Res>? get dress {
    if (_value.dress == null) {
      return null;
    }

    return $DressCopyWith<$Res>(_value.dress!, (value) {
      return _then(_value.copyWith(dress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PartnerGuideCopyWith<$Res>? get partnerGuide {
    if (_value.partnerGuide == null) {
      return null;
    }

    return $PartnerGuideCopyWith<$Res>(_value.partnerGuide!, (value) {
      return _then(_value.copyWith(partnerGuide: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ContinuousCopyWith<$Res>? get continuous {
    if (_value.continuous == null) {
      return null;
    }

    return $ContinuousCopyWith<$Res>(_value.continuous!, (value) {
      return _then(_value.copyWith(continuous: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LearnBeanCopyWith<$Res>? get learnBean {
    if (_value.learnBean == null) {
      return null;
    }

    return $LearnBeanCopyWith<$Res>(_value.learnBean!, (value) {
      return _then(_value.copyWith(learnBean: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LearnBeanCopyWith<$Res>? get medal {
    if (_value.medal == null) {
      return null;
    }

    return $LearnBeanCopyWith<$Res>(_value.medal!, (value) {
      return _then(_value.copyWith(medal: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $NewDressUpNoticeCopyWith<$Res>? get newDressUpNotice {
    if (_value.newDressUpNotice == null) {
      return null;
    }

    return $NewDressUpNoticeCopyWith<$Res>(_value.newDressUpNotice!, (value) {
      return _then(_value.copyWith(newDressUpNotice: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TopUserInfoCopyWith<$Res>
    implements $TopUserInfoCopyWith<$Res> {
  factory _$$_TopUserInfoCopyWith(
          _$_TopUserInfo value, $Res Function(_$_TopUserInfo) then) =
      __$$_TopUserInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Dress? dress,
      PartnerGuide? partnerGuide,
      Continuous? continuous,
      LearnBean? learnBean,
      LearnBean? medal,
      NewDressUpNotice? newDressUpNotice});

  @override
  $DressCopyWith<$Res>? get dress;
  @override
  $PartnerGuideCopyWith<$Res>? get partnerGuide;
  @override
  $ContinuousCopyWith<$Res>? get continuous;
  @override
  $LearnBeanCopyWith<$Res>? get learnBean;
  @override
  $LearnBeanCopyWith<$Res>? get medal;
  @override
  $NewDressUpNoticeCopyWith<$Res>? get newDressUpNotice;
}

/// @nodoc
class __$$_TopUserInfoCopyWithImpl<$Res>
    extends _$TopUserInfoCopyWithImpl<$Res, _$_TopUserInfo>
    implements _$$_TopUserInfoCopyWith<$Res> {
  __$$_TopUserInfoCopyWithImpl(
      _$_TopUserInfo _value, $Res Function(_$_TopUserInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dress = freezed,
    Object? partnerGuide = freezed,
    Object? continuous = freezed,
    Object? learnBean = freezed,
    Object? medal = freezed,
    Object? newDressUpNotice = freezed,
  }) {
    return _then(_$_TopUserInfo(
      dress: freezed == dress
          ? _value.dress
          : dress // ignore: cast_nullable_to_non_nullable
              as Dress?,
      partnerGuide: freezed == partnerGuide
          ? _value.partnerGuide
          : partnerGuide // ignore: cast_nullable_to_non_nullable
              as PartnerGuide?,
      continuous: freezed == continuous
          ? _value.continuous
          : continuous // ignore: cast_nullable_to_non_nullable
              as Continuous?,
      learnBean: freezed == learnBean
          ? _value.learnBean
          : learnBean // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as LearnBean?,
      newDressUpNotice: freezed == newDressUpNotice
          ? _value.newDressUpNotice
          : newDressUpNotice // ignore: cast_nullable_to_non_nullable
              as NewDressUpNotice?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TopUserInfo implements _TopUserInfo {
  const _$_TopUserInfo(
      {this.dress,
      this.partnerGuide,
      this.continuous,
      this.learnBean,
      this.medal,
      this.newDressUpNotice});

  factory _$_TopUserInfo.fromJson(Map<String, dynamic> json) =>
      _$$_TopUserInfoFromJson(json);

  @override
  final Dress? dress;
//装扮图
  @override
  final PartnerGuide? partnerGuide;
// 学伴引导
  @override
  final Continuous? continuous;
//连续学
  @override
  final LearnBean? learnBean;
//学豆
  @override
  final LearnBean? medal;
//徽章
  @override
  final NewDressUpNotice? newDressUpNotice;

  @override
  String toString() {
    return 'TopUserInfo(dress: $dress, partnerGuide: $partnerGuide, continuous: $continuous, learnBean: $learnBean, medal: $medal, newDressUpNotice: $newDressUpNotice)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TopUserInfo &&
            (identical(other.dress, dress) || other.dress == dress) &&
            (identical(other.partnerGuide, partnerGuide) ||
                other.partnerGuide == partnerGuide) &&
            (identical(other.continuous, continuous) ||
                other.continuous == continuous) &&
            (identical(other.learnBean, learnBean) ||
                other.learnBean == learnBean) &&
            (identical(other.medal, medal) || other.medal == medal) &&
            (identical(other.newDressUpNotice, newDressUpNotice) ||
                other.newDressUpNotice == newDressUpNotice));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, dress, partnerGuide, continuous,
      learnBean, medal, newDressUpNotice);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TopUserInfoCopyWith<_$_TopUserInfo> get copyWith =>
      __$$_TopUserInfoCopyWithImpl<_$_TopUserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TopUserInfoToJson(
      this,
    );
  }
}

abstract class _TopUserInfo implements TopUserInfo {
  const factory _TopUserInfo(
      {final Dress? dress,
      final PartnerGuide? partnerGuide,
      final Continuous? continuous,
      final LearnBean? learnBean,
      final LearnBean? medal,
      final NewDressUpNotice? newDressUpNotice}) = _$_TopUserInfo;

  factory _TopUserInfo.fromJson(Map<String, dynamic> json) =
      _$_TopUserInfo.fromJson;

  @override
  Dress? get dress;
  @override //装扮图
  PartnerGuide? get partnerGuide;
  @override // 学伴引导
  Continuous? get continuous;
  @override //连续学
  LearnBean? get learnBean;
  @override //学豆
  LearnBean? get medal;
  @override //徽章
  NewDressUpNotice? get newDressUpNotice;
  @override
  @JsonKey(ignore: true)
  _$$_TopUserInfoCopyWith<_$_TopUserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Continuous _$ContinuousFromJson(Map<String, dynamic> json) {
  return _Continuous.fromJson(json);
}

/// @nodoc
mixin _$Continuous {
  int? get days => throw _privateConstructorUsedError;
  set days(int? value) => throw _privateConstructorUsedError;
  int? get isStart => throw _privateConstructorUsedError;
  set isStart(int? value) => throw _privateConstructorUsedError; //是否开始(1是0否)
  String? get icon => throw _privateConstructorUsedError; //是否开始(1是0否)
  set icon(String? value) => throw _privateConstructorUsedError; // 连续学icon
  String? get clientDynamic => throw _privateConstructorUsedError; // 连续学icon
  set clientDynamic(String? value) =>
      throw _privateConstructorUsedError; // 动画名称
  String? get resource => throw _privateConstructorUsedError; // 动画名称
  set resource(String? value) =>
      throw _privateConstructorUsedError; // 连续学动效（如果没有返回，则显示icon）
  int? get isFinishLesson =>
      throw _privateConstructorUsedError; // 连续学动效（如果没有返回，则显示icon）
  set isFinishLesson(int? value) =>
      throw _privateConstructorUsedError; //是否完成课程(1是0否)
  int? get isFinishLessonPopup =>
      throw _privateConstructorUsedError; //是否完成课程(1是0否)
  set isFinishLessonPopup(int? value) =>
      throw _privateConstructorUsedError; //是否有完课后的捏脸弹窗(1是0否)
  String? get notStartText =>
      throw _privateConstructorUsedError; //是否有完课后的捏脸弹窗(1是0否)
  set notStartText(String? value) =>
      throw _privateConstructorUsedError; //等待期文案(isStart=0)
  int? get status => throw _privateConstructorUsedError; //等待期文案(isStart=0)
  set status(int? value) => throw _privateConstructorUsedError; // 学习状态
  String? get jumpRoute => throw _privateConstructorUsedError; // 学习状态
  set jumpRoute(String? value) => throw _privateConstructorUsedError;
  Milestone? get milestone => throw _privateConstructorUsedError;
  set milestone(Milestone? value) => throw _privateConstructorUsedError; // 里程碑
  int? get bestDays => throw _privateConstructorUsedError; // 里程碑
  set bestDays(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContinuousCopyWith<Continuous> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContinuousCopyWith<$Res> {
  factory $ContinuousCopyWith(
          Continuous value, $Res Function(Continuous) then) =
      _$ContinuousCopyWithImpl<$Res, Continuous>;
  @useResult
  $Res call(
      {int? days,
      int? isStart,
      String? icon,
      String? clientDynamic,
      String? resource,
      int? isFinishLesson,
      int? isFinishLessonPopup,
      String? notStartText,
      int? status,
      String? jumpRoute,
      Milestone? milestone,
      int? bestDays});

  $MilestoneCopyWith<$Res>? get milestone;
}

/// @nodoc
class _$ContinuousCopyWithImpl<$Res, $Val extends Continuous>
    implements $ContinuousCopyWith<$Res> {
  _$ContinuousCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? days = freezed,
    Object? isStart = freezed,
    Object? icon = freezed,
    Object? clientDynamic = freezed,
    Object? resource = freezed,
    Object? isFinishLesson = freezed,
    Object? isFinishLessonPopup = freezed,
    Object? notStartText = freezed,
    Object? status = freezed,
    Object? jumpRoute = freezed,
    Object? milestone = freezed,
    Object? bestDays = freezed,
  }) {
    return _then(_value.copyWith(
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      isStart: freezed == isStart
          ? _value.isStart
          : isStart // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinishLesson: freezed == isFinishLesson
          ? _value.isFinishLesson
          : isFinishLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinishLessonPopup: freezed == isFinishLessonPopup
          ? _value.isFinishLessonPopup
          : isFinishLessonPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      notStartText: freezed == notStartText
          ? _value.notStartText
          : notStartText // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      milestone: freezed == milestone
          ? _value.milestone
          : milestone // ignore: cast_nullable_to_non_nullable
              as Milestone?,
      bestDays: freezed == bestDays
          ? _value.bestDays
          : bestDays // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MilestoneCopyWith<$Res>? get milestone {
    if (_value.milestone == null) {
      return null;
    }

    return $MilestoneCopyWith<$Res>(_value.milestone!, (value) {
      return _then(_value.copyWith(milestone: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ContinuousCopyWith<$Res>
    implements $ContinuousCopyWith<$Res> {
  factory _$$_ContinuousCopyWith(
          _$_Continuous value, $Res Function(_$_Continuous) then) =
      __$$_ContinuousCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? days,
      int? isStart,
      String? icon,
      String? clientDynamic,
      String? resource,
      int? isFinishLesson,
      int? isFinishLessonPopup,
      String? notStartText,
      int? status,
      String? jumpRoute,
      Milestone? milestone,
      int? bestDays});

  @override
  $MilestoneCopyWith<$Res>? get milestone;
}

/// @nodoc
class __$$_ContinuousCopyWithImpl<$Res>
    extends _$ContinuousCopyWithImpl<$Res, _$_Continuous>
    implements _$$_ContinuousCopyWith<$Res> {
  __$$_ContinuousCopyWithImpl(
      _$_Continuous _value, $Res Function(_$_Continuous) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? days = freezed,
    Object? isStart = freezed,
    Object? icon = freezed,
    Object? clientDynamic = freezed,
    Object? resource = freezed,
    Object? isFinishLesson = freezed,
    Object? isFinishLessonPopup = freezed,
    Object? notStartText = freezed,
    Object? status = freezed,
    Object? jumpRoute = freezed,
    Object? milestone = freezed,
    Object? bestDays = freezed,
  }) {
    return _then(_$_Continuous(
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      isStart: freezed == isStart
          ? _value.isStart
          : isStart // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      clientDynamic: freezed == clientDynamic
          ? _value.clientDynamic
          : clientDynamic // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      isFinishLesson: freezed == isFinishLesson
          ? _value.isFinishLesson
          : isFinishLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinishLessonPopup: freezed == isFinishLessonPopup
          ? _value.isFinishLessonPopup
          : isFinishLessonPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      notStartText: freezed == notStartText
          ? _value.notStartText
          : notStartText // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      milestone: freezed == milestone
          ? _value.milestone
          : milestone // ignore: cast_nullable_to_non_nullable
              as Milestone?,
      bestDays: freezed == bestDays
          ? _value.bestDays
          : bestDays // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Continuous implements _Continuous {
  _$_Continuous(
      {this.days,
      this.isStart,
      this.icon,
      this.clientDynamic,
      this.resource,
      this.isFinishLesson,
      this.isFinishLessonPopup,
      this.notStartText,
      this.status,
      this.jumpRoute,
      this.milestone,
      this.bestDays});

  factory _$_Continuous.fromJson(Map<String, dynamic> json) =>
      _$$_ContinuousFromJson(json);

  @override
  int? days;
  @override
  int? isStart;
//是否开始(1是0否)
  @override
  String? icon;
// 连续学icon
  @override
  String? clientDynamic;
// 动画名称
  @override
  String? resource;
// 连续学动效（如果没有返回，则显示icon）
  @override
  int? isFinishLesson;
//是否完成课程(1是0否)
  @override
  int? isFinishLessonPopup;
//是否有完课后的捏脸弹窗(1是0否)
  @override
  String? notStartText;
//等待期文案(isStart=0)
  @override
  int? status;
// 学习状态
  @override
  String? jumpRoute;
  @override
  Milestone? milestone;
// 里程碑
  @override
  int? bestDays;

  @override
  String toString() {
    return 'Continuous(days: $days, isStart: $isStart, icon: $icon, clientDynamic: $clientDynamic, resource: $resource, isFinishLesson: $isFinishLesson, isFinishLessonPopup: $isFinishLessonPopup, notStartText: $notStartText, status: $status, jumpRoute: $jumpRoute, milestone: $milestone, bestDays: $bestDays)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContinuousCopyWith<_$_Continuous> get copyWith =>
      __$$_ContinuousCopyWithImpl<_$_Continuous>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContinuousToJson(
      this,
    );
  }
}

abstract class _Continuous implements Continuous {
  factory _Continuous(
      {int? days,
      int? isStart,
      String? icon,
      String? clientDynamic,
      String? resource,
      int? isFinishLesson,
      int? isFinishLessonPopup,
      String? notStartText,
      int? status,
      String? jumpRoute,
      Milestone? milestone,
      int? bestDays}) = _$_Continuous;

  factory _Continuous.fromJson(Map<String, dynamic> json) =
      _$_Continuous.fromJson;

  @override
  int? get days;
  set days(int? value);
  @override
  int? get isStart;
  set isStart(int? value);
  @override //是否开始(1是0否)
  String? get icon; //是否开始(1是0否)
  set icon(String? value);
  @override // 连续学icon
  String? get clientDynamic; // 连续学icon
  set clientDynamic(String? value);
  @override // 动画名称
  String? get resource; // 动画名称
  set resource(String? value);
  @override // 连续学动效（如果没有返回，则显示icon）
  int? get isFinishLesson; // 连续学动效（如果没有返回，则显示icon）
  set isFinishLesson(int? value);
  @override //是否完成课程(1是0否)
  int? get isFinishLessonPopup; //是否完成课程(1是0否)
  set isFinishLessonPopup(int? value);
  @override //是否有完课后的捏脸弹窗(1是0否)
  String? get notStartText; //是否有完课后的捏脸弹窗(1是0否)
  set notStartText(String? value);
  @override //等待期文案(isStart=0)
  int? get status; //等待期文案(isStart=0)
  set status(int? value);
  @override // 学习状态
  String? get jumpRoute; // 学习状态
  set jumpRoute(String? value);
  @override
  Milestone? get milestone;
  set milestone(Milestone? value);
  @override // 里程碑
  int? get bestDays; // 里程碑
  set bestDays(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_ContinuousCopyWith<_$_Continuous> get copyWith =>
      throw _privateConstructorUsedError;
}

Dress _$DressFromJson(Map<String, dynamic> json) {
  return _Dress.fromJson(json);
}

/// @nodoc
mixin _$Dress {
  String? get img => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DressCopyWith<Dress> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DressCopyWith<$Res> {
  factory $DressCopyWith(Dress value, $Res Function(Dress) then) =
      _$DressCopyWithImpl<$Res, Dress>;
  @useResult
  $Res call({String? img, String? jumpRoute});
}

/// @nodoc
class _$DressCopyWithImpl<$Res, $Val extends Dress>
    implements $DressCopyWith<$Res> {
  _$DressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? img = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DressCopyWith<$Res> implements $DressCopyWith<$Res> {
  factory _$$_DressCopyWith(_$_Dress value, $Res Function(_$_Dress) then) =
      __$$_DressCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? img, String? jumpRoute});
}

/// @nodoc
class __$$_DressCopyWithImpl<$Res> extends _$DressCopyWithImpl<$Res, _$_Dress>
    implements _$$_DressCopyWith<$Res> {
  __$$_DressCopyWithImpl(_$_Dress _value, $Res Function(_$_Dress) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? img = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_Dress(
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Dress implements _Dress {
  const _$_Dress({this.img, this.jumpRoute});

  factory _$_Dress.fromJson(Map<String, dynamic> json) =>
      _$$_DressFromJson(json);

  @override
  final String? img;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'Dress(img: $img, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Dress &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, img, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DressCopyWith<_$_Dress> get copyWith =>
      __$$_DressCopyWithImpl<_$_Dress>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DressToJson(
      this,
    );
  }
}

abstract class _Dress implements Dress {
  const factory _Dress({final String? img, final String? jumpRoute}) = _$_Dress;

  factory _Dress.fromJson(Map<String, dynamic> json) = _$_Dress.fromJson;

  @override
  String? get img;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_DressCopyWith<_$_Dress> get copyWith =>
      throw _privateConstructorUsedError;
}

PartnerGuide _$PartnerGuideFromJson(Map<String, dynamic> json) {
  return _PartnerGuide.fromJson(json);
}

/// @nodoc
mixin _$PartnerGuide {
  String? get coverImg => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnerGuideCopyWith<PartnerGuide> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerGuideCopyWith<$Res> {
  factory $PartnerGuideCopyWith(
          PartnerGuide value, $Res Function(PartnerGuide) then) =
      _$PartnerGuideCopyWithImpl<$Res, PartnerGuide>;
  @useResult
  $Res call({String? coverImg, String? jumpRoute});
}

/// @nodoc
class _$PartnerGuideCopyWithImpl<$Res, $Val extends PartnerGuide>
    implements $PartnerGuideCopyWith<$Res> {
  _$PartnerGuideCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coverImg = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      coverImg: freezed == coverImg
          ? _value.coverImg
          : coverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerGuideCopyWith<$Res>
    implements $PartnerGuideCopyWith<$Res> {
  factory _$$_PartnerGuideCopyWith(
          _$_PartnerGuide value, $Res Function(_$_PartnerGuide) then) =
      __$$_PartnerGuideCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? coverImg, String? jumpRoute});
}

/// @nodoc
class __$$_PartnerGuideCopyWithImpl<$Res>
    extends _$PartnerGuideCopyWithImpl<$Res, _$_PartnerGuide>
    implements _$$_PartnerGuideCopyWith<$Res> {
  __$$_PartnerGuideCopyWithImpl(
      _$_PartnerGuide _value, $Res Function(_$_PartnerGuide) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coverImg = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_PartnerGuide(
      coverImg: freezed == coverImg
          ? _value.coverImg
          : coverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PartnerGuide implements _PartnerGuide {
  const _$_PartnerGuide({this.coverImg, this.jumpRoute});

  factory _$_PartnerGuide.fromJson(Map<String, dynamic> json) =>
      _$$_PartnerGuideFromJson(json);

  @override
  final String? coverImg;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'PartnerGuide(coverImg: $coverImg, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnerGuide &&
            (identical(other.coverImg, coverImg) ||
                other.coverImg == coverImg) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, coverImg, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerGuideCopyWith<_$_PartnerGuide> get copyWith =>
      __$$_PartnerGuideCopyWithImpl<_$_PartnerGuide>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnerGuideToJson(
      this,
    );
  }
}

abstract class _PartnerGuide implements PartnerGuide {
  const factory _PartnerGuide(
      {final String? coverImg, final String? jumpRoute}) = _$_PartnerGuide;

  factory _PartnerGuide.fromJson(Map<String, dynamic> json) =
      _$_PartnerGuide.fromJson;

  @override
  String? get coverImg;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerGuideCopyWith<_$_PartnerGuide> get copyWith =>
      throw _privateConstructorUsedError;
}

LearnBean _$LearnBeanFromJson(Map<String, dynamic> json) {
  return _LearnBean.fromJson(json);
}

/// @nodoc
mixin _$LearnBean {
  int? get amount => throw _privateConstructorUsedError;
  set amount(int? value) => throw _privateConstructorUsedError;
  int? get isShow => throw _privateConstructorUsedError;
  set isShow(int? value) => throw _privateConstructorUsedError; //是否显示(1是0否)
  int? get type => throw _privateConstructorUsedError; //是否显示(1是0否)
  set type(int? value) => throw _privateConstructorUsedError; //类型，对应列表的奖励领取
  String? get icon => throw _privateConstructorUsedError; //类型，对应列表的奖励领取
  set icon(String? value) => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  set jumpRoute(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearnBeanCopyWith<LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearnBeanCopyWith<$Res> {
  factory $LearnBeanCopyWith(LearnBean value, $Res Function(LearnBean) then) =
      _$LearnBeanCopyWithImpl<$Res, LearnBean>;
  @useResult
  $Res call(
      {int? amount, int? isShow, int? type, String? icon, String? jumpRoute});
}

/// @nodoc
class _$LearnBeanCopyWithImpl<$Res, $Val extends LearnBean>
    implements $LearnBeanCopyWith<$Res> {
  _$LearnBeanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? isShow = freezed,
    Object? type = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      isShow: freezed == isShow
          ? _value.isShow
          : isShow // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearnBeanCopyWith<$Res> implements $LearnBeanCopyWith<$Res> {
  factory _$$_LearnBeanCopyWith(
          _$_LearnBean value, $Res Function(_$_LearnBean) then) =
      __$$_LearnBeanCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? amount, int? isShow, int? type, String? icon, String? jumpRoute});
}

/// @nodoc
class __$$_LearnBeanCopyWithImpl<$Res>
    extends _$LearnBeanCopyWithImpl<$Res, _$_LearnBean>
    implements _$$_LearnBeanCopyWith<$Res> {
  __$$_LearnBeanCopyWithImpl(
      _$_LearnBean _value, $Res Function(_$_LearnBean) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? isShow = freezed,
    Object? type = freezed,
    Object? icon = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_LearnBean(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      isShow: freezed == isShow
          ? _value.isShow
          : isShow // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearnBean implements _LearnBean {
  _$_LearnBean(
      {this.amount, this.isShow, this.type, this.icon, this.jumpRoute});

  factory _$_LearnBean.fromJson(Map<String, dynamic> json) =>
      _$$_LearnBeanFromJson(json);

  @override
  int? amount;
  @override
  int? isShow;
//是否显示(1是0否)
  @override
  int? type;
//类型，对应列表的奖励领取
  @override
  String? icon;
  @override
  String? jumpRoute;

  @override
  String toString() {
    return 'LearnBean(amount: $amount, isShow: $isShow, type: $type, icon: $icon, jumpRoute: $jumpRoute)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      __$$_LearnBeanCopyWithImpl<_$_LearnBean>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearnBeanToJson(
      this,
    );
  }
}

abstract class _LearnBean implements LearnBean {
  factory _LearnBean(
      {int? amount,
      int? isShow,
      int? type,
      String? icon,
      String? jumpRoute}) = _$_LearnBean;

  factory _LearnBean.fromJson(Map<String, dynamic> json) =
      _$_LearnBean.fromJson;

  @override
  int? get amount;
  set amount(int? value);
  @override
  int? get isShow;
  set isShow(int? value);
  @override //是否显示(1是0否)
  int? get type; //是否显示(1是0否)
  set type(int? value);
  @override //类型，对应列表的奖励领取
  String? get icon; //类型，对应列表的奖励领取
  set icon(String? value);
  @override
  String? get jumpRoute;
  set jumpRoute(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_LearnBeanCopyWith<_$_LearnBean> get copyWith =>
      throw _privateConstructorUsedError;
}

Milestone _$MilestoneFromJson(Map<String, dynamic> json) {
  return _Milestone.fromJson(json);
}

/// @nodoc
mixin _$Milestone {
  String? get progressText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneCopyWith<Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneCopyWith<$Res> {
  factory $MilestoneCopyWith(Milestone value, $Res Function(Milestone) then) =
      _$MilestoneCopyWithImpl<$Res, Milestone>;
  @useResult
  $Res call({String? progressText});
}

/// @nodoc
class _$MilestoneCopyWithImpl<$Res, $Val extends Milestone>
    implements $MilestoneCopyWith<$Res> {
  _$MilestoneCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressText = freezed,
  }) {
    return _then(_value.copyWith(
      progressText: freezed == progressText
          ? _value.progressText
          : progressText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneCopyWith<$Res> implements $MilestoneCopyWith<$Res> {
  factory _$$_MilestoneCopyWith(
          _$_Milestone value, $Res Function(_$_Milestone) then) =
      __$$_MilestoneCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? progressText});
}

/// @nodoc
class __$$_MilestoneCopyWithImpl<$Res>
    extends _$MilestoneCopyWithImpl<$Res, _$_Milestone>
    implements _$$_MilestoneCopyWith<$Res> {
  __$$_MilestoneCopyWithImpl(
      _$_Milestone _value, $Res Function(_$_Milestone) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressText = freezed,
  }) {
    return _then(_$_Milestone(
      progressText: freezed == progressText
          ? _value.progressText
          : progressText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Milestone implements _Milestone {
  const _$_Milestone({this.progressText});

  factory _$_Milestone.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneFromJson(json);

  @override
  final String? progressText;

  @override
  String toString() {
    return 'Milestone(progressText: $progressText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Milestone &&
            (identical(other.progressText, progressText) ||
                other.progressText == progressText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, progressText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      __$$_MilestoneCopyWithImpl<_$_Milestone>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneToJson(
      this,
    );
  }
}

abstract class _Milestone implements Milestone {
  const factory _Milestone({final String? progressText}) = _$_Milestone;

  factory _Milestone.fromJson(Map<String, dynamic> json) =
      _$_Milestone.fromJson;

  @override
  String? get progressText;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

NewDressUpNotice _$NewDressUpNoticeFromJson(Map<String, dynamic> json) {
  return _NewDressUpNotice.fromJson(json);
}

/// @nodoc
mixin _$NewDressUpNotice {
  int? get newDressUp => throw _privateConstructorUsedError;
  set newDressUp(int? value) => throw _privateConstructorUsedError;
  String? get newDressUpIcon => throw _privateConstructorUsedError;
  set newDressUpIcon(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NewDressUpNoticeCopyWith<NewDressUpNotice> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NewDressUpNoticeCopyWith<$Res> {
  factory $NewDressUpNoticeCopyWith(
          NewDressUpNotice value, $Res Function(NewDressUpNotice) then) =
      _$NewDressUpNoticeCopyWithImpl<$Res, NewDressUpNotice>;
  @useResult
  $Res call({int? newDressUp, String? newDressUpIcon});
}

/// @nodoc
class _$NewDressUpNoticeCopyWithImpl<$Res, $Val extends NewDressUpNotice>
    implements $NewDressUpNoticeCopyWith<$Res> {
  _$NewDressUpNoticeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newDressUp = freezed,
    Object? newDressUpIcon = freezed,
  }) {
    return _then(_value.copyWith(
      newDressUp: freezed == newDressUp
          ? _value.newDressUp
          : newDressUp // ignore: cast_nullable_to_non_nullable
              as int?,
      newDressUpIcon: freezed == newDressUpIcon
          ? _value.newDressUpIcon
          : newDressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_NewDressUpNoticeCopyWith<$Res>
    implements $NewDressUpNoticeCopyWith<$Res> {
  factory _$$_NewDressUpNoticeCopyWith(
          _$_NewDressUpNotice value, $Res Function(_$_NewDressUpNotice) then) =
      __$$_NewDressUpNoticeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? newDressUp, String? newDressUpIcon});
}

/// @nodoc
class __$$_NewDressUpNoticeCopyWithImpl<$Res>
    extends _$NewDressUpNoticeCopyWithImpl<$Res, _$_NewDressUpNotice>
    implements _$$_NewDressUpNoticeCopyWith<$Res> {
  __$$_NewDressUpNoticeCopyWithImpl(
      _$_NewDressUpNotice _value, $Res Function(_$_NewDressUpNotice) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newDressUp = freezed,
    Object? newDressUpIcon = freezed,
  }) {
    return _then(_$_NewDressUpNotice(
      newDressUp: freezed == newDressUp
          ? _value.newDressUp
          : newDressUp // ignore: cast_nullable_to_non_nullable
              as int?,
      newDressUpIcon: freezed == newDressUpIcon
          ? _value.newDressUpIcon
          : newDressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_NewDressUpNotice implements _NewDressUpNotice {
  _$_NewDressUpNotice({this.newDressUp, this.newDressUpIcon});

  factory _$_NewDressUpNotice.fromJson(Map<String, dynamic> json) =>
      _$$_NewDressUpNoticeFromJson(json);

  @override
  int? newDressUp;
  @override
  String? newDressUpIcon;

  @override
  String toString() {
    return 'NewDressUpNotice(newDressUp: $newDressUp, newDressUpIcon: $newDressUpIcon)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_NewDressUpNoticeCopyWith<_$_NewDressUpNotice> get copyWith =>
      __$$_NewDressUpNoticeCopyWithImpl<_$_NewDressUpNotice>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_NewDressUpNoticeToJson(
      this,
    );
  }
}

abstract class _NewDressUpNotice implements NewDressUpNotice {
  factory _NewDressUpNotice({int? newDressUp, String? newDressUpIcon}) =
      _$_NewDressUpNotice;

  factory _NewDressUpNotice.fromJson(Map<String, dynamic> json) =
      _$_NewDressUpNotice.fromJson;

  @override
  int? get newDressUp;
  set newDressUp(int? value);
  @override
  String? get newDressUpIcon;
  set newDressUpIcon(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_NewDressUpNoticeCopyWith<_$_NewDressUpNotice> get copyWith =>
      throw _privateConstructorUsedError;
}
