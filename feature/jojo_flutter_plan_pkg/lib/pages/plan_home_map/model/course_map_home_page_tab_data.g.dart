// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_map_home_page_tab_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseSubjectTabData _$$_CourseSubjectTabDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseSubjectTabData(
      subjectClassList: (json['subjectClassList'] as List<dynamic>?)
          ?.map((e) => SubjectClassList.fromJson(e as Map<String, dynamic>))
          .toList(),
      subjectList: (json['subjectList'] as List<dynamic>?)
          ?.map((e) => SubjectList.fromJson(e as Map<String, dynamic>))
          .toList(),
      popupList: (json['popupList'] as List<dynamic>?)
          ?.map((e) => ClassPagePopupList.fromJson(e as Map<String, dynamic>))
          .toList(),
      subjectExtendVo: json['subjectExtendVo'] == null
          ? null
          : SubjectExtendVo.fromJson(
              json['subjectExtendVo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseSubjectTabDataToJson(
        _$_CourseSubjectTabData instance) =>
    <String, dynamic>{
      'subjectClassList': instance.subjectClassList,
      'subjectList': instance.subjectList,
      'popupList': instance.popupList,
      'subjectExtendVo': instance.subjectExtendVo,
    };

_$_AfterServerEntranceVo _$$_AfterServerEntranceVoFromJson(
        Map<String, dynamic> json) =>
    _$_AfterServerEntranceVo(
      title: json['title'] as String?,
      icon: json['icon'] as String?,
      route: json['route'] as String?,
      toastList: (json['toastList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      configKey: json['configKey'] as String?,
    );

Map<String, dynamic> _$$_AfterServerEntranceVoToJson(
        _$_AfterServerEntranceVo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'icon': instance.icon,
      'route': instance.route,
      'toastList': instance.toastList,
      'configKey': instance.configKey,
    };

_$_ClassPagePopupList _$$_ClassPagePopupListFromJson(
        Map<String, dynamic> json) =>
    _$_ClassPagePopupList(
      showMode: json['showMode'] as String?,
      bgColor: json['bgColor'] as String?,
      popupType: json['popupType'] as int?,
      occurrenceIndex: json['occurrenceIndex'] as int?,
      popupTitle: json['popupTitle'] as String?,
      buttonText: json['buttonText'] as String?,
      cancelButtonText: json['cancelButtonText'] as String?,
      teacherProfileUrl: json['teacherProfileUrl'] as String?,
      addTeacherUrl: json['addTeacherUrl'] as String?,
      teacherName: json['teacherName'] as String?,
      subTitle: json['subTitle'] as String?,
      classId: json['classId'] as int?,
      icon: json['icon'] as String?,
      userCourseBusinessStatus: json['userCourseBusinessStatus'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      template: json['template'] as int?,
      pattern: json['pattern'] as int?,
      courseSegment: json['courseSegment'] as String?,
      courseKey: json['courseKey'] as String?,
      coursePopupInfoList: (json['coursePopupInfoList'] as List<dynamic>?)
          ?.map((e) => ClassPagePopupItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      limitFlow: json['limitFlow'] as bool?,
      businessTypeDesc: json['businessTypeDesc'] as String?,
    );

Map<String, dynamic> _$$_ClassPagePopupListToJson(
        _$_ClassPagePopupList instance) =>
    <String, dynamic>{
      'showMode': instance.showMode,
      'bgColor': instance.bgColor,
      'popupType': instance.popupType,
      'occurrenceIndex': instance.occurrenceIndex,
      'popupTitle': instance.popupTitle,
      'buttonText': instance.buttonText,
      'cancelButtonText': instance.cancelButtonText,
      'teacherProfileUrl': instance.teacherProfileUrl,
      'addTeacherUrl': instance.addTeacherUrl,
      'teacherName': instance.teacherName,
      'subTitle': instance.subTitle,
      'classId': instance.classId,
      'icon': instance.icon,
      'userCourseBusinessStatus': instance.userCourseBusinessStatus,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'tags': instance.tags,
      'template': instance.template,
      'pattern': instance.pattern,
      'courseSegment': instance.courseSegment,
      'courseKey': instance.courseKey,
      'coursePopupInfoList': instance.coursePopupInfoList,
      'limitFlow': instance.limitFlow,
      'businessTypeDesc': instance.businessTypeDesc,
    };

_$_ClassPagePopupItem _$$_ClassPagePopupItemFromJson(
        Map<String, dynamic> json) =>
    _$_ClassPagePopupItem(
      courseLabel: json['courseLabel'] as String?,
      courseIcon: json['courseIcon'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      subjectType: json['subjectType'] as int?,
    );

Map<String, dynamic> _$$_ClassPagePopupItemToJson(
        _$_ClassPagePopupItem instance) =>
    <String, dynamic>{
      'courseLabel': instance.courseLabel,
      'courseIcon': instance.courseIcon,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'subjectType': instance.subjectType,
    };

_$_SubjectClassList _$$_SubjectClassListFromJson(Map<String, dynamic> json) =>
    _$_SubjectClassList(
      subjectType: json['subjectType'] as int?,
      selected: json['selected'] as bool?,
      subjectStatus: json['subjectStatus'] as int?,
      bubbleList: (json['bubbleList'] as List<dynamic>?)
          ?.map((e) => BubbleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      userClassVoList: (json['userClassVoList'] as List<dynamic>?)
          ?.map((e) => UserClassVoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      own: json['own'] as bool?,
      specialCourseVo: json['specialCourseVo'] == null
          ? null
          : SpecialCourseVo.fromJson(
              json['specialCourseVo'] as Map<String, dynamic>),
      afterServerEntranceVo: json['afterServerEntranceVo'] == null
          ? null
          : AfterServerEntranceVo.fromJson(
              json['afterServerEntranceVo'] as Map<String, dynamic>),
      giftCourseVo: json['giftCourseVo'] == null
          ? null
          : GiftCourseVo.fromJson(json['giftCourseVo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_SubjectClassListToJson(_$_SubjectClassList instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'selected': instance.selected,
      'subjectStatus': instance.subjectStatus,
      'bubbleList': instance.bubbleList,
      'userClassVoList': instance.userClassVoList,
      'own': instance.own,
      'specialCourseVo': instance.specialCourseVo,
      'afterServerEntranceVo': instance.afterServerEntranceVo,
      'giftCourseVo': instance.giftCourseVo,
    };

_$_BubbleList _$$_BubbleListFromJson(Map<String, dynamic> json) =>
    _$_BubbleList(
      bubbleType: json['bubbleType'] as int?,
      bubble: json['bubble'] as String?,
      callBackType: json['callBackType'] as int?,
      classIdList: (json['classIdList'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
    );

Map<String, dynamic> _$$_BubbleListToJson(_$_BubbleList instance) =>
    <String, dynamic>{
      'bubbleType': instance.bubbleType,
      'bubble': instance.bubble,
      'callBackType': instance.callBackType,
      'classIdList': instance.classIdList,
    };

_$_SpecialCourseVo _$$_SpecialCourseVoFromJson(Map<String, dynamic> json) =>
    _$_SpecialCourseVo(
      tabTitle: json['tabTitle'] as String?,
      courseInfoList: (json['courseInfoList'] as List<dynamic>?)
          ?.map((e) => CourseInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      bufferDesc: json['bufferDesc'] as String?,
    );

Map<String, dynamic> _$$_SpecialCourseVoToJson(_$_SpecialCourseVo instance) =>
    <String, dynamic>{
      'tabTitle': instance.tabTitle,
      'courseInfoList': instance.courseInfoList,
      'bufferDesc': instance.bufferDesc,
    };

_$_GiftCourseVo _$$_GiftCourseVoFromJson(Map<String, dynamic> json) =>
    _$_GiftCourseVo(
      tabTitle: json['tabTitle'] as String?,
      courseInfoList: (json['courseInfoList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CourseInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_GiftCourseVoToJson(_$_GiftCourseVo instance) =>
    <String, dynamic>{
      'tabTitle': instance.tabTitle,
      'courseInfoList': instance.courseInfoList,
    };

_$_CourseInfoList _$$_CourseInfoListFromJson(Map<String, dynamic> json) =>
    _$_CourseInfoList(
      courseName: json['courseName'] as String?,
      courseIcon: json['courseIcon'] as String?,
      finishLessonNum: json['finishLessonNum'] as int?,
      totalLessonNum: json['totalLessonNum'] as int?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      route: json['route'] as String?,
      color: json['color'] as String?,
      subjectType: json['subjectType'] as int?,
      mainClassStatus: json['mainClassStatus'] as int?,
      mainCourseName: json['mainCourseName'] as String?,
      courseSegment: json['courseSegment'] as String?,
      courseType: json['courseType'] as int?,
      subjectTitle: json['subjectTitle'] as String?,
      verticalCoverImg: json['verticalCoverImg'] as String?,
      landscapeCoverImg: json['landscapeCoverImg'] as String?,
    );

Map<String, dynamic> _$$_CourseInfoListToJson(_$_CourseInfoList instance) =>
    <String, dynamic>{
      'courseName': instance.courseName,
      'courseIcon': instance.courseIcon,
      'finishLessonNum': instance.finishLessonNum,
      'totalLessonNum': instance.totalLessonNum,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'route': instance.route,
      'color': instance.color,
      'subjectType': instance.subjectType,
      'mainClassStatus': instance.mainClassStatus,
      'mainCourseName': instance.mainCourseName,
      'courseSegment': instance.courseSegment,
      'courseType': instance.courseType,
      'subjectTitle': instance.subjectTitle,
      'verticalCoverImg': instance.verticalCoverImg,
      'landscapeCoverImg': instance.landscapeCoverImg,
    };

_$_UserClassVoList _$$_UserClassVoListFromJson(Map<String, dynamic> json) =>
    _$_UserClassVoList(
      tabTitle: json['tabTitle'] as String?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      classStatus: json['classStatus'] as int?,
      bubbleList: (json['bubbleList'] as List<dynamic>?)
          ?.map((e) => BubbleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      startClassTime: json['startClassTime'] as int?,
      courseSegment: json['courseSegment'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
    );

Map<String, dynamic> _$$_UserClassVoListToJson(_$_UserClassVoList instance) =>
    <String, dynamic>{
      'tabTitle': instance.tabTitle,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'classStatus': instance.classStatus,
      'bubbleList': instance.bubbleList,
      'startClassTime': instance.startClassTime,
      'courseSegment': instance.courseSegment,
      'courseType': instance.courseType,
      'courseSegmentCode': instance.courseSegmentCode,
    };

_$_SubjectExtendVo _$$_SubjectExtendVoFromJson(Map<String, dynamic> json) =>
    _$_SubjectExtendVo(
      bufferText: json['bufferText'] as String?,
      bufferRouter: json['bufferRouter'] as String?,
      noServiceText: json['noServiceText'] as String?,
      noServiceTitle: json['noServiceTitle'] as String?,
      noServiceRouter: json['noServiceRouter'] as String?,
      buttonText: json['buttonText'] as String?,
    );

Map<String, dynamic> _$$_SubjectExtendVoToJson(_$_SubjectExtendVo instance) =>
    <String, dynamic>{
      'bufferText': instance.bufferText,
      'bufferRouter': instance.bufferRouter,
      'noServiceText': instance.noServiceText,
      'noServiceTitle': instance.noServiceTitle,
      'noServiceRouter': instance.noServiceRouter,
      'buttonText': instance.buttonText,
    };

_$_SubjectList _$$_SubjectListFromJson(Map<String, dynamic> json) =>
    _$_SubjectList(
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      subjectColor: json['subjectColor'] as String?,
      loadingScene: json['loadingScene'] as int?,
    );

Map<String, dynamic> _$$_SubjectListToJson(_$_SubjectList instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'subjectColor': instance.subjectColor,
      'loadingScene': instance.loadingScene,
    };
