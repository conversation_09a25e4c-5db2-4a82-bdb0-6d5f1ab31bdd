// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_after_ads_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseAfterAdsInfoData _$$_CourseAfterAdsInfoDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseAfterAdsInfoData(
      advertisementList: (json['advertisementList'] as List<dynamic>?)
          ?.map((e) => AdvertisementList.fromJson(e as Map<String, dynamic>))
          .toList(),
      scene: json['scene'] as String?,
    );

Map<String, dynamic> _$$_CourseAfterAdsInfoDataToJson(
        _$_CourseAfterAdsInfoData instance) =>
    <String, dynamic>{
      'advertisementList': instance.advertisementList,
      'scene': instance.scene,
    };

_$_AdvertisementList _$$_AdvertisementListFromJson(Map<String, dynamic> json) =>
    _$_AdvertisementList(
      advertisementCopy: json['advertisementCopy'] as String?,
      advertisementId: json['advertisementId'] as int?,
      advertisementName: json['advertisementName'] as String?,
      advertisementPosition: json['advertisementPosition'] as String?,
      businessTagId: json['businessTagId'] as String?,
      closeReasonList: (json['closeReasonList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      grayscaleList: (json['grayscaleList'] as List<dynamic>?)
          ?.map((e) => GrayscaleList.fromJson(e as Map<String, dynamic>))
          .toList(),
      id: json['id'] as int?,
      linkUrl: json['linkUrl'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
    );

Map<String, dynamic> _$$_AdvertisementListToJson(
        _$_AdvertisementList instance) =>
    <String, dynamic>{
      'advertisementCopy': instance.advertisementCopy,
      'advertisementId': instance.advertisementId,
      'advertisementName': instance.advertisementName,
      'advertisementPosition': instance.advertisementPosition,
      'businessTagId': instance.businessTagId,
      'closeReasonList': instance.closeReasonList,
      'grayscaleList': instance.grayscaleList,
      'id': instance.id,
      'linkUrl': instance.linkUrl,
      'pictureUrl': instance.pictureUrl,
    };

_$_GrayscaleList _$$_GrayscaleListFromJson(Map<String, dynamic> json) =>
    _$_GrayscaleList(
      grayCover: json['grayCover'] as bool?,
      grayHit: json['grayHit'] as bool?,
    );

Map<String, dynamic> _$$_GrayscaleListToJson(_$_GrayscaleList instance) =>
    <String, dynamic>{
      'grayCover': instance.grayCover,
      'grayHit': instance.grayHit,
    };
