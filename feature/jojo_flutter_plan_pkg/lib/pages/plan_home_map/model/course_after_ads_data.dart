import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'course_after_ads_data.freezed.dart';
part 'course_after_ads_data.g.dart';

@freezed
class CourseAfterAdsInfoData with _$CourseAfterAdsInfoData {
  const factory CourseAfterAdsInfoData({
    List<AdvertisementList>? advertisementList,
    String? scene,
  }) = _CourseAfterAdsInfoData;

  factory CourseAfterAdsInfoData.fromJson(Map<String, dynamic> json) =>
      _$CourseAfterAdsInfoDataFromJson(json);
}

@freezed
class AdvertisementList with _$AdvertisementList {
  const factory AdvertisementList({
    String? advertisementCopy,
    int? advertisementId,
    String? advertisementName,
    String? advertisementPosition,
    String? businessTagId,
    List<String>? closeReasonList,
    List<GrayscaleList>? grayscaleList,
    int? id,
    String? linkUrl,
    String? pictureUrl,
  }) = _AdvertisementList;

  factory AdvertisementList.fromJson(Map<String, dynamic> json) =>
      _$AdvertisementListFromJson(json);
}

@freezed
class GrayscaleList with _$GrayscaleList {
  const factory GrayscaleList({
    bool? grayCover,
    bool? grayHit,
  }) = _GrayscaleList;

  factory GrayscaleList.fromJson(Map<String, dynamic> json) =>
      _$GrayscaleListFromJson(json);
}
