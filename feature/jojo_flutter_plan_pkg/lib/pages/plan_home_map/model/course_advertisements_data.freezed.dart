// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_advertisements_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseAdvertisementsData _$CourseAdvertisementsDataFromJson(
    Map<String, dynamic> json) {
  return _CourseAdvertisementsData.fromJson(json);
}

/// @nodoc
mixin _$CourseAdvertisementsData {
  AdvertisementsReceiveCourseInfoData? get receiveCourseInfo =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseAdvertisementsDataCopyWith<CourseAdvertisementsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseAdvertisementsDataCopyWith<$Res> {
  factory $CourseAdvertisementsDataCopyWith(CourseAdvertisementsData value,
          $Res Function(CourseAdvertisementsData) then) =
      _$CourseAdvertisementsDataCopyWithImpl<$Res, CourseAdvertisementsData>;
  @useResult
  $Res call({AdvertisementsReceiveCourseInfoData? receiveCourseInfo});

  $AdvertisementsReceiveCourseInfoDataCopyWith<$Res>? get receiveCourseInfo;
}

/// @nodoc
class _$CourseAdvertisementsDataCopyWithImpl<$Res,
        $Val extends CourseAdvertisementsData>
    implements $CourseAdvertisementsDataCopyWith<$Res> {
  _$CourseAdvertisementsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? receiveCourseInfo = freezed,
  }) {
    return _then(_value.copyWith(
      receiveCourseInfo: freezed == receiveCourseInfo
          ? _value.receiveCourseInfo
          : receiveCourseInfo // ignore: cast_nullable_to_non_nullable
              as AdvertisementsReceiveCourseInfoData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AdvertisementsReceiveCourseInfoDataCopyWith<$Res>? get receiveCourseInfo {
    if (_value.receiveCourseInfo == null) {
      return null;
    }

    return $AdvertisementsReceiveCourseInfoDataCopyWith<$Res>(
        _value.receiveCourseInfo!, (value) {
      return _then(_value.copyWith(receiveCourseInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseAdvertisementsDataCopyWith<$Res>
    implements $CourseAdvertisementsDataCopyWith<$Res> {
  factory _$$_CourseAdvertisementsDataCopyWith(
          _$_CourseAdvertisementsData value,
          $Res Function(_$_CourseAdvertisementsData) then) =
      __$$_CourseAdvertisementsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AdvertisementsReceiveCourseInfoData? receiveCourseInfo});

  @override
  $AdvertisementsReceiveCourseInfoDataCopyWith<$Res>? get receiveCourseInfo;
}

/// @nodoc
class __$$_CourseAdvertisementsDataCopyWithImpl<$Res>
    extends _$CourseAdvertisementsDataCopyWithImpl<$Res,
        _$_CourseAdvertisementsData>
    implements _$$_CourseAdvertisementsDataCopyWith<$Res> {
  __$$_CourseAdvertisementsDataCopyWithImpl(_$_CourseAdvertisementsData _value,
      $Res Function(_$_CourseAdvertisementsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? receiveCourseInfo = freezed,
  }) {
    return _then(_$_CourseAdvertisementsData(
      receiveCourseInfo: freezed == receiveCourseInfo
          ? _value.receiveCourseInfo
          : receiveCourseInfo // ignore: cast_nullable_to_non_nullable
              as AdvertisementsReceiveCourseInfoData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseAdvertisementsData implements _CourseAdvertisementsData {
  const _$_CourseAdvertisementsData({this.receiveCourseInfo});

  factory _$_CourseAdvertisementsData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseAdvertisementsDataFromJson(json);

  @override
  final AdvertisementsReceiveCourseInfoData? receiveCourseInfo;

  @override
  String toString() {
    return 'CourseAdvertisementsData(receiveCourseInfo: $receiveCourseInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseAdvertisementsData &&
            (identical(other.receiveCourseInfo, receiveCourseInfo) ||
                other.receiveCourseInfo == receiveCourseInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, receiveCourseInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseAdvertisementsDataCopyWith<_$_CourseAdvertisementsData>
      get copyWith => __$$_CourseAdvertisementsDataCopyWithImpl<
          _$_CourseAdvertisementsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseAdvertisementsDataToJson(
      this,
    );
  }
}

abstract class _CourseAdvertisementsData implements CourseAdvertisementsData {
  const factory _CourseAdvertisementsData(
          {final AdvertisementsReceiveCourseInfoData? receiveCourseInfo}) =
      _$_CourseAdvertisementsData;

  factory _CourseAdvertisementsData.fromJson(Map<String, dynamic> json) =
      _$_CourseAdvertisementsData.fromJson;

  @override
  AdvertisementsReceiveCourseInfoData? get receiveCourseInfo;
  @override
  @JsonKey(ignore: true)
  _$$_CourseAdvertisementsDataCopyWith<_$_CourseAdvertisementsData>
      get copyWith => throw _privateConstructorUsedError;
}

AdvertisementsReceiveCourseInfoData
    _$AdvertisementsReceiveCourseInfoDataFromJson(Map<String, dynamic> json) {
  return _AdvertisementsReceiveCourseInfoData.fromJson(json);
}

/// @nodoc
mixin _$AdvertisementsReceiveCourseInfoData {
  int? get subjectId => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  String? get moreSubjectLinkUrl => throw _privateConstructorUsedError;
  List<AdvertisementsMaterialsData>? get materials =>
      throw _privateConstructorUsedError;
  String? get mainColor => throw _privateConstructorUsedError;
  bool? get showMoreSubject => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdvertisementsReceiveCourseInfoDataCopyWith<
          AdvertisementsReceiveCourseInfoData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvertisementsReceiveCourseInfoDataCopyWith<$Res> {
  factory $AdvertisementsReceiveCourseInfoDataCopyWith(
          AdvertisementsReceiveCourseInfoData value,
          $Res Function(AdvertisementsReceiveCourseInfoData) then) =
      _$AdvertisementsReceiveCourseInfoDataCopyWithImpl<$Res,
          AdvertisementsReceiveCourseInfoData>;
  @useResult
  $Res call(
      {int? subjectId,
      String? subjectName,
      String? moreSubjectLinkUrl,
      List<AdvertisementsMaterialsData>? materials,
      String? mainColor,
      bool? showMoreSubject});
}

/// @nodoc
class _$AdvertisementsReceiveCourseInfoDataCopyWithImpl<$Res,
        $Val extends AdvertisementsReceiveCourseInfoData>
    implements $AdvertisementsReceiveCourseInfoDataCopyWith<$Res> {
  _$AdvertisementsReceiveCourseInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectId = freezed,
    Object? subjectName = freezed,
    Object? moreSubjectLinkUrl = freezed,
    Object? materials = freezed,
    Object? mainColor = freezed,
    Object? showMoreSubject = freezed,
  }) {
    return _then(_value.copyWith(
      subjectId: freezed == subjectId
          ? _value.subjectId
          : subjectId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      moreSubjectLinkUrl: freezed == moreSubjectLinkUrl
          ? _value.moreSubjectLinkUrl
          : moreSubjectLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementsMaterialsData>?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      showMoreSubject: freezed == showMoreSubject
          ? _value.showMoreSubject
          : showMoreSubject // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdvertisementsReceiveCourseInfoDataCopyWith<$Res>
    implements $AdvertisementsReceiveCourseInfoDataCopyWith<$Res> {
  factory _$$_AdvertisementsReceiveCourseInfoDataCopyWith(
          _$_AdvertisementsReceiveCourseInfoData value,
          $Res Function(_$_AdvertisementsReceiveCourseInfoData) then) =
      __$$_AdvertisementsReceiveCourseInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectId,
      String? subjectName,
      String? moreSubjectLinkUrl,
      List<AdvertisementsMaterialsData>? materials,
      String? mainColor,
      bool? showMoreSubject});
}

/// @nodoc
class __$$_AdvertisementsReceiveCourseInfoDataCopyWithImpl<$Res>
    extends _$AdvertisementsReceiveCourseInfoDataCopyWithImpl<$Res,
        _$_AdvertisementsReceiveCourseInfoData>
    implements _$$_AdvertisementsReceiveCourseInfoDataCopyWith<$Res> {
  __$$_AdvertisementsReceiveCourseInfoDataCopyWithImpl(
      _$_AdvertisementsReceiveCourseInfoData _value,
      $Res Function(_$_AdvertisementsReceiveCourseInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectId = freezed,
    Object? subjectName = freezed,
    Object? moreSubjectLinkUrl = freezed,
    Object? materials = freezed,
    Object? mainColor = freezed,
    Object? showMoreSubject = freezed,
  }) {
    return _then(_$_AdvertisementsReceiveCourseInfoData(
      subjectId: freezed == subjectId
          ? _value.subjectId
          : subjectId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      moreSubjectLinkUrl: freezed == moreSubjectLinkUrl
          ? _value.moreSubjectLinkUrl
          : moreSubjectLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementsMaterialsData>?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      showMoreSubject: freezed == showMoreSubject
          ? _value.showMoreSubject
          : showMoreSubject // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdvertisementsReceiveCourseInfoData
    implements _AdvertisementsReceiveCourseInfoData {
  const _$_AdvertisementsReceiveCourseInfoData(
      {this.subjectId,
      this.subjectName,
      this.moreSubjectLinkUrl,
      final List<AdvertisementsMaterialsData>? materials,
      this.mainColor,
      this.showMoreSubject})
      : _materials = materials;

  factory _$_AdvertisementsReceiveCourseInfoData.fromJson(
          Map<String, dynamic> json) =>
      _$$_AdvertisementsReceiveCourseInfoDataFromJson(json);

  @override
  final int? subjectId;
  @override
  final String? subjectName;
  @override
  final String? moreSubjectLinkUrl;
  final List<AdvertisementsMaterialsData>? _materials;
  @override
  List<AdvertisementsMaterialsData>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? mainColor;
  @override
  final bool? showMoreSubject;

  @override
  String toString() {
    return 'AdvertisementsReceiveCourseInfoData(subjectId: $subjectId, subjectName: $subjectName, moreSubjectLinkUrl: $moreSubjectLinkUrl, materials: $materials, mainColor: $mainColor, showMoreSubject: $showMoreSubject)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdvertisementsReceiveCourseInfoData &&
            (identical(other.subjectId, subjectId) ||
                other.subjectId == subjectId) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.moreSubjectLinkUrl, moreSubjectLinkUrl) ||
                other.moreSubjectLinkUrl == moreSubjectLinkUrl) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            (identical(other.mainColor, mainColor) ||
                other.mainColor == mainColor) &&
            (identical(other.showMoreSubject, showMoreSubject) ||
                other.showMoreSubject == showMoreSubject));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectId,
      subjectName,
      moreSubjectLinkUrl,
      const DeepCollectionEquality().hash(_materials),
      mainColor,
      showMoreSubject);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdvertisementsReceiveCourseInfoDataCopyWith<
          _$_AdvertisementsReceiveCourseInfoData>
      get copyWith => __$$_AdvertisementsReceiveCourseInfoDataCopyWithImpl<
          _$_AdvertisementsReceiveCourseInfoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdvertisementsReceiveCourseInfoDataToJson(
      this,
    );
  }
}

abstract class _AdvertisementsReceiveCourseInfoData
    implements AdvertisementsReceiveCourseInfoData {
  const factory _AdvertisementsReceiveCourseInfoData(
      {final int? subjectId,
      final String? subjectName,
      final String? moreSubjectLinkUrl,
      final List<AdvertisementsMaterialsData>? materials,
      final String? mainColor,
      final bool? showMoreSubject}) = _$_AdvertisementsReceiveCourseInfoData;

  factory _AdvertisementsReceiveCourseInfoData.fromJson(
          Map<String, dynamic> json) =
      _$_AdvertisementsReceiveCourseInfoData.fromJson;

  @override
  int? get subjectId;
  @override
  String? get subjectName;
  @override
  String? get moreSubjectLinkUrl;
  @override
  List<AdvertisementsMaterialsData>? get materials;
  @override
  String? get mainColor;
  @override
  bool? get showMoreSubject;
  @override
  @JsonKey(ignore: true)
  _$$_AdvertisementsReceiveCourseInfoDataCopyWith<
          _$_AdvertisementsReceiveCourseInfoData>
      get copyWith => throw _privateConstructorUsedError;
}

AdvertisementsMaterialsData _$AdvertisementsMaterialsDataFromJson(
    Map<String, dynamic> json) {
  return _AdvertisementsMaterialsData.fromJson(json);
}

/// @nodoc
mixin _$AdvertisementsMaterialsData {
  int? get configId => throw _privateConstructorUsedError;
  String? get configName => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;
  int? get materialId => throw _privateConstructorUsedError;
  int? get subjectId => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  bool? get recommend => throw _privateConstructorUsedError;
  String? get mainColor => throw _privateConstructorUsedError;
  String? get popupText => throw _privateConstructorUsedError;
  int? get resLibId => throw _privateConstructorUsedError;
  int? get resourceId => throw _privateConstructorUsedError;
  List<AdvertisementsMaterialDetailData>? get materialDetails =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdvertisementsMaterialsDataCopyWith<AdvertisementsMaterialsData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvertisementsMaterialsDataCopyWith<$Res> {
  factory $AdvertisementsMaterialsDataCopyWith(
          AdvertisementsMaterialsData value,
          $Res Function(AdvertisementsMaterialsData) then) =
      _$AdvertisementsMaterialsDataCopyWithImpl<$Res,
          AdvertisementsMaterialsData>;
  @useResult
  $Res call(
      {int? configId,
      String? configName,
      String? businessTagId,
      int? materialId,
      int? subjectId,
      String? subjectName,
      String? pictureUrl,
      String? buttonText,
      String? linkUrl,
      String? description,
      String? title,
      bool? recommend,
      String? mainColor,
      String? popupText,
      int? resLibId,
      int? resourceId,
      List<AdvertisementsMaterialDetailData>? materialDetails});
}

/// @nodoc
class _$AdvertisementsMaterialsDataCopyWithImpl<$Res,
        $Val extends AdvertisementsMaterialsData>
    implements $AdvertisementsMaterialsDataCopyWith<$Res> {
  _$AdvertisementsMaterialsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? configId = freezed,
    Object? configName = freezed,
    Object? businessTagId = freezed,
    Object? materialId = freezed,
    Object? subjectId = freezed,
    Object? subjectName = freezed,
    Object? pictureUrl = freezed,
    Object? buttonText = freezed,
    Object? linkUrl = freezed,
    Object? description = freezed,
    Object? title = freezed,
    Object? recommend = freezed,
    Object? mainColor = freezed,
    Object? popupText = freezed,
    Object? resLibId = freezed,
    Object? resourceId = freezed,
    Object? materialDetails = freezed,
  }) {
    return _then(_value.copyWith(
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectId: freezed == subjectId
          ? _value.subjectId
          : subjectId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      recommend: freezed == recommend
          ? _value.recommend
          : recommend // ignore: cast_nullable_to_non_nullable
              as bool?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      popupText: freezed == popupText
          ? _value.popupText
          : popupText // ignore: cast_nullable_to_non_nullable
              as String?,
      resLibId: freezed == resLibId
          ? _value.resLibId
          : resLibId // ignore: cast_nullable_to_non_nullable
              as int?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as int?,
      materialDetails: freezed == materialDetails
          ? _value.materialDetails
          : materialDetails // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementsMaterialDetailData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdvertisementsMaterialsDataCopyWith<$Res>
    implements $AdvertisementsMaterialsDataCopyWith<$Res> {
  factory _$$_AdvertisementsMaterialsDataCopyWith(
          _$_AdvertisementsMaterialsData value,
          $Res Function(_$_AdvertisementsMaterialsData) then) =
      __$$_AdvertisementsMaterialsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? configId,
      String? configName,
      String? businessTagId,
      int? materialId,
      int? subjectId,
      String? subjectName,
      String? pictureUrl,
      String? buttonText,
      String? linkUrl,
      String? description,
      String? title,
      bool? recommend,
      String? mainColor,
      String? popupText,
      int? resLibId,
      int? resourceId,
      List<AdvertisementsMaterialDetailData>? materialDetails});
}

/// @nodoc
class __$$_AdvertisementsMaterialsDataCopyWithImpl<$Res>
    extends _$AdvertisementsMaterialsDataCopyWithImpl<$Res,
        _$_AdvertisementsMaterialsData>
    implements _$$_AdvertisementsMaterialsDataCopyWith<$Res> {
  __$$_AdvertisementsMaterialsDataCopyWithImpl(
      _$_AdvertisementsMaterialsData _value,
      $Res Function(_$_AdvertisementsMaterialsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? configId = freezed,
    Object? configName = freezed,
    Object? businessTagId = freezed,
    Object? materialId = freezed,
    Object? subjectId = freezed,
    Object? subjectName = freezed,
    Object? pictureUrl = freezed,
    Object? buttonText = freezed,
    Object? linkUrl = freezed,
    Object? description = freezed,
    Object? title = freezed,
    Object? recommend = freezed,
    Object? mainColor = freezed,
    Object? popupText = freezed,
    Object? resLibId = freezed,
    Object? resourceId = freezed,
    Object? materialDetails = freezed,
  }) {
    return _then(_$_AdvertisementsMaterialsData(
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectId: freezed == subjectId
          ? _value.subjectId
          : subjectId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      recommend: freezed == recommend
          ? _value.recommend
          : recommend // ignore: cast_nullable_to_non_nullable
              as bool?,
      mainColor: freezed == mainColor
          ? _value.mainColor
          : mainColor // ignore: cast_nullable_to_non_nullable
              as String?,
      popupText: freezed == popupText
          ? _value.popupText
          : popupText // ignore: cast_nullable_to_non_nullable
              as String?,
      resLibId: freezed == resLibId
          ? _value.resLibId
          : resLibId // ignore: cast_nullable_to_non_nullable
              as int?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as int?,
      materialDetails: freezed == materialDetails
          ? _value._materialDetails
          : materialDetails // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementsMaterialDetailData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdvertisementsMaterialsData implements _AdvertisementsMaterialsData {
  const _$_AdvertisementsMaterialsData(
      {this.configId,
      this.configName,
      this.businessTagId,
      this.materialId,
      this.subjectId,
      this.subjectName,
      this.pictureUrl,
      this.buttonText,
      this.linkUrl,
      this.description,
      this.title,
      this.recommend,
      this.mainColor,
      this.popupText,
      this.resLibId,
      this.resourceId,
      final List<AdvertisementsMaterialDetailData>? materialDetails})
      : _materialDetails = materialDetails;

  factory _$_AdvertisementsMaterialsData.fromJson(Map<String, dynamic> json) =>
      _$$_AdvertisementsMaterialsDataFromJson(json);

  @override
  final int? configId;
  @override
  final String? configName;
  @override
  final String? businessTagId;
  @override
  final int? materialId;
  @override
  final int? subjectId;
  @override
  final String? subjectName;
  @override
  final String? pictureUrl;
  @override
  final String? buttonText;
  @override
  final String? linkUrl;
  @override
  final String? description;
  @override
  final String? title;
  @override
  final bool? recommend;
  @override
  final String? mainColor;
  @override
  final String? popupText;
  @override
  final int? resLibId;
  @override
  final int? resourceId;
  final List<AdvertisementsMaterialDetailData>? _materialDetails;
  @override
  List<AdvertisementsMaterialDetailData>? get materialDetails {
    final value = _materialDetails;
    if (value == null) return null;
    if (_materialDetails is EqualUnmodifiableListView) return _materialDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AdvertisementsMaterialsData(configId: $configId, configName: $configName, businessTagId: $businessTagId, materialId: $materialId, subjectId: $subjectId, subjectName: $subjectName, pictureUrl: $pictureUrl, buttonText: $buttonText, linkUrl: $linkUrl, description: $description, title: $title, recommend: $recommend, mainColor: $mainColor, popupText: $popupText, resLibId: $resLibId, resourceId: $resourceId, materialDetails: $materialDetails)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdvertisementsMaterialsData &&
            (identical(other.configId, configId) ||
                other.configId == configId) &&
            (identical(other.configName, configName) ||
                other.configName == configName) &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.subjectId, subjectId) ||
                other.subjectId == subjectId) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.recommend, recommend) ||
                other.recommend == recommend) &&
            (identical(other.mainColor, mainColor) ||
                other.mainColor == mainColor) &&
            (identical(other.popupText, popupText) ||
                other.popupText == popupText) &&
            (identical(other.resLibId, resLibId) ||
                other.resLibId == resLibId) &&
            (identical(other.resourceId, resourceId) ||
                other.resourceId == resourceId) &&
            const DeepCollectionEquality()
                .equals(other._materialDetails, _materialDetails));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      configId,
      configName,
      businessTagId,
      materialId,
      subjectId,
      subjectName,
      pictureUrl,
      buttonText,
      linkUrl,
      description,
      title,
      recommend,
      mainColor,
      popupText,
      resLibId,
      resourceId,
      const DeepCollectionEquality().hash(_materialDetails));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdvertisementsMaterialsDataCopyWith<_$_AdvertisementsMaterialsData>
      get copyWith => __$$_AdvertisementsMaterialsDataCopyWithImpl<
          _$_AdvertisementsMaterialsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdvertisementsMaterialsDataToJson(
      this,
    );
  }
}

abstract class _AdvertisementsMaterialsData
    implements AdvertisementsMaterialsData {
  const factory _AdvertisementsMaterialsData(
          {final int? configId,
          final String? configName,
          final String? businessTagId,
          final int? materialId,
          final int? subjectId,
          final String? subjectName,
          final String? pictureUrl,
          final String? buttonText,
          final String? linkUrl,
          final String? description,
          final String? title,
          final bool? recommend,
          final String? mainColor,
          final String? popupText,
          final int? resLibId,
          final int? resourceId,
          final List<AdvertisementsMaterialDetailData>? materialDetails}) =
      _$_AdvertisementsMaterialsData;

  factory _AdvertisementsMaterialsData.fromJson(Map<String, dynamic> json) =
      _$_AdvertisementsMaterialsData.fromJson;

  @override
  int? get configId;
  @override
  String? get configName;
  @override
  String? get businessTagId;
  @override
  int? get materialId;
  @override
  int? get subjectId;
  @override
  String? get subjectName;
  @override
  String? get pictureUrl;
  @override
  String? get buttonText;
  @override
  String? get linkUrl;
  @override
  String? get description;
  @override
  String? get title;
  @override
  bool? get recommend;
  @override
  String? get mainColor;
  @override
  String? get popupText;
  @override
  int? get resLibId;
  @override
  int? get resourceId;
  @override
  List<AdvertisementsMaterialDetailData>? get materialDetails;
  @override
  @JsonKey(ignore: true)
  _$$_AdvertisementsMaterialsDataCopyWith<_$_AdvertisementsMaterialsData>
      get copyWith => throw _privateConstructorUsedError;
}

AdvertisementsMaterialDetailData _$AdvertisementsMaterialDetailDataFromJson(
    Map<String, dynamic> json) {
  return _AdvertisementsMaterialDetailData.fromJson(json);
}

/// @nodoc
mixin _$AdvertisementsMaterialDetailData {
  int? get resLibId => throw _privateConstructorUsedError;
  int? get resourceId => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  int? get countDownSecond => throw _privateConstructorUsedError;
  String? get receiveCourseImage => throw _privateConstructorUsedError;
  int? get linkId => throw _privateConstructorUsedError;
  String? get padImageUrl => throw _privateConstructorUsedError;
  String? get subjectDescImage => throw _privateConstructorUsedError;
  String? get selectCourseTitle => throw _privateConstructorUsedError;
  List<SkuData>? get skus => throw _privateConstructorUsedError;
  int? get cardStyle => throw _privateConstructorUsedError;
  SkuGroupData? get skuGroup => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdvertisementsMaterialDetailDataCopyWith<AdvertisementsMaterialDetailData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvertisementsMaterialDetailDataCopyWith<$Res> {
  factory $AdvertisementsMaterialDetailDataCopyWith(
          AdvertisementsMaterialDetailData value,
          $Res Function(AdvertisementsMaterialDetailData) then) =
      _$AdvertisementsMaterialDetailDataCopyWithImpl<$Res,
          AdvertisementsMaterialDetailData>;
  @useResult
  $Res call(
      {int? resLibId,
      int? resourceId,
      String? imageUrl,
      String? buttonText,
      String? linkUrl,
      String? title,
      String? subTitle,
      int? countDownSecond,
      String? receiveCourseImage,
      int? linkId,
      String? padImageUrl,
      String? subjectDescImage,
      String? selectCourseTitle,
      List<SkuData>? skus,
      int? cardStyle,
      SkuGroupData? skuGroup});

  $SkuGroupDataCopyWith<$Res>? get skuGroup;
}

/// @nodoc
class _$AdvertisementsMaterialDetailDataCopyWithImpl<$Res,
        $Val extends AdvertisementsMaterialDetailData>
    implements $AdvertisementsMaterialDetailDataCopyWith<$Res> {
  _$AdvertisementsMaterialDetailDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resLibId = freezed,
    Object? resourceId = freezed,
    Object? imageUrl = freezed,
    Object? buttonText = freezed,
    Object? linkUrl = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? countDownSecond = freezed,
    Object? receiveCourseImage = freezed,
    Object? linkId = freezed,
    Object? padImageUrl = freezed,
    Object? subjectDescImage = freezed,
    Object? selectCourseTitle = freezed,
    Object? skus = freezed,
    Object? cardStyle = freezed,
    Object? skuGroup = freezed,
  }) {
    return _then(_value.copyWith(
      resLibId: freezed == resLibId
          ? _value.resLibId
          : resLibId // ignore: cast_nullable_to_non_nullable
              as int?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      countDownSecond: freezed == countDownSecond
          ? _value.countDownSecond
          : countDownSecond // ignore: cast_nullable_to_non_nullable
              as int?,
      receiveCourseImage: freezed == receiveCourseImage
          ? _value.receiveCourseImage
          : receiveCourseImage // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as int?,
      padImageUrl: freezed == padImageUrl
          ? _value.padImageUrl
          : padImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectDescImage: freezed == subjectDescImage
          ? _value.subjectDescImage
          : subjectDescImage // ignore: cast_nullable_to_non_nullable
              as String?,
      selectCourseTitle: freezed == selectCourseTitle
          ? _value.selectCourseTitle
          : selectCourseTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      skus: freezed == skus
          ? _value.skus
          : skus // ignore: cast_nullable_to_non_nullable
              as List<SkuData>?,
      cardStyle: freezed == cardStyle
          ? _value.cardStyle
          : cardStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      skuGroup: freezed == skuGroup
          ? _value.skuGroup
          : skuGroup // ignore: cast_nullable_to_non_nullable
              as SkuGroupData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SkuGroupDataCopyWith<$Res>? get skuGroup {
    if (_value.skuGroup == null) {
      return null;
    }

    return $SkuGroupDataCopyWith<$Res>(_value.skuGroup!, (value) {
      return _then(_value.copyWith(skuGroup: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AdvertisementsMaterialDetailDataCopyWith<$Res>
    implements $AdvertisementsMaterialDetailDataCopyWith<$Res> {
  factory _$$_AdvertisementsMaterialDetailDataCopyWith(
          _$_AdvertisementsMaterialDetailData value,
          $Res Function(_$_AdvertisementsMaterialDetailData) then) =
      __$$_AdvertisementsMaterialDetailDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? resLibId,
      int? resourceId,
      String? imageUrl,
      String? buttonText,
      String? linkUrl,
      String? title,
      String? subTitle,
      int? countDownSecond,
      String? receiveCourseImage,
      int? linkId,
      String? padImageUrl,
      String? subjectDescImage,
      String? selectCourseTitle,
      List<SkuData>? skus,
      int? cardStyle,
      SkuGroupData? skuGroup});

  @override
  $SkuGroupDataCopyWith<$Res>? get skuGroup;
}

/// @nodoc
class __$$_AdvertisementsMaterialDetailDataCopyWithImpl<$Res>
    extends _$AdvertisementsMaterialDetailDataCopyWithImpl<$Res,
        _$_AdvertisementsMaterialDetailData>
    implements _$$_AdvertisementsMaterialDetailDataCopyWith<$Res> {
  __$$_AdvertisementsMaterialDetailDataCopyWithImpl(
      _$_AdvertisementsMaterialDetailData _value,
      $Res Function(_$_AdvertisementsMaterialDetailData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resLibId = freezed,
    Object? resourceId = freezed,
    Object? imageUrl = freezed,
    Object? buttonText = freezed,
    Object? linkUrl = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? countDownSecond = freezed,
    Object? receiveCourseImage = freezed,
    Object? linkId = freezed,
    Object? padImageUrl = freezed,
    Object? subjectDescImage = freezed,
    Object? selectCourseTitle = freezed,
    Object? skus = freezed,
    Object? cardStyle = freezed,
    Object? skuGroup = freezed,
  }) {
    return _then(_$_AdvertisementsMaterialDetailData(
      resLibId: freezed == resLibId
          ? _value.resLibId
          : resLibId // ignore: cast_nullable_to_non_nullable
              as int?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      countDownSecond: freezed == countDownSecond
          ? _value.countDownSecond
          : countDownSecond // ignore: cast_nullable_to_non_nullable
              as int?,
      receiveCourseImage: freezed == receiveCourseImage
          ? _value.receiveCourseImage
          : receiveCourseImage // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as int?,
      padImageUrl: freezed == padImageUrl
          ? _value.padImageUrl
          : padImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectDescImage: freezed == subjectDescImage
          ? _value.subjectDescImage
          : subjectDescImage // ignore: cast_nullable_to_non_nullable
              as String?,
      selectCourseTitle: freezed == selectCourseTitle
          ? _value.selectCourseTitle
          : selectCourseTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      skus: freezed == skus
          ? _value._skus
          : skus // ignore: cast_nullable_to_non_nullable
              as List<SkuData>?,
      cardStyle: freezed == cardStyle
          ? _value.cardStyle
          : cardStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      skuGroup: freezed == skuGroup
          ? _value.skuGroup
          : skuGroup // ignore: cast_nullable_to_non_nullable
              as SkuGroupData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdvertisementsMaterialDetailData
    implements _AdvertisementsMaterialDetailData {
  const _$_AdvertisementsMaterialDetailData(
      {this.resLibId,
      this.resourceId,
      this.imageUrl,
      this.buttonText,
      this.linkUrl,
      this.title,
      this.subTitle,
      this.countDownSecond,
      this.receiveCourseImage,
      this.linkId,
      this.padImageUrl,
      this.subjectDescImage,
      this.selectCourseTitle,
      final List<SkuData>? skus,
      this.cardStyle,
      this.skuGroup})
      : _skus = skus;

  factory _$_AdvertisementsMaterialDetailData.fromJson(
          Map<String, dynamic> json) =>
      _$$_AdvertisementsMaterialDetailDataFromJson(json);

  @override
  final int? resLibId;
  @override
  final int? resourceId;
  @override
  final String? imageUrl;
  @override
  final String? buttonText;
  @override
  final String? linkUrl;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final int? countDownSecond;
  @override
  final String? receiveCourseImage;
  @override
  final int? linkId;
  @override
  final String? padImageUrl;
  @override
  final String? subjectDescImage;
  @override
  final String? selectCourseTitle;
  final List<SkuData>? _skus;
  @override
  List<SkuData>? get skus {
    final value = _skus;
    if (value == null) return null;
    if (_skus is EqualUnmodifiableListView) return _skus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? cardStyle;
  @override
  final SkuGroupData? skuGroup;

  @override
  String toString() {
    return 'AdvertisementsMaterialDetailData(resLibId: $resLibId, resourceId: $resourceId, imageUrl: $imageUrl, buttonText: $buttonText, linkUrl: $linkUrl, title: $title, subTitle: $subTitle, countDownSecond: $countDownSecond, receiveCourseImage: $receiveCourseImage, linkId: $linkId, padImageUrl: $padImageUrl, subjectDescImage: $subjectDescImage, selectCourseTitle: $selectCourseTitle, skus: $skus, cardStyle: $cardStyle, skuGroup: $skuGroup)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdvertisementsMaterialDetailData &&
            (identical(other.resLibId, resLibId) ||
                other.resLibId == resLibId) &&
            (identical(other.resourceId, resourceId) ||
                other.resourceId == resourceId) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.countDownSecond, countDownSecond) ||
                other.countDownSecond == countDownSecond) &&
            (identical(other.receiveCourseImage, receiveCourseImage) ||
                other.receiveCourseImage == receiveCourseImage) &&
            (identical(other.linkId, linkId) || other.linkId == linkId) &&
            (identical(other.padImageUrl, padImageUrl) ||
                other.padImageUrl == padImageUrl) &&
            (identical(other.subjectDescImage, subjectDescImage) ||
                other.subjectDescImage == subjectDescImage) &&
            (identical(other.selectCourseTitle, selectCourseTitle) ||
                other.selectCourseTitle == selectCourseTitle) &&
            const DeepCollectionEquality().equals(other._skus, _skus) &&
            (identical(other.cardStyle, cardStyle) ||
                other.cardStyle == cardStyle) &&
            (identical(other.skuGroup, skuGroup) ||
                other.skuGroup == skuGroup));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      resLibId,
      resourceId,
      imageUrl,
      buttonText,
      linkUrl,
      title,
      subTitle,
      countDownSecond,
      receiveCourseImage,
      linkId,
      padImageUrl,
      subjectDescImage,
      selectCourseTitle,
      const DeepCollectionEquality().hash(_skus),
      cardStyle,
      skuGroup);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdvertisementsMaterialDetailDataCopyWith<
          _$_AdvertisementsMaterialDetailData>
      get copyWith => __$$_AdvertisementsMaterialDetailDataCopyWithImpl<
          _$_AdvertisementsMaterialDetailData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdvertisementsMaterialDetailDataToJson(
      this,
    );
  }
}

abstract class _AdvertisementsMaterialDetailData
    implements AdvertisementsMaterialDetailData {
  const factory _AdvertisementsMaterialDetailData(
      {final int? resLibId,
      final int? resourceId,
      final String? imageUrl,
      final String? buttonText,
      final String? linkUrl,
      final String? title,
      final String? subTitle,
      final int? countDownSecond,
      final String? receiveCourseImage,
      final int? linkId,
      final String? padImageUrl,
      final String? subjectDescImage,
      final String? selectCourseTitle,
      final List<SkuData>? skus,
      final int? cardStyle,
      final SkuGroupData? skuGroup}) = _$_AdvertisementsMaterialDetailData;

  factory _AdvertisementsMaterialDetailData.fromJson(
      Map<String, dynamic> json) = _$_AdvertisementsMaterialDetailData.fromJson;

  @override
  int? get resLibId;
  @override
  int? get resourceId;
  @override
  String? get imageUrl;
  @override
  String? get buttonText;
  @override
  String? get linkUrl;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  int? get countDownSecond;
  @override
  String? get receiveCourseImage;
  @override
  int? get linkId;
  @override
  String? get padImageUrl;
  @override
  String? get subjectDescImage;
  @override
  String? get selectCourseTitle;
  @override
  List<SkuData>? get skus;
  @override
  int? get cardStyle;
  @override
  SkuGroupData? get skuGroup;
  @override
  @JsonKey(ignore: true)
  _$$_AdvertisementsMaterialDetailDataCopyWith<
          _$_AdvertisementsMaterialDetailData>
      get copyWith => throw _privateConstructorUsedError;
}

SkuGroupData _$SkuGroupDataFromJson(Map<String, dynamic> json) {
  return _SkuGroupData.fromJson(json);
}

/// @nodoc
mixin _$SkuGroupData {
  String? get tipText => throw _privateConstructorUsedError;
  List<SkuGroup>? get skuGroups => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkuGroupDataCopyWith<SkuGroupData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkuGroupDataCopyWith<$Res> {
  factory $SkuGroupDataCopyWith(
          SkuGroupData value, $Res Function(SkuGroupData) then) =
      _$SkuGroupDataCopyWithImpl<$Res, SkuGroupData>;
  @useResult
  $Res call({String? tipText, List<SkuGroup>? skuGroups});
}

/// @nodoc
class _$SkuGroupDataCopyWithImpl<$Res, $Val extends SkuGroupData>
    implements $SkuGroupDataCopyWith<$Res> {
  _$SkuGroupDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipText = freezed,
    Object? skuGroups = freezed,
  }) {
    return _then(_value.copyWith(
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuGroups: freezed == skuGroups
          ? _value.skuGroups
          : skuGroups // ignore: cast_nullable_to_non_nullable
              as List<SkuGroup>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkuGroupDataCopyWith<$Res>
    implements $SkuGroupDataCopyWith<$Res> {
  factory _$$_SkuGroupDataCopyWith(
          _$_SkuGroupData value, $Res Function(_$_SkuGroupData) then) =
      __$$_SkuGroupDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? tipText, List<SkuGroup>? skuGroups});
}

/// @nodoc
class __$$_SkuGroupDataCopyWithImpl<$Res>
    extends _$SkuGroupDataCopyWithImpl<$Res, _$_SkuGroupData>
    implements _$$_SkuGroupDataCopyWith<$Res> {
  __$$_SkuGroupDataCopyWithImpl(
      _$_SkuGroupData _value, $Res Function(_$_SkuGroupData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipText = freezed,
    Object? skuGroups = freezed,
  }) {
    return _then(_$_SkuGroupData(
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuGroups: freezed == skuGroups
          ? _value._skuGroups
          : skuGroups // ignore: cast_nullable_to_non_nullable
              as List<SkuGroup>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SkuGroupData implements _SkuGroupData {
  const _$_SkuGroupData(
      {required this.tipText, required final List<SkuGroup>? skuGroups})
      : _skuGroups = skuGroups;

  factory _$_SkuGroupData.fromJson(Map<String, dynamic> json) =>
      _$$_SkuGroupDataFromJson(json);

  @override
  final String? tipText;
  final List<SkuGroup>? _skuGroups;
  @override
  List<SkuGroup>? get skuGroups {
    final value = _skuGroups;
    if (value == null) return null;
    if (_skuGroups is EqualUnmodifiableListView) return _skuGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SkuGroupData(tipText: $tipText, skuGroups: $skuGroups)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SkuGroupData &&
            (identical(other.tipText, tipText) || other.tipText == tipText) &&
            const DeepCollectionEquality()
                .equals(other._skuGroups, _skuGroups));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, tipText, const DeepCollectionEquality().hash(_skuGroups));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkuGroupDataCopyWith<_$_SkuGroupData> get copyWith =>
      __$$_SkuGroupDataCopyWithImpl<_$_SkuGroupData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkuGroupDataToJson(
      this,
    );
  }
}

abstract class _SkuGroupData implements SkuGroupData {
  const factory _SkuGroupData(
      {required final String? tipText,
      required final List<SkuGroup>? skuGroups}) = _$_SkuGroupData;

  factory _SkuGroupData.fromJson(Map<String, dynamic> json) =
      _$_SkuGroupData.fromJson;

  @override
  String? get tipText;
  @override
  List<SkuGroup>? get skuGroups;
  @override
  @JsonKey(ignore: true)
  _$$_SkuGroupDataCopyWith<_$_SkuGroupData> get copyWith =>
      throw _privateConstructorUsedError;
}

SkuGroup _$SkuGroupFromJson(Map<String, dynamic> json) {
  return _SkuGroup.fromJson(json);
}

/// @nodoc
mixin _$SkuGroup {
  String? get gradeKey => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  String? get tipText => throw _privateConstructorUsedError;
  List<SkuData>? get skus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkuGroupCopyWith<SkuGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkuGroupCopyWith<$Res> {
  factory $SkuGroupCopyWith(SkuGroup value, $Res Function(SkuGroup) then) =
      _$SkuGroupCopyWithImpl<$Res, SkuGroup>;
  @useResult
  $Res call(
      {String? gradeKey,
      String? gradeTitle,
      String? tipText,
      List<SkuData>? skus});
}

/// @nodoc
class _$SkuGroupCopyWithImpl<$Res, $Val extends SkuGroup>
    implements $SkuGroupCopyWith<$Res> {
  _$SkuGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? tipText = freezed,
    Object? skus = freezed,
  }) {
    return _then(_value.copyWith(
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
      skus: freezed == skus
          ? _value.skus
          : skus // ignore: cast_nullable_to_non_nullable
              as List<SkuData>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkuGroupCopyWith<$Res> implements $SkuGroupCopyWith<$Res> {
  factory _$$_SkuGroupCopyWith(
          _$_SkuGroup value, $Res Function(_$_SkuGroup) then) =
      __$$_SkuGroupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? gradeKey,
      String? gradeTitle,
      String? tipText,
      List<SkuData>? skus});
}

/// @nodoc
class __$$_SkuGroupCopyWithImpl<$Res>
    extends _$SkuGroupCopyWithImpl<$Res, _$_SkuGroup>
    implements _$$_SkuGroupCopyWith<$Res> {
  __$$_SkuGroupCopyWithImpl(
      _$_SkuGroup _value, $Res Function(_$_SkuGroup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? tipText = freezed,
    Object? skus = freezed,
  }) {
    return _then(_$_SkuGroup(
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      tipText: freezed == tipText
          ? _value.tipText
          : tipText // ignore: cast_nullable_to_non_nullable
              as String?,
      skus: freezed == skus
          ? _value._skus
          : skus // ignore: cast_nullable_to_non_nullable
              as List<SkuData>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SkuGroup implements _SkuGroup {
  const _$_SkuGroup(
      {required this.gradeKey,
      required this.gradeTitle,
      required this.tipText,
      required final List<SkuData>? skus})
      : _skus = skus;

  factory _$_SkuGroup.fromJson(Map<String, dynamic> json) =>
      _$$_SkuGroupFromJson(json);

  @override
  final String? gradeKey;
  @override
  final String? gradeTitle;
  @override
  final String? tipText;
  final List<SkuData>? _skus;
  @override
  List<SkuData>? get skus {
    final value = _skus;
    if (value == null) return null;
    if (_skus is EqualUnmodifiableListView) return _skus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SkuGroup(gradeKey: $gradeKey, gradeTitle: $gradeTitle, tipText: $tipText, skus: $skus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SkuGroup &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.tipText, tipText) || other.tipText == tipText) &&
            const DeepCollectionEquality().equals(other._skus, _skus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, gradeKey, gradeTitle, tipText,
      const DeepCollectionEquality().hash(_skus));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkuGroupCopyWith<_$_SkuGroup> get copyWith =>
      __$$_SkuGroupCopyWithImpl<_$_SkuGroup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkuGroupToJson(
      this,
    );
  }
}

abstract class _SkuGroup implements SkuGroup {
  const factory _SkuGroup(
      {required final String? gradeKey,
      required final String? gradeTitle,
      required final String? tipText,
      required final List<SkuData>? skus}) = _$_SkuGroup;

  factory _SkuGroup.fromJson(Map<String, dynamic> json) = _$_SkuGroup.fromJson;

  @override
  String? get gradeKey;
  @override
  String? get gradeTitle;
  @override
  String? get tipText;
  @override
  List<SkuData>? get skus;
  @override
  @JsonKey(ignore: true)
  _$$_SkuGroupCopyWith<_$_SkuGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

SkuData _$SkuDataFromJson(Map<String, dynamic> json) {
  return _SkuData.fromJson(json);
}

/// @nodoc
mixin _$SkuData {
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  int? get linkId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkuDataCopyWith<SkuData> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkuDataCopyWith<$Res> {
  factory $SkuDataCopyWith(SkuData value, $Res Function(SkuData) then) =
      _$SkuDataCopyWithImpl<$Res, SkuData>;
  @useResult
  $Res call({String? skuId, String? skuName, int? linkId});
}

/// @nodoc
class _$SkuDataCopyWithImpl<$Res, $Val extends SkuData>
    implements $SkuDataCopyWith<$Res> {
  _$SkuDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? linkId = freezed,
  }) {
    return _then(_value.copyWith(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkuDataCopyWith<$Res> implements $SkuDataCopyWith<$Res> {
  factory _$$_SkuDataCopyWith(
          _$_SkuData value, $Res Function(_$_SkuData) then) =
      __$$_SkuDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? skuId, String? skuName, int? linkId});
}

/// @nodoc
class __$$_SkuDataCopyWithImpl<$Res>
    extends _$SkuDataCopyWithImpl<$Res, _$_SkuData>
    implements _$$_SkuDataCopyWith<$Res> {
  __$$_SkuDataCopyWithImpl(_$_SkuData _value, $Res Function(_$_SkuData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? linkId = freezed,
  }) {
    return _then(_$_SkuData(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SkuData implements _SkuData {
  const _$_SkuData({this.skuId, this.skuName, this.linkId});

  factory _$_SkuData.fromJson(Map<String, dynamic> json) =>
      _$$_SkuDataFromJson(json);

  @override
  final String? skuId;
  @override
  final String? skuName;
  @override
  final int? linkId;

  @override
  String toString() {
    return 'SkuData(skuId: $skuId, skuName: $skuName, linkId: $linkId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SkuData &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.linkId, linkId) || other.linkId == linkId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skuId, skuName, linkId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkuDataCopyWith<_$_SkuData> get copyWith =>
      __$$_SkuDataCopyWithImpl<_$_SkuData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkuDataToJson(
      this,
    );
  }
}

abstract class _SkuData implements SkuData {
  const factory _SkuData(
      {final String? skuId,
      final String? skuName,
      final int? linkId}) = _$_SkuData;

  factory _SkuData.fromJson(Map<String, dynamic> json) = _$_SkuData.fromJson;

  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  int? get linkId;
  @override
  @JsonKey(ignore: true)
  _$$_SkuDataCopyWith<_$_SkuData> get copyWith =>
      throw _privateConstructorUsedError;
}
