// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_map_home_user_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TopUserInfo _$$_TopUserInfoFromJson(Map<String, dynamic> json) =>
    _$_TopUserInfo(
      dress: json['dress'] == null
          ? null
          : Dress.fromJson(json['dress'] as Map<String, dynamic>),
      partnerGuide: json['partnerGuide'] == null
          ? null
          : PartnerGuide.fromJson(json['partnerGuide'] as Map<String, dynamic>),
      continuous: json['continuous'] == null
          ? null
          : Continuous.fromJson(json['continuous'] as Map<String, dynamic>),
      learnBean: json['learnBean'] == null
          ? null
          : LearnBean.fromJson(json['learnBean'] as Map<String, dynamic>),
      medal: json['medal'] == null
          ? null
          : LearnBean.fromJson(json['medal'] as Map<String, dynamic>),
      newDressUpNotice: json['newDressUpNotice'] == null
          ? null
          : NewDressUpNotice.fromJson(
              json['newDressUpNotice'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TopUserInfoToJson(_$_TopUserInfo instance) =>
    <String, dynamic>{
      'dress': instance.dress,
      'partnerGuide': instance.partnerGuide,
      'continuous': instance.continuous,
      'learnBean': instance.learnBean,
      'medal': instance.medal,
      'newDressUpNotice': instance.newDressUpNotice,
    };

_$_Continuous _$$_ContinuousFromJson(Map<String, dynamic> json) =>
    _$_Continuous(
      days: json['days'] as int?,
      isStart: json['isStart'] as int?,
      icon: json['icon'] as String?,
      clientDynamic: json['clientDynamic'] as String?,
      resource: json['resource'] as String?,
      isFinishLesson: json['isFinishLesson'] as int?,
      isFinishLessonPopup: json['isFinishLessonPopup'] as int?,
      notStartText: json['notStartText'] as String?,
      status: json['status'] as int?,
      jumpRoute: json['jumpRoute'] as String?,
      milestone: json['milestone'] == null
          ? null
          : Milestone.fromJson(json['milestone'] as Map<String, dynamic>),
      bestDays: json['bestDays'] as int?,
    );

Map<String, dynamic> _$$_ContinuousToJson(_$_Continuous instance) =>
    <String, dynamic>{
      'days': instance.days,
      'isStart': instance.isStart,
      'icon': instance.icon,
      'clientDynamic': instance.clientDynamic,
      'resource': instance.resource,
      'isFinishLesson': instance.isFinishLesson,
      'isFinishLessonPopup': instance.isFinishLessonPopup,
      'notStartText': instance.notStartText,
      'status': instance.status,
      'jumpRoute': instance.jumpRoute,
      'milestone': instance.milestone,
      'bestDays': instance.bestDays,
    };

_$_Dress _$$_DressFromJson(Map<String, dynamic> json) => _$_Dress(
      img: json['img'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_DressToJson(_$_Dress instance) => <String, dynamic>{
      'img': instance.img,
      'jumpRoute': instance.jumpRoute,
    };

_$_PartnerGuide _$$_PartnerGuideFromJson(Map<String, dynamic> json) =>
    _$_PartnerGuide(
      coverImg: json['coverImg'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_PartnerGuideToJson(_$_PartnerGuide instance) =>
    <String, dynamic>{
      'coverImg': instance.coverImg,
      'jumpRoute': instance.jumpRoute,
    };

_$_LearnBean _$$_LearnBeanFromJson(Map<String, dynamic> json) => _$_LearnBean(
      amount: json['amount'] as int?,
      isShow: json['isShow'] as int?,
      type: json['type'] as int?,
      icon: json['icon'] as String?,
      jumpRoute: json['jumpRoute'] as String?,
    );

Map<String, dynamic> _$$_LearnBeanToJson(_$_LearnBean instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'isShow': instance.isShow,
      'type': instance.type,
      'icon': instance.icon,
      'jumpRoute': instance.jumpRoute,
    };

_$_Milestone _$$_MilestoneFromJson(Map<String, dynamic> json) => _$_Milestone(
      progressText: json['progressText'] as String?,
    );

Map<String, dynamic> _$$_MilestoneToJson(_$_Milestone instance) =>
    <String, dynamic>{
      'progressText': instance.progressText,
    };

_$_NewDressUpNotice _$$_NewDressUpNoticeFromJson(Map<String, dynamic> json) =>
    _$_NewDressUpNotice(
      newDressUp: json['newDressUp'] as int?,
      newDressUpIcon: json['newDressUpIcon'] as String?,
    );

Map<String, dynamic> _$$_NewDressUpNoticeToJson(_$_NewDressUpNotice instance) =>
    <String, dynamic>{
      'newDressUp': instance.newDressUp,
      'newDressUpIcon': instance.newDressUpIcon,
    };
