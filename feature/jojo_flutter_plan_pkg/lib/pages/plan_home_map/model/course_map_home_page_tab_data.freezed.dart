// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_map_home_page_tab_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseSubjectTabData _$CourseSubjectTabDataFromJson(Map<String, dynamic> json) {
  return _CourseSubjectTabData.fromJson(json);
}

/// @nodoc
mixin _$CourseSubjectTabData {
  List<SubjectClassList>? get subjectClassList =>
      throw _privateConstructorUsedError;
  List<SubjectList>? get subjectList => throw _privateConstructorUsedError;
  List<ClassPagePopupList>? get popupList => throw _privateConstructorUsedError;
  SubjectExtendVo? get subjectExtendVo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseSubjectTabDataCopyWith<CourseSubjectTabData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseSubjectTabDataCopyWith<$Res> {
  factory $CourseSubjectTabDataCopyWith(CourseSubjectTabData value,
          $Res Function(CourseSubjectTabData) then) =
      _$CourseSubjectTabDataCopyWithImpl<$Res, CourseSubjectTabData>;
  @useResult
  $Res call(
      {List<SubjectClassList>? subjectClassList,
      List<SubjectList>? subjectList,
      List<ClassPagePopupList>? popupList,
      SubjectExtendVo? subjectExtendVo});

  $SubjectExtendVoCopyWith<$Res>? get subjectExtendVo;
}

/// @nodoc
class _$CourseSubjectTabDataCopyWithImpl<$Res,
        $Val extends CourseSubjectTabData>
    implements $CourseSubjectTabDataCopyWith<$Res> {
  _$CourseSubjectTabDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectClassList = freezed,
    Object? subjectList = freezed,
    Object? popupList = freezed,
    Object? subjectExtendVo = freezed,
  }) {
    return _then(_value.copyWith(
      subjectClassList: freezed == subjectClassList
          ? _value.subjectClassList
          : subjectClassList // ignore: cast_nullable_to_non_nullable
              as List<SubjectClassList>?,
      subjectList: freezed == subjectList
          ? _value.subjectList
          : subjectList // ignore: cast_nullable_to_non_nullable
              as List<SubjectList>?,
      popupList: freezed == popupList
          ? _value.popupList
          : popupList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupList>?,
      subjectExtendVo: freezed == subjectExtendVo
          ? _value.subjectExtendVo
          : subjectExtendVo // ignore: cast_nullable_to_non_nullable
              as SubjectExtendVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SubjectExtendVoCopyWith<$Res>? get subjectExtendVo {
    if (_value.subjectExtendVo == null) {
      return null;
    }

    return $SubjectExtendVoCopyWith<$Res>(_value.subjectExtendVo!, (value) {
      return _then(_value.copyWith(subjectExtendVo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseSubjectTabDataCopyWith<$Res>
    implements $CourseSubjectTabDataCopyWith<$Res> {
  factory _$$_CourseSubjectTabDataCopyWith(_$_CourseSubjectTabData value,
          $Res Function(_$_CourseSubjectTabData) then) =
      __$$_CourseSubjectTabDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<SubjectClassList>? subjectClassList,
      List<SubjectList>? subjectList,
      List<ClassPagePopupList>? popupList,
      SubjectExtendVo? subjectExtendVo});

  @override
  $SubjectExtendVoCopyWith<$Res>? get subjectExtendVo;
}

/// @nodoc
class __$$_CourseSubjectTabDataCopyWithImpl<$Res>
    extends _$CourseSubjectTabDataCopyWithImpl<$Res, _$_CourseSubjectTabData>
    implements _$$_CourseSubjectTabDataCopyWith<$Res> {
  __$$_CourseSubjectTabDataCopyWithImpl(_$_CourseSubjectTabData _value,
      $Res Function(_$_CourseSubjectTabData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectClassList = freezed,
    Object? subjectList = freezed,
    Object? popupList = freezed,
    Object? subjectExtendVo = freezed,
  }) {
    return _then(_$_CourseSubjectTabData(
      subjectClassList: freezed == subjectClassList
          ? _value._subjectClassList
          : subjectClassList // ignore: cast_nullable_to_non_nullable
              as List<SubjectClassList>?,
      subjectList: freezed == subjectList
          ? _value._subjectList
          : subjectList // ignore: cast_nullable_to_non_nullable
              as List<SubjectList>?,
      popupList: freezed == popupList
          ? _value._popupList
          : popupList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupList>?,
      subjectExtendVo: freezed == subjectExtendVo
          ? _value.subjectExtendVo
          : subjectExtendVo // ignore: cast_nullable_to_non_nullable
              as SubjectExtendVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseSubjectTabData implements _CourseSubjectTabData {
  const _$_CourseSubjectTabData(
      {final List<SubjectClassList>? subjectClassList,
      final List<SubjectList>? subjectList,
      final List<ClassPagePopupList>? popupList,
      this.subjectExtendVo})
      : _subjectClassList = subjectClassList,
        _subjectList = subjectList,
        _popupList = popupList;

  factory _$_CourseSubjectTabData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseSubjectTabDataFromJson(json);

  final List<SubjectClassList>? _subjectClassList;
  @override
  List<SubjectClassList>? get subjectClassList {
    final value = _subjectClassList;
    if (value == null) return null;
    if (_subjectClassList is EqualUnmodifiableListView)
      return _subjectClassList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SubjectList>? _subjectList;
  @override
  List<SubjectList>? get subjectList {
    final value = _subjectList;
    if (value == null) return null;
    if (_subjectList is EqualUnmodifiableListView) return _subjectList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ClassPagePopupList>? _popupList;
  @override
  List<ClassPagePopupList>? get popupList {
    final value = _popupList;
    if (value == null) return null;
    if (_popupList is EqualUnmodifiableListView) return _popupList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final SubjectExtendVo? subjectExtendVo;

  @override
  String toString() {
    return 'CourseSubjectTabData(subjectClassList: $subjectClassList, subjectList: $subjectList, popupList: $popupList, subjectExtendVo: $subjectExtendVo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseSubjectTabData &&
            const DeepCollectionEquality()
                .equals(other._subjectClassList, _subjectClassList) &&
            const DeepCollectionEquality()
                .equals(other._subjectList, _subjectList) &&
            const DeepCollectionEquality()
                .equals(other._popupList, _popupList) &&
            (identical(other.subjectExtendVo, subjectExtendVo) ||
                other.subjectExtendVo == subjectExtendVo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_subjectClassList),
      const DeepCollectionEquality().hash(_subjectList),
      const DeepCollectionEquality().hash(_popupList),
      subjectExtendVo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseSubjectTabDataCopyWith<_$_CourseSubjectTabData> get copyWith =>
      __$$_CourseSubjectTabDataCopyWithImpl<_$_CourseSubjectTabData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseSubjectTabDataToJson(
      this,
    );
  }
}

abstract class _CourseSubjectTabData implements CourseSubjectTabData {
  const factory _CourseSubjectTabData(
      {final List<SubjectClassList>? subjectClassList,
      final List<SubjectList>? subjectList,
      final List<ClassPagePopupList>? popupList,
      final SubjectExtendVo? subjectExtendVo}) = _$_CourseSubjectTabData;

  factory _CourseSubjectTabData.fromJson(Map<String, dynamic> json) =
      _$_CourseSubjectTabData.fromJson;

  @override
  List<SubjectClassList>? get subjectClassList;
  @override
  List<SubjectList>? get subjectList;
  @override
  List<ClassPagePopupList>? get popupList;
  @override
  SubjectExtendVo? get subjectExtendVo;
  @override
  @JsonKey(ignore: true)
  _$$_CourseSubjectTabDataCopyWith<_$_CourseSubjectTabData> get copyWith =>
      throw _privateConstructorUsedError;
}

AfterServerEntranceVo _$AfterServerEntranceVoFromJson(
    Map<String, dynamic> json) {
  return _AfterServerEntranceVo.fromJson(json);
}

/// @nodoc
mixin _$AfterServerEntranceVo {
  String? get title => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  List<String>? get toastList => throw _privateConstructorUsedError;
  String? get configKey => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AfterServerEntranceVoCopyWith<AfterServerEntranceVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AfterServerEntranceVoCopyWith<$Res> {
  factory $AfterServerEntranceVoCopyWith(AfterServerEntranceVo value,
          $Res Function(AfterServerEntranceVo) then) =
      _$AfterServerEntranceVoCopyWithImpl<$Res, AfterServerEntranceVo>;
  @useResult
  $Res call(
      {String? title,
      String? icon,
      String? route,
      List<String>? toastList,
      String? configKey});
}

/// @nodoc
class _$AfterServerEntranceVoCopyWithImpl<$Res,
        $Val extends AfterServerEntranceVo>
    implements $AfterServerEntranceVoCopyWith<$Res> {
  _$AfterServerEntranceVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toastList = freezed,
    Object? configKey = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value.toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AfterServerEntranceVoCopyWith<$Res>
    implements $AfterServerEntranceVoCopyWith<$Res> {
  factory _$$_AfterServerEntranceVoCopyWith(_$_AfterServerEntranceVo value,
          $Res Function(_$_AfterServerEntranceVo) then) =
      __$$_AfterServerEntranceVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? icon,
      String? route,
      List<String>? toastList,
      String? configKey});
}

/// @nodoc
class __$$_AfterServerEntranceVoCopyWithImpl<$Res>
    extends _$AfterServerEntranceVoCopyWithImpl<$Res, _$_AfterServerEntranceVo>
    implements _$$_AfterServerEntranceVoCopyWith<$Res> {
  __$$_AfterServerEntranceVoCopyWithImpl(_$_AfterServerEntranceVo _value,
      $Res Function(_$_AfterServerEntranceVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toastList = freezed,
    Object? configKey = freezed,
  }) {
    return _then(_$_AfterServerEntranceVo(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value._toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      configKey: freezed == configKey
          ? _value.configKey
          : configKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AfterServerEntranceVo implements _AfterServerEntranceVo {
  const _$_AfterServerEntranceVo(
      {this.title,
      this.icon,
      this.route,
      final List<String>? toastList,
      this.configKey})
      : _toastList = toastList;

  factory _$_AfterServerEntranceVo.fromJson(Map<String, dynamic> json) =>
      _$$_AfterServerEntranceVoFromJson(json);

  @override
  final String? title;
  @override
  final String? icon;
  @override
  final String? route;
  final List<String>? _toastList;
  @override
  List<String>? get toastList {
    final value = _toastList;
    if (value == null) return null;
    if (_toastList is EqualUnmodifiableListView) return _toastList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? configKey;

  @override
  String toString() {
    return 'AfterServerEntranceVo(title: $title, icon: $icon, route: $route, toastList: $toastList, configKey: $configKey)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AfterServerEntranceVo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality()
                .equals(other._toastList, _toastList) &&
            (identical(other.configKey, configKey) ||
                other.configKey == configKey));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, icon, route,
      const DeepCollectionEquality().hash(_toastList), configKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AfterServerEntranceVoCopyWith<_$_AfterServerEntranceVo> get copyWith =>
      __$$_AfterServerEntranceVoCopyWithImpl<_$_AfterServerEntranceVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AfterServerEntranceVoToJson(
      this,
    );
  }
}

abstract class _AfterServerEntranceVo implements AfterServerEntranceVo {
  const factory _AfterServerEntranceVo(
      {final String? title,
      final String? icon,
      final String? route,
      final List<String>? toastList,
      final String? configKey}) = _$_AfterServerEntranceVo;

  factory _AfterServerEntranceVo.fromJson(Map<String, dynamic> json) =
      _$_AfterServerEntranceVo.fromJson;

  @override
  String? get title;
  @override
  String? get icon;
  @override
  String? get route;
  @override
  List<String>? get toastList;
  @override
  String? get configKey;
  @override
  @JsonKey(ignore: true)
  _$$_AfterServerEntranceVoCopyWith<_$_AfterServerEntranceVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassPagePopupList _$ClassPagePopupListFromJson(Map<String, dynamic> json) {
  return _ClassPagePopupList.fromJson(json);
}

/// @nodoc
mixin _$ClassPagePopupList {
  String? get showMode =>
      throw _privateConstructorUsedError; // 展示方式 popup(默认值)/page
  String? get bgColor =>
      throw _privateConstructorUsedError; // 背景色（可能为空，产品根据情况配置）
  int? get popupType => throw _privateConstructorUsedError;
  int? get occurrenceIndex => throw _privateConstructorUsedError;
  String? get popupTitle => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get cancelButtonText => throw _privateConstructorUsedError;
  String? get teacherProfileUrl => throw _privateConstructorUsedError;
  String? get addTeacherUrl => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get userCourseBusinessStatus => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  int? get template => throw _privateConstructorUsedError;
  int? get pattern => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  List<ClassPagePopupItem>? get coursePopupInfoList =>
      throw _privateConstructorUsedError;
  bool? get limitFlow => throw _privateConstructorUsedError;
  String? get businessTypeDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassPagePopupListCopyWith<ClassPagePopupList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassPagePopupListCopyWith<$Res> {
  factory $ClassPagePopupListCopyWith(
          ClassPagePopupList value, $Res Function(ClassPagePopupList) then) =
      _$ClassPagePopupListCopyWithImpl<$Res, ClassPagePopupList>;
  @useResult
  $Res call(
      {String? showMode,
      String? bgColor,
      int? popupType,
      int? occurrenceIndex,
      String? popupTitle,
      String? buttonText,
      String? cancelButtonText,
      String? teacherProfileUrl,
      String? addTeacherUrl,
      String? teacherName,
      String? subTitle,
      int? classId,
      String? icon,
      String? userCourseBusinessStatus,
      String? subjectTypeDesc,
      List<String>? tags,
      int? template,
      int? pattern,
      String? courseSegment,
      String? courseKey,
      List<ClassPagePopupItem>? coursePopupInfoList,
      bool? limitFlow,
      String? businessTypeDesc});
}

/// @nodoc
class _$ClassPagePopupListCopyWithImpl<$Res, $Val extends ClassPagePopupList>
    implements $ClassPagePopupListCopyWith<$Res> {
  _$ClassPagePopupListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showMode = freezed,
    Object? bgColor = freezed,
    Object? popupType = freezed,
    Object? occurrenceIndex = freezed,
    Object? popupTitle = freezed,
    Object? buttonText = freezed,
    Object? cancelButtonText = freezed,
    Object? teacherProfileUrl = freezed,
    Object? addTeacherUrl = freezed,
    Object? teacherName = freezed,
    Object? subTitle = freezed,
    Object? classId = freezed,
    Object? icon = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? subjectTypeDesc = freezed,
    Object? tags = freezed,
    Object? template = freezed,
    Object? pattern = freezed,
    Object? courseSegment = freezed,
    Object? courseKey = freezed,
    Object? coursePopupInfoList = freezed,
    Object? limitFlow = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_value.copyWith(
      showMode: freezed == showMode
          ? _value.showMode
          : showMode // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      popupType: freezed == popupType
          ? _value.popupType
          : popupType // ignore: cast_nullable_to_non_nullable
              as int?,
      occurrenceIndex: freezed == occurrenceIndex
          ? _value.occurrenceIndex
          : occurrenceIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButtonText: freezed == cancelButtonText
          ? _value.cancelButtonText
          : cancelButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as int?,
      pattern: freezed == pattern
          ? _value.pattern
          : pattern // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      coursePopupInfoList: freezed == coursePopupInfoList
          ? _value.coursePopupInfoList
          : coursePopupInfoList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupItem>?,
      limitFlow: freezed == limitFlow
          ? _value.limitFlow
          : limitFlow // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassPagePopupListCopyWith<$Res>
    implements $ClassPagePopupListCopyWith<$Res> {
  factory _$$_ClassPagePopupListCopyWith(_$_ClassPagePopupList value,
          $Res Function(_$_ClassPagePopupList) then) =
      __$$_ClassPagePopupListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? showMode,
      String? bgColor,
      int? popupType,
      int? occurrenceIndex,
      String? popupTitle,
      String? buttonText,
      String? cancelButtonText,
      String? teacherProfileUrl,
      String? addTeacherUrl,
      String? teacherName,
      String? subTitle,
      int? classId,
      String? icon,
      String? userCourseBusinessStatus,
      String? subjectTypeDesc,
      List<String>? tags,
      int? template,
      int? pattern,
      String? courseSegment,
      String? courseKey,
      List<ClassPagePopupItem>? coursePopupInfoList,
      bool? limitFlow,
      String? businessTypeDesc});
}

/// @nodoc
class __$$_ClassPagePopupListCopyWithImpl<$Res>
    extends _$ClassPagePopupListCopyWithImpl<$Res, _$_ClassPagePopupList>
    implements _$$_ClassPagePopupListCopyWith<$Res> {
  __$$_ClassPagePopupListCopyWithImpl(
      _$_ClassPagePopupList _value, $Res Function(_$_ClassPagePopupList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showMode = freezed,
    Object? bgColor = freezed,
    Object? popupType = freezed,
    Object? occurrenceIndex = freezed,
    Object? popupTitle = freezed,
    Object? buttonText = freezed,
    Object? cancelButtonText = freezed,
    Object? teacherProfileUrl = freezed,
    Object? addTeacherUrl = freezed,
    Object? teacherName = freezed,
    Object? subTitle = freezed,
    Object? classId = freezed,
    Object? icon = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? subjectTypeDesc = freezed,
    Object? tags = freezed,
    Object? template = freezed,
    Object? pattern = freezed,
    Object? courseSegment = freezed,
    Object? courseKey = freezed,
    Object? coursePopupInfoList = freezed,
    Object? limitFlow = freezed,
    Object? businessTypeDesc = freezed,
  }) {
    return _then(_$_ClassPagePopupList(
      showMode: freezed == showMode
          ? _value.showMode
          : showMode // ignore: cast_nullable_to_non_nullable
              as String?,
      bgColor: freezed == bgColor
          ? _value.bgColor
          : bgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      popupType: freezed == popupType
          ? _value.popupType
          : popupType // ignore: cast_nullable_to_non_nullable
              as int?,
      occurrenceIndex: freezed == occurrenceIndex
          ? _value.occurrenceIndex
          : occurrenceIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelButtonText: freezed == cancelButtonText
          ? _value.cancelButtonText
          : cancelButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherProfileUrl: freezed == teacherProfileUrl
          ? _value.teacherProfileUrl
          : teacherProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      template: freezed == template
          ? _value.template
          : template // ignore: cast_nullable_to_non_nullable
              as int?,
      pattern: freezed == pattern
          ? _value.pattern
          : pattern // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      coursePopupInfoList: freezed == coursePopupInfoList
          ? _value._coursePopupInfoList
          : coursePopupInfoList // ignore: cast_nullable_to_non_nullable
              as List<ClassPagePopupItem>?,
      limitFlow: freezed == limitFlow
          ? _value.limitFlow
          : limitFlow // ignore: cast_nullable_to_non_nullable
              as bool?,
      businessTypeDesc: freezed == businessTypeDesc
          ? _value.businessTypeDesc
          : businessTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassPagePopupList implements _ClassPagePopupList {
  const _$_ClassPagePopupList(
      {this.showMode,
      this.bgColor,
      this.popupType,
      this.occurrenceIndex,
      this.popupTitle,
      this.buttonText,
      this.cancelButtonText,
      this.teacherProfileUrl,
      this.addTeacherUrl,
      this.teacherName,
      this.subTitle,
      this.classId,
      this.icon,
      this.userCourseBusinessStatus,
      this.subjectTypeDesc,
      final List<String>? tags,
      this.template,
      this.pattern,
      this.courseSegment,
      this.courseKey,
      final List<ClassPagePopupItem>? coursePopupInfoList,
      this.limitFlow,
      this.businessTypeDesc})
      : _tags = tags,
        _coursePopupInfoList = coursePopupInfoList;

  factory _$_ClassPagePopupList.fromJson(Map<String, dynamic> json) =>
      _$$_ClassPagePopupListFromJson(json);

  @override
  final String? showMode;
// 展示方式 popup(默认值)/page
  @override
  final String? bgColor;
// 背景色（可能为空，产品根据情况配置）
  @override
  final int? popupType;
  @override
  final int? occurrenceIndex;
  @override
  final String? popupTitle;
  @override
  final String? buttonText;
  @override
  final String? cancelButtonText;
  @override
  final String? teacherProfileUrl;
  @override
  final String? addTeacherUrl;
  @override
  final String? teacherName;
  @override
  final String? subTitle;
  @override
  final int? classId;
  @override
  final String? icon;
  @override
  final String? userCourseBusinessStatus;
  @override
  final String? subjectTypeDesc;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? template;
  @override
  final int? pattern;
  @override
  final String? courseSegment;
  @override
  final String? courseKey;
  final List<ClassPagePopupItem>? _coursePopupInfoList;
  @override
  List<ClassPagePopupItem>? get coursePopupInfoList {
    final value = _coursePopupInfoList;
    if (value == null) return null;
    if (_coursePopupInfoList is EqualUnmodifiableListView)
      return _coursePopupInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? limitFlow;
  @override
  final String? businessTypeDesc;

  @override
  String toString() {
    return 'ClassPagePopupList(showMode: $showMode, bgColor: $bgColor, popupType: $popupType, occurrenceIndex: $occurrenceIndex, popupTitle: $popupTitle, buttonText: $buttonText, cancelButtonText: $cancelButtonText, teacherProfileUrl: $teacherProfileUrl, addTeacherUrl: $addTeacherUrl, teacherName: $teacherName, subTitle: $subTitle, classId: $classId, icon: $icon, userCourseBusinessStatus: $userCourseBusinessStatus, subjectTypeDesc: $subjectTypeDesc, tags: $tags, template: $template, pattern: $pattern, courseSegment: $courseSegment, courseKey: $courseKey, coursePopupInfoList: $coursePopupInfoList, limitFlow: $limitFlow, businessTypeDesc: $businessTypeDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassPagePopupList &&
            (identical(other.showMode, showMode) ||
                other.showMode == showMode) &&
            (identical(other.bgColor, bgColor) || other.bgColor == bgColor) &&
            (identical(other.popupType, popupType) ||
                other.popupType == popupType) &&
            (identical(other.occurrenceIndex, occurrenceIndex) ||
                other.occurrenceIndex == occurrenceIndex) &&
            (identical(other.popupTitle, popupTitle) ||
                other.popupTitle == popupTitle) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.cancelButtonText, cancelButtonText) ||
                other.cancelButtonText == cancelButtonText) &&
            (identical(other.teacherProfileUrl, teacherProfileUrl) ||
                other.teacherProfileUrl == teacherProfileUrl) &&
            (identical(other.addTeacherUrl, addTeacherUrl) ||
                other.addTeacherUrl == addTeacherUrl) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(
                    other.userCourseBusinessStatus, userCourseBusinessStatus) ||
                other.userCourseBusinessStatus == userCourseBusinessStatus) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.template, template) ||
                other.template == template) &&
            (identical(other.pattern, pattern) || other.pattern == pattern) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            const DeepCollectionEquality()
                .equals(other._coursePopupInfoList, _coursePopupInfoList) &&
            (identical(other.limitFlow, limitFlow) ||
                other.limitFlow == limitFlow) &&
            (identical(other.businessTypeDesc, businessTypeDesc) ||
                other.businessTypeDesc == businessTypeDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        showMode,
        bgColor,
        popupType,
        occurrenceIndex,
        popupTitle,
        buttonText,
        cancelButtonText,
        teacherProfileUrl,
        addTeacherUrl,
        teacherName,
        subTitle,
        classId,
        icon,
        userCourseBusinessStatus,
        subjectTypeDesc,
        const DeepCollectionEquality().hash(_tags),
        template,
        pattern,
        courseSegment,
        courseKey,
        const DeepCollectionEquality().hash(_coursePopupInfoList),
        limitFlow,
        businessTypeDesc
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassPagePopupListCopyWith<_$_ClassPagePopupList> get copyWith =>
      __$$_ClassPagePopupListCopyWithImpl<_$_ClassPagePopupList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassPagePopupListToJson(
      this,
    );
  }
}

abstract class _ClassPagePopupList implements ClassPagePopupList {
  const factory _ClassPagePopupList(
      {final String? showMode,
      final String? bgColor,
      final int? popupType,
      final int? occurrenceIndex,
      final String? popupTitle,
      final String? buttonText,
      final String? cancelButtonText,
      final String? teacherProfileUrl,
      final String? addTeacherUrl,
      final String? teacherName,
      final String? subTitle,
      final int? classId,
      final String? icon,
      final String? userCourseBusinessStatus,
      final String? subjectTypeDesc,
      final List<String>? tags,
      final int? template,
      final int? pattern,
      final String? courseSegment,
      final String? courseKey,
      final List<ClassPagePopupItem>? coursePopupInfoList,
      final bool? limitFlow,
      final String? businessTypeDesc}) = _$_ClassPagePopupList;

  factory _ClassPagePopupList.fromJson(Map<String, dynamic> json) =
      _$_ClassPagePopupList.fromJson;

  @override
  String? get showMode;
  @override // 展示方式 popup(默认值)/page
  String? get bgColor;
  @override // 背景色（可能为空，产品根据情况配置）
  int? get popupType;
  @override
  int? get occurrenceIndex;
  @override
  String? get popupTitle;
  @override
  String? get buttonText;
  @override
  String? get cancelButtonText;
  @override
  String? get teacherProfileUrl;
  @override
  String? get addTeacherUrl;
  @override
  String? get teacherName;
  @override
  String? get subTitle;
  @override
  int? get classId;
  @override
  String? get icon;
  @override
  String? get userCourseBusinessStatus;
  @override
  String? get subjectTypeDesc;
  @override
  List<String>? get tags;
  @override
  int? get template;
  @override
  int? get pattern;
  @override
  String? get courseSegment;
  @override
  String? get courseKey;
  @override
  List<ClassPagePopupItem>? get coursePopupInfoList;
  @override
  bool? get limitFlow;
  @override
  String? get businessTypeDesc;
  @override
  @JsonKey(ignore: true)
  _$$_ClassPagePopupListCopyWith<_$_ClassPagePopupList> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassPagePopupItem _$ClassPagePopupItemFromJson(Map<String, dynamic> json) {
  return _ClassPagePopupItem.fromJson(json);
}

/// @nodoc
mixin _$ClassPagePopupItem {
  String? get courseLabel => throw _privateConstructorUsedError;
  String? get courseIcon => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassPagePopupItemCopyWith<ClassPagePopupItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassPagePopupItemCopyWith<$Res> {
  factory $ClassPagePopupItemCopyWith(
          ClassPagePopupItem value, $Res Function(ClassPagePopupItem) then) =
      _$ClassPagePopupItemCopyWithImpl<$Res, ClassPagePopupItem>;
  @useResult
  $Res call(
      {String? courseLabel,
      String? courseIcon,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      int? subjectType});
}

/// @nodoc
class _$ClassPagePopupItemCopyWithImpl<$Res, $Val extends ClassPagePopupItem>
    implements $ClassPagePopupItemCopyWith<$Res> {
  _$ClassPagePopupItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLabel = freezed,
    Object? courseIcon = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_value.copyWith(
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseIcon: freezed == courseIcon
          ? _value.courseIcon
          : courseIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassPagePopupItemCopyWith<$Res>
    implements $ClassPagePopupItemCopyWith<$Res> {
  factory _$$_ClassPagePopupItemCopyWith(_$_ClassPagePopupItem value,
          $Res Function(_$_ClassPagePopupItem) then) =
      __$$_ClassPagePopupItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseLabel,
      String? courseIcon,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      int? subjectType});
}

/// @nodoc
class __$$_ClassPagePopupItemCopyWithImpl<$Res>
    extends _$ClassPagePopupItemCopyWithImpl<$Res, _$_ClassPagePopupItem>
    implements _$$_ClassPagePopupItemCopyWith<$Res> {
  __$$_ClassPagePopupItemCopyWithImpl(
      _$_ClassPagePopupItem _value, $Res Function(_$_ClassPagePopupItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseLabel = freezed,
    Object? courseIcon = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? subjectType = freezed,
  }) {
    return _then(_$_ClassPagePopupItem(
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseIcon: freezed == courseIcon
          ? _value.courseIcon
          : courseIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassPagePopupItem implements _ClassPagePopupItem {
  const _$_ClassPagePopupItem(
      {this.courseLabel,
      this.courseIcon,
      this.courseId,
      this.courseKey,
      this.classId,
      this.classKey,
      this.subjectType});

  factory _$_ClassPagePopupItem.fromJson(Map<String, dynamic> json) =>
      _$$_ClassPagePopupItemFromJson(json);

  @override
  final String? courseLabel;
  @override
  final String? courseIcon;
  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final int? classId;
  @override
  final String? classKey;
  @override
  final int? subjectType;

  @override
  String toString() {
    return 'ClassPagePopupItem(courseLabel: $courseLabel, courseIcon: $courseIcon, courseId: $courseId, courseKey: $courseKey, classId: $classId, classKey: $classKey, subjectType: $subjectType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassPagePopupItem &&
            (identical(other.courseLabel, courseLabel) ||
                other.courseLabel == courseLabel) &&
            (identical(other.courseIcon, courseIcon) ||
                other.courseIcon == courseIcon) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseLabel, courseIcon,
      courseId, courseKey, classId, classKey, subjectType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassPagePopupItemCopyWith<_$_ClassPagePopupItem> get copyWith =>
      __$$_ClassPagePopupItemCopyWithImpl<_$_ClassPagePopupItem>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassPagePopupItemToJson(
      this,
    );
  }
}

abstract class _ClassPagePopupItem implements ClassPagePopupItem {
  const factory _ClassPagePopupItem(
      {final String? courseLabel,
      final String? courseIcon,
      final int? courseId,
      final String? courseKey,
      final int? classId,
      final String? classKey,
      final int? subjectType}) = _$_ClassPagePopupItem;

  factory _ClassPagePopupItem.fromJson(Map<String, dynamic> json) =
      _$_ClassPagePopupItem.fromJson;

  @override
  String? get courseLabel;
  @override
  String? get courseIcon;
  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  int? get subjectType;
  @override
  @JsonKey(ignore: true)
  _$$_ClassPagePopupItemCopyWith<_$_ClassPagePopupItem> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectClassList _$SubjectClassListFromJson(Map<String, dynamic> json) {
  return _SubjectClassList.fromJson(json);
}

/// @nodoc
mixin _$SubjectClassList {
  int? get subjectType => throw _privateConstructorUsedError;
  bool? get selected => throw _privateConstructorUsedError;
  int? get subjectStatus => throw _privateConstructorUsedError;
  List<BubbleList>? get bubbleList => throw _privateConstructorUsedError;
  List<UserClassVoList>? get userClassVoList =>
      throw _privateConstructorUsedError;
  bool? get own => throw _privateConstructorUsedError;
  SpecialCourseVo? get specialCourseVo => throw _privateConstructorUsedError;
  AfterServerEntranceVo? get afterServerEntranceVo =>
      throw _privateConstructorUsedError;
  GiftCourseVo? get giftCourseVo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectClassListCopyWith<SubjectClassList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectClassListCopyWith<$Res> {
  factory $SubjectClassListCopyWith(
          SubjectClassList value, $Res Function(SubjectClassList) then) =
      _$SubjectClassListCopyWithImpl<$Res, SubjectClassList>;
  @useResult
  $Res call(
      {int? subjectType,
      bool? selected,
      int? subjectStatus,
      List<BubbleList>? bubbleList,
      List<UserClassVoList>? userClassVoList,
      bool? own,
      SpecialCourseVo? specialCourseVo,
      AfterServerEntranceVo? afterServerEntranceVo,
      GiftCourseVo? giftCourseVo});

  $SpecialCourseVoCopyWith<$Res>? get specialCourseVo;
  $AfterServerEntranceVoCopyWith<$Res>? get afterServerEntranceVo;
  $GiftCourseVoCopyWith<$Res>? get giftCourseVo;
}

/// @nodoc
class _$SubjectClassListCopyWithImpl<$Res, $Val extends SubjectClassList>
    implements $SubjectClassListCopyWith<$Res> {
  _$SubjectClassListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? selected = freezed,
    Object? subjectStatus = freezed,
    Object? bubbleList = freezed,
    Object? userClassVoList = freezed,
    Object? own = freezed,
    Object? specialCourseVo = freezed,
    Object? afterServerEntranceVo = freezed,
    Object? giftCourseVo = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectStatus: freezed == subjectStatus
          ? _value.subjectStatus
          : subjectStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bubbleList: freezed == bubbleList
          ? _value.bubbleList
          : bubbleList // ignore: cast_nullable_to_non_nullable
              as List<BubbleList>?,
      userClassVoList: freezed == userClassVoList
          ? _value.userClassVoList
          : userClassVoList // ignore: cast_nullable_to_non_nullable
              as List<UserClassVoList>?,
      own: freezed == own
          ? _value.own
          : own // ignore: cast_nullable_to_non_nullable
              as bool?,
      specialCourseVo: freezed == specialCourseVo
          ? _value.specialCourseVo
          : specialCourseVo // ignore: cast_nullable_to_non_nullable
              as SpecialCourseVo?,
      afterServerEntranceVo: freezed == afterServerEntranceVo
          ? _value.afterServerEntranceVo
          : afterServerEntranceVo // ignore: cast_nullable_to_non_nullable
              as AfterServerEntranceVo?,
      giftCourseVo: freezed == giftCourseVo
          ? _value.giftCourseVo
          : giftCourseVo // ignore: cast_nullable_to_non_nullable
              as GiftCourseVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SpecialCourseVoCopyWith<$Res>? get specialCourseVo {
    if (_value.specialCourseVo == null) {
      return null;
    }

    return $SpecialCourseVoCopyWith<$Res>(_value.specialCourseVo!, (value) {
      return _then(_value.copyWith(specialCourseVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AfterServerEntranceVoCopyWith<$Res>? get afterServerEntranceVo {
    if (_value.afterServerEntranceVo == null) {
      return null;
    }

    return $AfterServerEntranceVoCopyWith<$Res>(_value.afterServerEntranceVo!,
        (value) {
      return _then(_value.copyWith(afterServerEntranceVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GiftCourseVoCopyWith<$Res>? get giftCourseVo {
    if (_value.giftCourseVo == null) {
      return null;
    }

    return $GiftCourseVoCopyWith<$Res>(_value.giftCourseVo!, (value) {
      return _then(_value.copyWith(giftCourseVo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_SubjectClassListCopyWith<$Res>
    implements $SubjectClassListCopyWith<$Res> {
  factory _$$_SubjectClassListCopyWith(
          _$_SubjectClassList value, $Res Function(_$_SubjectClassList) then) =
      __$$_SubjectClassListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      bool? selected,
      int? subjectStatus,
      List<BubbleList>? bubbleList,
      List<UserClassVoList>? userClassVoList,
      bool? own,
      SpecialCourseVo? specialCourseVo,
      AfterServerEntranceVo? afterServerEntranceVo,
      GiftCourseVo? giftCourseVo});

  @override
  $SpecialCourseVoCopyWith<$Res>? get specialCourseVo;
  @override
  $AfterServerEntranceVoCopyWith<$Res>? get afterServerEntranceVo;
  @override
  $GiftCourseVoCopyWith<$Res>? get giftCourseVo;
}

/// @nodoc
class __$$_SubjectClassListCopyWithImpl<$Res>
    extends _$SubjectClassListCopyWithImpl<$Res, _$_SubjectClassList>
    implements _$$_SubjectClassListCopyWith<$Res> {
  __$$_SubjectClassListCopyWithImpl(
      _$_SubjectClassList _value, $Res Function(_$_SubjectClassList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? selected = freezed,
    Object? subjectStatus = freezed,
    Object? bubbleList = freezed,
    Object? userClassVoList = freezed,
    Object? own = freezed,
    Object? specialCourseVo = freezed,
    Object? afterServerEntranceVo = freezed,
    Object? giftCourseVo = freezed,
  }) {
    return _then(_$_SubjectClassList(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      subjectStatus: freezed == subjectStatus
          ? _value.subjectStatus
          : subjectStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bubbleList: freezed == bubbleList
          ? _value._bubbleList
          : bubbleList // ignore: cast_nullable_to_non_nullable
              as List<BubbleList>?,
      userClassVoList: freezed == userClassVoList
          ? _value._userClassVoList
          : userClassVoList // ignore: cast_nullable_to_non_nullable
              as List<UserClassVoList>?,
      own: freezed == own
          ? _value.own
          : own // ignore: cast_nullable_to_non_nullable
              as bool?,
      specialCourseVo: freezed == specialCourseVo
          ? _value.specialCourseVo
          : specialCourseVo // ignore: cast_nullable_to_non_nullable
              as SpecialCourseVo?,
      afterServerEntranceVo: freezed == afterServerEntranceVo
          ? _value.afterServerEntranceVo
          : afterServerEntranceVo // ignore: cast_nullable_to_non_nullable
              as AfterServerEntranceVo?,
      giftCourseVo: freezed == giftCourseVo
          ? _value.giftCourseVo
          : giftCourseVo // ignore: cast_nullable_to_non_nullable
              as GiftCourseVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectClassList implements _SubjectClassList {
  const _$_SubjectClassList(
      {this.subjectType,
      this.selected,
      this.subjectStatus,
      final List<BubbleList>? bubbleList,
      final List<UserClassVoList>? userClassVoList,
      this.own,
      this.specialCourseVo,
      this.afterServerEntranceVo,
      this.giftCourseVo})
      : _bubbleList = bubbleList,
        _userClassVoList = userClassVoList;

  factory _$_SubjectClassList.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectClassListFromJson(json);

  @override
  final int? subjectType;
  @override
  final bool? selected;
  @override
  final int? subjectStatus;
  final List<BubbleList>? _bubbleList;
  @override
  List<BubbleList>? get bubbleList {
    final value = _bubbleList;
    if (value == null) return null;
    if (_bubbleList is EqualUnmodifiableListView) return _bubbleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<UserClassVoList>? _userClassVoList;
  @override
  List<UserClassVoList>? get userClassVoList {
    final value = _userClassVoList;
    if (value == null) return null;
    if (_userClassVoList is EqualUnmodifiableListView) return _userClassVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? own;
  @override
  final SpecialCourseVo? specialCourseVo;
  @override
  final AfterServerEntranceVo? afterServerEntranceVo;
  @override
  final GiftCourseVo? giftCourseVo;

  @override
  String toString() {
    return 'SubjectClassList(subjectType: $subjectType, selected: $selected, subjectStatus: $subjectStatus, bubbleList: $bubbleList, userClassVoList: $userClassVoList, own: $own, specialCourseVo: $specialCourseVo, afterServerEntranceVo: $afterServerEntranceVo, giftCourseVo: $giftCourseVo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectClassList &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.selected, selected) ||
                other.selected == selected) &&
            (identical(other.subjectStatus, subjectStatus) ||
                other.subjectStatus == subjectStatus) &&
            const DeepCollectionEquality()
                .equals(other._bubbleList, _bubbleList) &&
            const DeepCollectionEquality()
                .equals(other._userClassVoList, _userClassVoList) &&
            (identical(other.own, own) || other.own == own) &&
            (identical(other.specialCourseVo, specialCourseVo) ||
                other.specialCourseVo == specialCourseVo) &&
            (identical(other.afterServerEntranceVo, afterServerEntranceVo) ||
                other.afterServerEntranceVo == afterServerEntranceVo) &&
            (identical(other.giftCourseVo, giftCourseVo) ||
                other.giftCourseVo == giftCourseVo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      subjectType,
      selected,
      subjectStatus,
      const DeepCollectionEquality().hash(_bubbleList),
      const DeepCollectionEquality().hash(_userClassVoList),
      own,
      specialCourseVo,
      afterServerEntranceVo,
      giftCourseVo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectClassListCopyWith<_$_SubjectClassList> get copyWith =>
      __$$_SubjectClassListCopyWithImpl<_$_SubjectClassList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectClassListToJson(
      this,
    );
  }
}

abstract class _SubjectClassList implements SubjectClassList {
  const factory _SubjectClassList(
      {final int? subjectType,
      final bool? selected,
      final int? subjectStatus,
      final List<BubbleList>? bubbleList,
      final List<UserClassVoList>? userClassVoList,
      final bool? own,
      final SpecialCourseVo? specialCourseVo,
      final AfterServerEntranceVo? afterServerEntranceVo,
      final GiftCourseVo? giftCourseVo}) = _$_SubjectClassList;

  factory _SubjectClassList.fromJson(Map<String, dynamic> json) =
      _$_SubjectClassList.fromJson;

  @override
  int? get subjectType;
  @override
  bool? get selected;
  @override
  int? get subjectStatus;
  @override
  List<BubbleList>? get bubbleList;
  @override
  List<UserClassVoList>? get userClassVoList;
  @override
  bool? get own;
  @override
  SpecialCourseVo? get specialCourseVo;
  @override
  AfterServerEntranceVo? get afterServerEntranceVo;
  @override
  GiftCourseVo? get giftCourseVo;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectClassListCopyWith<_$_SubjectClassList> get copyWith =>
      throw _privateConstructorUsedError;
}

BubbleList _$BubbleListFromJson(Map<String, dynamic> json) {
  return _BubbleList.fromJson(json);
}

/// @nodoc
mixin _$BubbleList {
  int? get bubbleType => throw _privateConstructorUsedError;
  String? get bubble => throw _privateConstructorUsedError;
  int? get callBackType => throw _privateConstructorUsedError;
  List<int>? get classIdList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BubbleListCopyWith<BubbleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BubbleListCopyWith<$Res> {
  factory $BubbleListCopyWith(
          BubbleList value, $Res Function(BubbleList) then) =
      _$BubbleListCopyWithImpl<$Res, BubbleList>;
  @useResult
  $Res call(
      {int? bubbleType,
      String? bubble,
      int? callBackType,
      List<int>? classIdList});
}

/// @nodoc
class _$BubbleListCopyWithImpl<$Res, $Val extends BubbleList>
    implements $BubbleListCopyWith<$Res> {
  _$BubbleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bubbleType = freezed,
    Object? bubble = freezed,
    Object? callBackType = freezed,
    Object? classIdList = freezed,
  }) {
    return _then(_value.copyWith(
      bubbleType: freezed == bubbleType
          ? _value.bubbleType
          : bubbleType // ignore: cast_nullable_to_non_nullable
              as int?,
      bubble: freezed == bubble
          ? _value.bubble
          : bubble // ignore: cast_nullable_to_non_nullable
              as String?,
      callBackType: freezed == callBackType
          ? _value.callBackType
          : callBackType // ignore: cast_nullable_to_non_nullable
              as int?,
      classIdList: freezed == classIdList
          ? _value.classIdList
          : classIdList // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BubbleListCopyWith<$Res>
    implements $BubbleListCopyWith<$Res> {
  factory _$$_BubbleListCopyWith(
          _$_BubbleList value, $Res Function(_$_BubbleList) then) =
      __$$_BubbleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? bubbleType,
      String? bubble,
      int? callBackType,
      List<int>? classIdList});
}

/// @nodoc
class __$$_BubbleListCopyWithImpl<$Res>
    extends _$BubbleListCopyWithImpl<$Res, _$_BubbleList>
    implements _$$_BubbleListCopyWith<$Res> {
  __$$_BubbleListCopyWithImpl(
      _$_BubbleList _value, $Res Function(_$_BubbleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bubbleType = freezed,
    Object? bubble = freezed,
    Object? callBackType = freezed,
    Object? classIdList = freezed,
  }) {
    return _then(_$_BubbleList(
      bubbleType: freezed == bubbleType
          ? _value.bubbleType
          : bubbleType // ignore: cast_nullable_to_non_nullable
              as int?,
      bubble: freezed == bubble
          ? _value.bubble
          : bubble // ignore: cast_nullable_to_non_nullable
              as String?,
      callBackType: freezed == callBackType
          ? _value.callBackType
          : callBackType // ignore: cast_nullable_to_non_nullable
              as int?,
      classIdList: freezed == classIdList
          ? _value._classIdList
          : classIdList // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BubbleList implements _BubbleList {
  const _$_BubbleList(
      {this.bubbleType,
      this.bubble,
      this.callBackType,
      final List<int>? classIdList})
      : _classIdList = classIdList;

  factory _$_BubbleList.fromJson(Map<String, dynamic> json) =>
      _$$_BubbleListFromJson(json);

  @override
  final int? bubbleType;
  @override
  final String? bubble;
  @override
  final int? callBackType;
  final List<int>? _classIdList;
  @override
  List<int>? get classIdList {
    final value = _classIdList;
    if (value == null) return null;
    if (_classIdList is EqualUnmodifiableListView) return _classIdList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'BubbleList(bubbleType: $bubbleType, bubble: $bubble, callBackType: $callBackType, classIdList: $classIdList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BubbleList &&
            (identical(other.bubbleType, bubbleType) ||
                other.bubbleType == bubbleType) &&
            (identical(other.bubble, bubble) || other.bubble == bubble) &&
            (identical(other.callBackType, callBackType) ||
                other.callBackType == callBackType) &&
            const DeepCollectionEquality()
                .equals(other._classIdList, _classIdList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bubbleType, bubble, callBackType,
      const DeepCollectionEquality().hash(_classIdList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BubbleListCopyWith<_$_BubbleList> get copyWith =>
      __$$_BubbleListCopyWithImpl<_$_BubbleList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BubbleListToJson(
      this,
    );
  }
}

abstract class _BubbleList implements BubbleList {
  const factory _BubbleList(
      {final int? bubbleType,
      final String? bubble,
      final int? callBackType,
      final List<int>? classIdList}) = _$_BubbleList;

  factory _BubbleList.fromJson(Map<String, dynamic> json) =
      _$_BubbleList.fromJson;

  @override
  int? get bubbleType;
  @override
  String? get bubble;
  @override
  int? get callBackType;
  @override
  List<int>? get classIdList;
  @override
  @JsonKey(ignore: true)
  _$$_BubbleListCopyWith<_$_BubbleList> get copyWith =>
      throw _privateConstructorUsedError;
}

SpecialCourseVo _$SpecialCourseVoFromJson(Map<String, dynamic> json) {
  return _SpecialCourseVo.fromJson(json);
}

/// @nodoc
mixin _$SpecialCourseVo {
  String? get tabTitle => throw _privateConstructorUsedError;
  List<CourseInfoList>? get courseInfoList =>
      throw _privateConstructorUsedError;
  String? get bufferDesc => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialCourseVoCopyWith<SpecialCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialCourseVoCopyWith<$Res> {
  factory $SpecialCourseVoCopyWith(
          SpecialCourseVo value, $Res Function(SpecialCourseVo) then) =
      _$SpecialCourseVoCopyWithImpl<$Res, SpecialCourseVo>;
  @useResult
  $Res call(
      {String? tabTitle,
      List<CourseInfoList>? courseInfoList,
      String? bufferDesc});
}

/// @nodoc
class _$SpecialCourseVoCopyWithImpl<$Res, $Val extends SpecialCourseVo>
    implements $SpecialCourseVoCopyWith<$Res> {
  _$SpecialCourseVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? courseInfoList = freezed,
    Object? bufferDesc = freezed,
  }) {
    return _then(_value.copyWith(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseInfoList: freezed == courseInfoList
          ? _value.courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoList>?,
      bufferDesc: freezed == bufferDesc
          ? _value.bufferDesc
          : bufferDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SpecialCourseVoCopyWith<$Res>
    implements $SpecialCourseVoCopyWith<$Res> {
  factory _$$_SpecialCourseVoCopyWith(
          _$_SpecialCourseVo value, $Res Function(_$_SpecialCourseVo) then) =
      __$$_SpecialCourseVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tabTitle,
      List<CourseInfoList>? courseInfoList,
      String? bufferDesc});
}

/// @nodoc
class __$$_SpecialCourseVoCopyWithImpl<$Res>
    extends _$SpecialCourseVoCopyWithImpl<$Res, _$_SpecialCourseVo>
    implements _$$_SpecialCourseVoCopyWith<$Res> {
  __$$_SpecialCourseVoCopyWithImpl(
      _$_SpecialCourseVo _value, $Res Function(_$_SpecialCourseVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? courseInfoList = freezed,
    Object? bufferDesc = freezed,
  }) {
    return _then(_$_SpecialCourseVo(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseInfoList: freezed == courseInfoList
          ? _value._courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoList>?,
      bufferDesc: freezed == bufferDesc
          ? _value.bufferDesc
          : bufferDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SpecialCourseVo implements _SpecialCourseVo {
  const _$_SpecialCourseVo(
      {this.tabTitle,
      final List<CourseInfoList>? courseInfoList,
      this.bufferDesc})
      : _courseInfoList = courseInfoList;

  factory _$_SpecialCourseVo.fromJson(Map<String, dynamic> json) =>
      _$$_SpecialCourseVoFromJson(json);

  @override
  final String? tabTitle;
  final List<CourseInfoList>? _courseInfoList;
  @override
  List<CourseInfoList>? get courseInfoList {
    final value = _courseInfoList;
    if (value == null) return null;
    if (_courseInfoList is EqualUnmodifiableListView) return _courseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? bufferDesc;

  @override
  String toString() {
    return 'SpecialCourseVo(tabTitle: $tabTitle, courseInfoList: $courseInfoList, bufferDesc: $bufferDesc)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SpecialCourseVo &&
            (identical(other.tabTitle, tabTitle) ||
                other.tabTitle == tabTitle) &&
            const DeepCollectionEquality()
                .equals(other._courseInfoList, _courseInfoList) &&
            (identical(other.bufferDesc, bufferDesc) ||
                other.bufferDesc == bufferDesc));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tabTitle,
      const DeepCollectionEquality().hash(_courseInfoList), bufferDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SpecialCourseVoCopyWith<_$_SpecialCourseVo> get copyWith =>
      __$$_SpecialCourseVoCopyWithImpl<_$_SpecialCourseVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SpecialCourseVoToJson(
      this,
    );
  }
}

abstract class _SpecialCourseVo implements SpecialCourseVo {
  const factory _SpecialCourseVo(
      {final String? tabTitle,
      final List<CourseInfoList>? courseInfoList,
      final String? bufferDesc}) = _$_SpecialCourseVo;

  factory _SpecialCourseVo.fromJson(Map<String, dynamic> json) =
      _$_SpecialCourseVo.fromJson;

  @override
  String? get tabTitle;
  @override
  List<CourseInfoList>? get courseInfoList;
  @override
  String? get bufferDesc;
  @override
  @JsonKey(ignore: true)
  _$$_SpecialCourseVoCopyWith<_$_SpecialCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

GiftCourseVo _$GiftCourseVoFromJson(Map<String, dynamic> json) {
  return _GiftCourseVo.fromJson(json);
}

/// @nodoc
mixin _$GiftCourseVo {
  String? get tabTitle => throw _privateConstructorUsedError;
  List<CourseInfoList?>? get courseInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GiftCourseVoCopyWith<GiftCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftCourseVoCopyWith<$Res> {
  factory $GiftCourseVoCopyWith(
          GiftCourseVo value, $Res Function(GiftCourseVo) then) =
      _$GiftCourseVoCopyWithImpl<$Res, GiftCourseVo>;
  @useResult
  $Res call({String? tabTitle, List<CourseInfoList?>? courseInfoList});
}

/// @nodoc
class _$GiftCourseVoCopyWithImpl<$Res, $Val extends GiftCourseVo>
    implements $GiftCourseVoCopyWith<$Res> {
  _$GiftCourseVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? courseInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseInfoList: freezed == courseInfoList
          ? _value.courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoList?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GiftCourseVoCopyWith<$Res>
    implements $GiftCourseVoCopyWith<$Res> {
  factory _$$_GiftCourseVoCopyWith(
          _$_GiftCourseVo value, $Res Function(_$_GiftCourseVo) then) =
      __$$_GiftCourseVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? tabTitle, List<CourseInfoList?>? courseInfoList});
}

/// @nodoc
class __$$_GiftCourseVoCopyWithImpl<$Res>
    extends _$GiftCourseVoCopyWithImpl<$Res, _$_GiftCourseVo>
    implements _$$_GiftCourseVoCopyWith<$Res> {
  __$$_GiftCourseVoCopyWithImpl(
      _$_GiftCourseVo _value, $Res Function(_$_GiftCourseVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? courseInfoList = freezed,
  }) {
    return _then(_$_GiftCourseVo(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseInfoList: freezed == courseInfoList
          ? _value._courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoList?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GiftCourseVo implements _GiftCourseVo {
  const _$_GiftCourseVo(
      {this.tabTitle, final List<CourseInfoList?>? courseInfoList})
      : _courseInfoList = courseInfoList;

  factory _$_GiftCourseVo.fromJson(Map<String, dynamic> json) =>
      _$$_GiftCourseVoFromJson(json);

  @override
  final String? tabTitle;
  final List<CourseInfoList?>? _courseInfoList;
  @override
  List<CourseInfoList?>? get courseInfoList {
    final value = _courseInfoList;
    if (value == null) return null;
    if (_courseInfoList is EqualUnmodifiableListView) return _courseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GiftCourseVo(tabTitle: $tabTitle, courseInfoList: $courseInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GiftCourseVo &&
            (identical(other.tabTitle, tabTitle) ||
                other.tabTitle == tabTitle) &&
            const DeepCollectionEquality()
                .equals(other._courseInfoList, _courseInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tabTitle,
      const DeepCollectionEquality().hash(_courseInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GiftCourseVoCopyWith<_$_GiftCourseVo> get copyWith =>
      __$$_GiftCourseVoCopyWithImpl<_$_GiftCourseVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GiftCourseVoToJson(
      this,
    );
  }
}

abstract class _GiftCourseVo implements GiftCourseVo {
  const factory _GiftCourseVo(
      {final String? tabTitle,
      final List<CourseInfoList?>? courseInfoList}) = _$_GiftCourseVo;

  factory _GiftCourseVo.fromJson(Map<String, dynamic> json) =
      _$_GiftCourseVo.fromJson;

  @override
  String? get tabTitle;
  @override
  List<CourseInfoList?>? get courseInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_GiftCourseVoCopyWith<_$_GiftCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfoList _$CourseInfoListFromJson(Map<String, dynamic> json) {
  return _CourseInfoList.fromJson(json);
}

/// @nodoc
mixin _$CourseInfoList {
  String? get courseName => throw _privateConstructorUsedError; //课时名称(标签)
  String? get courseIcon => throw _privateConstructorUsedError; //课程icon
  int? get finishLessonNum => throw _privateConstructorUsedError; //完成课时数量
  int? get totalLessonNum => throw _privateConstructorUsedError; //总课时数量
  int? get courseId => throw _privateConstructorUsedError; //课程id
  String? get courseKey => throw _privateConstructorUsedError; //课程key
  int? get classId => throw _privateConstructorUsedError; //班级id
  String? get classKey => throw _privateConstructorUsedError; //班级key
  String? get route => throw _privateConstructorUsedError; //路由
  String? get color => throw _privateConstructorUsedError; //主色，卡片背景色
  int? get subjectType => throw _privateConstructorUsedError; //科目类型
  int? get mainClassStatus => throw _privateConstructorUsedError; //班级课程进行状态
  String? get mainCourseName => throw _privateConstructorUsedError; //主课课程名称(标签)
  String? get courseSegment => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get subjectTitle => throw _privateConstructorUsedError;
  String? get verticalCoverImg =>
      throw _privateConstructorUsedError; //竖屏封面用于专项课
  String? get landscapeCoverImg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoListCopyWith<CourseInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoListCopyWith<$Res> {
  factory $CourseInfoListCopyWith(
          CourseInfoList value, $Res Function(CourseInfoList) then) =
      _$CourseInfoListCopyWithImpl<$Res, CourseInfoList>;
  @useResult
  $Res call(
      {String? courseName,
      String? courseIcon,
      int? finishLessonNum,
      int? totalLessonNum,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      String? route,
      String? color,
      int? subjectType,
      int? mainClassStatus,
      String? mainCourseName,
      String? courseSegment,
      int? courseType,
      String? subjectTitle,
      String? verticalCoverImg,
      String? landscapeCoverImg});
}

/// @nodoc
class _$CourseInfoListCopyWithImpl<$Res, $Val extends CourseInfoList>
    implements $CourseInfoListCopyWith<$Res> {
  _$CourseInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseIcon = freezed,
    Object? finishLessonNum = freezed,
    Object? totalLessonNum = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? route = freezed,
    Object? color = freezed,
    Object? subjectType = freezed,
    Object? mainClassStatus = freezed,
    Object? mainCourseName = freezed,
    Object? courseSegment = freezed,
    Object? courseType = freezed,
    Object? subjectTitle = freezed,
    Object? verticalCoverImg = freezed,
    Object? landscapeCoverImg = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseIcon: freezed == courseIcon
          ? _value.courseIcon
          : courseIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      finishLessonNum: freezed == finishLessonNum
          ? _value.finishLessonNum
          : finishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      totalLessonNum: freezed == totalLessonNum
          ? _value.totalLessonNum
          : totalLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainClassStatus: freezed == mainClassStatus
          ? _value.mainClassStatus
          : mainClassStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      mainCourseName: freezed == mainCourseName
          ? _value.mainCourseName
          : mainCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTitle: freezed == subjectTitle
          ? _value.subjectTitle
          : subjectTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      verticalCoverImg: freezed == verticalCoverImg
          ? _value.verticalCoverImg
          : verticalCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapeCoverImg: freezed == landscapeCoverImg
          ? _value.landscapeCoverImg
          : landscapeCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoListCopyWith<$Res>
    implements $CourseInfoListCopyWith<$Res> {
  factory _$$_CourseInfoListCopyWith(
          _$_CourseInfoList value, $Res Function(_$_CourseInfoList) then) =
      __$$_CourseInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseName,
      String? courseIcon,
      int? finishLessonNum,
      int? totalLessonNum,
      int? courseId,
      String? courseKey,
      int? classId,
      String? classKey,
      String? route,
      String? color,
      int? subjectType,
      int? mainClassStatus,
      String? mainCourseName,
      String? courseSegment,
      int? courseType,
      String? subjectTitle,
      String? verticalCoverImg,
      String? landscapeCoverImg});
}

/// @nodoc
class __$$_CourseInfoListCopyWithImpl<$Res>
    extends _$CourseInfoListCopyWithImpl<$Res, _$_CourseInfoList>
    implements _$$_CourseInfoListCopyWith<$Res> {
  __$$_CourseInfoListCopyWithImpl(
      _$_CourseInfoList _value, $Res Function(_$_CourseInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? courseIcon = freezed,
    Object? finishLessonNum = freezed,
    Object? totalLessonNum = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? route = freezed,
    Object? color = freezed,
    Object? subjectType = freezed,
    Object? mainClassStatus = freezed,
    Object? mainCourseName = freezed,
    Object? courseSegment = freezed,
    Object? courseType = freezed,
    Object? subjectTitle = freezed,
    Object? verticalCoverImg = freezed,
    Object? landscapeCoverImg = freezed,
  }) {
    return _then(_$_CourseInfoList(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseIcon: freezed == courseIcon
          ? _value.courseIcon
          : courseIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      finishLessonNum: freezed == finishLessonNum
          ? _value.finishLessonNum
          : finishLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      totalLessonNum: freezed == totalLessonNum
          ? _value.totalLessonNum
          : totalLessonNum // ignore: cast_nullable_to_non_nullable
              as int?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainClassStatus: freezed == mainClassStatus
          ? _value.mainClassStatus
          : mainClassStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      mainCourseName: freezed == mainCourseName
          ? _value.mainCourseName
          : mainCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTitle: freezed == subjectTitle
          ? _value.subjectTitle
          : subjectTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      verticalCoverImg: freezed == verticalCoverImg
          ? _value.verticalCoverImg
          : verticalCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapeCoverImg: freezed == landscapeCoverImg
          ? _value.landscapeCoverImg
          : landscapeCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfoList implements _CourseInfoList {
  const _$_CourseInfoList(
      {this.courseName,
      this.courseIcon,
      this.finishLessonNum,
      this.totalLessonNum,
      this.courseId,
      this.courseKey,
      this.classId,
      this.classKey,
      this.route,
      this.color,
      this.subjectType,
      this.mainClassStatus,
      this.mainCourseName,
      this.courseSegment,
      this.courseType,
      this.subjectTitle,
      this.verticalCoverImg,
      this.landscapeCoverImg});

  factory _$_CourseInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoListFromJson(json);

  @override
  final String? courseName;
//课时名称(标签)
  @override
  final String? courseIcon;
//课程icon
  @override
  final int? finishLessonNum;
//完成课时数量
  @override
  final int? totalLessonNum;
//总课时数量
  @override
  final int? courseId;
//课程id
  @override
  final String? courseKey;
//课程key
  @override
  final int? classId;
//班级id
  @override
  final String? classKey;
//班级key
  @override
  final String? route;
//路由
  @override
  final String? color;
//主色，卡片背景色
  @override
  final int? subjectType;
//科目类型
  @override
  final int? mainClassStatus;
//班级课程进行状态
  @override
  final String? mainCourseName;
//主课课程名称(标签)
  @override
  final String? courseSegment;
  @override
  final int? courseType;
  @override
  final String? subjectTitle;
  @override
  final String? verticalCoverImg;
//竖屏封面用于专项课
  @override
  final String? landscapeCoverImg;

  @override
  String toString() {
    return 'CourseInfoList(courseName: $courseName, courseIcon: $courseIcon, finishLessonNum: $finishLessonNum, totalLessonNum: $totalLessonNum, courseId: $courseId, courseKey: $courseKey, classId: $classId, classKey: $classKey, route: $route, color: $color, subjectType: $subjectType, mainClassStatus: $mainClassStatus, mainCourseName: $mainCourseName, courseSegment: $courseSegment, courseType: $courseType, subjectTitle: $subjectTitle, verticalCoverImg: $verticalCoverImg, landscapeCoverImg: $landscapeCoverImg)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfoList &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseIcon, courseIcon) ||
                other.courseIcon == courseIcon) &&
            (identical(other.finishLessonNum, finishLessonNum) ||
                other.finishLessonNum == finishLessonNum) &&
            (identical(other.totalLessonNum, totalLessonNum) ||
                other.totalLessonNum == totalLessonNum) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.mainClassStatus, mainClassStatus) ||
                other.mainClassStatus == mainClassStatus) &&
            (identical(other.mainCourseName, mainCourseName) ||
                other.mainCourseName == mainCourseName) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.subjectTitle, subjectTitle) ||
                other.subjectTitle == subjectTitle) &&
            (identical(other.verticalCoverImg, verticalCoverImg) ||
                other.verticalCoverImg == verticalCoverImg) &&
            (identical(other.landscapeCoverImg, landscapeCoverImg) ||
                other.landscapeCoverImg == landscapeCoverImg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseName,
      courseIcon,
      finishLessonNum,
      totalLessonNum,
      courseId,
      courseKey,
      classId,
      classKey,
      route,
      color,
      subjectType,
      mainClassStatus,
      mainCourseName,
      courseSegment,
      courseType,
      subjectTitle,
      verticalCoverImg,
      landscapeCoverImg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoListCopyWith<_$_CourseInfoList> get copyWith =>
      __$$_CourseInfoListCopyWithImpl<_$_CourseInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoListToJson(
      this,
    );
  }
}

abstract class _CourseInfoList implements CourseInfoList {
  const factory _CourseInfoList(
      {final String? courseName,
      final String? courseIcon,
      final int? finishLessonNum,
      final int? totalLessonNum,
      final int? courseId,
      final String? courseKey,
      final int? classId,
      final String? classKey,
      final String? route,
      final String? color,
      final int? subjectType,
      final int? mainClassStatus,
      final String? mainCourseName,
      final String? courseSegment,
      final int? courseType,
      final String? subjectTitle,
      final String? verticalCoverImg,
      final String? landscapeCoverImg}) = _$_CourseInfoList;

  factory _CourseInfoList.fromJson(Map<String, dynamic> json) =
      _$_CourseInfoList.fromJson;

  @override
  String? get courseName;
  @override //课时名称(标签)
  String? get courseIcon;
  @override //课程icon
  int? get finishLessonNum;
  @override //完成课时数量
  int? get totalLessonNum;
  @override //总课时数量
  int? get courseId;
  @override //课程id
  String? get courseKey;
  @override //课程key
  int? get classId;
  @override //班级id
  String? get classKey;
  @override //班级key
  String? get route;
  @override //路由
  String? get color;
  @override //主色，卡片背景色
  int? get subjectType;
  @override //科目类型
  int? get mainClassStatus;
  @override //班级课程进行状态
  String? get mainCourseName;
  @override //主课课程名称(标签)
  String? get courseSegment;
  @override
  int? get courseType;
  @override
  String? get subjectTitle;
  @override
  String? get verticalCoverImg;
  @override //竖屏封面用于专项课
  String? get landscapeCoverImg;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoListCopyWith<_$_CourseInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

UserClassVoList _$UserClassVoListFromJson(Map<String, dynamic> json) {
  return _UserClassVoList.fromJson(json);
}

/// @nodoc
mixin _$UserClassVoList {
  String? get tabTitle => throw _privateConstructorUsedError;
  set tabTitle(String? value) => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  set classKey(String? value) => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  set courseId(int? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError;
  int? get classStatus => throw _privateConstructorUsedError;
  set classStatus(int? value) => throw _privateConstructorUsedError;
  List<BubbleList>? get bubbleList => throw _privateConstructorUsedError;
  set bubbleList(List<BubbleList>? value) => throw _privateConstructorUsedError;
  int? get startClassTime => throw _privateConstructorUsedError;
  set startClassTime(int? value) => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  set courseSegment(String? value) => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  set courseType(int? value) => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;
  set courseSegmentCode(int? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserClassVoListCopyWith<UserClassVoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserClassVoListCopyWith<$Res> {
  factory $UserClassVoListCopyWith(
          UserClassVoList value, $Res Function(UserClassVoList) then) =
      _$UserClassVoListCopyWithImpl<$Res, UserClassVoList>;
  @useResult
  $Res call(
      {String? tabTitle,
      int? classId,
      String? classKey,
      int? courseId,
      String? courseKey,
      int? classStatus,
      List<BubbleList>? bubbleList,
      int? startClassTime,
      String? courseSegment,
      int? courseType,
      int? courseSegmentCode});
}

/// @nodoc
class _$UserClassVoListCopyWithImpl<$Res, $Val extends UserClassVoList>
    implements $UserClassVoListCopyWith<$Res> {
  _$UserClassVoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classStatus = freezed,
    Object? bubbleList = freezed,
    Object? startClassTime = freezed,
    Object? courseSegment = freezed,
    Object? courseType = freezed,
    Object? courseSegmentCode = freezed,
  }) {
    return _then(_value.copyWith(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bubbleList: freezed == bubbleList
          ? _value.bubbleList
          : bubbleList // ignore: cast_nullable_to_non_nullable
              as List<BubbleList>?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserClassVoListCopyWith<$Res>
    implements $UserClassVoListCopyWith<$Res> {
  factory _$$_UserClassVoListCopyWith(
          _$_UserClassVoList value, $Res Function(_$_UserClassVoList) then) =
      __$$_UserClassVoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tabTitle,
      int? classId,
      String? classKey,
      int? courseId,
      String? courseKey,
      int? classStatus,
      List<BubbleList>? bubbleList,
      int? startClassTime,
      String? courseSegment,
      int? courseType,
      int? courseSegmentCode});
}

/// @nodoc
class __$$_UserClassVoListCopyWithImpl<$Res>
    extends _$UserClassVoListCopyWithImpl<$Res, _$_UserClassVoList>
    implements _$$_UserClassVoListCopyWith<$Res> {
  __$$_UserClassVoListCopyWithImpl(
      _$_UserClassVoList _value, $Res Function(_$_UserClassVoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tabTitle = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? classStatus = freezed,
    Object? bubbleList = freezed,
    Object? startClassTime = freezed,
    Object? courseSegment = freezed,
    Object? courseType = freezed,
    Object? courseSegmentCode = freezed,
  }) {
    return _then(_$_UserClassVoList(
      tabTitle: freezed == tabTitle
          ? _value.tabTitle
          : tabTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      bubbleList: freezed == bubbleList
          ? _value.bubbleList
          : bubbleList // ignore: cast_nullable_to_non_nullable
              as List<BubbleList>?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserClassVoList implements _UserClassVoList {
  _$_UserClassVoList(
      {this.tabTitle,
      this.classId,
      this.classKey,
      this.courseId,
      this.courseKey,
      this.classStatus,
      this.bubbleList,
      this.startClassTime,
      this.courseSegment,
      this.courseType,
      this.courseSegmentCode});

  factory _$_UserClassVoList.fromJson(Map<String, dynamic> json) =>
      _$$_UserClassVoListFromJson(json);

  @override
  String? tabTitle;
  @override
  int? classId;
  @override
  String? classKey;
  @override
  int? courseId;
  @override
  String? courseKey;
  @override
  int? classStatus;
  @override
  List<BubbleList>? bubbleList;
  @override
  int? startClassTime;
  @override
  String? courseSegment;
  @override
  int? courseType;
  @override
  int? courseSegmentCode;

  @override
  String toString() {
    return 'UserClassVoList(tabTitle: $tabTitle, classId: $classId, classKey: $classKey, courseId: $courseId, courseKey: $courseKey, classStatus: $classStatus, bubbleList: $bubbleList, startClassTime: $startClassTime, courseSegment: $courseSegment, courseType: $courseType, courseSegmentCode: $courseSegmentCode)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserClassVoListCopyWith<_$_UserClassVoList> get copyWith =>
      __$$_UserClassVoListCopyWithImpl<_$_UserClassVoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserClassVoListToJson(
      this,
    );
  }
}

abstract class _UserClassVoList implements UserClassVoList {
  factory _UserClassVoList(
      {String? tabTitle,
      int? classId,
      String? classKey,
      int? courseId,
      String? courseKey,
      int? classStatus,
      List<BubbleList>? bubbleList,
      int? startClassTime,
      String? courseSegment,
      int? courseType,
      int? courseSegmentCode}) = _$_UserClassVoList;

  factory _UserClassVoList.fromJson(Map<String, dynamic> json) =
      _$_UserClassVoList.fromJson;

  @override
  String? get tabTitle;
  set tabTitle(String? value);
  @override
  int? get classId;
  set classId(int? value);
  @override
  String? get classKey;
  set classKey(String? value);
  @override
  int? get courseId;
  set courseId(int? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override
  int? get classStatus;
  set classStatus(int? value);
  @override
  List<BubbleList>? get bubbleList;
  set bubbleList(List<BubbleList>? value);
  @override
  int? get startClassTime;
  set startClassTime(int? value);
  @override
  String? get courseSegment;
  set courseSegment(String? value);
  @override
  int? get courseType;
  set courseType(int? value);
  @override
  int? get courseSegmentCode;
  set courseSegmentCode(int? value);
  @override
  @JsonKey(ignore: true)
  _$$_UserClassVoListCopyWith<_$_UserClassVoList> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectExtendVo _$SubjectExtendVoFromJson(Map<String, dynamic> json) {
  return _SubjectExtendVo.fromJson(json);
}

/// @nodoc
mixin _$SubjectExtendVo {
  String? get bufferText => throw _privateConstructorUsedError;
  String? get bufferRouter => throw _privateConstructorUsedError;
  String? get noServiceText => throw _privateConstructorUsedError;
  String? get noServiceTitle => throw _privateConstructorUsedError;
  String? get noServiceRouter => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectExtendVoCopyWith<SubjectExtendVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectExtendVoCopyWith<$Res> {
  factory $SubjectExtendVoCopyWith(
          SubjectExtendVo value, $Res Function(SubjectExtendVo) then) =
      _$SubjectExtendVoCopyWithImpl<$Res, SubjectExtendVo>;
  @useResult
  $Res call(
      {String? bufferText,
      String? bufferRouter,
      String? noServiceText,
      String? noServiceTitle,
      String? noServiceRouter,
      String? buttonText});
}

/// @nodoc
class _$SubjectExtendVoCopyWithImpl<$Res, $Val extends SubjectExtendVo>
    implements $SubjectExtendVoCopyWith<$Res> {
  _$SubjectExtendVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bufferText = freezed,
    Object? bufferRouter = freezed,
    Object? noServiceText = freezed,
    Object? noServiceTitle = freezed,
    Object? noServiceRouter = freezed,
    Object? buttonText = freezed,
  }) {
    return _then(_value.copyWith(
      bufferText: freezed == bufferText
          ? _value.bufferText
          : bufferText // ignore: cast_nullable_to_non_nullable
              as String?,
      bufferRouter: freezed == bufferRouter
          ? _value.bufferRouter
          : bufferRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceText: freezed == noServiceText
          ? _value.noServiceText
          : noServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceTitle: freezed == noServiceTitle
          ? _value.noServiceTitle
          : noServiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceRouter: freezed == noServiceRouter
          ? _value.noServiceRouter
          : noServiceRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectExtendVoCopyWith<$Res>
    implements $SubjectExtendVoCopyWith<$Res> {
  factory _$$_SubjectExtendVoCopyWith(
          _$_SubjectExtendVo value, $Res Function(_$_SubjectExtendVo) then) =
      __$$_SubjectExtendVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? bufferText,
      String? bufferRouter,
      String? noServiceText,
      String? noServiceTitle,
      String? noServiceRouter,
      String? buttonText});
}

/// @nodoc
class __$$_SubjectExtendVoCopyWithImpl<$Res>
    extends _$SubjectExtendVoCopyWithImpl<$Res, _$_SubjectExtendVo>
    implements _$$_SubjectExtendVoCopyWith<$Res> {
  __$$_SubjectExtendVoCopyWithImpl(
      _$_SubjectExtendVo _value, $Res Function(_$_SubjectExtendVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bufferText = freezed,
    Object? bufferRouter = freezed,
    Object? noServiceText = freezed,
    Object? noServiceTitle = freezed,
    Object? noServiceRouter = freezed,
    Object? buttonText = freezed,
  }) {
    return _then(_$_SubjectExtendVo(
      bufferText: freezed == bufferText
          ? _value.bufferText
          : bufferText // ignore: cast_nullable_to_non_nullable
              as String?,
      bufferRouter: freezed == bufferRouter
          ? _value.bufferRouter
          : bufferRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceText: freezed == noServiceText
          ? _value.noServiceText
          : noServiceText // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceTitle: freezed == noServiceTitle
          ? _value.noServiceTitle
          : noServiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      noServiceRouter: freezed == noServiceRouter
          ? _value.noServiceRouter
          : noServiceRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectExtendVo implements _SubjectExtendVo {
  const _$_SubjectExtendVo(
      {this.bufferText,
      this.bufferRouter,
      this.noServiceText,
      this.noServiceTitle,
      this.noServiceRouter,
      this.buttonText});

  factory _$_SubjectExtendVo.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectExtendVoFromJson(json);

  @override
  final String? bufferText;
  @override
  final String? bufferRouter;
  @override
  final String? noServiceText;
  @override
  final String? noServiceTitle;
  @override
  final String? noServiceRouter;
  @override
  final String? buttonText;

  @override
  String toString() {
    return 'SubjectExtendVo(bufferText: $bufferText, bufferRouter: $bufferRouter, noServiceText: $noServiceText, noServiceTitle: $noServiceTitle, noServiceRouter: $noServiceRouter, buttonText: $buttonText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectExtendVo &&
            (identical(other.bufferText, bufferText) ||
                other.bufferText == bufferText) &&
            (identical(other.bufferRouter, bufferRouter) ||
                other.bufferRouter == bufferRouter) &&
            (identical(other.noServiceText, noServiceText) ||
                other.noServiceText == noServiceText) &&
            (identical(other.noServiceTitle, noServiceTitle) ||
                other.noServiceTitle == noServiceTitle) &&
            (identical(other.noServiceRouter, noServiceRouter) ||
                other.noServiceRouter == noServiceRouter) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bufferText, bufferRouter,
      noServiceText, noServiceTitle, noServiceRouter, buttonText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectExtendVoCopyWith<_$_SubjectExtendVo> get copyWith =>
      __$$_SubjectExtendVoCopyWithImpl<_$_SubjectExtendVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectExtendVoToJson(
      this,
    );
  }
}

abstract class _SubjectExtendVo implements SubjectExtendVo {
  const factory _SubjectExtendVo(
      {final String? bufferText,
      final String? bufferRouter,
      final String? noServiceText,
      final String? noServiceTitle,
      final String? noServiceRouter,
      final String? buttonText}) = _$_SubjectExtendVo;

  factory _SubjectExtendVo.fromJson(Map<String, dynamic> json) =
      _$_SubjectExtendVo.fromJson;

  @override
  String? get bufferText;
  @override
  String? get bufferRouter;
  @override
  String? get noServiceText;
  @override
  String? get noServiceTitle;
  @override
  String? get noServiceRouter;
  @override
  String? get buttonText;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectExtendVoCopyWith<_$_SubjectExtendVo> get copyWith =>
      throw _privateConstructorUsedError;
}

SubjectList _$SubjectListFromJson(Map<String, dynamic> json) {
  return _SubjectList.fromJson(json);
}

/// @nodoc
mixin _$SubjectList {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;
  int? get loadingScene => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectListCopyWith<SubjectList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectListCopyWith<$Res> {
  factory $SubjectListCopyWith(
          SubjectList value, $Res Function(SubjectList) then) =
      _$SubjectListCopyWithImpl<$Res, SubjectList>;
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectName,
      String? subjectColor,
      int? loadingScene});
}

/// @nodoc
class _$SubjectListCopyWithImpl<$Res, $Val extends SubjectList>
    implements $SubjectListCopyWith<$Res> {
  _$SubjectListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? subjectColor = freezed,
    Object? loadingScene = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      loadingScene: freezed == loadingScene
          ? _value.loadingScene
          : loadingScene // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectListCopyWith<$Res>
    implements $SubjectListCopyWith<$Res> {
  factory _$$_SubjectListCopyWith(
          _$_SubjectList value, $Res Function(_$_SubjectList) then) =
      __$$_SubjectListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? subjectType,
      String? subjectName,
      String? subjectColor,
      int? loadingScene});
}

/// @nodoc
class __$$_SubjectListCopyWithImpl<$Res>
    extends _$SubjectListCopyWithImpl<$Res, _$_SubjectList>
    implements _$$_SubjectListCopyWith<$Res> {
  __$$_SubjectListCopyWithImpl(
      _$_SubjectList _value, $Res Function(_$_SubjectList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? subjectColor = freezed,
    Object? loadingScene = freezed,
  }) {
    return _then(_$_SubjectList(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      loadingScene: freezed == loadingScene
          ? _value.loadingScene
          : loadingScene // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectList implements _SubjectList {
  const _$_SubjectList(
      {this.subjectType,
      this.subjectName,
      this.subjectColor,
      this.loadingScene});

  factory _$_SubjectList.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectListFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectName;
  @override
  final String? subjectColor;
  @override
  final int? loadingScene;

  @override
  String toString() {
    return 'SubjectList(subjectType: $subjectType, subjectName: $subjectName, subjectColor: $subjectColor, loadingScene: $loadingScene)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectList &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            (identical(other.loadingScene, loadingScene) ||
                other.loadingScene == loadingScene));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, subjectType, subjectName, subjectColor, loadingScene);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectListCopyWith<_$_SubjectList> get copyWith =>
      __$$_SubjectListCopyWithImpl<_$_SubjectList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectListToJson(
      this,
    );
  }
}

abstract class _SubjectList implements SubjectList {
  const factory _SubjectList(
      {final int? subjectType,
      final String? subjectName,
      final String? subjectColor,
      final int? loadingScene}) = _$_SubjectList;

  factory _SubjectList.fromJson(Map<String, dynamic> json) =
      _$_SubjectList.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectName;
  @override
  String? get subjectColor;
  @override
  int? get loadingScene;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectListCopyWith<_$_SubjectList> get copyWith =>
      throw _privateConstructorUsedError;
}
