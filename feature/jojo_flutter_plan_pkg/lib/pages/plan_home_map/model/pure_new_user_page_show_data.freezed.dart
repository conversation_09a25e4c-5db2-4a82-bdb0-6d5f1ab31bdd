// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pure_new_user_page_show_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PureNewUserPageShowData _$PureNewUserPageShowDataFromJson(
    Map<String, dynamic> json) {
  return _PureNewUserPageShowData.fromJson(json);
}

/// @nodoc
mixin _$PureNewUserPageShowData {
  String? get mainPictureUrl => throw _privateConstructorUsedError;
  String? get mainButtonLinkUrl => throw _privateConstructorUsedError;
  String? get mainButtonDesc => throw _privateConstructorUsedError;
  String? get subButtonDesc => throw _privateConstructorUsedError;
  String? get subButtonLinkUrl => throw _privateConstructorUsedError;
  List<GrayList>? get grayList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PureNewUserPageShowDataCopyWith<PureNewUserPageShowData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PureNewUserPageShowDataCopyWith<$Res> {
  factory $PureNewUserPageShowDataCopyWith(PureNewUserPageShowData value,
          $Res Function(PureNewUserPageShowData) then) =
      _$PureNewUserPageShowDataCopyWithImpl<$Res, PureNewUserPageShowData>;
  @useResult
  $Res call(
      {String? mainPictureUrl,
      String? mainButtonLinkUrl,
      String? mainButtonDesc,
      String? subButtonDesc,
      String? subButtonLinkUrl,
      List<GrayList>? grayList});
}

/// @nodoc
class _$PureNewUserPageShowDataCopyWithImpl<$Res,
        $Val extends PureNewUserPageShowData>
    implements $PureNewUserPageShowDataCopyWith<$Res> {
  _$PureNewUserPageShowDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainPictureUrl = freezed,
    Object? mainButtonLinkUrl = freezed,
    Object? mainButtonDesc = freezed,
    Object? subButtonDesc = freezed,
    Object? subButtonLinkUrl = freezed,
    Object? grayList = freezed,
  }) {
    return _then(_value.copyWith(
      mainPictureUrl: freezed == mainPictureUrl
          ? _value.mainPictureUrl
          : mainPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainButtonLinkUrl: freezed == mainButtonLinkUrl
          ? _value.mainButtonLinkUrl
          : mainButtonLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainButtonDesc: freezed == mainButtonDesc
          ? _value.mainButtonDesc
          : mainButtonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subButtonDesc: freezed == subButtonDesc
          ? _value.subButtonDesc
          : subButtonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subButtonLinkUrl: freezed == subButtonLinkUrl
          ? _value.subButtonLinkUrl
          : subButtonLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      grayList: freezed == grayList
          ? _value.grayList
          : grayList // ignore: cast_nullable_to_non_nullable
              as List<GrayList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PureNewUserPageShowDataCopyWith<$Res>
    implements $PureNewUserPageShowDataCopyWith<$Res> {
  factory _$$_PureNewUserPageShowDataCopyWith(_$_PureNewUserPageShowData value,
          $Res Function(_$_PureNewUserPageShowData) then) =
      __$$_PureNewUserPageShowDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? mainPictureUrl,
      String? mainButtonLinkUrl,
      String? mainButtonDesc,
      String? subButtonDesc,
      String? subButtonLinkUrl,
      List<GrayList>? grayList});
}

/// @nodoc
class __$$_PureNewUserPageShowDataCopyWithImpl<$Res>
    extends _$PureNewUserPageShowDataCopyWithImpl<$Res,
        _$_PureNewUserPageShowData>
    implements _$$_PureNewUserPageShowDataCopyWith<$Res> {
  __$$_PureNewUserPageShowDataCopyWithImpl(_$_PureNewUserPageShowData _value,
      $Res Function(_$_PureNewUserPageShowData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainPictureUrl = freezed,
    Object? mainButtonLinkUrl = freezed,
    Object? mainButtonDesc = freezed,
    Object? subButtonDesc = freezed,
    Object? subButtonLinkUrl = freezed,
    Object? grayList = freezed,
  }) {
    return _then(_$_PureNewUserPageShowData(
      mainPictureUrl: freezed == mainPictureUrl
          ? _value.mainPictureUrl
          : mainPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainButtonLinkUrl: freezed == mainButtonLinkUrl
          ? _value.mainButtonLinkUrl
          : mainButtonLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainButtonDesc: freezed == mainButtonDesc
          ? _value.mainButtonDesc
          : mainButtonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subButtonDesc: freezed == subButtonDesc
          ? _value.subButtonDesc
          : subButtonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subButtonLinkUrl: freezed == subButtonLinkUrl
          ? _value.subButtonLinkUrl
          : subButtonLinkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      grayList: freezed == grayList
          ? _value._grayList
          : grayList // ignore: cast_nullable_to_non_nullable
              as List<GrayList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PureNewUserPageShowData implements _PureNewUserPageShowData {
  const _$_PureNewUserPageShowData(
      {this.mainPictureUrl,
      this.mainButtonLinkUrl,
      this.mainButtonDesc,
      this.subButtonDesc,
      this.subButtonLinkUrl,
      final List<GrayList>? grayList})
      : _grayList = grayList;

  factory _$_PureNewUserPageShowData.fromJson(Map<String, dynamic> json) =>
      _$$_PureNewUserPageShowDataFromJson(json);

  @override
  final String? mainPictureUrl;
  @override
  final String? mainButtonLinkUrl;
  @override
  final String? mainButtonDesc;
  @override
  final String? subButtonDesc;
  @override
  final String? subButtonLinkUrl;
  final List<GrayList>? _grayList;
  @override
  List<GrayList>? get grayList {
    final value = _grayList;
    if (value == null) return null;
    if (_grayList is EqualUnmodifiableListView) return _grayList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PureNewUserPageShowData(mainPictureUrl: $mainPictureUrl, mainButtonLinkUrl: $mainButtonLinkUrl, mainButtonDesc: $mainButtonDesc, subButtonDesc: $subButtonDesc, subButtonLinkUrl: $subButtonLinkUrl, grayList: $grayList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PureNewUserPageShowData &&
            (identical(other.mainPictureUrl, mainPictureUrl) ||
                other.mainPictureUrl == mainPictureUrl) &&
            (identical(other.mainButtonLinkUrl, mainButtonLinkUrl) ||
                other.mainButtonLinkUrl == mainButtonLinkUrl) &&
            (identical(other.mainButtonDesc, mainButtonDesc) ||
                other.mainButtonDesc == mainButtonDesc) &&
            (identical(other.subButtonDesc, subButtonDesc) ||
                other.subButtonDesc == subButtonDesc) &&
            (identical(other.subButtonLinkUrl, subButtonLinkUrl) ||
                other.subButtonLinkUrl == subButtonLinkUrl) &&
            const DeepCollectionEquality().equals(other._grayList, _grayList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      mainPictureUrl,
      mainButtonLinkUrl,
      mainButtonDesc,
      subButtonDesc,
      subButtonLinkUrl,
      const DeepCollectionEquality().hash(_grayList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PureNewUserPageShowDataCopyWith<_$_PureNewUserPageShowData>
      get copyWith =>
          __$$_PureNewUserPageShowDataCopyWithImpl<_$_PureNewUserPageShowData>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PureNewUserPageShowDataToJson(
      this,
    );
  }
}

abstract class _PureNewUserPageShowData implements PureNewUserPageShowData {
  const factory _PureNewUserPageShowData(
      {final String? mainPictureUrl,
      final String? mainButtonLinkUrl,
      final String? mainButtonDesc,
      final String? subButtonDesc,
      final String? subButtonLinkUrl,
      final List<GrayList>? grayList}) = _$_PureNewUserPageShowData;

  factory _PureNewUserPageShowData.fromJson(Map<String, dynamic> json) =
      _$_PureNewUserPageShowData.fromJson;

  @override
  String? get mainPictureUrl;
  @override
  String? get mainButtonLinkUrl;
  @override
  String? get mainButtonDesc;
  @override
  String? get subButtonDesc;
  @override
  String? get subButtonLinkUrl;
  @override
  List<GrayList>? get grayList;
  @override
  @JsonKey(ignore: true)
  _$$_PureNewUserPageShowDataCopyWith<_$_PureNewUserPageShowData>
      get copyWith => throw _privateConstructorUsedError;
}

GrayList _$GrayListFromJson(Map<String, dynamic> json) {
  return _GrayList.fromJson(json);
}

/// @nodoc
mixin _$GrayList {
  bool? get grayCover => throw _privateConstructorUsedError;
  bool? get grayHit => throw _privateConstructorUsedError;
  String? get grayScaleId => throw _privateConstructorUsedError;
  int? get testValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrayListCopyWith<GrayList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrayListCopyWith<$Res> {
  factory $GrayListCopyWith(GrayList value, $Res Function(GrayList) then) =
      _$GrayListCopyWithImpl<$Res, GrayList>;
  @useResult
  $Res call(
      {bool? grayCover, bool? grayHit, String? grayScaleId, int? testValue});
}

/// @nodoc
class _$GrayListCopyWithImpl<$Res, $Val extends GrayList>
    implements $GrayListCopyWith<$Res> {
  _$GrayListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_value.copyWith(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GrayListCopyWith<$Res> implements $GrayListCopyWith<$Res> {
  factory _$$_GrayListCopyWith(
          _$_GrayList value, $Res Function(_$_GrayList) then) =
      __$$_GrayListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? grayCover, bool? grayHit, String? grayScaleId, int? testValue});
}

/// @nodoc
class __$$_GrayListCopyWithImpl<$Res>
    extends _$GrayListCopyWithImpl<$Res, _$_GrayList>
    implements _$$_GrayListCopyWith<$Res> {
  __$$_GrayListCopyWithImpl(
      _$_GrayList _value, $Res Function(_$_GrayList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
    Object? grayScaleId = freezed,
    Object? testValue = freezed,
  }) {
    return _then(_$_GrayList(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayScaleId: freezed == grayScaleId
          ? _value.grayScaleId
          : grayScaleId // ignore: cast_nullable_to_non_nullable
              as String?,
      testValue: freezed == testValue
          ? _value.testValue
          : testValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GrayList implements _GrayList {
  const _$_GrayList(
      {this.grayCover, this.grayHit, this.grayScaleId, this.testValue});

  factory _$_GrayList.fromJson(Map<String, dynamic> json) =>
      _$$_GrayListFromJson(json);

  @override
  final bool? grayCover;
  @override
  final bool? grayHit;
  @override
  final String? grayScaleId;
  @override
  final int? testValue;

  @override
  String toString() {
    return 'GrayList(grayCover: $grayCover, grayHit: $grayHit, grayScaleId: $grayScaleId, testValue: $testValue)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GrayList &&
            (identical(other.grayCover, grayCover) ||
                other.grayCover == grayCover) &&
            (identical(other.grayHit, grayHit) || other.grayHit == grayHit) &&
            (identical(other.grayScaleId, grayScaleId) ||
                other.grayScaleId == grayScaleId) &&
            (identical(other.testValue, testValue) ||
                other.testValue == testValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, grayCover, grayHit, grayScaleId, testValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GrayListCopyWith<_$_GrayList> get copyWith =>
      __$$_GrayListCopyWithImpl<_$_GrayList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GrayListToJson(
      this,
    );
  }
}

abstract class _GrayList implements GrayList {
  const factory _GrayList(
      {final bool? grayCover,
      final bool? grayHit,
      final String? grayScaleId,
      final int? testValue}) = _$_GrayList;

  factory _GrayList.fromJson(Map<String, dynamic> json) = _$_GrayList.fromJson;

  @override
  bool? get grayCover;
  @override
  bool? get grayHit;
  @override
  String? get grayScaleId;
  @override
  int? get testValue;
  @override
  @JsonKey(ignore: true)
  _$$_GrayListCopyWith<_$_GrayList> get copyWith =>
      throw _privateConstructorUsedError;
}
