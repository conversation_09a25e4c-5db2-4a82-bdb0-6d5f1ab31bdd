// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pure_new_user_page_show_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PureNewUserPageShowData _$$_PureNewUserPageShowDataFromJson(
        Map<String, dynamic> json) =>
    _$_PureNewUserPageShowData(
      mainPictureUrl: json['mainPictureUrl'] as String?,
      mainButtonLinkUrl: json['mainButtonLinkUrl'] as String?,
      mainButtonDesc: json['mainButtonDesc'] as String?,
      subButtonDesc: json['subButtonDesc'] as String?,
      subButtonLinkUrl: json['subButtonLinkUrl'] as String?,
      grayList: (json['grayList'] as List<dynamic>?)
          ?.map((e) => GrayList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PureNewUserPageShowDataToJson(
        _$_PureNewUserPageShowData instance) =>
    <String, dynamic>{
      'mainPictureUrl': instance.mainPictureUrl,
      'mainButtonLinkUrl': instance.mainButtonLinkUrl,
      'mainButtonDesc': instance.mainButtonDesc,
      'subButtonDesc': instance.subButtonDesc,
      'subButtonLinkUrl': instance.subButtonLinkUrl,
      'grayList': instance.grayList,
    };

_$_GrayList _$$_GrayListFromJson(Map<String, dynamic> json) => _$_GrayList(
      grayCover: json['grayCover'] as bool?,
      grayHit: json['grayHit'] as bool?,
      grayScaleId: json['grayScaleId'] as String?,
      testValue: json['testValue'] as int?,
    );

Map<String, dynamic> _$$_GrayListToJson(_$_GrayList instance) =>
    <String, dynamic>{
      'grayCover': instance.grayCover,
      'grayHit': instance.grayHit,
      'grayScaleId': instance.grayScaleId,
      'testValue': instance.testValue,
    };
