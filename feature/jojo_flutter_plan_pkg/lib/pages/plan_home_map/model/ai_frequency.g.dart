// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_frequency.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AiFrequency _$$_AiFrequencyFromJson(Map<String, dynamic> json) =>
    _$_AiFrequency(
      callVoice: json['callVoice'] as String?,
      localCallVoice: json['localCallVoice'] as String?,
      dayCallFrequency: json['dayCallFrequency'] as int?,
      ableCallVoice: json['ableCallVoice'] as String?,
      localAbleCallVoice: json['localAbleCallVoice'] as String?,
      ableCallTip: json['ableCallTip'] as String?,
      disableCallVoice: json['disableCallVoice'] as String?,
      localDisableCallVoice: json['localDisableCallVoice'] as String?,
      disableCallTip: json['disableCallTip'] as String?,
      remaining: json['remaining'] as int?,
      callDuration: json['callDuration'] as int?,
      hangupCallVoice: json['hangupCallVoice'] as String?,
      localHangupCallVoice: json['localHangupCallVoice'] as String?,
      callSecondDuration: json['callSecondDuration'] as int?,
      segmentCodeList: (json['segmentCodeList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      classIds:
          (json['classIds'] as List<dynamic>?)?.map((e) => e as int).toList(),
      customState: json['customState'] as String?,
      grayInfo: json['grayInfo'] == null
          ? null
          : GrayInfo.fromJson(json['grayInfo'] as Map<String, dynamic>),
      encouragementConversationParamsVo:
          json['encouragementConversationParamsVo'] == null
              ? null
              : EncouragementConversationParamsVo.fromJson(
                  json['encouragementConversationParamsVo']
                      as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_AiFrequencyToJson(_$_AiFrequency instance) =>
    <String, dynamic>{
      'callVoice': instance.callVoice,
      'localCallVoice': instance.localCallVoice,
      'dayCallFrequency': instance.dayCallFrequency,
      'ableCallVoice': instance.ableCallVoice,
      'localAbleCallVoice': instance.localAbleCallVoice,
      'ableCallTip': instance.ableCallTip,
      'disableCallVoice': instance.disableCallVoice,
      'localDisableCallVoice': instance.localDisableCallVoice,
      'disableCallTip': instance.disableCallTip,
      'remaining': instance.remaining,
      'callDuration': instance.callDuration,
      'hangupCallVoice': instance.hangupCallVoice,
      'localHangupCallVoice': instance.localHangupCallVoice,
      'callSecondDuration': instance.callSecondDuration,
      'segmentCodeList': instance.segmentCodeList,
      'classIds': instance.classIds,
      'customState': instance.customState,
      'grayInfo': instance.grayInfo,
      'encouragementConversationParamsVo':
          instance.encouragementConversationParamsVo,
    };

_$_EncouragementConversationParamsVo
    _$$_EncouragementConversationParamsVoFromJson(Map<String, dynamic> json) =>
        _$_EncouragementConversationParamsVo(
          canPopupAiCall: json['canPopupAiCall'] as bool?,
          encouragementConversationIcon:
              json['encouragementConversationIcon'] as String?,
          encouragementConversationTitle:
              json['encouragementConversationTitle'] as String?,
          encouragementConversationSubTitle:
              json['encouragementConversationSubTitle'] as String?,
          encouragementConversationBgm:
              json['encouragementConversationBgm'] as String?,
          encouragementConversationVoice:
              json['encouragementConversationVoice'] as String?,
          encouragementConversationRouter:
              json['encouragementConversationRouter'] as String?,
          localEncouragementConversationBgm:
              json['localEncouragementConversationBgm'] as String?,
          localEncouragementConversationVoice:
              json['localEncouragementConversationVoice'] as String?,
          roomTaskId: json['roomTaskId'] as String?,
        );

Map<String, dynamic> _$$_EncouragementConversationParamsVoToJson(
        _$_EncouragementConversationParamsVo instance) =>
    <String, dynamic>{
      'canPopupAiCall': instance.canPopupAiCall,
      'encouragementConversationIcon': instance.encouragementConversationIcon,
      'encouragementConversationTitle': instance.encouragementConversationTitle,
      'encouragementConversationSubTitle':
          instance.encouragementConversationSubTitle,
      'encouragementConversationBgm': instance.encouragementConversationBgm,
      'encouragementConversationVoice': instance.encouragementConversationVoice,
      'encouragementConversationRouter':
          instance.encouragementConversationRouter,
      'localEncouragementConversationBgm':
          instance.localEncouragementConversationBgm,
      'localEncouragementConversationVoice':
          instance.localEncouragementConversationVoice,
      'roomTaskId': instance.roomTaskId,
    };

_$_GrayInfo _$$_GrayInfoFromJson(Map<String, dynamic> json) => _$_GrayInfo(
      grayScaleId: json['grayScaleId'] as String?,
      testValue: json['testValue'] as int?,
    );

Map<String, dynamic> _$$_GrayInfoToJson(_$_GrayInfo instance) =>
    <String, dynamic>{
      'grayScaleId': instance.grayScaleId,
      'testValue': instance.testValue,
    };
