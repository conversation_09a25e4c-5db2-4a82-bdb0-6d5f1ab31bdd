import 'dart:convert';

import 'package:jojo_flutter_base/utils/log.dart';

class MilestionSaveData {
  String date;
  List<int> subjects;

  MilestionSaveData({
    required this.date,
    required this.subjects,
  });

  factory MilestionSaveData.fromJson(String jsonStr) {
    final Map<String, dynamic> json = jsonDecode(jsonStr);
    l.i("里程碑动画", "信息 $json");
    return MilestionSaveData(
      date: json['date'] ?? "",
      subjects:
          (json['subjects'] as List<dynamic>?)?.map((e) => e as int).toList() ??
              [],
    );
  }

  /// 创建空数据
  factory MilestionSaveData.empty() {
    return MilestionSaveData(date: "", subjects: []);
  }

  /// 转换为JSON字符串
  String toJson() {
    final Map<String, dynamic> data = {
      'date': date,
      'subjects': subjects,
    };
    return json.encode(data);
  }

  /// 检查是否为空数据
  bool get isEmpty => date.isEmpty;

  /// 检查是否包含指定科目
  bool hasSubject(int subjectType) {
    return subjects.contains(subjectType);
  }

  /// 添加科目（如果不存在）
  void addSubject(int subjectType) {
    if (!hasSubject(subjectType)) {
      subjects.add(subjectType);
    }
  }
}
