// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_advertisements_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_CourseAdvertisementsData _$$_CourseAdvertisementsDataFromJson(
        Map<String, dynamic> json) =>
    _$_CourseAdvertisementsData(
      receiveCourseInfo: json['receiveCourseInfo'] == null
          ? null
          : AdvertisementsReceiveCourseInfoData.fromJson(
              json['receiveCourseInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_CourseAdvertisementsDataToJson(
        _$_CourseAdvertisementsData instance) =>
    <String, dynamic>{
      'receiveCourseInfo': instance.receiveCourseInfo,
    };

_$_AdvertisementsReceiveCourseInfoData
    _$$_AdvertisementsReceiveCourseInfoDataFromJson(
            Map<String, dynamic> json) =>
        _$_AdvertisementsReceiveCourseInfoData(
          subjectId: json['subjectId'] as int?,
          subjectName: json['subjectName'] as String?,
          moreSubjectLinkUrl: json['moreSubjectLinkUrl'] as String?,
          materials: (json['materials'] as List<dynamic>?)
              ?.map((e) => AdvertisementsMaterialsData.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
          mainColor: json['mainColor'] as String?,
          showMoreSubject: json['showMoreSubject'] as bool?,
        );

Map<String, dynamic> _$$_AdvertisementsReceiveCourseInfoDataToJson(
        _$_AdvertisementsReceiveCourseInfoData instance) =>
    <String, dynamic>{
      'subjectId': instance.subjectId,
      'subjectName': instance.subjectName,
      'moreSubjectLinkUrl': instance.moreSubjectLinkUrl,
      'materials': instance.materials,
      'mainColor': instance.mainColor,
      'showMoreSubject': instance.showMoreSubject,
    };

_$_AdvertisementsMaterialsData _$$_AdvertisementsMaterialsDataFromJson(
        Map<String, dynamic> json) =>
    _$_AdvertisementsMaterialsData(
      configId: json['configId'] as int?,
      configName: json['configName'] as String?,
      businessTagId: json['businessTagId'] as String?,
      materialId: json['materialId'] as int?,
      subjectId: json['subjectId'] as int?,
      subjectName: json['subjectName'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      buttonText: json['buttonText'] as String?,
      linkUrl: json['linkUrl'] as String?,
      description: json['description'] as String?,
      title: json['title'] as String?,
      recommend: json['recommend'] as bool?,
      mainColor: json['mainColor'] as String?,
      popupText: json['popupText'] as String?,
      resLibId: json['resLibId'] as int?,
      resourceId: json['resourceId'] as int?,
      materialDetails: (json['materialDetails'] as List<dynamic>?)
          ?.map((e) => AdvertisementsMaterialDetailData.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AdvertisementsMaterialsDataToJson(
        _$_AdvertisementsMaterialsData instance) =>
    <String, dynamic>{
      'configId': instance.configId,
      'configName': instance.configName,
      'businessTagId': instance.businessTagId,
      'materialId': instance.materialId,
      'subjectId': instance.subjectId,
      'subjectName': instance.subjectName,
      'pictureUrl': instance.pictureUrl,
      'buttonText': instance.buttonText,
      'linkUrl': instance.linkUrl,
      'description': instance.description,
      'title': instance.title,
      'recommend': instance.recommend,
      'mainColor': instance.mainColor,
      'popupText': instance.popupText,
      'resLibId': instance.resLibId,
      'resourceId': instance.resourceId,
      'materialDetails': instance.materialDetails,
    };

_$_AdvertisementsMaterialDetailData
    _$$_AdvertisementsMaterialDetailDataFromJson(Map<String, dynamic> json) =>
        _$_AdvertisementsMaterialDetailData(
          resLibId: json['resLibId'] as int?,
          resourceId: json['resourceId'] as int?,
          imageUrl: json['imageUrl'] as String?,
          buttonText: json['buttonText'] as String?,
          linkUrl: json['linkUrl'] as String?,
          title: json['title'] as String?,
          subTitle: json['subTitle'] as String?,
          countDownSecond: json['countDownSecond'] as int?,
          receiveCourseImage: json['receiveCourseImage'] as String?,
          linkId: json['linkId'] as int?,
          padImageUrl: json['padImageUrl'] as String?,
          subjectDescImage: json['subjectDescImage'] as String?,
          selectCourseTitle: json['selectCourseTitle'] as String?,
          skus: (json['skus'] as List<dynamic>?)
              ?.map((e) => SkuData.fromJson(e as Map<String, dynamic>))
              .toList(),
          cardStyle: json['cardStyle'] as int?,
          skuGroup: json['skuGroup'] == null
              ? null
              : SkuGroupData.fromJson(json['skuGroup'] as Map<String, dynamic>),
        );

Map<String, dynamic> _$$_AdvertisementsMaterialDetailDataToJson(
        _$_AdvertisementsMaterialDetailData instance) =>
    <String, dynamic>{
      'resLibId': instance.resLibId,
      'resourceId': instance.resourceId,
      'imageUrl': instance.imageUrl,
      'buttonText': instance.buttonText,
      'linkUrl': instance.linkUrl,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'countDownSecond': instance.countDownSecond,
      'receiveCourseImage': instance.receiveCourseImage,
      'linkId': instance.linkId,
      'padImageUrl': instance.padImageUrl,
      'subjectDescImage': instance.subjectDescImage,
      'selectCourseTitle': instance.selectCourseTitle,
      'skus': instance.skus,
      'cardStyle': instance.cardStyle,
      'skuGroup': instance.skuGroup,
    };

_$_SkuGroupData _$$_SkuGroupDataFromJson(Map<String, dynamic> json) =>
    _$_SkuGroupData(
      tipText: json['tipText'] as String?,
      skuGroups: (json['skuGroups'] as List<dynamic>?)
          ?.map((e) => SkuGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SkuGroupDataToJson(_$_SkuGroupData instance) =>
    <String, dynamic>{
      'tipText': instance.tipText,
      'skuGroups': instance.skuGroups,
    };

_$_SkuGroup _$$_SkuGroupFromJson(Map<String, dynamic> json) => _$_SkuGroup(
      gradeKey: json['gradeKey'] as String?,
      gradeTitle: json['gradeTitle'] as String?,
      tipText: json['tipText'] as String?,
      skus: (json['skus'] as List<dynamic>?)
          ?.map((e) => SkuData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SkuGroupToJson(_$_SkuGroup instance) =>
    <String, dynamic>{
      'gradeKey': instance.gradeKey,
      'gradeTitle': instance.gradeTitle,
      'tipText': instance.tipText,
      'skus': instance.skus,
    };

_$_SkuData _$$_SkuDataFromJson(Map<String, dynamic> json) => _$_SkuData(
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      linkId: json['linkId'] as int?,
    );

Map<String, dynamic> _$$_SkuDataToJson(_$_SkuData instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'linkId': instance.linkId,
    };
