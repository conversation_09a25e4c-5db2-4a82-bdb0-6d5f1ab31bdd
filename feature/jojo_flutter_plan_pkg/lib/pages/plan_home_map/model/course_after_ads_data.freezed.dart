// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_after_ads_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseAfterAdsInfoData _$CourseAfterAdsInfoDataFromJson(
    Map<String, dynamic> json) {
  return _CourseAfterAdsInfoData.fromJson(json);
}

/// @nodoc
mixin _$CourseAfterAdsInfoData {
  List<AdvertisementList>? get advertisementList =>
      throw _privateConstructorUsedError;
  String? get scene => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseAfterAdsInfoDataCopyWith<CourseAfterAdsInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseAfterAdsInfoDataCopyWith<$Res> {
  factory $CourseAfterAdsInfoDataCopyWith(CourseAfterAdsInfoData value,
          $Res Function(CourseAfterAdsInfoData) then) =
      _$CourseAfterAdsInfoDataCopyWithImpl<$Res, CourseAfterAdsInfoData>;
  @useResult
  $Res call({List<AdvertisementList>? advertisementList, String? scene});
}

/// @nodoc
class _$CourseAfterAdsInfoDataCopyWithImpl<$Res,
        $Val extends CourseAfterAdsInfoData>
    implements $CourseAfterAdsInfoDataCopyWith<$Res> {
  _$CourseAfterAdsInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? advertisementList = freezed,
    Object? scene = freezed,
  }) {
    return _then(_value.copyWith(
      advertisementList: freezed == advertisementList
          ? _value.advertisementList
          : advertisementList // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementList>?,
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseAfterAdsInfoDataCopyWith<$Res>
    implements $CourseAfterAdsInfoDataCopyWith<$Res> {
  factory _$$_CourseAfterAdsInfoDataCopyWith(_$_CourseAfterAdsInfoData value,
          $Res Function(_$_CourseAfterAdsInfoData) then) =
      __$$_CourseAfterAdsInfoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AdvertisementList>? advertisementList, String? scene});
}

/// @nodoc
class __$$_CourseAfterAdsInfoDataCopyWithImpl<$Res>
    extends _$CourseAfterAdsInfoDataCopyWithImpl<$Res,
        _$_CourseAfterAdsInfoData>
    implements _$$_CourseAfterAdsInfoDataCopyWith<$Res> {
  __$$_CourseAfterAdsInfoDataCopyWithImpl(_$_CourseAfterAdsInfoData _value,
      $Res Function(_$_CourseAfterAdsInfoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? advertisementList = freezed,
    Object? scene = freezed,
  }) {
    return _then(_$_CourseAfterAdsInfoData(
      advertisementList: freezed == advertisementList
          ? _value._advertisementList
          : advertisementList // ignore: cast_nullable_to_non_nullable
              as List<AdvertisementList>?,
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseAfterAdsInfoData implements _CourseAfterAdsInfoData {
  const _$_CourseAfterAdsInfoData(
      {final List<AdvertisementList>? advertisementList, this.scene})
      : _advertisementList = advertisementList;

  factory _$_CourseAfterAdsInfoData.fromJson(Map<String, dynamic> json) =>
      _$$_CourseAfterAdsInfoDataFromJson(json);

  final List<AdvertisementList>? _advertisementList;
  @override
  List<AdvertisementList>? get advertisementList {
    final value = _advertisementList;
    if (value == null) return null;
    if (_advertisementList is EqualUnmodifiableListView)
      return _advertisementList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? scene;

  @override
  String toString() {
    return 'CourseAfterAdsInfoData(advertisementList: $advertisementList, scene: $scene)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseAfterAdsInfoData &&
            const DeepCollectionEquality()
                .equals(other._advertisementList, _advertisementList) &&
            (identical(other.scene, scene) || other.scene == scene));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_advertisementList), scene);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseAfterAdsInfoDataCopyWith<_$_CourseAfterAdsInfoData> get copyWith =>
      __$$_CourseAfterAdsInfoDataCopyWithImpl<_$_CourseAfterAdsInfoData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseAfterAdsInfoDataToJson(
      this,
    );
  }
}

abstract class _CourseAfterAdsInfoData implements CourseAfterAdsInfoData {
  const factory _CourseAfterAdsInfoData(
      {final List<AdvertisementList>? advertisementList,
      final String? scene}) = _$_CourseAfterAdsInfoData;

  factory _CourseAfterAdsInfoData.fromJson(Map<String, dynamic> json) =
      _$_CourseAfterAdsInfoData.fromJson;

  @override
  List<AdvertisementList>? get advertisementList;
  @override
  String? get scene;
  @override
  @JsonKey(ignore: true)
  _$$_CourseAfterAdsInfoDataCopyWith<_$_CourseAfterAdsInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

AdvertisementList _$AdvertisementListFromJson(Map<String, dynamic> json) {
  return _AdvertisementList.fromJson(json);
}

/// @nodoc
mixin _$AdvertisementList {
  String? get advertisementCopy => throw _privateConstructorUsedError;
  int? get advertisementId => throw _privateConstructorUsedError;
  String? get advertisementName => throw _privateConstructorUsedError;
  String? get advertisementPosition => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;
  List<String>? get closeReasonList => throw _privateConstructorUsedError;
  List<GrayscaleList>? get grayscaleList => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdvertisementListCopyWith<AdvertisementList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdvertisementListCopyWith<$Res> {
  factory $AdvertisementListCopyWith(
          AdvertisementList value, $Res Function(AdvertisementList) then) =
      _$AdvertisementListCopyWithImpl<$Res, AdvertisementList>;
  @useResult
  $Res call(
      {String? advertisementCopy,
      int? advertisementId,
      String? advertisementName,
      String? advertisementPosition,
      String? businessTagId,
      List<String>? closeReasonList,
      List<GrayscaleList>? grayscaleList,
      int? id,
      String? linkUrl,
      String? pictureUrl});
}

/// @nodoc
class _$AdvertisementListCopyWithImpl<$Res, $Val extends AdvertisementList>
    implements $AdvertisementListCopyWith<$Res> {
  _$AdvertisementListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? advertisementCopy = freezed,
    Object? advertisementId = freezed,
    Object? advertisementName = freezed,
    Object? advertisementPosition = freezed,
    Object? businessTagId = freezed,
    Object? closeReasonList = freezed,
    Object? grayscaleList = freezed,
    Object? id = freezed,
    Object? linkUrl = freezed,
    Object? pictureUrl = freezed,
  }) {
    return _then(_value.copyWith(
      advertisementCopy: freezed == advertisementCopy
          ? _value.advertisementCopy
          : advertisementCopy // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementId: freezed == advertisementId
          ? _value.advertisementId
          : advertisementId // ignore: cast_nullable_to_non_nullable
              as int?,
      advertisementName: freezed == advertisementName
          ? _value.advertisementName
          : advertisementName // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementPosition: freezed == advertisementPosition
          ? _value.advertisementPosition
          : advertisementPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      closeReasonList: freezed == closeReasonList
          ? _value.closeReasonList
          : closeReasonList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      grayscaleList: freezed == grayscaleList
          ? _value.grayscaleList
          : grayscaleList // ignore: cast_nullable_to_non_nullable
              as List<GrayscaleList>?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AdvertisementListCopyWith<$Res>
    implements $AdvertisementListCopyWith<$Res> {
  factory _$$_AdvertisementListCopyWith(_$_AdvertisementList value,
          $Res Function(_$_AdvertisementList) then) =
      __$$_AdvertisementListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? advertisementCopy,
      int? advertisementId,
      String? advertisementName,
      String? advertisementPosition,
      String? businessTagId,
      List<String>? closeReasonList,
      List<GrayscaleList>? grayscaleList,
      int? id,
      String? linkUrl,
      String? pictureUrl});
}

/// @nodoc
class __$$_AdvertisementListCopyWithImpl<$Res>
    extends _$AdvertisementListCopyWithImpl<$Res, _$_AdvertisementList>
    implements _$$_AdvertisementListCopyWith<$Res> {
  __$$_AdvertisementListCopyWithImpl(
      _$_AdvertisementList _value, $Res Function(_$_AdvertisementList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? advertisementCopy = freezed,
    Object? advertisementId = freezed,
    Object? advertisementName = freezed,
    Object? advertisementPosition = freezed,
    Object? businessTagId = freezed,
    Object? closeReasonList = freezed,
    Object? grayscaleList = freezed,
    Object? id = freezed,
    Object? linkUrl = freezed,
    Object? pictureUrl = freezed,
  }) {
    return _then(_$_AdvertisementList(
      advertisementCopy: freezed == advertisementCopy
          ? _value.advertisementCopy
          : advertisementCopy // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementId: freezed == advertisementId
          ? _value.advertisementId
          : advertisementId // ignore: cast_nullable_to_non_nullable
              as int?,
      advertisementName: freezed == advertisementName
          ? _value.advertisementName
          : advertisementName // ignore: cast_nullable_to_non_nullable
              as String?,
      advertisementPosition: freezed == advertisementPosition
          ? _value.advertisementPosition
          : advertisementPosition // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      closeReasonList: freezed == closeReasonList
          ? _value._closeReasonList
          : closeReasonList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      grayscaleList: freezed == grayscaleList
          ? _value._grayscaleList
          : grayscaleList // ignore: cast_nullable_to_non_nullable
              as List<GrayscaleList>?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AdvertisementList implements _AdvertisementList {
  const _$_AdvertisementList(
      {this.advertisementCopy,
      this.advertisementId,
      this.advertisementName,
      this.advertisementPosition,
      this.businessTagId,
      final List<String>? closeReasonList,
      final List<GrayscaleList>? grayscaleList,
      this.id,
      this.linkUrl,
      this.pictureUrl})
      : _closeReasonList = closeReasonList,
        _grayscaleList = grayscaleList;

  factory _$_AdvertisementList.fromJson(Map<String, dynamic> json) =>
      _$$_AdvertisementListFromJson(json);

  @override
  final String? advertisementCopy;
  @override
  final int? advertisementId;
  @override
  final String? advertisementName;
  @override
  final String? advertisementPosition;
  @override
  final String? businessTagId;
  final List<String>? _closeReasonList;
  @override
  List<String>? get closeReasonList {
    final value = _closeReasonList;
    if (value == null) return null;
    if (_closeReasonList is EqualUnmodifiableListView) return _closeReasonList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<GrayscaleList>? _grayscaleList;
  @override
  List<GrayscaleList>? get grayscaleList {
    final value = _grayscaleList;
    if (value == null) return null;
    if (_grayscaleList is EqualUnmodifiableListView) return _grayscaleList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? id;
  @override
  final String? linkUrl;
  @override
  final String? pictureUrl;

  @override
  String toString() {
    return 'AdvertisementList(advertisementCopy: $advertisementCopy, advertisementId: $advertisementId, advertisementName: $advertisementName, advertisementPosition: $advertisementPosition, businessTagId: $businessTagId, closeReasonList: $closeReasonList, grayscaleList: $grayscaleList, id: $id, linkUrl: $linkUrl, pictureUrl: $pictureUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AdvertisementList &&
            (identical(other.advertisementCopy, advertisementCopy) ||
                other.advertisementCopy == advertisementCopy) &&
            (identical(other.advertisementId, advertisementId) ||
                other.advertisementId == advertisementId) &&
            (identical(other.advertisementName, advertisementName) ||
                other.advertisementName == advertisementName) &&
            (identical(other.advertisementPosition, advertisementPosition) ||
                other.advertisementPosition == advertisementPosition) &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId) &&
            const DeepCollectionEquality()
                .equals(other._closeReasonList, _closeReasonList) &&
            const DeepCollectionEquality()
                .equals(other._grayscaleList, _grayscaleList) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      advertisementCopy,
      advertisementId,
      advertisementName,
      advertisementPosition,
      businessTagId,
      const DeepCollectionEquality().hash(_closeReasonList),
      const DeepCollectionEquality().hash(_grayscaleList),
      id,
      linkUrl,
      pictureUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AdvertisementListCopyWith<_$_AdvertisementList> get copyWith =>
      __$$_AdvertisementListCopyWithImpl<_$_AdvertisementList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AdvertisementListToJson(
      this,
    );
  }
}

abstract class _AdvertisementList implements AdvertisementList {
  const factory _AdvertisementList(
      {final String? advertisementCopy,
      final int? advertisementId,
      final String? advertisementName,
      final String? advertisementPosition,
      final String? businessTagId,
      final List<String>? closeReasonList,
      final List<GrayscaleList>? grayscaleList,
      final int? id,
      final String? linkUrl,
      final String? pictureUrl}) = _$_AdvertisementList;

  factory _AdvertisementList.fromJson(Map<String, dynamic> json) =
      _$_AdvertisementList.fromJson;

  @override
  String? get advertisementCopy;
  @override
  int? get advertisementId;
  @override
  String? get advertisementName;
  @override
  String? get advertisementPosition;
  @override
  String? get businessTagId;
  @override
  List<String>? get closeReasonList;
  @override
  List<GrayscaleList>? get grayscaleList;
  @override
  int? get id;
  @override
  String? get linkUrl;
  @override
  String? get pictureUrl;
  @override
  @JsonKey(ignore: true)
  _$$_AdvertisementListCopyWith<_$_AdvertisementList> get copyWith =>
      throw _privateConstructorUsedError;
}

GrayscaleList _$GrayscaleListFromJson(Map<String, dynamic> json) {
  return _GrayscaleList.fromJson(json);
}

/// @nodoc
mixin _$GrayscaleList {
  bool? get grayCover => throw _privateConstructorUsedError;
  bool? get grayHit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrayscaleListCopyWith<GrayscaleList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrayscaleListCopyWith<$Res> {
  factory $GrayscaleListCopyWith(
          GrayscaleList value, $Res Function(GrayscaleList) then) =
      _$GrayscaleListCopyWithImpl<$Res, GrayscaleList>;
  @useResult
  $Res call({bool? grayCover, bool? grayHit});
}

/// @nodoc
class _$GrayscaleListCopyWithImpl<$Res, $Val extends GrayscaleList>
    implements $GrayscaleListCopyWith<$Res> {
  _$GrayscaleListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
  }) {
    return _then(_value.copyWith(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GrayscaleListCopyWith<$Res>
    implements $GrayscaleListCopyWith<$Res> {
  factory _$$_GrayscaleListCopyWith(
          _$_GrayscaleList value, $Res Function(_$_GrayscaleList) then) =
      __$$_GrayscaleListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? grayCover, bool? grayHit});
}

/// @nodoc
class __$$_GrayscaleListCopyWithImpl<$Res>
    extends _$GrayscaleListCopyWithImpl<$Res, _$_GrayscaleList>
    implements _$$_GrayscaleListCopyWith<$Res> {
  __$$_GrayscaleListCopyWithImpl(
      _$_GrayscaleList _value, $Res Function(_$_GrayscaleList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? grayCover = freezed,
    Object? grayHit = freezed,
  }) {
    return _then(_$_GrayscaleList(
      grayCover: freezed == grayCover
          ? _value.grayCover
          : grayCover // ignore: cast_nullable_to_non_nullable
              as bool?,
      grayHit: freezed == grayHit
          ? _value.grayHit
          : grayHit // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GrayscaleList implements _GrayscaleList {
  const _$_GrayscaleList({this.grayCover, this.grayHit});

  factory _$_GrayscaleList.fromJson(Map<String, dynamic> json) =>
      _$$_GrayscaleListFromJson(json);

  @override
  final bool? grayCover;
  @override
  final bool? grayHit;

  @override
  String toString() {
    return 'GrayscaleList(grayCover: $grayCover, grayHit: $grayHit)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GrayscaleList &&
            (identical(other.grayCover, grayCover) ||
                other.grayCover == grayCover) &&
            (identical(other.grayHit, grayHit) || other.grayHit == grayHit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, grayCover, grayHit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GrayscaleListCopyWith<_$_GrayscaleList> get copyWith =>
      __$$_GrayscaleListCopyWithImpl<_$_GrayscaleList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GrayscaleListToJson(
      this,
    );
  }
}

abstract class _GrayscaleList implements GrayscaleList {
  const factory _GrayscaleList({final bool? grayCover, final bool? grayHit}) =
      _$_GrayscaleList;

  factory _GrayscaleList.fromJson(Map<String, dynamic> json) =
      _$_GrayscaleList.fromJson;

  @override
  bool? get grayCover;
  @override
  bool? get grayHit;
  @override
  @JsonKey(ignore: true)
  _$$_GrayscaleListCopyWith<_$_GrayscaleList> get copyWith =>
      throw _privateConstructorUsedError;
}
