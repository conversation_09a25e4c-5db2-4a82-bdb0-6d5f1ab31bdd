
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ai_dialog_data.freezed.dart';
part 'ai_dialog_data.g.dart';

@unfreezed
class AiDialogData with _$AiDialogData {
  factory AiDialogData({
    int? jojoCallTime,
    String? jojoCallIcon,
    String? jojoCallTitle,
    String? jojoCallSubTitle,
    String? jojoCallBgm,
    String? jojoCallVoice,
    String? jojoCallRouter,
    String? jojoCallBgImgLocalPath,
    String? jojoCallVoiceLocalPath,
  }) = _AiDialogData;

  factory AiDialogData.fromJson(Map<String, dynamic> json) =>
      _$AiDialogDataFromJson(json);

}