// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'growth_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ScheduleViewsData _$$_ScheduleViewsDataFromJson(Map<String, dynamic> json) =>
    _$_ScheduleViewsData(
      periodLearningGrowthList:
          (json['periodLearningGrowthList'] as List<dynamic>?)
              ?.map((e) =>
                  PeriodLearningGrowthItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_ScheduleViewsDataToJson(
        _$_ScheduleViewsData instance) =>
    <String, dynamic>{
      'periodLearningGrowthList': instance.periodLearningGrowthList,
    };

_$_PeriodLearningGrowthItem _$$_PeriodLearningGrowthItemFromJson(
        Map<String, dynamic> json) =>
    _$_PeriodLearningGrowthItem(
      endTime: json['endTime'] as int?,
      startTime: json['startTime'] as int?,
    );

Map<String, dynamic> _$$_PeriodLearningGrowthItemToJson(
        _$_PeriodLearningGrowthItem instance) =>
    <String, dynamic>{
      'endTime': instance.endTime,
      'startTime': instance.startTime,
    };

_$_PeriodDataListData _$$_PeriodDataListDataFromJson(
        Map<String, dynamic> json) =>
    _$_PeriodDataListData(
      periodDataList: (json['periodDataList'] as List<dynamic>?)
          ?.map((e) => PeriodDataItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PeriodDataListDataToJson(
        _$_PeriodDataListData instance) =>
    <String, dynamic>{
      'periodDataList': instance.periodDataList,
    };

_$_PeriodDataItem _$$_PeriodDataItemFromJson(Map<String, dynamic> json) =>
    _$_PeriodDataItem(
      current: json['current'] as bool?,
      performanceType: json['performanceType'] as String?,
      periodName: json['periodName'] as String?,
      periodOrder: json['periodOrder'] as int?,
      periodPerformance: json['periodPerformance'] as int?,
      status: json['status'] as int?,
    );

Map<String, dynamic> _$$_PeriodDataItemToJson(_$_PeriodDataItem instance) =>
    <String, dynamic>{
      'current': instance.current,
      'performanceType': instance.performanceType,
      'periodName': instance.periodName,
      'periodOrder': instance.periodOrder,
      'periodPerformance': instance.periodPerformance,
      'status': instance.status,
    };

_$_IConfigValue _$$_IConfigValueFromJson(Map<String, dynamic> json) =>
    _$_IConfigValue(
      statisticInfoList: (json['statisticInfoList'] as List<dynamic>?)
          ?.map((e) => StatisticInfoItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_IConfigValueToJson(_$_IConfigValue instance) =>
    <String, dynamic>{
      'statisticInfoList': instance.statisticInfoList,
    };

_$_StatisticInfoItem _$$_StatisticInfoItemFromJson(Map<String, dynamic> json) =>
    _$_StatisticInfoItem(
      courseSegmentCodeList: (json['courseSegmentCodeList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      statisticKey: json['statisticKey'] as String?,
      statisticName: json['statisticName'] as String?,
      statisticType: json['statisticType'] as int?,
      statisticUnit: json['statisticUnit'] as String?,
    );

Map<String, dynamic> _$$_StatisticInfoItemToJson(
        _$_StatisticInfoItem instance) =>
    <String, dynamic>{
      'courseSegmentCodeList': instance.courseSegmentCodeList,
      'statisticKey': instance.statisticKey,
      'statisticName': instance.statisticName,
      'statisticType': instance.statisticType,
      'statisticUnit': instance.statisticUnit,
    };
