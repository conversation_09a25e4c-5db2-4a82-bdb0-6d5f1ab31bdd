// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'growth_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ScheduleViewsData _$ScheduleViewsDataFromJson(Map<String, dynamic> json) {
  return _ScheduleViewsData.fromJson(json);
}

/// @nodoc
mixin _$ScheduleViewsData {
  List<PeriodLearningGrowthItem>? get periodLearningGrowthList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScheduleViewsDataCopyWith<ScheduleViewsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleViewsDataCopyWith<$Res> {
  factory $ScheduleViewsDataCopyWith(
          ScheduleViewsData value, $Res Function(ScheduleViewsData) then) =
      _$ScheduleViewsDataCopyWithImpl<$Res, ScheduleViewsData>;
  @useResult
  $Res call({List<PeriodLearningGrowthItem>? periodLearningGrowthList});
}

/// @nodoc
class _$ScheduleViewsDataCopyWithImpl<$Res, $Val extends ScheduleViewsData>
    implements $ScheduleViewsDataCopyWith<$Res> {
  _$ScheduleViewsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodLearningGrowthList = freezed,
  }) {
    return _then(_value.copyWith(
      periodLearningGrowthList: freezed == periodLearningGrowthList
          ? _value.periodLearningGrowthList
          : periodLearningGrowthList // ignore: cast_nullable_to_non_nullable
              as List<PeriodLearningGrowthItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ScheduleViewsDataCopyWith<$Res>
    implements $ScheduleViewsDataCopyWith<$Res> {
  factory _$$_ScheduleViewsDataCopyWith(_$_ScheduleViewsData value,
          $Res Function(_$_ScheduleViewsData) then) =
      __$$_ScheduleViewsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PeriodLearningGrowthItem>? periodLearningGrowthList});
}

/// @nodoc
class __$$_ScheduleViewsDataCopyWithImpl<$Res>
    extends _$ScheduleViewsDataCopyWithImpl<$Res, _$_ScheduleViewsData>
    implements _$$_ScheduleViewsDataCopyWith<$Res> {
  __$$_ScheduleViewsDataCopyWithImpl(
      _$_ScheduleViewsData _value, $Res Function(_$_ScheduleViewsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodLearningGrowthList = freezed,
  }) {
    return _then(_$_ScheduleViewsData(
      periodLearningGrowthList: freezed == periodLearningGrowthList
          ? _value._periodLearningGrowthList
          : periodLearningGrowthList // ignore: cast_nullable_to_non_nullable
              as List<PeriodLearningGrowthItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ScheduleViewsData implements _ScheduleViewsData {
  const _$_ScheduleViewsData(
      {final List<PeriodLearningGrowthItem>? periodLearningGrowthList})
      : _periodLearningGrowthList = periodLearningGrowthList;

  factory _$_ScheduleViewsData.fromJson(Map<String, dynamic> json) =>
      _$$_ScheduleViewsDataFromJson(json);

  final List<PeriodLearningGrowthItem>? _periodLearningGrowthList;
  @override
  List<PeriodLearningGrowthItem>? get periodLearningGrowthList {
    final value = _periodLearningGrowthList;
    if (value == null) return null;
    if (_periodLearningGrowthList is EqualUnmodifiableListView)
      return _periodLearningGrowthList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ScheduleViewsData(periodLearningGrowthList: $periodLearningGrowthList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ScheduleViewsData &&
            const DeepCollectionEquality().equals(
                other._periodLearningGrowthList, _periodLearningGrowthList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_periodLearningGrowthList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ScheduleViewsDataCopyWith<_$_ScheduleViewsData> get copyWith =>
      __$$_ScheduleViewsDataCopyWithImpl<_$_ScheduleViewsData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ScheduleViewsDataToJson(
      this,
    );
  }
}

abstract class _ScheduleViewsData implements ScheduleViewsData {
  const factory _ScheduleViewsData(
          {final List<PeriodLearningGrowthItem>? periodLearningGrowthList}) =
      _$_ScheduleViewsData;

  factory _ScheduleViewsData.fromJson(Map<String, dynamic> json) =
      _$_ScheduleViewsData.fromJson;

  @override
  List<PeriodLearningGrowthItem>? get periodLearningGrowthList;
  @override
  @JsonKey(ignore: true)
  _$$_ScheduleViewsDataCopyWith<_$_ScheduleViewsData> get copyWith =>
      throw _privateConstructorUsedError;
}

PeriodLearningGrowthItem _$PeriodLearningGrowthItemFromJson(
    Map<String, dynamic> json) {
  return _PeriodLearningGrowthItem.fromJson(json);
}

/// @nodoc
mixin _$PeriodLearningGrowthItem {
  int? get endTime => throw _privateConstructorUsedError;
  int? get startTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PeriodLearningGrowthItemCopyWith<PeriodLearningGrowthItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodLearningGrowthItemCopyWith<$Res> {
  factory $PeriodLearningGrowthItemCopyWith(PeriodLearningGrowthItem value,
          $Res Function(PeriodLearningGrowthItem) then) =
      _$PeriodLearningGrowthItemCopyWithImpl<$Res, PeriodLearningGrowthItem>;
  @useResult
  $Res call({int? endTime, int? startTime});
}

/// @nodoc
class _$PeriodLearningGrowthItemCopyWithImpl<$Res,
        $Val extends PeriodLearningGrowthItem>
    implements $PeriodLearningGrowthItemCopyWith<$Res> {
  _$PeriodLearningGrowthItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endTime = freezed,
    Object? startTime = freezed,
  }) {
    return _then(_value.copyWith(
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PeriodLearningGrowthItemCopyWith<$Res>
    implements $PeriodLearningGrowthItemCopyWith<$Res> {
  factory _$$_PeriodLearningGrowthItemCopyWith(
          _$_PeriodLearningGrowthItem value,
          $Res Function(_$_PeriodLearningGrowthItem) then) =
      __$$_PeriodLearningGrowthItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? endTime, int? startTime});
}

/// @nodoc
class __$$_PeriodLearningGrowthItemCopyWithImpl<$Res>
    extends _$PeriodLearningGrowthItemCopyWithImpl<$Res,
        _$_PeriodLearningGrowthItem>
    implements _$$_PeriodLearningGrowthItemCopyWith<$Res> {
  __$$_PeriodLearningGrowthItemCopyWithImpl(_$_PeriodLearningGrowthItem _value,
      $Res Function(_$_PeriodLearningGrowthItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? endTime = freezed,
    Object? startTime = freezed,
  }) {
    return _then(_$_PeriodLearningGrowthItem(
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PeriodLearningGrowthItem implements _PeriodLearningGrowthItem {
  const _$_PeriodLearningGrowthItem({this.endTime, this.startTime});

  factory _$_PeriodLearningGrowthItem.fromJson(Map<String, dynamic> json) =>
      _$$_PeriodLearningGrowthItemFromJson(json);

  @override
  final int? endTime;
  @override
  final int? startTime;

  @override
  String toString() {
    return 'PeriodLearningGrowthItem(endTime: $endTime, startTime: $startTime)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PeriodLearningGrowthItem &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, endTime, startTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PeriodLearningGrowthItemCopyWith<_$_PeriodLearningGrowthItem>
      get copyWith => __$$_PeriodLearningGrowthItemCopyWithImpl<
          _$_PeriodLearningGrowthItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PeriodLearningGrowthItemToJson(
      this,
    );
  }
}

abstract class _PeriodLearningGrowthItem implements PeriodLearningGrowthItem {
  const factory _PeriodLearningGrowthItem(
      {final int? endTime, final int? startTime}) = _$_PeriodLearningGrowthItem;

  factory _PeriodLearningGrowthItem.fromJson(Map<String, dynamic> json) =
      _$_PeriodLearningGrowthItem.fromJson;

  @override
  int? get endTime;
  @override
  int? get startTime;
  @override
  @JsonKey(ignore: true)
  _$$_PeriodLearningGrowthItemCopyWith<_$_PeriodLearningGrowthItem>
      get copyWith => throw _privateConstructorUsedError;
}

PeriodDataListData _$PeriodDataListDataFromJson(Map<String, dynamic> json) {
  return _PeriodDataListData.fromJson(json);
}

/// @nodoc
mixin _$PeriodDataListData {
  List<PeriodDataItem>? get periodDataList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PeriodDataListDataCopyWith<PeriodDataListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodDataListDataCopyWith<$Res> {
  factory $PeriodDataListDataCopyWith(
          PeriodDataListData value, $Res Function(PeriodDataListData) then) =
      _$PeriodDataListDataCopyWithImpl<$Res, PeriodDataListData>;
  @useResult
  $Res call({List<PeriodDataItem>? periodDataList});
}

/// @nodoc
class _$PeriodDataListDataCopyWithImpl<$Res, $Val extends PeriodDataListData>
    implements $PeriodDataListDataCopyWith<$Res> {
  _$PeriodDataListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodDataList = freezed,
  }) {
    return _then(_value.copyWith(
      periodDataList: freezed == periodDataList
          ? _value.periodDataList
          : periodDataList // ignore: cast_nullable_to_non_nullable
              as List<PeriodDataItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PeriodDataListDataCopyWith<$Res>
    implements $PeriodDataListDataCopyWith<$Res> {
  factory _$$_PeriodDataListDataCopyWith(_$_PeriodDataListData value,
          $Res Function(_$_PeriodDataListData) then) =
      __$$_PeriodDataListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PeriodDataItem>? periodDataList});
}

/// @nodoc
class __$$_PeriodDataListDataCopyWithImpl<$Res>
    extends _$PeriodDataListDataCopyWithImpl<$Res, _$_PeriodDataListData>
    implements _$$_PeriodDataListDataCopyWith<$Res> {
  __$$_PeriodDataListDataCopyWithImpl(
      _$_PeriodDataListData _value, $Res Function(_$_PeriodDataListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodDataList = freezed,
  }) {
    return _then(_$_PeriodDataListData(
      periodDataList: freezed == periodDataList
          ? _value._periodDataList
          : periodDataList // ignore: cast_nullable_to_non_nullable
              as List<PeriodDataItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PeriodDataListData implements _PeriodDataListData {
  const _$_PeriodDataListData({final List<PeriodDataItem>? periodDataList})
      : _periodDataList = periodDataList;

  factory _$_PeriodDataListData.fromJson(Map<String, dynamic> json) =>
      _$$_PeriodDataListDataFromJson(json);

  final List<PeriodDataItem>? _periodDataList;
  @override
  List<PeriodDataItem>? get periodDataList {
    final value = _periodDataList;
    if (value == null) return null;
    if (_periodDataList is EqualUnmodifiableListView) return _periodDataList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PeriodDataListData(periodDataList: $periodDataList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PeriodDataListData &&
            const DeepCollectionEquality()
                .equals(other._periodDataList, _periodDataList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_periodDataList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PeriodDataListDataCopyWith<_$_PeriodDataListData> get copyWith =>
      __$$_PeriodDataListDataCopyWithImpl<_$_PeriodDataListData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PeriodDataListDataToJson(
      this,
    );
  }
}

abstract class _PeriodDataListData implements PeriodDataListData {
  const factory _PeriodDataListData(
      {final List<PeriodDataItem>? periodDataList}) = _$_PeriodDataListData;

  factory _PeriodDataListData.fromJson(Map<String, dynamic> json) =
      _$_PeriodDataListData.fromJson;

  @override
  List<PeriodDataItem>? get periodDataList;
  @override
  @JsonKey(ignore: true)
  _$$_PeriodDataListDataCopyWith<_$_PeriodDataListData> get copyWith =>
      throw _privateConstructorUsedError;
}

PeriodDataItem _$PeriodDataItemFromJson(Map<String, dynamic> json) {
  return _PeriodDataItem.fromJson(json);
}

/// @nodoc
mixin _$PeriodDataItem {
  bool? get current => throw _privateConstructorUsedError;
  String? get performanceType => throw _privateConstructorUsedError;
  String? get periodName => throw _privateConstructorUsedError;
  int? get periodOrder => throw _privateConstructorUsedError;
  int? get periodPerformance => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PeriodDataItemCopyWith<PeriodDataItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodDataItemCopyWith<$Res> {
  factory $PeriodDataItemCopyWith(
          PeriodDataItem value, $Res Function(PeriodDataItem) then) =
      _$PeriodDataItemCopyWithImpl<$Res, PeriodDataItem>;
  @useResult
  $Res call(
      {bool? current,
      String? performanceType,
      String? periodName,
      int? periodOrder,
      int? periodPerformance,
      int? status});
}

/// @nodoc
class _$PeriodDataItemCopyWithImpl<$Res, $Val extends PeriodDataItem>
    implements $PeriodDataItemCopyWith<$Res> {
  _$PeriodDataItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? performanceType = freezed,
    Object? periodName = freezed,
    Object? periodOrder = freezed,
    Object? periodPerformance = freezed,
    Object? status = freezed,
  }) {
    return _then(_value.copyWith(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as bool?,
      performanceType: freezed == performanceType
          ? _value.performanceType
          : performanceType // ignore: cast_nullable_to_non_nullable
              as String?,
      periodName: freezed == periodName
          ? _value.periodName
          : periodName // ignore: cast_nullable_to_non_nullable
              as String?,
      periodOrder: freezed == periodOrder
          ? _value.periodOrder
          : periodOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      periodPerformance: freezed == periodPerformance
          ? _value.periodPerformance
          : periodPerformance // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PeriodDataItemCopyWith<$Res>
    implements $PeriodDataItemCopyWith<$Res> {
  factory _$$_PeriodDataItemCopyWith(
          _$_PeriodDataItem value, $Res Function(_$_PeriodDataItem) then) =
      __$$_PeriodDataItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? current,
      String? performanceType,
      String? periodName,
      int? periodOrder,
      int? periodPerformance,
      int? status});
}

/// @nodoc
class __$$_PeriodDataItemCopyWithImpl<$Res>
    extends _$PeriodDataItemCopyWithImpl<$Res, _$_PeriodDataItem>
    implements _$$_PeriodDataItemCopyWith<$Res> {
  __$$_PeriodDataItemCopyWithImpl(
      _$_PeriodDataItem _value, $Res Function(_$_PeriodDataItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? current = freezed,
    Object? performanceType = freezed,
    Object? periodName = freezed,
    Object? periodOrder = freezed,
    Object? periodPerformance = freezed,
    Object? status = freezed,
  }) {
    return _then(_$_PeriodDataItem(
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as bool?,
      performanceType: freezed == performanceType
          ? _value.performanceType
          : performanceType // ignore: cast_nullable_to_non_nullable
              as String?,
      periodName: freezed == periodName
          ? _value.periodName
          : periodName // ignore: cast_nullable_to_non_nullable
              as String?,
      periodOrder: freezed == periodOrder
          ? _value.periodOrder
          : periodOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      periodPerformance: freezed == periodPerformance
          ? _value.periodPerformance
          : periodPerformance // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PeriodDataItem implements _PeriodDataItem {
  const _$_PeriodDataItem(
      {this.current,
      this.performanceType,
      this.periodName,
      this.periodOrder,
      this.periodPerformance,
      this.status});

  factory _$_PeriodDataItem.fromJson(Map<String, dynamic> json) =>
      _$$_PeriodDataItemFromJson(json);

  @override
  final bool? current;
  @override
  final String? performanceType;
  @override
  final String? periodName;
  @override
  final int? periodOrder;
  @override
  final int? periodPerformance;
  @override
  final int? status;

  @override
  String toString() {
    return 'PeriodDataItem(current: $current, performanceType: $performanceType, periodName: $periodName, periodOrder: $periodOrder, periodPerformance: $periodPerformance, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PeriodDataItem &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.performanceType, performanceType) ||
                other.performanceType == performanceType) &&
            (identical(other.periodName, periodName) ||
                other.periodName == periodName) &&
            (identical(other.periodOrder, periodOrder) ||
                other.periodOrder == periodOrder) &&
            (identical(other.periodPerformance, periodPerformance) ||
                other.periodPerformance == periodPerformance) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, current, performanceType,
      periodName, periodOrder, periodPerformance, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PeriodDataItemCopyWith<_$_PeriodDataItem> get copyWith =>
      __$$_PeriodDataItemCopyWithImpl<_$_PeriodDataItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PeriodDataItemToJson(
      this,
    );
  }
}

abstract class _PeriodDataItem implements PeriodDataItem {
  const factory _PeriodDataItem(
      {final bool? current,
      final String? performanceType,
      final String? periodName,
      final int? periodOrder,
      final int? periodPerformance,
      final int? status}) = _$_PeriodDataItem;

  factory _PeriodDataItem.fromJson(Map<String, dynamic> json) =
      _$_PeriodDataItem.fromJson;

  @override
  bool? get current;
  @override
  String? get performanceType;
  @override
  String? get periodName;
  @override
  int? get periodOrder;
  @override
  int? get periodPerformance;
  @override
  int? get status;
  @override
  @JsonKey(ignore: true)
  _$$_PeriodDataItemCopyWith<_$_PeriodDataItem> get copyWith =>
      throw _privateConstructorUsedError;
}

IConfigValue _$IConfigValueFromJson(Map<String, dynamic> json) {
  return _IConfigValue.fromJson(json);
}

/// @nodoc
mixin _$IConfigValue {
  List<StatisticInfoItem>? get statisticInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IConfigValueCopyWith<IConfigValue> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IConfigValueCopyWith<$Res> {
  factory $IConfigValueCopyWith(
          IConfigValue value, $Res Function(IConfigValue) then) =
      _$IConfigValueCopyWithImpl<$Res, IConfigValue>;
  @useResult
  $Res call({List<StatisticInfoItem>? statisticInfoList});
}

/// @nodoc
class _$IConfigValueCopyWithImpl<$Res, $Val extends IConfigValue>
    implements $IConfigValueCopyWith<$Res> {
  _$IConfigValueCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statisticInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      statisticInfoList: freezed == statisticInfoList
          ? _value.statisticInfoList
          : statisticInfoList // ignore: cast_nullable_to_non_nullable
              as List<StatisticInfoItem>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_IConfigValueCopyWith<$Res>
    implements $IConfigValueCopyWith<$Res> {
  factory _$$_IConfigValueCopyWith(
          _$_IConfigValue value, $Res Function(_$_IConfigValue) then) =
      __$$_IConfigValueCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<StatisticInfoItem>? statisticInfoList});
}

/// @nodoc
class __$$_IConfigValueCopyWithImpl<$Res>
    extends _$IConfigValueCopyWithImpl<$Res, _$_IConfigValue>
    implements _$$_IConfigValueCopyWith<$Res> {
  __$$_IConfigValueCopyWithImpl(
      _$_IConfigValue _value, $Res Function(_$_IConfigValue) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statisticInfoList = freezed,
  }) {
    return _then(_$_IConfigValue(
      statisticInfoList: freezed == statisticInfoList
          ? _value._statisticInfoList
          : statisticInfoList // ignore: cast_nullable_to_non_nullable
              as List<StatisticInfoItem>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_IConfigValue implements _IConfigValue {
  const _$_IConfigValue({final List<StatisticInfoItem>? statisticInfoList})
      : _statisticInfoList = statisticInfoList;

  factory _$_IConfigValue.fromJson(Map<String, dynamic> json) =>
      _$$_IConfigValueFromJson(json);

  final List<StatisticInfoItem>? _statisticInfoList;
  @override
  List<StatisticInfoItem>? get statisticInfoList {
    final value = _statisticInfoList;
    if (value == null) return null;
    if (_statisticInfoList is EqualUnmodifiableListView)
      return _statisticInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'IConfigValue(statisticInfoList: $statisticInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_IConfigValue &&
            const DeepCollectionEquality()
                .equals(other._statisticInfoList, _statisticInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_statisticInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_IConfigValueCopyWith<_$_IConfigValue> get copyWith =>
      __$$_IConfigValueCopyWithImpl<_$_IConfigValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_IConfigValueToJson(
      this,
    );
  }
}

abstract class _IConfigValue implements IConfigValue {
  const factory _IConfigValue(
      {final List<StatisticInfoItem>? statisticInfoList}) = _$_IConfigValue;

  factory _IConfigValue.fromJson(Map<String, dynamic> json) =
      _$_IConfigValue.fromJson;

  @override
  List<StatisticInfoItem>? get statisticInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_IConfigValueCopyWith<_$_IConfigValue> get copyWith =>
      throw _privateConstructorUsedError;
}

StatisticInfoItem _$StatisticInfoItemFromJson(Map<String, dynamic> json) {
  return _StatisticInfoItem.fromJson(json);
}

/// @nodoc
mixin _$StatisticInfoItem {
  List<String>? get courseSegmentCodeList => throw _privateConstructorUsedError;
  String? get statisticKey => throw _privateConstructorUsedError;
  String? get statisticName => throw _privateConstructorUsedError;
  int? get statisticType => throw _privateConstructorUsedError;
  String? get statisticUnit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StatisticInfoItemCopyWith<StatisticInfoItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatisticInfoItemCopyWith<$Res> {
  factory $StatisticInfoItemCopyWith(
          StatisticInfoItem value, $Res Function(StatisticInfoItem) then) =
      _$StatisticInfoItemCopyWithImpl<$Res, StatisticInfoItem>;
  @useResult
  $Res call(
      {List<String>? courseSegmentCodeList,
      String? statisticKey,
      String? statisticName,
      int? statisticType,
      String? statisticUnit});
}

/// @nodoc
class _$StatisticInfoItemCopyWithImpl<$Res, $Val extends StatisticInfoItem>
    implements $StatisticInfoItemCopyWith<$Res> {
  _$StatisticInfoItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentCodeList = freezed,
    Object? statisticKey = freezed,
    Object? statisticName = freezed,
    Object? statisticType = freezed,
    Object? statisticUnit = freezed,
  }) {
    return _then(_value.copyWith(
      courseSegmentCodeList: freezed == courseSegmentCodeList
          ? _value.courseSegmentCodeList
          : courseSegmentCodeList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      statisticKey: freezed == statisticKey
          ? _value.statisticKey
          : statisticKey // ignore: cast_nullable_to_non_nullable
              as String?,
      statisticName: freezed == statisticName
          ? _value.statisticName
          : statisticName // ignore: cast_nullable_to_non_nullable
              as String?,
      statisticType: freezed == statisticType
          ? _value.statisticType
          : statisticType // ignore: cast_nullable_to_non_nullable
              as int?,
      statisticUnit: freezed == statisticUnit
          ? _value.statisticUnit
          : statisticUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StatisticInfoItemCopyWith<$Res>
    implements $StatisticInfoItemCopyWith<$Res> {
  factory _$$_StatisticInfoItemCopyWith(_$_StatisticInfoItem value,
          $Res Function(_$_StatisticInfoItem) then) =
      __$$_StatisticInfoItemCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String>? courseSegmentCodeList,
      String? statisticKey,
      String? statisticName,
      int? statisticType,
      String? statisticUnit});
}

/// @nodoc
class __$$_StatisticInfoItemCopyWithImpl<$Res>
    extends _$StatisticInfoItemCopyWithImpl<$Res, _$_StatisticInfoItem>
    implements _$$_StatisticInfoItemCopyWith<$Res> {
  __$$_StatisticInfoItemCopyWithImpl(
      _$_StatisticInfoItem _value, $Res Function(_$_StatisticInfoItem) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseSegmentCodeList = freezed,
    Object? statisticKey = freezed,
    Object? statisticName = freezed,
    Object? statisticType = freezed,
    Object? statisticUnit = freezed,
  }) {
    return _then(_$_StatisticInfoItem(
      courseSegmentCodeList: freezed == courseSegmentCodeList
          ? _value._courseSegmentCodeList
          : courseSegmentCodeList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      statisticKey: freezed == statisticKey
          ? _value.statisticKey
          : statisticKey // ignore: cast_nullable_to_non_nullable
              as String?,
      statisticName: freezed == statisticName
          ? _value.statisticName
          : statisticName // ignore: cast_nullable_to_non_nullable
              as String?,
      statisticType: freezed == statisticType
          ? _value.statisticType
          : statisticType // ignore: cast_nullable_to_non_nullable
              as int?,
      statisticUnit: freezed == statisticUnit
          ? _value.statisticUnit
          : statisticUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StatisticInfoItem implements _StatisticInfoItem {
  const _$_StatisticInfoItem(
      {final List<String>? courseSegmentCodeList,
      this.statisticKey,
      this.statisticName,
      this.statisticType,
      this.statisticUnit})
      : _courseSegmentCodeList = courseSegmentCodeList;

  factory _$_StatisticInfoItem.fromJson(Map<String, dynamic> json) =>
      _$$_StatisticInfoItemFromJson(json);

  final List<String>? _courseSegmentCodeList;
  @override
  List<String>? get courseSegmentCodeList {
    final value = _courseSegmentCodeList;
    if (value == null) return null;
    if (_courseSegmentCodeList is EqualUnmodifiableListView)
      return _courseSegmentCodeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? statisticKey;
  @override
  final String? statisticName;
  @override
  final int? statisticType;
  @override
  final String? statisticUnit;

  @override
  String toString() {
    return 'StatisticInfoItem(courseSegmentCodeList: $courseSegmentCodeList, statisticKey: $statisticKey, statisticName: $statisticName, statisticType: $statisticType, statisticUnit: $statisticUnit)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StatisticInfoItem &&
            const DeepCollectionEquality()
                .equals(other._courseSegmentCodeList, _courseSegmentCodeList) &&
            (identical(other.statisticKey, statisticKey) ||
                other.statisticKey == statisticKey) &&
            (identical(other.statisticName, statisticName) ||
                other.statisticName == statisticName) &&
            (identical(other.statisticType, statisticType) ||
                other.statisticType == statisticType) &&
            (identical(other.statisticUnit, statisticUnit) ||
                other.statisticUnit == statisticUnit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_courseSegmentCodeList),
      statisticKey,
      statisticName,
      statisticType,
      statisticUnit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StatisticInfoItemCopyWith<_$_StatisticInfoItem> get copyWith =>
      __$$_StatisticInfoItemCopyWithImpl<_$_StatisticInfoItem>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StatisticInfoItemToJson(
      this,
    );
  }
}

abstract class _StatisticInfoItem implements StatisticInfoItem {
  const factory _StatisticInfoItem(
      {final List<String>? courseSegmentCodeList,
      final String? statisticKey,
      final String? statisticName,
      final int? statisticType,
      final String? statisticUnit}) = _$_StatisticInfoItem;

  factory _StatisticInfoItem.fromJson(Map<String, dynamic> json) =
      _$_StatisticInfoItem.fromJson;

  @override
  List<String>? get courseSegmentCodeList;
  @override
  String? get statisticKey;
  @override
  String? get statisticName;
  @override
  int? get statisticType;
  @override
  String? get statisticUnit;
  @override
  @JsonKey(ignore: true)
  _$$_StatisticInfoItemCopyWith<_$_StatisticInfoItem> get copyWith =>
      throw _privateConstructorUsedError;
}
