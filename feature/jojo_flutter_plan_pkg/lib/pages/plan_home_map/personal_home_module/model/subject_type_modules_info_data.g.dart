// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_type_modules_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SubjectTypeModulesInfo _$$_SubjectTypeModulesInfoFromJson(
        Map<String, dynamic> json) =>
    _$_SubjectTypeModulesInfo(
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      modules: (json['modules'] as List<dynamic>?)
          ?.map((e) => Module.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SubjectTypeModulesInfoToJson(
        _$_SubjectTypeModulesInfo instance) =>
    <String, dynamic>{
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'modules': instance.modules,
    };

_$_Module _$$_ModuleFromJson(Map<String, dynamic> json) => _$_Module(
      type: json['type'] as String?,
      name: json['name'] as String?,
      bestDays: json['bestDays'] as int?,
      icon: json['icon'] as String?,
      days: json['days'] as int?,
      num: json['num'] as int?,
      jumpRoute: json['jumpRoute'] as String?,
      teamList: (json['teamList'] as List<dynamic>?)
          ?.map((e) => TeamList.fromJson(e as Map<String, dynamic>))
          .toList(),
      medals: (json['medals'] as List<dynamic>?)
          ?.map((e) => Medel.fromJson(e as Map<String, dynamic>))
          .toList(),
      partners: (json['partners'] as List<dynamic>?)
          ?.map((e) => Partner.fromJson(e as Map<String, dynamic>))
          .toList(),
      img: json['img'] as String?,
      growthData: json['growthData'] == null
          ? null
          : GrowthData.fromJson(json['growthData'] as Map<String, dynamic>),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) =>
              LearnDataStatisticsData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ModuleToJson(_$_Module instance) => <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'bestDays': instance.bestDays,
      'icon': instance.icon,
      'days': instance.days,
      'num': instance.num,
      'jumpRoute': instance.jumpRoute,
      'teamList': instance.teamList,
      'medals': instance.medals,
      'partners': instance.partners,
      'img': instance.img,
      'growthData': instance.growthData,
      'data': instance.data,
    };

_$_LearnDataStatisticsData _$$_LearnDataStatisticsDataFromJson(
        Map<String, dynamic> json) =>
    _$_LearnDataStatisticsData(
      type: json['type'] as int?,
      name: json['name'] as String?,
      value: json['value'] as int?,
      tagImg: json['tagImg'] as String?,
    );

Map<String, dynamic> _$$_LearnDataStatisticsDataToJson(
        _$_LearnDataStatisticsData instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
      'value': instance.value,
      'tagImg': instance.tagImg,
    };

_$_Partner _$$_PartnerFromJson(Map<String, dynamic> json) => _$_Partner(
      nickName: json['nickName'] as String? ?? "",
      medal: json['medal'] as int? ?? 0,
      continuousDays: json['continuousDays'] as int? ?? 0,
      studyDays: json['studyDays'] as int? ?? 0,
      like: json['like'] as int? ?? 1,
      likeDescription: json['likeDescription'] as String? ?? "",
      img: json['img'] as String? ?? "",
      url: json['url'] as String? ?? "",
    );

Map<String, dynamic> _$$_PartnerToJson(_$_Partner instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'medal': instance.medal,
      'continuousDays': instance.continuousDays,
      'studyDays': instance.studyDays,
      'like': instance.like,
      'likeDescription': instance.likeDescription,
      'img': instance.img,
      'url': instance.url,
    };

_$_Medel _$$_MedelFromJson(Map<String, dynamic> json) => _$_Medel(
      name: json['name'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$_MedelToJson(_$_Medel instance) => <String, dynamic>{
      'name': instance.name,
      'img': instance.img,
    };

_$_TeamList _$$_TeamListFromJson(Map<String, dynamic> json) => _$_TeamList(
      photo: json['photo'] as String?,
      isSelf: json['isSelf'] as int?,
    );

Map<String, dynamic> _$$_TeamListToJson(_$_TeamList instance) =>
    <String, dynamic>{
      'photo': instance.photo,
      'isSelf': instance.isSelf,
    };

_$_GrowthData _$$_GrowthDataFromJson(Map<String, dynamic> json) =>
    _$_GrowthData(
      classId: json['classId'] as int?,
      courseSegmentCode: json['courseSegmentCode'] as int?,
    );

Map<String, dynamic> _$$_GrowthDataToJson(_$_GrowthData instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'courseSegmentCode': instance.courseSegmentCode,
    };
