import 'package:flutter/material.dart';

class OutlineText extends StatelessWidget {
  final String text;
  final TextStyle? textStyle;
  final Color strokeColor;
  final Color fillColor;
  final double strokeWidth;
  final TextAlign? textAlign;

  const OutlineText(
    this.text, {
    Key? key,
    this.textStyle,
    this.strokeColor = Colors.black,
    this.fillColor = Colors.white,
    this.strokeWidth = 1.0,
    this.textAlign,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 描边文字
        Text(
          text,
          textAlign: textAlign,
          style: textStyle?.copyWith(
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = strokeWidth
              ..color = strokeColor,
          ),
        ),
        // 填充文字
        Text(
          text,
          textAlign: textAlign,
          style: textStyle?.copyWith(
            color: fillColor,
          ),
        ),
      ],
    );
  }
}