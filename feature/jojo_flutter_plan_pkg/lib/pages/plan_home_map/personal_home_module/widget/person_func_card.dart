import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';

class PersonFuncCard extends StatefulWidget {
  final BoxDecoration? decoration;
  final double width;
  final double height;
  final bool canValueChangeAnima;
  final bool canIconClickAnima;
  final String? imageUrl;
  final String tipText;
  final String mainText;
  final String valueText;
  final bool isShowValueText;
  final VoidCallback? onTap;
  final bool canShowDot;
  const PersonFuncCard(
      {super.key,
      required this.width,
      required this.height,
      this.tipText = '',
      this.mainText = '',
      this.valueText = '',
      this.decoration,
      this.imageUrl,
      this.canValueChangeAnima = false,
      this.canIconClickAnima = false,
      this.isShowValueText = false,
      this.canShowDot = false,
      this.onTap});

  @override
  State<PersonFuncCard> createState() => _PersonFuncCardState();
}

class _PersonFuncCardState extends State<PersonFuncCard>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _textController;

  late Animation<double> _bounceUpAnimation;
  late Animation<double> _bounceDownAnimation;
  late Animation<double> _textFadeInAnimation;
  late Animation<double> _textFadeOutAnimation;

  bool _isAnimating = false;
  bool _showAnimatedText = false;
  bool _animationCompleted = false;

  @override
  void initState() {
    _initAnimations();
    super.initState();
  }

  // 计算显示的数值
  String get _displayText {
    if (_animationCompleted && widget.valueText.isNotEmpty) {
      try {
        // 解析主文字数值
        int mainValue = int.tryParse(widget.mainText) ?? 0;

        // 解析动画文字数值（去掉+号）
        String animatedValue = widget.valueText.replaceAll('+', '');
        int addValue = int.tryParse(animatedValue) ?? 0;

        // 返回计算结果
        return (mainValue + addValue).toString();
      } catch (e) {
        // 如果解析失败，返回原始文字
        return widget.mainText;
      }
    }
    return widget.mainText;
  }

  void _initAnimations() {
    // 弹跳动画控制器
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1050),
      vsync: this,
    );

    // 文字动画控制器
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1600),
      vsync: this,
    );

    // 弹起动画：延迟600ms，Y+5，150ms，Ease in
    _bounceUpAnimation = Tween<double>(
      begin: 0.0,
      end: -5.0.rdp,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: const Interval(0.57, 0.71, curve: Curves.easeIn),
    ));

    // 落下动画：Y+5，300ms，Ease out back
    _bounceDownAnimation = Tween<double>(
      begin: -5.0.rdp,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: const Interval(0.71, 1.0, curve: Curves.easeOutBack),
    ));

    // 文字淡入：延迟300ms，透明度0→1，300ms，Linear
    _textFadeInAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.1875, 0.375, curve: Curves.linear),
    ));

    // 文字淡出：延迟1000ms，透明度1→0，Linear
    _textFadeOutAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.625, 1.0, curve: Curves.linear),
    ));

    // 监听动画状态
    _bounceController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });

    _textController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _showAnimatedText = false;
          _animationCompleted = true;
        });
      }
    });
    if (widget.valueText.isNotEmpty) {
      _textController.reset();
      _textController.forward();
    }
  }

  Widget _buildRightWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.tipText,
          style: context.textstyles.smallestText.pf
              .copyWith(color: context.appColors.jColorGray5, height: 1.5),
        ),
        Transform.translate(
          offset: Offset(0, -4.rdp),
          child: Row(
            children: [
              Text(
                _displayText,
                style: context.textstyles.bodyText.pf.copyWith(
                    fontSize: 16.rdp,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                    color: context.appColors.jColorGray6),
              ),
              AnimatedBuilder(
                  animation: _textController,
                  builder: (context, child) {
                    double opacity = 0.0;
                    if (_textController.value <= 0.375) {
                      opacity = _textFadeInAnimation.value;
                    } else if (_textController.value >= 0.625) {
                      opacity = _textFadeOutAnimation.value;
                    } else {
                      opacity = 1.0;
                    }
                    return Opacity(
                      opacity: opacity,
                      child: Text(widget.valueText,
                          style: context.textstyles.bodyText.pf.copyWith(
                            fontSize: 16.rdp,
                            fontWeight: FontWeight.w500,
                            height: 1.5,
                            color: context.appColors.jColorRed4,
                          )),
                    );
                  })
            ],
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ClickWidget(
      type: ClickType.debounce,
      onTap: () {
        if (!_isAnimating) {
          // 重置动画控制器
          _bounceController.reset();
          // 设置动画状态
          setState(() {
            _isAnimating = true;
          });
          _bounceController.forward();
        }
        widget.onTap?.call();
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: widget.decoration,
        padding: EdgeInsets.all(12.rdp),
        clipBehavior: Clip.none,
        alignment: Alignment.centerLeft,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            AnimatedBuilder(
                animation: Listenable.merge([_bounceController]),
                builder: (context, child) {
                  double offsetY = 0.0;
                  if (_bounceController.value <= 0.71) {
                    offsetY = _bounceUpAnimation.value;
                  } else {
                    offsetY = _bounceDownAnimation.value;
                  }
                  return Transform.translate(
                    offset: Offset(0, offsetY),
                    child: ImageNetworkCached(
                      imageUrl: widget.imageUrl ?? '',
                      width: 32.rdp,
                      height: 32.rdp,
                    ),
                  );
                }),
            Positioned(left: 40.rdp, child: _buildRightWidget(context)),
            if (widget.canShowDot)
              Positioned(
                right: (0 - widget.height) + 16.rdp,
                top: -8.rdp,
                child: Container(
                  height: 10.rdp,
                  width: 10.rdp,
                  decoration: BoxDecoration(
                      color: context.appColors.jColorOrange4,
                      shape: BoxShape.circle,
                      border: Border.all(width: 2.rdp, color: Colors.white)),
                ),
              )
          ],
        ),
      ),
    );
  }
}
