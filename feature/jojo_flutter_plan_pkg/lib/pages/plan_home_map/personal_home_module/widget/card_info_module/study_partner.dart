import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/outline_text.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

import '../../model/subject_type_modules_info_data.dart';

class StudyPartner extends StatelessWidget {
  final List<Partner> partnerList;
  const StudyPartner({super.key, required this.partnerList});

  String getKudos(int kudosCount) {
    if (kudosCount < 1000) {
      return kudosCount.toString();
    } else {
      int kValue = kudosCount ~/ 1000;
      return '${kValue}k';
    }
  }

  Widget _buildHeadCard(BuildContext context, Partner partner) {
    return SizedBox(
      width: 82.rdp,
      height: 82.rdp,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(29.rdp),
              border: Border.all(
                color: context.appColors.jColorGray3,
                width: 1.rdp,
              ),
            ),
            child: CircleAvatar(
              radius: 29.rdp,
              backgroundColor: context.appColors.jColorGray2,
              child: ImageNetworkCached(
                imageUrl: partner.img ?? '',
                width: 58.rdp,
                height: 58.rdp,
              ),
            ),
          ),
          Positioned(
            bottom: 0.rdp,
            child: SizedBox(
              width: 68.rdp,
              height: 18.rdp,
              child: Text(
                partner.nickName ?? "",
                style: context.textstyles.smallestText.pf.copyWith(
                  fontSize: 12.rdp,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Positioned(
              right: 5.rdp,
              bottom: 16.rdp,
              child: SvgAssetWeb(
                assetName: AssetsSvg.LEARNING_INCENTIVES_ICON_FIRE_STUDY_DAY,
                package: Config.package,
                height: 24.rdp,
                width: 24.rdp,
              )),
          Positioned(
              right: 5.rdp,
              bottom: 17.rdp,
              child: Container(
                  alignment: Alignment.center,
                  width: 24.rdp,
                  child: OutlineText(getKudos(partner.continuousDays ?? 0),
                      textAlign: TextAlign.center,
                      textStyle: context.textstyles.bodyText.mrB.copyWith(
                        fontSize: 12.rdp,
                        fontStyle: FontStyle.normal,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'MohrRounded',
                        package: Config.package,
                      ),
                      strokeColor: HexColor("#ED601A"),
                      fillColor: Colors.white,
                      strokeWidth: 1.rdp)))
        ],
      ),
    );
  }

  Widget _buildlPartnerHeadCard(BuildContext context) {
    return Row(
      mainAxisAlignment: partnerList.length == 4
          ? MainAxisAlignment.spaceBetween
          : MainAxisAlignment.start,
      children: partnerList.map((e) => _buildHeadCard(context, e)).toList(),
    );
  }

  Widget _buildStudyPartner(BuildContext context) {
    return Container(
      height: 148.rdp,
      padding: EdgeInsets.symmetric(vertical: 14.rdp, horizontal: 0.rdp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.rdp),
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.rdp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).studyPartner,
                  style: context.textstyles.heading.pf.copyWith(
                    fontSize: 18.rdp,
                    fontWeight: FontWeight.w500,
                    color: context.appColors.jColorGray6.withOpacity(0.9),
                  ),
                ),
                Text(
                  S.of(context).more,
                  style: context.textstyles.remark.pf.copyWith(
                    fontSize: 14.rdp,
                    fontWeight: FontWeight.w400,
                    color: context.appColors.jColorGray5,
                  ),
                )
              ],
            ),
          ),
          _buildlPartnerHeadCard(context)
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildStudyPartner(context);
  }
}
