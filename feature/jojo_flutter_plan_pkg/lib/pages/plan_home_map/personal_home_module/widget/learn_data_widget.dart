import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/subject_type_modules_info_data.dart';

class LearnDataWidget extends StatefulHookWidget {
  final Module module;
  final bool showTitle;
  const LearnDataWidget(
      {Key? key, required this.module, required this.showTitle})
      : super(key: key);

  @override
  LearnDataWidgetState createState() => LearnDataWidgetState();
}

class LearnDataWidgetState extends State<LearnDataWidget> {
  @override
  Widget build(BuildContext context) {
    final data = widget.module.data;
    if (data == null || data.isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if(widget.showTitle)
            _moduleTitle(),
          if(!widget.showTitle)
            _title(),
          _contentWidget()
        ],
    );
  }

  Widget _moduleTitle(){
    return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.rdp),
                child: Text(
                  S.of(context).growthRecord,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 20.rdp,
                    color: HexColor('#404040'),
                    height: 1.5,
                  ),
                ),
              ),
              SizedBox(height: 8.rdp),
            ],
          );
  }

  Widget _contentWidget() {
    final learningMetrics = widget.module.data;
    if (learningMetrics == null) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (learningMetrics.isNotEmpty) _contentItemWidget(learningMetrics[0]),
        if (learningMetrics.length > 1) _contentItemWidget(learningMetrics[1]),
      ],
    );
  }

  Widget _contentItemWidget(LearnDataStatisticsData statistics) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 8.rdp),
        Text(
          statistics.name ?? '',
          style: TextStyle(
            fontSize: 12.rdp,
            color: HexColor("#666666"),
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(height: 8.rdp),
        _valueContentWidget(statistics),
      ],
    );
  }

  Widget _valueContentWidget(LearnDataStatisticsData statistics) {
    return Row(
      children: [
        _valueWidget(statistics),
        SizedBox(height: 4.rdp),
        _tagWidget(statistics),
      ],
    );
  }

  Widget _valueWidget(LearnDataStatisticsData statistics) {
    final type = statistics.type;
    if (type == 1) {
      return RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style, // 继承默认样式
          children: [
            TextSpan(
              text: '${statistics.value}',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: HexColor('#404040'),
                fontSize: 20.rdp,
                fontFamily: "Baloo_Regular",
                package: 'jojo_flutter_base',
              ),
            ),
            WidgetSpan(
              // 插入空白控件
              child: SizedBox(width: 2.rdp), // 固定间距
              alignment: PlaceholderAlignment.middle,
            ),
            TextSpan(
              text: '%',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: HexColor('#404040'),
                fontSize: 12.rdp,
                fontFamily: "Baloo_Regular",
                package: 'jojo_flutter_base',
              ),
            ),
          ],
        ),
      );
    } else if (type == 2) {
      return RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style, // 继承默认样式
          children: [
            if (statistics.getValueHour() > 0)
              TextSpan(
                text: '${statistics.getValueHour()}',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  color: HexColor('#404040'),
                  fontSize: 20.rdp,
                  fontFamily: "Baloo_Regular",
                  package: 'jojo_flutter_base',
                ),
              ),
            if (statistics.getValueHour() > 0)
              TextSpan(
                text: '小时',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: HexColor('#404040'),
                  fontSize: 12.rdp,
                  fontFamily: "Baloo_Regular",
                  package: 'jojo_flutter_base',
                ),
              ),
            if (statistics.getResidueMinute() > 0 ||
                statistics.getValueHour() <= 0)
              TextSpan(
                text: '${statistics.getResidueMinute()}',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  color: HexColor('#404040'),
                  fontSize: 20.rdp,
                  fontFamily: "Baloo_Regular",
                  package: 'jojo_flutter_base',
                ),
              ),
            if (statistics.getResidueMinute() > 0 ||
                statistics.getValueHour() <= 0)
              TextSpan(
                text: '分钟',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: HexColor('#404040'),
                  fontSize: 12.rdp,
                  fontFamily: "Baloo_Regular",
                  package: 'jojo_flutter_base',
                ),
              ),
          ],
        ),
      );
    } else if (type == 3) {
      return Text(
        statistics.getValueLevel(),
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 20.rdp,
          color: HexColor('#404040'),
          fontFamily: "Baloo_Regular",
          package: 'jojo_flutter_base',
        ),
      );
    } else if (type == 4) {
      return RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style, // 继承默认样式
          children: [
            TextSpan(
              text: '${statistics.value}',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: HexColor('#404040'),
                fontSize: 20.rdp,
                fontFamily: "Baloo_Regular",
                package: 'jojo_flutter_base',  
              ),
            ),
            WidgetSpan(
              // 插入空白控件
              child: SizedBox(width: 2.rdp), // 固定间距
              alignment: PlaceholderAlignment.middle,
            ),
            TextSpan(
              text: '个',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: HexColor('#404040'),
                fontSize: 12.rdp,
                fontFamily: "Baloo_Regular",
                package: 'jojo_flutter_base',
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _tagWidget(LearnDataStatisticsData statistics) {
    if (!statistics.haveTagImg()) {
      return const SizedBox.shrink();
    }
    return ImageNetworkCached(
      imageUrl: statistics.tagImg!,
      fit: BoxFit.contain,
      height: 22.rdp,
      width: 32.rdp,
    );
  }

  Widget _title() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          // 左侧自适应宽度
          child: Container(
            color: hexToColor('#E0DFDF'),
            height: 1.rdp,
          ),
        ),
        SizedBox(width: 8.rdp),
        Text(
          S.of(context).leanDataStatistics,
          style: TextStyle(
            fontSize: 12.rdp,
            color: hexToColor("#ACB2BB"),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(width: 8.rdp),
        Expanded(
          // 右侧自适应宽度
          child: Container(
            color: hexToColor('#E0DFDF'),
            height: 1.rdp,
          ),
        ),
      ],
    );
  }
}
