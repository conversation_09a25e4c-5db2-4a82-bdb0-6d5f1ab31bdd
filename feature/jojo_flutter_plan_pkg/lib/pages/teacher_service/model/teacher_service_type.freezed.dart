// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teacher_service_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeacherServiceData _$TeacherServiceDataFromJson(Map<String, dynamic> json) {
  return _TeacherServiceData.fromJson(json);
}

/// @nodoc
mixin _$TeacherServiceData {
  TeacherInfo? get teacherInfo => throw _privateConstructorUsedError;
  List<ServiceList>? get serviceList => throw _privateConstructorUsedError;
  bool? get isShowMore => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  String? get courseSegment => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  List<RollTeacherList>? get rollTeacherList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherServiceDataCopyWith<TeacherServiceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherServiceDataCopyWith<$Res> {
  factory $TeacherServiceDataCopyWith(
          TeacherServiceData value, $Res Function(TeacherServiceData) then) =
      _$TeacherServiceDataCopyWithImpl<$Res, TeacherServiceData>;
  @useResult
  $Res call(
      {TeacherInfo? teacherInfo,
      List<ServiceList>? serviceList,
      bool? isShowMore,
      String? courseKey,
      String? courseSegment,
      int? classId,
      List<RollTeacherList>? rollTeacherList});

  $TeacherInfoCopyWith<$Res>? get teacherInfo;
}

/// @nodoc
class _$TeacherServiceDataCopyWithImpl<$Res, $Val extends TeacherServiceData>
    implements $TeacherServiceDataCopyWith<$Res> {
  _$TeacherServiceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherInfo = freezed,
    Object? serviceList = freezed,
    Object? isShowMore = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? classId = freezed,
    Object? rollTeacherList = freezed,
  }) {
    return _then(_value.copyWith(
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as TeacherInfo?,
      serviceList: freezed == serviceList
          ? _value.serviceList
          : serviceList // ignore: cast_nullable_to_non_nullable
              as List<ServiceList>?,
      isShowMore: freezed == isShowMore
          ? _value.isShowMore
          : isShowMore // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      rollTeacherList: freezed == rollTeacherList
          ? _value.rollTeacherList
          : rollTeacherList // ignore: cast_nullable_to_non_nullable
              as List<RollTeacherList>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TeacherInfoCopyWith<$Res>? get teacherInfo {
    if (_value.teacherInfo == null) {
      return null;
    }

    return $TeacherInfoCopyWith<$Res>(_value.teacherInfo!, (value) {
      return _then(_value.copyWith(teacherInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeacherServiceDataCopyWith<$Res>
    implements $TeacherServiceDataCopyWith<$Res> {
  factory _$$_TeacherServiceDataCopyWith(_$_TeacherServiceData value,
          $Res Function(_$_TeacherServiceData) then) =
      __$$_TeacherServiceDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TeacherInfo? teacherInfo,
      List<ServiceList>? serviceList,
      bool? isShowMore,
      String? courseKey,
      String? courseSegment,
      int? classId,
      List<RollTeacherList>? rollTeacherList});

  @override
  $TeacherInfoCopyWith<$Res>? get teacherInfo;
}

/// @nodoc
class __$$_TeacherServiceDataCopyWithImpl<$Res>
    extends _$TeacherServiceDataCopyWithImpl<$Res, _$_TeacherServiceData>
    implements _$$_TeacherServiceDataCopyWith<$Res> {
  __$$_TeacherServiceDataCopyWithImpl(
      _$_TeacherServiceData _value, $Res Function(_$_TeacherServiceData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherInfo = freezed,
    Object? serviceList = freezed,
    Object? isShowMore = freezed,
    Object? courseKey = freezed,
    Object? courseSegment = freezed,
    Object? classId = freezed,
    Object? rollTeacherList = freezed,
  }) {
    return _then(_$_TeacherServiceData(
      teacherInfo: freezed == teacherInfo
          ? _value.teacherInfo
          : teacherInfo // ignore: cast_nullable_to_non_nullable
              as TeacherInfo?,
      serviceList: freezed == serviceList
          ? _value._serviceList
          : serviceList // ignore: cast_nullable_to_non_nullable
              as List<ServiceList>?,
      isShowMore: freezed == isShowMore
          ? _value.isShowMore
          : isShowMore // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      rollTeacherList: freezed == rollTeacherList
          ? _value._rollTeacherList
          : rollTeacherList // ignore: cast_nullable_to_non_nullable
              as List<RollTeacherList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherServiceData
    with DiagnosticableTreeMixin
    implements _TeacherServiceData {
  const _$_TeacherServiceData(
      {this.teacherInfo,
      final List<ServiceList>? serviceList,
      this.isShowMore,
      this.courseKey,
      this.courseSegment,
      this.classId,
      final List<RollTeacherList>? rollTeacherList})
      : _serviceList = serviceList,
        _rollTeacherList = rollTeacherList;

  factory _$_TeacherServiceData.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherServiceDataFromJson(json);

  @override
  final TeacherInfo? teacherInfo;
  final List<ServiceList>? _serviceList;
  @override
  List<ServiceList>? get serviceList {
    final value = _serviceList;
    if (value == null) return null;
    if (_serviceList is EqualUnmodifiableListView) return _serviceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isShowMore;
  @override
  final String? courseKey;
  @override
  final String? courseSegment;
  @override
  final int? classId;
  final List<RollTeacherList>? _rollTeacherList;
  @override
  List<RollTeacherList>? get rollTeacherList {
    final value = _rollTeacherList;
    if (value == null) return null;
    if (_rollTeacherList is EqualUnmodifiableListView) return _rollTeacherList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherServiceData(teacherInfo: $teacherInfo, serviceList: $serviceList, isShowMore: $isShowMore, courseKey: $courseKey, courseSegment: $courseSegment, classId: $classId, rollTeacherList: $rollTeacherList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherServiceData'))
      ..add(DiagnosticsProperty('teacherInfo', teacherInfo))
      ..add(DiagnosticsProperty('serviceList', serviceList))
      ..add(DiagnosticsProperty('isShowMore', isShowMore))
      ..add(DiagnosticsProperty('courseKey', courseKey))
      ..add(DiagnosticsProperty('courseSegment', courseSegment))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('rollTeacherList', rollTeacherList));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherServiceData &&
            (identical(other.teacherInfo, teacherInfo) ||
                other.teacherInfo == teacherInfo) &&
            const DeepCollectionEquality()
                .equals(other._serviceList, _serviceList) &&
            (identical(other.isShowMore, isShowMore) ||
                other.isShowMore == isShowMore) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.courseSegment, courseSegment) ||
                other.courseSegment == courseSegment) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            const DeepCollectionEquality()
                .equals(other._rollTeacherList, _rollTeacherList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teacherInfo,
      const DeepCollectionEquality().hash(_serviceList),
      isShowMore,
      courseKey,
      courseSegment,
      classId,
      const DeepCollectionEquality().hash(_rollTeacherList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherServiceDataCopyWith<_$_TeacherServiceData> get copyWith =>
      __$$_TeacherServiceDataCopyWithImpl<_$_TeacherServiceData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherServiceDataToJson(
      this,
    );
  }
}

abstract class _TeacherServiceData implements TeacherServiceData {
  const factory _TeacherServiceData(
      {final TeacherInfo? teacherInfo,
      final List<ServiceList>? serviceList,
      final bool? isShowMore,
      final String? courseKey,
      final String? courseSegment,
      final int? classId,
      final List<RollTeacherList>? rollTeacherList}) = _$_TeacherServiceData;

  factory _TeacherServiceData.fromJson(Map<String, dynamic> json) =
      _$_TeacherServiceData.fromJson;

  @override
  TeacherInfo? get teacherInfo;
  @override
  List<ServiceList>? get serviceList;
  @override
  bool? get isShowMore;
  @override
  String? get courseKey;
  @override
  String? get courseSegment;
  @override
  int? get classId;
  @override
  List<RollTeacherList>? get rollTeacherList;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherServiceDataCopyWith<_$_TeacherServiceData> get copyWith =>
      throw _privateConstructorUsedError;
}

TeacherInfo _$TeacherInfoFromJson(Map<String, dynamic> json) {
  return _TeacherInfo.fromJson(json);
}

/// @nodoc
mixin _$TeacherInfo {
  String? get teacherImg => throw _privateConstructorUsedError;
  String? get teacherAna => throw _privateConstructorUsedError;
  int? get teacherId => throw _privateConstructorUsedError;
  String? get teacherName => throw _privateConstructorUsedError;
  bool? get needAddTeacher => throw _privateConstructorUsedError;
  String? get addTeacherUrl => throw _privateConstructorUsedError;
  dynamic get classId => throw _privateConstructorUsedError;
  int? get needShowContactTeacher => throw _privateConstructorUsedError;
  String? get contactTeacherUrl => throw _privateConstructorUsedError;
  bool? get openClass => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherInfoCopyWith<TeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherInfoCopyWith<$Res> {
  factory $TeacherInfoCopyWith(
          TeacherInfo value, $Res Function(TeacherInfo) then) =
      _$TeacherInfoCopyWithImpl<$Res, TeacherInfo>;
  @useResult
  $Res call(
      {String? teacherImg,
      String? teacherAna,
      int? teacherId,
      String? teacherName,
      bool? needAddTeacher,
      String? addTeacherUrl,
      dynamic classId,
      int? needShowContactTeacher,
      String? contactTeacherUrl,
      bool? openClass});
}

/// @nodoc
class _$TeacherInfoCopyWithImpl<$Res, $Val extends TeacherInfo>
    implements $TeacherInfoCopyWith<$Res> {
  _$TeacherInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherImg = freezed,
    Object? teacherAna = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
    Object? needAddTeacher = freezed,
    Object? addTeacherUrl = freezed,
    Object? classId = freezed,
    Object? needShowContactTeacher = freezed,
    Object? contactTeacherUrl = freezed,
    Object? openClass = freezed,
  }) {
    return _then(_value.copyWith(
      teacherImg: freezed == teacherImg
          ? _value.teacherImg
          : teacherImg // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAna: freezed == teacherAna
          ? _value.teacherAna
          : teacherAna // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      needAddTeacher: freezed == needAddTeacher
          ? _value.needAddTeacher
          : needAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needShowContactTeacher: freezed == needShowContactTeacher
          ? _value.needShowContactTeacher
          : needShowContactTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      openClass: freezed == openClass
          ? _value.openClass
          : openClass // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherInfoCopyWith<$Res>
    implements $TeacherInfoCopyWith<$Res> {
  factory _$$_TeacherInfoCopyWith(
          _$_TeacherInfo value, $Res Function(_$_TeacherInfo) then) =
      __$$_TeacherInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? teacherImg,
      String? teacherAna,
      int? teacherId,
      String? teacherName,
      bool? needAddTeacher,
      String? addTeacherUrl,
      dynamic classId,
      int? needShowContactTeacher,
      String? contactTeacherUrl,
      bool? openClass});
}

/// @nodoc
class __$$_TeacherInfoCopyWithImpl<$Res>
    extends _$TeacherInfoCopyWithImpl<$Res, _$_TeacherInfo>
    implements _$$_TeacherInfoCopyWith<$Res> {
  __$$_TeacherInfoCopyWithImpl(
      _$_TeacherInfo _value, $Res Function(_$_TeacherInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherImg = freezed,
    Object? teacherAna = freezed,
    Object? teacherId = freezed,
    Object? teacherName = freezed,
    Object? needAddTeacher = freezed,
    Object? addTeacherUrl = freezed,
    Object? classId = freezed,
    Object? needShowContactTeacher = freezed,
    Object? contactTeacherUrl = freezed,
    Object? openClass = freezed,
  }) {
    return _then(_$_TeacherInfo(
      teacherImg: freezed == teacherImg
          ? _value.teacherImg
          : teacherImg // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherAna: freezed == teacherAna
          ? _value.teacherAna
          : teacherAna // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      needAddTeacher: freezed == needAddTeacher
          ? _value.needAddTeacher
          : needAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needShowContactTeacher: freezed == needShowContactTeacher
          ? _value.needShowContactTeacher
          : needShowContactTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      openClass: freezed == openClass
          ? _value.openClass
          : openClass // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherInfo with DiagnosticableTreeMixin implements _TeacherInfo {
  const _$_TeacherInfo(
      {this.teacherImg,
      this.teacherAna,
      this.teacherId,
      this.teacherName,
      this.needAddTeacher,
      this.addTeacherUrl,
      this.classId,
      this.needShowContactTeacher,
      this.contactTeacherUrl,
      this.openClass});

  factory _$_TeacherInfo.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherInfoFromJson(json);

  @override
  final String? teacherImg;
  @override
  final String? teacherAna;
  @override
  final int? teacherId;
  @override
  final String? teacherName;
  @override
  final bool? needAddTeacher;
  @override
  final String? addTeacherUrl;
  @override
  final dynamic classId;
  @override
  final int? needShowContactTeacher;
  @override
  final String? contactTeacherUrl;
  @override
  final bool? openClass;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherInfo(teacherImg: $teacherImg, teacherAna: $teacherAna, teacherId: $teacherId, teacherName: $teacherName, needAddTeacher: $needAddTeacher, addTeacherUrl: $addTeacherUrl, classId: $classId, needShowContactTeacher: $needShowContactTeacher, contactTeacherUrl: $contactTeacherUrl, openClass: $openClass)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherInfo'))
      ..add(DiagnosticsProperty('teacherImg', teacherImg))
      ..add(DiagnosticsProperty('teacherAna', teacherAna))
      ..add(DiagnosticsProperty('teacherId', teacherId))
      ..add(DiagnosticsProperty('teacherName', teacherName))
      ..add(DiagnosticsProperty('needAddTeacher', needAddTeacher))
      ..add(DiagnosticsProperty('addTeacherUrl', addTeacherUrl))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(
          DiagnosticsProperty('needShowContactTeacher', needShowContactTeacher))
      ..add(DiagnosticsProperty('contactTeacherUrl', contactTeacherUrl))
      ..add(DiagnosticsProperty('openClass', openClass));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherInfo &&
            (identical(other.teacherImg, teacherImg) ||
                other.teacherImg == teacherImg) &&
            (identical(other.teacherAna, teacherAna) ||
                other.teacherAna == teacherAna) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.needAddTeacher, needAddTeacher) ||
                other.needAddTeacher == needAddTeacher) &&
            (identical(other.addTeacherUrl, addTeacherUrl) ||
                other.addTeacherUrl == addTeacherUrl) &&
            const DeepCollectionEquality().equals(other.classId, classId) &&
            (identical(other.needShowContactTeacher, needShowContactTeacher) ||
                other.needShowContactTeacher == needShowContactTeacher) &&
            (identical(other.contactTeacherUrl, contactTeacherUrl) ||
                other.contactTeacherUrl == contactTeacherUrl) &&
            (identical(other.openClass, openClass) ||
                other.openClass == openClass));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teacherImg,
      teacherAna,
      teacherId,
      teacherName,
      needAddTeacher,
      addTeacherUrl,
      const DeepCollectionEquality().hash(classId),
      needShowContactTeacher,
      contactTeacherUrl,
      openClass);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherInfoCopyWith<_$_TeacherInfo> get copyWith =>
      __$$_TeacherInfoCopyWithImpl<_$_TeacherInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherInfoToJson(
      this,
    );
  }
}

abstract class _TeacherInfo implements TeacherInfo {
  const factory _TeacherInfo(
      {final String? teacherImg,
      final String? teacherAna,
      final int? teacherId,
      final String? teacherName,
      final bool? needAddTeacher,
      final String? addTeacherUrl,
      final dynamic classId,
      final int? needShowContactTeacher,
      final String? contactTeacherUrl,
      final bool? openClass}) = _$_TeacherInfo;

  factory _TeacherInfo.fromJson(Map<String, dynamic> json) =
      _$_TeacherInfo.fromJson;

  @override
  String? get teacherImg;
  @override
  String? get teacherAna;
  @override
  int? get teacherId;
  @override
  String? get teacherName;
  @override
  bool? get needAddTeacher;
  @override
  String? get addTeacherUrl;
  @override
  dynamic get classId;
  @override
  int? get needShowContactTeacher;
  @override
  String? get contactTeacherUrl;
  @override
  bool? get openClass;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherInfoCopyWith<_$_TeacherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ServiceList _$ServiceListFromJson(Map<String, dynamic> json) {
  return _ServiceList.fromJson(json);
}

/// @nodoc
mixin _$ServiceList {
  String? get key => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get configName => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;
  int? get updateCount => throw _privateConstructorUsedError;
  String? get appDetailPageUrl => throw _privateConstructorUsedError;
  int? get showOrder => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ServiceListCopyWith<ServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServiceListCopyWith<$Res> {
  factory $ServiceListCopyWith(
          ServiceList value, $Res Function(ServiceList) then) =
      _$ServiceListCopyWithImpl<$Res, ServiceList>;
  @useResult
  $Res call(
      {String? key,
      String? name,
      String? configName,
      String? icon,
      String? color,
      int? updateCount,
      String? appDetailPageUrl,
      int? showOrder});
}

/// @nodoc
class _$ServiceListCopyWithImpl<$Res, $Val extends ServiceList>
    implements $ServiceListCopyWith<$Res> {
  _$ServiceListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = freezed,
    Object? name = freezed,
    Object? configName = freezed,
    Object? icon = freezed,
    Object? color = freezed,
    Object? updateCount = freezed,
    Object? appDetailPageUrl = freezed,
    Object? showOrder = freezed,
  }) {
    return _then(_value.copyWith(
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      updateCount: freezed == updateCount
          ? _value.updateCount
          : updateCount // ignore: cast_nullable_to_non_nullable
              as int?,
      appDetailPageUrl: freezed == appDetailPageUrl
          ? _value.appDetailPageUrl
          : appDetailPageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ServiceListCopyWith<$Res>
    implements $ServiceListCopyWith<$Res> {
  factory _$$_ServiceListCopyWith(
          _$_ServiceList value, $Res Function(_$_ServiceList) then) =
      __$$_ServiceListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? key,
      String? name,
      String? configName,
      String? icon,
      String? color,
      int? updateCount,
      String? appDetailPageUrl,
      int? showOrder});
}

/// @nodoc
class __$$_ServiceListCopyWithImpl<$Res>
    extends _$ServiceListCopyWithImpl<$Res, _$_ServiceList>
    implements _$$_ServiceListCopyWith<$Res> {
  __$$_ServiceListCopyWithImpl(
      _$_ServiceList _value, $Res Function(_$_ServiceList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = freezed,
    Object? name = freezed,
    Object? configName = freezed,
    Object? icon = freezed,
    Object? color = freezed,
    Object? updateCount = freezed,
    Object? appDetailPageUrl = freezed,
    Object? showOrder = freezed,
  }) {
    return _then(_$_ServiceList(
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      configName: freezed == configName
          ? _value.configName
          : configName // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      updateCount: freezed == updateCount
          ? _value.updateCount
          : updateCount // ignore: cast_nullable_to_non_nullable
              as int?,
      appDetailPageUrl: freezed == appDetailPageUrl
          ? _value.appDetailPageUrl
          : appDetailPageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ServiceList with DiagnosticableTreeMixin implements _ServiceList {
  const _$_ServiceList(
      {this.key,
      this.name,
      this.configName,
      this.icon,
      this.color,
      this.updateCount,
      this.appDetailPageUrl,
      this.showOrder});

  factory _$_ServiceList.fromJson(Map<String, dynamic> json) =>
      _$$_ServiceListFromJson(json);

  @override
  final String? key;
  @override
  final String? name;
  @override
  final String? configName;
  @override
  final String? icon;
  @override
  final String? color;
  @override
  final int? updateCount;
  @override
  final String? appDetailPageUrl;
  @override
  final int? showOrder;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ServiceList(key: $key, name: $name, configName: $configName, icon: $icon, color: $color, updateCount: $updateCount, appDetailPageUrl: $appDetailPageUrl, showOrder: $showOrder)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ServiceList'))
      ..add(DiagnosticsProperty('key', key))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('configName', configName))
      ..add(DiagnosticsProperty('icon', icon))
      ..add(DiagnosticsProperty('color', color))
      ..add(DiagnosticsProperty('updateCount', updateCount))
      ..add(DiagnosticsProperty('appDetailPageUrl', appDetailPageUrl))
      ..add(DiagnosticsProperty('showOrder', showOrder));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ServiceList &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.configName, configName) ||
                other.configName == configName) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.updateCount, updateCount) ||
                other.updateCount == updateCount) &&
            (identical(other.appDetailPageUrl, appDetailPageUrl) ||
                other.appDetailPageUrl == appDetailPageUrl) &&
            (identical(other.showOrder, showOrder) ||
                other.showOrder == showOrder));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, key, name, configName, icon,
      color, updateCount, appDetailPageUrl, showOrder);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ServiceListCopyWith<_$_ServiceList> get copyWith =>
      __$$_ServiceListCopyWithImpl<_$_ServiceList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ServiceListToJson(
      this,
    );
  }
}

abstract class _ServiceList implements ServiceList {
  const factory _ServiceList(
      {final String? key,
      final String? name,
      final String? configName,
      final String? icon,
      final String? color,
      final int? updateCount,
      final String? appDetailPageUrl,
      final int? showOrder}) = _$_ServiceList;

  factory _ServiceList.fromJson(Map<String, dynamic> json) =
      _$_ServiceList.fromJson;

  @override
  String? get key;
  @override
  String? get name;
  @override
  String? get configName;
  @override
  String? get icon;
  @override
  String? get color;
  @override
  int? get updateCount;
  @override
  String? get appDetailPageUrl;
  @override
  int? get showOrder;
  @override
  @JsonKey(ignore: true)
  _$$_ServiceListCopyWith<_$_ServiceList> get copyWith =>
      throw _privateConstructorUsedError;
}

RollTeacherList _$RollTeacherListFromJson(Map<String, dynamic> json) {
  return _RollTeacherList.fromJson(json);
}

/// @nodoc
mixin _$RollTeacherList {
// 老师头像
  String? get teacherImg => throw _privateConstructorUsedError; // 加老师状态
  bool? get needAddTeacher => throw _privateConstructorUsedError; // 老师语录
  String? get teacherAna => throw _privateConstructorUsedError; // 老师头像
  String? get teacherName => throw _privateConstructorUsedError; // 老师id
  int? get teacherId => throw _privateConstructorUsedError; // 老师对应的班级id
  int? get classId => throw _privateConstructorUsedError; // 添加老师链接
  String? get addTeacherUrl => throw _privateConstructorUsedError; // 是否展示联系老师
  int? get needShowContactTeacher =>
      throw _privateConstructorUsedError; // 联系老师链接
  String? get contactTeacherUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RollTeacherListCopyWith<RollTeacherList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RollTeacherListCopyWith<$Res> {
  factory $RollTeacherListCopyWith(
          RollTeacherList value, $Res Function(RollTeacherList) then) =
      _$RollTeacherListCopyWithImpl<$Res, RollTeacherList>;
  @useResult
  $Res call(
      {String? teacherImg,
      bool? needAddTeacher,
      String? teacherAna,
      String? teacherName,
      int? teacherId,
      int? classId,
      String? addTeacherUrl,
      int? needShowContactTeacher,
      String? contactTeacherUrl});
}

/// @nodoc
class _$RollTeacherListCopyWithImpl<$Res, $Val extends RollTeacherList>
    implements $RollTeacherListCopyWith<$Res> {
  _$RollTeacherListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherImg = freezed,
    Object? needAddTeacher = freezed,
    Object? teacherAna = freezed,
    Object? teacherName = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
    Object? addTeacherUrl = freezed,
    Object? needShowContactTeacher = freezed,
    Object? contactTeacherUrl = freezed,
  }) {
    return _then(_value.copyWith(
      teacherImg: freezed == teacherImg
          ? _value.teacherImg
          : teacherImg // ignore: cast_nullable_to_non_nullable
              as String?,
      needAddTeacher: freezed == needAddTeacher
          ? _value.needAddTeacher
          : needAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      teacherAna: freezed == teacherAna
          ? _value.teacherAna
          : teacherAna // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      needShowContactTeacher: freezed == needShowContactTeacher
          ? _value.needShowContactTeacher
          : needShowContactTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RollTeacherListCopyWith<$Res>
    implements $RollTeacherListCopyWith<$Res> {
  factory _$$_RollTeacherListCopyWith(
          _$_RollTeacherList value, $Res Function(_$_RollTeacherList) then) =
      __$$_RollTeacherListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? teacherImg,
      bool? needAddTeacher,
      String? teacherAna,
      String? teacherName,
      int? teacherId,
      int? classId,
      String? addTeacherUrl,
      int? needShowContactTeacher,
      String? contactTeacherUrl});
}

/// @nodoc
class __$$_RollTeacherListCopyWithImpl<$Res>
    extends _$RollTeacherListCopyWithImpl<$Res, _$_RollTeacherList>
    implements _$$_RollTeacherListCopyWith<$Res> {
  __$$_RollTeacherListCopyWithImpl(
      _$_RollTeacherList _value, $Res Function(_$_RollTeacherList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherImg = freezed,
    Object? needAddTeacher = freezed,
    Object? teacherAna = freezed,
    Object? teacherName = freezed,
    Object? teacherId = freezed,
    Object? classId = freezed,
    Object? addTeacherUrl = freezed,
    Object? needShowContactTeacher = freezed,
    Object? contactTeacherUrl = freezed,
  }) {
    return _then(_$_RollTeacherList(
      teacherImg: freezed == teacherImg
          ? _value.teacherImg
          : teacherImg // ignore: cast_nullable_to_non_nullable
              as String?,
      needAddTeacher: freezed == needAddTeacher
          ? _value.needAddTeacher
          : needAddTeacher // ignore: cast_nullable_to_non_nullable
              as bool?,
      teacherAna: freezed == teacherAna
          ? _value.teacherAna
          : teacherAna // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherName: freezed == teacherName
          ? _value.teacherName
          : teacherName // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherId: freezed == teacherId
          ? _value.teacherId
          : teacherId // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      addTeacherUrl: freezed == addTeacherUrl
          ? _value.addTeacherUrl
          : addTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      needShowContactTeacher: freezed == needShowContactTeacher
          ? _value.needShowContactTeacher
          : needShowContactTeacher // ignore: cast_nullable_to_non_nullable
              as int?,
      contactTeacherUrl: freezed == contactTeacherUrl
          ? _value.contactTeacherUrl
          : contactTeacherUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RollTeacherList
    with DiagnosticableTreeMixin
    implements _RollTeacherList {
  const _$_RollTeacherList(
      {this.teacherImg,
      this.needAddTeacher,
      this.teacherAna,
      this.teacherName,
      this.teacherId,
      this.classId,
      this.addTeacherUrl,
      this.needShowContactTeacher,
      this.contactTeacherUrl});

  factory _$_RollTeacherList.fromJson(Map<String, dynamic> json) =>
      _$$_RollTeacherListFromJson(json);

// 老师头像
  @override
  final String? teacherImg;
// 加老师状态
  @override
  final bool? needAddTeacher;
// 老师语录
  @override
  final String? teacherAna;
// 老师头像
  @override
  final String? teacherName;
// 老师id
  @override
  final int? teacherId;
// 老师对应的班级id
  @override
  final int? classId;
// 添加老师链接
  @override
  final String? addTeacherUrl;
// 是否展示联系老师
  @override
  final int? needShowContactTeacher;
// 联系老师链接
  @override
  final String? contactTeacherUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RollTeacherList(teacherImg: $teacherImg, needAddTeacher: $needAddTeacher, teacherAna: $teacherAna, teacherName: $teacherName, teacherId: $teacherId, classId: $classId, addTeacherUrl: $addTeacherUrl, needShowContactTeacher: $needShowContactTeacher, contactTeacherUrl: $contactTeacherUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RollTeacherList'))
      ..add(DiagnosticsProperty('teacherImg', teacherImg))
      ..add(DiagnosticsProperty('needAddTeacher', needAddTeacher))
      ..add(DiagnosticsProperty('teacherAna', teacherAna))
      ..add(DiagnosticsProperty('teacherName', teacherName))
      ..add(DiagnosticsProperty('teacherId', teacherId))
      ..add(DiagnosticsProperty('classId', classId))
      ..add(DiagnosticsProperty('addTeacherUrl', addTeacherUrl))
      ..add(
          DiagnosticsProperty('needShowContactTeacher', needShowContactTeacher))
      ..add(DiagnosticsProperty('contactTeacherUrl', contactTeacherUrl));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RollTeacherList &&
            (identical(other.teacherImg, teacherImg) ||
                other.teacherImg == teacherImg) &&
            (identical(other.needAddTeacher, needAddTeacher) ||
                other.needAddTeacher == needAddTeacher) &&
            (identical(other.teacherAna, teacherAna) ||
                other.teacherAna == teacherAna) &&
            (identical(other.teacherName, teacherName) ||
                other.teacherName == teacherName) &&
            (identical(other.teacherId, teacherId) ||
                other.teacherId == teacherId) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.addTeacherUrl, addTeacherUrl) ||
                other.addTeacherUrl == addTeacherUrl) &&
            (identical(other.needShowContactTeacher, needShowContactTeacher) ||
                other.needShowContactTeacher == needShowContactTeacher) &&
            (identical(other.contactTeacherUrl, contactTeacherUrl) ||
                other.contactTeacherUrl == contactTeacherUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teacherImg,
      needAddTeacher,
      teacherAna,
      teacherName,
      teacherId,
      classId,
      addTeacherUrl,
      needShowContactTeacher,
      contactTeacherUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RollTeacherListCopyWith<_$_RollTeacherList> get copyWith =>
      __$$_RollTeacherListCopyWithImpl<_$_RollTeacherList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RollTeacherListToJson(
      this,
    );
  }
}

abstract class _RollTeacherList implements RollTeacherList {
  const factory _RollTeacherList(
      {final String? teacherImg,
      final bool? needAddTeacher,
      final String? teacherAna,
      final String? teacherName,
      final int? teacherId,
      final int? classId,
      final String? addTeacherUrl,
      final int? needShowContactTeacher,
      final String? contactTeacherUrl}) = _$_RollTeacherList;

  factory _RollTeacherList.fromJson(Map<String, dynamic> json) =
      _$_RollTeacherList.fromJson;

  @override // 老师头像
  String? get teacherImg;
  @override // 加老师状态
  bool? get needAddTeacher;
  @override // 老师语录
  String? get teacherAna;
  @override // 老师头像
  String? get teacherName;
  @override // 老师id
  int? get teacherId;
  @override // 老师对应的班级id
  int? get classId;
  @override // 添加老师链接
  String? get addTeacherUrl;
  @override // 是否展示联系老师
  int? get needShowContactTeacher;
  @override // 联系老师链接
  String? get contactTeacherUrl;
  @override
  @JsonKey(ignore: true)
  _$$_RollTeacherListCopyWith<_$_RollTeacherList> get copyWith =>
      throw _privateConstructorUsedError;
}
