// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teacher_service_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeacherServiceData _$$_TeacherServiceDataFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherServiceData(
      teacherInfo: json['teacherInfo'] == null
          ? null
          : TeacherInfo.fromJson(json['teacherInfo'] as Map<String, dynamic>),
      serviceList: (json['serviceList'] as List<dynamic>?)
          ?.map((e) => ServiceList.fromJson(e as Map<String, dynamic>))
          .toList(),
      isShowMore: json['isShowMore'] as bool?,
      courseKey: json['courseKey'] as String?,
      courseSegment: json['courseSegment'] as String?,
      classId: json['classId'] as int?,
      rollTeacherList: (json['rollTeacherList'] as List<dynamic>?)
          ?.map((e) => RollTeacherList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeacherServiceDataToJson(
        _$_TeacherServiceData instance) =>
    <String, dynamic>{
      'teacherInfo': instance.teacherInfo,
      'serviceList': instance.serviceList,
      'isShowMore': instance.isShowMore,
      'courseKey': instance.courseKey,
      'courseSegment': instance.courseSegment,
      'classId': instance.classId,
      'rollTeacherList': instance.rollTeacherList,
    };

_$_TeacherInfo _$$_TeacherInfoFromJson(Map<String, dynamic> json) =>
    _$_TeacherInfo(
      teacherImg: json['teacherImg'] as String?,
      teacherAna: json['teacherAna'] as String?,
      teacherId: json['teacherId'] as int?,
      teacherName: json['teacherName'] as String?,
      needAddTeacher: json['needAddTeacher'] as bool?,
      addTeacherUrl: json['addTeacherUrl'] as String?,
      classId: json['classId'],
      needShowContactTeacher: json['needShowContactTeacher'] as int?,
      contactTeacherUrl: json['contactTeacherUrl'] as String?,
      openClass: json['openClass'] as bool?,
    );

Map<String, dynamic> _$$_TeacherInfoToJson(_$_TeacherInfo instance) =>
    <String, dynamic>{
      'teacherImg': instance.teacherImg,
      'teacherAna': instance.teacherAna,
      'teacherId': instance.teacherId,
      'teacherName': instance.teacherName,
      'needAddTeacher': instance.needAddTeacher,
      'addTeacherUrl': instance.addTeacherUrl,
      'classId': instance.classId,
      'needShowContactTeacher': instance.needShowContactTeacher,
      'contactTeacherUrl': instance.contactTeacherUrl,
      'openClass': instance.openClass,
    };

_$_ServiceList _$$_ServiceListFromJson(Map<String, dynamic> json) =>
    _$_ServiceList(
      key: json['key'] as String?,
      name: json['name'] as String?,
      configName: json['configName'] as String?,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      updateCount: json['updateCount'] as int?,
      appDetailPageUrl: json['appDetailPageUrl'] as String?,
      showOrder: json['showOrder'] as int?,
    );

Map<String, dynamic> _$$_ServiceListToJson(_$_ServiceList instance) =>
    <String, dynamic>{
      'key': instance.key,
      'name': instance.name,
      'configName': instance.configName,
      'icon': instance.icon,
      'color': instance.color,
      'updateCount': instance.updateCount,
      'appDetailPageUrl': instance.appDetailPageUrl,
      'showOrder': instance.showOrder,
    };

_$_RollTeacherList _$$_RollTeacherListFromJson(Map<String, dynamic> json) =>
    _$_RollTeacherList(
      teacherImg: json['teacherImg'] as String?,
      needAddTeacher: json['needAddTeacher'] as bool?,
      teacherAna: json['teacherAna'] as String?,
      teacherName: json['teacherName'] as String?,
      teacherId: json['teacherId'] as int?,
      classId: json['classId'] as int?,
      addTeacherUrl: json['addTeacherUrl'] as String?,
      needShowContactTeacher: json['needShowContactTeacher'] as int?,
      contactTeacherUrl: json['contactTeacherUrl'] as String?,
    );

Map<String, dynamic> _$$_RollTeacherListToJson(_$_RollTeacherList instance) =>
    <String, dynamic>{
      'teacherImg': instance.teacherImg,
      'needAddTeacher': instance.needAddTeacher,
      'teacherAna': instance.teacherAna,
      'teacherName': instance.teacherName,
      'teacherId': instance.teacherId,
      'classId': instance.classId,
      'addTeacherUrl': instance.addTeacherUrl,
      'needShowContactTeacher': instance.needShowContactTeacher,
      'contactTeacherUrl': instance.contactTeacherUrl,
    };
