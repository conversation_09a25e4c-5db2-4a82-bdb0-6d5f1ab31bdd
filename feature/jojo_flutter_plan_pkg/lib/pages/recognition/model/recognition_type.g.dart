// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recognition_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_RecognitionData _$$_RecognitionDataFromJson(Map<String, dynamic> json) =>
    _$_RecognitionData(
      citeName: json['citeName'] as String?,
      userTitleInfo: json['userTitleInfo'] == null
          ? null
          : UserTitleInfo.fromJson(
              json['userTitleInfo'] as Map<String, dynamic>),
      backgroundImage: json['backgroundImage'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      classUserTitleInfoList: json['classUserTitleInfoList'] as String?,
      teacherRemark: json['teacherRemark'] == null
          ? null
          : TeacherRemark.fromJson(
              json['teacherRemark'] as Map<String, dynamic>),
      citeShareUrl: json['citeShareUrl'] as String?,
      courseKey: json['courseKey'] as String?,
      firstGearTotal: json['firstGearTotal'] as int?,
      secondGearTotal: json['secondGearTotal'] as int?,
      firstGearId: json['firstGearId'] as int?,
      secondGearId: json['secondGearId'] as int?,
      classPraiseId: json['classPraiseId'] as int?,
      firstGearTitle: json['firstGearTitle'] as String?,
      firstGearLevel: json['firstGearLevel'] as String?,
      secondGearTitle: json['secondGearTitle'] as String?,
      secondGearLevel: json['secondGearLevel'] as String?,
    );

Map<String, dynamic> _$$_RecognitionDataToJson(_$_RecognitionData instance) =>
    <String, dynamic>{
      'citeName': instance.citeName,
      'userTitleInfo': instance.userTitleInfo,
      'backgroundImage': instance.backgroundImage,
      'backgroundColor': instance.backgroundColor,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'classUserTitleInfoList': instance.classUserTitleInfoList,
      'teacherRemark': instance.teacherRemark,
      'citeShareUrl': instance.citeShareUrl,
      'courseKey': instance.courseKey,
      'firstGearTotal': instance.firstGearTotal,
      'secondGearTotal': instance.secondGearTotal,
      'firstGearId': instance.firstGearId,
      'secondGearId': instance.secondGearId,
      'classPraiseId': instance.classPraiseId,
      'firstGearTitle': instance.firstGearTitle,
      'firstGearLevel': instance.firstGearLevel,
      'secondGearTitle': instance.secondGearTitle,
      'secondGearLevel': instance.secondGearLevel,
    };

_$_UserTitleInfo _$$_UserTitleInfoFromJson(Map<String, dynamic> json) =>
    _$_UserTitleInfo(
      userId: json['userId'] as int?,
      title: json['title'] as String?,
      status: json['status'] as int?,
      nickname: json['nickname'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      remark: json['remark'] as String?,
      certificateBackImage: json['certificateBackImage'] as String?,
      certificateImage: json['certificateImage'] as String?,
      certificateTime: json['certificateTime'] as String?,
      mendMimiUrl: json['mendMimiUrl'] as String?,
      mendAppUrl: json['mendAppUrl'] as String?,
    );

Map<String, dynamic> _$$_UserTitleInfoToJson(_$_UserTitleInfo instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'title': instance.title,
      'status': instance.status,
      'nickname': instance.nickname,
      'avatarUrl': instance.avatarUrl,
      'remark': instance.remark,
      'certificateBackImage': instance.certificateBackImage,
      'certificateImage': instance.certificateImage,
      'certificateTime': instance.certificateTime,
      'mendMimiUrl': instance.mendMimiUrl,
      'mendAppUrl': instance.mendAppUrl,
    };

_$_TeacherRemark _$$_TeacherRemarkFromJson(Map<String, dynamic> json) =>
    _$_TeacherRemark(
      teacherRemarks: json['teacherRemarks'] as String?,
      signatureImg: json['signatureImg'] as String?,
      profileUrl: json['profileUrl'] as String?,
    );

Map<String, dynamic> _$$_TeacherRemarkToJson(_$_TeacherRemark instance) =>
    <String, dynamic>{
      'teacherRemarks': instance.teacherRemarks,
      'signatureImg': instance.signatureImg,
      'profileUrl': instance.profileUrl,
    };
