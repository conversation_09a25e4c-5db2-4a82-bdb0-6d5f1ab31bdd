// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recognition_list_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

RecognitionList _$RecognitionListFromJson(Map<String, dynamic> json) {
  return _RecognitionList.fromJson(json);
}

/// @nodoc
mixin _$RecognitionList {
  String? get title => throw _privateConstructorUsedError;
  set title(String? value) => throw _privateConstructorUsedError;
  String? get level => throw _privateConstructorUsedError;
  set level(String? value) => throw _privateConstructorUsedError;
  bool? get hasNext => throw _privateConstructorUsedError;
  set hasNext(bool? value) => throw _privateConstructorUsedError;
  List<UserInfoList>? get userInfoList => throw _privateConstructorUsedError;
  set userInfoList(List<UserInfoList>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecognitionListCopyWith<RecognitionList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecognitionListCopyWith<$Res> {
  factory $RecognitionListCopyWith(
          RecognitionList value, $Res Function(RecognitionList) then) =
      _$RecognitionListCopyWithImpl<$Res, RecognitionList>;
  @useResult
  $Res call(
      {String? title,
      String? level,
      bool? hasNext,
      List<UserInfoList>? userInfoList});
}

/// @nodoc
class _$RecognitionListCopyWithImpl<$Res, $Val extends RecognitionList>
    implements $RecognitionListCopyWith<$Res> {
  _$RecognitionListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? level = freezed,
    Object? hasNext = freezed,
    Object? userInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      userInfoList: freezed == userInfoList
          ? _value.userInfoList
          : userInfoList // ignore: cast_nullable_to_non_nullable
              as List<UserInfoList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RecognitionListCopyWith<$Res>
    implements $RecognitionListCopyWith<$Res> {
  factory _$$_RecognitionListCopyWith(
          _$_RecognitionList value, $Res Function(_$_RecognitionList) then) =
      __$$_RecognitionListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? level,
      bool? hasNext,
      List<UserInfoList>? userInfoList});
}

/// @nodoc
class __$$_RecognitionListCopyWithImpl<$Res>
    extends _$RecognitionListCopyWithImpl<$Res, _$_RecognitionList>
    implements _$$_RecognitionListCopyWith<$Res> {
  __$$_RecognitionListCopyWithImpl(
      _$_RecognitionList _value, $Res Function(_$_RecognitionList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? level = freezed,
    Object? hasNext = freezed,
    Object? userInfoList = freezed,
  }) {
    return _then(_$_RecognitionList(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as String?,
      hasNext: freezed == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool?,
      userInfoList: freezed == userInfoList
          ? _value.userInfoList
          : userInfoList // ignore: cast_nullable_to_non_nullable
              as List<UserInfoList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RecognitionList
    with DiagnosticableTreeMixin
    implements _RecognitionList {
  _$_RecognitionList({this.title, this.level, this.hasNext, this.userInfoList});

  factory _$_RecognitionList.fromJson(Map<String, dynamic> json) =>
      _$$_RecognitionListFromJson(json);

  @override
  String? title;
  @override
  String? level;
  @override
  bool? hasNext;
  @override
  List<UserInfoList>? userInfoList;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RecognitionList(title: $title, level: $level, hasNext: $hasNext, userInfoList: $userInfoList)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RecognitionList'))
      ..add(DiagnosticsProperty('title', title))
      ..add(DiagnosticsProperty('level', level))
      ..add(DiagnosticsProperty('hasNext', hasNext))
      ..add(DiagnosticsProperty('userInfoList', userInfoList));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RecognitionListCopyWith<_$_RecognitionList> get copyWith =>
      __$$_RecognitionListCopyWithImpl<_$_RecognitionList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RecognitionListToJson(
      this,
    );
  }
}

abstract class _RecognitionList implements RecognitionList {
  factory _RecognitionList(
      {String? title,
      String? level,
      bool? hasNext,
      List<UserInfoList>? userInfoList}) = _$_RecognitionList;

  factory _RecognitionList.fromJson(Map<String, dynamic> json) =
      _$_RecognitionList.fromJson;

  @override
  String? get title;
  set title(String? value);
  @override
  String? get level;
  set level(String? value);
  @override
  bool? get hasNext;
  set hasNext(bool? value);
  @override
  List<UserInfoList>? get userInfoList;
  set userInfoList(List<UserInfoList>? value);
  @override
  @JsonKey(ignore: true)
  _$$_RecognitionListCopyWith<_$_RecognitionList> get copyWith =>
      throw _privateConstructorUsedError;
}

UserInfoList _$UserInfoListFromJson(Map<String, dynamic> json) {
  return _UserInfoList.fromJson(json);
}

/// @nodoc
mixin _$UserInfoList {
  int? get userId => throw _privateConstructorUsedError;
  set userId(int? value) => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  set nickname(String? value) => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  set avatarUrl(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserInfoListCopyWith<UserInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoListCopyWith<$Res> {
  factory $UserInfoListCopyWith(
          UserInfoList value, $Res Function(UserInfoList) then) =
      _$UserInfoListCopyWithImpl<$Res, UserInfoList>;
  @useResult
  $Res call({int? userId, String? nickname, String? avatarUrl});
}

/// @nodoc
class _$UserInfoListCopyWithImpl<$Res, $Val extends UserInfoList>
    implements $UserInfoListCopyWith<$Res> {
  _$UserInfoListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserInfoListCopyWith<$Res>
    implements $UserInfoListCopyWith<$Res> {
  factory _$$_UserInfoListCopyWith(
          _$_UserInfoList value, $Res Function(_$_UserInfoList) then) =
      __$$_UserInfoListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? userId, String? nickname, String? avatarUrl});
}

/// @nodoc
class __$$_UserInfoListCopyWithImpl<$Res>
    extends _$UserInfoListCopyWithImpl<$Res, _$_UserInfoList>
    implements _$$_UserInfoListCopyWith<$Res> {
  __$$_UserInfoListCopyWithImpl(
      _$_UserInfoList _value, $Res Function(_$_UserInfoList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
  }) {
    return _then(_$_UserInfoList(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserInfoList with DiagnosticableTreeMixin implements _UserInfoList {
  _$_UserInfoList({this.userId, this.nickname, this.avatarUrl});

  factory _$_UserInfoList.fromJson(Map<String, dynamic> json) =>
      _$$_UserInfoListFromJson(json);

  @override
  int? userId;
  @override
  String? nickname;
  @override
  String? avatarUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserInfoList(userId: $userId, nickname: $nickname, avatarUrl: $avatarUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserInfoList'))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('nickname', nickname))
      ..add(DiagnosticsProperty('avatarUrl', avatarUrl));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserInfoListCopyWith<_$_UserInfoList> get copyWith =>
      __$$_UserInfoListCopyWithImpl<_$_UserInfoList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserInfoListToJson(
      this,
    );
  }
}

abstract class _UserInfoList implements UserInfoList {
  factory _UserInfoList({int? userId, String? nickname, String? avatarUrl}) =
      _$_UserInfoList;

  factory _UserInfoList.fromJson(Map<String, dynamic> json) =
      _$_UserInfoList.fromJson;

  @override
  int? get userId;
  set userId(int? value);
  @override
  String? get nickname;
  set nickname(String? value);
  @override
  String? get avatarUrl;
  set avatarUrl(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_UserInfoListCopyWith<_$_UserInfoList> get copyWith =>
      throw _privateConstructorUsedError;
}
