// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recognition_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

RecognitionData _$RecognitionDataFromJson(Map<String, dynamic> json) {
  return _RecognitionData.fromJson(json);
}

/// @nodoc
mixin _$RecognitionData {
  String? get citeName => throw _privateConstructorUsedError;
  UserTitleInfo? get userTitleInfo => throw _privateConstructorUsedError;
  String? get backgroundImage => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  String? get startTime => throw _privateConstructorUsedError;
  String? get endTime => throw _privateConstructorUsedError;
  String? get classUserTitleInfoList => throw _privateConstructorUsedError;
  TeacherRemark? get teacherRemark => throw _privateConstructorUsedError;
  String? get citeShareUrl => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  int? get firstGearTotal => throw _privateConstructorUsedError;
  int? get secondGearTotal => throw _privateConstructorUsedError;
  int? get firstGearId => throw _privateConstructorUsedError;
  int? get secondGearId => throw _privateConstructorUsedError;
  int? get classPraiseId => throw _privateConstructorUsedError;
  String? get firstGearTitle => throw _privateConstructorUsedError;
  String? get firstGearLevel => throw _privateConstructorUsedError;
  String? get secondGearTitle => throw _privateConstructorUsedError;
  String? get secondGearLevel => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecognitionDataCopyWith<RecognitionData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecognitionDataCopyWith<$Res> {
  factory $RecognitionDataCopyWith(
          RecognitionData value, $Res Function(RecognitionData) then) =
      _$RecognitionDataCopyWithImpl<$Res, RecognitionData>;
  @useResult
  $Res call(
      {String? citeName,
      UserTitleInfo? userTitleInfo,
      String? backgroundImage,
      String? backgroundColor,
      String? startTime,
      String? endTime,
      String? classUserTitleInfoList,
      TeacherRemark? teacherRemark,
      String? citeShareUrl,
      String? courseKey,
      int? firstGearTotal,
      int? secondGearTotal,
      int? firstGearId,
      int? secondGearId,
      int? classPraiseId,
      String? firstGearTitle,
      String? firstGearLevel,
      String? secondGearTitle,
      String? secondGearLevel});

  $UserTitleInfoCopyWith<$Res>? get userTitleInfo;
  $TeacherRemarkCopyWith<$Res>? get teacherRemark;
}

/// @nodoc
class _$RecognitionDataCopyWithImpl<$Res, $Val extends RecognitionData>
    implements $RecognitionDataCopyWith<$Res> {
  _$RecognitionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? citeName = freezed,
    Object? userTitleInfo = freezed,
    Object? backgroundImage = freezed,
    Object? backgroundColor = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? classUserTitleInfoList = freezed,
    Object? teacherRemark = freezed,
    Object? citeShareUrl = freezed,
    Object? courseKey = freezed,
    Object? firstGearTotal = freezed,
    Object? secondGearTotal = freezed,
    Object? firstGearId = freezed,
    Object? secondGearId = freezed,
    Object? classPraiseId = freezed,
    Object? firstGearTitle = freezed,
    Object? firstGearLevel = freezed,
    Object? secondGearTitle = freezed,
    Object? secondGearLevel = freezed,
  }) {
    return _then(_value.copyWith(
      citeName: freezed == citeName
          ? _value.citeName
          : citeName // ignore: cast_nullable_to_non_nullable
              as String?,
      userTitleInfo: freezed == userTitleInfo
          ? _value.userTitleInfo
          : userTitleInfo // ignore: cast_nullable_to_non_nullable
              as UserTitleInfo?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      classUserTitleInfoList: freezed == classUserTitleInfoList
          ? _value.classUserTitleInfoList
          : classUserTitleInfoList // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherRemark: freezed == teacherRemark
          ? _value.teacherRemark
          : teacherRemark // ignore: cast_nullable_to_non_nullable
              as TeacherRemark?,
      citeShareUrl: freezed == citeShareUrl
          ? _value.citeShareUrl
          : citeShareUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      firstGearTotal: freezed == firstGearTotal
          ? _value.firstGearTotal
          : firstGearTotal // ignore: cast_nullable_to_non_nullable
              as int?,
      secondGearTotal: freezed == secondGearTotal
          ? _value.secondGearTotal
          : secondGearTotal // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGearId: freezed == firstGearId
          ? _value.firstGearId
          : firstGearId // ignore: cast_nullable_to_non_nullable
              as int?,
      secondGearId: freezed == secondGearId
          ? _value.secondGearId
          : secondGearId // ignore: cast_nullable_to_non_nullable
              as int?,
      classPraiseId: freezed == classPraiseId
          ? _value.classPraiseId
          : classPraiseId // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGearTitle: freezed == firstGearTitle
          ? _value.firstGearTitle
          : firstGearTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      firstGearLevel: freezed == firstGearLevel
          ? _value.firstGearLevel
          : firstGearLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      secondGearTitle: freezed == secondGearTitle
          ? _value.secondGearTitle
          : secondGearTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      secondGearLevel: freezed == secondGearLevel
          ? _value.secondGearLevel
          : secondGearLevel // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserTitleInfoCopyWith<$Res>? get userTitleInfo {
    if (_value.userTitleInfo == null) {
      return null;
    }

    return $UserTitleInfoCopyWith<$Res>(_value.userTitleInfo!, (value) {
      return _then(_value.copyWith(userTitleInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TeacherRemarkCopyWith<$Res>? get teacherRemark {
    if (_value.teacherRemark == null) {
      return null;
    }

    return $TeacherRemarkCopyWith<$Res>(_value.teacherRemark!, (value) {
      return _then(_value.copyWith(teacherRemark: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_RecognitionDataCopyWith<$Res>
    implements $RecognitionDataCopyWith<$Res> {
  factory _$$_RecognitionDataCopyWith(
          _$_RecognitionData value, $Res Function(_$_RecognitionData) then) =
      __$$_RecognitionDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? citeName,
      UserTitleInfo? userTitleInfo,
      String? backgroundImage,
      String? backgroundColor,
      String? startTime,
      String? endTime,
      String? classUserTitleInfoList,
      TeacherRemark? teacherRemark,
      String? citeShareUrl,
      String? courseKey,
      int? firstGearTotal,
      int? secondGearTotal,
      int? firstGearId,
      int? secondGearId,
      int? classPraiseId,
      String? firstGearTitle,
      String? firstGearLevel,
      String? secondGearTitle,
      String? secondGearLevel});

  @override
  $UserTitleInfoCopyWith<$Res>? get userTitleInfo;
  @override
  $TeacherRemarkCopyWith<$Res>? get teacherRemark;
}

/// @nodoc
class __$$_RecognitionDataCopyWithImpl<$Res>
    extends _$RecognitionDataCopyWithImpl<$Res, _$_RecognitionData>
    implements _$$_RecognitionDataCopyWith<$Res> {
  __$$_RecognitionDataCopyWithImpl(
      _$_RecognitionData _value, $Res Function(_$_RecognitionData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? citeName = freezed,
    Object? userTitleInfo = freezed,
    Object? backgroundImage = freezed,
    Object? backgroundColor = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? classUserTitleInfoList = freezed,
    Object? teacherRemark = freezed,
    Object? citeShareUrl = freezed,
    Object? courseKey = freezed,
    Object? firstGearTotal = freezed,
    Object? secondGearTotal = freezed,
    Object? firstGearId = freezed,
    Object? secondGearId = freezed,
    Object? classPraiseId = freezed,
    Object? firstGearTitle = freezed,
    Object? firstGearLevel = freezed,
    Object? secondGearTitle = freezed,
    Object? secondGearLevel = freezed,
  }) {
    return _then(_$_RecognitionData(
      citeName: freezed == citeName
          ? _value.citeName
          : citeName // ignore: cast_nullable_to_non_nullable
              as String?,
      userTitleInfo: freezed == userTitleInfo
          ? _value.userTitleInfo
          : userTitleInfo // ignore: cast_nullable_to_non_nullable
              as UserTitleInfo?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      classUserTitleInfoList: freezed == classUserTitleInfoList
          ? _value.classUserTitleInfoList
          : classUserTitleInfoList // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherRemark: freezed == teacherRemark
          ? _value.teacherRemark
          : teacherRemark // ignore: cast_nullable_to_non_nullable
              as TeacherRemark?,
      citeShareUrl: freezed == citeShareUrl
          ? _value.citeShareUrl
          : citeShareUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      firstGearTotal: freezed == firstGearTotal
          ? _value.firstGearTotal
          : firstGearTotal // ignore: cast_nullable_to_non_nullable
              as int?,
      secondGearTotal: freezed == secondGearTotal
          ? _value.secondGearTotal
          : secondGearTotal // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGearId: freezed == firstGearId
          ? _value.firstGearId
          : firstGearId // ignore: cast_nullable_to_non_nullable
              as int?,
      secondGearId: freezed == secondGearId
          ? _value.secondGearId
          : secondGearId // ignore: cast_nullable_to_non_nullable
              as int?,
      classPraiseId: freezed == classPraiseId
          ? _value.classPraiseId
          : classPraiseId // ignore: cast_nullable_to_non_nullable
              as int?,
      firstGearTitle: freezed == firstGearTitle
          ? _value.firstGearTitle
          : firstGearTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      firstGearLevel: freezed == firstGearLevel
          ? _value.firstGearLevel
          : firstGearLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      secondGearTitle: freezed == secondGearTitle
          ? _value.secondGearTitle
          : secondGearTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      secondGearLevel: freezed == secondGearLevel
          ? _value.secondGearLevel
          : secondGearLevel // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RecognitionData
    with DiagnosticableTreeMixin
    implements _RecognitionData {
  const _$_RecognitionData(
      {this.citeName,
      this.userTitleInfo,
      this.backgroundImage,
      this.backgroundColor,
      this.startTime,
      this.endTime,
      this.classUserTitleInfoList,
      this.teacherRemark,
      this.citeShareUrl,
      this.courseKey,
      this.firstGearTotal,
      this.secondGearTotal,
      this.firstGearId,
      this.secondGearId,
      this.classPraiseId,
      this.firstGearTitle,
      this.firstGearLevel,
      this.secondGearTitle,
      this.secondGearLevel});

  factory _$_RecognitionData.fromJson(Map<String, dynamic> json) =>
      _$$_RecognitionDataFromJson(json);

  @override
  final String? citeName;
  @override
  final UserTitleInfo? userTitleInfo;
  @override
  final String? backgroundImage;
  @override
  final String? backgroundColor;
  @override
  final String? startTime;
  @override
  final String? endTime;
  @override
  final String? classUserTitleInfoList;
  @override
  final TeacherRemark? teacherRemark;
  @override
  final String? citeShareUrl;
  @override
  final String? courseKey;
  @override
  final int? firstGearTotal;
  @override
  final int? secondGearTotal;
  @override
  final int? firstGearId;
  @override
  final int? secondGearId;
  @override
  final int? classPraiseId;
  @override
  final String? firstGearTitle;
  @override
  final String? firstGearLevel;
  @override
  final String? secondGearTitle;
  @override
  final String? secondGearLevel;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RecognitionData(citeName: $citeName, userTitleInfo: $userTitleInfo, backgroundImage: $backgroundImage, backgroundColor: $backgroundColor, startTime: $startTime, endTime: $endTime, classUserTitleInfoList: $classUserTitleInfoList, teacherRemark: $teacherRemark, citeShareUrl: $citeShareUrl, courseKey: $courseKey, firstGearTotal: $firstGearTotal, secondGearTotal: $secondGearTotal, firstGearId: $firstGearId, secondGearId: $secondGearId, classPraiseId: $classPraiseId, firstGearTitle: $firstGearTitle, firstGearLevel: $firstGearLevel, secondGearTitle: $secondGearTitle, secondGearLevel: $secondGearLevel)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RecognitionData'))
      ..add(DiagnosticsProperty('citeName', citeName))
      ..add(DiagnosticsProperty('userTitleInfo', userTitleInfo))
      ..add(DiagnosticsProperty('backgroundImage', backgroundImage))
      ..add(DiagnosticsProperty('backgroundColor', backgroundColor))
      ..add(DiagnosticsProperty('startTime', startTime))
      ..add(DiagnosticsProperty('endTime', endTime))
      ..add(
          DiagnosticsProperty('classUserTitleInfoList', classUserTitleInfoList))
      ..add(DiagnosticsProperty('teacherRemark', teacherRemark))
      ..add(DiagnosticsProperty('citeShareUrl', citeShareUrl))
      ..add(DiagnosticsProperty('courseKey', courseKey))
      ..add(DiagnosticsProperty('firstGearTotal', firstGearTotal))
      ..add(DiagnosticsProperty('secondGearTotal', secondGearTotal))
      ..add(DiagnosticsProperty('firstGearId', firstGearId))
      ..add(DiagnosticsProperty('secondGearId', secondGearId))
      ..add(DiagnosticsProperty('classPraiseId', classPraiseId))
      ..add(DiagnosticsProperty('firstGearTitle', firstGearTitle))
      ..add(DiagnosticsProperty('firstGearLevel', firstGearLevel))
      ..add(DiagnosticsProperty('secondGearTitle', secondGearTitle))
      ..add(DiagnosticsProperty('secondGearLevel', secondGearLevel));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RecognitionData &&
            (identical(other.citeName, citeName) ||
                other.citeName == citeName) &&
            (identical(other.userTitleInfo, userTitleInfo) ||
                other.userTitleInfo == userTitleInfo) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.classUserTitleInfoList, classUserTitleInfoList) ||
                other.classUserTitleInfoList == classUserTitleInfoList) &&
            (identical(other.teacherRemark, teacherRemark) ||
                other.teacherRemark == teacherRemark) &&
            (identical(other.citeShareUrl, citeShareUrl) ||
                other.citeShareUrl == citeShareUrl) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.firstGearTotal, firstGearTotal) ||
                other.firstGearTotal == firstGearTotal) &&
            (identical(other.secondGearTotal, secondGearTotal) ||
                other.secondGearTotal == secondGearTotal) &&
            (identical(other.firstGearId, firstGearId) ||
                other.firstGearId == firstGearId) &&
            (identical(other.secondGearId, secondGearId) ||
                other.secondGearId == secondGearId) &&
            (identical(other.classPraiseId, classPraiseId) ||
                other.classPraiseId == classPraiseId) &&
            (identical(other.firstGearTitle, firstGearTitle) ||
                other.firstGearTitle == firstGearTitle) &&
            (identical(other.firstGearLevel, firstGearLevel) ||
                other.firstGearLevel == firstGearLevel) &&
            (identical(other.secondGearTitle, secondGearTitle) ||
                other.secondGearTitle == secondGearTitle) &&
            (identical(other.secondGearLevel, secondGearLevel) ||
                other.secondGearLevel == secondGearLevel));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        citeName,
        userTitleInfo,
        backgroundImage,
        backgroundColor,
        startTime,
        endTime,
        classUserTitleInfoList,
        teacherRemark,
        citeShareUrl,
        courseKey,
        firstGearTotal,
        secondGearTotal,
        firstGearId,
        secondGearId,
        classPraiseId,
        firstGearTitle,
        firstGearLevel,
        secondGearTitle,
        secondGearLevel
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RecognitionDataCopyWith<_$_RecognitionData> get copyWith =>
      __$$_RecognitionDataCopyWithImpl<_$_RecognitionData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RecognitionDataToJson(
      this,
    );
  }
}

abstract class _RecognitionData implements RecognitionData {
  const factory _RecognitionData(
      {final String? citeName,
      final UserTitleInfo? userTitleInfo,
      final String? backgroundImage,
      final String? backgroundColor,
      final String? startTime,
      final String? endTime,
      final String? classUserTitleInfoList,
      final TeacherRemark? teacherRemark,
      final String? citeShareUrl,
      final String? courseKey,
      final int? firstGearTotal,
      final int? secondGearTotal,
      final int? firstGearId,
      final int? secondGearId,
      final int? classPraiseId,
      final String? firstGearTitle,
      final String? firstGearLevel,
      final String? secondGearTitle,
      final String? secondGearLevel}) = _$_RecognitionData;

  factory _RecognitionData.fromJson(Map<String, dynamic> json) =
      _$_RecognitionData.fromJson;

  @override
  String? get citeName;
  @override
  UserTitleInfo? get userTitleInfo;
  @override
  String? get backgroundImage;
  @override
  String? get backgroundColor;
  @override
  String? get startTime;
  @override
  String? get endTime;
  @override
  String? get classUserTitleInfoList;
  @override
  TeacherRemark? get teacherRemark;
  @override
  String? get citeShareUrl;
  @override
  String? get courseKey;
  @override
  int? get firstGearTotal;
  @override
  int? get secondGearTotal;
  @override
  int? get firstGearId;
  @override
  int? get secondGearId;
  @override
  int? get classPraiseId;
  @override
  String? get firstGearTitle;
  @override
  String? get firstGearLevel;
  @override
  String? get secondGearTitle;
  @override
  String? get secondGearLevel;
  @override
  @JsonKey(ignore: true)
  _$$_RecognitionDataCopyWith<_$_RecognitionData> get copyWith =>
      throw _privateConstructorUsedError;
}

UserTitleInfo _$UserTitleInfoFromJson(Map<String, dynamic> json) {
  return _UserTitleInfo.fromJson(json);
}

/// @nodoc
mixin _$UserTitleInfo {
  int? get userId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get remark => throw _privateConstructorUsedError;
  String? get certificateBackImage => throw _privateConstructorUsedError;
  String? get certificateImage => throw _privateConstructorUsedError;
  String? get certificateTime => throw _privateConstructorUsedError;
  String? get mendMimiUrl => throw _privateConstructorUsedError;
  String? get mendAppUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserTitleInfoCopyWith<UserTitleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserTitleInfoCopyWith<$Res> {
  factory $UserTitleInfoCopyWith(
          UserTitleInfo value, $Res Function(UserTitleInfo) then) =
      _$UserTitleInfoCopyWithImpl<$Res, UserTitleInfo>;
  @useResult
  $Res call(
      {int? userId,
      String? title,
      int? status,
      String? nickname,
      String? avatarUrl,
      String? remark,
      String? certificateBackImage,
      String? certificateImage,
      String? certificateTime,
      String? mendMimiUrl,
      String? mendAppUrl});
}

/// @nodoc
class _$UserTitleInfoCopyWithImpl<$Res, $Val extends UserTitleInfo>
    implements $UserTitleInfoCopyWith<$Res> {
  _$UserTitleInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? remark = freezed,
    Object? certificateBackImage = freezed,
    Object? certificateImage = freezed,
    Object? certificateTime = freezed,
    Object? mendMimiUrl = freezed,
    Object? mendAppUrl = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateBackImage: freezed == certificateBackImage
          ? _value.certificateBackImage
          : certificateBackImage // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateImage: freezed == certificateImage
          ? _value.certificateImage
          : certificateImage // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateTime: freezed == certificateTime
          ? _value.certificateTime
          : certificateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      mendMimiUrl: freezed == mendMimiUrl
          ? _value.mendMimiUrl
          : mendMimiUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mendAppUrl: freezed == mendAppUrl
          ? _value.mendAppUrl
          : mendAppUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserTitleInfoCopyWith<$Res>
    implements $UserTitleInfoCopyWith<$Res> {
  factory _$$_UserTitleInfoCopyWith(
          _$_UserTitleInfo value, $Res Function(_$_UserTitleInfo) then) =
      __$$_UserTitleInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId,
      String? title,
      int? status,
      String? nickname,
      String? avatarUrl,
      String? remark,
      String? certificateBackImage,
      String? certificateImage,
      String? certificateTime,
      String? mendMimiUrl,
      String? mendAppUrl});
}

/// @nodoc
class __$$_UserTitleInfoCopyWithImpl<$Res>
    extends _$UserTitleInfoCopyWithImpl<$Res, _$_UserTitleInfo>
    implements _$$_UserTitleInfoCopyWith<$Res> {
  __$$_UserTitleInfoCopyWithImpl(
      _$_UserTitleInfo _value, $Res Function(_$_UserTitleInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? nickname = freezed,
    Object? avatarUrl = freezed,
    Object? remark = freezed,
    Object? certificateBackImage = freezed,
    Object? certificateImage = freezed,
    Object? certificateTime = freezed,
    Object? mendMimiUrl = freezed,
    Object? mendAppUrl = freezed,
  }) {
    return _then(_$_UserTitleInfo(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateBackImage: freezed == certificateBackImage
          ? _value.certificateBackImage
          : certificateBackImage // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateImage: freezed == certificateImage
          ? _value.certificateImage
          : certificateImage // ignore: cast_nullable_to_non_nullable
              as String?,
      certificateTime: freezed == certificateTime
          ? _value.certificateTime
          : certificateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      mendMimiUrl: freezed == mendMimiUrl
          ? _value.mendMimiUrl
          : mendMimiUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mendAppUrl: freezed == mendAppUrl
          ? _value.mendAppUrl
          : mendAppUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserTitleInfo with DiagnosticableTreeMixin implements _UserTitleInfo {
  const _$_UserTitleInfo(
      {this.userId,
      this.title,
      this.status,
      this.nickname,
      this.avatarUrl,
      this.remark,
      this.certificateBackImage,
      this.certificateImage,
      this.certificateTime,
      this.mendMimiUrl,
      this.mendAppUrl});

  factory _$_UserTitleInfo.fromJson(Map<String, dynamic> json) =>
      _$$_UserTitleInfoFromJson(json);

  @override
  final int? userId;
  @override
  final String? title;
  @override
  final int? status;
  @override
  final String? nickname;
  @override
  final String? avatarUrl;
  @override
  final String? remark;
  @override
  final String? certificateBackImage;
  @override
  final String? certificateImage;
  @override
  final String? certificateTime;
  @override
  final String? mendMimiUrl;
  @override
  final String? mendAppUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'UserTitleInfo(userId: $userId, title: $title, status: $status, nickname: $nickname, avatarUrl: $avatarUrl, remark: $remark, certificateBackImage: $certificateBackImage, certificateImage: $certificateImage, certificateTime: $certificateTime, mendMimiUrl: $mendMimiUrl, mendAppUrl: $mendAppUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'UserTitleInfo'))
      ..add(DiagnosticsProperty('userId', userId))
      ..add(DiagnosticsProperty('title', title))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('nickname', nickname))
      ..add(DiagnosticsProperty('avatarUrl', avatarUrl))
      ..add(DiagnosticsProperty('remark', remark))
      ..add(DiagnosticsProperty('certificateBackImage', certificateBackImage))
      ..add(DiagnosticsProperty('certificateImage', certificateImage))
      ..add(DiagnosticsProperty('certificateTime', certificateTime))
      ..add(DiagnosticsProperty('mendMimiUrl', mendMimiUrl))
      ..add(DiagnosticsProperty('mendAppUrl', mendAppUrl));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserTitleInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.certificateBackImage, certificateBackImage) ||
                other.certificateBackImage == certificateBackImage) &&
            (identical(other.certificateImage, certificateImage) ||
                other.certificateImage == certificateImage) &&
            (identical(other.certificateTime, certificateTime) ||
                other.certificateTime == certificateTime) &&
            (identical(other.mendMimiUrl, mendMimiUrl) ||
                other.mendMimiUrl == mendMimiUrl) &&
            (identical(other.mendAppUrl, mendAppUrl) ||
                other.mendAppUrl == mendAppUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      title,
      status,
      nickname,
      avatarUrl,
      remark,
      certificateBackImage,
      certificateImage,
      certificateTime,
      mendMimiUrl,
      mendAppUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserTitleInfoCopyWith<_$_UserTitleInfo> get copyWith =>
      __$$_UserTitleInfoCopyWithImpl<_$_UserTitleInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserTitleInfoToJson(
      this,
    );
  }
}

abstract class _UserTitleInfo implements UserTitleInfo {
  const factory _UserTitleInfo(
      {final int? userId,
      final String? title,
      final int? status,
      final String? nickname,
      final String? avatarUrl,
      final String? remark,
      final String? certificateBackImage,
      final String? certificateImage,
      final String? certificateTime,
      final String? mendMimiUrl,
      final String? mendAppUrl}) = _$_UserTitleInfo;

  factory _UserTitleInfo.fromJson(Map<String, dynamic> json) =
      _$_UserTitleInfo.fromJson;

  @override
  int? get userId;
  @override
  String? get title;
  @override
  int? get status;
  @override
  String? get nickname;
  @override
  String? get avatarUrl;
  @override
  String? get remark;
  @override
  String? get certificateBackImage;
  @override
  String? get certificateImage;
  @override
  String? get certificateTime;
  @override
  String? get mendMimiUrl;
  @override
  String? get mendAppUrl;
  @override
  @JsonKey(ignore: true)
  _$$_UserTitleInfoCopyWith<_$_UserTitleInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

TeacherRemark _$TeacherRemarkFromJson(Map<String, dynamic> json) {
  return _TeacherRemark.fromJson(json);
}

/// @nodoc
mixin _$TeacherRemark {
  String? get teacherRemarks => throw _privateConstructorUsedError;
  String? get signatureImg => throw _privateConstructorUsedError;
  String? get profileUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherRemarkCopyWith<TeacherRemark> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherRemarkCopyWith<$Res> {
  factory $TeacherRemarkCopyWith(
          TeacherRemark value, $Res Function(TeacherRemark) then) =
      _$TeacherRemarkCopyWithImpl<$Res, TeacherRemark>;
  @useResult
  $Res call({String? teacherRemarks, String? signatureImg, String? profileUrl});
}

/// @nodoc
class _$TeacherRemarkCopyWithImpl<$Res, $Val extends TeacherRemark>
    implements $TeacherRemarkCopyWith<$Res> {
  _$TeacherRemarkCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherRemarks = freezed,
    Object? signatureImg = freezed,
    Object? profileUrl = freezed,
  }) {
    return _then(_value.copyWith(
      teacherRemarks: freezed == teacherRemarks
          ? _value.teacherRemarks
          : teacherRemarks // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImg: freezed == signatureImg
          ? _value.signatureImg
          : signatureImg // ignore: cast_nullable_to_non_nullable
              as String?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeacherRemarkCopyWith<$Res>
    implements $TeacherRemarkCopyWith<$Res> {
  factory _$$_TeacherRemarkCopyWith(
          _$_TeacherRemark value, $Res Function(_$_TeacherRemark) then) =
      __$$_TeacherRemarkCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? teacherRemarks, String? signatureImg, String? profileUrl});
}

/// @nodoc
class __$$_TeacherRemarkCopyWithImpl<$Res>
    extends _$TeacherRemarkCopyWithImpl<$Res, _$_TeacherRemark>
    implements _$$_TeacherRemarkCopyWith<$Res> {
  __$$_TeacherRemarkCopyWithImpl(
      _$_TeacherRemark _value, $Res Function(_$_TeacherRemark) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teacherRemarks = freezed,
    Object? signatureImg = freezed,
    Object? profileUrl = freezed,
  }) {
    return _then(_$_TeacherRemark(
      teacherRemarks: freezed == teacherRemarks
          ? _value.teacherRemarks
          : teacherRemarks // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImg: freezed == signatureImg
          ? _value.signatureImg
          : signatureImg // ignore: cast_nullable_to_non_nullable
              as String?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherRemark with DiagnosticableTreeMixin implements _TeacherRemark {
  const _$_TeacherRemark(
      {this.teacherRemarks, this.signatureImg, this.profileUrl});

  factory _$_TeacherRemark.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherRemarkFromJson(json);

  @override
  final String? teacherRemarks;
  @override
  final String? signatureImg;
  @override
  final String? profileUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'TeacherRemark(teacherRemarks: $teacherRemarks, signatureImg: $signatureImg, profileUrl: $profileUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'TeacherRemark'))
      ..add(DiagnosticsProperty('teacherRemarks', teacherRemarks))
      ..add(DiagnosticsProperty('signatureImg', signatureImg))
      ..add(DiagnosticsProperty('profileUrl', profileUrl));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherRemark &&
            (identical(other.teacherRemarks, teacherRemarks) ||
                other.teacherRemarks == teacherRemarks) &&
            (identical(other.signatureImg, signatureImg) ||
                other.signatureImg == signatureImg) &&
            (identical(other.profileUrl, profileUrl) ||
                other.profileUrl == profileUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, teacherRemarks, signatureImg, profileUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherRemarkCopyWith<_$_TeacherRemark> get copyWith =>
      __$$_TeacherRemarkCopyWithImpl<_$_TeacherRemark>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherRemarkToJson(
      this,
    );
  }
}

abstract class _TeacherRemark implements TeacherRemark {
  const factory _TeacherRemark(
      {final String? teacherRemarks,
      final String? signatureImg,
      final String? profileUrl}) = _$_TeacherRemark;

  factory _TeacherRemark.fromJson(Map<String, dynamic> json) =
      _$_TeacherRemark.fromJson;

  @override
  String? get teacherRemarks;
  @override
  String? get signatureImg;
  @override
  String? get profileUrl;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherRemarkCopyWith<_$_TeacherRemark> get copyWith =>
      throw _privateConstructorUsedError;
}
