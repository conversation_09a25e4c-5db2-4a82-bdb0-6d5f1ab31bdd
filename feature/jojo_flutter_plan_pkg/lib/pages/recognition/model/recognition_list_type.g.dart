// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recognition_list_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_RecognitionList _$$_RecognitionListFromJson(Map<String, dynamic> json) =>
    _$_RecognitionList(
      title: json['title'] as String?,
      level: json['level'] as String?,
      hasNext: json['hasNext'] as bool?,
      userInfoList: (json['userInfoList'] as List<dynamic>?)
          ?.map((e) => UserInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_RecognitionListToJson(_$_RecognitionList instance) =>
    <String, dynamic>{
      'title': instance.title,
      'level': instance.level,
      'hasNext': instance.hasNext,
      'userInfoList': instance.userInfoList,
    };

_$_UserInfoList _$$_UserInfoListFromJson(Map<String, dynamic> json) =>
    _$_UserInfoList(
      userId: json['userId'] as int?,
      nickname: json['nickname'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
    );

Map<String, dynamic> _$$_UserInfoListToJson(_$_UserInfoList instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'nickname': instance.nickname,
      'avatarUrl': instance.avatarUrl,
    };
