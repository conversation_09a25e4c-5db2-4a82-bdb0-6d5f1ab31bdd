import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/view.dart';

import '../../generated/l10n.dart';
import 'controller.dart';

class PlanLessonChangePage extends StatefulHookWidget {
  final int loadingScene;
  final int? classId;
  final int? courseId;

  const PlanLessonChangePage(
      {super.key,
      required this.loadingScene,
      required this.classId,
      required this.courseId});

  @override
  State<StatefulWidget> createState() => PlanCourseChangeState();
}

class PlanCourseChangeState extends State<PlanLessonChangePage> {
  ChangeController? _ctrl;

  @override
  void initState() {
    super.initState();
    _ctrl =
        ChangeController(classId: widget.classId, courseId: widget.courseId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) {
        return _ctrl ??= ChangeController(
            classId: widget.classId, courseId: widget.courseId);
      },
      child:
          BlocBuilder<ChangeController, ChangeState>(builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: JoJoAppBar(
            onBack: (backHandler) {
              if (_ctrl?.backToStudyPage == true) {
                CourseUtils.goHome();
              } else {
                backHandler?.call();
              }
            },
            title: state.skuData?.title ?? S.of(context).lessonAll,
          ),
          body: JoJoPageLoadingV25(
            hideProgress: true,
            status: state.pageStatus,
            exception: state.exception,
            retry: () {
              _ctrl?.retry();
            },
            child: PlanCourseChangeView(
              currentIndex: state.currentIndex,
              skuData: state.skuData,
            ),
          ),
        );
      }),
    );
  }
}
