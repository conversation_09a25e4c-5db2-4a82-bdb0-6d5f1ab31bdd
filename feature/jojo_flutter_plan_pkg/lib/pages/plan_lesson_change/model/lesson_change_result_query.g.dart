// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_change_result_query.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LessonChangeResultQuery _$$_LessonChangeResultQueryFromJson(
        Map<String, dynamic> json) =>
    _$_LessonChangeResultQuery(
      simpleCourseCardList: (json['simpleCourseCardList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : SimpleCourseCardList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LessonChangeResultQueryToJson(
        _$_LessonChangeResultQuery instance) =>
    <String, dynamic>{
      'simpleCourseCardList': instance.simpleCourseCardList,
    };

_$_SimpleCourseCardList _$$_SimpleCourseCardListFromJson(
        Map<String, dynamic> json) =>
    _$_SimpleCourseCardList(
      activateStatus: json['activateStatus'] as int?,
      unactivatedReason: json['unactivatedReason'] as String?,
      unactivatedReasonType: json['unactivatedReasonType'] as int?,
      classId: json['classId'] as int?,
      classKey: json['classKey'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
    );

Map<String, dynamic> _$$_SimpleCourseCardListToJson(
        _$_SimpleCourseCardList instance) =>
    <String, dynamic>{
      'activateStatus': instance.activateStatus,
      'unactivatedReason': instance.unactivatedReason,
      'unactivatedReasonType': instance.unactivatedReasonType,
      'classId': instance.classId,
      'classKey': instance.classKey,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
    };
