// To parse this JSON data, do
//
//     final lessonChangeResult = lessonChangeResultFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'lesson_change_result.freezed.dart';
part 'lesson_change_result.g.dart';

LessonChangeResult lessonChangeResultFromJson(String str) => LessonChangeResult.fromJson(json.decode(str));

String lessonChangeResultToJson(LessonChangeResult data) => json.encode(data.toJson());

@freezed
class LessonChangeResult with _$LessonChangeResult {
  const factory LessonChangeResult({
    bool? success,
    String? orderNo,
    int? courseId,
    String? contactRoute,
  }) = _LessonChangeResult;

  factory LessonChangeResult.fromJson(Map<String, dynamic> json) => _$LessonChangeResultFromJson(json);
}
