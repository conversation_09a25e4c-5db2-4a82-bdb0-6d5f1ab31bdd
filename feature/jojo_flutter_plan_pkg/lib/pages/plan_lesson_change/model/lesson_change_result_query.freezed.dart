// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_change_result_query.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonChangeResultQuery _$LessonChangeResultQueryFromJson(
    Map<String, dynamic> json) {
  return _LessonChangeResultQuery.fromJson(json);
}

/// @nodoc
mixin _$LessonChangeResultQuery {
  List<SimpleCourseCardList?>? get simpleCourseCardList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonChangeResultQueryCopyWith<LessonChangeResultQuery> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonChangeResultQueryCopyWith<$Res> {
  factory $LessonChangeResultQueryCopyWith(LessonChangeResultQuery value,
          $Res Function(LessonChangeResultQuery) then) =
      _$LessonChangeResultQueryCopyWithImpl<$Res, LessonChangeResultQuery>;
  @useResult
  $Res call({List<SimpleCourseCardList?>? simpleCourseCardList});
}

/// @nodoc
class _$LessonChangeResultQueryCopyWithImpl<$Res,
        $Val extends LessonChangeResultQuery>
    implements $LessonChangeResultQueryCopyWith<$Res> {
  _$LessonChangeResultQueryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? simpleCourseCardList = freezed,
  }) {
    return _then(_value.copyWith(
      simpleCourseCardList: freezed == simpleCourseCardList
          ? _value.simpleCourseCardList
          : simpleCourseCardList // ignore: cast_nullable_to_non_nullable
              as List<SimpleCourseCardList?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonChangeResultQueryCopyWith<$Res>
    implements $LessonChangeResultQueryCopyWith<$Res> {
  factory _$$_LessonChangeResultQueryCopyWith(_$_LessonChangeResultQuery value,
          $Res Function(_$_LessonChangeResultQuery) then) =
      __$$_LessonChangeResultQueryCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SimpleCourseCardList?>? simpleCourseCardList});
}

/// @nodoc
class __$$_LessonChangeResultQueryCopyWithImpl<$Res>
    extends _$LessonChangeResultQueryCopyWithImpl<$Res,
        _$_LessonChangeResultQuery>
    implements _$$_LessonChangeResultQueryCopyWith<$Res> {
  __$$_LessonChangeResultQueryCopyWithImpl(_$_LessonChangeResultQuery _value,
      $Res Function(_$_LessonChangeResultQuery) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? simpleCourseCardList = freezed,
  }) {
    return _then(_$_LessonChangeResultQuery(
      simpleCourseCardList: freezed == simpleCourseCardList
          ? _value._simpleCourseCardList
          : simpleCourseCardList // ignore: cast_nullable_to_non_nullable
              as List<SimpleCourseCardList?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonChangeResultQuery implements _LessonChangeResultQuery {
  const _$_LessonChangeResultQuery(
      {final List<SimpleCourseCardList?>? simpleCourseCardList})
      : _simpleCourseCardList = simpleCourseCardList;

  factory _$_LessonChangeResultQuery.fromJson(Map<String, dynamic> json) =>
      _$$_LessonChangeResultQueryFromJson(json);

  final List<SimpleCourseCardList?>? _simpleCourseCardList;
  @override
  List<SimpleCourseCardList?>? get simpleCourseCardList {
    final value = _simpleCourseCardList;
    if (value == null) return null;
    if (_simpleCourseCardList is EqualUnmodifiableListView)
      return _simpleCourseCardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LessonChangeResultQuery(simpleCourseCardList: $simpleCourseCardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonChangeResultQuery &&
            const DeepCollectionEquality()
                .equals(other._simpleCourseCardList, _simpleCourseCardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_simpleCourseCardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonChangeResultQueryCopyWith<_$_LessonChangeResultQuery>
      get copyWith =>
          __$$_LessonChangeResultQueryCopyWithImpl<_$_LessonChangeResultQuery>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonChangeResultQueryToJson(
      this,
    );
  }
}

abstract class _LessonChangeResultQuery implements LessonChangeResultQuery {
  const factory _LessonChangeResultQuery(
          {final List<SimpleCourseCardList?>? simpleCourseCardList}) =
      _$_LessonChangeResultQuery;

  factory _LessonChangeResultQuery.fromJson(Map<String, dynamic> json) =
      _$_LessonChangeResultQuery.fromJson;

  @override
  List<SimpleCourseCardList?>? get simpleCourseCardList;
  @override
  @JsonKey(ignore: true)
  _$$_LessonChangeResultQueryCopyWith<_$_LessonChangeResultQuery>
      get copyWith => throw _privateConstructorUsedError;
}

SimpleCourseCardList _$SimpleCourseCardListFromJson(Map<String, dynamic> json) {
  return _SimpleCourseCardList.fromJson(json);
}

/// @nodoc
mixin _$SimpleCourseCardList {
  int? get activateStatus => throw _privateConstructorUsedError;
  String? get unactivatedReason => throw _privateConstructorUsedError;
  int? get unactivatedReasonType => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SimpleCourseCardListCopyWith<SimpleCourseCardList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SimpleCourseCardListCopyWith<$Res> {
  factory $SimpleCourseCardListCopyWith(SimpleCourseCardList value,
          $Res Function(SimpleCourseCardList) then) =
      _$SimpleCourseCardListCopyWithImpl<$Res, SimpleCourseCardList>;
  @useResult
  $Res call(
      {int? activateStatus,
      String? unactivatedReason,
      int? unactivatedReasonType,
      int? classId,
      String? classKey,
      int? courseId,
      String? courseKey});
}

/// @nodoc
class _$SimpleCourseCardListCopyWithImpl<$Res,
        $Val extends SimpleCourseCardList>
    implements $SimpleCourseCardListCopyWith<$Res> {
  _$SimpleCourseCardListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activateStatus = freezed,
    Object? unactivatedReason = freezed,
    Object? unactivatedReasonType = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_value.copyWith(
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReasonType: freezed == unactivatedReasonType
          ? _value.unactivatedReasonType
          : unactivatedReasonType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SimpleCourseCardListCopyWith<$Res>
    implements $SimpleCourseCardListCopyWith<$Res> {
  factory _$$_SimpleCourseCardListCopyWith(_$_SimpleCourseCardList value,
          $Res Function(_$_SimpleCourseCardList) then) =
      __$$_SimpleCourseCardListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? activateStatus,
      String? unactivatedReason,
      int? unactivatedReasonType,
      int? classId,
      String? classKey,
      int? courseId,
      String? courseKey});
}

/// @nodoc
class __$$_SimpleCourseCardListCopyWithImpl<$Res>
    extends _$SimpleCourseCardListCopyWithImpl<$Res, _$_SimpleCourseCardList>
    implements _$$_SimpleCourseCardListCopyWith<$Res> {
  __$$_SimpleCourseCardListCopyWithImpl(_$_SimpleCourseCardList _value,
      $Res Function(_$_SimpleCourseCardList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activateStatus = freezed,
    Object? unactivatedReason = freezed,
    Object? unactivatedReasonType = freezed,
    Object? classId = freezed,
    Object? classKey = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_$_SimpleCourseCardList(
      activateStatus: freezed == activateStatus
          ? _value.activateStatus
          : activateStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      unactivatedReason: freezed == unactivatedReason
          ? _value.unactivatedReason
          : unactivatedReason // ignore: cast_nullable_to_non_nullable
              as String?,
      unactivatedReasonType: freezed == unactivatedReasonType
          ? _value.unactivatedReasonType
          : unactivatedReasonType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SimpleCourseCardList implements _SimpleCourseCardList {
  const _$_SimpleCourseCardList(
      {this.activateStatus,
      this.unactivatedReason,
      this.unactivatedReasonType,
      this.classId,
      this.classKey,
      this.courseId,
      this.courseKey});

  factory _$_SimpleCourseCardList.fromJson(Map<String, dynamic> json) =>
      _$$_SimpleCourseCardListFromJson(json);

  @override
  final int? activateStatus;
  @override
  final String? unactivatedReason;
  @override
  final int? unactivatedReasonType;
  @override
  final int? classId;
  @override
  final String? classKey;
  @override
  final int? courseId;
  @override
  final String? courseKey;

  @override
  String toString() {
    return 'SimpleCourseCardList(activateStatus: $activateStatus, unactivatedReason: $unactivatedReason, unactivatedReasonType: $unactivatedReasonType, classId: $classId, classKey: $classKey, courseId: $courseId, courseKey: $courseKey)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SimpleCourseCardList &&
            (identical(other.activateStatus, activateStatus) ||
                other.activateStatus == activateStatus) &&
            (identical(other.unactivatedReason, unactivatedReason) ||
                other.unactivatedReason == unactivatedReason) &&
            (identical(other.unactivatedReasonType, unactivatedReasonType) ||
                other.unactivatedReasonType == unactivatedReasonType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      activateStatus,
      unactivatedReason,
      unactivatedReasonType,
      classId,
      classKey,
      courseId,
      courseKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SimpleCourseCardListCopyWith<_$_SimpleCourseCardList> get copyWith =>
      __$$_SimpleCourseCardListCopyWithImpl<_$_SimpleCourseCardList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SimpleCourseCardListToJson(
      this,
    );
  }
}

abstract class _SimpleCourseCardList implements SimpleCourseCardList {
  const factory _SimpleCourseCardList(
      {final int? activateStatus,
      final String? unactivatedReason,
      final int? unactivatedReasonType,
      final int? classId,
      final String? classKey,
      final int? courseId,
      final String? courseKey}) = _$_SimpleCourseCardList;

  factory _SimpleCourseCardList.fromJson(Map<String, dynamic> json) =
      _$_SimpleCourseCardList.fromJson;

  @override
  int? get activateStatus;
  @override
  String? get unactivatedReason;
  @override
  int? get unactivatedReasonType;
  @override
  int? get classId;
  @override
  String? get classKey;
  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  @JsonKey(ignore: true)
  _$$_SimpleCourseCardListCopyWith<_$_SimpleCourseCardList> get copyWith =>
      throw _privateConstructorUsedError;
}
