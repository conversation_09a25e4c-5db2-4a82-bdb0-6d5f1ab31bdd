// To parse this JSON data, do
//
//     final lessonChangeResultQuery = lessonChangeResultQueryFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'lesson_change_result_query.freezed.dart';
part 'lesson_change_result_query.g.dart';

LessonChangeResultQuery lessonChangeResultQueryFromJson(String str) => LessonChangeResultQuery.fromJson(json.decode(str));

String lessonChangeResultQueryToJson(LessonChangeResultQuery data) => json.encode(data.toJson());

@freezed
class LessonChangeResultQuery with _$LessonChangeResultQuery {
  const factory LessonChangeResultQuery({
    List<SimpleCourseCardList?>? simpleCourseCardList,
  }) = _LessonChangeResultQuery;

  factory LessonChangeResultQuery.fromJson(Map<String, dynamic> json) => _$LessonChangeResultQueryFromJson(json);
}

@freezed
class SimpleCourseCardList with _$SimpleCourseCardList {
  const factory SimpleCourseCardList({
    int? activateStatus,
    String? unactivatedReason,
    int? unactivatedReasonType,
    int? classId,
    String? classKey,
    int? courseId,
    String? courseKey
  }) = _SimpleCourseCardList;

  factory SimpleCourseCardList.fromJson(Map<String, dynamic> json) => _$SimpleCourseCardListFromJson(json);
}
