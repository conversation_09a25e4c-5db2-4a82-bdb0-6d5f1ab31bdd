// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_change_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonChangeResult _$LessonChangeResultFromJson(Map<String, dynamic> json) {
  return _LessonChangeResult.fromJson(json);
}

/// @nodoc
mixin _$LessonChangeResult {
  bool? get success => throw _privateConstructorUsedError;
  String? get orderNo => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get contactRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonChangeResultCopyWith<LessonChangeResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonChangeResultCopyWith<$Res> {
  factory $LessonChangeResultCopyWith(
          LessonChangeResult value, $Res Function(LessonChangeResult) then) =
      _$LessonChangeResultCopyWithImpl<$Res, LessonChangeResult>;
  @useResult
  $Res call(
      {bool? success, String? orderNo, int? courseId, String? contactRoute});
}

/// @nodoc
class _$LessonChangeResultCopyWithImpl<$Res, $Val extends LessonChangeResult>
    implements $LessonChangeResultCopyWith<$Res> {
  _$LessonChangeResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = freezed,
    Object? orderNo = freezed,
    Object? courseId = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_value.copyWith(
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonChangeResultCopyWith<$Res>
    implements $LessonChangeResultCopyWith<$Res> {
  factory _$$_LessonChangeResultCopyWith(_$_LessonChangeResult value,
          $Res Function(_$_LessonChangeResult) then) =
      __$$_LessonChangeResultCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? success, String? orderNo, int? courseId, String? contactRoute});
}

/// @nodoc
class __$$_LessonChangeResultCopyWithImpl<$Res>
    extends _$LessonChangeResultCopyWithImpl<$Res, _$_LessonChangeResult>
    implements _$$_LessonChangeResultCopyWith<$Res> {
  __$$_LessonChangeResultCopyWithImpl(
      _$_LessonChangeResult _value, $Res Function(_$_LessonChangeResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = freezed,
    Object? orderNo = freezed,
    Object? courseId = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_$_LessonChangeResult(
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonChangeResult implements _LessonChangeResult {
  const _$_LessonChangeResult(
      {this.success, this.orderNo, this.courseId, this.contactRoute});

  factory _$_LessonChangeResult.fromJson(Map<String, dynamic> json) =>
      _$$_LessonChangeResultFromJson(json);

  @override
  final bool? success;
  @override
  final String? orderNo;
  @override
  final int? courseId;
  @override
  final String? contactRoute;

  @override
  String toString() {
    return 'LessonChangeResult(success: $success, orderNo: $orderNo, courseId: $courseId, contactRoute: $contactRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonChangeResult &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.contactRoute, contactRoute) ||
                other.contactRoute == contactRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, orderNo, courseId, contactRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonChangeResultCopyWith<_$_LessonChangeResult> get copyWith =>
      __$$_LessonChangeResultCopyWithImpl<_$_LessonChangeResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonChangeResultToJson(
      this,
    );
  }
}

abstract class _LessonChangeResult implements LessonChangeResult {
  const factory _LessonChangeResult(
      {final bool? success,
      final String? orderNo,
      final int? courseId,
      final String? contactRoute}) = _$_LessonChangeResult;

  factory _LessonChangeResult.fromJson(Map<String, dynamic> json) =
      _$_LessonChangeResult.fromJson;

  @override
  bool? get success;
  @override
  String? get orderNo;
  @override
  int? get courseId;
  @override
  String? get contactRoute;
  @override
  @JsonKey(ignore: true)
  _$$_LessonChangeResultCopyWith<_$_LessonChangeResult> get copyWith =>
      throw _privateConstructorUsedError;
}
