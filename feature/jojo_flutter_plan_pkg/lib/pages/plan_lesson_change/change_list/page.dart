import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_center.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_helper.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/customization/landscape_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/utils/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/widget/lesson_change_list_widget.dart';

import '../../../generated/l10n.dart';
import '../../plan_home_lesson/utils/course_utils.dart';
import '../controller.dart';
import '../model/lesson_change_data.dart';
import 'controller.dart';

class PlanLessonListChangePage extends StatefulWidget {
  final BuildContext context;
  final LessonChangeData? skuData;
  final SkuList? skuItemBean;
  final double rate;

  const PlanLessonListChangePage(
      {super.key,
      required this.context,
      required this.skuData,
      required this.skuItemBean,
      required this.rate});

  @override
  State<StatefulWidget> createState() => PlanLessonListChangeState();
}

class PlanLessonListChangeState extends State<PlanLessonListChangePage>
    with AutomaticKeepAliveClientMixin {
  ChangeListController? _ctrl;

  @override
  void initState() {
    super.initState();
    _ctrl = ChangeListController(
        skuData: widget.skuData, skuItemBean: widget.skuItemBean);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create: (BuildContext context) {
        return _ctrl ??= ChangeListController(
            skuData: widget.skuData, skuItemBean: widget.skuItemBean);
      },
      child: BlocBuilder<ChangeListController, ChangeListState>(
          builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: VisibilityObserve(
            onShow: () {
              RunEnv.sensorsTrack('\$AppViewScreen', {
                '\$screen_name': '全部内容页面',
                'course_key': widget.skuItemBean?.courseKey,
                'custom_state':
                    (widget.skuItemBean?.change == true) ? '未更换' : '已更换'
              });
            },
            child: JoJoPageLoading(
              status: state.pageStatus,
              retry: () {
                _ctrl?.retry();
              },
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
                          child: LessonChangeListWidget(
                            context: widget.context,
                            skuItemBean: state.skuItemInfo,
                            list: state.lessonList,
                            rate: widget.rate,
                          ),
                        ),
                        Positioned(
                            bottom: -20, left: 0, right: 0, child: _buildLine())
                      ],
                    ),
                  ),
                  _buildButton()
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildLine() {
    if (LandscapeUtils.isLandscape()) {
      return const SizedBox();
    } else {
      return Container(
          width: double.infinity,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03), // rgba(0, 0, 0, 0.03)
                offset: const Offset(0, -10), // 0px -10px
                blurRadius: 20, // 20px
                spreadRadius: 0, // 0px
              ),
            ],
          ));
    }
  }

  Widget _buildButton() {
    Widget? buttonWidget;
    if (widget.skuItemBean?.own == true) {
      buttonWidget = _buildHasInterestsWidget();
    } else if (widget.skuItemBean?.own == false &&
        widget.skuItemBean?.change == true) {
      buttonWidget = _buildChangeInterestsWidget();
    } else if (widget.skuItemBean?.change == false) {
      buttonWidget = _buildChangedWidget();
    }
    return SizedBox(
      height: 83.rdp,
      child: buttonWidget,
    );
  }

  ///权益生效中
  Widget _buildHasInterestsWidget() {
    return SizedBox(
      height: 83.rdp * widget.rate,
      child: Align(
        alignment: Alignment.center,
        child: Container(
          decoration: BoxDecoration(
            color: widget.context.appColors.jColorGreen1,
            borderRadius: BorderRadius.all(
              Radius.circular(32.rdp * widget.rate),
            ),
          ),
          width: 280.rdp * widget.rate,
          height: 43.rdp * widget.rate,
          child: Center(
            child: Text(
              S.of(widget.context).lessonInteresting,
              style: TextStyle(
                  fontSize: 18.rdp * widget.rate,
                  color: widget.context.appColors.jColorGreen5,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ),
    );
  }

  ///更换权益--黄色按钮
  Widget _buildChangeInterestsWidget() {
    return VisibilityObserve(
      onShow: () {
        _sensorView("更换权益按钮_曝光");
      },
      onClick: () {
        _sensorClick("更换权益按钮_点击");
        RunEnv.sensorsTrack(
          'ElementView',
          {
            'c_element_name': '同步练家长验证弹窗_曝光',
          },
        );
        showParentVerifyDialog(() {
          RunEnv.sensorsTrack(
            'ElementView',
            {
              'c_element_name': '更换权益确认弹窗_曝光',
              'course_key': widget.skuItemBean?.courseKey,
              'custom_state': (widget.skuItemBean?.change == true) ? '已更换' : '未更换'
            },
          );
          ModuleDialogHelper.getInstance()
              .getModuleCenterDialog(ModuleCenterDialogData(
              backDismissible: false,
              clickMaskDismiss: false,
              onLeftButtonTap: () {
                RunEnv.sensorsTrack(
                    '\$AppClick', {'\$element_name': '更换权益确认弹窗_取消'});
                ModuleDialogHelper.getInstance().dismissWithTag();
              },
              onRightButtonTap: () {
                //调用替换课
                ModuleDialogHelper.getInstance().dismissWithTag();
                _changeLesson();
                RunEnv.sensorsTrack(
                    '\$AppClick', {'\$element_name': '更换权益确认弹窗_更换'});
              },
              title: S.of(widget.context).lessonInterested,
              content: S.of(widget.context).lessonChangeDes(
                  widget.skuData?.ownSkuName ?? "",
                  widget.skuItemBean?.skuName ?? ""),
              leftButtonText: S.of(widget.context).lessonChangeCancel,
              rightButtonText: S.of(widget.context).lessonChange))
              .show();
        });
      },
      child: SizedBox(
        height: 83.rdp * widget.rate,
        child: Align(
          alignment: Alignment.center,
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: widget.context.appColors.jColorYellow4,
              borderRadius: BorderRadius.all(
                Radius.circular(32.rdp * widget.rate),
              ),
            ),
            width: 280.rdp * widget.rate,
            height: 43.rdp * widget.rate,
            child: Text(
              S.of(widget.context).lessonInterested,
              style: TextStyle(
                  fontSize: 18.rdp * widget.rate,
                  color: widget.context.appColors.jColorYellow6,
                  fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }

  ///已经更换过权益
  Widget _buildChangedWidget() {
    return SizedBox(
      height: 83.rdp * widget.rate,
      child: Center(
        child: Text(
          S.of(widget.context).lessonUnChange,
          style: TextStyle(
              color: widget.context.appColors.jColorGray6,
              fontWeight: FontWeight.w400,
              fontSize: 16.rdp * widget.rate),
        ),
      ),
    );
  }

  _changeLesson() {
    try {
      context.read<ChangeController>().lessonChange(
          widget.skuData?.orderNo,
          widget.skuData?.ownSkuId,
          widget.skuItemBean?.skuId,
          widget.skuItemBean?.courseId,
          widget.skuData?.gift,
          widget.skuData?.belongSkuId,
          S.of(widget.context).lessonLoadingText, (status, msg, contactRoute) {
        if (status == ChangeStatus.changeSuccess) {
          JoJoToast.showSuccess(S.of(widget.context).lessonChangeSuccess);
        }
        if (status == ChangeStatus.changeFail) {
          //更换失败
          ModuleDialogHelper.getInstance().dismissWithTag();
          _showChangeFailDialog(contactRoute);
          return;
        }
        if (status == ChangeStatus.lessonActionFail) {
          //激活失败
          _setChangeErr();
          ModuleDialogHelper.getInstance().dismissWithTag();
          _showLessonActionFailDialog(contactRoute);
          return;
        }
        if (status == ChangeStatus.lessonActionTimeout) {
          //激活超时
          _setChangeErr();
          ModuleDialogHelper.getInstance().dismissWithTag();
          _showLessonActionTimeoutDialog();
          return;
        }
        if (status == ChangeStatus.changeSuccess) {
          JoJoToast.showSuccess(S.of(widget.context).lessonChangeSuccess);
          RunEnv.sensorsTrack(
            'ElementView',
            {'c_element_name': '更换成功_曝光'},
          );
          return;
        }
        l.i("2025交换课列表", "_changeLesson-交换结果出现异常");
        JoJoToast.showError(S.of(widget.context).lessonChangeActionFail);
      });
    } catch (e, stack) {
      JoJoToast.showError(S.of(widget.context).lessonChangeActionFail);
      l.e("2025交换课列表", "交换接口异常$e\n stack=$stack");
    }
  }

  _setChangeErr() {
    try {
      context.read<ChangeController>().backToStudyPage = true;
    } catch (e) {}
  }

  ///更换失败，联系客服弹窗
  _showChangeFailDialog(String? contactRouter) {
    RunEnv.sensorsTrack(
      'ElementView',
      {'c_element_name': '更换失败弹窗_曝光'},
    );
    ModuleDialogHelper.getInstance()
        .getModuleCenterDialog(ModuleCenterDialogData(
            backDismissible: false,
            clickMaskDismiss: false,
            onRightButtonTap: () {
              ModuleDialogHelper.getInstance().dismissWithTag();
              _changeLesson();
            },
            title: S.of(widget.context).lessonChangeFail,
            richContent: RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(widget.context).style,
                children: <TextSpan>[
                  TextSpan(
                      text: S.of(widget.context).lessonChangeFailRetry,
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontWeight: FontWeight.w400,
                          fontSize: 16.rdp,
                          color: widget.context.appColors.jColorGray6)),
                  TextSpan(
                    text: S.of(widget.context).lessonChangeFailContact,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16.rdp,
                      color: widget.context.appColors.jColorYellow4,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        if (contactRouter != null) {
                          ModuleDialogHelper.getInstance().dismissWithTag();
                          RunEnv.jumpLink(contactRouter);
                        } else {
                          l.e("2025交换课列表", "联系客服路由为null");
                        }
                      },
                  ),
                  TextSpan(
                      text: S.of(widget.context).lessonChangeFailDeal,
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontWeight: FontWeight.w400,
                          fontSize: 16.rdp,
                          color: widget.context.appColors.jColorGray6)),
                ],
              ),
            ),
            leftButtonText: S.of(widget.context).lessonChangeCancel,
            rightButtonText: S.of(widget.context).lessonChangeRetry))
        .show();
  }

  ///激活失败，联系客服
  _showLessonActionFailDialog(String? contactRouter) {
    RunEnv.sensorsTrack(
      'ElementView',
      {'c_element_name': '激活失败弹窗_曝光'},
    );
    ModuleDialogHelper.getInstance()
        .getModuleCenterDialog(ModuleCenterDialogData(
            backDismissible: false,
            clickMaskDismiss: false,
            onRightButtonTap: () {
              if (contactRouter != null) {
                ModuleDialogHelper.getInstance().dismissWithTag();
                RunEnv.jumpLink(contactRouter);
              } else {
                l.i("2025交换课列表", "联系客服路由为null");
              }
            },
            title: S.of(widget.context).lessonChangeActionFail,
            content: S.of(widget.context).lessonChangeActionFailDetail,
            rightButtonText: S.of(widget.context).lessonChangeFailContact))
        .show();
  }

  ///激活超时，回到上课页
  _showLessonActionTimeoutDialog() {
    RunEnv.sensorsTrack(
      'ElementView',
      {'c_element_name': '更换超时弹窗_曝光'},
    );
    ModuleDialogHelper.getInstance()
        .getModuleCenterDialog(ModuleCenterDialogData(
            backDismissible: false,
            clickMaskDismiss: false,
            onRightButtonTap: () {
              ModuleDialogHelper.getInstance().dismissWithTag();
              CourseUtils.goHome();
            },
            title: S.of(widget.context).lessonChangeActionTimeout,
            content: S.of(widget.context).lessonChangeActionTimeoutDetail,
            rightButtonText:
                S.of(widget.context).lessonChangeActionBackPlanHome))
        .show();
  }

  @override
  bool get wantKeepAlive => true;

  _sensorView(String elementName) {
    RunEnv.sensorsTrack(
      'ElementView',
      {
        'c_element_name': elementName,
        'course_key': widget.skuItemBean?.courseKey,
        'custom_state': (widget.skuItemBean?.change == true) ? '未更换' : '已更换'
      },
    );
  }

  _sensorClick(String elementName) {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': elementName,
      'course_key': widget.skuItemBean?.courseKey,
      'custom_state': (widget.skuItemBean?.change == true) ? '未更换' : '已更换'
    });
  }
}
