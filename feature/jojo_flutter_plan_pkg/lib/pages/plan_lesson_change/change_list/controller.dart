import 'dart:ffi';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/ext/list_ext.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/model/lesson_change_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/model/lesson_change_theme.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/model/lesson_change_unit.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/model/lesson_change_data.dart';

import '../../../service/home_lesson_change_api.dart';
import 'model/lesson_change_base.dart';
import 'model/lesson_change_list_data.dart';

class ChangeListController extends Cubit<ChangeListState> {
  HomeLessonChangeApi? _api;
  LessonChangeData? skuData;
  SkuList? skuItemBean;

  ChangeListController(
      {HomeLessonChangeApi? api, this.skuData, this.skuItemBean})
      : super(ChangeListState(
            pageStatus: PageStatus.loading,
            skuData: skuData,
            skuItemInfo: skuItemBean,
            lessonList: [])) {
    _api = api ?? homeLessonChangeApiService;
    _initData();
  }

  retry() {
    var newState = state.copyWith()..pageStatus = PageStatus.loading;
    emit(newState);
    _initData();
  }

  _initData() {
    onRefresh();
  }

  Future onRefresh() async {
    try {
      LessonChangeListData? skuList = await _api?.getLessonList(
          int.tryParse(skuItemBean?.courseKey ?? ""), true);
      var newState = state.copyWith();
      if (skuList == null || skuList.list.isNullOrEmpty()) {
        newState
          ..pageStatus = PageStatus.empty
          ..skuData = skuData
          ..skuItemInfo = skuItemBean;
        emit(newState);
        return;
      }
      List<LessonChangeBase> itemList = _formatList(skuList);
      newState
        ..pageStatus = PageStatus.success
        ..listElement = skuList.list
        ..lessonList = itemList
        ..skuData = skuData;
      emit(newState);
    } catch (e, stack) {
      l.e("2025交换课列表", "sku列表: $e\n stack=$stack");
      final newState = state.copyWith()..pageStatus = PageStatus.error;
      emit(newState);
    }
  }

  ///处理主题和单元
  List<LessonChangeBase> _formatList(LessonChangeListData skuList) {
    List<LessonChangeBase> itemList = [];
    skuList.list?.forEach((segment) {
      //每一个list就是一个主题
      itemList.add(LessonChangeTheme(
          segmentId: segment?.segmentId, segmentName: segment?.segmentName));
      int? unitWeekId;
      segment?.lessonInfos?.forEach((element) {
        if (element != null && element.weekName.isNotNullOrEmpty()) {
          //这里面存在不同单元
          if ((element.weekId ?? 0) > 0 && unitWeekId != element.weekId) {
            //说明有存在不同单元
            itemList.add(LessonChangeUnit(
                weekId: element.weekId, weekName: element.weekName));
            unitWeekId = element.weekId;
          }
        }
        //添加课时数据
        itemList.add(LessonChangeInfo(
            segmentId: segment.segmentId,
            segmentName: segment.segmentName,
            weekId: element?.weekId,
            weekName: element?.weekName,
            icon: element?.icon,
            lessonName: element?.lessonName));
      });
    });
    return itemList;
  }
}
