import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';

import '../../model/lesson_change_data.dart';
import '../model/lesson_change_base.dart';
import '../model/lesson_change_info.dart';
import '../model/lesson_change_theme.dart';
import '../model/lesson_change_unit.dart';

class LessonChangeListWidget extends StatelessWidget {
  final BuildContext context;
  final SkuList? skuItemBean;
  final List<LessonChangeBase> list;
  final double rate;

  const LessonChangeListWidget(
      {super.key,
      required this.context,
      required this.skuItemBean,
      required this.list,
      required this.rate});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const BouncingScrollPhysics(),
          shrinkWrap: true,
          itemCount: list.length,
          itemBuilder: (context, index) {
            var item = list[index];
            if (item is LessonChangeTheme) {
              return _buildThemeWidget(item);
            }
            if (item is LessonChangeUnit) {
              return _buildThemeUnit(item);
            }
            if (item is LessonChangeInfo) {
              return _buildLessonWidget(item);
            }
            return const SizedBox();
          }),
    );
  }

  Widget _buildThemeWidget(LessonChangeTheme theme) {
    return SizedBox(
        height: 46.rdp * rate,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            theme.segmentName ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: context.appColors.jColorGray6,
                fontSize: 16.rdp * rate,
                fontWeight: FontWeight.w500),
          ),
        ));
  }

  Widget _buildThemeUnit(LessonChangeUnit unit) {
    return SizedBox(
        height: 36.rdp * rate,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            unit.weekName ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: context.appColors.jColorGray4,
                fontSize: 16.rdp * rate,
                fontWeight: FontWeight.w400),
          ),
        ));
  }

  Widget _buildLessonWidget(LessonChangeInfo lesson) {
    return SizedBox(
      height: 84.rdp * rate,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildIconImageWidget(lesson.icon),
            SizedBox(
              width: 8.rdp * rate,
            ),
            Expanded(
              child: Text(lesson.lessonName ?? "",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: context.appColors.jColorGray6,
                    fontSize: 16.rdp * rate,
                  )),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildIconImageWidget(String? iconUrl) {
    return ImageNetworkCached(
      borderRadius: 6.rdp * rate,
      imageUrl: iconUrl ?? "",
      width: 40.rdp * rate,
      height: 40.rdp * rate,
      fit: BoxFit.cover,
    );
  }
}
