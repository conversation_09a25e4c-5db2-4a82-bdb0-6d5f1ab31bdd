import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/change_list/model/lesson_change_base.dart';

class LessonChangeInfo extends LessonChangeBase {
  int? segmentId;
  String? segmentName;
  int? weekId;
  String? weekName;
  String? icon;
  String? lessonName;

  LessonChangeInfo(
      {this.segmentId,
      this.segmentName,
      this.weekId,
      this.weekName,
      this.icon,
      this.lessonName});
}
