// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_change_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LessonChangeListData _$$_LessonChangeListDataFromJson(
        Map<String, dynamic> json) =>
    _$_LessonChangeListData(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ListElement.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_LessonChangeListDataToJson(
        _$_LessonChangeListData instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

_$_ListElement _$$_ListElementFromJson(Map<String, dynamic> json) =>
    _$_ListElement(
      segmentStartTime: json['segmentStartTime'] as String?,
      segmentName: json['segmentName'] as String?,
      segmentId: json['segmentId'] as int?,
      lessonInfos: (json['lessonInfos'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : LessonInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ListElementToJson(_$_ListElement instance) =>
    <String, dynamic>{
      'segmentStartTime': instance.segmentStartTime,
      'segmentName': instance.segmentName,
      'segmentId': instance.segmentId,
      'lessonInfos': instance.lessonInfos,
    };

_$_LessonInfo _$$_LessonInfoFromJson(Map<String, dynamic> json) =>
    _$_LessonInfo(
      lessonName: json['lessonName'] as String?,
      lessonId: json['lessonId'] as int?,
      icon: json['icon'] as String?,
      lessonOrder: json['lessonOrder'] as int?,
      studyStatus: json['studyStatus'] as int?,
      studyTips: json['studyTips'] as String?,
      studyTipsVoice: json['studyTipsVoice'] as String?,
      lockVoice: json['lockVoice'] as String?,
      lessonGrade: json['lessonGrade'] as int?,
      lessonGradeResourceUrl: json['lessonGradeResourceUrl'] as String?,
      route: json['route'] as String?,
      finish: json['finish'] as bool?,
      unlock: json['unlock'] as bool?,
      makeup: json['makeup'] as bool?,
      today: json['today'] as bool?,
      weekId: json['weekId'] as int?,
      weekName: json['weekName'] as String?,
    );

Map<String, dynamic> _$$_LessonInfoToJson(_$_LessonInfo instance) =>
    <String, dynamic>{
      'lessonName': instance.lessonName,
      'lessonId': instance.lessonId,
      'icon': instance.icon,
      'lessonOrder': instance.lessonOrder,
      'studyStatus': instance.studyStatus,
      'studyTips': instance.studyTips,
      'studyTipsVoice': instance.studyTipsVoice,
      'lockVoice': instance.lockVoice,
      'lessonGrade': instance.lessonGrade,
      'lessonGradeResourceUrl': instance.lessonGradeResourceUrl,
      'route': instance.route,
      'finish': instance.finish,
      'unlock': instance.unlock,
      'makeup': instance.makeup,
      'today': instance.today,
      'weekId': instance.weekId,
      'weekName': instance.weekName,
    };
