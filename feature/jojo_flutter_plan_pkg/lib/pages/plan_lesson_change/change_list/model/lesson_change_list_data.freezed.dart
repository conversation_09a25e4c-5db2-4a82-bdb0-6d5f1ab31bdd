// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_change_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonChangeListData _$LessonChangeListDataFromJson(Map<String, dynamic> json) {
  return _LessonChangeListData.fromJson(json);
}

/// @nodoc
mixin _$LessonChangeListData {
  List<ListElement?>? get list => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonChangeListDataCopyWith<LessonChangeListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonChangeListDataCopyWith<$Res> {
  factory $LessonChangeListDataCopyWith(LessonChangeListData value,
          $Res Function(LessonChangeListData) then) =
      _$LessonChangeListDataCopyWithImpl<$Res, LessonChangeListData>;
  @useResult
  $Res call({List<ListElement?>? list});
}

/// @nodoc
class _$LessonChangeListDataCopyWithImpl<$Res,
        $Val extends LessonChangeListData>
    implements $LessonChangeListDataCopyWith<$Res> {
  _$LessonChangeListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
  }) {
    return _then(_value.copyWith(
      list: freezed == list
          ? _value.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<ListElement?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonChangeListDataCopyWith<$Res>
    implements $LessonChangeListDataCopyWith<$Res> {
  factory _$$_LessonChangeListDataCopyWith(_$_LessonChangeListData value,
          $Res Function(_$_LessonChangeListData) then) =
      __$$_LessonChangeListDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ListElement?>? list});
}

/// @nodoc
class __$$_LessonChangeListDataCopyWithImpl<$Res>
    extends _$LessonChangeListDataCopyWithImpl<$Res, _$_LessonChangeListData>
    implements _$$_LessonChangeListDataCopyWith<$Res> {
  __$$_LessonChangeListDataCopyWithImpl(_$_LessonChangeListData _value,
      $Res Function(_$_LessonChangeListData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
  }) {
    return _then(_$_LessonChangeListData(
      list: freezed == list
          ? _value._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<ListElement?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonChangeListData implements _LessonChangeListData {
  const _$_LessonChangeListData({final List<ListElement?>? list})
      : _list = list;

  factory _$_LessonChangeListData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonChangeListDataFromJson(json);

  final List<ListElement?>? _list;
  @override
  List<ListElement?>? get list {
    final value = _list;
    if (value == null) return null;
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'LessonChangeListData(list: $list)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonChangeListData &&
            const DeepCollectionEquality().equals(other._list, _list));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_list));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonChangeListDataCopyWith<_$_LessonChangeListData> get copyWith =>
      __$$_LessonChangeListDataCopyWithImpl<_$_LessonChangeListData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonChangeListDataToJson(
      this,
    );
  }
}

abstract class _LessonChangeListData implements LessonChangeListData {
  const factory _LessonChangeListData({final List<ListElement?>? list}) =
      _$_LessonChangeListData;

  factory _LessonChangeListData.fromJson(Map<String, dynamic> json) =
      _$_LessonChangeListData.fromJson;

  @override
  List<ListElement?>? get list;
  @override
  @JsonKey(ignore: true)
  _$$_LessonChangeListDataCopyWith<_$_LessonChangeListData> get copyWith =>
      throw _privateConstructorUsedError;
}

ListElement _$ListElementFromJson(Map<String, dynamic> json) {
  return _ListElement.fromJson(json);
}

/// @nodoc
mixin _$ListElement {
  String? get segmentStartTime => throw _privateConstructorUsedError;
  String? get segmentName => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  List<LessonInfo?>? get lessonInfos => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ListElementCopyWith<ListElement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListElementCopyWith<$Res> {
  factory $ListElementCopyWith(
          ListElement value, $Res Function(ListElement) then) =
      _$ListElementCopyWithImpl<$Res, ListElement>;
  @useResult
  $Res call(
      {String? segmentStartTime,
      String? segmentName,
      int? segmentId,
      List<LessonInfo?>? lessonInfos});
}

/// @nodoc
class _$ListElementCopyWithImpl<$Res, $Val extends ListElement>
    implements $ListElementCopyWith<$Res> {
  _$ListElementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentStartTime = freezed,
    Object? segmentName = freezed,
    Object? segmentId = freezed,
    Object? lessonInfos = freezed,
  }) {
    return _then(_value.copyWith(
      segmentStartTime: freezed == segmentStartTime
          ? _value.segmentStartTime
          : segmentStartTime // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfos: freezed == lessonInfos
          ? _value.lessonInfos
          : lessonInfos // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ListElementCopyWith<$Res>
    implements $ListElementCopyWith<$Res> {
  factory _$$_ListElementCopyWith(
          _$_ListElement value, $Res Function(_$_ListElement) then) =
      __$$_ListElementCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? segmentStartTime,
      String? segmentName,
      int? segmentId,
      List<LessonInfo?>? lessonInfos});
}

/// @nodoc
class __$$_ListElementCopyWithImpl<$Res>
    extends _$ListElementCopyWithImpl<$Res, _$_ListElement>
    implements _$$_ListElementCopyWith<$Res> {
  __$$_ListElementCopyWithImpl(
      _$_ListElement _value, $Res Function(_$_ListElement) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? segmentStartTime = freezed,
    Object? segmentName = freezed,
    Object? segmentId = freezed,
    Object? lessonInfos = freezed,
  }) {
    return _then(_$_ListElement(
      segmentStartTime: freezed == segmentStartTime
          ? _value.segmentStartTime
          : segmentStartTime // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonInfos: freezed == lessonInfos
          ? _value._lessonInfos
          : lessonInfos // ignore: cast_nullable_to_non_nullable
              as List<LessonInfo?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ListElement implements _ListElement {
  const _$_ListElement(
      {this.segmentStartTime,
      this.segmentName,
      this.segmentId,
      final List<LessonInfo?>? lessonInfos})
      : _lessonInfos = lessonInfos;

  factory _$_ListElement.fromJson(Map<String, dynamic> json) =>
      _$$_ListElementFromJson(json);

  @override
  final String? segmentStartTime;
  @override
  final String? segmentName;
  @override
  final int? segmentId;
  final List<LessonInfo?>? _lessonInfos;
  @override
  List<LessonInfo?>? get lessonInfos {
    final value = _lessonInfos;
    if (value == null) return null;
    if (_lessonInfos is EqualUnmodifiableListView) return _lessonInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ListElement(segmentStartTime: $segmentStartTime, segmentName: $segmentName, segmentId: $segmentId, lessonInfos: $lessonInfos)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ListElement &&
            (identical(other.segmentStartTime, segmentStartTime) ||
                other.segmentStartTime == segmentStartTime) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            const DeepCollectionEquality()
                .equals(other._lessonInfos, _lessonInfos));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, segmentStartTime, segmentName,
      segmentId, const DeepCollectionEquality().hash(_lessonInfos));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ListElementCopyWith<_$_ListElement> get copyWith =>
      __$$_ListElementCopyWithImpl<_$_ListElement>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ListElementToJson(
      this,
    );
  }
}

abstract class _ListElement implements ListElement {
  const factory _ListElement(
      {final String? segmentStartTime,
      final String? segmentName,
      final int? segmentId,
      final List<LessonInfo?>? lessonInfos}) = _$_ListElement;

  factory _ListElement.fromJson(Map<String, dynamic> json) =
      _$_ListElement.fromJson;

  @override
  String? get segmentStartTime;
  @override
  String? get segmentName;
  @override
  int? get segmentId;
  @override
  List<LessonInfo?>? get lessonInfos;
  @override
  @JsonKey(ignore: true)
  _$$_ListElementCopyWith<_$_ListElement> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfo _$LessonInfoFromJson(Map<String, dynamic> json) {
  return _LessonInfo.fromJson(json);
}

/// @nodoc
mixin _$LessonInfo {
  String? get lessonName => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  int? get lessonOrder => throw _privateConstructorUsedError;
  int? get studyStatus => throw _privateConstructorUsedError;
  String? get studyTips => throw _privateConstructorUsedError;
  String? get studyTipsVoice => throw _privateConstructorUsedError;
  String? get lockVoice => throw _privateConstructorUsedError;
  int? get lessonGrade => throw _privateConstructorUsedError;
  String? get lessonGradeResourceUrl => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  bool? get finish => throw _privateConstructorUsedError;
  bool? get unlock => throw _privateConstructorUsedError;
  bool? get makeup => throw _privateConstructorUsedError;
  bool? get today => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  String? get weekName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoCopyWith<LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoCopyWith<$Res> {
  factory $LessonInfoCopyWith(
          LessonInfo value, $Res Function(LessonInfo) then) =
      _$LessonInfoCopyWithImpl<$Res, LessonInfo>;
  @useResult
  $Res call(
      {String? lessonName,
      int? lessonId,
      String? icon,
      int? lessonOrder,
      int? studyStatus,
      String? studyTips,
      String? studyTipsVoice,
      String? lockVoice,
      int? lessonGrade,
      String? lessonGradeResourceUrl,
      String? route,
      bool? finish,
      bool? unlock,
      bool? makeup,
      bool? today,
      int? weekId,
      String? weekName});
}

/// @nodoc
class _$LessonInfoCopyWithImpl<$Res, $Val extends LessonInfo>
    implements $LessonInfoCopyWith<$Res> {
  _$LessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonName = freezed,
    Object? lessonId = freezed,
    Object? icon = freezed,
    Object? lessonOrder = freezed,
    Object? studyStatus = freezed,
    Object? studyTips = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockVoice = freezed,
    Object? lessonGrade = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? route = freezed,
    Object? finish = freezed,
    Object? unlock = freezed,
    Object? makeup = freezed,
    Object? today = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
  }) {
    return _then(_value.copyWith(
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyTips: freezed == studyTips
          ? _value.studyTips
          : studyTips // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      finish: freezed == finish
          ? _value.finish
          : finish // ignore: cast_nullable_to_non_nullable
              as bool?,
      unlock: freezed == unlock
          ? _value.unlock
          : unlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeup: freezed == makeup
          ? _value.makeup
          : makeup // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonInfoCopyWith<$Res>
    implements $LessonInfoCopyWith<$Res> {
  factory _$$_LessonInfoCopyWith(
          _$_LessonInfo value, $Res Function(_$_LessonInfo) then) =
      __$$_LessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lessonName,
      int? lessonId,
      String? icon,
      int? lessonOrder,
      int? studyStatus,
      String? studyTips,
      String? studyTipsVoice,
      String? lockVoice,
      int? lessonGrade,
      String? lessonGradeResourceUrl,
      String? route,
      bool? finish,
      bool? unlock,
      bool? makeup,
      bool? today,
      int? weekId,
      String? weekName});
}

/// @nodoc
class __$$_LessonInfoCopyWithImpl<$Res>
    extends _$LessonInfoCopyWithImpl<$Res, _$_LessonInfo>
    implements _$$_LessonInfoCopyWith<$Res> {
  __$$_LessonInfoCopyWithImpl(
      _$_LessonInfo _value, $Res Function(_$_LessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonName = freezed,
    Object? lessonId = freezed,
    Object? icon = freezed,
    Object? lessonOrder = freezed,
    Object? studyStatus = freezed,
    Object? studyTips = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockVoice = freezed,
    Object? lessonGrade = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? route = freezed,
    Object? finish = freezed,
    Object? unlock = freezed,
    Object? makeup = freezed,
    Object? today = freezed,
    Object? weekId = freezed,
    Object? weekName = freezed,
  }) {
    return _then(_$_LessonInfo(
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyTips: freezed == studyTips
          ? _value.studyTips
          : studyTips // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      finish: freezed == finish
          ? _value.finish
          : finish // ignore: cast_nullable_to_non_nullable
              as bool?,
      unlock: freezed == unlock
          ? _value.unlock
          : unlock // ignore: cast_nullable_to_non_nullable
              as bool?,
      makeup: freezed == makeup
          ? _value.makeup
          : makeup // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfo implements _LessonInfo {
  const _$_LessonInfo(
      {this.lessonName,
      this.lessonId,
      this.icon,
      this.lessonOrder,
      this.studyStatus,
      this.studyTips,
      this.studyTipsVoice,
      this.lockVoice,
      this.lessonGrade,
      this.lessonGradeResourceUrl,
      this.route,
      this.finish,
      this.unlock,
      this.makeup,
      this.today,
      this.weekId,
      this.weekName});

  factory _$_LessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoFromJson(json);

  @override
  final String? lessonName;
  @override
  final int? lessonId;
  @override
  final String? icon;
  @override
  final int? lessonOrder;
  @override
  final int? studyStatus;
  @override
  final String? studyTips;
  @override
  final String? studyTipsVoice;
  @override
  final String? lockVoice;
  @override
  final int? lessonGrade;
  @override
  final String? lessonGradeResourceUrl;
  @override
  final String? route;
  @override
  final bool? finish;
  @override
  final bool? unlock;
  @override
  final bool? makeup;
  @override
  final bool? today;
  @override
  final int? weekId;
  @override
  final String? weekName;

  @override
  String toString() {
    return 'LessonInfo(lessonName: $lessonName, lessonId: $lessonId, icon: $icon, lessonOrder: $lessonOrder, studyStatus: $studyStatus, studyTips: $studyTips, studyTipsVoice: $studyTipsVoice, lockVoice: $lockVoice, lessonGrade: $lessonGrade, lessonGradeResourceUrl: $lessonGradeResourceUrl, route: $route, finish: $finish, unlock: $unlock, makeup: $makeup, today: $today, weekId: $weekId, weekName: $weekName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonInfo &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.studyStatus, studyStatus) ||
                other.studyStatus == studyStatus) &&
            (identical(other.studyTips, studyTips) ||
                other.studyTips == studyTips) &&
            (identical(other.studyTipsVoice, studyTipsVoice) ||
                other.studyTipsVoice == studyTipsVoice) &&
            (identical(other.lockVoice, lockVoice) ||
                other.lockVoice == lockVoice) &&
            (identical(other.lessonGrade, lessonGrade) ||
                other.lessonGrade == lessonGrade) &&
            (identical(other.lessonGradeResourceUrl, lessonGradeResourceUrl) ||
                other.lessonGradeResourceUrl == lessonGradeResourceUrl) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.finish, finish) || other.finish == finish) &&
            (identical(other.unlock, unlock) || other.unlock == unlock) &&
            (identical(other.makeup, makeup) || other.makeup == makeup) &&
            (identical(other.today, today) || other.today == today) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.weekName, weekName) ||
                other.weekName == weekName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      lessonName,
      lessonId,
      icon,
      lessonOrder,
      studyStatus,
      studyTips,
      studyTipsVoice,
      lockVoice,
      lessonGrade,
      lessonGradeResourceUrl,
      route,
      finish,
      unlock,
      makeup,
      today,
      weekId,
      weekName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      __$$_LessonInfoCopyWithImpl<_$_LessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoToJson(
      this,
    );
  }
}

abstract class _LessonInfo implements LessonInfo {
  const factory _LessonInfo(
      {final String? lessonName,
      final int? lessonId,
      final String? icon,
      final int? lessonOrder,
      final int? studyStatus,
      final String? studyTips,
      final String? studyTipsVoice,
      final String? lockVoice,
      final int? lessonGrade,
      final String? lessonGradeResourceUrl,
      final String? route,
      final bool? finish,
      final bool? unlock,
      final bool? makeup,
      final bool? today,
      final int? weekId,
      final String? weekName}) = _$_LessonInfo;

  factory _LessonInfo.fromJson(Map<String, dynamic> json) =
      _$_LessonInfo.fromJson;

  @override
  String? get lessonName;
  @override
  int? get lessonId;
  @override
  String? get icon;
  @override
  int? get lessonOrder;
  @override
  int? get studyStatus;
  @override
  String? get studyTips;
  @override
  String? get studyTipsVoice;
  @override
  String? get lockVoice;
  @override
  int? get lessonGrade;
  @override
  String? get lessonGradeResourceUrl;
  @override
  String? get route;
  @override
  bool? get finish;
  @override
  bool? get unlock;
  @override
  bool? get makeup;
  @override
  bool? get today;
  @override
  int? get weekId;
  @override
  String? get weekName;
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoCopyWith<_$_LessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
