// To parse this JSON data, do
//
//     final lessonChangeListData = lessonChangeListDataFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'lesson_change_list_data.freezed.dart';
part 'lesson_change_list_data.g.dart';

LessonChangeListData lessonChangeListDataFromJson(String str) => LessonChangeListData.fromJson(json.decode(str));

String lessonChangeListDataToJson(LessonChangeListData data) => json.encode(data.toJson());

@freezed
class LessonChangeListData with _$LessonChangeListData {
  const factory LessonChangeListData({
    List<ListElement?>? list,
  }) = _LessonChangeListData;

  factory LessonChangeListData.fromJson(Map<String, dynamic> json) => _$LessonChangeListDataFromJson(json);
}

@freezed
class ListElement with _$ListElement {
  const factory ListElement({
    String? segmentStartTime,
    String? segmentName,
    int? segmentId,
    List<LessonInfo?>? lessonInfos,
  }) = _ListElement;

  factory ListElement.fromJson(Map<String, dynamic> json) => _$ListElementFromJson(json);
}

@freezed
class LessonInfo with _$LessonInfo {
  const factory LessonInfo({
    String? lessonName,
    int? lessonId,
    String? icon,
    int? lessonOrder,
    int? studyStatus,
    String? studyTips,
    String? studyTipsVoice,
    String? lockVoice,
    int? lessonGrade,
    String? lessonGradeResourceUrl,
    String? route,
    bool? finish,
    bool? unlock,
    bool? makeup,
    bool? today,
    int? weekId,
    String? weekName
  }) = _LessonInfo;

  factory LessonInfo.fromJson(Map<String, dynamic> json) => _$LessonInfoFromJson(json);
}
