import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import '../model/lesson_change_data.dart';
import 'model/lesson_change_base.dart';
import 'model/lesson_change_list_data.dart';

class ChangeListState {
  PageStatus pageStatus;
  LessonChangeData? skuData;
  SkuList? skuItemInfo;
  List<ListElement?>? listElement;
  List<LessonChangeBase> lessonList;

  ChangeListState(
      {required this.pageStatus,
      this.skuData,
      this.skuItemInfo,
      this.listElement,
      required this.lessonList});

  ChangeListState copyWith() {
    return ChangeListState(
        pageStatus: pageStatus,
        skuData: skuData,
        skuItemInfo: skuItemInfo,
        listElement: listElement,
        lessonList: lessonList);
  }
}
