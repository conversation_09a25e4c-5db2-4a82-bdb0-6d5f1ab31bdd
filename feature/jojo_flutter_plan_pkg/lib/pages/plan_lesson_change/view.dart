import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/widget/lesson_change_tab_widget.dart';

import 'model/lesson_change_data.dart';

class PlanCourseChangeView extends StatefulWidget {
  final int currentIndex;
  final LessonChangeData? skuData;

  const PlanCourseChangeView(
      {super.key, required this.currentIndex, required this.skuData});

  @override
  State<StatefulWidget> createState() => PlanCourseChangeState();
}

class PlanCourseChangeState extends State<PlanCourseChangeView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: 2.rdp,
            color: context.appColors.jColorGray1,
          ),
          AdaptiveOrientationLayout(portrait: (context) {
            return LessonChangeTabWidget(
              rate: 1.0,
              currentIndex: widget.currentIndex,
              skuData: widget.skuData,
            );
          }, landscape: (context) {
            return Align(
              alignment: Alignment.center,
              child: SizedBox(
                  width: 780.rdp,
                  child: LessonChangeTabWidget(
                    rate: 1.25,
                    currentIndex: widget.currentIndex,
                    skuData: widget.skuData,
                  )),
            );
          })
        ],
      ),
    );
  }
}
