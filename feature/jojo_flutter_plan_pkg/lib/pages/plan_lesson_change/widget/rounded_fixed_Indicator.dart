import 'package:flutter/material.dart';

class RoundedFixedIndicator extends Decoration {
  final Color color;
  final double width;
  final double height;
  final double radius;

  const RoundedFixedIndicator({
    required this.color,
    this.width = 20,
    this.height = 4,
    this.radius = 4,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _RoundedFixedIndicatorPainter(this, onChanged);
  }
}

class _RoundedFixedIndicatorPainter extends BoxPainter {
  final RoundedFixedIndicator decoration;

  _RoundedFixedIndicatorPainter(this.decoration, VoidCallback? onChanged)
      : super(onChanged);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = Paint();
    paint.color = decoration.color;
    paint.style = PaintingStyle.fill;

    // Tab的宽度和高度
    final double tabWidth = configuration.size!.width;
    final double tabHeight = configuration.size!.height;

    // 计算指示器的起始位置，使其水平居中
    final double dx = offset.dx + (tabWidth - decoration.width) / 2;
    final double dy = offset.dy + tabHeight - decoration.height;

    final Rect rect =
        Rect.fromLTWH(dx, dy, decoration.width, decoration.height);
    final RRect rRect =
        RRect.fromRectAndRadius(rect, Radius.circular(decoration.radius));

    canvas.drawRRect(rRect, paint);
  }
}
