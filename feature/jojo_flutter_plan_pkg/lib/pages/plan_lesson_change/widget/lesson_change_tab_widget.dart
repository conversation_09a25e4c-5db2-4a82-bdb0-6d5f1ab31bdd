import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/widget/rounded_fixed_Indicator.dart';

import '../change_list/page.dart';
import '../model/lesson_change_data.dart';

class LessonChangeTabWidget extends StatefulWidget {
  final double rate;
  final int currentIndex;
  final LessonChangeData? skuData;

  const LessonChangeTabWidget(
      {super.key,
      required this.rate,
      required this.currentIndex,
      required this.skuData});

  @override
  State<LessonChangeTabWidget> createState() => _LessonChangeTabWidgetState();
}

class _LessonChangeTabWidgetState extends State<LessonChangeTabWidget> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: DefaultTabController(
          length: widget.skuData?.skuList?.length ?? 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 48.rdp * widget.rate,
                padding: EdgeInsets.only(
                    top: 12.rdp * widget.rate, bottom: 8.rdp * widget.rate),
                child: TabBar(
                  isScrollable: true,
                  padding: EdgeInsets.zero,
                  labelColor: HexColor('#4a4a4a'),
                  indicator: RoundedFixedIndicator(
                    color: context.appColors.jColorYellow4,
                    width: 40.rdp * widget.rate,
                    radius: 2.rdp * widget.rate,
                    height: 4.rdp * widget.rate,
                  ),
                  labelPadding:
                      EdgeInsets.symmetric(horizontal: 16.rdp * widget.rate),
                  indicatorSize: TabBarIndicatorSize.label,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.rdp * widget.rate,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 16.rdp * widget.rate,
                  ),
                  tabs: widget.skuData?.skuList
                          ?.map((e) => _buildTabWidget(e))
                          .toList() ??
                      [],
                ),
              ),
              Expanded(
                child: TabBarView(
                  physics: const LowSensitivityScrollPhysics(),
                  children: widget.skuData?.skuList
                          ?.map((e) => PlanLessonListChangePage(
                                context: context,
                                skuData: widget.skuData,
                                skuItemBean: e,
                                rate: widget.rate,
                              ))
                          .toList() ??
                      [],
                ),
              )
            ],
          )),
    );
  }

  Widget _buildTabWidget(SkuList? itemSku) {
    return Tab(
      height: 24.rdp * widget.rate,
      child: Text(
        itemSku?.skuName ?? "",
        style: TextStyle(fontSize: 16.rdp * widget.rate),
      ),
    );
  }
}

class LowSensitivityScrollPhysics extends ScrollPhysics {
  const LowSensitivityScrollPhysics({ super.parent });

  @override
  ScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return LowSensitivityScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double? get dragStartDistanceMotionThreshold => 100.0; // 调整滑动阈值
}
