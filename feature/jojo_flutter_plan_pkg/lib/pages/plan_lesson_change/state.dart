import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import 'model/lesson_change_data.dart';

class ChangeState {
  PageStatus pageStatus;
  Exception? exception;
  int currentIndex; //默认定位
  LessonChangeData? skuData;

  ChangeState(
      {required this.pageStatus,
      required this.currentIndex, this.skuData});

  ChangeState copyWith() {
    return ChangeState(
        pageStatus: pageStatus, currentIndex: currentIndex, skuData: skuData);
  }
}
