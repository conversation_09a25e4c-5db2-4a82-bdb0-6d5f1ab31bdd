import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/ext/list_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/state.dart';

import '../../service/home_lesson_change_api.dart';
import '../plan_harmonyos/widget/loadingCircle.dart';
import 'model/lesson_change_data.dart';
import 'model/lesson_change_result.dart';
import 'model/lesson_change_result_query.dart';

const CHANGE_RESLUT = "changeLesson";

class ChangeController extends Cubit<ChangeState> {
  HomeLessonChangeApi? _api;
  int? classId;
  int? courseId;
  bool backToStudyPage = false;

  ChangeController({HomeLessonChangeApi? api, this.classId, this.courseId})
      : super(ChangeState(pageStatus: PageStatus.loading, currentIndex: 0)) {
    _api = api ?? homeLessonChangeApiService;
    // _api = homeLessonChangeApiServiceMock;
    _initData();
  }

  retry() {
    var newState = state.copyWith()..pageStatus = PageStatus.loading;
    emit(newState);
    _initData();
  }

  _initData() {
    onRefresh();
  }

  Future onRefresh() async {
    try {
      LessonChangeData? skuData = await _api?.getUserSkus(courseId, classId);
      var newState = state.copyWith()
        ..pageStatus = PageStatus.success
        ..skuData = skuData;
      if (skuData?.skuList.isNullOrEmpty() == true) {
        newState.pageStatus = PageStatus.empty;
      }
      emit(newState);
    } catch (e, stack) {
      l.e("2025交换课列表", "sku列表: $e\n stack=$stack");
      var exception = Exception("$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      emit(newState);
    }
  }

  Future lessonChange(
      String? orderNo,
      int? ownSkuId,
      int? newSkuId,
      int? newCourseId,
      bool? gift,
      int? belongSkuId,
      String loadingText,
      Function(ChangeStatus status, String? msg, String? contactRoute)?
          changeListener) async {
    try {
      final Map<String, dynamic> map = {
        "orderNo": orderNo,
        "ownSkuId": ownSkuId,
        "newSkuId": newSkuId,
        "newCourseId": newCourseId,
        "gift": gift,
        "belongSkuId": belongSkuId
      };
      LoadingCircle.show(
          msg: loadingText,
          maskWidget: Container(
            color: SmartDialog.config.custom.maskColor,
          ));
      LessonChangeResult? changeResult = await _api?.lessonChange(orderNo, map);
      if (changeResult?.success != null && changeResult?.success == false) {
        //更换失败,展示更换失败弹窗
        changeListener?.call(
            ChangeStatus.changeFail, "", changeResult?.contactRoute);
        return;
      }
      if (changeResult?.success == true && (changeResult?.courseId ?? 0) > 0) {
        //商城订单创建成功，开始轮训
        await retryRequest(() {
          return _api!
              .lessonChangeQuery(changeResult?.orderNo, changeResult?.courseId);
        }, changeResult, changeListener);
        return;
      }
    } catch (e, stack) {
      l.e("2025交换课列表", "$e\n stack=$stack");
      changeListener?.call(ChangeStatus.changeFail, "", '');
    } finally {
      LoadingCircle.dismiss();
    }
  }

  Future<LessonChangeResultQuery?> retryRequest(
      Future<LessonChangeResultQuery?> Function() request,
      LessonChangeResult? changeResult,
      Function(ChangeStatus status, String? msg, String? contactRoute)?
          changeListener) async {
    int retryCount = 0;
    int maxRetries = 7;
    while (retryCount <= maxRetries) {
      try {
        await Future.delayed(const Duration(seconds: 3));
        LessonChangeResultQuery? queryResult = await request();
        SimpleCourseCardList? queryInfo = queryResult?.simpleCourseCardList?[0];
        if (queryInfo != null &&
            queryInfo.unactivatedReason != null &&
            queryInfo.unactivatedReasonType != null &&
            queryInfo.unactivatedReasonType != 0) {
          //查询到失败，展示激活失败弹窗
          changeListener?.call(ChangeStatus.lessonActionFail,
              queryInfo.unactivatedReason, changeResult?.contactRoute);
          return queryResult;
        }
        if (retryCount > maxRetries) {
          //更换超时，展示超时弹窗,按钮返回学习页面
          changeListener?.call(ChangeStatus.lessonActionTimeout, '', '');
          return queryResult;
        }
        if (queryInfo != null && (queryInfo.classId ?? 0) > 0) {
          //更换成功,用新的courseId，classId更新数据，并且展示toast数据
          changeListener?.call(ChangeStatus.changeSuccess, '', '');
          classId = queryInfo.classId;
          courseId = queryInfo.courseId;
          if (queryInfo.classKey != null) {
            JoJoNativeBridge.shared.operationNativeValueSet(
                key: CHANGE_RESLUT, value: "${queryInfo.classKey}"); //存储交换结果
          }
          onRefresh();
          return queryResult;
        }
        retryCount++;
        continue;
      } on DioException catch (e) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.unknown ||
            e.type == DioExceptionType.receiveTimeout) {
          retryCount++;
          if (retryCount > maxRetries) {
            //更换超时，展示超时弹窗
            return null;
          }
          continue;
        } else {
          rethrow;
        }
      }
    }
    changeListener?.call(ChangeStatus.lessonActionTimeout, '', '');
    return null;
  }
}

enum ChangeStatus {
  changeSuccess,
  changeFail,
  lessonActionTimeout,
  lessonActionFail
}
