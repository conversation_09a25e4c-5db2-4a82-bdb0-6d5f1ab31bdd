import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
class PlanStudyPreferenceState {
  PageStatus pageStatus;
  String? dataJsonString;
  CourseGuideService? serviceItem;

  PlanStudyPreferenceState({required this.pageStatus, required this.serviceItem, required this.dataJsonString,});

  PlanStudyPreferenceState copyWith() {
    return PlanStudyPreferenceState(
      pageStatus: pageStatus,
      serviceItem: serviceItem,
      dataJsonString: dataJsonString,
    );
  }
}
