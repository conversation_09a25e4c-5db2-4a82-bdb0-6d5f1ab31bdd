import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/btn.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_lesson_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/state.dart';

class PlanStudyPreferencePageView extends StatefulWidget {
  final PlanStudyPreferenceState state;

  const PlanStudyPreferencePageView({
    super.key,
    required this.state,
  });

  @override
  State<StatefulWidget> createState() {
    return PlanStudyPreferencePageViewState();
  }
}

class PlanStudyPreferencePageViewState
    extends State<PlanStudyPreferencePageView> {
  int? selectedKey; // 当前选中的按钮的 navKey
  String? selectedContentImg = ''; // 当前选中的按钮的 contentImg

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        PlanStudyPreferenceCtrl _ctrl = context.read<PlanStudyPreferenceCtrl>();
        _ctrl.setPopInfo();
        if (_ctrl.state.serviceItem == null) return;
        RunEnv.sensorsTrack('ElementView', {
          'c_element_name': "学习偏好设置半弹窗",
          'course_type': getCourseTypeStr(_ctrl.state.serviceItem?.courseType),
          'business_type':
              getClassStatusStr(_ctrl.state.serviceItem?.classStatus),
          'course_stage': _ctrl.state.serviceItem?.courseSegment,
          'material_id': _ctrl.state.serviceItem?.subjectName,
          'class_id': _ctrl.state.serviceItem?.classId,
          'course_key': _ctrl.state.serviceItem?.courseKey
        });

        List<Option>? options =
            _ctrl.state.serviceItem?.popupInfo?.options ?? [];
        Option selectedOption = options.firstWhere(
          (option) => option.hasChoice == 1,
          orElse: () => const Option(), // 返回一个默认值
        );
        selectedKey = selectedOption.navKey ?? 0;
        selectedContentImg = selectedOption.contentImg ?? '';
      } catch (e) {
        l.e("学习偏好设置半弹窗", "数据获取异常");
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        primary: !JoJoRouter.isWindow,
        appBar: JoJoAppBar(
            title: widget.state.serviceItem?.popupInfo?.popupTitle ?? "",
            backgroundColor: Colors.transparent,
            centerTitle: true),
        body: Container(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            width: double.infinity,
            decoration: BoxDecoration(
              color: HexColor("#ffffff"),
            ),
            child: _buildContentWidget()));
  }

  // 正文内容
  Widget _buildContentWidget() {
    if (widget.state.serviceItem == null) return Container();
    return Stack(
      children: [
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          SizedBox(
            height: 20.rdp,
          ),
          _buildTitle(),
          SizedBox(
            height: 10.rdp,
          ),
          _buildDescription(),
          SizedBox(
            height: 40.rdp,
          ),
          _buildWeekOptions(),
          SizedBox(
            height: 20.rdp,
          ),
          _buildWeekImage(),
          SizedBox(
            height: 20.rdp,
          ),
          _buildTips(),
        ]),
        Positioned(
          bottom: 30.rdp,
          child: _buildConfirmBtn(),
        )
      ],
    );
  }

  Widget _buildTitle() {
    //标题
    return Text(
      widget.state.serviceItem?.popupInfo?.actionTitle ?? "",
      style: TextStyle(
        fontSize: 20.rdp,
        color: HexColor("#404040"),
      ),
    );
  }

  Widget _buildDescription() {
    //描述
    return Text(
      widget.state.serviceItem?.popupInfo?.introduce ?? "",
      style: TextStyle(
        fontSize: 15.rdp,
        color: HexColor("#666666"),
      ),
    );
  }

  Widget _buildWeekOptions() {
    // 获取 options 列表
    List<Option>? options = widget.state.serviceItem?.popupInfo?.options ?? [];

    // 如果 options 为空，返回空容器
    if (options.isEmpty) {
      return Container();
    }

    return Wrap(
        spacing: 16.rdp, // 按钮之间的水平间距
        runSpacing: 16.rdp, // 按钮之间的垂直间距
        children: options.take(6).map(_buildWeekItem).toList());
  }

  Widget _buildWeekItem(Option option) {
    double width = (MediaQuery.of(context).size.width - 40.rdp - 36.rdp) / 3;
    // 判断当前按钮是否选中
    bool isSelected = selectedKey == option.navKey;

    //描述
    return JoJoBtn(
      width: width,
      height: 38.rdp,
      fontSize: 14.rdp,
      text: option.navTitle ?? "",
      color: isSelected ? HexColor("#FCDA00") : HexColor('#F6F7F8'),
      fontColor: isSelected ? HexColor("#544300") : HexColor('#2B323C'),
      tapHandle: () {
        RunEnv.sensorsTrack('\$AppClick', {
          '\$screen_name': "学习偏好设置半弹窗",
          '\$element_name': option.navTitle,
          'course_type': getCourseTypeStr(widget.state.serviceItem?.courseType),
          'business_type':
              getClassStatusStr(widget.state.serviceItem?.classStatus),
          'course_stage': widget.state.serviceItem?.courseSegment,
          'material_id': widget.state.serviceItem?.subjectName,
          'class_id': widget.state.serviceItem?.classId,
          'course_key': widget.state.serviceItem?.courseKey
        });
        // 更新选中状态
        setState(() {
          selectedKey = option.navKey;
          selectedContentImg = option.contentImg;
        });
      },
    );
  }

  Widget _buildWeekImage() {
    //星期的图片
    return ImageNetworkCached(
      imageUrl: selectedContentImg ?? "",
      fit: BoxFit.cover,
      width: MediaQuery.of(context).size.width,
      placeholderWidget: Container(),
      errorWidget: Container(),
    );
  }

  Widget _buildTips() {
    String? tip = selectedContentImg ?? '';
    //提示
    return Text(
      tip.isNotEmpty
          ? widget.state.serviceItem?.popupInfo?.actionTip ?? ""
          : '',
      style: TextStyle(
        fontSize: 12.rdp,
        color: HexColor("#B2B2B2"),
      ),
    );
  }

  Widget _buildConfirmBtn() {
    double width = MediaQuery.of(context).size.width - 40.rdp;
    //描述
    int confirmKey = selectedKey ?? 0;
    return JoJoBtn(
      width: width,
      color: confirmKey > 0 ? HexColor('#FCDA00') : HexColor("#F5F4F4"),
      fontColor: confirmKey > 0 ? HexColor('#544300') : HexColor("#B2B2B2"),
      height: 44.rdp,
      fontSize: 18.rdp,
      text: widget.state.serviceItem?.popupInfo?.buttonText ?? "",
      tapHandle: () async {
        if (!mounted) return;
        if (selectedKey == null) {
          l.e("学习偏好设置", "selectedKey为空");
          return;
        }
        try {
          PlanStudyPreferenceCtrl ctrl =
              context.read<PlanStudyPreferenceCtrl>();
          await ctrl.requestStudyPreference(
              selectedKey, widget.state.serviceItem?.popupInfo?.closedText);
          // 关闭页面
          JoJoRouter.pop();
        } catch (e) {
          JoJoToast.showSuccess('学习偏好保存失败');
          l.e("学习偏好设置", "获取ctrl失败");
        }
        RunEnv.sensorsTrack('\$AppClick', {
          '\$screen_name': "学习偏好设置半弹窗",
          '\$element_name': widget.state.serviceItem?.popupInfo?.buttonText,
          'course_type': getCourseTypeStr(widget.state.serviceItem?.courseType),
          'business_type':
              getClassStatusStr(widget.state.serviceItem?.classStatus),
          'course_stage': widget.state.serviceItem?.courseSegment,
          'material_id': widget.state.serviceItem?.subjectName,
          'class_id': widget.state.serviceItem?.classId,
          'course_key': widget.state.serviceItem?.courseKey
        });
      },
    );
  }

  // 获取课程类型(用于埋点,不做国际化)
  String getCourseTypeStr(int? courseType) {
    switch (courseType) {
      case 1:
        return "训练营";
      case 2:
        return "单课";
      case 3:
        return "年课";
      case 4:
        return "体验课";
      default:
        return "unknown";
    }
  }

  // 获取班期状态(用于埋点,不做国际化)
  String getClassStatusStr(int? classStatus) {
    switch (classStatus) {
      case 1:
        return "行课期";
      case 2:
        return "等待期";
      case 3:
        return "结课期";
      case 4:
        return "缓冲期";
      case 5:
        return "缓冲期外";
      case 6:
        return "未激活";
      default:
        return "unknown";
    }
  }
}
