import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/state.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_lesson_page_api.dart';

class PlanStudyPreferenceCtrl extends Cubit<PlanStudyPreferenceState> {
  final String? dataJsonString;
  final int? classId;
  final int? courseId;
  HomeMapLessonPageApi? _lessonPageApi;

  PlanStudyPreferenceCtrl(
      {HomeMapLessonPageApi? api,
      required this.dataJsonString,
      required this.classId,
      required this.courseId})
      : super(
          PlanStudyPreferenceState(
              pageStatus: PageStatus.loading,
              serviceItem: null,
              dataJsonString: dataJsonString),
        ) {
    _lessonPageApi = api ?? homeMapPageApiService;
  }

  /// 把json转化成对象
  void setPopInfo() {
    if (dataJsonString == null) {
      return;
    }
    PlanStudyPreferenceState newState = state.copyWith();
    newState.serviceItem =
        CourseGuideService.fromJson(jsonDecode(dataJsonString!));
    newState.pageStatus = PageStatus.success;
    emit(newState);
  }

  /// 上传学习偏好数据
  Future requestStudyPreference(int? navKey, String? toastString) async {
    try {
      final Map<String, dynamic> map = {
        "type": "learning_preference",
        "learningPreference": {
          "classId": classId ?? 0,
          "courseId": courseId ?? 0,
          "preferenceType": navKey
        }
      };
      JoJoLoading.show();
      await _lessonPageApi?.requestStudyPreference(map);
      JoJoLoading.dismiss();
      JoJoToast.showSuccess(toastString ?? '学习偏好已保存');
      SmartDialog.dismiss();
    } catch (e, stack) {
      JoJoLoading.dismiss();
      JoJoToast.showError('学习偏好保存失败');
      l.e("学习偏好设置", "上报接口异常: $e\n stack=$stack");
    }
  }
}
