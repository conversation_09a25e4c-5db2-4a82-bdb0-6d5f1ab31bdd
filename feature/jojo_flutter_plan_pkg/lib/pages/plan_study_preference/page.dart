import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/view.dart';

class PlanStudyPreferencePageModel extends BasePage {
  final String? dataJsonString;
  final int? classId;
  final int? courseId;

  const PlanStudyPreferencePageModel({Key? key, this.dataJsonString, this.classId, this.courseId}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _PlanStudyPreferenceState();
}

class _PlanStudyPreferenceState extends BaseState<PlanStudyPreferencePageModel> with BasicInitPage {

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => PlanStudyPreferenceCtrl(dataJsonString: widget.dataJsonString, classId: widget.classId, courseId: widget.courseId),
      child: BlocBuilder<PlanStudyPreferenceCtrl, PlanStudyPreferenceState>(
          builder: (context, state) {
            return PlanStudyPreferencePageView(state: state);
          }),
    );
  }
}
