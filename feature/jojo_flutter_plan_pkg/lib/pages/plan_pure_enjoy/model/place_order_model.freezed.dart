// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'place_order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlaceOrderModel _$PlaceOrderModelFromJson(Map<String, dynamic> json) {
  return _PlaceOrderModel.fromJson(json);
}

/// @nodoc
mixin _$PlaceOrderModel {
  String? get orderId => throw _privateConstructorUsedError;
  ErrorTips? get errorTips => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlaceOrderModelCopyWith<PlaceOrderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlaceOrderModelCopyWith<$Res> {
  factory $PlaceOrderModelCopyWith(
          PlaceOrderModel value, $Res Function(PlaceOrderModel) then) =
      _$PlaceOrderModelCopyWithImpl<$Res, PlaceOrderModel>;
  @useResult
  $Res call({String? orderId, ErrorTips? errorTips});

  $ErrorTipsCopyWith<$Res>? get errorTips;
}

/// @nodoc
class _$PlaceOrderModelCopyWithImpl<$Res, $Val extends PlaceOrderModel>
    implements $PlaceOrderModelCopyWith<$Res> {
  _$PlaceOrderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = freezed,
    Object? errorTips = freezed,
  }) {
    return _then(_value.copyWith(
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorTips: freezed == errorTips
          ? _value.errorTips
          : errorTips // ignore: cast_nullable_to_non_nullable
              as ErrorTips?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ErrorTipsCopyWith<$Res>? get errorTips {
    if (_value.errorTips == null) {
      return null;
    }

    return $ErrorTipsCopyWith<$Res>(_value.errorTips!, (value) {
      return _then(_value.copyWith(errorTips: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PlaceOrderModelCopyWith<$Res>
    implements $PlaceOrderModelCopyWith<$Res> {
  factory _$$_PlaceOrderModelCopyWith(
          _$_PlaceOrderModel value, $Res Function(_$_PlaceOrderModel) then) =
      __$$_PlaceOrderModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? orderId, ErrorTips? errorTips});

  @override
  $ErrorTipsCopyWith<$Res>? get errorTips;
}

/// @nodoc
class __$$_PlaceOrderModelCopyWithImpl<$Res>
    extends _$PlaceOrderModelCopyWithImpl<$Res, _$_PlaceOrderModel>
    implements _$$_PlaceOrderModelCopyWith<$Res> {
  __$$_PlaceOrderModelCopyWithImpl(
      _$_PlaceOrderModel _value, $Res Function(_$_PlaceOrderModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = freezed,
    Object? errorTips = freezed,
  }) {
    return _then(_$_PlaceOrderModel(
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      errorTips: freezed == errorTips
          ? _value.errorTips
          : errorTips // ignore: cast_nullable_to_non_nullable
              as ErrorTips?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlaceOrderModel implements _PlaceOrderModel {
  const _$_PlaceOrderModel({this.orderId, this.errorTips});

  factory _$_PlaceOrderModel.fromJson(Map<String, dynamic> json) =>
      _$$_PlaceOrderModelFromJson(json);

  @override
  final String? orderId;
  @override
  final ErrorTips? errorTips;

  @override
  String toString() {
    return 'PlaceOrderModel(orderId: $orderId, errorTips: $errorTips)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlaceOrderModel &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.errorTips, errorTips) ||
                other.errorTips == errorTips));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, orderId, errorTips);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlaceOrderModelCopyWith<_$_PlaceOrderModel> get copyWith =>
      __$$_PlaceOrderModelCopyWithImpl<_$_PlaceOrderModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlaceOrderModelToJson(
      this,
    );
  }
}

abstract class _PlaceOrderModel implements PlaceOrderModel {
  const factory _PlaceOrderModel(
      {final String? orderId, final ErrorTips? errorTips}) = _$_PlaceOrderModel;

  factory _PlaceOrderModel.fromJson(Map<String, dynamic> json) =
      _$_PlaceOrderModel.fromJson;

  @override
  String? get orderId;
  @override
  ErrorTips? get errorTips;
  @override
  @JsonKey(ignore: true)
  _$$_PlaceOrderModelCopyWith<_$_PlaceOrderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

ErrorTips _$ErrorTipsFromJson(Map<String, dynamic> json) {
  return _ErrorTips.fromJson(json);
}

/// @nodoc
mixin _$ErrorTips {
  String? get message => throw _privateConstructorUsedError;
  List<ErrorButton>? get buttons => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ErrorTipsCopyWith<ErrorTips> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorTipsCopyWith<$Res> {
  factory $ErrorTipsCopyWith(ErrorTips value, $Res Function(ErrorTips) then) =
      _$ErrorTipsCopyWithImpl<$Res, ErrorTips>;
  @useResult
  $Res call({String? message, List<ErrorButton>? buttons});
}

/// @nodoc
class _$ErrorTipsCopyWithImpl<$Res, $Val extends ErrorTips>
    implements $ErrorTipsCopyWith<$Res> {
  _$ErrorTipsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? buttons = freezed,
  }) {
    return _then(_value.copyWith(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttons: freezed == buttons
          ? _value.buttons
          : buttons // ignore: cast_nullable_to_non_nullable
              as List<ErrorButton>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ErrorTipsCopyWith<$Res> implements $ErrorTipsCopyWith<$Res> {
  factory _$$_ErrorTipsCopyWith(
          _$_ErrorTips value, $Res Function(_$_ErrorTips) then) =
      __$$_ErrorTipsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message, List<ErrorButton>? buttons});
}

/// @nodoc
class __$$_ErrorTipsCopyWithImpl<$Res>
    extends _$ErrorTipsCopyWithImpl<$Res, _$_ErrorTips>
    implements _$$_ErrorTipsCopyWith<$Res> {
  __$$_ErrorTipsCopyWithImpl(
      _$_ErrorTips _value, $Res Function(_$_ErrorTips) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? buttons = freezed,
  }) {
    return _then(_$_ErrorTips(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttons: freezed == buttons
          ? _value._buttons
          : buttons // ignore: cast_nullable_to_non_nullable
              as List<ErrorButton>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ErrorTips implements _ErrorTips {
  const _$_ErrorTips({this.message, final List<ErrorButton>? buttons})
      : _buttons = buttons;

  factory _$_ErrorTips.fromJson(Map<String, dynamic> json) =>
      _$$_ErrorTipsFromJson(json);

  @override
  final String? message;
  final List<ErrorButton>? _buttons;
  @override
  List<ErrorButton>? get buttons {
    final value = _buttons;
    if (value == null) return null;
    if (_buttons is EqualUnmodifiableListView) return _buttons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ErrorTips(message: $message, buttons: $buttons)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ErrorTips &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._buttons, _buttons));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, message, const DeepCollectionEquality().hash(_buttons));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ErrorTipsCopyWith<_$_ErrorTips> get copyWith =>
      __$$_ErrorTipsCopyWithImpl<_$_ErrorTips>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ErrorTipsToJson(
      this,
    );
  }
}

abstract class _ErrorTips implements ErrorTips {
  const factory _ErrorTips(
      {final String? message, final List<ErrorButton>? buttons}) = _$_ErrorTips;

  factory _ErrorTips.fromJson(Map<String, dynamic> json) =
      _$_ErrorTips.fromJson;

  @override
  String? get message;
  @override
  List<ErrorButton>? get buttons;
  @override
  @JsonKey(ignore: true)
  _$$_ErrorTipsCopyWith<_$_ErrorTips> get copyWith =>
      throw _privateConstructorUsedError;
}

ErrorButton _$ErrorButtonFromJson(Map<String, dynamic> json) {
  return _ErrorButton.fromJson(json);
}

/// @nodoc
mixin _$ErrorButton {
  String? get description => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ErrorButtonCopyWith<ErrorButton> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorButtonCopyWith<$Res> {
  factory $ErrorButtonCopyWith(
          ErrorButton value, $Res Function(ErrorButton) then) =
      _$ErrorButtonCopyWithImpl<$Res, ErrorButton>;
  @useResult
  $Res call({String? description, String? linkUrl});
}

/// @nodoc
class _$ErrorButtonCopyWithImpl<$Res, $Val extends ErrorButton>
    implements $ErrorButtonCopyWith<$Res> {
  _$ErrorButtonCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ErrorButtonCopyWith<$Res>
    implements $ErrorButtonCopyWith<$Res> {
  factory _$$_ErrorButtonCopyWith(
          _$_ErrorButton value, $Res Function(_$_ErrorButton) then) =
      __$$_ErrorButtonCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? description, String? linkUrl});
}

/// @nodoc
class __$$_ErrorButtonCopyWithImpl<$Res>
    extends _$ErrorButtonCopyWithImpl<$Res, _$_ErrorButton>
    implements _$$_ErrorButtonCopyWith<$Res> {
  __$$_ErrorButtonCopyWithImpl(
      _$_ErrorButton _value, $Res Function(_$_ErrorButton) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_$_ErrorButton(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ErrorButton implements _ErrorButton {
  const _$_ErrorButton({this.description, this.linkUrl});

  factory _$_ErrorButton.fromJson(Map<String, dynamic> json) =>
      _$$_ErrorButtonFromJson(json);

  @override
  final String? description;
  @override
  final String? linkUrl;

  @override
  String toString() {
    return 'ErrorButton(description: $description, linkUrl: $linkUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ErrorButton &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, description, linkUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ErrorButtonCopyWith<_$_ErrorButton> get copyWith =>
      __$$_ErrorButtonCopyWithImpl<_$_ErrorButton>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ErrorButtonToJson(
      this,
    );
  }
}

abstract class _ErrorButton implements ErrorButton {
  const factory _ErrorButton(
      {final String? description, final String? linkUrl}) = _$_ErrorButton;

  factory _ErrorButton.fromJson(Map<String, dynamic> json) =
      _$_ErrorButton.fromJson;

  @override
  String? get description;
  @override
  String? get linkUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ErrorButtonCopyWith<_$_ErrorButton> get copyWith =>
      throw _privateConstructorUsedError;
}
