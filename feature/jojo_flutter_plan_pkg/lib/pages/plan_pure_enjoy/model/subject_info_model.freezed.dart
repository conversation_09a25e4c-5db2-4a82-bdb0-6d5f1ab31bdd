// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subject_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SubjectInfoModel _$SubjectInfoModelFromJson(Map<String, dynamic> json) {
  return _SubjectInfoModel.fromJson(json);
}

/// @nodoc
mixin _$SubjectInfoModel {
  bool? get selected => throw _privateConstructorUsedError;
  bool? get skipped => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  String? get titleColor => throw _privateConstructorUsedError;
  String? get descriptionColor => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get subDesc => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  String? get typeDesc => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get selectedIcon => throw _privateConstructorUsedError;
  String? get selectedTextColor => throw _privateConstructorUsedError;
  String? get selectedBgColor => throw _privateConstructorUsedError;
  String? get libId => throw _privateConstructorUsedError;
  String? get topTips => throw _privateConstructorUsedError;
  String? get topIcon => throw _privateConstructorUsedError;
  List<GradeInfoModel>? get gradeInfoList => throw _privateConstructorUsedError;
  String? get missionMaterialType => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  int? get lastSelectedTipType => throw _privateConstructorUsedError;
  bool? get lastSelected => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectInfoModelCopyWith<SubjectInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectInfoModelCopyWith<$Res> {
  factory $SubjectInfoModelCopyWith(
          SubjectInfoModel value, $Res Function(SubjectInfoModel) then) =
      _$SubjectInfoModelCopyWithImpl<$Res, SubjectInfoModel>;
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      String? pictureUrl,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? btnText,
      String? subDesc,
      int? type,
      String? typeDesc,
      String? subjectTypeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedTextColor,
      String? selectedBgColor,
      String? libId,
      String? topTips,
      String? topIcon,
      List<GradeInfoModel>? gradeInfoList,
      String? missionMaterialType,
      String? linkUrl,
      int? lastSelectedTipType,
      bool? lastSelected});
}

/// @nodoc
class _$SubjectInfoModelCopyWithImpl<$Res, $Val extends SubjectInfoModel>
    implements $SubjectInfoModelCopyWith<$Res> {
  _$SubjectInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? btnText = freezed,
    Object? subDesc = freezed,
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? subjectTypeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? selectedBgColor = freezed,
    Object? libId = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? gradeInfoList = freezed,
    Object? missionMaterialType = freezed,
    Object? linkUrl = freezed,
    Object? lastSelectedTipType = freezed,
    Object? lastSelected = freezed,
  }) {
    return _then(_value.copyWith(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      subDesc: freezed == subDesc
          ? _value.subDesc
          : subDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      libId: freezed == libId
          ? _value.libId
          : libId // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeInfoList: freezed == gradeInfoList
          ? _value.gradeInfoList
          : gradeInfoList // ignore: cast_nullable_to_non_nullable
              as List<GradeInfoModel>?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSelectedTipType: freezed == lastSelectedTipType
          ? _value.lastSelectedTipType
          : lastSelectedTipType // ignore: cast_nullable_to_non_nullable
              as int?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectInfoModelCopyWith<$Res>
    implements $SubjectInfoModelCopyWith<$Res> {
  factory _$$_SubjectInfoModelCopyWith(
          _$_SubjectInfoModel value, $Res Function(_$_SubjectInfoModel) then) =
      __$$_SubjectInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      String? pictureUrl,
      String? backgroundColor,
      String? titleColor,
      String? descriptionColor,
      String? btnText,
      String? subDesc,
      int? type,
      String? typeDesc,
      String? subjectTypeDesc,
      String? icon,
      String? selectedIcon,
      String? selectedTextColor,
      String? selectedBgColor,
      String? libId,
      String? topTips,
      String? topIcon,
      List<GradeInfoModel>? gradeInfoList,
      String? missionMaterialType,
      String? linkUrl,
      int? lastSelectedTipType,
      bool? lastSelected});
}

/// @nodoc
class __$$_SubjectInfoModelCopyWithImpl<$Res>
    extends _$SubjectInfoModelCopyWithImpl<$Res, _$_SubjectInfoModel>
    implements _$$_SubjectInfoModelCopyWith<$Res> {
  __$$_SubjectInfoModelCopyWithImpl(
      _$_SubjectInfoModel _value, $Res Function(_$_SubjectInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? btnText = freezed,
    Object? subDesc = freezed,
    Object? type = freezed,
    Object? typeDesc = freezed,
    Object? subjectTypeDesc = freezed,
    Object? icon = freezed,
    Object? selectedIcon = freezed,
    Object? selectedTextColor = freezed,
    Object? selectedBgColor = freezed,
    Object? libId = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? gradeInfoList = freezed,
    Object? missionMaterialType = freezed,
    Object? linkUrl = freezed,
    Object? lastSelectedTipType = freezed,
    Object? lastSelected = freezed,
  }) {
    return _then(_$_SubjectInfoModel(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      subDesc: freezed == subDesc
          ? _value.subDesc
          : subDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      typeDesc: freezed == typeDesc
          ? _value.typeDesc
          : typeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedIcon: freezed == selectedIcon
          ? _value.selectedIcon
          : selectedIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedTextColor: freezed == selectedTextColor
          ? _value.selectedTextColor
          : selectedTextColor // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedBgColor: freezed == selectedBgColor
          ? _value.selectedBgColor
          : selectedBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      libId: freezed == libId
          ? _value.libId
          : libId // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeInfoList: freezed == gradeInfoList
          ? _value._gradeInfoList
          : gradeInfoList // ignore: cast_nullable_to_non_nullable
              as List<GradeInfoModel>?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSelectedTipType: freezed == lastSelectedTipType
          ? _value.lastSelectedTipType
          : lastSelectedTipType // ignore: cast_nullable_to_non_nullable
              as int?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectInfoModel implements _SubjectInfoModel {
  const _$_SubjectInfoModel(
      {this.selected,
      this.skipped,
      this.pictureUrl,
      this.backgroundColor,
      this.titleColor,
      this.descriptionColor,
      this.btnText,
      this.subDesc,
      this.type,
      this.typeDesc,
      this.subjectTypeDesc,
      this.icon,
      this.selectedIcon,
      this.selectedTextColor,
      this.selectedBgColor,
      this.libId,
      this.topTips,
      this.topIcon,
      final List<GradeInfoModel>? gradeInfoList,
      this.missionMaterialType,
      this.linkUrl,
      this.lastSelectedTipType,
      this.lastSelected})
      : _gradeInfoList = gradeInfoList;

  factory _$_SubjectInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectInfoModelFromJson(json);

  @override
  final bool? selected;
  @override
  final bool? skipped;
  @override
  final String? pictureUrl;
  @override
  final String? backgroundColor;
  @override
  final String? titleColor;
  @override
  final String? descriptionColor;
  @override
  final String? btnText;
  @override
  final String? subDesc;
  @override
  final int? type;
  @override
  final String? typeDesc;
  @override
  final String? subjectTypeDesc;
  @override
  final String? icon;
  @override
  final String? selectedIcon;
  @override
  final String? selectedTextColor;
  @override
  final String? selectedBgColor;
  @override
  final String? libId;
  @override
  final String? topTips;
  @override
  final String? topIcon;
  final List<GradeInfoModel>? _gradeInfoList;
  @override
  List<GradeInfoModel>? get gradeInfoList {
    final value = _gradeInfoList;
    if (value == null) return null;
    if (_gradeInfoList is EqualUnmodifiableListView) return _gradeInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? missionMaterialType;
  @override
  final String? linkUrl;
  @override
  final int? lastSelectedTipType;
  @override
  final bool? lastSelected;

  @override
  String toString() {
    return 'SubjectInfoModel(selected: $selected, skipped: $skipped, pictureUrl: $pictureUrl, backgroundColor: $backgroundColor, titleColor: $titleColor, descriptionColor: $descriptionColor, btnText: $btnText, subDesc: $subDesc, type: $type, typeDesc: $typeDesc, subjectTypeDesc: $subjectTypeDesc, icon: $icon, selectedIcon: $selectedIcon, selectedTextColor: $selectedTextColor, selectedBgColor: $selectedBgColor, libId: $libId, topTips: $topTips, topIcon: $topIcon, gradeInfoList: $gradeInfoList, missionMaterialType: $missionMaterialType, linkUrl: $linkUrl, lastSelectedTipType: $lastSelectedTipType, lastSelected: $lastSelected)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectInfoModel &&
            (identical(other.selected, selected) ||
                other.selected == selected) &&
            (identical(other.skipped, skipped) || other.skipped == skipped) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.titleColor, titleColor) ||
                other.titleColor == titleColor) &&
            (identical(other.descriptionColor, descriptionColor) ||
                other.descriptionColor == descriptionColor) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.subDesc, subDesc) || other.subDesc == subDesc) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.typeDesc, typeDesc) ||
                other.typeDesc == typeDesc) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.selectedIcon, selectedIcon) ||
                other.selectedIcon == selectedIcon) &&
            (identical(other.selectedTextColor, selectedTextColor) ||
                other.selectedTextColor == selectedTextColor) &&
            (identical(other.selectedBgColor, selectedBgColor) ||
                other.selectedBgColor == selectedBgColor) &&
            (identical(other.libId, libId) || other.libId == libId) &&
            (identical(other.topTips, topTips) || other.topTips == topTips) &&
            (identical(other.topIcon, topIcon) || other.topIcon == topIcon) &&
            const DeepCollectionEquality()
                .equals(other._gradeInfoList, _gradeInfoList) &&
            (identical(other.missionMaterialType, missionMaterialType) ||
                other.missionMaterialType == missionMaterialType) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.lastSelectedTipType, lastSelectedTipType) ||
                other.lastSelectedTipType == lastSelectedTipType) &&
            (identical(other.lastSelected, lastSelected) ||
                other.lastSelected == lastSelected));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        selected,
        skipped,
        pictureUrl,
        backgroundColor,
        titleColor,
        descriptionColor,
        btnText,
        subDesc,
        type,
        typeDesc,
        subjectTypeDesc,
        icon,
        selectedIcon,
        selectedTextColor,
        selectedBgColor,
        libId,
        topTips,
        topIcon,
        const DeepCollectionEquality().hash(_gradeInfoList),
        missionMaterialType,
        linkUrl,
        lastSelectedTipType,
        lastSelected
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectInfoModelCopyWith<_$_SubjectInfoModel> get copyWith =>
      __$$_SubjectInfoModelCopyWithImpl<_$_SubjectInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectInfoModelToJson(
      this,
    );
  }
}

abstract class _SubjectInfoModel implements SubjectInfoModel {
  const factory _SubjectInfoModel(
      {final bool? selected,
      final bool? skipped,
      final String? pictureUrl,
      final String? backgroundColor,
      final String? titleColor,
      final String? descriptionColor,
      final String? btnText,
      final String? subDesc,
      final int? type,
      final String? typeDesc,
      final String? subjectTypeDesc,
      final String? icon,
      final String? selectedIcon,
      final String? selectedTextColor,
      final String? selectedBgColor,
      final String? libId,
      final String? topTips,
      final String? topIcon,
      final List<GradeInfoModel>? gradeInfoList,
      final String? missionMaterialType,
      final String? linkUrl,
      final int? lastSelectedTipType,
      final bool? lastSelected}) = _$_SubjectInfoModel;

  factory _SubjectInfoModel.fromJson(Map<String, dynamic> json) =
      _$_SubjectInfoModel.fromJson;

  @override
  bool? get selected;
  @override
  bool? get skipped;
  @override
  String? get pictureUrl;
  @override
  String? get backgroundColor;
  @override
  String? get titleColor;
  @override
  String? get descriptionColor;
  @override
  String? get btnText;
  @override
  String? get subDesc;
  @override
  int? get type;
  @override
  String? get typeDesc;
  @override
  String? get subjectTypeDesc;
  @override
  String? get icon;
  @override
  String? get selectedIcon;
  @override
  String? get selectedTextColor;
  @override
  String? get selectedBgColor;
  @override
  String? get libId;
  @override
  String? get topTips;
  @override
  String? get topIcon;
  @override
  List<GradeInfoModel>? get gradeInfoList;
  @override
  String? get missionMaterialType;
  @override
  String? get linkUrl;
  @override
  int? get lastSelectedTipType;
  @override
  bool? get lastSelected;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectInfoModelCopyWith<_$_SubjectInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

GradeInfoModel _$GradeInfoModelFromJson(Map<String, dynamic> json) {
  return _GradeInfoModel.fromJson(json);
}

/// @nodoc
mixin _$GradeInfoModel {
  bool? get selected => throw _privateConstructorUsedError;
  bool? get skipped => throw _privateConstructorUsedError;
  bool? get lastSelected => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  String? get gradeDesc => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get topTips => throw _privateConstructorUsedError;
  String? get topIcon => throw _privateConstructorUsedError;
  String? get resourceId => throw _privateConstructorUsedError;
  List<CourseCardModel>? get courseCardDataList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GradeInfoModelCopyWith<GradeInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GradeInfoModelCopyWith<$Res> {
  factory $GradeInfoModelCopyWith(
          GradeInfoModel value, $Res Function(GradeInfoModel) then) =
      _$GradeInfoModelCopyWithImpl<$Res, GradeInfoModel>;
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      bool? lastSelected,
      int? gradeKey,
      String? gradeTitle,
      String? gradeDesc,
      String? tip,
      String? topTips,
      String? topIcon,
      String? resourceId,
      List<CourseCardModel>? courseCardDataList});
}

/// @nodoc
class _$GradeInfoModelCopyWithImpl<$Res, $Val extends GradeInfoModel>
    implements $GradeInfoModelCopyWith<$Res> {
  _$GradeInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? lastSelected = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? gradeDesc = freezed,
    Object? tip = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? resourceId = freezed,
    Object? courseCardDataList = freezed,
  }) {
    return _then(_value.copyWith(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeDesc: freezed == gradeDesc
          ? _value.gradeDesc
          : gradeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCardDataList: freezed == courseCardDataList
          ? _value.courseCardDataList
          : courseCardDataList // ignore: cast_nullable_to_non_nullable
              as List<CourseCardModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GradeInfoModelCopyWith<$Res>
    implements $GradeInfoModelCopyWith<$Res> {
  factory _$$_GradeInfoModelCopyWith(
          _$_GradeInfoModel value, $Res Function(_$_GradeInfoModel) then) =
      __$$_GradeInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      bool? lastSelected,
      int? gradeKey,
      String? gradeTitle,
      String? gradeDesc,
      String? tip,
      String? topTips,
      String? topIcon,
      String? resourceId,
      List<CourseCardModel>? courseCardDataList});
}

/// @nodoc
class __$$_GradeInfoModelCopyWithImpl<$Res>
    extends _$GradeInfoModelCopyWithImpl<$Res, _$_GradeInfoModel>
    implements _$$_GradeInfoModelCopyWith<$Res> {
  __$$_GradeInfoModelCopyWithImpl(
      _$_GradeInfoModel _value, $Res Function(_$_GradeInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? lastSelected = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? gradeDesc = freezed,
    Object? tip = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? resourceId = freezed,
    Object? courseCardDataList = freezed,
  }) {
    return _then(_$_GradeInfoModel(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeDesc: freezed == gradeDesc
          ? _value.gradeDesc
          : gradeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCardDataList: freezed == courseCardDataList
          ? _value._courseCardDataList
          : courseCardDataList // ignore: cast_nullable_to_non_nullable
              as List<CourseCardModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GradeInfoModel implements _GradeInfoModel {
  const _$_GradeInfoModel(
      {this.selected,
      this.skipped,
      this.lastSelected,
      this.gradeKey,
      this.gradeTitle,
      this.gradeDesc,
      this.tip,
      this.topTips,
      this.topIcon,
      this.resourceId,
      final List<CourseCardModel>? courseCardDataList})
      : _courseCardDataList = courseCardDataList;

  factory _$_GradeInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_GradeInfoModelFromJson(json);

  @override
  final bool? selected;
  @override
  final bool? skipped;
  @override
  final bool? lastSelected;
  @override
  final int? gradeKey;
  @override
  final String? gradeTitle;
  @override
  final String? gradeDesc;
  @override
  final String? tip;
  @override
  final String? topTips;
  @override
  final String? topIcon;
  @override
  final String? resourceId;
  final List<CourseCardModel>? _courseCardDataList;
  @override
  List<CourseCardModel>? get courseCardDataList {
    final value = _courseCardDataList;
    if (value == null) return null;
    if (_courseCardDataList is EqualUnmodifiableListView)
      return _courseCardDataList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'GradeInfoModel(selected: $selected, skipped: $skipped, lastSelected: $lastSelected, gradeKey: $gradeKey, gradeTitle: $gradeTitle, gradeDesc: $gradeDesc, tip: $tip, topTips: $topTips, topIcon: $topIcon, resourceId: $resourceId, courseCardDataList: $courseCardDataList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GradeInfoModel &&
            (identical(other.selected, selected) ||
                other.selected == selected) &&
            (identical(other.skipped, skipped) || other.skipped == skipped) &&
            (identical(other.lastSelected, lastSelected) ||
                other.lastSelected == lastSelected) &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.gradeDesc, gradeDesc) ||
                other.gradeDesc == gradeDesc) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.topTips, topTips) || other.topTips == topTips) &&
            (identical(other.topIcon, topIcon) || other.topIcon == topIcon) &&
            (identical(other.resourceId, resourceId) ||
                other.resourceId == resourceId) &&
            const DeepCollectionEquality()
                .equals(other._courseCardDataList, _courseCardDataList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      selected,
      skipped,
      lastSelected,
      gradeKey,
      gradeTitle,
      gradeDesc,
      tip,
      topTips,
      topIcon,
      resourceId,
      const DeepCollectionEquality().hash(_courseCardDataList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GradeInfoModelCopyWith<_$_GradeInfoModel> get copyWith =>
      __$$_GradeInfoModelCopyWithImpl<_$_GradeInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GradeInfoModelToJson(
      this,
    );
  }
}

abstract class _GradeInfoModel implements GradeInfoModel {
  const factory _GradeInfoModel(
      {final bool? selected,
      final bool? skipped,
      final bool? lastSelected,
      final int? gradeKey,
      final String? gradeTitle,
      final String? gradeDesc,
      final String? tip,
      final String? topTips,
      final String? topIcon,
      final String? resourceId,
      final List<CourseCardModel>? courseCardDataList}) = _$_GradeInfoModel;

  factory _GradeInfoModel.fromJson(Map<String, dynamic> json) =
      _$_GradeInfoModel.fromJson;

  @override
  bool? get selected;
  @override
  bool? get skipped;
  @override
  bool? get lastSelected;
  @override
  int? get gradeKey;
  @override
  String? get gradeTitle;
  @override
  String? get gradeDesc;
  @override
  String? get tip;
  @override
  String? get topTips;
  @override
  String? get topIcon;
  @override
  String? get resourceId;
  @override
  List<CourseCardModel>? get courseCardDataList;
  @override
  @JsonKey(ignore: true)
  _$$_GradeInfoModelCopyWith<_$_GradeInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseCardModel _$CourseCardModelFromJson(Map<String, dynamic> json) {
  return _CourseCardModel.fromJson(json);
}

/// @nodoc
mixin _$CourseCardModel {
  List<CourseInfoModel>? get courseInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseCardModelCopyWith<CourseCardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseCardModelCopyWith<$Res> {
  factory $CourseCardModelCopyWith(
          CourseCardModel value, $Res Function(CourseCardModel) then) =
      _$CourseCardModelCopyWithImpl<$Res, CourseCardModel>;
  @useResult
  $Res call({List<CourseInfoModel>? courseInfoList});
}

/// @nodoc
class _$CourseCardModelCopyWithImpl<$Res, $Val extends CourseCardModel>
    implements $CourseCardModelCopyWith<$Res> {
  _$CourseCardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      courseInfoList: freezed == courseInfoList
          ? _value.courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseCardModelCopyWith<$Res>
    implements $CourseCardModelCopyWith<$Res> {
  factory _$$_CourseCardModelCopyWith(
          _$_CourseCardModel value, $Res Function(_$_CourseCardModel) then) =
      __$$_CourseCardModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CourseInfoModel>? courseInfoList});
}

/// @nodoc
class __$$_CourseCardModelCopyWithImpl<$Res>
    extends _$CourseCardModelCopyWithImpl<$Res, _$_CourseCardModel>
    implements _$$_CourseCardModelCopyWith<$Res> {
  __$$_CourseCardModelCopyWithImpl(
      _$_CourseCardModel _value, $Res Function(_$_CourseCardModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseInfoList = freezed,
  }) {
    return _then(_$_CourseCardModel(
      courseInfoList: freezed == courseInfoList
          ? _value._courseInfoList
          : courseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseCardModel implements _CourseCardModel {
  const _$_CourseCardModel({final List<CourseInfoModel>? courseInfoList})
      : _courseInfoList = courseInfoList;

  factory _$_CourseCardModel.fromJson(Map<String, dynamic> json) =>
      _$$_CourseCardModelFromJson(json);

  final List<CourseInfoModel>? _courseInfoList;
  @override
  List<CourseInfoModel>? get courseInfoList {
    final value = _courseInfoList;
    if (value == null) return null;
    if (_courseInfoList is EqualUnmodifiableListView) return _courseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseCardModel(courseInfoList: $courseInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseCardModel &&
            const DeepCollectionEquality()
                .equals(other._courseInfoList, _courseInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_courseInfoList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseCardModelCopyWith<_$_CourseCardModel> get copyWith =>
      __$$_CourseCardModelCopyWithImpl<_$_CourseCardModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseCardModelToJson(
      this,
    );
  }
}

abstract class _CourseCardModel implements CourseCardModel {
  const factory _CourseCardModel(
      {final List<CourseInfoModel>? courseInfoList}) = _$_CourseCardModel;

  factory _CourseCardModel.fromJson(Map<String, dynamic> json) =
      _$_CourseCardModel.fromJson;

  @override
  List<CourseInfoModel>? get courseInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseCardModelCopyWith<_$_CourseCardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseInfoModel _$CourseInfoModelFromJson(Map<String, dynamic> json) {
  return _CourseInfoModel.fromJson(json);
}

/// @nodoc
mixin _$CourseInfoModel {
  bool? get selected => throw _privateConstructorUsedError;
  bool? get skipped => throw _privateConstructorUsedError;
  String? get courseId => throw _privateConstructorUsedError;
  String? get courseTag => throw _privateConstructorUsedError;
  int? get courseTagKey => throw _privateConstructorUsedError;
  String? get courseName => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  String? get courseDesc => throw _privateConstructorUsedError;
  String? get tip => throw _privateConstructorUsedError;
  String? get cornerMarkerText => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  String? get experienceLink => throw _privateConstructorUsedError;
  String? get linkId => throw _privateConstructorUsedError;
  int? get courseStatus => throw _privateConstructorUsedError;
  String? get topTips => throw _privateConstructorUsedError;
  String? get topIcon => throw _privateConstructorUsedError;
  String? get teacherImgUrl => throw _privateConstructorUsedError;
  String? get learningUrl => throw _privateConstructorUsedError;
  String? get learningStatus => throw _privateConstructorUsedError;
  String? get entranceImage => throw _privateConstructorUsedError;
  String? get entranceImageSmall => throw _privateConstructorUsedError;
  String? get trainingCampTips => throw _privateConstructorUsedError;
  String? get recommendText => throw _privateConstructorUsedError; //推荐文案
  String? get recommendIcon => throw _privateConstructorUsedError; //推荐图标
  String? get trainingCampPurchaseUrl =>
      throw _privateConstructorUsedError; //训练营下单链接
  String? get trainingCampBtn => throw _privateConstructorUsedError; //训练营下单按钮图片
  String? get trainingCampDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //选中前
  String? get trainingCampDescImgSelected =>
      throw _privateConstructorUsedError; //选中后
  String? get monthCoursePurchaseUrl =>
      throw _privateConstructorUsedError; //月课下单链接
  String? get monthCourseBtn => throw _privateConstructorUsedError; //月课下单按钮
  String? get monthCourseDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //选中前
  String? get monthCourseDescImgSelected =>
      throw _privateConstructorUsedError; //选中后
  String? get yearCoursePurchaseLink =>
      throw _privateConstructorUsedError; //年课下单链接
  String? get yearCourseBtn => throw _privateConstructorUsedError; //年课下单按钮
  String? get yearCourseDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //选中前
  String? get yearCourseDescImgSelected =>
      throw _privateConstructorUsedError; //选中后
  String? get segmentDescImg => throw _privateConstructorUsedError; //介绍信息
  String? get padTrainingCampBtn =>
      throw _privateConstructorUsedError; //pad训练营下单按钮
  String? get padTrainingCampDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //pad选中前
  String? get padTrainingCampDescImgSelected =>
      throw _privateConstructorUsedError; //pad选中后
  String? get padMonthCourseBtn =>
      throw _privateConstructorUsedError; //pad月课下单按钮
  String? get padMonthCourseDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //pad选中前
  String? get padMonthCourseDescImgSelected =>
      throw _privateConstructorUsedError; //pad选中后
  String? get padYearCourseBtn =>
      throw _privateConstructorUsedError; //pad年课下单按钮
  String? get padYearCourseDescImgBeforeSelection =>
      throw _privateConstructorUsedError; //pad选中前
  String? get padYearCourseDescImgSelected =>
      throw _privateConstructorUsedError; //pad选中后
  String? get padRecommendIcon => throw _privateConstructorUsedError; // pad推荐图标
  String? get padSegmentDescImg => throw _privateConstructorUsedError; //pad介绍信息
  List<CourseBtnModel>? get receiveBtnInfos =>
      throw _privateConstructorUsedError;
  List<LessonInfoModel>? get lessonInfoList =>
      throw _privateConstructorUsedError;
  List<CourseInfoModel>? get trainingCampCourseInfoList =>
      throw _privateConstructorUsedError; // 挽留弹窗数据
  bool? get showRetentionPopup => throw _privateConstructorUsedError;
  String? get retainPopupTitle => throw _privateConstructorUsedError;
  String? get retainPopupSubTitle => throw _privateConstructorUsedError;
  String? get retainPopupImage => throw _privateConstructorUsedError;
  String? get retainPopupBtnTxt => throw _privateConstructorUsedError;
  int? get retainPopupCourseId => throw _privateConstructorUsedError;
  String? get retainPopupCourseKey => throw _privateConstructorUsedError;
  int? get retainPopupCourseLessonId => throw _privateConstructorUsedError;
  String? get retainPopupCourseName => throw _privateConstructorUsedError;
  String? get retainPopupOrderId => throw _privateConstructorUsedError;
  int? get retainPopupClassId => throw _privateConstructorUsedError;
  int? get retainPopupLinkId => throw _privateConstructorUsedError;
  int? get retainPopupSkuId => throw _privateConstructorUsedError;
  int? get retainPopupLinkType => throw _privateConstructorUsedError;
  int? get retainPopupCourseSegmentId => throw _privateConstructorUsedError;
  int? get orderId => throw _privateConstructorUsedError;
  int? get drainageCourseLinkType => throw _privateConstructorUsedError;
  bool? get directExit => throw _privateConstructorUsedError;
  bool? get lastSelected => throw _privateConstructorUsedError;
  @JsonKey(fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
  RecommendStyle? get recommendStyle => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseInfoModelCopyWith<CourseInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseInfoModelCopyWith<$Res> {
  factory $CourseInfoModelCopyWith(
          CourseInfoModel value, $Res Function(CourseInfoModel) then) =
      _$CourseInfoModelCopyWithImpl<$Res, CourseInfoModel>;
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      String? courseId,
      String? courseTag,
      int? courseTagKey,
      String? courseName,
      String? courseKey,
      int? classId,
      int? gradeKey,
      String? gradeTitle,
      String? courseDesc,
      String? tip,
      String? cornerMarkerText,
      String? btnText,
      String? skuId,
      String? skuName,
      String? experienceLink,
      String? linkId,
      int? courseStatus,
      String? topTips,
      String? topIcon,
      String? teacherImgUrl,
      String? learningUrl,
      String? learningStatus,
      String? entranceImage,
      String? entranceImageSmall,
      String? trainingCampTips,
      String? recommendText,
      String? recommendIcon,
      String? trainingCampPurchaseUrl,
      String? trainingCampBtn,
      String? trainingCampDescImgBeforeSelection,
      String? trainingCampDescImgSelected,
      String? monthCoursePurchaseUrl,
      String? monthCourseBtn,
      String? monthCourseDescImgBeforeSelection,
      String? monthCourseDescImgSelected,
      String? yearCoursePurchaseLink,
      String? yearCourseBtn,
      String? yearCourseDescImgBeforeSelection,
      String? yearCourseDescImgSelected,
      String? segmentDescImg,
      String? padTrainingCampBtn,
      String? padTrainingCampDescImgBeforeSelection,
      String? padTrainingCampDescImgSelected,
      String? padMonthCourseBtn,
      String? padMonthCourseDescImgBeforeSelection,
      String? padMonthCourseDescImgSelected,
      String? padYearCourseBtn,
      String? padYearCourseDescImgBeforeSelection,
      String? padYearCourseDescImgSelected,
      String? padRecommendIcon,
      String? padSegmentDescImg,
      List<CourseBtnModel>? receiveBtnInfos,
      List<LessonInfoModel>? lessonInfoList,
      List<CourseInfoModel>? trainingCampCourseInfoList,
      bool? showRetentionPopup,
      String? retainPopupTitle,
      String? retainPopupSubTitle,
      String? retainPopupImage,
      String? retainPopupBtnTxt,
      int? retainPopupCourseId,
      String? retainPopupCourseKey,
      int? retainPopupCourseLessonId,
      String? retainPopupCourseName,
      String? retainPopupOrderId,
      int? retainPopupClassId,
      int? retainPopupLinkId,
      int? retainPopupSkuId,
      int? retainPopupLinkType,
      int? retainPopupCourseSegmentId,
      int? orderId,
      int? drainageCourseLinkType,
      bool? directExit,
      bool? lastSelected,
      @JsonKey(
          fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
      RecommendStyle? recommendStyle});
}

/// @nodoc
class _$CourseInfoModelCopyWithImpl<$Res, $Val extends CourseInfoModel>
    implements $CourseInfoModelCopyWith<$Res> {
  _$CourseInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? courseName = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? courseDesc = freezed,
    Object? tip = freezed,
    Object? cornerMarkerText = freezed,
    Object? btnText = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? experienceLink = freezed,
    Object? linkId = freezed,
    Object? courseStatus = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? teacherImgUrl = freezed,
    Object? learningUrl = freezed,
    Object? learningStatus = freezed,
    Object? entranceImage = freezed,
    Object? entranceImageSmall = freezed,
    Object? trainingCampTips = freezed,
    Object? recommendText = freezed,
    Object? recommendIcon = freezed,
    Object? trainingCampPurchaseUrl = freezed,
    Object? trainingCampBtn = freezed,
    Object? trainingCampDescImgBeforeSelection = freezed,
    Object? trainingCampDescImgSelected = freezed,
    Object? monthCoursePurchaseUrl = freezed,
    Object? monthCourseBtn = freezed,
    Object? monthCourseDescImgBeforeSelection = freezed,
    Object? monthCourseDescImgSelected = freezed,
    Object? yearCoursePurchaseLink = freezed,
    Object? yearCourseBtn = freezed,
    Object? yearCourseDescImgBeforeSelection = freezed,
    Object? yearCourseDescImgSelected = freezed,
    Object? segmentDescImg = freezed,
    Object? padTrainingCampBtn = freezed,
    Object? padTrainingCampDescImgBeforeSelection = freezed,
    Object? padTrainingCampDescImgSelected = freezed,
    Object? padMonthCourseBtn = freezed,
    Object? padMonthCourseDescImgBeforeSelection = freezed,
    Object? padMonthCourseDescImgSelected = freezed,
    Object? padYearCourseBtn = freezed,
    Object? padYearCourseDescImgBeforeSelection = freezed,
    Object? padYearCourseDescImgSelected = freezed,
    Object? padRecommendIcon = freezed,
    Object? padSegmentDescImg = freezed,
    Object? receiveBtnInfos = freezed,
    Object? lessonInfoList = freezed,
    Object? trainingCampCourseInfoList = freezed,
    Object? showRetentionPopup = freezed,
    Object? retainPopupTitle = freezed,
    Object? retainPopupSubTitle = freezed,
    Object? retainPopupImage = freezed,
    Object? retainPopupBtnTxt = freezed,
    Object? retainPopupCourseId = freezed,
    Object? retainPopupCourseKey = freezed,
    Object? retainPopupCourseLessonId = freezed,
    Object? retainPopupCourseName = freezed,
    Object? retainPopupOrderId = freezed,
    Object? retainPopupClassId = freezed,
    Object? retainPopupLinkId = freezed,
    Object? retainPopupSkuId = freezed,
    Object? retainPopupLinkType = freezed,
    Object? retainPopupCourseSegmentId = freezed,
    Object? orderId = freezed,
    Object? drainageCourseLinkType = freezed,
    Object? directExit = freezed,
    Object? lastSelected = freezed,
    Object? recommendStyle = freezed,
  }) {
    return _then(_value.copyWith(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      cornerMarkerText: freezed == cornerMarkerText
          ? _value.cornerMarkerText
          : cornerMarkerText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceLink: freezed == experienceLink
          ? _value.experienceLink
          : experienceLink // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherImgUrl: freezed == teacherImgUrl
          ? _value.teacherImgUrl
          : teacherImgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learningUrl: freezed == learningUrl
          ? _value.learningUrl
          : learningUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learningStatus: freezed == learningStatus
          ? _value.learningStatus
          : learningStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImage: freezed == entranceImage
          ? _value.entranceImage
          : entranceImage // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImageSmall: freezed == entranceImageSmall
          ? _value.entranceImageSmall
          : entranceImageSmall // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampTips: freezed == trainingCampTips
          ? _value.trainingCampTips
          : trainingCampTips // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendText: freezed == recommendText
          ? _value.recommendText
          : recommendText // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendIcon: freezed == recommendIcon
          ? _value.recommendIcon
          : recommendIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampPurchaseUrl: freezed == trainingCampPurchaseUrl
          ? _value.trainingCampPurchaseUrl
          : trainingCampPurchaseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampBtn: freezed == trainingCampBtn
          ? _value.trainingCampBtn
          : trainingCampBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampDescImgBeforeSelection: freezed ==
              trainingCampDescImgBeforeSelection
          ? _value.trainingCampDescImgBeforeSelection
          : trainingCampDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampDescImgSelected: freezed == trainingCampDescImgSelected
          ? _value.trainingCampDescImgSelected
          : trainingCampDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCoursePurchaseUrl: freezed == monthCoursePurchaseUrl
          ? _value.monthCoursePurchaseUrl
          : monthCoursePurchaseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseBtn: freezed == monthCourseBtn
          ? _value.monthCourseBtn
          : monthCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseDescImgBeforeSelection: freezed ==
              monthCourseDescImgBeforeSelection
          ? _value.monthCourseDescImgBeforeSelection
          : monthCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseDescImgSelected: freezed == monthCourseDescImgSelected
          ? _value.monthCourseDescImgSelected
          : monthCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCoursePurchaseLink: freezed == yearCoursePurchaseLink
          ? _value.yearCoursePurchaseLink
          : yearCoursePurchaseLink // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseBtn: freezed == yearCourseBtn
          ? _value.yearCourseBtn
          : yearCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseDescImgBeforeSelection: freezed ==
              yearCourseDescImgBeforeSelection
          ? _value.yearCourseDescImgBeforeSelection
          : yearCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseDescImgSelected: freezed == yearCourseDescImgSelected
          ? _value.yearCourseDescImgSelected
          : yearCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentDescImg: freezed == segmentDescImg
          ? _value.segmentDescImg
          : segmentDescImg // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampBtn: freezed == padTrainingCampBtn
          ? _value.padTrainingCampBtn
          : padTrainingCampBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampDescImgBeforeSelection: freezed ==
              padTrainingCampDescImgBeforeSelection
          ? _value.padTrainingCampDescImgBeforeSelection
          : padTrainingCampDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampDescImgSelected: freezed == padTrainingCampDescImgSelected
          ? _value.padTrainingCampDescImgSelected
          : padTrainingCampDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseBtn: freezed == padMonthCourseBtn
          ? _value.padMonthCourseBtn
          : padMonthCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseDescImgBeforeSelection: freezed ==
              padMonthCourseDescImgBeforeSelection
          ? _value.padMonthCourseDescImgBeforeSelection
          : padMonthCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseDescImgSelected: freezed == padMonthCourseDescImgSelected
          ? _value.padMonthCourseDescImgSelected
          : padMonthCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseBtn: freezed == padYearCourseBtn
          ? _value.padYearCourseBtn
          : padYearCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseDescImgBeforeSelection: freezed ==
              padYearCourseDescImgBeforeSelection
          ? _value.padYearCourseDescImgBeforeSelection
          : padYearCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseDescImgSelected: freezed == padYearCourseDescImgSelected
          ? _value.padYearCourseDescImgSelected
          : padYearCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padRecommendIcon: freezed == padRecommendIcon
          ? _value.padRecommendIcon
          : padRecommendIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      padSegmentDescImg: freezed == padSegmentDescImg
          ? _value.padSegmentDescImg
          : padSegmentDescImg // ignore: cast_nullable_to_non_nullable
              as String?,
      receiveBtnInfos: freezed == receiveBtnInfos
          ? _value.receiveBtnInfos
          : receiveBtnInfos // ignore: cast_nullable_to_non_nullable
              as List<CourseBtnModel>?,
      lessonInfoList: freezed == lessonInfoList
          ? _value.lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfoModel>?,
      trainingCampCourseInfoList: freezed == trainingCampCourseInfoList
          ? _value.trainingCampCourseInfoList
          : trainingCampCourseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoModel>?,
      showRetentionPopup: freezed == showRetentionPopup
          ? _value.showRetentionPopup
          : showRetentionPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      retainPopupTitle: freezed == retainPopupTitle
          ? _value.retainPopupTitle
          : retainPopupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupSubTitle: freezed == retainPopupSubTitle
          ? _value.retainPopupSubTitle
          : retainPopupSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupImage: freezed == retainPopupImage
          ? _value.retainPopupImage
          : retainPopupImage // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupBtnTxt: freezed == retainPopupBtnTxt
          ? _value.retainPopupBtnTxt
          : retainPopupBtnTxt // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupCourseId: freezed == retainPopupCourseId
          ? _value.retainPopupCourseId
          : retainPopupCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseKey: freezed == retainPopupCourseKey
          ? _value.retainPopupCourseKey
          : retainPopupCourseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupCourseLessonId: freezed == retainPopupCourseLessonId
          ? _value.retainPopupCourseLessonId
          : retainPopupCourseLessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseName: freezed == retainPopupCourseName
          ? _value.retainPopupCourseName
          : retainPopupCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupOrderId: freezed == retainPopupOrderId
          ? _value.retainPopupOrderId
          : retainPopupOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupClassId: freezed == retainPopupClassId
          ? _value.retainPopupClassId
          : retainPopupClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupLinkId: freezed == retainPopupLinkId
          ? _value.retainPopupLinkId
          : retainPopupLinkId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupSkuId: freezed == retainPopupSkuId
          ? _value.retainPopupSkuId
          : retainPopupSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupLinkType: freezed == retainPopupLinkType
          ? _value.retainPopupLinkType
          : retainPopupLinkType // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseSegmentId: freezed == retainPopupCourseSegmentId
          ? _value.retainPopupCourseSegmentId
          : retainPopupCourseSegmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      drainageCourseLinkType: freezed == drainageCourseLinkType
          ? _value.drainageCourseLinkType
          : drainageCourseLinkType // ignore: cast_nullable_to_non_nullable
              as int?,
      directExit: freezed == directExit
          ? _value.directExit
          : directExit // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      recommendStyle: freezed == recommendStyle
          ? _value.recommendStyle
          : recommendStyle // ignore: cast_nullable_to_non_nullable
              as RecommendStyle?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseInfoModelCopyWith<$Res>
    implements $CourseInfoModelCopyWith<$Res> {
  factory _$$_CourseInfoModelCopyWith(
          _$_CourseInfoModel value, $Res Function(_$_CourseInfoModel) then) =
      __$$_CourseInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? selected,
      bool? skipped,
      String? courseId,
      String? courseTag,
      int? courseTagKey,
      String? courseName,
      String? courseKey,
      int? classId,
      int? gradeKey,
      String? gradeTitle,
      String? courseDesc,
      String? tip,
      String? cornerMarkerText,
      String? btnText,
      String? skuId,
      String? skuName,
      String? experienceLink,
      String? linkId,
      int? courseStatus,
      String? topTips,
      String? topIcon,
      String? teacherImgUrl,
      String? learningUrl,
      String? learningStatus,
      String? entranceImage,
      String? entranceImageSmall,
      String? trainingCampTips,
      String? recommendText,
      String? recommendIcon,
      String? trainingCampPurchaseUrl,
      String? trainingCampBtn,
      String? trainingCampDescImgBeforeSelection,
      String? trainingCampDescImgSelected,
      String? monthCoursePurchaseUrl,
      String? monthCourseBtn,
      String? monthCourseDescImgBeforeSelection,
      String? monthCourseDescImgSelected,
      String? yearCoursePurchaseLink,
      String? yearCourseBtn,
      String? yearCourseDescImgBeforeSelection,
      String? yearCourseDescImgSelected,
      String? segmentDescImg,
      String? padTrainingCampBtn,
      String? padTrainingCampDescImgBeforeSelection,
      String? padTrainingCampDescImgSelected,
      String? padMonthCourseBtn,
      String? padMonthCourseDescImgBeforeSelection,
      String? padMonthCourseDescImgSelected,
      String? padYearCourseBtn,
      String? padYearCourseDescImgBeforeSelection,
      String? padYearCourseDescImgSelected,
      String? padRecommendIcon,
      String? padSegmentDescImg,
      List<CourseBtnModel>? receiveBtnInfos,
      List<LessonInfoModel>? lessonInfoList,
      List<CourseInfoModel>? trainingCampCourseInfoList,
      bool? showRetentionPopup,
      String? retainPopupTitle,
      String? retainPopupSubTitle,
      String? retainPopupImage,
      String? retainPopupBtnTxt,
      int? retainPopupCourseId,
      String? retainPopupCourseKey,
      int? retainPopupCourseLessonId,
      String? retainPopupCourseName,
      String? retainPopupOrderId,
      int? retainPopupClassId,
      int? retainPopupLinkId,
      int? retainPopupSkuId,
      int? retainPopupLinkType,
      int? retainPopupCourseSegmentId,
      int? orderId,
      int? drainageCourseLinkType,
      bool? directExit,
      bool? lastSelected,
      @JsonKey(
          fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
      RecommendStyle? recommendStyle});
}

/// @nodoc
class __$$_CourseInfoModelCopyWithImpl<$Res>
    extends _$CourseInfoModelCopyWithImpl<$Res, _$_CourseInfoModel>
    implements _$$_CourseInfoModelCopyWith<$Res> {
  __$$_CourseInfoModelCopyWithImpl(
      _$_CourseInfoModel _value, $Res Function(_$_CourseInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selected = freezed,
    Object? skipped = freezed,
    Object? courseId = freezed,
    Object? courseTag = freezed,
    Object? courseTagKey = freezed,
    Object? courseName = freezed,
    Object? courseKey = freezed,
    Object? classId = freezed,
    Object? gradeKey = freezed,
    Object? gradeTitle = freezed,
    Object? courseDesc = freezed,
    Object? tip = freezed,
    Object? cornerMarkerText = freezed,
    Object? btnText = freezed,
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? experienceLink = freezed,
    Object? linkId = freezed,
    Object? courseStatus = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? teacherImgUrl = freezed,
    Object? learningUrl = freezed,
    Object? learningStatus = freezed,
    Object? entranceImage = freezed,
    Object? entranceImageSmall = freezed,
    Object? trainingCampTips = freezed,
    Object? recommendText = freezed,
    Object? recommendIcon = freezed,
    Object? trainingCampPurchaseUrl = freezed,
    Object? trainingCampBtn = freezed,
    Object? trainingCampDescImgBeforeSelection = freezed,
    Object? trainingCampDescImgSelected = freezed,
    Object? monthCoursePurchaseUrl = freezed,
    Object? monthCourseBtn = freezed,
    Object? monthCourseDescImgBeforeSelection = freezed,
    Object? monthCourseDescImgSelected = freezed,
    Object? yearCoursePurchaseLink = freezed,
    Object? yearCourseBtn = freezed,
    Object? yearCourseDescImgBeforeSelection = freezed,
    Object? yearCourseDescImgSelected = freezed,
    Object? segmentDescImg = freezed,
    Object? padTrainingCampBtn = freezed,
    Object? padTrainingCampDescImgBeforeSelection = freezed,
    Object? padTrainingCampDescImgSelected = freezed,
    Object? padMonthCourseBtn = freezed,
    Object? padMonthCourseDescImgBeforeSelection = freezed,
    Object? padMonthCourseDescImgSelected = freezed,
    Object? padYearCourseBtn = freezed,
    Object? padYearCourseDescImgBeforeSelection = freezed,
    Object? padYearCourseDescImgSelected = freezed,
    Object? padRecommendIcon = freezed,
    Object? padSegmentDescImg = freezed,
    Object? receiveBtnInfos = freezed,
    Object? lessonInfoList = freezed,
    Object? trainingCampCourseInfoList = freezed,
    Object? showRetentionPopup = freezed,
    Object? retainPopupTitle = freezed,
    Object? retainPopupSubTitle = freezed,
    Object? retainPopupImage = freezed,
    Object? retainPopupBtnTxt = freezed,
    Object? retainPopupCourseId = freezed,
    Object? retainPopupCourseKey = freezed,
    Object? retainPopupCourseLessonId = freezed,
    Object? retainPopupCourseName = freezed,
    Object? retainPopupOrderId = freezed,
    Object? retainPopupClassId = freezed,
    Object? retainPopupLinkId = freezed,
    Object? retainPopupSkuId = freezed,
    Object? retainPopupLinkType = freezed,
    Object? retainPopupCourseSegmentId = freezed,
    Object? orderId = freezed,
    Object? drainageCourseLinkType = freezed,
    Object? directExit = freezed,
    Object? lastSelected = freezed,
    Object? recommendStyle = freezed,
  }) {
    return _then(_$_CourseInfoModel(
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
      skipped: freezed == skipped
          ? _value.skipped
          : skipped // ignore: cast_nullable_to_non_nullable
              as bool?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTag: freezed == courseTag
          ? _value.courseTag
          : courseTag // ignore: cast_nullable_to_non_nullable
              as String?,
      courseTagKey: freezed == courseTagKey
          ? _value.courseTagKey
          : courseTagKey // ignore: cast_nullable_to_non_nullable
              as int?,
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      courseDesc: freezed == courseDesc
          ? _value.courseDesc
          : courseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      tip: freezed == tip
          ? _value.tip
          : tip // ignore: cast_nullable_to_non_nullable
              as String?,
      cornerMarkerText: freezed == cornerMarkerText
          ? _value.cornerMarkerText
          : cornerMarkerText // ignore: cast_nullable_to_non_nullable
              as String?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      experienceLink: freezed == experienceLink
          ? _value.experienceLink
          : experienceLink // ignore: cast_nullable_to_non_nullable
              as String?,
      linkId: freezed == linkId
          ? _value.linkId
          : linkId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      teacherImgUrl: freezed == teacherImgUrl
          ? _value.teacherImgUrl
          : teacherImgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learningUrl: freezed == learningUrl
          ? _value.learningUrl
          : learningUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      learningStatus: freezed == learningStatus
          ? _value.learningStatus
          : learningStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImage: freezed == entranceImage
          ? _value.entranceImage
          : entranceImage // ignore: cast_nullable_to_non_nullable
              as String?,
      entranceImageSmall: freezed == entranceImageSmall
          ? _value.entranceImageSmall
          : entranceImageSmall // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampTips: freezed == trainingCampTips
          ? _value.trainingCampTips
          : trainingCampTips // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendText: freezed == recommendText
          ? _value.recommendText
          : recommendText // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendIcon: freezed == recommendIcon
          ? _value.recommendIcon
          : recommendIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampPurchaseUrl: freezed == trainingCampPurchaseUrl
          ? _value.trainingCampPurchaseUrl
          : trainingCampPurchaseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampBtn: freezed == trainingCampBtn
          ? _value.trainingCampBtn
          : trainingCampBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampDescImgBeforeSelection: freezed ==
              trainingCampDescImgBeforeSelection
          ? _value.trainingCampDescImgBeforeSelection
          : trainingCampDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingCampDescImgSelected: freezed == trainingCampDescImgSelected
          ? _value.trainingCampDescImgSelected
          : trainingCampDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCoursePurchaseUrl: freezed == monthCoursePurchaseUrl
          ? _value.monthCoursePurchaseUrl
          : monthCoursePurchaseUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseBtn: freezed == monthCourseBtn
          ? _value.monthCourseBtn
          : monthCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseDescImgBeforeSelection: freezed ==
              monthCourseDescImgBeforeSelection
          ? _value.monthCourseDescImgBeforeSelection
          : monthCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      monthCourseDescImgSelected: freezed == monthCourseDescImgSelected
          ? _value.monthCourseDescImgSelected
          : monthCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCoursePurchaseLink: freezed == yearCoursePurchaseLink
          ? _value.yearCoursePurchaseLink
          : yearCoursePurchaseLink // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseBtn: freezed == yearCourseBtn
          ? _value.yearCourseBtn
          : yearCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseDescImgBeforeSelection: freezed ==
              yearCourseDescImgBeforeSelection
          ? _value.yearCourseDescImgBeforeSelection
          : yearCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      yearCourseDescImgSelected: freezed == yearCourseDescImgSelected
          ? _value.yearCourseDescImgSelected
          : yearCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentDescImg: freezed == segmentDescImg
          ? _value.segmentDescImg
          : segmentDescImg // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampBtn: freezed == padTrainingCampBtn
          ? _value.padTrainingCampBtn
          : padTrainingCampBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampDescImgBeforeSelection: freezed ==
              padTrainingCampDescImgBeforeSelection
          ? _value.padTrainingCampDescImgBeforeSelection
          : padTrainingCampDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padTrainingCampDescImgSelected: freezed == padTrainingCampDescImgSelected
          ? _value.padTrainingCampDescImgSelected
          : padTrainingCampDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseBtn: freezed == padMonthCourseBtn
          ? _value.padMonthCourseBtn
          : padMonthCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseDescImgBeforeSelection: freezed ==
              padMonthCourseDescImgBeforeSelection
          ? _value.padMonthCourseDescImgBeforeSelection
          : padMonthCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padMonthCourseDescImgSelected: freezed == padMonthCourseDescImgSelected
          ? _value.padMonthCourseDescImgSelected
          : padMonthCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseBtn: freezed == padYearCourseBtn
          ? _value.padYearCourseBtn
          : padYearCourseBtn // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseDescImgBeforeSelection: freezed ==
              padYearCourseDescImgBeforeSelection
          ? _value.padYearCourseDescImgBeforeSelection
          : padYearCourseDescImgBeforeSelection // ignore: cast_nullable_to_non_nullable
              as String?,
      padYearCourseDescImgSelected: freezed == padYearCourseDescImgSelected
          ? _value.padYearCourseDescImgSelected
          : padYearCourseDescImgSelected // ignore: cast_nullable_to_non_nullable
              as String?,
      padRecommendIcon: freezed == padRecommendIcon
          ? _value.padRecommendIcon
          : padRecommendIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      padSegmentDescImg: freezed == padSegmentDescImg
          ? _value.padSegmentDescImg
          : padSegmentDescImg // ignore: cast_nullable_to_non_nullable
              as String?,
      receiveBtnInfos: freezed == receiveBtnInfos
          ? _value._receiveBtnInfos
          : receiveBtnInfos // ignore: cast_nullable_to_non_nullable
              as List<CourseBtnModel>?,
      lessonInfoList: freezed == lessonInfoList
          ? _value._lessonInfoList
          : lessonInfoList // ignore: cast_nullable_to_non_nullable
              as List<LessonInfoModel>?,
      trainingCampCourseInfoList: freezed == trainingCampCourseInfoList
          ? _value._trainingCampCourseInfoList
          : trainingCampCourseInfoList // ignore: cast_nullable_to_non_nullable
              as List<CourseInfoModel>?,
      showRetentionPopup: freezed == showRetentionPopup
          ? _value.showRetentionPopup
          : showRetentionPopup // ignore: cast_nullable_to_non_nullable
              as bool?,
      retainPopupTitle: freezed == retainPopupTitle
          ? _value.retainPopupTitle
          : retainPopupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupSubTitle: freezed == retainPopupSubTitle
          ? _value.retainPopupSubTitle
          : retainPopupSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupImage: freezed == retainPopupImage
          ? _value.retainPopupImage
          : retainPopupImage // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupBtnTxt: freezed == retainPopupBtnTxt
          ? _value.retainPopupBtnTxt
          : retainPopupBtnTxt // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupCourseId: freezed == retainPopupCourseId
          ? _value.retainPopupCourseId
          : retainPopupCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseKey: freezed == retainPopupCourseKey
          ? _value.retainPopupCourseKey
          : retainPopupCourseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupCourseLessonId: freezed == retainPopupCourseLessonId
          ? _value.retainPopupCourseLessonId
          : retainPopupCourseLessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseName: freezed == retainPopupCourseName
          ? _value.retainPopupCourseName
          : retainPopupCourseName // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupOrderId: freezed == retainPopupOrderId
          ? _value.retainPopupOrderId
          : retainPopupOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      retainPopupClassId: freezed == retainPopupClassId
          ? _value.retainPopupClassId
          : retainPopupClassId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupLinkId: freezed == retainPopupLinkId
          ? _value.retainPopupLinkId
          : retainPopupLinkId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupSkuId: freezed == retainPopupSkuId
          ? _value.retainPopupSkuId
          : retainPopupSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupLinkType: freezed == retainPopupLinkType
          ? _value.retainPopupLinkType
          : retainPopupLinkType // ignore: cast_nullable_to_non_nullable
              as int?,
      retainPopupCourseSegmentId: freezed == retainPopupCourseSegmentId
          ? _value.retainPopupCourseSegmentId
          : retainPopupCourseSegmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as int?,
      drainageCourseLinkType: freezed == drainageCourseLinkType
          ? _value.drainageCourseLinkType
          : drainageCourseLinkType // ignore: cast_nullable_to_non_nullable
              as int?,
      directExit: freezed == directExit
          ? _value.directExit
          : directExit // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSelected: freezed == lastSelected
          ? _value.lastSelected
          : lastSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      recommendStyle: freezed == recommendStyle
          ? _value.recommendStyle
          : recommendStyle // ignore: cast_nullable_to_non_nullable
              as RecommendStyle?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseInfoModel implements _CourseInfoModel {
  const _$_CourseInfoModel(
      {this.selected,
      this.skipped,
      this.courseId,
      this.courseTag,
      this.courseTagKey,
      this.courseName,
      this.courseKey,
      this.classId,
      this.gradeKey,
      this.gradeTitle,
      this.courseDesc,
      this.tip,
      this.cornerMarkerText,
      this.btnText,
      this.skuId,
      this.skuName,
      this.experienceLink,
      this.linkId,
      this.courseStatus,
      this.topTips,
      this.topIcon,
      this.teacherImgUrl,
      this.learningUrl,
      this.learningStatus,
      this.entranceImage,
      this.entranceImageSmall,
      this.trainingCampTips,
      this.recommendText,
      this.recommendIcon,
      this.trainingCampPurchaseUrl,
      this.trainingCampBtn,
      this.trainingCampDescImgBeforeSelection,
      this.trainingCampDescImgSelected,
      this.monthCoursePurchaseUrl,
      this.monthCourseBtn,
      this.monthCourseDescImgBeforeSelection,
      this.monthCourseDescImgSelected,
      this.yearCoursePurchaseLink,
      this.yearCourseBtn,
      this.yearCourseDescImgBeforeSelection,
      this.yearCourseDescImgSelected,
      this.segmentDescImg,
      this.padTrainingCampBtn,
      this.padTrainingCampDescImgBeforeSelection,
      this.padTrainingCampDescImgSelected,
      this.padMonthCourseBtn,
      this.padMonthCourseDescImgBeforeSelection,
      this.padMonthCourseDescImgSelected,
      this.padYearCourseBtn,
      this.padYearCourseDescImgBeforeSelection,
      this.padYearCourseDescImgSelected,
      this.padRecommendIcon,
      this.padSegmentDescImg,
      final List<CourseBtnModel>? receiveBtnInfos,
      final List<LessonInfoModel>? lessonInfoList,
      final List<CourseInfoModel>? trainingCampCourseInfoList,
      this.showRetentionPopup,
      this.retainPopupTitle,
      this.retainPopupSubTitle,
      this.retainPopupImage,
      this.retainPopupBtnTxt,
      this.retainPopupCourseId,
      this.retainPopupCourseKey,
      this.retainPopupCourseLessonId,
      this.retainPopupCourseName,
      this.retainPopupOrderId,
      this.retainPopupClassId,
      this.retainPopupLinkId,
      this.retainPopupSkuId,
      this.retainPopupLinkType,
      this.retainPopupCourseSegmentId,
      this.orderId,
      this.drainageCourseLinkType,
      this.directExit,
      this.lastSelected,
      @JsonKey(
          fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
      this.recommendStyle})
      : _receiveBtnInfos = receiveBtnInfos,
        _lessonInfoList = lessonInfoList,
        _trainingCampCourseInfoList = trainingCampCourseInfoList;

  factory _$_CourseInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_CourseInfoModelFromJson(json);

  @override
  final bool? selected;
  @override
  final bool? skipped;
  @override
  final String? courseId;
  @override
  final String? courseTag;
  @override
  final int? courseTagKey;
  @override
  final String? courseName;
  @override
  final String? courseKey;
  @override
  final int? classId;
  @override
  final int? gradeKey;
  @override
  final String? gradeTitle;
  @override
  final String? courseDesc;
  @override
  final String? tip;
  @override
  final String? cornerMarkerText;
  @override
  final String? btnText;
  @override
  final String? skuId;
  @override
  final String? skuName;
  @override
  final String? experienceLink;
  @override
  final String? linkId;
  @override
  final int? courseStatus;
  @override
  final String? topTips;
  @override
  final String? topIcon;
  @override
  final String? teacherImgUrl;
  @override
  final String? learningUrl;
  @override
  final String? learningStatus;
  @override
  final String? entranceImage;
  @override
  final String? entranceImageSmall;
  @override
  final String? trainingCampTips;
  @override
  final String? recommendText;
//推荐文案
  @override
  final String? recommendIcon;
//推荐图标
  @override
  final String? trainingCampPurchaseUrl;
//训练营下单链接
  @override
  final String? trainingCampBtn;
//训练营下单按钮图片
  @override
  final String? trainingCampDescImgBeforeSelection;
//选中前
  @override
  final String? trainingCampDescImgSelected;
//选中后
  @override
  final String? monthCoursePurchaseUrl;
//月课下单链接
  @override
  final String? monthCourseBtn;
//月课下单按钮
  @override
  final String? monthCourseDescImgBeforeSelection;
//选中前
  @override
  final String? monthCourseDescImgSelected;
//选中后
  @override
  final String? yearCoursePurchaseLink;
//年课下单链接
  @override
  final String? yearCourseBtn;
//年课下单按钮
  @override
  final String? yearCourseDescImgBeforeSelection;
//选中前
  @override
  final String? yearCourseDescImgSelected;
//选中后
  @override
  final String? segmentDescImg;
//介绍信息
  @override
  final String? padTrainingCampBtn;
//pad训练营下单按钮
  @override
  final String? padTrainingCampDescImgBeforeSelection;
//pad选中前
  @override
  final String? padTrainingCampDescImgSelected;
//pad选中后
  @override
  final String? padMonthCourseBtn;
//pad月课下单按钮
  @override
  final String? padMonthCourseDescImgBeforeSelection;
//pad选中前
  @override
  final String? padMonthCourseDescImgSelected;
//pad选中后
  @override
  final String? padYearCourseBtn;
//pad年课下单按钮
  @override
  final String? padYearCourseDescImgBeforeSelection;
//pad选中前
  @override
  final String? padYearCourseDescImgSelected;
//pad选中后
  @override
  final String? padRecommendIcon;
// pad推荐图标
  @override
  final String? padSegmentDescImg;
//pad介绍信息
  final List<CourseBtnModel>? _receiveBtnInfos;
//pad介绍信息
  @override
  List<CourseBtnModel>? get receiveBtnInfos {
    final value = _receiveBtnInfos;
    if (value == null) return null;
    if (_receiveBtnInfos is EqualUnmodifiableListView) return _receiveBtnInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<LessonInfoModel>? _lessonInfoList;
  @override
  List<LessonInfoModel>? get lessonInfoList {
    final value = _lessonInfoList;
    if (value == null) return null;
    if (_lessonInfoList is EqualUnmodifiableListView) return _lessonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<CourseInfoModel>? _trainingCampCourseInfoList;
  @override
  List<CourseInfoModel>? get trainingCampCourseInfoList {
    final value = _trainingCampCourseInfoList;
    if (value == null) return null;
    if (_trainingCampCourseInfoList is EqualUnmodifiableListView)
      return _trainingCampCourseInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 挽留弹窗数据
  @override
  final bool? showRetentionPopup;
  @override
  final String? retainPopupTitle;
  @override
  final String? retainPopupSubTitle;
  @override
  final String? retainPopupImage;
  @override
  final String? retainPopupBtnTxt;
  @override
  final int? retainPopupCourseId;
  @override
  final String? retainPopupCourseKey;
  @override
  final int? retainPopupCourseLessonId;
  @override
  final String? retainPopupCourseName;
  @override
  final String? retainPopupOrderId;
  @override
  final int? retainPopupClassId;
  @override
  final int? retainPopupLinkId;
  @override
  final int? retainPopupSkuId;
  @override
  final int? retainPopupLinkType;
  @override
  final int? retainPopupCourseSegmentId;
  @override
  final int? orderId;
  @override
  final int? drainageCourseLinkType;
  @override
  final bool? directExit;
  @override
  final bool? lastSelected;
  @override
  @JsonKey(fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
  final RecommendStyle? recommendStyle;

  @override
  String toString() {
    return 'CourseInfoModel(selected: $selected, skipped: $skipped, courseId: $courseId, courseTag: $courseTag, courseTagKey: $courseTagKey, courseName: $courseName, courseKey: $courseKey, classId: $classId, gradeKey: $gradeKey, gradeTitle: $gradeTitle, courseDesc: $courseDesc, tip: $tip, cornerMarkerText: $cornerMarkerText, btnText: $btnText, skuId: $skuId, skuName: $skuName, experienceLink: $experienceLink, linkId: $linkId, courseStatus: $courseStatus, topTips: $topTips, topIcon: $topIcon, teacherImgUrl: $teacherImgUrl, learningUrl: $learningUrl, learningStatus: $learningStatus, entranceImage: $entranceImage, entranceImageSmall: $entranceImageSmall, trainingCampTips: $trainingCampTips, recommendText: $recommendText, recommendIcon: $recommendIcon, trainingCampPurchaseUrl: $trainingCampPurchaseUrl, trainingCampBtn: $trainingCampBtn, trainingCampDescImgBeforeSelection: $trainingCampDescImgBeforeSelection, trainingCampDescImgSelected: $trainingCampDescImgSelected, monthCoursePurchaseUrl: $monthCoursePurchaseUrl, monthCourseBtn: $monthCourseBtn, monthCourseDescImgBeforeSelection: $monthCourseDescImgBeforeSelection, monthCourseDescImgSelected: $monthCourseDescImgSelected, yearCoursePurchaseLink: $yearCoursePurchaseLink, yearCourseBtn: $yearCourseBtn, yearCourseDescImgBeforeSelection: $yearCourseDescImgBeforeSelection, yearCourseDescImgSelected: $yearCourseDescImgSelected, segmentDescImg: $segmentDescImg, padTrainingCampBtn: $padTrainingCampBtn, padTrainingCampDescImgBeforeSelection: $padTrainingCampDescImgBeforeSelection, padTrainingCampDescImgSelected: $padTrainingCampDescImgSelected, padMonthCourseBtn: $padMonthCourseBtn, padMonthCourseDescImgBeforeSelection: $padMonthCourseDescImgBeforeSelection, padMonthCourseDescImgSelected: $padMonthCourseDescImgSelected, padYearCourseBtn: $padYearCourseBtn, padYearCourseDescImgBeforeSelection: $padYearCourseDescImgBeforeSelection, padYearCourseDescImgSelected: $padYearCourseDescImgSelected, padRecommendIcon: $padRecommendIcon, padSegmentDescImg: $padSegmentDescImg, receiveBtnInfos: $receiveBtnInfos, lessonInfoList: $lessonInfoList, trainingCampCourseInfoList: $trainingCampCourseInfoList, showRetentionPopup: $showRetentionPopup, retainPopupTitle: $retainPopupTitle, retainPopupSubTitle: $retainPopupSubTitle, retainPopupImage: $retainPopupImage, retainPopupBtnTxt: $retainPopupBtnTxt, retainPopupCourseId: $retainPopupCourseId, retainPopupCourseKey: $retainPopupCourseKey, retainPopupCourseLessonId: $retainPopupCourseLessonId, retainPopupCourseName: $retainPopupCourseName, retainPopupOrderId: $retainPopupOrderId, retainPopupClassId: $retainPopupClassId, retainPopupLinkId: $retainPopupLinkId, retainPopupSkuId: $retainPopupSkuId, retainPopupLinkType: $retainPopupLinkType, retainPopupCourseSegmentId: $retainPopupCourseSegmentId, orderId: $orderId, drainageCourseLinkType: $drainageCourseLinkType, directExit: $directExit, lastSelected: $lastSelected, recommendStyle: $recommendStyle)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseInfoModel &&
            (identical(other.selected, selected) ||
                other.selected == selected) &&
            (identical(other.skipped, skipped) || other.skipped == skipped) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseTag, courseTag) ||
                other.courseTag == courseTag) &&
            (identical(other.courseTagKey, courseTagKey) ||
                other.courseTagKey == courseTagKey) &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.courseDesc, courseDesc) ||
                other.courseDesc == courseDesc) &&
            (identical(other.tip, tip) || other.tip == tip) &&
            (identical(other.cornerMarkerText, cornerMarkerText) ||
                other.cornerMarkerText == cornerMarkerText) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.experienceLink, experienceLink) ||
                other.experienceLink == experienceLink) &&
            (identical(other.linkId, linkId) || other.linkId == linkId) &&
            (identical(other.courseStatus, courseStatus) ||
                other.courseStatus == courseStatus) &&
            (identical(other.topTips, topTips) || other.topTips == topTips) &&
            (identical(other.topIcon, topIcon) || other.topIcon == topIcon) &&
            (identical(other.teacherImgUrl, teacherImgUrl) ||
                other.teacherImgUrl == teacherImgUrl) &&
            (identical(other.learningUrl, learningUrl) ||
                other.learningUrl == learningUrl) &&
            (identical(other.learningStatus, learningStatus) ||
                other.learningStatus == learningStatus) &&
            (identical(other.entranceImage, entranceImage) ||
                other.entranceImage == entranceImage) &&
            (identical(other.entranceImageSmall, entranceImageSmall) ||
                other.entranceImageSmall == entranceImageSmall) &&
            (identical(other.trainingCampTips, trainingCampTips) ||
                other.trainingCampTips == trainingCampTips) &&
            (identical(other.recommendText, recommendText) ||
                other.recommendText == recommendText) &&
            (identical(other.recommendIcon, recommendIcon) ||
                other.recommendIcon == recommendIcon) &&
            (identical(other.trainingCampPurchaseUrl, trainingCampPurchaseUrl) ||
                other.trainingCampPurchaseUrl == trainingCampPurchaseUrl) &&
            (identical(other.trainingCampBtn, trainingCampBtn) ||
                other.trainingCampBtn == trainingCampBtn) &&
            (identical(other.trainingCampDescImgBeforeSelection, trainingCampDescImgBeforeSelection) ||
                other.trainingCampDescImgBeforeSelection ==
                    trainingCampDescImgBeforeSelection) &&
            (identical(other.trainingCampDescImgSelected, trainingCampDescImgSelected) ||
                other.trainingCampDescImgSelected ==
                    trainingCampDescImgSelected) &&
            (identical(other.monthCoursePurchaseUrl, monthCoursePurchaseUrl) ||
                other.monthCoursePurchaseUrl == monthCoursePurchaseUrl) &&
            (identical(other.monthCourseBtn, monthCourseBtn) ||
                other.monthCourseBtn == monthCourseBtn) &&
            (identical(other.monthCourseDescImgBeforeSelection, monthCourseDescImgBeforeSelection) ||
                other.monthCourseDescImgBeforeSelection ==
                    monthCourseDescImgBeforeSelection) &&
            (identical(other.monthCourseDescImgSelected, monthCourseDescImgSelected) ||
                other.monthCourseDescImgSelected ==
                    monthCourseDescImgSelected) &&
            (identical(other.yearCoursePurchaseLink, yearCoursePurchaseLink) ||
                other.yearCoursePurchaseLink == yearCoursePurchaseLink) &&
            (identical(other.yearCourseBtn, yearCourseBtn) ||
                other.yearCourseBtn == yearCourseBtn) &&
            (identical(other.yearCourseDescImgBeforeSelection, yearCourseDescImgBeforeSelection) ||
                other.yearCourseDescImgBeforeSelection ==
                    yearCourseDescImgBeforeSelection) &&
            (identical(other.yearCourseDescImgSelected, yearCourseDescImgSelected) ||
                other.yearCourseDescImgSelected == yearCourseDescImgSelected) &&
            (identical(other.segmentDescImg, segmentDescImg) || other.segmentDescImg == segmentDescImg) &&
            (identical(other.padTrainingCampBtn, padTrainingCampBtn) || other.padTrainingCampBtn == padTrainingCampBtn) &&
            (identical(other.padTrainingCampDescImgBeforeSelection, padTrainingCampDescImgBeforeSelection) || other.padTrainingCampDescImgBeforeSelection == padTrainingCampDescImgBeforeSelection) &&
            (identical(other.padTrainingCampDescImgSelected, padTrainingCampDescImgSelected) || other.padTrainingCampDescImgSelected == padTrainingCampDescImgSelected) &&
            (identical(other.padMonthCourseBtn, padMonthCourseBtn) || other.padMonthCourseBtn == padMonthCourseBtn) &&
            (identical(other.padMonthCourseDescImgBeforeSelection, padMonthCourseDescImgBeforeSelection) || other.padMonthCourseDescImgBeforeSelection == padMonthCourseDescImgBeforeSelection) &&
            (identical(other.padMonthCourseDescImgSelected, padMonthCourseDescImgSelected) || other.padMonthCourseDescImgSelected == padMonthCourseDescImgSelected) &&
            (identical(other.padYearCourseBtn, padYearCourseBtn) || other.padYearCourseBtn == padYearCourseBtn) &&
            (identical(other.padYearCourseDescImgBeforeSelection, padYearCourseDescImgBeforeSelection) || other.padYearCourseDescImgBeforeSelection == padYearCourseDescImgBeforeSelection) &&
            (identical(other.padYearCourseDescImgSelected, padYearCourseDescImgSelected) || other.padYearCourseDescImgSelected == padYearCourseDescImgSelected) &&
            (identical(other.padRecommendIcon, padRecommendIcon) || other.padRecommendIcon == padRecommendIcon) &&
            (identical(other.padSegmentDescImg, padSegmentDescImg) || other.padSegmentDescImg == padSegmentDescImg) &&
            const DeepCollectionEquality().equals(other._receiveBtnInfos, _receiveBtnInfos) &&
            const DeepCollectionEquality().equals(other._lessonInfoList, _lessonInfoList) &&
            const DeepCollectionEquality().equals(other._trainingCampCourseInfoList, _trainingCampCourseInfoList) &&
            (identical(other.showRetentionPopup, showRetentionPopup) || other.showRetentionPopup == showRetentionPopup) &&
            (identical(other.retainPopupTitle, retainPopupTitle) || other.retainPopupTitle == retainPopupTitle) &&
            (identical(other.retainPopupSubTitle, retainPopupSubTitle) || other.retainPopupSubTitle == retainPopupSubTitle) &&
            (identical(other.retainPopupImage, retainPopupImage) || other.retainPopupImage == retainPopupImage) &&
            (identical(other.retainPopupBtnTxt, retainPopupBtnTxt) || other.retainPopupBtnTxt == retainPopupBtnTxt) &&
            (identical(other.retainPopupCourseId, retainPopupCourseId) || other.retainPopupCourseId == retainPopupCourseId) &&
            (identical(other.retainPopupCourseKey, retainPopupCourseKey) || other.retainPopupCourseKey == retainPopupCourseKey) &&
            (identical(other.retainPopupCourseLessonId, retainPopupCourseLessonId) || other.retainPopupCourseLessonId == retainPopupCourseLessonId) &&
            (identical(other.retainPopupCourseName, retainPopupCourseName) || other.retainPopupCourseName == retainPopupCourseName) &&
            (identical(other.retainPopupOrderId, retainPopupOrderId) || other.retainPopupOrderId == retainPopupOrderId) &&
            (identical(other.retainPopupClassId, retainPopupClassId) || other.retainPopupClassId == retainPopupClassId) &&
            (identical(other.retainPopupLinkId, retainPopupLinkId) || other.retainPopupLinkId == retainPopupLinkId) &&
            (identical(other.retainPopupSkuId, retainPopupSkuId) || other.retainPopupSkuId == retainPopupSkuId) &&
            (identical(other.retainPopupLinkType, retainPopupLinkType) || other.retainPopupLinkType == retainPopupLinkType) &&
            (identical(other.retainPopupCourseSegmentId, retainPopupCourseSegmentId) || other.retainPopupCourseSegmentId == retainPopupCourseSegmentId) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.drainageCourseLinkType, drainageCourseLinkType) || other.drainageCourseLinkType == drainageCourseLinkType) &&
            (identical(other.directExit, directExit) || other.directExit == directExit) &&
            (identical(other.lastSelected, lastSelected) || other.lastSelected == lastSelected) &&
            (identical(other.recommendStyle, recommendStyle) || other.recommendStyle == recommendStyle));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        selected,
        skipped,
        courseId,
        courseTag,
        courseTagKey,
        courseName,
        courseKey,
        classId,
        gradeKey,
        gradeTitle,
        courseDesc,
        tip,
        cornerMarkerText,
        btnText,
        skuId,
        skuName,
        experienceLink,
        linkId,
        courseStatus,
        topTips,
        topIcon,
        teacherImgUrl,
        learningUrl,
        learningStatus,
        entranceImage,
        entranceImageSmall,
        trainingCampTips,
        recommendText,
        recommendIcon,
        trainingCampPurchaseUrl,
        trainingCampBtn,
        trainingCampDescImgBeforeSelection,
        trainingCampDescImgSelected,
        monthCoursePurchaseUrl,
        monthCourseBtn,
        monthCourseDescImgBeforeSelection,
        monthCourseDescImgSelected,
        yearCoursePurchaseLink,
        yearCourseBtn,
        yearCourseDescImgBeforeSelection,
        yearCourseDescImgSelected,
        segmentDescImg,
        padTrainingCampBtn,
        padTrainingCampDescImgBeforeSelection,
        padTrainingCampDescImgSelected,
        padMonthCourseBtn,
        padMonthCourseDescImgBeforeSelection,
        padMonthCourseDescImgSelected,
        padYearCourseBtn,
        padYearCourseDescImgBeforeSelection,
        padYearCourseDescImgSelected,
        padRecommendIcon,
        padSegmentDescImg,
        const DeepCollectionEquality().hash(_receiveBtnInfos),
        const DeepCollectionEquality().hash(_lessonInfoList),
        const DeepCollectionEquality().hash(_trainingCampCourseInfoList),
        showRetentionPopup,
        retainPopupTitle,
        retainPopupSubTitle,
        retainPopupImage,
        retainPopupBtnTxt,
        retainPopupCourseId,
        retainPopupCourseKey,
        retainPopupCourseLessonId,
        retainPopupCourseName,
        retainPopupOrderId,
        retainPopupClassId,
        retainPopupLinkId,
        retainPopupSkuId,
        retainPopupLinkType,
        retainPopupCourseSegmentId,
        orderId,
        drainageCourseLinkType,
        directExit,
        lastSelected,
        recommendStyle
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseInfoModelCopyWith<_$_CourseInfoModel> get copyWith =>
      __$$_CourseInfoModelCopyWithImpl<_$_CourseInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseInfoModelToJson(
      this,
    );
  }
}

abstract class _CourseInfoModel implements CourseInfoModel {
  const factory _CourseInfoModel(
      {final bool? selected,
      final bool? skipped,
      final String? courseId,
      final String? courseTag,
      final int? courseTagKey,
      final String? courseName,
      final String? courseKey,
      final int? classId,
      final int? gradeKey,
      final String? gradeTitle,
      final String? courseDesc,
      final String? tip,
      final String? cornerMarkerText,
      final String? btnText,
      final String? skuId,
      final String? skuName,
      final String? experienceLink,
      final String? linkId,
      final int? courseStatus,
      final String? topTips,
      final String? topIcon,
      final String? teacherImgUrl,
      final String? learningUrl,
      final String? learningStatus,
      final String? entranceImage,
      final String? entranceImageSmall,
      final String? trainingCampTips,
      final String? recommendText,
      final String? recommendIcon,
      final String? trainingCampPurchaseUrl,
      final String? trainingCampBtn,
      final String? trainingCampDescImgBeforeSelection,
      final String? trainingCampDescImgSelected,
      final String? monthCoursePurchaseUrl,
      final String? monthCourseBtn,
      final String? monthCourseDescImgBeforeSelection,
      final String? monthCourseDescImgSelected,
      final String? yearCoursePurchaseLink,
      final String? yearCourseBtn,
      final String? yearCourseDescImgBeforeSelection,
      final String? yearCourseDescImgSelected,
      final String? segmentDescImg,
      final String? padTrainingCampBtn,
      final String? padTrainingCampDescImgBeforeSelection,
      final String? padTrainingCampDescImgSelected,
      final String? padMonthCourseBtn,
      final String? padMonthCourseDescImgBeforeSelection,
      final String? padMonthCourseDescImgSelected,
      final String? padYearCourseBtn,
      final String? padYearCourseDescImgBeforeSelection,
      final String? padYearCourseDescImgSelected,
      final String? padRecommendIcon,
      final String? padSegmentDescImg,
      final List<CourseBtnModel>? receiveBtnInfos,
      final List<LessonInfoModel>? lessonInfoList,
      final List<CourseInfoModel>? trainingCampCourseInfoList,
      final bool? showRetentionPopup,
      final String? retainPopupTitle,
      final String? retainPopupSubTitle,
      final String? retainPopupImage,
      final String? retainPopupBtnTxt,
      final int? retainPopupCourseId,
      final String? retainPopupCourseKey,
      final int? retainPopupCourseLessonId,
      final String? retainPopupCourseName,
      final String? retainPopupOrderId,
      final int? retainPopupClassId,
      final int? retainPopupLinkId,
      final int? retainPopupSkuId,
      final int? retainPopupLinkType,
      final int? retainPopupCourseSegmentId,
      final int? orderId,
      final int? drainageCourseLinkType,
      final bool? directExit,
      final bool? lastSelected,
      @JsonKey(
          fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
      final RecommendStyle? recommendStyle}) = _$_CourseInfoModel;

  factory _CourseInfoModel.fromJson(Map<String, dynamic> json) =
      _$_CourseInfoModel.fromJson;

  @override
  bool? get selected;
  @override
  bool? get skipped;
  @override
  String? get courseId;
  @override
  String? get courseTag;
  @override
  int? get courseTagKey;
  @override
  String? get courseName;
  @override
  String? get courseKey;
  @override
  int? get classId;
  @override
  int? get gradeKey;
  @override
  String? get gradeTitle;
  @override
  String? get courseDesc;
  @override
  String? get tip;
  @override
  String? get cornerMarkerText;
  @override
  String? get btnText;
  @override
  String? get skuId;
  @override
  String? get skuName;
  @override
  String? get experienceLink;
  @override
  String? get linkId;
  @override
  int? get courseStatus;
  @override
  String? get topTips;
  @override
  String? get topIcon;
  @override
  String? get teacherImgUrl;
  @override
  String? get learningUrl;
  @override
  String? get learningStatus;
  @override
  String? get entranceImage;
  @override
  String? get entranceImageSmall;
  @override
  String? get trainingCampTips;
  @override
  String? get recommendText;
  @override //推荐文案
  String? get recommendIcon;
  @override //推荐图标
  String? get trainingCampPurchaseUrl;
  @override //训练营下单链接
  String? get trainingCampBtn;
  @override //训练营下单按钮图片
  String? get trainingCampDescImgBeforeSelection;
  @override //选中前
  String? get trainingCampDescImgSelected;
  @override //选中后
  String? get monthCoursePurchaseUrl;
  @override //月课下单链接
  String? get monthCourseBtn;
  @override //月课下单按钮
  String? get monthCourseDescImgBeforeSelection;
  @override //选中前
  String? get monthCourseDescImgSelected;
  @override //选中后
  String? get yearCoursePurchaseLink;
  @override //年课下单链接
  String? get yearCourseBtn;
  @override //年课下单按钮
  String? get yearCourseDescImgBeforeSelection;
  @override //选中前
  String? get yearCourseDescImgSelected;
  @override //选中后
  String? get segmentDescImg;
  @override //介绍信息
  String? get padTrainingCampBtn;
  @override //pad训练营下单按钮
  String? get padTrainingCampDescImgBeforeSelection;
  @override //pad选中前
  String? get padTrainingCampDescImgSelected;
  @override //pad选中后
  String? get padMonthCourseBtn;
  @override //pad月课下单按钮
  String? get padMonthCourseDescImgBeforeSelection;
  @override //pad选中前
  String? get padMonthCourseDescImgSelected;
  @override //pad选中后
  String? get padYearCourseBtn;
  @override //pad年课下单按钮
  String? get padYearCourseDescImgBeforeSelection;
  @override //pad选中前
  String? get padYearCourseDescImgSelected;
  @override //pad选中后
  String? get padRecommendIcon;
  @override // pad推荐图标
  String? get padSegmentDescImg;
  @override //pad介绍信息
  List<CourseBtnModel>? get receiveBtnInfos;
  @override
  List<LessonInfoModel>? get lessonInfoList;
  @override
  List<CourseInfoModel>? get trainingCampCourseInfoList;
  @override // 挽留弹窗数据
  bool? get showRetentionPopup;
  @override
  String? get retainPopupTitle;
  @override
  String? get retainPopupSubTitle;
  @override
  String? get retainPopupImage;
  @override
  String? get retainPopupBtnTxt;
  @override
  int? get retainPopupCourseId;
  @override
  String? get retainPopupCourseKey;
  @override
  int? get retainPopupCourseLessonId;
  @override
  String? get retainPopupCourseName;
  @override
  String? get retainPopupOrderId;
  @override
  int? get retainPopupClassId;
  @override
  int? get retainPopupLinkId;
  @override
  int? get retainPopupSkuId;
  @override
  int? get retainPopupLinkType;
  @override
  int? get retainPopupCourseSegmentId;
  @override
  int? get orderId;
  @override
  int? get drainageCourseLinkType;
  @override
  bool? get directExit;
  @override
  bool? get lastSelected;
  @override
  @JsonKey(fromJson: RecommendStyle.fromString, toJson: RecommendStyle.toJson)
  RecommendStyle? get recommendStyle;
  @override
  @JsonKey(ignore: true)
  _$$_CourseInfoModelCopyWith<_$_CourseInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonInfoModel _$LessonInfoModelFromJson(Map<String, dynamic> json) {
  return _LessonInfoModel.fromJson(json);
}

/// @nodoc
mixin _$LessonInfoModel {
  String? get lessonId => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String? get lessonDesc => throw _privateConstructorUsedError;
  String? get lessonCoverUrl => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonInfoModelCopyWith<LessonInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonInfoModelCopyWith<$Res> {
  factory $LessonInfoModelCopyWith(
          LessonInfoModel value, $Res Function(LessonInfoModel) then) =
      _$LessonInfoModelCopyWithImpl<$Res, LessonInfoModel>;
  @useResult
  $Res call(
      {String? lessonId,
      String? lessonName,
      String? lessonDesc,
      String? lessonCoverUrl,
      int? segmentId});
}

/// @nodoc
class _$LessonInfoModelCopyWithImpl<$Res, $Val extends LessonInfoModel>
    implements $LessonInfoModelCopyWith<$Res> {
  _$LessonInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonDesc = freezed,
    Object? lessonCoverUrl = freezed,
    Object? segmentId = freezed,
  }) {
    return _then(_value.copyWith(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonDesc: freezed == lessonDesc
          ? _value.lessonDesc
          : lessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverUrl: freezed == lessonCoverUrl
          ? _value.lessonCoverUrl
          : lessonCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonInfoModelCopyWith<$Res>
    implements $LessonInfoModelCopyWith<$Res> {
  factory _$$_LessonInfoModelCopyWith(
          _$_LessonInfoModel value, $Res Function(_$_LessonInfoModel) then) =
      __$$_LessonInfoModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? lessonId,
      String? lessonName,
      String? lessonDesc,
      String? lessonCoverUrl,
      int? segmentId});
}

/// @nodoc
class __$$_LessonInfoModelCopyWithImpl<$Res>
    extends _$LessonInfoModelCopyWithImpl<$Res, _$_LessonInfoModel>
    implements _$$_LessonInfoModelCopyWith<$Res> {
  __$$_LessonInfoModelCopyWithImpl(
      _$_LessonInfoModel _value, $Res Function(_$_LessonInfoModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = freezed,
    Object? lessonName = freezed,
    Object? lessonDesc = freezed,
    Object? lessonCoverUrl = freezed,
    Object? segmentId = freezed,
  }) {
    return _then(_$_LessonInfoModel(
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonDesc: freezed == lessonDesc
          ? _value.lessonDesc
          : lessonDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverUrl: freezed == lessonCoverUrl
          ? _value.lessonCoverUrl
          : lessonCoverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonInfoModel implements _LessonInfoModel {
  const _$_LessonInfoModel(
      {this.lessonId,
      this.lessonName,
      this.lessonDesc,
      this.lessonCoverUrl,
      this.segmentId});

  factory _$_LessonInfoModel.fromJson(Map<String, dynamic> json) =>
      _$$_LessonInfoModelFromJson(json);

  @override
  final String? lessonId;
  @override
  final String? lessonName;
  @override
  final String? lessonDesc;
  @override
  final String? lessonCoverUrl;
  @override
  final int? segmentId;

  @override
  String toString() {
    return 'LessonInfoModel(lessonId: $lessonId, lessonName: $lessonName, lessonDesc: $lessonDesc, lessonCoverUrl: $lessonCoverUrl, segmentId: $segmentId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonInfoModel &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonDesc, lessonDesc) ||
                other.lessonDesc == lessonDesc) &&
            (identical(other.lessonCoverUrl, lessonCoverUrl) ||
                other.lessonCoverUrl == lessonCoverUrl) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, lessonId, lessonName, lessonDesc, lessonCoverUrl, segmentId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonInfoModelCopyWith<_$_LessonInfoModel> get copyWith =>
      __$$_LessonInfoModelCopyWithImpl<_$_LessonInfoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonInfoModelToJson(
      this,
    );
  }
}

abstract class _LessonInfoModel implements LessonInfoModel {
  const factory _LessonInfoModel(
      {final String? lessonId,
      final String? lessonName,
      final String? lessonDesc,
      final String? lessonCoverUrl,
      final int? segmentId}) = _$_LessonInfoModel;

  factory _LessonInfoModel.fromJson(Map<String, dynamic> json) =
      _$_LessonInfoModel.fromJson;

  @override
  String? get lessonId;
  @override
  String? get lessonName;
  @override
  String? get lessonDesc;
  @override
  String? get lessonCoverUrl;
  @override
  int? get segmentId;
  @override
  @JsonKey(ignore: true)
  _$$_LessonInfoModelCopyWith<_$_LessonInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseBtnModel _$CourseBtnModelFromJson(Map<String, dynamic> json) {
  return _CourseBtnModel.fromJson(json);
}

/// @nodoc
mixin _$CourseBtnModel {
  int? get type => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get router => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseBtnModelCopyWith<CourseBtnModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseBtnModelCopyWith<$Res> {
  factory $CourseBtnModelCopyWith(
          CourseBtnModel value, $Res Function(CourseBtnModel) then) =
      _$CourseBtnModelCopyWithImpl<$Res, CourseBtnModel>;
  @useResult
  $Res call({int? type, String? content, String? router});
}

/// @nodoc
class _$CourseBtnModelCopyWithImpl<$Res, $Val extends CourseBtnModel>
    implements $CourseBtnModelCopyWith<$Res> {
  _$CourseBtnModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? content = freezed,
    Object? router = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseBtnModelCopyWith<$Res>
    implements $CourseBtnModelCopyWith<$Res> {
  factory _$$_CourseBtnModelCopyWith(
          _$_CourseBtnModel value, $Res Function(_$_CourseBtnModel) then) =
      __$$_CourseBtnModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? content, String? router});
}

/// @nodoc
class __$$_CourseBtnModelCopyWithImpl<$Res>
    extends _$CourseBtnModelCopyWithImpl<$Res, _$_CourseBtnModel>
    implements _$$_CourseBtnModelCopyWith<$Res> {
  __$$_CourseBtnModelCopyWithImpl(
      _$_CourseBtnModel _value, $Res Function(_$_CourseBtnModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? content = freezed,
    Object? router = freezed,
  }) {
    return _then(_$_CourseBtnModel(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseBtnModel implements _CourseBtnModel {
  const _$_CourseBtnModel({this.type, this.content, this.router});

  factory _$_CourseBtnModel.fromJson(Map<String, dynamic> json) =>
      _$$_CourseBtnModelFromJson(json);

  @override
  final int? type;
  @override
  final String? content;
  @override
  final String? router;

  @override
  String toString() {
    return 'CourseBtnModel(type: $type, content: $content, router: $router)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseBtnModel &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.router, router) || other.router == router));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, content, router);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseBtnModelCopyWith<_$_CourseBtnModel> get copyWith =>
      __$$_CourseBtnModelCopyWithImpl<_$_CourseBtnModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseBtnModelToJson(
      this,
    );
  }
}

abstract class _CourseBtnModel implements CourseBtnModel {
  const factory _CourseBtnModel(
      {final int? type,
      final String? content,
      final String? router}) = _$_CourseBtnModel;

  factory _CourseBtnModel.fromJson(Map<String, dynamic> json) =
      _$_CourseBtnModel.fromJson;

  @override
  int? get type;
  @override
  String? get content;
  @override
  String? get router;
  @override
  @JsonKey(ignore: true)
  _$$_CourseBtnModelCopyWith<_$_CourseBtnModel> get copyWith =>
      throw _privateConstructorUsedError;
}
