// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enjoy_course_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_EnjoyDrainageModel _$$_EnjoyDrainageModelFromJson(
        Map<String, dynamic> json) =>
    _$_EnjoyDrainageModel(
      hasCourse: json['hasCourse'] as bool?,
      cmsTasks: (json['cmsTasks'] as List<dynamic>?)
          ?.map((e) => EnjoyCourseTaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_EnjoyDrainageModelToJson(
        _$_EnjoyDrainageModel instance) =>
    <String, dynamic>{
      'hasCourse': instance.hasCourse,
      'cmsTasks': instance.cmsTasks,
    };

_$_EnjoyCourseTaskModel _$$_EnjoyCourseTaskModelFromJson(
        Map<String, dynamic> json) =>
    _$_EnjoyCourseTaskModel(
      taskStatus: json['taskStatus'] as int?,
      materialId: json['materialId'] as int?,
      materialName: json['materialName'] as String?,
      configId: json['configId'] as int?,
      taskStatusDesc: json['taskStatusDesc'] as String?,
      showDateTime: json['showDateTime'] as int?,
      startClassTime: json['startClassTime'] as int?,
      firstLessonStartTime: json['firstLessonStartTime'] as int?,
      joinClassTime: json['joinClassTime'] as int?,
      taskType: json['taskType'] as int?,
      classId: json['classId'] as int?,
      scheduleTaskId: json['scheduleTaskId'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      lessonId: json['lessonId'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      segmentId: json['segmentId'] as int?,
      weekId: json['weekId'] as int?,
      unlockType: json['unlockType'] as int?,
      title: json['title'] as String?,
      subTitle: json['subTitle'] as String?,
      route: json['route'] as String?,
      icon: json['icon'] as String?,
      lessonLabel: json['lessonLabel'] as String?,
      lessonOrder: json['lessonOrder'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonCoverImage: json['lessonCoverImage'] as String?,
      image: json['image'] as String?,
      courseType: json['courseType'] as int?,
      courseSegmentName: json['courseSegmentName'] as String?,
      userCourseBusinessStatus: json['userCourseBusinessStatus'] as String?,
      newGetFlag: json['newGetFlag'] as bool?,
      btnText: json['btnText'] as String?,
      pictureUrl: json['pictureUrl'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      linkUrl: json['linkUrl'] as String?,
      missionMaterialType: json['missionMaterialType'] as String?,
      missionBusinessTypeName: json['missionBusinessTypeName'] as String?,
      expired: json['expired'] as bool?,
      description: json['description'] as String?,
      titleColor: json['titleColor'] as String?,
      descriptionColor: json['descriptionColor'] as String?,
      missionMaterialTypeName: json['missionMaterialTypeName'] as String?,
      missionTypeName: json['missionTypeName'] as String?,
      businessTagId: json['businessTagId'] as String?,
      topTips: json['topTips'] as String?,
      topIcon: json['topIcon'] as String?,
      gradeTitle: json['gradeTitle'] as String?,
      gradeKey: json['gradeKey'] as int?,
      lastDrainageCourseLessonTask:
          json['lastDrainageCourseLessonTask'] as bool?,
      needPop: json['needPop'] as bool?,
      buttonDescription: json['buttonDescription'] as String?,
      landscapePictureUrl: json['landscapePictureUrl'] as String?,
      auditId: json['auditId'] as int?,
      drainageCourseConfigId: json['drainageCourseConfigId'] as int?,
      channelNo: json['channelNo'] as String?,
      subjectInfoList:
          (json['drainageCourseReceiveCardDataBo'] as List<dynamic>?)
              ?.map((e) => SubjectInfoModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$_EnjoyCourseTaskModelToJson(
        _$_EnjoyCourseTaskModel instance) =>
    <String, dynamic>{
      'taskStatus': instance.taskStatus,
      'materialId': instance.materialId,
      'materialName': instance.materialName,
      'configId': instance.configId,
      'taskStatusDesc': instance.taskStatusDesc,
      'showDateTime': instance.showDateTime,
      'startClassTime': instance.startClassTime,
      'firstLessonStartTime': instance.firstLessonStartTime,
      'joinClassTime': instance.joinClassTime,
      'taskType': instance.taskType,
      'classId': instance.classId,
      'scheduleTaskId': instance.scheduleTaskId,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'lessonId': instance.lessonId,
      'subjectType': instance.subjectType,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'segmentId': instance.segmentId,
      'weekId': instance.weekId,
      'unlockType': instance.unlockType,
      'title': instance.title,
      'subTitle': instance.subTitle,
      'route': instance.route,
      'icon': instance.icon,
      'lessonLabel': instance.lessonLabel,
      'lessonOrder': instance.lessonOrder,
      'lessonName': instance.lessonName,
      'lessonCoverImage': instance.lessonCoverImage,
      'image': instance.image,
      'courseType': instance.courseType,
      'courseSegmentName': instance.courseSegmentName,
      'userCourseBusinessStatus': instance.userCourseBusinessStatus,
      'newGetFlag': instance.newGetFlag,
      'btnText': instance.btnText,
      'pictureUrl': instance.pictureUrl,
      'backgroundColor': instance.backgroundColor,
      'linkUrl': instance.linkUrl,
      'missionMaterialType': instance.missionMaterialType,
      'missionBusinessTypeName': instance.missionBusinessTypeName,
      'expired': instance.expired,
      'description': instance.description,
      'titleColor': instance.titleColor,
      'descriptionColor': instance.descriptionColor,
      'missionMaterialTypeName': instance.missionMaterialTypeName,
      'missionTypeName': instance.missionTypeName,
      'businessTagId': instance.businessTagId,
      'topTips': instance.topTips,
      'topIcon': instance.topIcon,
      'gradeTitle': instance.gradeTitle,
      'gradeKey': instance.gradeKey,
      'lastDrainageCourseLessonTask': instance.lastDrainageCourseLessonTask,
      'needPop': instance.needPop,
      'buttonDescription': instance.buttonDescription,
      'landscapePictureUrl': instance.landscapePictureUrl,
      'auditId': instance.auditId,
      'drainageCourseConfigId': instance.drainageCourseConfigId,
      'channelNo': instance.channelNo,
      'drainageCourseReceiveCardDataBo': instance.subjectInfoList,
    };
