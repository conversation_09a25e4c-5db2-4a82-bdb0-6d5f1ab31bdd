// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enjoy_course_task_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

EnjoyDrainageModel _$EnjoyDrainageModelFromJson(Map<String, dynamic> json) {
  return _EnjoyDrainageModel.fromJson(json);
}

/// @nodoc
mixin _$EnjoyDrainageModel {
  bool? get hasCourse => throw _privateConstructorUsedError;
  List<EnjoyCourseTaskModel>? get cmsTasks =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EnjoyDrainageModelCopyWith<EnjoyDrainageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnjoyDrainageModelCopyWith<$Res> {
  factory $EnjoyDrainageModelCopyWith(
          EnjoyDrainageModel value, $Res Function(EnjoyDrainageModel) then) =
      _$EnjoyDrainageModelCopyWithImpl<$Res, EnjoyDrainageModel>;
  @useResult
  $Res call({bool? hasCourse, List<EnjoyCourseTaskModel>? cmsTasks});
}

/// @nodoc
class _$EnjoyDrainageModelCopyWithImpl<$Res, $Val extends EnjoyDrainageModel>
    implements $EnjoyDrainageModelCopyWith<$Res> {
  _$EnjoyDrainageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCourse = freezed,
    Object? cmsTasks = freezed,
  }) {
    return _then(_value.copyWith(
      hasCourse: freezed == hasCourse
          ? _value.hasCourse
          : hasCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
      cmsTasks: freezed == cmsTasks
          ? _value.cmsTasks
          : cmsTasks // ignore: cast_nullable_to_non_nullable
              as List<EnjoyCourseTaskModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EnjoyDrainageModelCopyWith<$Res>
    implements $EnjoyDrainageModelCopyWith<$Res> {
  factory _$$_EnjoyDrainageModelCopyWith(_$_EnjoyDrainageModel value,
          $Res Function(_$_EnjoyDrainageModel) then) =
      __$$_EnjoyDrainageModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? hasCourse, List<EnjoyCourseTaskModel>? cmsTasks});
}

/// @nodoc
class __$$_EnjoyDrainageModelCopyWithImpl<$Res>
    extends _$EnjoyDrainageModelCopyWithImpl<$Res, _$_EnjoyDrainageModel>
    implements _$$_EnjoyDrainageModelCopyWith<$Res> {
  __$$_EnjoyDrainageModelCopyWithImpl(
      _$_EnjoyDrainageModel _value, $Res Function(_$_EnjoyDrainageModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCourse = freezed,
    Object? cmsTasks = freezed,
  }) {
    return _then(_$_EnjoyDrainageModel(
      hasCourse: freezed == hasCourse
          ? _value.hasCourse
          : hasCourse // ignore: cast_nullable_to_non_nullable
              as bool?,
      cmsTasks: freezed == cmsTasks
          ? _value._cmsTasks
          : cmsTasks // ignore: cast_nullable_to_non_nullable
              as List<EnjoyCourseTaskModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EnjoyDrainageModel implements _EnjoyDrainageModel {
  const _$_EnjoyDrainageModel(
      {this.hasCourse, final List<EnjoyCourseTaskModel>? cmsTasks})
      : _cmsTasks = cmsTasks;

  factory _$_EnjoyDrainageModel.fromJson(Map<String, dynamic> json) =>
      _$$_EnjoyDrainageModelFromJson(json);

  @override
  final bool? hasCourse;
  final List<EnjoyCourseTaskModel>? _cmsTasks;
  @override
  List<EnjoyCourseTaskModel>? get cmsTasks {
    final value = _cmsTasks;
    if (value == null) return null;
    if (_cmsTasks is EqualUnmodifiableListView) return _cmsTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'EnjoyDrainageModel(hasCourse: $hasCourse, cmsTasks: $cmsTasks)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EnjoyDrainageModel &&
            (identical(other.hasCourse, hasCourse) ||
                other.hasCourse == hasCourse) &&
            const DeepCollectionEquality().equals(other._cmsTasks, _cmsTasks));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, hasCourse, const DeepCollectionEquality().hash(_cmsTasks));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EnjoyDrainageModelCopyWith<_$_EnjoyDrainageModel> get copyWith =>
      __$$_EnjoyDrainageModelCopyWithImpl<_$_EnjoyDrainageModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EnjoyDrainageModelToJson(
      this,
    );
  }
}

abstract class _EnjoyDrainageModel implements EnjoyDrainageModel {
  const factory _EnjoyDrainageModel(
      {final bool? hasCourse,
      final List<EnjoyCourseTaskModel>? cmsTasks}) = _$_EnjoyDrainageModel;

  factory _EnjoyDrainageModel.fromJson(Map<String, dynamic> json) =
      _$_EnjoyDrainageModel.fromJson;

  @override
  bool? get hasCourse;
  @override
  List<EnjoyCourseTaskModel>? get cmsTasks;
  @override
  @JsonKey(ignore: true)
  _$$_EnjoyDrainageModelCopyWith<_$_EnjoyDrainageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

EnjoyCourseTaskModel _$EnjoyCourseTaskModelFromJson(Map<String, dynamic> json) {
  return _EnjoyCourseTaskModel.fromJson(json);
}

/// @nodoc
mixin _$EnjoyCourseTaskModel {
  int? get taskStatus => throw _privateConstructorUsedError;
  int? get materialId => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  int? get configId => throw _privateConstructorUsedError;
  String? get taskStatusDesc => throw _privateConstructorUsedError;
  int? get showDateTime => throw _privateConstructorUsedError;
  int? get startClassTime => throw _privateConstructorUsedError;
  int? get firstLessonStartTime => throw _privateConstructorUsedError;
  int? get joinClassTime => throw _privateConstructorUsedError;
  int? get taskType => throw _privateConstructorUsedError;
  int? get classId => throw _privateConstructorUsedError;
  String? get scheduleTaskId => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectTypeDesc => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  int? get weekId => throw _privateConstructorUsedError;
  int? get unlockType => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get lessonLabel => throw _privateConstructorUsedError;
  String? get lessonOrder => throw _privateConstructorUsedError;
  String? get lessonName => throw _privateConstructorUsedError;
  String? get lessonCoverImage => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  String? get courseSegmentName => throw _privateConstructorUsedError;
  String? get userCourseBusinessStatus => throw _privateConstructorUsedError;
  bool? get newGetFlag => throw _privateConstructorUsedError;
  String? get btnText => throw _privateConstructorUsedError;
  String? get pictureUrl => throw _privateConstructorUsedError;
  String? get backgroundColor => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;
  String? get missionMaterialType => throw _privateConstructorUsedError;
  String? get missionBusinessTypeName => throw _privateConstructorUsedError;
  bool? get expired => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get titleColor => throw _privateConstructorUsedError;
  String? get descriptionColor => throw _privateConstructorUsedError;
  String? get missionMaterialTypeName => throw _privateConstructorUsedError;
  String? get missionTypeName => throw _privateConstructorUsedError;
  String? get businessTagId => throw _privateConstructorUsedError;
  String? get topTips => throw _privateConstructorUsedError;
  String? get topIcon => throw _privateConstructorUsedError;
  String? get gradeTitle => throw _privateConstructorUsedError;
  int? get gradeKey => throw _privateConstructorUsedError;
  bool? get lastDrainageCourseLessonTask => throw _privateConstructorUsedError;
  bool? get needPop => throw _privateConstructorUsedError;
  String? get buttonDescription => throw _privateConstructorUsedError;
  String? get landscapePictureUrl => throw _privateConstructorUsedError;
  int? get auditId => throw _privateConstructorUsedError;
  int? get drainageCourseConfigId => throw _privateConstructorUsedError;
  String? get channelNo =>
      throw _privateConstructorUsedError; // ignore: invalid_annotation_target
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfoModel>? get subjectInfoList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EnjoyCourseTaskModelCopyWith<EnjoyCourseTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnjoyCourseTaskModelCopyWith<$Res> {
  factory $EnjoyCourseTaskModelCopyWith(EnjoyCourseTaskModel value,
          $Res Function(EnjoyCourseTaskModel) then) =
      _$EnjoyCourseTaskModelCopyWithImpl<$Res, EnjoyCourseTaskModel>;
  @useResult
  $Res call(
      {int? taskStatus,
      int? materialId,
      String? materialName,
      int? configId,
      String? taskStatusDesc,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? joinClassTime,
      int? taskType,
      int? classId,
      String? scheduleTaskId,
      int? courseId,
      String? courseKey,
      int? lessonId,
      int? subjectType,
      String? subjectTypeDesc,
      int? segmentId,
      int? weekId,
      int? unlockType,
      String? title,
      String? subTitle,
      String? route,
      String? icon,
      String? lessonLabel,
      String? lessonOrder,
      String? lessonName,
      String? lessonCoverImage,
      String? image,
      int? courseType,
      String? courseSegmentName,
      String? userCourseBusinessStatus,
      bool? newGetFlag,
      String? btnText,
      String? pictureUrl,
      String? backgroundColor,
      String? linkUrl,
      String? missionMaterialType,
      String? missionBusinessTypeName,
      bool? expired,
      String? description,
      String? titleColor,
      String? descriptionColor,
      String? missionMaterialTypeName,
      String? missionTypeName,
      String? businessTagId,
      String? topTips,
      String? topIcon,
      String? gradeTitle,
      int? gradeKey,
      bool? lastDrainageCourseLessonTask,
      bool? needPop,
      String? buttonDescription,
      String? landscapePictureUrl,
      int? auditId,
      int? drainageCourseConfigId,
      String? channelNo,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      List<SubjectInfoModel>? subjectInfoList});
}

/// @nodoc
class _$EnjoyCourseTaskModelCopyWithImpl<$Res,
        $Val extends EnjoyCourseTaskModel>
    implements $EnjoyCourseTaskModelCopyWith<$Res> {
  _$EnjoyCourseTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? configId = freezed,
    Object? taskStatusDesc = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? joinClassTime = freezed,
    Object? taskType = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonOrder = freezed,
    Object? lessonName = freezed,
    Object? lessonCoverImage = freezed,
    Object? image = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? newGetFlag = freezed,
    Object? btnText = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? linkUrl = freezed,
    Object? missionMaterialType = freezed,
    Object? missionBusinessTypeName = freezed,
    Object? expired = freezed,
    Object? description = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? missionMaterialTypeName = freezed,
    Object? missionTypeName = freezed,
    Object? businessTagId = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? gradeTitle = freezed,
    Object? gradeKey = freezed,
    Object? lastDrainageCourseLessonTask = freezed,
    Object? needPop = freezed,
    Object? buttonDescription = freezed,
    Object? landscapePictureUrl = freezed,
    Object? auditId = freezed,
    Object? drainageCourseConfigId = freezed,
    Object? channelNo = freezed,
    Object? subjectInfoList = freezed,
  }) {
    return _then(_value.copyWith(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatusDesc: freezed == taskStatusDesc
          ? _value.taskStatusDesc
          : taskStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      joinClassTime: freezed == joinClassTime
          ? _value.joinClassTime
          : joinClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      missionBusinessTypeName: freezed == missionBusinessTypeName
          ? _value.missionBusinessTypeName
          : missionBusinessTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      expired: freezed == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialTypeName: freezed == missionMaterialTypeName
          ? _value.missionMaterialTypeName
          : missionMaterialTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      missionTypeName: freezed == missionTypeName
          ? _value.missionTypeName
          : missionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      lastDrainageCourseLessonTask: freezed == lastDrainageCourseLessonTask
          ? _value.lastDrainageCourseLessonTask
          : lastDrainageCourseLessonTask // ignore: cast_nullable_to_non_nullable
              as bool?,
      needPop: freezed == needPop
          ? _value.needPop
          : needPop // ignore: cast_nullable_to_non_nullable
              as bool?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapePictureUrl: freezed == landscapePictureUrl
          ? _value.landscapePictureUrl
          : landscapePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      auditId: freezed == auditId
          ? _value.auditId
          : auditId // ignore: cast_nullable_to_non_nullable
              as int?,
      drainageCourseConfigId: freezed == drainageCourseConfigId
          ? _value.drainageCourseConfigId
          : drainageCourseConfigId // ignore: cast_nullable_to_non_nullable
              as int?,
      channelNo: freezed == channelNo
          ? _value.channelNo
          : channelNo // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value.subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectInfoModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EnjoyCourseTaskModelCopyWith<$Res>
    implements $EnjoyCourseTaskModelCopyWith<$Res> {
  factory _$$_EnjoyCourseTaskModelCopyWith(_$_EnjoyCourseTaskModel value,
          $Res Function(_$_EnjoyCourseTaskModel) then) =
      __$$_EnjoyCourseTaskModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskStatus,
      int? materialId,
      String? materialName,
      int? configId,
      String? taskStatusDesc,
      int? showDateTime,
      int? startClassTime,
      int? firstLessonStartTime,
      int? joinClassTime,
      int? taskType,
      int? classId,
      String? scheduleTaskId,
      int? courseId,
      String? courseKey,
      int? lessonId,
      int? subjectType,
      String? subjectTypeDesc,
      int? segmentId,
      int? weekId,
      int? unlockType,
      String? title,
      String? subTitle,
      String? route,
      String? icon,
      String? lessonLabel,
      String? lessonOrder,
      String? lessonName,
      String? lessonCoverImage,
      String? image,
      int? courseType,
      String? courseSegmentName,
      String? userCourseBusinessStatus,
      bool? newGetFlag,
      String? btnText,
      String? pictureUrl,
      String? backgroundColor,
      String? linkUrl,
      String? missionMaterialType,
      String? missionBusinessTypeName,
      bool? expired,
      String? description,
      String? titleColor,
      String? descriptionColor,
      String? missionMaterialTypeName,
      String? missionTypeName,
      String? businessTagId,
      String? topTips,
      String? topIcon,
      String? gradeTitle,
      int? gradeKey,
      bool? lastDrainageCourseLessonTask,
      bool? needPop,
      String? buttonDescription,
      String? landscapePictureUrl,
      int? auditId,
      int? drainageCourseConfigId,
      String? channelNo,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      List<SubjectInfoModel>? subjectInfoList});
}

/// @nodoc
class __$$_EnjoyCourseTaskModelCopyWithImpl<$Res>
    extends _$EnjoyCourseTaskModelCopyWithImpl<$Res, _$_EnjoyCourseTaskModel>
    implements _$$_EnjoyCourseTaskModelCopyWith<$Res> {
  __$$_EnjoyCourseTaskModelCopyWithImpl(_$_EnjoyCourseTaskModel _value,
      $Res Function(_$_EnjoyCourseTaskModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskStatus = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? configId = freezed,
    Object? taskStatusDesc = freezed,
    Object? showDateTime = freezed,
    Object? startClassTime = freezed,
    Object? firstLessonStartTime = freezed,
    Object? joinClassTime = freezed,
    Object? taskType = freezed,
    Object? classId = freezed,
    Object? scheduleTaskId = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? subjectType = freezed,
    Object? subjectTypeDesc = freezed,
    Object? segmentId = freezed,
    Object? weekId = freezed,
    Object? unlockType = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? route = freezed,
    Object? icon = freezed,
    Object? lessonLabel = freezed,
    Object? lessonOrder = freezed,
    Object? lessonName = freezed,
    Object? lessonCoverImage = freezed,
    Object? image = freezed,
    Object? courseType = freezed,
    Object? courseSegmentName = freezed,
    Object? userCourseBusinessStatus = freezed,
    Object? newGetFlag = freezed,
    Object? btnText = freezed,
    Object? pictureUrl = freezed,
    Object? backgroundColor = freezed,
    Object? linkUrl = freezed,
    Object? missionMaterialType = freezed,
    Object? missionBusinessTypeName = freezed,
    Object? expired = freezed,
    Object? description = freezed,
    Object? titleColor = freezed,
    Object? descriptionColor = freezed,
    Object? missionMaterialTypeName = freezed,
    Object? missionTypeName = freezed,
    Object? businessTagId = freezed,
    Object? topTips = freezed,
    Object? topIcon = freezed,
    Object? gradeTitle = freezed,
    Object? gradeKey = freezed,
    Object? lastDrainageCourseLessonTask = freezed,
    Object? needPop = freezed,
    Object? buttonDescription = freezed,
    Object? landscapePictureUrl = freezed,
    Object? auditId = freezed,
    Object? drainageCourseConfigId = freezed,
    Object? channelNo = freezed,
    Object? subjectInfoList = freezed,
  }) {
    return _then(_$_EnjoyCourseTaskModel(
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as int?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      configId: freezed == configId
          ? _value.configId
          : configId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskStatusDesc: freezed == taskStatusDesc
          ? _value.taskStatusDesc
          : taskStatusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      firstLessonStartTime: freezed == firstLessonStartTime
          ? _value.firstLessonStartTime
          : firstLessonStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      joinClassTime: freezed == joinClassTime
          ? _value.joinClassTime
          : joinClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as int?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      scheduleTaskId: freezed == scheduleTaskId
          ? _value.scheduleTaskId
          : scheduleTaskId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectTypeDesc: freezed == subjectTypeDesc
          ? _value.subjectTypeDesc
          : subjectTypeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      unlockType: freezed == unlockType
          ? _value.unlockType
          : unlockType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonLabel: freezed == lessonLabel
          ? _value.lessonLabel
          : lessonLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonCoverImage: freezed == lessonCoverImage
          ? _value.lessonCoverImage
          : lessonCoverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentName: freezed == courseSegmentName
          ? _value.courseSegmentName
          : courseSegmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      userCourseBusinessStatus: freezed == userCourseBusinessStatus
          ? _value.userCourseBusinessStatus
          : userCourseBusinessStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      newGetFlag: freezed == newGetFlag
          ? _value.newGetFlag
          : newGetFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      btnText: freezed == btnText
          ? _value.btnText
          : btnText // ignore: cast_nullable_to_non_nullable
              as String?,
      pictureUrl: freezed == pictureUrl
          ? _value.pictureUrl
          : pictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialType: freezed == missionMaterialType
          ? _value.missionMaterialType
          : missionMaterialType // ignore: cast_nullable_to_non_nullable
              as String?,
      missionBusinessTypeName: freezed == missionBusinessTypeName
          ? _value.missionBusinessTypeName
          : missionBusinessTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      expired: freezed == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      titleColor: freezed == titleColor
          ? _value.titleColor
          : titleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionColor: freezed == descriptionColor
          ? _value.descriptionColor
          : descriptionColor // ignore: cast_nullable_to_non_nullable
              as String?,
      missionMaterialTypeName: freezed == missionMaterialTypeName
          ? _value.missionMaterialTypeName
          : missionMaterialTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      missionTypeName: freezed == missionTypeName
          ? _value.missionTypeName
          : missionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      businessTagId: freezed == businessTagId
          ? _value.businessTagId
          : businessTagId // ignore: cast_nullable_to_non_nullable
              as String?,
      topTips: freezed == topTips
          ? _value.topTips
          : topTips // ignore: cast_nullable_to_non_nullable
              as String?,
      topIcon: freezed == topIcon
          ? _value.topIcon
          : topIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeTitle: freezed == gradeTitle
          ? _value.gradeTitle
          : gradeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      gradeKey: freezed == gradeKey
          ? _value.gradeKey
          : gradeKey // ignore: cast_nullable_to_non_nullable
              as int?,
      lastDrainageCourseLessonTask: freezed == lastDrainageCourseLessonTask
          ? _value.lastDrainageCourseLessonTask
          : lastDrainageCourseLessonTask // ignore: cast_nullable_to_non_nullable
              as bool?,
      needPop: freezed == needPop
          ? _value.needPop
          : needPop // ignore: cast_nullable_to_non_nullable
              as bool?,
      buttonDescription: freezed == buttonDescription
          ? _value.buttonDescription
          : buttonDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      landscapePictureUrl: freezed == landscapePictureUrl
          ? _value.landscapePictureUrl
          : landscapePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      auditId: freezed == auditId
          ? _value.auditId
          : auditId // ignore: cast_nullable_to_non_nullable
              as int?,
      drainageCourseConfigId: freezed == drainageCourseConfigId
          ? _value.drainageCourseConfigId
          : drainageCourseConfigId // ignore: cast_nullable_to_non_nullable
              as int?,
      channelNo: freezed == channelNo
          ? _value.channelNo
          : channelNo // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectInfoList: freezed == subjectInfoList
          ? _value._subjectInfoList
          : subjectInfoList // ignore: cast_nullable_to_non_nullable
              as List<SubjectInfoModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EnjoyCourseTaskModel implements _EnjoyCourseTaskModel {
  const _$_EnjoyCourseTaskModel(
      {this.taskStatus,
      this.materialId,
      this.materialName,
      this.configId,
      this.taskStatusDesc,
      this.showDateTime,
      this.startClassTime,
      this.firstLessonStartTime,
      this.joinClassTime,
      this.taskType,
      this.classId,
      this.scheduleTaskId,
      this.courseId,
      this.courseKey,
      this.lessonId,
      this.subjectType,
      this.subjectTypeDesc,
      this.segmentId,
      this.weekId,
      this.unlockType,
      this.title,
      this.subTitle,
      this.route,
      this.icon,
      this.lessonLabel,
      this.lessonOrder,
      this.lessonName,
      this.lessonCoverImage,
      this.image,
      this.courseType,
      this.courseSegmentName,
      this.userCourseBusinessStatus,
      this.newGetFlag,
      this.btnText,
      this.pictureUrl,
      this.backgroundColor,
      this.linkUrl,
      this.missionMaterialType,
      this.missionBusinessTypeName,
      this.expired,
      this.description,
      this.titleColor,
      this.descriptionColor,
      this.missionMaterialTypeName,
      this.missionTypeName,
      this.businessTagId,
      this.topTips,
      this.topIcon,
      this.gradeTitle,
      this.gradeKey,
      this.lastDrainageCourseLessonTask,
      this.needPop,
      this.buttonDescription,
      this.landscapePictureUrl,
      this.auditId,
      this.drainageCourseConfigId,
      this.channelNo,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      final List<SubjectInfoModel>? subjectInfoList})
      : _subjectInfoList = subjectInfoList;

  factory _$_EnjoyCourseTaskModel.fromJson(Map<String, dynamic> json) =>
      _$$_EnjoyCourseTaskModelFromJson(json);

  @override
  final int? taskStatus;
  @override
  final int? materialId;
  @override
  final String? materialName;
  @override
  final int? configId;
  @override
  final String? taskStatusDesc;
  @override
  final int? showDateTime;
  @override
  final int? startClassTime;
  @override
  final int? firstLessonStartTime;
  @override
  final int? joinClassTime;
  @override
  final int? taskType;
  @override
  final int? classId;
  @override
  final String? scheduleTaskId;
  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final int? lessonId;
  @override
  final int? subjectType;
  @override
  final String? subjectTypeDesc;
  @override
  final int? segmentId;
  @override
  final int? weekId;
  @override
  final int? unlockType;
  @override
  final String? title;
  @override
  final String? subTitle;
  @override
  final String? route;
  @override
  final String? icon;
  @override
  final String? lessonLabel;
  @override
  final String? lessonOrder;
  @override
  final String? lessonName;
  @override
  final String? lessonCoverImage;
  @override
  final String? image;
  @override
  final int? courseType;
  @override
  final String? courseSegmentName;
  @override
  final String? userCourseBusinessStatus;
  @override
  final bool? newGetFlag;
  @override
  final String? btnText;
  @override
  final String? pictureUrl;
  @override
  final String? backgroundColor;
  @override
  final String? linkUrl;
  @override
  final String? missionMaterialType;
  @override
  final String? missionBusinessTypeName;
  @override
  final bool? expired;
  @override
  final String? description;
  @override
  final String? titleColor;
  @override
  final String? descriptionColor;
  @override
  final String? missionMaterialTypeName;
  @override
  final String? missionTypeName;
  @override
  final String? businessTagId;
  @override
  final String? topTips;
  @override
  final String? topIcon;
  @override
  final String? gradeTitle;
  @override
  final int? gradeKey;
  @override
  final bool? lastDrainageCourseLessonTask;
  @override
  final bool? needPop;
  @override
  final String? buttonDescription;
  @override
  final String? landscapePictureUrl;
  @override
  final int? auditId;
  @override
  final int? drainageCourseConfigId;
  @override
  final String? channelNo;
// ignore: invalid_annotation_target
  final List<SubjectInfoModel>? _subjectInfoList;
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfoModel>? get subjectInfoList {
    final value = _subjectInfoList;
    if (value == null) return null;
    if (_subjectInfoList is EqualUnmodifiableListView) return _subjectInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'EnjoyCourseTaskModel(taskStatus: $taskStatus, materialId: $materialId, materialName: $materialName, configId: $configId, taskStatusDesc: $taskStatusDesc, showDateTime: $showDateTime, startClassTime: $startClassTime, firstLessonStartTime: $firstLessonStartTime, joinClassTime: $joinClassTime, taskType: $taskType, classId: $classId, scheduleTaskId: $scheduleTaskId, courseId: $courseId, courseKey: $courseKey, lessonId: $lessonId, subjectType: $subjectType, subjectTypeDesc: $subjectTypeDesc, segmentId: $segmentId, weekId: $weekId, unlockType: $unlockType, title: $title, subTitle: $subTitle, route: $route, icon: $icon, lessonLabel: $lessonLabel, lessonOrder: $lessonOrder, lessonName: $lessonName, lessonCoverImage: $lessonCoverImage, image: $image, courseType: $courseType, courseSegmentName: $courseSegmentName, userCourseBusinessStatus: $userCourseBusinessStatus, newGetFlag: $newGetFlag, btnText: $btnText, pictureUrl: $pictureUrl, backgroundColor: $backgroundColor, linkUrl: $linkUrl, missionMaterialType: $missionMaterialType, missionBusinessTypeName: $missionBusinessTypeName, expired: $expired, description: $description, titleColor: $titleColor, descriptionColor: $descriptionColor, missionMaterialTypeName: $missionMaterialTypeName, missionTypeName: $missionTypeName, businessTagId: $businessTagId, topTips: $topTips, topIcon: $topIcon, gradeTitle: $gradeTitle, gradeKey: $gradeKey, lastDrainageCourseLessonTask: $lastDrainageCourseLessonTask, needPop: $needPop, buttonDescription: $buttonDescription, landscapePictureUrl: $landscapePictureUrl, auditId: $auditId, drainageCourseConfigId: $drainageCourseConfigId, channelNo: $channelNo, subjectInfoList: $subjectInfoList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EnjoyCourseTaskModel &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.configId, configId) ||
                other.configId == configId) &&
            (identical(other.taskStatusDesc, taskStatusDesc) ||
                other.taskStatusDesc == taskStatusDesc) &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.startClassTime, startClassTime) ||
                other.startClassTime == startClassTime) &&
            (identical(other.firstLessonStartTime, firstLessonStartTime) ||
                other.firstLessonStartTime == firstLessonStartTime) &&
            (identical(other.joinClassTime, joinClassTime) ||
                other.joinClassTime == joinClassTime) &&
            (identical(other.taskType, taskType) ||
                other.taskType == taskType) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.scheduleTaskId, scheduleTaskId) ||
                other.scheduleTaskId == scheduleTaskId) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectTypeDesc, subjectTypeDesc) ||
                other.subjectTypeDesc == subjectTypeDesc) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.unlockType, unlockType) ||
                other.unlockType == unlockType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.lessonLabel, lessonLabel) ||
                other.lessonLabel == lessonLabel) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonCoverImage, lessonCoverImage) ||
                other.lessonCoverImage == lessonCoverImage) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseSegmentName, courseSegmentName) ||
                other.courseSegmentName == courseSegmentName) &&
            (identical(other.userCourseBusinessStatus, userCourseBusinessStatus) ||
                other.userCourseBusinessStatus == userCourseBusinessStatus) &&
            (identical(other.newGetFlag, newGetFlag) ||
                other.newGetFlag == newGetFlag) &&
            (identical(other.btnText, btnText) || other.btnText == btnText) &&
            (identical(other.pictureUrl, pictureUrl) ||
                other.pictureUrl == pictureUrl) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl) &&
            (identical(other.missionMaterialType, missionMaterialType) ||
                other.missionMaterialType == missionMaterialType) &&
            (identical(other.missionBusinessTypeName, missionBusinessTypeName) ||
                other.missionBusinessTypeName == missionBusinessTypeName) &&
            (identical(other.expired, expired) || other.expired == expired) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.titleColor, titleColor) ||
                other.titleColor == titleColor) &&
            (identical(other.descriptionColor, descriptionColor) ||
                other.descriptionColor == descriptionColor) &&
            (identical(other.missionMaterialTypeName, missionMaterialTypeName) ||
                other.missionMaterialTypeName == missionMaterialTypeName) &&
            (identical(other.missionTypeName, missionTypeName) ||
                other.missionTypeName == missionTypeName) &&
            (identical(other.businessTagId, businessTagId) ||
                other.businessTagId == businessTagId) &&
            (identical(other.topTips, topTips) || other.topTips == topTips) &&
            (identical(other.topIcon, topIcon) || other.topIcon == topIcon) &&
            (identical(other.gradeTitle, gradeTitle) ||
                other.gradeTitle == gradeTitle) &&
            (identical(other.gradeKey, gradeKey) ||
                other.gradeKey == gradeKey) &&
            (identical(other.lastDrainageCourseLessonTask, lastDrainageCourseLessonTask) ||
                other.lastDrainageCourseLessonTask == lastDrainageCourseLessonTask) &&
            (identical(other.needPop, needPop) || other.needPop == needPop) &&
            (identical(other.buttonDescription, buttonDescription) || other.buttonDescription == buttonDescription) &&
            (identical(other.landscapePictureUrl, landscapePictureUrl) || other.landscapePictureUrl == landscapePictureUrl) &&
            (identical(other.auditId, auditId) || other.auditId == auditId) &&
            (identical(other.drainageCourseConfigId, drainageCourseConfigId) || other.drainageCourseConfigId == drainageCourseConfigId) &&
            (identical(other.channelNo, channelNo) || other.channelNo == channelNo) &&
            const DeepCollectionEquality().equals(other._subjectInfoList, _subjectInfoList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        taskStatus,
        materialId,
        materialName,
        configId,
        taskStatusDesc,
        showDateTime,
        startClassTime,
        firstLessonStartTime,
        joinClassTime,
        taskType,
        classId,
        scheduleTaskId,
        courseId,
        courseKey,
        lessonId,
        subjectType,
        subjectTypeDesc,
        segmentId,
        weekId,
        unlockType,
        title,
        subTitle,
        route,
        icon,
        lessonLabel,
        lessonOrder,
        lessonName,
        lessonCoverImage,
        image,
        courseType,
        courseSegmentName,
        userCourseBusinessStatus,
        newGetFlag,
        btnText,
        pictureUrl,
        backgroundColor,
        linkUrl,
        missionMaterialType,
        missionBusinessTypeName,
        expired,
        description,
        titleColor,
        descriptionColor,
        missionMaterialTypeName,
        missionTypeName,
        businessTagId,
        topTips,
        topIcon,
        gradeTitle,
        gradeKey,
        lastDrainageCourseLessonTask,
        needPop,
        buttonDescription,
        landscapePictureUrl,
        auditId,
        drainageCourseConfigId,
        channelNo,
        const DeepCollectionEquality().hash(_subjectInfoList)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EnjoyCourseTaskModelCopyWith<_$_EnjoyCourseTaskModel> get copyWith =>
      __$$_EnjoyCourseTaskModelCopyWithImpl<_$_EnjoyCourseTaskModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EnjoyCourseTaskModelToJson(
      this,
    );
  }
}

abstract class _EnjoyCourseTaskModel implements EnjoyCourseTaskModel {
  const factory _EnjoyCourseTaskModel(
      {final int? taskStatus,
      final int? materialId,
      final String? materialName,
      final int? configId,
      final String? taskStatusDesc,
      final int? showDateTime,
      final int? startClassTime,
      final int? firstLessonStartTime,
      final int? joinClassTime,
      final int? taskType,
      final int? classId,
      final String? scheduleTaskId,
      final int? courseId,
      final String? courseKey,
      final int? lessonId,
      final int? subjectType,
      final String? subjectTypeDesc,
      final int? segmentId,
      final int? weekId,
      final int? unlockType,
      final String? title,
      final String? subTitle,
      final String? route,
      final String? icon,
      final String? lessonLabel,
      final String? lessonOrder,
      final String? lessonName,
      final String? lessonCoverImage,
      final String? image,
      final int? courseType,
      final String? courseSegmentName,
      final String? userCourseBusinessStatus,
      final bool? newGetFlag,
      final String? btnText,
      final String? pictureUrl,
      final String? backgroundColor,
      final String? linkUrl,
      final String? missionMaterialType,
      final String? missionBusinessTypeName,
      final bool? expired,
      final String? description,
      final String? titleColor,
      final String? descriptionColor,
      final String? missionMaterialTypeName,
      final String? missionTypeName,
      final String? businessTagId,
      final String? topTips,
      final String? topIcon,
      final String? gradeTitle,
      final int? gradeKey,
      final bool? lastDrainageCourseLessonTask,
      final bool? needPop,
      final String? buttonDescription,
      final String? landscapePictureUrl,
      final int? auditId,
      final int? drainageCourseConfigId,
      final String? channelNo,
      @JsonKey(name: "drainageCourseReceiveCardDataBo")
      final List<SubjectInfoModel>? subjectInfoList}) = _$_EnjoyCourseTaskModel;

  factory _EnjoyCourseTaskModel.fromJson(Map<String, dynamic> json) =
      _$_EnjoyCourseTaskModel.fromJson;

  @override
  int? get taskStatus;
  @override
  int? get materialId;
  @override
  String? get materialName;
  @override
  int? get configId;
  @override
  String? get taskStatusDesc;
  @override
  int? get showDateTime;
  @override
  int? get startClassTime;
  @override
  int? get firstLessonStartTime;
  @override
  int? get joinClassTime;
  @override
  int? get taskType;
  @override
  int? get classId;
  @override
  String? get scheduleTaskId;
  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  int? get lessonId;
  @override
  int? get subjectType;
  @override
  String? get subjectTypeDesc;
  @override
  int? get segmentId;
  @override
  int? get weekId;
  @override
  int? get unlockType;
  @override
  String? get title;
  @override
  String? get subTitle;
  @override
  String? get route;
  @override
  String? get icon;
  @override
  String? get lessonLabel;
  @override
  String? get lessonOrder;
  @override
  String? get lessonName;
  @override
  String? get lessonCoverImage;
  @override
  String? get image;
  @override
  int? get courseType;
  @override
  String? get courseSegmentName;
  @override
  String? get userCourseBusinessStatus;
  @override
  bool? get newGetFlag;
  @override
  String? get btnText;
  @override
  String? get pictureUrl;
  @override
  String? get backgroundColor;
  @override
  String? get linkUrl;
  @override
  String? get missionMaterialType;
  @override
  String? get missionBusinessTypeName;
  @override
  bool? get expired;
  @override
  String? get description;
  @override
  String? get titleColor;
  @override
  String? get descriptionColor;
  @override
  String? get missionMaterialTypeName;
  @override
  String? get missionTypeName;
  @override
  String? get businessTagId;
  @override
  String? get topTips;
  @override
  String? get topIcon;
  @override
  String? get gradeTitle;
  @override
  int? get gradeKey;
  @override
  bool? get lastDrainageCourseLessonTask;
  @override
  bool? get needPop;
  @override
  String? get buttonDescription;
  @override
  String? get landscapePictureUrl;
  @override
  int? get auditId;
  @override
  int? get drainageCourseConfigId;
  @override
  String? get channelNo;
  @override // ignore: invalid_annotation_target
  @JsonKey(name: "drainageCourseReceiveCardDataBo")
  List<SubjectInfoModel>? get subjectInfoList;
  @override
  @JsonKey(ignore: true)
  _$$_EnjoyCourseTaskModelCopyWith<_$_EnjoyCourseTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}
