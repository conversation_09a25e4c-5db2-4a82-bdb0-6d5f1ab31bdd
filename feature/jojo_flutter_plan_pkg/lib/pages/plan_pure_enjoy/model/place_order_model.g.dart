// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlaceOrderModel _$$_PlaceOrderModelFromJson(Map<String, dynamic> json) =>
    _$_PlaceOrderModel(
      orderId: json['orderId'] as String?,
      errorTips: json['errorTips'] == null
          ? null
          : ErrorTips.fromJson(json['errorTips'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_PlaceOrderModelToJson(_$_PlaceOrderModel instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'errorTips': instance.errorTips,
    };

_$_ErrorTips _$$_ErrorTipsFromJson(Map<String, dynamic> json) => _$_ErrorTips(
      message: json['message'] as String?,
      buttons: (json['buttons'] as List<dynamic>?)
          ?.map((e) => ErrorButton.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ErrorTipsToJson(_$_ErrorTips instance) =>
    <String, dynamic>{
      'message': instance.message,
      'buttons': instance.buttons,
    };

_$_ErrorButton _$$_ErrorButtonFromJson(Map<String, dynamic> json) =>
    _$_ErrorButton(
      description: json['description'] as String?,
      linkUrl: json['linkUrl'] as String?,
    );

Map<String, dynamic> _$$_ErrorButtonToJson(_$_ErrorButton instance) =>
    <String, dynamic>{
      'description': instance.description,
      'linkUrl': instance.linkUrl,
    };
