// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_SubjectInfoModel _$$_SubjectInfoModelFromJson(Map<String, dynamic> json) =>
    _$_SubjectInfoModel(
      selected: json['selected'] as bool?,
      skipped: json['skipped'] as bool?,
      pictureUrl: json['pictureUrl'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      titleColor: json['titleColor'] as String?,
      descriptionColor: json['descriptionColor'] as String?,
      btnText: json['btnText'] as String?,
      subDesc: json['subDesc'] as String?,
      type: json['type'] as int?,
      typeDesc: json['typeDesc'] as String?,
      subjectTypeDesc: json['subjectTypeDesc'] as String?,
      icon: json['icon'] as String?,
      selectedIcon: json['selectedIcon'] as String?,
      selectedTextColor: json['selectedTextColor'] as String?,
      selectedBgColor: json['selectedBgColor'] as String?,
      libId: json['libId'] as String?,
      topTips: json['topTips'] as String?,
      topIcon: json['topIcon'] as String?,
      gradeInfoList: (json['gradeInfoList'] as List<dynamic>?)
          ?.map((e) => GradeInfoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      missionMaterialType: json['missionMaterialType'] as String?,
      linkUrl: json['linkUrl'] as String?,
      lastSelectedTipType: json['lastSelectedTipType'] as int?,
      lastSelected: json['lastSelected'] as bool?,
    );

Map<String, dynamic> _$$_SubjectInfoModelToJson(_$_SubjectInfoModel instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'skipped': instance.skipped,
      'pictureUrl': instance.pictureUrl,
      'backgroundColor': instance.backgroundColor,
      'titleColor': instance.titleColor,
      'descriptionColor': instance.descriptionColor,
      'btnText': instance.btnText,
      'subDesc': instance.subDesc,
      'type': instance.type,
      'typeDesc': instance.typeDesc,
      'subjectTypeDesc': instance.subjectTypeDesc,
      'icon': instance.icon,
      'selectedIcon': instance.selectedIcon,
      'selectedTextColor': instance.selectedTextColor,
      'selectedBgColor': instance.selectedBgColor,
      'libId': instance.libId,
      'topTips': instance.topTips,
      'topIcon': instance.topIcon,
      'gradeInfoList': instance.gradeInfoList,
      'missionMaterialType': instance.missionMaterialType,
      'linkUrl': instance.linkUrl,
      'lastSelectedTipType': instance.lastSelectedTipType,
      'lastSelected': instance.lastSelected,
    };

_$_GradeInfoModel _$$_GradeInfoModelFromJson(Map<String, dynamic> json) =>
    _$_GradeInfoModel(
      selected: json['selected'] as bool?,
      skipped: json['skipped'] as bool?,
      lastSelected: json['lastSelected'] as bool?,
      gradeKey: json['gradeKey'] as int?,
      gradeTitle: json['gradeTitle'] as String?,
      gradeDesc: json['gradeDesc'] as String?,
      tip: json['tip'] as String?,
      topTips: json['topTips'] as String?,
      topIcon: json['topIcon'] as String?,
      resourceId: json['resourceId'] as String?,
      courseCardDataList: (json['courseCardDataList'] as List<dynamic>?)
          ?.map((e) => CourseCardModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_GradeInfoModelToJson(_$_GradeInfoModel instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'skipped': instance.skipped,
      'lastSelected': instance.lastSelected,
      'gradeKey': instance.gradeKey,
      'gradeTitle': instance.gradeTitle,
      'gradeDesc': instance.gradeDesc,
      'tip': instance.tip,
      'topTips': instance.topTips,
      'topIcon': instance.topIcon,
      'resourceId': instance.resourceId,
      'courseCardDataList': instance.courseCardDataList,
    };

_$_CourseCardModel _$$_CourseCardModelFromJson(Map<String, dynamic> json) =>
    _$_CourseCardModel(
      courseInfoList: (json['courseInfoList'] as List<dynamic>?)
          ?.map((e) => CourseInfoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_CourseCardModelToJson(_$_CourseCardModel instance) =>
    <String, dynamic>{
      'courseInfoList': instance.courseInfoList,
    };

_$_CourseInfoModel _$$_CourseInfoModelFromJson(Map<String, dynamic> json) =>
    _$_CourseInfoModel(
      selected: json['selected'] as bool?,
      skipped: json['skipped'] as bool?,
      courseId: json['courseId'] as String?,
      courseTag: json['courseTag'] as String?,
      courseTagKey: json['courseTagKey'] as int?,
      courseName: json['courseName'] as String?,
      courseKey: json['courseKey'] as String?,
      classId: json['classId'] as int?,
      gradeKey: json['gradeKey'] as int?,
      gradeTitle: json['gradeTitle'] as String?,
      courseDesc: json['courseDesc'] as String?,
      tip: json['tip'] as String?,
      cornerMarkerText: json['cornerMarkerText'] as String?,
      btnText: json['btnText'] as String?,
      skuId: json['skuId'] as String?,
      skuName: json['skuName'] as String?,
      experienceLink: json['experienceLink'] as String?,
      linkId: json['linkId'] as String?,
      courseStatus: json['courseStatus'] as int?,
      topTips: json['topTips'] as String?,
      topIcon: json['topIcon'] as String?,
      teacherImgUrl: json['teacherImgUrl'] as String?,
      learningUrl: json['learningUrl'] as String?,
      learningStatus: json['learningStatus'] as String?,
      entranceImage: json['entranceImage'] as String?,
      entranceImageSmall: json['entranceImageSmall'] as String?,
      trainingCampTips: json['trainingCampTips'] as String?,
      recommendText: json['recommendText'] as String?,
      recommendIcon: json['recommendIcon'] as String?,
      trainingCampPurchaseUrl: json['trainingCampPurchaseUrl'] as String?,
      trainingCampBtn: json['trainingCampBtn'] as String?,
      trainingCampDescImgBeforeSelection:
          json['trainingCampDescImgBeforeSelection'] as String?,
      trainingCampDescImgSelected:
          json['trainingCampDescImgSelected'] as String?,
      monthCoursePurchaseUrl: json['monthCoursePurchaseUrl'] as String?,
      monthCourseBtn: json['monthCourseBtn'] as String?,
      monthCourseDescImgBeforeSelection:
          json['monthCourseDescImgBeforeSelection'] as String?,
      monthCourseDescImgSelected: json['monthCourseDescImgSelected'] as String?,
      yearCoursePurchaseLink: json['yearCoursePurchaseLink'] as String?,
      yearCourseBtn: json['yearCourseBtn'] as String?,
      yearCourseDescImgBeforeSelection:
          json['yearCourseDescImgBeforeSelection'] as String?,
      yearCourseDescImgSelected: json['yearCourseDescImgSelected'] as String?,
      segmentDescImg: json['segmentDescImg'] as String?,
      padTrainingCampBtn: json['padTrainingCampBtn'] as String?,
      padTrainingCampDescImgBeforeSelection:
          json['padTrainingCampDescImgBeforeSelection'] as String?,
      padTrainingCampDescImgSelected:
          json['padTrainingCampDescImgSelected'] as String?,
      padMonthCourseBtn: json['padMonthCourseBtn'] as String?,
      padMonthCourseDescImgBeforeSelection:
          json['padMonthCourseDescImgBeforeSelection'] as String?,
      padMonthCourseDescImgSelected:
          json['padMonthCourseDescImgSelected'] as String?,
      padYearCourseBtn: json['padYearCourseBtn'] as String?,
      padYearCourseDescImgBeforeSelection:
          json['padYearCourseDescImgBeforeSelection'] as String?,
      padYearCourseDescImgSelected:
          json['padYearCourseDescImgSelected'] as String?,
      padRecommendIcon: json['padRecommendIcon'] as String?,
      padSegmentDescImg: json['padSegmentDescImg'] as String?,
      receiveBtnInfos: (json['receiveBtnInfos'] as List<dynamic>?)
          ?.map((e) => CourseBtnModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      lessonInfoList: (json['lessonInfoList'] as List<dynamic>?)
          ?.map((e) => LessonInfoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      trainingCampCourseInfoList:
          (json['trainingCampCourseInfoList'] as List<dynamic>?)
              ?.map((e) => CourseInfoModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      showRetentionPopup: json['showRetentionPopup'] as bool?,
      retainPopupTitle: json['retainPopupTitle'] as String?,
      retainPopupSubTitle: json['retainPopupSubTitle'] as String?,
      retainPopupImage: json['retainPopupImage'] as String?,
      retainPopupBtnTxt: json['retainPopupBtnTxt'] as String?,
      retainPopupCourseId: json['retainPopupCourseId'] as int?,
      retainPopupCourseKey: json['retainPopupCourseKey'] as String?,
      retainPopupCourseLessonId: json['retainPopupCourseLessonId'] as int?,
      retainPopupCourseName: json['retainPopupCourseName'] as String?,
      retainPopupOrderId: json['retainPopupOrderId'] as String?,
      retainPopupClassId: json['retainPopupClassId'] as int?,
      retainPopupLinkId: json['retainPopupLinkId'] as int?,
      retainPopupSkuId: json['retainPopupSkuId'] as int?,
      retainPopupLinkType: json['retainPopupLinkType'] as int?,
      retainPopupCourseSegmentId: json['retainPopupCourseSegmentId'] as int?,
      orderId: json['orderId'] as int?,
      drainageCourseLinkType: json['drainageCourseLinkType'] as int?,
      directExit: json['directExit'] as bool?,
      lastSelected: json['lastSelected'] as bool?,
      recommendStyle:
          RecommendStyle.fromString(json['recommendStyle'] as String?),
    );

Map<String, dynamic> _$$_CourseInfoModelToJson(_$_CourseInfoModel instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'skipped': instance.skipped,
      'courseId': instance.courseId,
      'courseTag': instance.courseTag,
      'courseTagKey': instance.courseTagKey,
      'courseName': instance.courseName,
      'courseKey': instance.courseKey,
      'classId': instance.classId,
      'gradeKey': instance.gradeKey,
      'gradeTitle': instance.gradeTitle,
      'courseDesc': instance.courseDesc,
      'tip': instance.tip,
      'cornerMarkerText': instance.cornerMarkerText,
      'btnText': instance.btnText,
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'experienceLink': instance.experienceLink,
      'linkId': instance.linkId,
      'courseStatus': instance.courseStatus,
      'topTips': instance.topTips,
      'topIcon': instance.topIcon,
      'teacherImgUrl': instance.teacherImgUrl,
      'learningUrl': instance.learningUrl,
      'learningStatus': instance.learningStatus,
      'entranceImage': instance.entranceImage,
      'entranceImageSmall': instance.entranceImageSmall,
      'trainingCampTips': instance.trainingCampTips,
      'recommendText': instance.recommendText,
      'recommendIcon': instance.recommendIcon,
      'trainingCampPurchaseUrl': instance.trainingCampPurchaseUrl,
      'trainingCampBtn': instance.trainingCampBtn,
      'trainingCampDescImgBeforeSelection':
          instance.trainingCampDescImgBeforeSelection,
      'trainingCampDescImgSelected': instance.trainingCampDescImgSelected,
      'monthCoursePurchaseUrl': instance.monthCoursePurchaseUrl,
      'monthCourseBtn': instance.monthCourseBtn,
      'monthCourseDescImgBeforeSelection':
          instance.monthCourseDescImgBeforeSelection,
      'monthCourseDescImgSelected': instance.monthCourseDescImgSelected,
      'yearCoursePurchaseLink': instance.yearCoursePurchaseLink,
      'yearCourseBtn': instance.yearCourseBtn,
      'yearCourseDescImgBeforeSelection':
          instance.yearCourseDescImgBeforeSelection,
      'yearCourseDescImgSelected': instance.yearCourseDescImgSelected,
      'segmentDescImg': instance.segmentDescImg,
      'padTrainingCampBtn': instance.padTrainingCampBtn,
      'padTrainingCampDescImgBeforeSelection':
          instance.padTrainingCampDescImgBeforeSelection,
      'padTrainingCampDescImgSelected': instance.padTrainingCampDescImgSelected,
      'padMonthCourseBtn': instance.padMonthCourseBtn,
      'padMonthCourseDescImgBeforeSelection':
          instance.padMonthCourseDescImgBeforeSelection,
      'padMonthCourseDescImgSelected': instance.padMonthCourseDescImgSelected,
      'padYearCourseBtn': instance.padYearCourseBtn,
      'padYearCourseDescImgBeforeSelection':
          instance.padYearCourseDescImgBeforeSelection,
      'padYearCourseDescImgSelected': instance.padYearCourseDescImgSelected,
      'padRecommendIcon': instance.padRecommendIcon,
      'padSegmentDescImg': instance.padSegmentDescImg,
      'receiveBtnInfos': instance.receiveBtnInfos,
      'lessonInfoList': instance.lessonInfoList,
      'trainingCampCourseInfoList': instance.trainingCampCourseInfoList,
      'showRetentionPopup': instance.showRetentionPopup,
      'retainPopupTitle': instance.retainPopupTitle,
      'retainPopupSubTitle': instance.retainPopupSubTitle,
      'retainPopupImage': instance.retainPopupImage,
      'retainPopupBtnTxt': instance.retainPopupBtnTxt,
      'retainPopupCourseId': instance.retainPopupCourseId,
      'retainPopupCourseKey': instance.retainPopupCourseKey,
      'retainPopupCourseLessonId': instance.retainPopupCourseLessonId,
      'retainPopupCourseName': instance.retainPopupCourseName,
      'retainPopupOrderId': instance.retainPopupOrderId,
      'retainPopupClassId': instance.retainPopupClassId,
      'retainPopupLinkId': instance.retainPopupLinkId,
      'retainPopupSkuId': instance.retainPopupSkuId,
      'retainPopupLinkType': instance.retainPopupLinkType,
      'retainPopupCourseSegmentId': instance.retainPopupCourseSegmentId,
      'orderId': instance.orderId,
      'drainageCourseLinkType': instance.drainageCourseLinkType,
      'directExit': instance.directExit,
      'lastSelected': instance.lastSelected,
      'recommendStyle': RecommendStyle.toJson(instance.recommendStyle),
    };

_$_LessonInfoModel _$$_LessonInfoModelFromJson(Map<String, dynamic> json) =>
    _$_LessonInfoModel(
      lessonId: json['lessonId'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonDesc: json['lessonDesc'] as String?,
      lessonCoverUrl: json['lessonCoverUrl'] as String?,
      segmentId: json['segmentId'] as int?,
    );

Map<String, dynamic> _$$_LessonInfoModelToJson(_$_LessonInfoModel instance) =>
    <String, dynamic>{
      'lessonId': instance.lessonId,
      'lessonName': instance.lessonName,
      'lessonDesc': instance.lessonDesc,
      'lessonCoverUrl': instance.lessonCoverUrl,
      'segmentId': instance.segmentId,
    };

_$_CourseBtnModel _$$_CourseBtnModelFromJson(Map<String, dynamic> json) =>
    _$_CourseBtnModel(
      type: json['type'] as int?,
      content: json['content'] as String?,
      router: json['router'] as String?,
    );

Map<String, dynamic> _$$_CourseBtnModelToJson(_$_CourseBtnModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'content': instance.content,
      'router': instance.router,
    };
