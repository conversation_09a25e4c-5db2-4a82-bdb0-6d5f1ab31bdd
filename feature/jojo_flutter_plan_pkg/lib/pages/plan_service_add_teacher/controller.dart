
import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/course_home_page_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/state.dart';

class PlanAddTeacherCtrl extends Cubit<PlanAddTeacherState> {
  final String? dataJsonString;

  PlanAddTeacherCtrl({required this.dataJsonString}) : super(PlanAddTeacherState(pageStatus: PageStatus.loading,dataJsonString: dataJsonString, popInfo: null),);

  /// 把json转化成对象
  void setPopInfo() {
    if (dataJsonString == null) {
      return;
    }
    PlanAddTeacherState newState = state.copyWith();
    newState.popInfo = PopInfo.fromJson(jsonDecode(dataJsonString!));
    newState.pageStatus = PageStatus.success;
    emit(newState);
  }
}
