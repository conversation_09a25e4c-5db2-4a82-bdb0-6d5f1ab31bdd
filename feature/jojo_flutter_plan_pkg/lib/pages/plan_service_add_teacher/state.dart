import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/course_home_page_data.dart';
class PlanAddTeacherState {
  PageStatus pageStatus;
  String? dataJsonString;
  PopInfo? popInfo;

  PlanAddTeacherState({required this.pageStatus, required this.dataJsonString, required this.popInfo,});

  PlanAddTeacherState copyWith() {
    return PlanAddTeacherState(
      pageStatus: pageStatus,
      dataJsonString: dataJsonString,
      popInfo: popInfo,
    );
  }
}
