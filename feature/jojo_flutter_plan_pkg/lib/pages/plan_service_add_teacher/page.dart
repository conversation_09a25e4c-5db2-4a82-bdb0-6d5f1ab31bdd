import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/view.dart';

class PlanAddTeacherPageModel extends BasePage {
  final String? dataJsonString;

  const PlanAddTeacherPageModel({Key? key, this.dataJsonString}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _PlanAddTeacherPageModelState();
}

class _PlanAddTeacherPageModelState extends BaseState<PlanAddTeacherPageModel> with BasicInitPage {

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => PlanAddTeacherCtrl(dataJsonString: widget.dataJsonString),
      child: <PERSON><PERSON><PERSON><PERSON><PlanAddTeacherCtrl, PlanAddTeacherState>(
          builder: (context, state) {
            return PlanAddTeacherPageView(state: state);
          }),
    );
  }
}
