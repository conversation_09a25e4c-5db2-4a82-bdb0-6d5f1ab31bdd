
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/state.dart';

class PlanAddTeacherPageView extends StatefulWidget {

  final PlanAddTeacherState state;

  const PlanAddTeacherPageView({super.key, required this.state,});

  @override
  State<StatefulWidget> createState() {
    return PlanAddTeacherPageViewState();
  }
}

class PlanAddTeacherPageViewState extends State<PlanAddTeacherPageView>{

  @override
  void initState() {
    super.initState();
    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': "课前准备_指导师弹窗",
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        PlanAddTeacherCtrl _ctrl = context.read<PlanAddTeacherCtrl>();
        _ctrl.setPopInfo();
      } catch (e) {
        l.e("课前准备添加指导师", "数据获取异常");
      }
    });
  }

  @override
  void dispose() {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': "课前准备_指导师_关闭"
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        primary: !JoJoRouter.isWindow,
        appBar: JoJoAppBar(
            title: widget.state.popInfo?.popupTitle ?? "",
            backgroundColor: Colors.transparent,
            centerTitle: true),
        body: Stack(
          children: [
            Container(
                color: Colors.white,
                child: SingleChildScrollView(
                    child: Column(
                      children: [
                        ImageNetworkCached(
                          imageUrl: widget.state.popInfo?.introduceImageList?.first ?? "",
                          fit: BoxFit.fitWidth,
                        ),
                      ],
                    )
                )
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: GestureDetector(
                onTap: () {
                  RunEnv.sensorsTrack('\$AppClick', {
                    '\$element_name': "课前准备_指导师_去添加"
                  });
                  if (widget.state.popInfo?.buttonRoute != null) {
                    RunEnv.jumpLink(widget.state.popInfo?.buttonRoute ?? "");
                  }
                },
                child: Container(
                    margin: EdgeInsets.only(bottom: 30.rdp),
                    alignment: Alignment.bottomCenter,
                    width: 280.rdp,
                    height: 44.rdp,
                    decoration: BoxDecoration(
                      color: HexColor('#FCDA00'),
                      borderRadius: BorderRadius.circular(30.rdp),
                    ),
                    child: Center(
                      child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        widget.state.popInfo?.buttonText ?? "",
                        style: TextStyle(
                          fontSize: 18.rdp,
                          fontWeight: FontWeight.w500,
                          color: HexColor("#544300"),
                        ),
                      ),
                    )),
              ),
            )
          ],
        ));
  }
}