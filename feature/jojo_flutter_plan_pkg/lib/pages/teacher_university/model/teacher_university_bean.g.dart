// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'teacher_university_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeacherUniversityBean _$$_TeacherUniversityBeanFromJson(
        Map<String, dynamic> json) =>
    _$_TeacherUniversityBean(
      assessmentList: (json['assessmentList'] as List<dynamic>?)
          ?.map((e) => Assessment.fromJson(e as Map<String, dynamic>))
          .toList(),
      tipsVo: json['tipsVo'] == null
          ? null
          : Tipsment.fromJson(json['tipsVo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TeacherUniversityBeanToJson(
        _$_TeacherUniversityBean instance) =>
    <String, dynamic>{
      'assessmentList': instance.assessmentList,
      'tipsVo': instance.tipsVo,
    };

_$_Assessment _$$_AssessmentFromJson(Map<String, dynamic> json) =>
    _$_Assessment(
      id: json['id'] as int?,
      text: json['text'] as String?,
      evaluationLevel: json['evaluationLevel'] as String?,
      evaluationStatus: json['evaluationStatus'] as int?,
      icon: json['icon'] as String?,
      bgImg: json['bgImg'] as String?,
      unLockTime: json['unLockTime'] as int?,
      expireTime: json['expireTime'] as int?,
      finishTime: json['finishTime'] as int?,
      order: json['order'] as int?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$_AssessmentToJson(_$_Assessment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'evaluationLevel': instance.evaluationLevel,
      'evaluationStatus': instance.evaluationStatus,
      'icon': instance.icon,
      'bgImg': instance.bgImg,
      'unLockTime': instance.unLockTime,
      'expireTime': instance.expireTime,
      'finishTime': instance.finishTime,
      'order': instance.order,
      'url': instance.url,
    };

_$_Tipsment _$$_TipsmentFromJson(Map<String, dynamic> json) => _$_Tipsment(
      tipsIcon: json['tipsIcon'] as String?,
      tipsText: json['tipsText'] as String?,
      tipsContent: json['tipsContent'] as String?,
      showAble: json['showAble'] as bool?,
    );

Map<String, dynamic> _$$_TipsmentToJson(_$_Tipsment instance) =>
    <String, dynamic>{
      'tipsIcon': instance.tipsIcon,
      'tipsText': instance.tipsText,
      'tipsContent': instance.tipsContent,
      'showAble': instance.showAble,
    };
