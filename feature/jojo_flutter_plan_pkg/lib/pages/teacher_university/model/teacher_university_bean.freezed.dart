// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'teacher_university_bean.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeacherUniversityBean _$TeacherUniversityBeanFromJson(
    Map<String, dynamic> json) {
  return _TeacherUniversityBean.fromJson(json);
}

/// @nodoc
mixin _$TeacherUniversityBean {
  List<Assessment>? get assessmentList => throw _privateConstructorUsedError;
  Tipsment? get tipsVo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeacherUniversityBeanCopyWith<TeacherUniversityBean> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeacherUniversityBeanCopyWith<$Res> {
  factory $TeacherUniversityBeanCopyWith(TeacherUniversityBean value,
          $Res Function(TeacherUniversityBean) then) =
      _$TeacherUniversityBeanCopyWithImpl<$Res, TeacherUniversityBean>;
  @useResult
  $Res call({List<Assessment>? assessmentList, Tipsment? tipsVo});

  $TipsmentCopyWith<$Res>? get tipsVo;
}

/// @nodoc
class _$TeacherUniversityBeanCopyWithImpl<$Res,
        $Val extends TeacherUniversityBean>
    implements $TeacherUniversityBeanCopyWith<$Res> {
  _$TeacherUniversityBeanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assessmentList = freezed,
    Object? tipsVo = freezed,
  }) {
    return _then(_value.copyWith(
      assessmentList: freezed == assessmentList
          ? _value.assessmentList
          : assessmentList // ignore: cast_nullable_to_non_nullable
              as List<Assessment>?,
      tipsVo: freezed == tipsVo
          ? _value.tipsVo
          : tipsVo // ignore: cast_nullable_to_non_nullable
              as Tipsment?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TipsmentCopyWith<$Res>? get tipsVo {
    if (_value.tipsVo == null) {
      return null;
    }

    return $TipsmentCopyWith<$Res>(_value.tipsVo!, (value) {
      return _then(_value.copyWith(tipsVo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeacherUniversityBeanCopyWith<$Res>
    implements $TeacherUniversityBeanCopyWith<$Res> {
  factory _$$_TeacherUniversityBeanCopyWith(_$_TeacherUniversityBean value,
          $Res Function(_$_TeacherUniversityBean) then) =
      __$$_TeacherUniversityBeanCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Assessment>? assessmentList, Tipsment? tipsVo});

  @override
  $TipsmentCopyWith<$Res>? get tipsVo;
}

/// @nodoc
class __$$_TeacherUniversityBeanCopyWithImpl<$Res>
    extends _$TeacherUniversityBeanCopyWithImpl<$Res, _$_TeacherUniversityBean>
    implements _$$_TeacherUniversityBeanCopyWith<$Res> {
  __$$_TeacherUniversityBeanCopyWithImpl(_$_TeacherUniversityBean _value,
      $Res Function(_$_TeacherUniversityBean) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assessmentList = freezed,
    Object? tipsVo = freezed,
  }) {
    return _then(_$_TeacherUniversityBean(
      assessmentList: freezed == assessmentList
          ? _value._assessmentList
          : assessmentList // ignore: cast_nullable_to_non_nullable
              as List<Assessment>?,
      tipsVo: freezed == tipsVo
          ? _value.tipsVo
          : tipsVo // ignore: cast_nullable_to_non_nullable
              as Tipsment?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeacherUniversityBean implements _TeacherUniversityBean {
  const _$_TeacherUniversityBean(
      {final List<Assessment>? assessmentList, this.tipsVo})
      : _assessmentList = assessmentList;

  factory _$_TeacherUniversityBean.fromJson(Map<String, dynamic> json) =>
      _$$_TeacherUniversityBeanFromJson(json);

  final List<Assessment>? _assessmentList;
  @override
  List<Assessment>? get assessmentList {
    final value = _assessmentList;
    if (value == null) return null;
    if (_assessmentList is EqualUnmodifiableListView) return _assessmentList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final Tipsment? tipsVo;

  @override
  String toString() {
    return 'TeacherUniversityBean(assessmentList: $assessmentList, tipsVo: $tipsVo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeacherUniversityBean &&
            const DeepCollectionEquality()
                .equals(other._assessmentList, _assessmentList) &&
            (identical(other.tipsVo, tipsVo) || other.tipsVo == tipsVo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_assessmentList), tipsVo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeacherUniversityBeanCopyWith<_$_TeacherUniversityBean> get copyWith =>
      __$$_TeacherUniversityBeanCopyWithImpl<_$_TeacherUniversityBean>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeacherUniversityBeanToJson(
      this,
    );
  }
}

abstract class _TeacherUniversityBean implements TeacherUniversityBean {
  const factory _TeacherUniversityBean(
      {final List<Assessment>? assessmentList,
      final Tipsment? tipsVo}) = _$_TeacherUniversityBean;

  factory _TeacherUniversityBean.fromJson(Map<String, dynamic> json) =
      _$_TeacherUniversityBean.fromJson;

  @override
  List<Assessment>? get assessmentList;
  @override
  Tipsment? get tipsVo;
  @override
  @JsonKey(ignore: true)
  _$$_TeacherUniversityBeanCopyWith<_$_TeacherUniversityBean> get copyWith =>
      throw _privateConstructorUsedError;
}

Assessment _$AssessmentFromJson(Map<String, dynamic> json) {
  return _Assessment.fromJson(json);
}

/// @nodoc
mixin _$Assessment {
  int? get id => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get evaluationLevel => throw _privateConstructorUsedError;
  int? get evaluationStatus => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get bgImg => throw _privateConstructorUsedError;
  int? get unLockTime => throw _privateConstructorUsedError;
  int? get expireTime => throw _privateConstructorUsedError;
  int? get finishTime => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AssessmentCopyWith<Assessment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AssessmentCopyWith<$Res> {
  factory $AssessmentCopyWith(
          Assessment value, $Res Function(Assessment) then) =
      _$AssessmentCopyWithImpl<$Res, Assessment>;
  @useResult
  $Res call(
      {int? id,
      String? text,
      String? evaluationLevel,
      int? evaluationStatus,
      String? icon,
      String? bgImg,
      int? unLockTime,
      int? expireTime,
      int? finishTime,
      int? order,
      String? url});
}

/// @nodoc
class _$AssessmentCopyWithImpl<$Res, $Val extends Assessment>
    implements $AssessmentCopyWith<$Res> {
  _$AssessmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? text = freezed,
    Object? evaluationLevel = freezed,
    Object? evaluationStatus = freezed,
    Object? icon = freezed,
    Object? bgImg = freezed,
    Object? unLockTime = freezed,
    Object? expireTime = freezed,
    Object? finishTime = freezed,
    Object? order = freezed,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluationLevel: freezed == evaluationLevel
          ? _value.evaluationLevel
          : evaluationLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluationStatus: freezed == evaluationStatus
          ? _value.evaluationStatus
          : evaluationStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImg: freezed == bgImg
          ? _value.bgImg
          : bgImg // ignore: cast_nullable_to_non_nullable
              as String?,
      unLockTime: freezed == unLockTime
          ? _value.unLockTime
          : unLockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AssessmentCopyWith<$Res>
    implements $AssessmentCopyWith<$Res> {
  factory _$$_AssessmentCopyWith(
          _$_Assessment value, $Res Function(_$_Assessment) then) =
      __$$_AssessmentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? text,
      String? evaluationLevel,
      int? evaluationStatus,
      String? icon,
      String? bgImg,
      int? unLockTime,
      int? expireTime,
      int? finishTime,
      int? order,
      String? url});
}

/// @nodoc
class __$$_AssessmentCopyWithImpl<$Res>
    extends _$AssessmentCopyWithImpl<$Res, _$_Assessment>
    implements _$$_AssessmentCopyWith<$Res> {
  __$$_AssessmentCopyWithImpl(
      _$_Assessment _value, $Res Function(_$_Assessment) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? text = freezed,
    Object? evaluationLevel = freezed,
    Object? evaluationStatus = freezed,
    Object? icon = freezed,
    Object? bgImg = freezed,
    Object? unLockTime = freezed,
    Object? expireTime = freezed,
    Object? finishTime = freezed,
    Object? order = freezed,
    Object? url = freezed,
  }) {
    return _then(_$_Assessment(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluationLevel: freezed == evaluationLevel
          ? _value.evaluationLevel
          : evaluationLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      evaluationStatus: freezed == evaluationStatus
          ? _value.evaluationStatus
          : evaluationStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      bgImg: freezed == bgImg
          ? _value.bgImg
          : bgImg // ignore: cast_nullable_to_non_nullable
              as String?,
      unLockTime: freezed == unLockTime
          ? _value.unLockTime
          : unLockTime // ignore: cast_nullable_to_non_nullable
              as int?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Assessment implements _Assessment {
  const _$_Assessment(
      {this.id,
      this.text,
      this.evaluationLevel,
      this.evaluationStatus,
      this.icon,
      this.bgImg,
      this.unLockTime,
      this.expireTime,
      this.finishTime,
      this.order,
      this.url});

  factory _$_Assessment.fromJson(Map<String, dynamic> json) =>
      _$$_AssessmentFromJson(json);

  @override
  final int? id;
  @override
  final String? text;
  @override
  final String? evaluationLevel;
  @override
  final int? evaluationStatus;
  @override
  final String? icon;
  @override
  final String? bgImg;
  @override
  final int? unLockTime;
  @override
  final int? expireTime;
  @override
  final int? finishTime;
  @override
  final int? order;
  @override
  final String? url;

  @override
  String toString() {
    return 'Assessment(id: $id, text: $text, evaluationLevel: $evaluationLevel, evaluationStatus: $evaluationStatus, icon: $icon, bgImg: $bgImg, unLockTime: $unLockTime, expireTime: $expireTime, finishTime: $finishTime, order: $order, url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Assessment &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.evaluationLevel, evaluationLevel) ||
                other.evaluationLevel == evaluationLevel) &&
            (identical(other.evaluationStatus, evaluationStatus) ||
                other.evaluationStatus == evaluationStatus) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.bgImg, bgImg) || other.bgImg == bgImg) &&
            (identical(other.unLockTime, unLockTime) ||
                other.unLockTime == unLockTime) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime) &&
            (identical(other.finishTime, finishTime) ||
                other.finishTime == finishTime) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      text,
      evaluationLevel,
      evaluationStatus,
      icon,
      bgImg,
      unLockTime,
      expireTime,
      finishTime,
      order,
      url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AssessmentCopyWith<_$_Assessment> get copyWith =>
      __$$_AssessmentCopyWithImpl<_$_Assessment>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AssessmentToJson(
      this,
    );
  }
}

abstract class _Assessment implements Assessment {
  const factory _Assessment(
      {final int? id,
      final String? text,
      final String? evaluationLevel,
      final int? evaluationStatus,
      final String? icon,
      final String? bgImg,
      final int? unLockTime,
      final int? expireTime,
      final int? finishTime,
      final int? order,
      final String? url}) = _$_Assessment;

  factory _Assessment.fromJson(Map<String, dynamic> json) =
      _$_Assessment.fromJson;

  @override
  int? get id;
  @override
  String? get text;
  @override
  String? get evaluationLevel;
  @override
  int? get evaluationStatus;
  @override
  String? get icon;
  @override
  String? get bgImg;
  @override
  int? get unLockTime;
  @override
  int? get expireTime;
  @override
  int? get finishTime;
  @override
  int? get order;
  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$_AssessmentCopyWith<_$_Assessment> get copyWith =>
      throw _privateConstructorUsedError;
}

Tipsment _$TipsmentFromJson(Map<String, dynamic> json) {
  return _Tipsment.fromJson(json);
}

/// @nodoc
mixin _$Tipsment {
  String? get tipsIcon => throw _privateConstructorUsedError;
  String? get tipsText => throw _privateConstructorUsedError;
  String? get tipsContent => throw _privateConstructorUsedError;
  bool? get showAble => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TipsmentCopyWith<Tipsment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TipsmentCopyWith<$Res> {
  factory $TipsmentCopyWith(Tipsment value, $Res Function(Tipsment) then) =
      _$TipsmentCopyWithImpl<$Res, Tipsment>;
  @useResult
  $Res call(
      {String? tipsIcon,
      String? tipsText,
      String? tipsContent,
      bool? showAble});
}

/// @nodoc
class _$TipsmentCopyWithImpl<$Res, $Val extends Tipsment>
    implements $TipsmentCopyWith<$Res> {
  _$TipsmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipsIcon = freezed,
    Object? tipsText = freezed,
    Object? tipsContent = freezed,
    Object? showAble = freezed,
  }) {
    return _then(_value.copyWith(
      tipsIcon: freezed == tipsIcon
          ? _value.tipsIcon
          : tipsIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      tipsText: freezed == tipsText
          ? _value.tipsText
          : tipsText // ignore: cast_nullable_to_non_nullable
              as String?,
      tipsContent: freezed == tipsContent
          ? _value.tipsContent
          : tipsContent // ignore: cast_nullable_to_non_nullable
              as String?,
      showAble: freezed == showAble
          ? _value.showAble
          : showAble // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TipsmentCopyWith<$Res> implements $TipsmentCopyWith<$Res> {
  factory _$$_TipsmentCopyWith(
          _$_Tipsment value, $Res Function(_$_Tipsment) then) =
      __$$_TipsmentCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tipsIcon,
      String? tipsText,
      String? tipsContent,
      bool? showAble});
}

/// @nodoc
class __$$_TipsmentCopyWithImpl<$Res>
    extends _$TipsmentCopyWithImpl<$Res, _$_Tipsment>
    implements _$$_TipsmentCopyWith<$Res> {
  __$$_TipsmentCopyWithImpl(
      _$_Tipsment _value, $Res Function(_$_Tipsment) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tipsIcon = freezed,
    Object? tipsText = freezed,
    Object? tipsContent = freezed,
    Object? showAble = freezed,
  }) {
    return _then(_$_Tipsment(
      tipsIcon: freezed == tipsIcon
          ? _value.tipsIcon
          : tipsIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      tipsText: freezed == tipsText
          ? _value.tipsText
          : tipsText // ignore: cast_nullable_to_non_nullable
              as String?,
      tipsContent: freezed == tipsContent
          ? _value.tipsContent
          : tipsContent // ignore: cast_nullable_to_non_nullable
              as String?,
      showAble: freezed == showAble
          ? _value.showAble
          : showAble // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Tipsment implements _Tipsment {
  const _$_Tipsment(
      {this.tipsIcon, this.tipsText, this.tipsContent, this.showAble});

  factory _$_Tipsment.fromJson(Map<String, dynamic> json) =>
      _$$_TipsmentFromJson(json);

  @override
  final String? tipsIcon;
  @override
  final String? tipsText;
  @override
  final String? tipsContent;
  @override
  final bool? showAble;

  @override
  String toString() {
    return 'Tipsment(tipsIcon: $tipsIcon, tipsText: $tipsText, tipsContent: $tipsContent, showAble: $showAble)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Tipsment &&
            (identical(other.tipsIcon, tipsIcon) ||
                other.tipsIcon == tipsIcon) &&
            (identical(other.tipsText, tipsText) ||
                other.tipsText == tipsText) &&
            (identical(other.tipsContent, tipsContent) ||
                other.tipsContent == tipsContent) &&
            (identical(other.showAble, showAble) ||
                other.showAble == showAble));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, tipsIcon, tipsText, tipsContent, showAble);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TipsmentCopyWith<_$_Tipsment> get copyWith =>
      __$$_TipsmentCopyWithImpl<_$_Tipsment>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TipsmentToJson(
      this,
    );
  }
}

abstract class _Tipsment implements Tipsment {
  const factory _Tipsment(
      {final String? tipsIcon,
      final String? tipsText,
      final String? tipsContent,
      final bool? showAble}) = _$_Tipsment;

  factory _Tipsment.fromJson(Map<String, dynamic> json) = _$_Tipsment.fromJson;

  @override
  String? get tipsIcon;
  @override
  String? get tipsText;
  @override
  String? get tipsContent;
  @override
  bool? get showAble;
  @override
  @JsonKey(ignore: true)
  _$$_TipsmentCopyWith<_$_Tipsment> get copyWith =>
      throw _privateConstructorUsedError;
}
