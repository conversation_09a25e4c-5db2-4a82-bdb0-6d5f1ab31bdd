// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dispatch_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

DispatchData _$DispatchDataFromJson(Map<String, dynamic> json) {
  return _DispatchData.fromJson(json);
}

/// @nodoc
mixin _$DispatchData {
  /// 领课链路纯享
  bool? get userEnjoyLinked => throw _privateConstructorUsedError;

  /// 领课链路纯享
  set userEnjoyLinked(bool? value) => throw _privateConstructorUsedError;

  /// 训练营纯享版本
  bool? get useEnjoyTrain => throw _privateConstructorUsedError;

  /// 训练营纯享版本
  set useEnjoyTrain(bool? value) => throw _privateConstructorUsedError;

  /// 日历选中 在学
  int? get selectBarIndex => throw _privateConstructorUsedError;

  /// 日历选中 在学
  set selectBarIndex(int? value) => throw _privateConstructorUsedError;

  /// 联系客服
  String? get contactRoute => throw _privateConstructorUsedError;

  /// 联系客服
  set contactRoute(String? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DispatchDataCopyWith<DispatchData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DispatchDataCopyWith<$Res> {
  factory $DispatchDataCopyWith(
          DispatchData value, $Res Function(DispatchData) then) =
      _$DispatchDataCopyWithImpl<$Res, DispatchData>;
  @useResult
  $Res call(
      {bool? userEnjoyLinked,
      bool? useEnjoyTrain,
      int? selectBarIndex,
      String? contactRoute});
}

/// @nodoc
class _$DispatchDataCopyWithImpl<$Res, $Val extends DispatchData>
    implements $DispatchDataCopyWith<$Res> {
  _$DispatchDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userEnjoyLinked = freezed,
    Object? useEnjoyTrain = freezed,
    Object? selectBarIndex = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_value.copyWith(
      userEnjoyLinked: freezed == userEnjoyLinked
          ? _value.userEnjoyLinked
          : userEnjoyLinked // ignore: cast_nullable_to_non_nullable
              as bool?,
      useEnjoyTrain: freezed == useEnjoyTrain
          ? _value.useEnjoyTrain
          : useEnjoyTrain // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectBarIndex: freezed == selectBarIndex
          ? _value.selectBarIndex
          : selectBarIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DispatchDataCopyWith<$Res>
    implements $DispatchDataCopyWith<$Res> {
  factory _$$_DispatchDataCopyWith(
          _$_DispatchData value, $Res Function(_$_DispatchData) then) =
      __$$_DispatchDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? userEnjoyLinked,
      bool? useEnjoyTrain,
      int? selectBarIndex,
      String? contactRoute});
}

/// @nodoc
class __$$_DispatchDataCopyWithImpl<$Res>
    extends _$DispatchDataCopyWithImpl<$Res, _$_DispatchData>
    implements _$$_DispatchDataCopyWith<$Res> {
  __$$_DispatchDataCopyWithImpl(
      _$_DispatchData _value, $Res Function(_$_DispatchData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userEnjoyLinked = freezed,
    Object? useEnjoyTrain = freezed,
    Object? selectBarIndex = freezed,
    Object? contactRoute = freezed,
  }) {
    return _then(_$_DispatchData(
      userEnjoyLinked: freezed == userEnjoyLinked
          ? _value.userEnjoyLinked
          : userEnjoyLinked // ignore: cast_nullable_to_non_nullable
              as bool?,
      useEnjoyTrain: freezed == useEnjoyTrain
          ? _value.useEnjoyTrain
          : useEnjoyTrain // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectBarIndex: freezed == selectBarIndex
          ? _value.selectBarIndex
          : selectBarIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      contactRoute: freezed == contactRoute
          ? _value.contactRoute
          : contactRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_DispatchData implements _DispatchData {
  _$_DispatchData(
      {required this.userEnjoyLinked,
      required this.useEnjoyTrain,
      required this.selectBarIndex,
      required this.contactRoute});

  factory _$_DispatchData.fromJson(Map<String, dynamic> json) =>
      _$$_DispatchDataFromJson(json);

  /// 领课链路纯享
  @override
  bool? userEnjoyLinked;

  /// 训练营纯享版本
  @override
  bool? useEnjoyTrain;

  /// 日历选中 在学
  @override
  int? selectBarIndex;

  /// 联系客服
  @override
  String? contactRoute;

  @override
  String toString() {
    return 'DispatchData(userEnjoyLinked: $userEnjoyLinked, useEnjoyTrain: $useEnjoyTrain, selectBarIndex: $selectBarIndex, contactRoute: $contactRoute)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DispatchDataCopyWith<_$_DispatchData> get copyWith =>
      __$$_DispatchDataCopyWithImpl<_$_DispatchData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_DispatchDataToJson(
      this,
    );
  }
}

abstract class _DispatchData implements DispatchData {
  factory _DispatchData(
      {required bool? userEnjoyLinked,
      required bool? useEnjoyTrain,
      required int? selectBarIndex,
      required String? contactRoute}) = _$_DispatchData;

  factory _DispatchData.fromJson(Map<String, dynamic> json) =
      _$_DispatchData.fromJson;

  @override

  /// 领课链路纯享
  bool? get userEnjoyLinked;

  /// 领课链路纯享
  set userEnjoyLinked(bool? value);
  @override

  /// 训练营纯享版本
  bool? get useEnjoyTrain;

  /// 训练营纯享版本
  set useEnjoyTrain(bool? value);
  @override

  /// 日历选中 在学
  int? get selectBarIndex;

  /// 日历选中 在学
  set selectBarIndex(int? value);
  @override

  /// 联系客服
  String? get contactRoute;

  /// 联系客服
  set contactRoute(String? value);
  @override
  @JsonKey(ignore: true)
  _$$_DispatchDataCopyWith<_$_DispatchData> get copyWith =>
      throw _privateConstructorUsedError;
}
