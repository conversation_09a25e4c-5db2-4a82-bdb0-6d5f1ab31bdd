import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/widget/spaced_flex_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/state.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

class MilestoneListView extends HookWidget {
  final int subjectType;
  final Color hexColor;
  const MilestoneListView(
      {super.key, required this.subjectType, required this.hexColor});

  Widget _buildMilestoneItem(
      BuildContext context, MilestoneInfo milestioneInfo) {
    return Container(
      width: 335.rdp,
      height: 168.rdp,
      padding: EdgeInsets.symmetric(vertical: 14.rdp),
      decoration: BoxDecoration(
          color: context.appColors.colorVariant1(hexColor),
          border: Border.all(
              color: context.appColors.colorVariant2(hexColor), width: 1.rdp),
          borderRadius: BorderRadius.circular(24.rdp)),
      child: Stack(
        children: [
          Positioned(
            left: 22.rdp,
            top: 0.rdp,
            child: Row(
              children: [
                Text(
                  milestioneInfo.name,
                  style: context.textstyles.bodyTextEmphasis.pf.copyWith(
                    fontSize: 18.rdp,
                    fontStyle: FontStyle.normal,
                    color: context.appColors.colorVariant6(hexColor),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(
                  width: 2.rdp,
                ),
                SvgAssetWeb(
                  assetName:
                      AssetsSvg.LEARNING_INCENTIVES_ICON_MILESTIONE_TITLE,
                  height: 30.rdp,
                  width: 30.rdp,
                  package: Config.package,
                )
              ],
            ),
          ),
          Positioned(
              right: -20.rdp,
              bottom: 9.rdp,
              child: SvgAssetWeb(
                  assetName: AssetsSvg.LEARNING_INCENTIVES_ICON_CLAIMED_TAG,
                  height: 102.rdp,
                  width: 98.rdp,
                  color: context.appColors.jColorOrange5,
                  package: Config.package)),
          Positioned(
              bottom: 0.rdp,
              left: 22.rdp,
              child: SpacedRow(
                spacing: 8.rdp,
                children: milestioneInfo.rewardList
                    .map((e) => _buildBadgeWidget(context, e))
                    .toList(),
              ))
        ],
      ),
    );
  }

  Widget _buildBadgeWidget(BuildContext context, RewardInfo info) {
    return SizedBox(
      width: 80.rdp,
      height: 102.rdp,
      child: Column(children: [
        Container(
          width: 80.rdp,
          height: 80.rdp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(18.rdp),
          ),
          child:
              _buildNetImage(context:context,height: 61.5.rdp, width: 61.5.rdp, url: info.img),
        ),
        SizedBox(
          height: 4.rdp,
        ),
        Text(
          info.name,
          style: context.textstyles.bodyText.pf.copyWith(
            fontSize: 12.rdp,
          ),
        )
      ]),
    );
  }

  Widget _buildNetImage(
      {required BuildContext context,required double height, required double width, required String url}) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      memCacheWidth: width.ceil() * MediaQuery.of(context).devicePixelRatio.toInt(),
      memCacheHeight: height.ceil() * MediaQuery.of(context).devicePixelRatio.toInt(),
      placeholder: (context, url) => ImageAssetWeb(
        assetName: AssetsImg.LEARNING_INCENTIVES_MEDAL_PERSIST_LOADING,
        height: height,
        width: width,
        package: Config.package,
      ),
      errorWidget: (context, url, error) => ImageAssetWeb(
        assetName: AssetsImg.LEARNING_INCENTIVES_MEDAL_PERSIST_ERROR,
        height: height,
        width: width,
        package: Config.package,
      ),
    );
  }

  Widget buildEmpty(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ImageAssetWeb(
          assetName: AssetsImg.LEARNING_INCENTIVES_ICON_EMPTY,
          height: 200.rdp,
          width: 200.rdp,
          fit: BoxFit.contain,
          package: Config.package,
        ),
        Text(
          S.of(context).milestoneEmpty,
          style: context.textstyles.bodyText.pf
              .copyWith(fontSize: 16.rdp, color: context.appColors.jColorGray4),
        ),
        SizedBox(
          height: 14.rdp,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final ctl = context.read<MilestoneListController>();
    ctl.trackingViewScreen();
    final milestoneList = ctl.state.milestoneList;
    return Scaffold(
      appBar: JoJoAppBar(
        title: S.of(context).milestoneAward,
      ),
      body: Container(
        padding: EdgeInsets.only(top: 14.rdp),
        constraints: const BoxConstraints.expand(),
        child: milestoneList.isEmpty
            ? buildEmpty(context)
            : SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: SpacedColumn(
                  spacing: 14.rdp,
                  children: milestoneList
                      .map((e) => _buildMilestoneItem(context, e))
                      .toList(),
                ),
              ),
      ),
    );
  }
}
