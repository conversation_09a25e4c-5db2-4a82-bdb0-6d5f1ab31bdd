import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/color_util.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/view.dart';

class MilestoneListPage extends BasePage {
  final int subjectType;
  final int loadingScene;
  const MilestoneListPage(
      {super.key, required this.subjectType, required this.loadingScene});

  @override
  State<MilestoneListPage> createState() => _MilestoneListPageState();
}

class _MilestoneListPageState extends BaseState<MilestoneListPage>
    with BasicInitPage {
  late MilestoneListController controller;

  @override
  void initState() {
    controller = MilestoneListController(subjectType: widget.subjectType);
    super.initState();
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => controller,
      child: BlocBuilder<MilestoneListController, MilestoneListState>(
          builder: (context, state) {
        return Container(
          color: Colors.white,
          child: JoJoPageLoadingV25(
            status: state.status,
            exception: state.exception,
            scene: PageScene.fromValue(widget.loadingScene) ?? PageScene.getDefault(),
            child: MilestoneListView(
                subjectType: widget.subjectType,
                hexColor: HexColor.fromHex(state.subjectColor)),
            retry: () {
              controller.getMilestoneList(subjectType: widget.subjectType);
            },
          ),
        );
      }),
    );
  }
}
