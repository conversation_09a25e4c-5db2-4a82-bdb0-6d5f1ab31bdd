import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

class MilestoneListState {
  PageStatus status;
  Exception? exception;
  List<MilestoneInfo> milestoneList;
  String subjectColor;
  MilestoneListState(
      {this.status = PageStatus.loading,
      this.exception,
      this.milestoneList = const [],
      this.subjectColor = ""});

  MilestoneListState copyWith({
    PageStatus? status,
    Exception? exception,
    List<MilestoneInfo>? milestoneList,
    String? subjectColor,
  }) {
    return MilestoneListState(
      status: status ?? this.status,
      exception: exception ?? this.exception,
      milestoneList: milestoneList ?? this.milestoneList,
      subjectColor: subjectColor ?? this.subjectColor,
    );
  }
}

class MilestoneInfo {
  String name;
  List<RewardInfo> rewardList;
  MilestoneInfo({this.name = "", this.rewardList = const []});
}

class RewardInfo {
  String name;
  String img;
  RewardInfo({this.name = "", this.img = ""});
}
