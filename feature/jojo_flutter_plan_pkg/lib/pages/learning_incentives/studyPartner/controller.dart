import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/ext.dart';
import 'converter/study_buddy_converter.dart';
import 'service/study_buddy_sevice.dart';
import 'state.dart';

class StudyPartnerController extends Cubit<StudyBuddyUiState> {
  final pageSize = 10;
  final String classKey;
  final StudyBuddyConverter _converter = StudyBuddyConverter();
  final StudyBuddySevice service;

  StudyPartnerController({required this.classKey, required this.service})
      : super(StudyBuddyUiState()) {
    loadData();
  }

  Future<void> loadData() async {
    try {
      final result = await service.getStudyBuddyList(
          classKey: classKey, offset: 0, size: pageSize);
      final uiState = generateUiState(true, result);
      updateUiState(uiState);
    } catch (e) {
      final error = e is Exception ? e : Exception(e.toString());
      updateUiState(
          state.copyWith(pageStatus: PageStatus.error, exception: error));
    }
  }

  Future<void> loadMore() async {
    final preState = state;
    if (preState.hasNext == false) {
      return;
    }
    final nextResult = await service.getStudyBuddyList(
        classKey: classKey, size: pageSize, offset: preState.offset);
    final newState = generateUiState(false, nextResult);
    updateUiState(newState);
  }

  void updateUiState(StudyBuddyUiState state) {
    safeEmit(state);
  }

  List<BuddyItemUiState> generateItemUiState(List<StudyBuddyModel> models) {
    return _converter.convertToItemStates(models);
  }

  StudyBuddyUiState generateUiState(bool isRefresh, StudyBuddy studyBuddy) {
    final buddyItemUiStates = generateItemUiState(studyBuddy.list ?? []);
    final preBuddyItemUiStates = state.buddyItemUiStates;
    final newBuddyItemUiStates = isRefresh
        ? buddyItemUiStates
        : [...preBuddyItemUiStates, ...buddyItemUiStates];
    final hasNext = studyBuddy.offset != -1;
    return StudyBuddyUiState(
        pageStatus: PageStatus.success,
        hasNext: hasNext,
        offset: studyBuddy.offset ?? 0,
        buddyItemUiStates: newBuddyItemUiStates);
  }

  String getKudos(int kudosCount) {
    if (kudosCount < 1000) {
      return kudosCount.toString();
    } else {
      int kValue = kudosCount ~/ 1000;
      return '${kValue}k';
    }
  }

  Future<void> postLikeRecords(int index) async {
    final itemList = state.buddyItemUiStates;
    final action = itemList[index].canLike == 1 ? '1' : '0';
    final targetId = itemList[index].targetId;
    final newState = generateUiSateWithLikeAction(itemList, index);
    updateUiState(newState); //考虑数据刷新问题这儿先手动修改数据在进行点赞行为更改
    await service.postLikeRecords(targetId: targetId, action: action);
  }

  void startClickLikeAnima(int index) {
    final newState =
        generateUiStateWithAnimaInitEnd(state.buddyItemUiStates, index);
    safeEmit(newState);
  }

  StudyBuddyUiState generateUiStateWithAnimaInitEnd(
      List<BuddyItemUiState> itemList, int index) {
    final BuddyItemUiState item = itemList[index];
    final newCanLike = item.canLike == 1 ? 0 : 1;
    final preItem = item.copyWhith(canLike: newCanLike);
    itemList[index] = preItem;
    return state.copyWith(buddyItemUiStates: itemList);
  }

  StudyBuddyUiState generateUiSateWithLikeAction(
      List<BuddyItemUiState> itemList, int index) {
    final BuddyItemUiState item = itemList[index];
    final kudosCout = item.kudosCount;
    final newKudosCount = item.canLike == 1 ? kudosCout + 1 : kudosCout - 1;
    final playAnima = item.canLike == 1;
    int newCanLike = item.canLike;
    if (newCanLike == 0) {
      //由于取消点赞没有动画完成事件无法触发 canlike 状态修改这儿主动修改
      newCanLike = 1;
    }
    final preItem = item.copyWhith(
        canLike: newCanLike,
        kudosCount: newKudosCount,
        playClickLikeAnima: playAnima);
    itemList[index] = preItem;
    return state.copyWith(buddyItemUiStates: itemList);
  }

  void restClickLikeAnima(int index) {
    final newState = restClickLikeAnimaState(state.buddyItemUiStates, index);
    safeEmit(newState);
  }

  StudyBuddyUiState restClickLikeAnimaState(
      List<BuddyItemUiState> itemList, int index) {
    final BuddyItemUiState item = itemList[index];
    final playAnima = item.canLike == 1;
    final preItem = item.copyWhith(playClickLikeAnima: playAnima);
    itemList[index] = preItem;
    return state.copyWith(buddyItemUiStates: itemList);
  }
}
