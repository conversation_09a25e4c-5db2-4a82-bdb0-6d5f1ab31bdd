import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';
import 'package:jojo_flutter_plan_pkg/service/learning_incentives_api.dart';

class StudyBuddySevice {
  final api = learningIncentivesApi;

  Future<StudyBuddy> getStudyBuddyList(
      {required String classKey,required int size,required int offset}) async {
    final response = await api.getStudyBuddyList(classKey, size,
        offset);
    return response;
  }

  Future<void> postLikeRecords({required int targetId, required String action}) async {
     await api.postLikeRecord(targetId,  action);
  }
}
