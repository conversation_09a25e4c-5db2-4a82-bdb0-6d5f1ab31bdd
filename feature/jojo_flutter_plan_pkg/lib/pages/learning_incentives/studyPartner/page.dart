import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/service/study_buddy_sevice.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/view.dart';

class StudyPartnerPage extends BasePage {
  final int loadingScene;
  final String classKey;

  const StudyPartnerPage(
      {Key? key, required this.loadingScene, required this.classKey})
      : super(key: key);

  @override
  State<StudyPartnerPage> createState() => _StudyPartnerPageState();
}

class _StudyPartnerPageState extends BaseState<StudyPartnerPage>
    with BasicInitPage {
  late StudyPartnerController controller;
  final service = StudyBuddySevice();
  @override
  void initState() {
    controller =
        StudyPartnerController(classKey: widget.classKey, service: service);
    super.initState();
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => controller,
      child: BlocBuilder<StudyPartnerController, StudyBuddyUiState>(
          builder: (context, state) {
        return Container(
          color: Colors.white,
          child: JoJoPageLoadingV25(
            hideProgress: true,
            status: state.pageStatus,
            exception: state.exception,
            scene: PageScene.fromValue(widget.loadingScene) ??
                PageScene.getDefault(),
            child: StudyPartnerListView(
              controller: controller,
              state: state,
            ),
            retry: () {
              controller.loadData();
            },
          ),
        );
      }),
    );
  }
}
