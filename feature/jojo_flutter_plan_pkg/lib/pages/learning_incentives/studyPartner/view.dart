import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/%20widget/study_pratner_item_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';

class StudyPartnerListView extends StatefulWidget {
  final StudyPartnerController controller;
  final StudyBuddyUiState state;

  const StudyPartnerListView(
      {super.key, required this.controller, required this.state});

  @override
  State<StudyPartnerListView> createState() => _StudyPartnerListViewState();
}

class _StudyPartnerListViewState extends State<StudyPartnerListView> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  void _onRefresh() async {
    await widget.controller.loadData();
    _refreshController.refreshCompleted();
  }

  void _loadMore() async {
    await widget.controller.loadMore();
    _refreshController.loadComplete();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final items = widget.state.buddyItemUiStates;
    return Scaffold(
      appBar: JoJoAppBar(
        title: S.of(context).studyPartner,
      ),
      body: Container(
        constraints: const BoxConstraints.expand(),
        child: PullOrRefresh(
          onRefresh: _onRefresh,
          onLoading: _loadMore,
          refreshController: _refreshController,
          child: ListView.separated(
              itemBuilder: (context, index) {
                return ClickWidget(
                    type: ClickType.debounce,
                    onTap: () {
                      if (items[index].router.isNotEmpty) {
                        RunEnv.sensorsTrack('\$AppClick', {
                          '\$element_name': "学伴页面_点击个人形象",
                        });
                        RunEnv.jumpLink(items[index].router);
                      }
                    },
                    child: StudyPratnerItemWidget(
                        controller: widget.controller,
                        item: items[index],
                        index: index));
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 16.rdp,
                );
              },
              itemCount: items.length),
        ),
      ),
    );
  }
}
