import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/outline_text.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

class StudyPratnerItemWidget extends StatefulWidget {
  final StudyPartnerController controller;
  final BuddyItemUiState item;
  final int index;
  const StudyPratnerItemWidget(
      {super.key,
      required this.controller,
      required this.item,
      required this.index});

  @override
  State<StudyPratnerItemWidget> createState() => _StudyPratnerItemWidgetState();
}

class _StudyPratnerItemWidgetState extends State<StudyPratnerItemWidget> {
  late JoJoSpineAnimationController _controller;
  late bool isPlaying;
  @override
  void initState() {
    _controller = JoJoSpineAnimationController();
    isPlaying = false;
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildClickLike() {
    return SizedBox(
      height: 80.rdp,
      width: 80.rdp,
      child: JoJoSpineAnimationWidget(
        AssetsSpine.SPINE_FLOWER_CLIKC_ATKAS,
        AssetsSpine.SPINE_FLOWER_CLIKC_SKEL,
        LoadMode.assets,
        _controller,
        package: Config.package,
        onInitialized: (controller) {
          if (mounted) {
            widget.controller.startClickLikeAnima(widget.index);
            _controller.playAnimation(JoJoSpineAnimation(
              animaitonName: "click",
              trackIndex: 0,
              loop: false,
              listener: (type) {
                if (type == AnimationEventType.complete) {
                  isPlaying = false;
                }
              },
            ));
          }
        },
      ),
    );
  }

  Widget _buildKudosWidget(BuildContext context, BuddyItemUiState item,
      {void Function()? onTap}) {
    return ClickWidget(
      type: ClickType.debounce,
      onTap: () {
        onTap?.call();
      },
      child: Container(
        clipBehavior: Clip.none,
        height: 24.rdp,
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 10.rdp, right: 14.rdp),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14.rdp),
            border: Border.all(color: context.appColors.mainColor)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              clipBehavior: Clip.none,
              height: 24.rdp,
              width: 24.rdp,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  ImageAssetWeb(
                      height: 24.rdp,
                      width: 24.rdp,
                      assetName: widget.item.canLike == 1
                          ? AssetsImg.LEARNING_INCENTIVES_ICON_NO_KOUDS
                          : AssetsImg.LEARNING_INCENTIVES_ICON_KOUDS,
                      package: Config.package),
                  if (widget.item.playClickLikeAnima)
                    Positioned(
                      top: -28.rdp, //
                      left: -24.rdp, // 向左偏移
                      child: _buildClickLike(),
                    )
                ],
              ),
            ),
            Container(
              height: 24.rdp,
              padding: EdgeInsets.only(top: 1.rdp),
              alignment: Alignment.center,
              child: Text(
                widget.controller.getKudos(item.kudosCount),
                textAlign: TextAlign.center,
                style: getMbrTextStyle(context).copyWith(
                    color: context.appColors.jColorGray6.withOpacity(0.7)),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildStudyCountDay(BuildContext context, BuddyItemUiState item) {
    return SizedBox(
      height: 36.rdp,
      width: 36.rdp,
      child: Stack(
        children: [
          SvgAssetWeb(
              width: 36.rdp,
              height: 36.rdp,
              assetName: AssetsSvg.LEARNING_INCENTIVES_ICON_FIRE_STUDY_DAY,
              package: Config.package),
          Positioned(
            top: 6.rdp,
            child: Container(
                alignment: Alignment.topCenter,
                width: 36.rdp,
                child: OutlineText("${item.studyCount}",
                    textAlign: TextAlign.center,
                    textStyle: context.textstyles.bodyText.mrB.copyWith(
                      fontSize: 18.rdp,
                      fontStyle: FontStyle.normal,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'MohrRounded_Bold',
                      package: 'jojo_flutter_base',
                    ),
                    strokeColor: HexColor("#ED601A"),
                    fillColor: Colors.white,
                    strokeWidth: 1.rdp)),
          )
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context, BuddyItemUiState item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          item.name,
          style: context.textstyles.bodyText.pf
              .copyWith(fontSize: 16.rdp, color: context.appColors.jColorGray6),
        ),
        Text(
          S.of(context).stduyAllCount(item.studyAllCount),
          style: context.textstyles.smallestText.pf
              .copyWith(color: context.appColors.jColorGray4),
        )
      ],
    );
  }

  Widget _buildPersonImage(BuildContext context, String url) {
    return ImageNetworkCached(
      imageUrl: url,
      height: 120.rdp,
      width: 100.rdp,
      fit: BoxFit.contain,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildBodyWidget(context);
  }

  Container _buildBodyWidget(BuildContext context) {
    return Container(
      height: 148.rdp,
      padding: EdgeInsets.all(14.rdp),
      margin: EdgeInsets.symmetric(horizontal: 14.rdp),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.rdp),
          border: Border.all(color: context.appColors.jColorGray3)),
      child: Row(children: [
        _buildPersonImage(context, widget.item.personalImage),
        SizedBox(
          width: 14.rdp,
        ),
        Expanded(
          flex: 1,
          child: Container(
            padding: EdgeInsets.only(top: 14.rdp),
            height: 120.rdp,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                SizedBox(
                  height: 46.rdp,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                          flex: 1, child: _buildUserInfo(context, widget.item)),
                      _buildStudyCountDay(context, widget.item)
                    ],
                  ),
                ),
                Positioned(
                  bottom: 22.rdp,
                  child: SizedBox(
                    height: 24.rdp,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ImageAssetWeb(
                            height: 24.rdp,
                            width: 24.rdp,
                            assetName:
                                AssetsImg.LEARNING_INCENTIVES_ICON_STUDY_BADGE,
                            package: Config.package),
                        Text(
                          '${widget.item.achievementCount}',
                          style: getMbrTextStyle(context).copyWith(
                              color: context.appColors.jColorGray6
                                  .withOpacity(0.7)),
                        )
                      ],
                    ),
                  ),
                ),
                Positioned(
                    right: 0.rdp,
                    bottom: 0.rdp,
                    child: _buildKudosWidget(
                      context,
                      widget.item,
                      onTap: () {
                        if (isPlaying) {
                          return;
                        }
                        RunEnv.sensorsTrack('\$AppClick', {
                          '\$element_name': "学伴页面_点击送花",
                        });
                        widget.controller.postLikeRecords(widget.index);
                      },
                    )),
              ],
            ),
          ),
        )
      ]),
    );
  }

  TextStyle getMbrTextStyle(BuildContext context) {
    return context.textstyles.smallestText.copyWith(
      fontStyle: FontStyle.normal,
      fontWeight: FontWeight.w400,
      fontFamily: 'MohrRounded',
      package: Config.package,
    );
  }
}
