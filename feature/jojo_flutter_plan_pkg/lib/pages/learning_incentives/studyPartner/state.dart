import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

class StudyBuddyUiState {
  PageStatus pageStatus;
  Exception? exception;
  bool hasNext;
  int offset;
  List<BuddyItemUiState> buddyItemUiStates = [];

  StudyBuddyUiState({
    this.pageStatus = PageStatus.loading,
    this.hasNext = true,
    this.offset = 0,
    this.exception,
    this.buddyItemUiStates = const [],
  });

  StudyBuddyUiState copyWith({
    PageStatus? pageStatus,
    Exception? exception,
    List<BuddyItemUiState>? buddyItemUiStates,
    bool? hasNext,
    int? offset,
  }) {
    return StudyBuddyUiState(
      pageStatus: pageStatus ?? this.pageStatus,
      exception: exception ?? this.exception,
      buddyItemUiStates: buddyItemUiStates ?? this.buddyItemUiStates,
      hasNext: hasNext ?? this.hasNext,
      offset: offset ?? this.offset,
    );
  }
}

class BuddyItemUiState {
  String name;
  int studyAllCount;
  int studyCount;
  int achievementCount;
  String personalImage;
  int canLike;
  int kudosCount;
  String router;
  int targetId;
  bool playClickLikeAnima;

  BuddyItemUiState({
    this.name = "",
    this.studyAllCount = 0,
    this.studyCount = 0,
    this.achievementCount = 0,
    this.personalImage = "",
    this.canLike = 0,
    this.kudosCount = 1,
    this.router = "",
    this.targetId = 0,
    this.playClickLikeAnima = false,
  });

  BuddyItemUiState copyWhith({
    String? name,
    int? studyAllCount,
    int? studyCount,
    int? achievementCount,
    String? personalImage,
    int? canLike,
    int? kudosCount,
    String? router,
    int? targetId,
    bool? playClickLikeAnima,
  }) {
    return BuddyItemUiState(
      name: name ?? this.name,
      studyAllCount: studyAllCount ?? this.studyAllCount,
      studyCount: studyCount ?? this.studyCount,
      achievementCount: achievementCount ?? this.achievementCount,
      personalImage: personalImage ?? this.personalImage,
      canLike: canLike ?? this.canLike,
      kudosCount: kudosCount ?? this.kudosCount,
      router: router ?? this.router,
      targetId: targetId ?? this.targetId,
      playClickLikeAnima: playClickLikeAnima?? this.playClickLikeAnima,
    );
  }
}
