// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'study_buddy_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_StudyBuddy _$$_StudyBuddyFromJson(Map<String, dynamic> json) =>
    _$_StudyBuddy(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => StudyBuddyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      offset: json['offset'] as int?,
      size: json['size'] as int?,
    );

Map<String, dynamic> _$$_StudyBuddyToJson(_$_StudyBuddy instance) =>
    <String, dynamic>{
      'list': instance.list,
      'offset': instance.offset,
      'size': instance.size,
    };

_$_StudyBuddyModel _$$_StudyBuddyModelFromJson(Map<String, dynamic> json) =>
    _$_StudyBuddyModel(
      nickName: json['nickName'] as String?,
      medal: json['medal'] as int?,
      continuousDays: json['continuousDays'] as int?,
      studyDays: json['studyDays'] as int?,
      like: json['like'] as int?,
      likeDescription: json['likeDescription'] as String?,
      img: json['img'] as String?,
      canLike: json['canLike'] as int?,
      url: json['url'] as String?,
      targetId: json['targetId'] as int?,
    );

Map<String, dynamic> _$$_StudyBuddyModelToJson(_$_StudyBuddyModel instance) =>
    <String, dynamic>{
      'nickName': instance.nickName,
      'medal': instance.medal,
      'continuousDays': instance.continuousDays,
      'studyDays': instance.studyDays,
      'like': instance.like,
      'likeDescription': instance.likeDescription,
      'img': instance.img,
      'canLike': instance.canLike,
      'url': instance.url,
      'targetId': instance.targetId,
    };
