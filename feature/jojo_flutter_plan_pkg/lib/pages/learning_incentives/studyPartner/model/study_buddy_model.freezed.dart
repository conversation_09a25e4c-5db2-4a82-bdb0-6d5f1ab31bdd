// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'study_buddy_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

StudyBuddy _$StudyBuddyFromJson(Map<String, dynamic> json) {
  return _StudyBuddy.fromJson(json);
}

/// @nodoc
mixin _$StudyBuddy {
  List<StudyBuddyModel>? get list => throw _privateConstructorUsedError;
  int? get offset => throw _privateConstructorUsedError;
  int? get size => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StudyBuddyCopyWith<StudyBuddy> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StudyBuddyCopyWith<$Res> {
  factory $StudyBuddyCopyWith(
          StudyBuddy value, $Res Function(StudyBuddy) then) =
      _$StudyBuddyCopyWithImpl<$Res, StudyBuddy>;
  @useResult
  $Res call({List<StudyBuddyModel>? list, int? offset, int? size});
}

/// @nodoc
class _$StudyBuddyCopyWithImpl<$Res, $Val extends StudyBuddy>
    implements $StudyBuddyCopyWith<$Res> {
  _$StudyBuddyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
    Object? offset = freezed,
    Object? size = freezed,
  }) {
    return _then(_value.copyWith(
      list: freezed == list
          ? _value.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<StudyBuddyModel>?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StudyBuddyCopyWith<$Res>
    implements $StudyBuddyCopyWith<$Res> {
  factory _$$_StudyBuddyCopyWith(
          _$_StudyBuddy value, $Res Function(_$_StudyBuddy) then) =
      __$$_StudyBuddyCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<StudyBuddyModel>? list, int? offset, int? size});
}

/// @nodoc
class __$$_StudyBuddyCopyWithImpl<$Res>
    extends _$StudyBuddyCopyWithImpl<$Res, _$_StudyBuddy>
    implements _$$_StudyBuddyCopyWith<$Res> {
  __$$_StudyBuddyCopyWithImpl(
      _$_StudyBuddy _value, $Res Function(_$_StudyBuddy) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
    Object? offset = freezed,
    Object? size = freezed,
  }) {
    return _then(_$_StudyBuddy(
      list: freezed == list
          ? _value._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<StudyBuddyModel>?,
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StudyBuddy implements _StudyBuddy {
  const _$_StudyBuddy(
      {final List<StudyBuddyModel>? list, this.offset, this.size})
      : _list = list;

  factory _$_StudyBuddy.fromJson(Map<String, dynamic> json) =>
      _$$_StudyBuddyFromJson(json);

  final List<StudyBuddyModel>? _list;
  @override
  List<StudyBuddyModel>? get list {
    final value = _list;
    if (value == null) return null;
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? offset;
  @override
  final int? size;

  @override
  String toString() {
    return 'StudyBuddy(list: $list, offset: $offset, size: $size)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StudyBuddy &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_list), offset, size);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StudyBuddyCopyWith<_$_StudyBuddy> get copyWith =>
      __$$_StudyBuddyCopyWithImpl<_$_StudyBuddy>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StudyBuddyToJson(
      this,
    );
  }
}

abstract class _StudyBuddy implements StudyBuddy {
  const factory _StudyBuddy(
      {final List<StudyBuddyModel>? list,
      final int? offset,
      final int? size}) = _$_StudyBuddy;

  factory _StudyBuddy.fromJson(Map<String, dynamic> json) =
      _$_StudyBuddy.fromJson;

  @override
  List<StudyBuddyModel>? get list;
  @override
  int? get offset;
  @override
  int? get size;
  @override
  @JsonKey(ignore: true)
  _$$_StudyBuddyCopyWith<_$_StudyBuddy> get copyWith =>
      throw _privateConstructorUsedError;
}

StudyBuddyModel _$StudyBuddyModelFromJson(Map<String, dynamic> json) {
  return _StudyBuddyModel.fromJson(json);
}

/// @nodoc
mixin _$StudyBuddyModel {
  /// 昵称
  String? get nickName => throw _privateConstructorUsedError;

  /// 勋章
  int? get medal => throw _privateConstructorUsedError;

  /// 连续学习天数
  int? get continuousDays => throw _privateConstructorUsedError;

  /// 学习天数
  int? get studyDays => throw _privateConstructorUsedError;

  /// 点赞数量
  int? get like => throw _privateConstructorUsedError;

  /// 点赞数量描述
  String? get likeDescription => throw _privateConstructorUsedError;

  /// 头像
  String? get img => throw _privateConstructorUsedError;

  /// 是否可点赞，1:可以，0:不可以
  int? get canLike => throw _privateConstructorUsedError;

  /// 个人主页URL
  String? get url => throw _privateConstructorUsedError;
  int? get targetId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StudyBuddyModelCopyWith<StudyBuddyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StudyBuddyModelCopyWith<$Res> {
  factory $StudyBuddyModelCopyWith(
          StudyBuddyModel value, $Res Function(StudyBuddyModel) then) =
      _$StudyBuddyModelCopyWithImpl<$Res, StudyBuddyModel>;
  @useResult
  $Res call(
      {String? nickName,
      int? medal,
      int? continuousDays,
      int? studyDays,
      int? like,
      String? likeDescription,
      String? img,
      int? canLike,
      String? url,
      int? targetId});
}

/// @nodoc
class _$StudyBuddyModelCopyWithImpl<$Res, $Val extends StudyBuddyModel>
    implements $StudyBuddyModelCopyWith<$Res> {
  _$StudyBuddyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? medal = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? img = freezed,
    Object? canLike = freezed,
    Object? url = freezed,
    Object? targetId = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as int?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      canLike: freezed == canLike
          ? _value.canLike
          : canLike // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_StudyBuddyModelCopyWith<$Res>
    implements $StudyBuddyModelCopyWith<$Res> {
  factory _$$_StudyBuddyModelCopyWith(
          _$_StudyBuddyModel value, $Res Function(_$_StudyBuddyModel) then) =
      __$$_StudyBuddyModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickName,
      int? medal,
      int? continuousDays,
      int? studyDays,
      int? like,
      String? likeDescription,
      String? img,
      int? canLike,
      String? url,
      int? targetId});
}

/// @nodoc
class __$$_StudyBuddyModelCopyWithImpl<$Res>
    extends _$StudyBuddyModelCopyWithImpl<$Res, _$_StudyBuddyModel>
    implements _$$_StudyBuddyModelCopyWith<$Res> {
  __$$_StudyBuddyModelCopyWithImpl(
      _$_StudyBuddyModel _value, $Res Function(_$_StudyBuddyModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? medal = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? img = freezed,
    Object? canLike = freezed,
    Object? url = freezed,
    Object? targetId = freezed,
  }) {
    return _then(_$_StudyBuddyModel(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as int?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      canLike: freezed == canLike
          ? _value.canLike
          : canLike // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_StudyBuddyModel implements _StudyBuddyModel {
  const _$_StudyBuddyModel(
      {this.nickName,
      this.medal,
      this.continuousDays,
      this.studyDays,
      this.like,
      this.likeDescription,
      this.img,
      this.canLike,
      this.url,
      this.targetId});

  factory _$_StudyBuddyModel.fromJson(Map<String, dynamic> json) =>
      _$$_StudyBuddyModelFromJson(json);

  /// 昵称
  @override
  final String? nickName;

  /// 勋章
  @override
  final int? medal;

  /// 连续学习天数
  @override
  final int? continuousDays;

  /// 学习天数
  @override
  final int? studyDays;

  /// 点赞数量
  @override
  final int? like;

  /// 点赞数量描述
  @override
  final String? likeDescription;

  /// 头像
  @override
  final String? img;

  /// 是否可点赞，1:可以，0:不可以
  @override
  final int? canLike;

  /// 个人主页URL
  @override
  final String? url;
  @override
  final int? targetId;

  @override
  String toString() {
    return 'StudyBuddyModel(nickName: $nickName, medal: $medal, continuousDays: $continuousDays, studyDays: $studyDays, like: $like, likeDescription: $likeDescription, img: $img, canLike: $canLike, url: $url, targetId: $targetId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StudyBuddyModel &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.medal, medal) || other.medal == medal) &&
            (identical(other.continuousDays, continuousDays) ||
                other.continuousDays == continuousDays) &&
            (identical(other.studyDays, studyDays) ||
                other.studyDays == studyDays) &&
            (identical(other.like, like) || other.like == like) &&
            (identical(other.likeDescription, likeDescription) ||
                other.likeDescription == likeDescription) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.canLike, canLike) || other.canLike == canLike) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickName, medal, continuousDays,
      studyDays, like, likeDescription, img, canLike, url, targetId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StudyBuddyModelCopyWith<_$_StudyBuddyModel> get copyWith =>
      __$$_StudyBuddyModelCopyWithImpl<_$_StudyBuddyModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_StudyBuddyModelToJson(
      this,
    );
  }
}

abstract class _StudyBuddyModel implements StudyBuddyModel {
  const factory _StudyBuddyModel(
      {final String? nickName,
      final int? medal,
      final int? continuousDays,
      final int? studyDays,
      final int? like,
      final String? likeDescription,
      final String? img,
      final int? canLike,
      final String? url,
      final int? targetId}) = _$_StudyBuddyModel;

  factory _StudyBuddyModel.fromJson(Map<String, dynamic> json) =
      _$_StudyBuddyModel.fromJson;

  @override

  /// 昵称
  String? get nickName;
  @override

  /// 勋章
  int? get medal;
  @override

  /// 连续学习天数
  int? get continuousDays;
  @override

  /// 学习天数
  int? get studyDays;
  @override

  /// 点赞数量
  int? get like;
  @override

  /// 点赞数量描述
  String? get likeDescription;
  @override

  /// 头像
  String? get img;
  @override

  /// 是否可点赞，1:可以，0:不可以
  int? get canLike;
  @override

  /// 个人主页URL
  String? get url;
  @override
  int? get targetId;
  @override
  @JsonKey(ignore: true)
  _$$_StudyBuddyModelCopyWith<_$_StudyBuddyModel> get copyWith =>
      throw _privateConstructorUsedError;
}
