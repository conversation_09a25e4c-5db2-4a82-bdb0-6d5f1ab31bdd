import 'package:freezed_annotation/freezed_annotation.dart';

part 'study_buddy_model.freezed.dart';
part 'study_buddy_model.g.dart';

@freezed
class StudyBuddy with _$StudyBuddy {
  const factory StudyBuddy({
    List<StudyBuddyModel>? list,
    int? offset,
    int? size,
  }) = _StudyBuddy;
  factory StudyBuddy.fromJson(Map<String, dynamic> json) =>
      _$StudyBuddyFromJson(json);
}

@freezed
class StudyBuddyModel with _$StudyBuddyModel {
  const factory StudyBuddyModel(
      {
      /// 昵称
      String? nickName,

      /// 勋章
      int? medal,

      /// 连续学习天数
      int? continuousDays,

      /// 学习天数
      int? studyDays,

      /// 点赞数量
      int? like,

      /// 点赞数量描述
      String? likeDescription,

      /// 头像
      String? img,

      /// 是否可点赞，1:可以，0:不可以
      int? canLike,

      /// 个人主页URL
      String? url,
      int? targetId //点赞记录 Id
      }) = _StudyBuddyModel;

  factory StudyBuddyModel.fromJson(Map<String, dynamic> json) =>
      _$StudyBuddyModelFromJson(json);
}
