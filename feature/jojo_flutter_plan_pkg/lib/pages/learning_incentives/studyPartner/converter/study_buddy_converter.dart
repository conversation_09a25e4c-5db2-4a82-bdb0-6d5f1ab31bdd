import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';

class StudyBuddyConverter {
  BuddyItemUiState _convertToItemState(StudyBuddyModel model) {
    return BuddyItemUiState(
        name: model.nickName ?? '',
        studyAllCount: model.studyDays ?? 0,
        studyCount: model.continuousDays ?? 0,
        achievementCount: model.medal ?? 0,
        kudosCount: model.like ?? 1,
        personalImage: model.img ?? '',
        router: model.url ?? '',
        canLike: model.canLike ?? 0,
        targetId: model.targetId ?? 0);
  }

  List<BuddyItemUiState> convertToItemStates(List<StudyBuddyModel> models) {
    return models.map((model) => _convertToItemState(model)).toList();
  }
}
