import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/static/img.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

enum ImageLoadMode { asset, network }

// 显示区域配置类
class ImageDisplayConfig {
  final double widthRatio; // 宽度占比 (0.0 - 1.0)
  final double heightRatio; // 高度占比 (0.0 - 1.0)
  final Alignment alignment; // 对齐方式
  final double verticalOffset; // 垂直偏移量 (-1.0 到 1.0，负值向上，正值向下)
  final String? description; // 描述信息
  BoxFit fit;

  ImageDisplayConfig(
      {required this.widthRatio,
      required this.heightRatio,
      required this.alignment,
      this.verticalOffset = 0.0,
      this.description,
      this.fit = BoxFit.contain});

  // 这些预设只适合装扮个人形象
  static ImageDisplayConfig head = ImageDisplayConfig(
      widthRatio: 0.57,
      heightRatio: 0.47,
      alignment: Alignment.topCenter,
      verticalOffset: -0.2,
      fit: BoxFit.fitHeight,
      description: '头部');
  // 这些预设只适合装扮个人形象
  static ImageDisplayConfig waist = ImageDisplayConfig(
      widthRatio: 1.0,
      heightRatio: 0.71,
      alignment: Alignment.topCenter,
      verticalOffset: 0,
      fit: BoxFit.fitHeight,
      description: '半身');

  static ImageDisplayConfig full = ImageDisplayConfig(
    widthRatio: 1.0,
    heightRatio: 1.0,
    alignment: Alignment.topCenter,
    verticalOffset: 0.0,
    description: '全身',
  );

  // 自定义矩形区域
  static ImageDisplayConfig customRect({
    required double x, // 左上角 x 坐标占比 (0.0 - 1.0)
    required double y, // 左上角 y 坐标占比 (0.0 - 1.0)
    required double width, // 宽度占比 (0.0 - 1.0)
    required double height, // 高度占比 (0.0 - 1.0)
    String? description,
  }) {
    // 计算对齐方式
    final alignmentX = (x + width / 2) * 2 - 1; // 转换为 -1 到 1
    final alignmentY = (y + height / 2) * 2 - 1; // 转换为 -1 到 1

    return ImageDisplayConfig(
      widthRatio: width,
      heightRatio: height,
      alignment: Alignment(alignmentX, alignmentY),
      description: description ?? '自定义区域',
    );
  }

  // 便捷方法：创建带垂直偏移的配置
  ImageDisplayConfig withVerticalOffset(double offset) {
    return ImageDisplayConfig(
      widthRatio: widthRatio,
      heightRatio: heightRatio,
      alignment: alignment,
      verticalOffset: offset,
      description: description,
    );
  }
}

class AlternativeImageWidget extends StatefulWidget {
  final String imageUrl;
  final double displayWidth;
  final double displayHeight;
  final ImageDisplayConfig displayConfig;
  final BoxDecoration?
      boxDecoration; //由于内部使用的是ClipRRect因此只能使用 BoraderRadius 而不能使用shape 后续会想办法优化
  final ImageLoadMode loadMode;
  final String? package;
  final bool hideDefaultHolder;
  const AlternativeImageWidget({
    super.key,
    required this.imageUrl,
    required this.displayWidth,
    required this.displayHeight,
    required this.displayConfig,
    this.loadMode = ImageLoadMode.network,
    this.package, //loadMode == ImagLoadMode.asset ? package : null,
    this.boxDecoration,
    this.hideDefaultHolder = false,
  });

  @override
  State<AlternativeImageWidget> createState() => _AlternativeImageWidgetState();
}

class _AlternativeImageWidgetState extends State<AlternativeImageWidget> {
  bool onLoaded = false;

  @override
  void initState() {
    onLoaded = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // 直接使用外部指定的尺寸作为容器约束
    return SizedBox(
      width: widget.displayWidth,
      height: widget.displayHeight,
      child: Container(
        decoration: widget.boxDecoration,
        child: ClipRRect(
          borderRadius: widget.boxDecoration?.borderRadius ?? BorderRadius.zero,
          child: Stack(
            clipBehavior: Clip.hardEdge,
            children: [
              _buildPositionedImage(),
              if (widget.loadMode == ImageLoadMode.network &&
                  onLoaded == false &&
                  !widget.hideDefaultHolder)
                _buildPlaceholder(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPositionedImage() {
    // 计算图片实际尺寸
    final imageWidth = widget.displayWidth / widget.displayConfig.widthRatio;
    final imageHeight = widget.displayHeight / widget.displayConfig.heightRatio;

    // 计算偏移位置
    final offsetX = (widget.displayWidth - imageWidth) / 2; // 水平居中
    final offsetY = widget.displayConfig.verticalOffset *
        (imageHeight - widget.displayHeight);

    if (widget.loadMode == ImageLoadMode.network && !onLoaded) {
      return Positioned.fill(
        child: _buildImageWidget(imageWidth, imageHeight),
      );
    }

    return Positioned(
      left: offsetX,
      top: offsetY.ceilToDouble(),
      child: _buildImageWidget(imageWidth, imageHeight),
    );
  }

  Widget _buildPlaceholder() {
    return Positioned.fill(
      child: ImageAssetWeb(
        assetName: AssetsImg.WGT_DEFAULT_HOLDER,
        fit: BoxFit.contain,
        width: widget.displayWidth,
        height: widget.displayHeight,
        package: 'jojo_flutter_base',
      ),
    );
  }

  Widget _buildImageWidget(double imageWidth, double imageHeight) {
    if (widget.loadMode == ImageLoadMode.network) {
      return ImageNetworkCached(
        imageUrl: widget.imageUrl,
        width: imageWidth,
        height: imageHeight,
        hideDefaultHolder: widget.hideDefaultHolder,
        hideErrorHolder: !widget.hideDefaultHolder,
        fit: widget.displayConfig.fit,
        onloaded: () {
          // 延迟到下一帧执行setState，避免在build过程中调用
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                onLoaded = true;
              });
            }
          });
        },
      );
    }
    return ImageAssetWeb(
      assetName: widget.imageUrl,
      fit: widget.displayConfig.fit,
      width: imageWidth,
      height: imageHeight,
      package: widget.package,
    );
  }
}
