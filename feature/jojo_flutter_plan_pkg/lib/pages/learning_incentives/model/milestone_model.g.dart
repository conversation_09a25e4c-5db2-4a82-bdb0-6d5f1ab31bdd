// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'milestone_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_MilestoneResponse _$$_MilestoneResponseFromJson(Map<String, dynamic> json) =>
    _$_MilestoneResponse(
      subjectColor: json['subjectColor'] as String? ?? "",
      milestoneList: (json['milestoneList'] as List<dynamic>?)
          ?.map((e) => Milestone.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MilestoneResponseToJson(
        _$_MilestoneResponse instance) =>
    <String, dynamic>{
      'subjectColor': instance.subjectColor,
      'milestoneList': instance.milestoneList,
    };

_$_Milestone _$$_MilestoneFromJson(Map<String, dynamic> json) => _$_Milestone(
      name: json['name'] as String?,
      rewardList: (json['rewardList'] as List<dynamic>?)
          ?.map((e) => Reward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_MilestoneToJson(_$_Milestone instance) =>
    <String, dynamic>{
      'name': instance.name,
      'rewardList': instance.rewardList,
    };

_$_Reward _$$_RewardFromJson(Map<String, dynamic> json) => _$_Reward(
      name: json['name'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$_RewardToJson(_$_Reward instance) => <String, dynamic>{
      'name': instance.name,
      'img': instance.img,
    };
