// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'milestone_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MilestoneResponse _$MilestoneResponseFromJson(Map<String, dynamic> json) {
  return _MilestoneResponse.fromJson(json);
}

/// @nodoc
mixin _$MilestoneResponse {
  String get subjectColor => throw _privateConstructorUsedError;
  List<Milestone>? get milestoneList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneResponseCopyWith<MilestoneResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneResponseCopyWith<$Res> {
  factory $MilestoneResponseCopyWith(
          MilestoneResponse value, $Res Function(MilestoneResponse) then) =
      _$MilestoneResponseCopyWithImpl<$Res, MilestoneResponse>;
  @useResult
  $Res call({String subjectColor, List<Milestone>? milestoneList});
}

/// @nodoc
class _$MilestoneResponseCopyWithImpl<$Res, $Val extends MilestoneResponse>
    implements $MilestoneResponseCopyWith<$Res> {
  _$MilestoneResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectColor = null,
    Object? milestoneList = freezed,
  }) {
    return _then(_value.copyWith(
      subjectColor: null == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String,
      milestoneList: freezed == milestoneList
          ? _value.milestoneList
          : milestoneList // ignore: cast_nullable_to_non_nullable
              as List<Milestone>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneResponseCopyWith<$Res>
    implements $MilestoneResponseCopyWith<$Res> {
  factory _$$_MilestoneResponseCopyWith(_$_MilestoneResponse value,
          $Res Function(_$_MilestoneResponse) then) =
      __$$_MilestoneResponseCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String subjectColor, List<Milestone>? milestoneList});
}

/// @nodoc
class __$$_MilestoneResponseCopyWithImpl<$Res>
    extends _$MilestoneResponseCopyWithImpl<$Res, _$_MilestoneResponse>
    implements _$$_MilestoneResponseCopyWith<$Res> {
  __$$_MilestoneResponseCopyWithImpl(
      _$_MilestoneResponse _value, $Res Function(_$_MilestoneResponse) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectColor = null,
    Object? milestoneList = freezed,
  }) {
    return _then(_$_MilestoneResponse(
      subjectColor: null == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String,
      milestoneList: freezed == milestoneList
          ? _value._milestoneList
          : milestoneList // ignore: cast_nullable_to_non_nullable
              as List<Milestone>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MilestoneResponse implements _MilestoneResponse {
  const _$_MilestoneResponse(
      {this.subjectColor = "", final List<Milestone>? milestoneList})
      : _milestoneList = milestoneList;

  factory _$_MilestoneResponse.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneResponseFromJson(json);

  @override
  @JsonKey()
  final String subjectColor;
  final List<Milestone>? _milestoneList;
  @override
  List<Milestone>? get milestoneList {
    final value = _milestoneList;
    if (value == null) return null;
    if (_milestoneList is EqualUnmodifiableListView) return _milestoneList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MilestoneResponse(subjectColor: $subjectColor, milestoneList: $milestoneList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MilestoneResponse &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            const DeepCollectionEquality()
                .equals(other._milestoneList, _milestoneList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectColor,
      const DeepCollectionEquality().hash(_milestoneList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneResponseCopyWith<_$_MilestoneResponse> get copyWith =>
      __$$_MilestoneResponseCopyWithImpl<_$_MilestoneResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneResponseToJson(
      this,
    );
  }
}

abstract class _MilestoneResponse implements MilestoneResponse {
  const factory _MilestoneResponse(
      {final String subjectColor,
      final List<Milestone>? milestoneList}) = _$_MilestoneResponse;

  factory _MilestoneResponse.fromJson(Map<String, dynamic> json) =
      _$_MilestoneResponse.fromJson;

  @override
  String get subjectColor;
  @override
  List<Milestone>? get milestoneList;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneResponseCopyWith<_$_MilestoneResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

Milestone _$MilestoneFromJson(Map<String, dynamic> json) {
  return _Milestone.fromJson(json);
}

/// @nodoc
mixin _$Milestone {
  String? get name => throw _privateConstructorUsedError;
  List<Reward>? get rewardList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneCopyWith<Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneCopyWith<$Res> {
  factory $MilestoneCopyWith(Milestone value, $Res Function(Milestone) then) =
      _$MilestoneCopyWithImpl<$Res, Milestone>;
  @useResult
  $Res call({String? name, List<Reward>? rewardList});
}

/// @nodoc
class _$MilestoneCopyWithImpl<$Res, $Val extends Milestone>
    implements $MilestoneCopyWith<$Res> {
  _$MilestoneCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<Reward>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneCopyWith<$Res> implements $MilestoneCopyWith<$Res> {
  factory _$$_MilestoneCopyWith(
          _$_Milestone value, $Res Function(_$_Milestone) then) =
      __$$_MilestoneCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, List<Reward>? rewardList});
}

/// @nodoc
class __$$_MilestoneCopyWithImpl<$Res>
    extends _$MilestoneCopyWithImpl<$Res, _$_Milestone>
    implements _$$_MilestoneCopyWith<$Res> {
  __$$_MilestoneCopyWithImpl(
      _$_Milestone _value, $Res Function(_$_Milestone) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_$_Milestone(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardList: freezed == rewardList
          ? _value._rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<Reward>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Milestone implements _Milestone {
  const _$_Milestone({this.name, final List<Reward>? rewardList})
      : _rewardList = rewardList;

  factory _$_Milestone.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneFromJson(json);

  @override
  final String? name;
  final List<Reward>? _rewardList;
  @override
  List<Reward>? get rewardList {
    final value = _rewardList;
    if (value == null) return null;
    if (_rewardList is EqualUnmodifiableListView) return _rewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Milestone(name: $name, rewardList: $rewardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Milestone &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._rewardList, _rewardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, const DeepCollectionEquality().hash(_rewardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      __$$_MilestoneCopyWithImpl<_$_Milestone>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneToJson(
      this,
    );
  }
}

abstract class _Milestone implements Milestone {
  const factory _Milestone(
      {final String? name, final List<Reward>? rewardList}) = _$_Milestone;

  factory _Milestone.fromJson(Map<String, dynamic> json) =
      _$_Milestone.fromJson;

  @override
  String? get name;
  @override
  List<Reward>? get rewardList;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneCopyWith<_$_Milestone> get copyWith =>
      throw _privateConstructorUsedError;
}

Reward _$RewardFromJson(Map<String, dynamic> json) {
  return _Reward.fromJson(json);
}

/// @nodoc
mixin _$Reward {
  String? get name => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardCopyWith<Reward> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardCopyWith<$Res> {
  factory $RewardCopyWith(Reward value, $Res Function(Reward) then) =
      _$RewardCopyWithImpl<$Res, Reward>;
  @useResult
  $Res call({String? name, String? img});
}

/// @nodoc
class _$RewardCopyWithImpl<$Res, $Val extends Reward>
    implements $RewardCopyWith<$Res> {
  _$RewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RewardCopyWith<$Res> implements $RewardCopyWith<$Res> {
  factory _$$_RewardCopyWith(_$_Reward value, $Res Function(_$_Reward) then) =
      __$$_RewardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? img});
}

/// @nodoc
class __$$_RewardCopyWithImpl<$Res>
    extends _$RewardCopyWithImpl<$Res, _$_Reward>
    implements _$$_RewardCopyWith<$Res> {
  __$$_RewardCopyWithImpl(_$_Reward _value, $Res Function(_$_Reward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
  }) {
    return _then(_$_Reward(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Reward implements _Reward {
  const _$_Reward({this.name, this.img});

  factory _$_Reward.fromJson(Map<String, dynamic> json) =>
      _$$_RewardFromJson(json);

  @override
  final String? name;
  @override
  final String? img;

  @override
  String toString() {
    return 'Reward(name: $name, img: $img)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Reward &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, img);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RewardCopyWith<_$_Reward> get copyWith =>
      __$$_RewardCopyWithImpl<_$_Reward>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RewardToJson(
      this,
    );
  }
}

abstract class _Reward implements Reward {
  const factory _Reward({final String? name, final String? img}) = _$_Reward;

  factory _Reward.fromJson(Map<String, dynamic> json) = _$_Reward.fromJson;

  @override
  String? get name;
  @override
  String? get img;
  @override
  @JsonKey(ignore: true)
  _$$_RewardCopyWith<_$_Reward> get copyWith =>
      throw _privateConstructorUsedError;
}
