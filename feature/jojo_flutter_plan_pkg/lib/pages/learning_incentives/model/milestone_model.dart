import 'package:jojo_flutter_base/base.dart';

part 'milestone_model.freezed.dart';
part 'milestone_model.g.dart';

@freezed
class MilestoneResponse with _$MilestoneResponse {
  const factory MilestoneResponse({
    @Default("") String subjectColor,
    List<Milestone>? milestoneList,
  }) = _MilestoneResponse;

  factory MilestoneResponse.fromJson(Map<String, dynamic> json) =>
      _$MilestoneResponseFromJson(json);
}

@freezed
class Milestone with _$Milestone {
  const factory Milestone({
    String? name,
    List<Reward>? rewardList,
  }) = _Milestone;

  factory Milestone.fromJson(Map<String, dynamic> json) =>
      _$MilestoneFromJson(json);
}

@freezed
class Reward with _$Reward {
  const factory Reward({
    String? name,
    String? img,
  }) = _Reward;

  factory Reward.fromJson(Map<String, dynamic> json) =>
      _$RewardFromJson(json);
}