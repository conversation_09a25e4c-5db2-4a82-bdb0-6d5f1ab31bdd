import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/model/milestone_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/subject_type.dart';
import 'package:jojo_flutter_plan_pkg/service/learning_incentives_api.dart';

class MilestoneListController extends Cubit<MilestoneListState> {
  final int subjectType;
  MilestoneListController({required this.subjectType}) : super(MilestoneListState()){
    getMilestoneList(subjectType: subjectType);
  }

  void getMilestoneList({required int subjectType}) async {
    try {
      final MilestoneResponse response =
          await learningIncentivesApi.getMilestoneList(subjectType);
      final newState = _generateMilestoneListState(response);
      emit(newState);
    } catch (e) {
      final error = e is Exception ? e : Exception(e.toString());
      emit(state.copyWith(status: PageStatus.error, exception: error));
    }
  }

  MilestoneListState _generateMilestoneListState(MilestoneResponse response) {
    final mliestioneList = response.milestoneList ?? [];
    if (mliestioneList.isEmpty) {
      return MilestoneListState(status: PageStatus.success,);
    }
    List<MilestoneInfo> milestoneInfoList = mliestioneList.map((e) {
      String name = e.name ?? "";
      List<RewardInfo> rewardInfoList = _generateRewardInfo(e.rewardList ?? []);
      return MilestoneInfo(name: name, rewardList: rewardInfoList);
    }).toList();
    return MilestoneListState(
      status: PageStatus.success,
      milestoneList: milestoneInfoList,
      subjectColor: response.subjectColor,
    );
  }

  List<RewardInfo> _generateRewardInfo(List<Reward> rewardList) {
    return rewardList.map((e) {
      return RewardInfo(
        name: e.name ?? "",
        img: e.img ?? "",
      );
    }).toList();
  }

   void trackingViewScreen() {
    RunEnv.sensorsTrack('\$AppViewScreen', {
      '\$screen_name': '里程碑宝箱列表页',
      'material_id':  getSubjectName(subjectType)
    });
  }

}
