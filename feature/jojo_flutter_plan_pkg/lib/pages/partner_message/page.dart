import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/view.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

// 消息页面
class PartnerMessagePage extends BasePage {
  final int loadingScene;
  const PartnerMessagePage({Key? key, required this.loadingScene})
      : super(key: key);

  @override
  State<PartnerMessagePage> createState() => _PartnerMessagePageState();
}

class _PartnerMessagePageState extends BaseState<PartnerMessagePage>
    with BasicInitPage {
  late PartnerMessageController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PartnerMessageController();
  }

  @override
  void onResume() {
    super.onResume();
    _controller.refreshMessages();
  }

  @override
  Widget body(context) {
    return BlocProvider(
        create: (_) => _controller,
        child: VisibilityObserve(
          onShow: () => {
            // 埋点
            RunEnv.sensorsTrack(
                '\$AppViewScreen', {'\$screen_name': '个人消息_浏览'}),
          },
          child: _buildBody(context),
        ));
  }

  Widget _buildBody(context) {
    return BlocBuilder<PartnerMessageController, PartnerMessageState>(
        builder: (context, state) {
      return JoJoPageLoadingV25(
        status: state.status,
        exception: state.exception,
        hideProgress: true,
        emptyText: '您当前没有消息哦~',
        retry:() => _controller.refreshMessages(),

        backWidget: JoJoAppBar(
          title: S.of(context).partnerMessage,
          backgroundColor: Colors.transparent),
        child: PartnerMessageView(state: state, loadingScene: widget.loadingScene),
      );
    });
  }
}
