import 'package:equatable/equatable.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/model/partner_message_data.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

class PartnerMessageState {
  PageStatus status; //loading状态
  Exception? exception;

  List<PartnerMessage>? messages; // 所有消息
  int offset;
  int size;
  bool noMoreMessage;

  PartnerMessageState(
      {this.status = PageStatus.loading,
      this.messages,
      this.offset = 0,
      this.size = 20,
      this.noMoreMessage = false});

  PartnerMessageState copyWith() {
    return PartnerMessageState(
        messages: messages,
        offset: offset,
        size: size,
        noMoreMessage: noMoreMessage,
        status: status);
  }

  int messageCount() {
    return messages?.length ?? 0;
  }

  void resetState() {
    messages = null;
    offset = 0;
    noMoreMessage = false;
  }

  void addMessages(PartnerMessageModel model) {
    if (model.isNoMessages()) {
      return;
    }
    if (model.size != null && model.size! < size) {
      // 数据取完了
      noMoreMessage = true;
    }
    messages = messages ??= [];
    messages!.addAll(model.messages!);
    if (messageCount() > 0) {
      // 更新offset
      PartnerMessage message = messages!.last;
      offset = message.msgId ?? 0;
    }
  }

  // 替换消息，做本地状态更新
  void replaceMessage(PartnerMessage newMsg, PartnerMessage oldMsg) {
    if (messages == null) {
      return;
    }
    int index = messages!.indexWhere((m) => m.msgId == oldMsg.msgId);
    if (index >= 0 && index < messages!.length) {
      messages![index] = newMsg;
    }
  }

  bool haveMessages() {
    return messages != null && messages!.isNotEmpty;
  }
}
