import 'package:freezed_annotation/freezed_annotation.dart';
part 'partner_message_data.freezed.dart';
part 'partner_message_data.g.dart';

@freezed
class PartnerMessageModel with _$PartnerMessageModel {
  factory PartnerMessageModel({
    int? size,
    List<PartnerMessage>? messages,
  }) = _PartnerMessageModel;
  factory PartnerMessageModel.fromJson(Map<String, dynamic> json) =>
      _$PartnerMessageModelFromJson(json);
}

extension PartnerMessageModelExt on PartnerMessageModel {
  bool isNoMessages() {
    return messages == null || messages!.isEmpty;
  }
}

@freezed
class PartnerMessage with _$PartnerMessage {
  factory PartnerMessage({
    int? msgId, // 消息id
    String? content, // 消息内容
    int? type, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
    int? partnerId, // 学伴id
    String? nickName, // 学伴昵称
    String? avatarUrl, // 学伴头像
    int? receiveTime, // 消息接收时间
    String? timeDesc, // 时间描述
    int? friendsAcceptRequest, //0 已通过(申请方) 1 已拒绝(被申请方) 2 已通过(被申请方) 3 未处理(被申请方)
    String? jumpUrl,
  }) = _PartnerMessage;
  factory PartnerMessage.fromJson(Map<String, dynamic> json) =>
      _$PartnerMessageFromJson(json);
}

extension PartnerMessageExt on PartnerMessage {
  bool haveAcceptResult() {
    return friendsAcceptRequest != null && (friendsAcceptRequest == 1 || friendsAcceptRequest == 2);
  }
}
