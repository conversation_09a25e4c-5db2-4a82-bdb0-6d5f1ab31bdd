// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'partner_message_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PartnerMessageModel _$$_PartnerMessageModelFromJson(
        Map<String, dynamic> json) =>
    _$_PartnerMessageModel(
      size: json['size'] as int?,
      messages: (json['messages'] as List<dynamic>?)
          ?.map((e) => PartnerMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PartnerMessageModelToJson(
        _$_PartnerMessageModel instance) =>
    <String, dynamic>{
      'size': instance.size,
      'messages': instance.messages,
    };

_$_PartnerMessage _$$_PartnerMessageFromJson(Map<String, dynamic> json) =>
    _$_PartnerMessage(
      msgId: json['msgId'] as int?,
      content: json['content'] as String?,
      type: json['type'] as int?,
      partnerId: json['partnerId'] as int?,
      nickName: json['nickName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      receiveTime: json['receiveTime'] as int?,
      timeDesc: json['timeDesc'] as String?,
      friendsAcceptRequest: json['friendsAcceptRequest'] as int?,
      jumpUrl: json['jumpUrl'] as String?,
    );

Map<String, dynamic> _$$_PartnerMessageToJson(_$_PartnerMessage instance) =>
    <String, dynamic>{
      'msgId': instance.msgId,
      'content': instance.content,
      'type': instance.type,
      'partnerId': instance.partnerId,
      'nickName': instance.nickName,
      'avatarUrl': instance.avatarUrl,
      'receiveTime': instance.receiveTime,
      'timeDesc': instance.timeDesc,
      'friendsAcceptRequest': instance.friendsAcceptRequest,
      'jumpUrl': instance.jumpUrl,
    };
