import 'package:jojo_flutter_base/base.dart';
part 'activity_data.freezed.dart';
part 'activity_data.g.dart';

@freezed
class PlanActivityData with _$PlanActivityData {
  const factory PlanActivityData({
    int? activityId,
    String? activitySubject,
    ClassActivitiesThemeCardVo? themeCard,
    List<ClassActivitiesTaskVo?>? tasks,
    List<PlanActivityFreePagesVo?>? freePages,
  }) = _PlanActivityData;

  factory PlanActivityData.fromJson(Map<String, dynamic> json) =>
      _$PlanActivityDataFromJson(json);
}

@freezed
class ClassActivitiesThemeCardVo with _$ClassActivitiesThemeCardVo {
  const factory ClassActivitiesThemeCardVo({
    String? progressHeadColor,
    String? progressTailColor,
    String? backgroundRes,
  }) = _ClassActivitiesThemeCardVo;

  factory ClassActivitiesThemeCardVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesThemeCardVoFromJson(json);
}

@freezed
class ClassActivitiesTaskVo with _$ClassActivitiesTaskVo {
  const factory ClassActivitiesTaskVo({
    int? taskId,
    String? taskType,
    String? name,
    List<ClassActivitiesTaskConditionsVo?>? conditions,
    List<ClassActivitiesTaskRewardsVo?>? rewards,
    ClassActivitiesTaskExtendResourceVo? taskExtendResource,
  }) = _ClassActivitiesTaskVo;

  factory ClassActivitiesTaskVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesTaskVoFromJson(json);
}

@freezed
class ClassActivitiesTaskConditionsVo with _$ClassActivitiesTaskConditionsVo {
  const factory ClassActivitiesTaskConditionsVo({
    int? currentValue,
    int? targetValue,
  }) = _ClassActivitiesTaskConditionsVo;

  factory ClassActivitiesTaskConditionsVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesTaskConditionsVoFromJson(json);
}

@freezed
class ClassActivitiesTaskRewardsVo with _$ClassActivitiesTaskRewardsVo {
  const factory ClassActivitiesTaskRewardsVo({
    int? rewardId,
    int? isGet,
    int? isPopup,
    int? type,
    String? lockImage,
    String? unlockImage,
    String? resourceFlutter,
    String? bizId,
    String? rewardBizUrl,
  }) = _ClassActivitiesTaskRewardsVo;

  factory ClassActivitiesTaskRewardsVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesTaskRewardsVoFromJson(json);
}

@freezed
class ClassActivitiesTaskExtendResourceVo with _$ClassActivitiesTaskExtendResourceVo {
  const factory ClassActivitiesTaskExtendResourceVo({
    String? rewardDisplayUrl,
    String? mainText,
    String? subText,
    List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts,
  }) = _ClassActivitiesTaskExtendResourceVo;

  factory ClassActivitiesTaskExtendResourceVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesTaskExtendResourceVoFromJson(json);
}

@freezed
class ClassActivitiesTaskRewardNodeTextVo with _$ClassActivitiesTaskRewardNodeTextVo {
  const factory ClassActivitiesTaskRewardNodeTextVo({
    int? rewardType,
    String? mainText,
    String? subText,
  }) = _ClassActivitiesTaskRewardNodeTextVo;

  factory ClassActivitiesTaskRewardNodeTextVo.fromJson(Map<String, dynamic> json) =>
      _$ClassActivitiesTaskRewardNodeTextVoFromJson(json);
}

@freezed
class PlanActivityFreePagesVo with _$PlanActivityFreePagesVo {
  const factory PlanActivityFreePagesVo({
    int? id,
    String? pageType,
    String? pageName,
    List<PlanActivityFreePagesComponentsVo?>? components,
  }) = _PlanActivityFreePagesVo;

  factory PlanActivityFreePagesVo.fromJson(Map<String, dynamic> json) =>
      _$PlanActivityFreePagesVoFromJson(json);
}

@freezed
class PlanActivityFreePagesComponentsVo with _$PlanActivityFreePagesComponentsVo {
  const factory PlanActivityFreePagesComponentsVo({
    int? orderNum,
    String? componentType,
    String? imgUrl,
    String? videoUrl,
    String? videoCoverImg,
    String? videoBgImg,
    String? topImg,
    String? surroundImg,
    String? bottomImg,
  }) = _PlanActivityFreePagesComponentsVo;

  factory PlanActivityFreePagesComponentsVo.fromJson(Map<String, dynamic> json) =>
      _$PlanActivityFreePagesComponentsVoFromJson(json);
}