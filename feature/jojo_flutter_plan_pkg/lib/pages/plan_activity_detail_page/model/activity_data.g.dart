// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_PlanActivityData _$$_PlanActivityDataFromJson(Map<String, dynamic> json) =>
    _$_PlanActivityData(
      activityId: json['activityId'] as int?,
      activitySubject: json['activitySubject'] as String?,
      themeCard: json['themeCard'] == null
          ? null
          : ClassActivitiesThemeCardVo.fromJson(
              json['themeCard'] as Map<String, dynamic>),
      tasks: (json['tasks'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskVo.fromJson(e as Map<String, dynamic>))
          .toList(),
      freePages: (json['freePages'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : PlanActivityFreePagesVo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PlanActivityDataToJson(_$_PlanActivityData instance) =>
    <String, dynamic>{
      'activityId': instance.activityId,
      'activitySubject': instance.activitySubject,
      'themeCard': instance.themeCard,
      'tasks': instance.tasks,
      'freePages': instance.freePages,
    };

_$_ClassActivitiesThemeCardVo _$$_ClassActivitiesThemeCardVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesThemeCardVo(
      progressHeadColor: json['progressHeadColor'] as String?,
      progressTailColor: json['progressTailColor'] as String?,
      backgroundRes: json['backgroundRes'] as String?,
    );

Map<String, dynamic> _$$_ClassActivitiesThemeCardVoToJson(
        _$_ClassActivitiesThemeCardVo instance) =>
    <String, dynamic>{
      'progressHeadColor': instance.progressHeadColor,
      'progressTailColor': instance.progressTailColor,
      'backgroundRes': instance.backgroundRes,
    };

_$_ClassActivitiesTaskVo _$$_ClassActivitiesTaskVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskVo(
      taskId: json['taskId'] as int?,
      taskType: json['taskType'] as String?,
      name: json['name'] as String?,
      conditions: (json['conditions'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskConditionsVo.fromJson(
                  e as Map<String, dynamic>))
          .toList(),
      rewards: (json['rewards'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : ClassActivitiesTaskRewardsVo.fromJson(
                  e as Map<String, dynamic>))
          .toList(),
      taskExtendResource: json['taskExtendResource'] == null
          ? null
          : ClassActivitiesTaskExtendResourceVo.fromJson(
              json['taskExtendResource'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_ClassActivitiesTaskVoToJson(
        _$_ClassActivitiesTaskVo instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'taskType': instance.taskType,
      'name': instance.name,
      'conditions': instance.conditions,
      'rewards': instance.rewards,
      'taskExtendResource': instance.taskExtendResource,
    };

_$_ClassActivitiesTaskConditionsVo _$$_ClassActivitiesTaskConditionsVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskConditionsVo(
      currentValue: json['currentValue'] as int?,
      targetValue: json['targetValue'] as int?,
    );

Map<String, dynamic> _$$_ClassActivitiesTaskConditionsVoToJson(
        _$_ClassActivitiesTaskConditionsVo instance) =>
    <String, dynamic>{
      'currentValue': instance.currentValue,
      'targetValue': instance.targetValue,
    };

_$_ClassActivitiesTaskRewardsVo _$$_ClassActivitiesTaskRewardsVoFromJson(
        Map<String, dynamic> json) =>
    _$_ClassActivitiesTaskRewardsVo(
      rewardId: json['rewardId'] as int?,
      isGet: json['isGet'] as int?,
      isPopup: json['isPopup'] as int?,
      type: json['type'] as int?,
      lockImage: json['lockImage'] as String?,
      unlockImage: json['unlockImage'] as String?,
      resourceFlutter: json['resourceFlutter'] as String?,
      bizId: json['bizId'] as String?,
      rewardBizUrl: json['rewardBizUrl'] as String?,
    );

Map<String, dynamic> _$$_ClassActivitiesTaskRewardsVoToJson(
        _$_ClassActivitiesTaskRewardsVo instance) =>
    <String, dynamic>{
      'rewardId': instance.rewardId,
      'isGet': instance.isGet,
      'isPopup': instance.isPopup,
      'type': instance.type,
      'lockImage': instance.lockImage,
      'unlockImage': instance.unlockImage,
      'resourceFlutter': instance.resourceFlutter,
      'bizId': instance.bizId,
      'rewardBizUrl': instance.rewardBizUrl,
    };

_$_ClassActivitiesTaskExtendResourceVo
    _$$_ClassActivitiesTaskExtendResourceVoFromJson(
            Map<String, dynamic> json) =>
        _$_ClassActivitiesTaskExtendResourceVo(
          rewardDisplayUrl: json['rewardDisplayUrl'] as String?,
          mainText: json['mainText'] as String?,
          subText: json['subText'] as String?,
          rewardNodeTexts: (json['rewardNodeTexts'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : ClassActivitiesTaskRewardNodeTextVo.fromJson(
                      e as Map<String, dynamic>))
              .toList(),
        );

Map<String, dynamic> _$$_ClassActivitiesTaskExtendResourceVoToJson(
        _$_ClassActivitiesTaskExtendResourceVo instance) =>
    <String, dynamic>{
      'rewardDisplayUrl': instance.rewardDisplayUrl,
      'mainText': instance.mainText,
      'subText': instance.subText,
      'rewardNodeTexts': instance.rewardNodeTexts,
    };

_$_ClassActivitiesTaskRewardNodeTextVo
    _$$_ClassActivitiesTaskRewardNodeTextVoFromJson(
            Map<String, dynamic> json) =>
        _$_ClassActivitiesTaskRewardNodeTextVo(
          rewardType: json['rewardType'] as int?,
          mainText: json['mainText'] as String?,
          subText: json['subText'] as String?,
        );

Map<String, dynamic> _$$_ClassActivitiesTaskRewardNodeTextVoToJson(
        _$_ClassActivitiesTaskRewardNodeTextVo instance) =>
    <String, dynamic>{
      'rewardType': instance.rewardType,
      'mainText': instance.mainText,
      'subText': instance.subText,
    };

_$_PlanActivityFreePagesVo _$$_PlanActivityFreePagesVoFromJson(
        Map<String, dynamic> json) =>
    _$_PlanActivityFreePagesVo(
      id: json['id'] as int?,
      pageType: json['pageType'] as String?,
      pageName: json['pageName'] as String?,
      components: (json['components'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : PlanActivityFreePagesComponentsVo.fromJson(
                  e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_PlanActivityFreePagesVoToJson(
        _$_PlanActivityFreePagesVo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'pageType': instance.pageType,
      'pageName': instance.pageName,
      'components': instance.components,
    };

_$_PlanActivityFreePagesComponentsVo
    _$$_PlanActivityFreePagesComponentsVoFromJson(Map<String, dynamic> json) =>
        _$_PlanActivityFreePagesComponentsVo(
          orderNum: json['orderNum'] as int?,
          componentType: json['componentType'] as String?,
          imgUrl: json['imgUrl'] as String?,
          videoUrl: json['videoUrl'] as String?,
          videoCoverImg: json['videoCoverImg'] as String?,
          videoBgImg: json['videoBgImg'] as String?,
          topImg: json['topImg'] as String?,
          surroundImg: json['surroundImg'] as String?,
          bottomImg: json['bottomImg'] as String?,
        );

Map<String, dynamic> _$$_PlanActivityFreePagesComponentsVoToJson(
        _$_PlanActivityFreePagesComponentsVo instance) =>
    <String, dynamic>{
      'orderNum': instance.orderNum,
      'componentType': instance.componentType,
      'imgUrl': instance.imgUrl,
      'videoUrl': instance.videoUrl,
      'videoCoverImg': instance.videoCoverImg,
      'videoBgImg': instance.videoBgImg,
      'topImg': instance.topImg,
      'surroundImg': instance.surroundImg,
      'bottomImg': instance.bottomImg,
    };
