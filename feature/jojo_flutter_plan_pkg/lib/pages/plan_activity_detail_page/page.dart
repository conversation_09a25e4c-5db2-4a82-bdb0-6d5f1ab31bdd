import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/view.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/spine_download_manager.dart';

class PlanActivityDetailPageModel extends BasePage {
  final String? buriedString; // 埋点数据
  final String? subjectColor;
  final int? activityId;
  final int? classId;
  final int? courseId;
  final int? pageId;

  const PlanActivityDetailPageModel({
    Key? key,
    this.buriedString,
    this.subjectColor,
    this.activityId,
    this.classId,
    this.courseId,
    this.pageId,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _PlanActivityDetailPageModelState();
}

class _PlanActivityDetailPageModelState extends BaseState<PlanActivityDetailPageModel> with BasicInitPage {

  PlanActivityDetailCtrl? _ctrl;

  @override
  void initState() {
    super.initState();
    // 浏览埋点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        if (widget.buriedString != null) {
          Map<String, dynamic> buriedMap = jsonDecode(widget.buriedString!);
          Map<String, dynamic> properties = {
            'c_element_name': "完课活动_活动页_进入",
            ...buriedMap,
          };
          RunEnv.sensorsTrack('\$AppViewScreen',properties,);
        }
      } catch (e) {
        l.i("促完课活动", "落地页，首页浏览埋点异常");
      }
    });
  }

  @override
  void dispose() {
    _ctrl?.playerStateSubscription?.cancel();
    _ctrl?.playerStateSubscription = null;
    if (_ctrl?.audioPlayer != null) {
      _ctrl?.audioPlayer?.stop();
      _ctrl?.audioPlayer?.dispose();
      _ctrl?.audioPlayer = null;
    }
    GlobalDownloadManager downloadManager = GlobalDownloadManager();
    downloadManager.cancelAllDownloads();
    super.dispose();
  }

  @override
  void onPause() {
    _ctrl?.isPlayAudio = false;
    if (_ctrl?.audioPlayer != null) {
      _ctrl?.audioPlayer?.pause();
    }
    jojoEventBus.fire(EventBusActivityDetailLifecycleData(LifecycleType.paused));
    super.onPause();
  }

  @override
  void onResume() {
    super.onResume();
    if (mounted) {
      _ctrl?.getDetailInfoData();
      jojoEventBus.fire(EventBusActivityDetailLifecycleData(LifecycleType.resumed));
    }
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) {
        _ctrl = PlanActivityDetailCtrl(
          buriedString: widget.buriedString,
          subjectColor: widget.subjectColor,
          activityId: widget.activityId,
          classId: widget.classId,
          courseId: widget.courseId,
          pageId: widget.pageId,
        );
        return _ctrl!;
      },
      child: BlocBuilder<PlanActivityDetailCtrl, PlanActivityDetailState>(
          builder: (context, state) {
            return PlanActivityDetailPageView(state: state);
          }),
    );
  }
}
