import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_madel_get.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ActivityNodeItemPage extends StatefulWidget {

  final LessonInProgressGiftModel? giftModel;
  final PromoteLessonFinishModel? finishModel;
  final String? finishNodeIconColor;
  final bool isLastTargetNode;

  const ActivityNodeItemPage({
    super.key,
    required this.giftModel,
    required this.isLastTargetNode,
    required this.finishModel,
    required this.finishNodeIconColor,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityNodeItemPageState();
  }
}

class _ActivityNodeItemPageState extends State<ActivityNodeItemPage> {

  final _gotSpineController = JoJoSpineAnimationController();
  bool _needStopAnimation = false;

  void _playAnimation(bool isEndNode) {
    String animationName = isEndNode ? SpineAnimationConstants.badgeInfoAnimationNormal : SpineAnimationConstants.bubbleAnimationLoop;
    if (_gotSpineController.spineController?.skeletonData.findAnimation(animationName) == null) {
      l.i("促完课活动", "落地页，奖章动效资源中缺少$animationName动画");
    }
    _gotSpineController.playAnimation(JoJoSpineAnimation(
        animaitonName: animationName,
        trackIndex: 0,
        loop: false,
        delay: 0, listener: _spineAnimationEvent));
  }

  /// 动效动画完成回调
  _spineAnimationEvent(AnimationEventType type) {
    if(AnimationEventType.start == type && _needStopAnimation) {
      _gotSpineController.pauseAnimation();
      _needStopAnimation = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildNodeWidget(),
        _buildSpineWidget()
      ],
    );
  }

  Widget _buildSpineWidget() {
    if (widget.giftModel == null) return Container();
    if (widget.giftModel?.spineResourceVo?.isDowned == false) return Container();
    String? atlasFilePath = widget.giftModel?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = widget.giftModel?.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_SKL);
    if ((atlasFilePath ?? "").isEmpty || (skelFilePath ?? "").isEmpty) return Container();

    Key spineKey = ValueKey('$atlasFilePath-$skelFilePath');
    return Positioned(
        top: 15.rdp,
        right: 10.rdp,
        child: GestureDetector(
          onTap: () {
            nodeOnClick();
          },
          child: SizedBox(
            key: spineKey,
            width: 50.rdp,
            height: 50.rdp,
            child: _buildAnimationWidget(atlasFilePath, skelFilePath, widget.isLastTargetNode? SpineAnimationConstants.badgeInfoAnimationNormal : SpineAnimationConstants.bubbleAnimationLoop),
          ),
        ));
  }

  Widget _buildAnimationWidget(String? atlasFilePath, String? skelFilePath, String? animationName) {
    return JoJoSpineAnimationWidget(
      atlasFilePath ?? "",
      skelFilePath ?? "",
      LoadMode.file,
      _gotSpineController,
      useRootBoneAlign: true,
      fit: BoxFit.none,
      onInitialized: (controller) {
        if (mounted) {
          controller.skeleton
            ..setScaleX(50.rdp/500)
            ..setScaleY(50.rdp/500);
          _playAnimation(widget.isLastTargetNode);
          _needStopAnimation = true;
        }
      },
    );
  }

  Widget _buildNodeStatusIcon(bool isFinish) {
    if (isFinish) {
      return Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned(
                top: (ConfigSize.nodeItemHeight - ConfigSize.nodeItemFinishBgHeight)/2.0,
                left: (ConfigSize.nodeItemWidth - ConfigSize.nodeItemFinishBgWidth)/2.0,
                child: Container(
                  width: ConfigSize.nodeItemFinishBgWidth,
                  height: ConfigSize.nodeItemFinishBgHeight,
                  decoration: BoxDecoration(
                      color: HexColor(widget.finishNodeIconColor ?? "#81CA42"), // 填充颜色,
                      border: Border.all(
                        color: Colors.white, // 边框颜色
                        width: 2.rdp, // 边框宽度
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(ConfigSize.nodeItemFinishBgHeight/2.0))),
                )
            ),
          ],
        ),
      );
    } else {
      return ImageAssetWeb(
        assetName: AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_NODE_ICON,
        fit: BoxFit.cover,
        package: Config.package,
      );
    }
  }

  void _creatAudioPlayerAndPlay() {
    if (!mounted) return;
    try {
      PlanActivityDetailCtrl ctrl = context.read<PlanActivityDetailCtrl>();
      if (ctrl.isPlayAudio) return;

      if (ctrl.audioPlayer == null) {
        _setAudioContext();
        ctrl.audioPlayer = AudioPlayer();
        // 取消之前的监听器
        ctrl.playerStateSubscription?.cancel();
        // 创建新的监听器并保存引用
        ctrl.playerStateSubscription = ctrl.audioPlayer?.onPlayerStateChanged.listen((event) {
          if (!mounted) return;
          if (event == PlayerState.completed || event == PlayerState.stopped) {
            try {
              PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
              _ctrl.isPlayAudio = false;
            } catch (e) {
              l.e("促完课活动", "落地页 音频播放完成，但isPlayAudio并未设置成false: ${e.toString()}");
            }
          }
        });
      }
      ctrl.isPlayAudio = true;
      String? package = RunEnv.package;
      String audioPath = AssetsAudio.PLAN_ACTIVITY_DETAIL_UNFINISH;
      String keyName = package == null ? audioPath : 'packages/$package/$audioPath';
      ctrl.audioPlayer?.audioCache.prefix = '';
      ctrl.audioPlayer?.play(AssetSource(keyName));
    } catch (e) {
      l.e("促完课活动", "落地页 音频播放失败: ${e.toString()}");
    }
  }

  void _setAudioContext() {
    AudioPlayer.global.setGlobalAudioContext(
      const AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
      ),
    );
  }

  void nodeOnClick() {
    if (widget.giftModel?.currentValue == widget.giftModel?.index) {
      if (!mounted || widget.giftModel?.spineResourceVo?.isDowned != true) return;
      PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
      // 已完成，进入结算页
      SmartDialog.show(
          clickMaskDismiss: true,
          useAnimation: false,
          onDismiss: () {},
          alignment: Alignment.center,
          builder: (_) => CoursePromoteFinishAnimationDialogMadelGetWidget(
              model: widget.finishModel,
              giftModel: widget.giftModel,
              isFromClick: true,
              subjectColor: _ctrl.subjectColor ?? "#81CA42",
              isAllLessonFirstGet: false,
              disMissCallBack: () {
                SmartDialog.dismiss();
              }));
    } else {
      // 未完成，播放语音提醒
      _creatAudioPlayerAndPlay();
    }
    try {
      PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
      // 浏览埋点
      if (_ctrl.buriedString != null) {
        Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
        Map<String, dynamic> properties = {
          '\$element_name': "完课活动_活动页_点击查看奖励",
          'user_state': widget.giftModel?.currentValue == widget.giftModel?.index ? "1" : "0",
          ...buriedMap,
        };
        RunEnv.sensorsTrack("\$AppClick", properties);
      }
    } catch (e) {
      l.i("促完课活动", "落地页，视频组件点击埋点异常");
    }
  }

  Widget _buildNodeWidget() {
    return GestureDetector(
      onTap: () {
        nodeOnClick();
      },
      child: Container(
        height: 114.rdp,
        width: 70.rdp,
        color: Colors.transparent,
        alignment: Alignment.topCenter,
        child: Column(
          children: [
            SizedBox(height: 9.rdp,),
            ImageAssetWeb(
              assetName:  AssetsImg.PLAN_ACTIVITY_TASK_BUBBLE,
              height: 73.rdp,
              width: 66.rdp,
              package: Config.package,
              fit: BoxFit.cover,
            ),
            Container(
              width: ConfigSize.nodeItemWidth,
              height: ConfigSize.nodeItemHeight,
              color: Colors.transparent,
              child: Stack(
                children: [
                  _buildNodeStatusIcon(widget.giftModel?.currentValue == widget.giftModel?.index),
                  Positioned.fill(
                      child: Center(
                        child: Text(
                            "${widget.giftModel?.index}",
                            style: TextStyle(
                                fontSize: 16.rdp,
                                color: Colors.white,
                                fontFamily: "MohrRounded_Bold",
                                overflow: TextOverflow.ellipsis,
                                package: 'jojo_flutter_base'
                            )),
                      ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
