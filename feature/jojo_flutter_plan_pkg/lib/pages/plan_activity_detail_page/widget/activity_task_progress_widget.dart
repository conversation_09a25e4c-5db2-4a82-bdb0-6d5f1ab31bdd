

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';

class PathPainter extends CustomPainter {
  final double progress; // 0-1 之间的进度值
  final double strokeWidth;
  final List<Color> gradientColors; // 渐变颜色列表
  final Color backgroundColor; // 背景颜色
  List<PlanActivityDetailProgressItemVo> positions;

  PathPainter({
    required this.progress,
    this.strokeWidth = 18.0,
    required this.gradientColors,
    required this.backgroundColor,
    required this.positions,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 创建完整路径
    final fullPath = _createPath();

    // 绘制背景路径（未填充部分）
    _drawBackgroundPath(canvas, fullPath);

    // 绘制进度路径（已填充部分）
    _drawProgressPath(canvas, fullPath, size);
  }

  /// 创建贝塞尔曲线路径
  Path _createPath() {
    final path = Path();
    if (positions.isNotEmpty) {
      path.moveTo(positions[0].startPoint?.dx ?? 0, positions[0].startPoint?.dy ?? 0);

      for (int i = 1; i <= positions.length; i++) {
        final position = positions[i - 1];
        if (position.controlPoint == null) {
          path.lineTo(position.endPoint?.dx ?? 0, position.endPoint?.dy ?? 0);
        } else {
          path.quadraticBezierTo(
              position.controlPoint?.dx ?? 0, position.controlPoint?.dy ?? 0,
              position.endPoint?.dx ?? 0, position.endPoint?.dy ?? 0);
        }
      }
    }
    return path;
  }

  /// 绘制背景路径
  void _drawBackgroundPath(Canvas canvas, Path path) {
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, backgroundPaint);
  }

  /// 绘制进度路径
  void _drawProgressPath(Canvas canvas, Path fullPath, Size size) {
    if (progress <= 0) return;

    // 计算进度路径
    final progressPath = _extractProgressPath(fullPath);

    // 创建渐变画笔
    final progressPaint = Paint()
      ..shader = _createGradientShader(size)
      ..strokeWidth = strokeWidth - 4.rdp
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.square;

    canvas.drawPath(progressPath, progressPaint);
  }

  /// 根据进度提取路径的一部分
  Path _extractProgressPath(Path fullPath) {
    final pathMetrics = fullPath.computeMetrics();
    final progressPath = Path();

    for (final metric in pathMetrics) {
      final progressLength = metric.length * progress.clamp(0.0, 1.0);
      final extractedPath = metric.extractPath(0, progressLength);
      progressPath.addPath(extractedPath, Offset.zero);
    }

    return progressPath;
  }

  /// 创建渐变着色器
  Shader _createGradientShader(Size size) {
    return LinearGradient(
      colors: gradientColors,
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
  }

  @override
  bool shouldRepaint(covariant PathPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.gradientColors != gradientColors ||
           oldDelegate.backgroundColor != backgroundColor ||
           oldDelegate.strokeWidth != strokeWidth;
  }
}

class ActivityProgressPage extends StatefulWidget {

  final double height;
  final List<Color> gradientColors;
  final Color backgroundColor;
  final PlanActivityDetailProgressVo item;

  const ActivityProgressPage({
    super.key,
    required this.height,
    required this.item,
    required this.gradientColors,
    required this.backgroundColor,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityProgressPageState();
  }
}

class _ActivityProgressPageState extends State<ActivityProgressPage> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      child: Stack(
        children: [
          CustomPaint(
            size: Size(screenWidth, 600.rdp),
            painter: PathPainter(
              progress: widget.item.progress,
              strokeWidth: 22.rdp,
              gradientColors: widget.gradientColors,
              backgroundColor: widget.backgroundColor,
              positions: widget.item.itemList,
            ),
          )
        ],
      ),
    );
  }
}
