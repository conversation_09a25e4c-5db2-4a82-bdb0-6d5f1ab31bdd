import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/resources/jojo_colors.dart';
import 'package:jojo_flutter_base/utils/debouncer.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_node_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_task_progress_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';

class ActivityTaskWidget extends StatefulWidget {

  final double screenWidth;
  final PlanActivityDetailDataActivityVo? activityVo;

  const ActivityTaskWidget({
    super.key,
    required this.screenWidth,
    required this.activityVo,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityTaskWidgetState();
  }
}

class _ActivityTaskWidgetState extends State<ActivityTaskWidget> {

  final List<PlanActivityDetailProgressVo> _positions = [];

  @override
  void initState() {
    super.initState();
    _initPosition();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
        // 浏览埋点
        if (_ctrl.buriedString != null) {
          Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
          Map<String, dynamic> properties = {
            'c_element_name': "完课活动_活动页_展示任务进度",
            ...buriedMap,
          };
          RunEnv.sensorsTrack('ElementView',properties,);
        }
      } catch (e) {
        l.i("促完课活动", "落地页，任务组件浏览埋点异常");
      }
    });
  }

  void _initPosition() {
    List<LessonInProgressGiftModel> giftList = widget.activityVo?.giftList ?? [];
    if (giftList.length == 1) {
      _buildSingleGift(giftList);
    } else if (giftList.length == 2) {
      _buildDoubleGift(giftList);
    } else {
      _buildMultiGift(giftList);
    }
  }

  // 处理只有一个节点的情况
  void _buildSingleGift(List<LessonInProgressGiftModel> list) {
    LessonInProgressGiftModel giftModel = list.last;
    // 最终节点点位
    Offset endPoint = Offset(widget.screenWidth  - ActivityDetailSizeConstants.rightNodeCenterToRightSpace, ActivityDetailSizeConstants.taskContentTopSpace);
    double progress = giftModel.currentValue * 1.0 / giftModel.index;
    _addNode(Offset(ActivityDetailSizeConstants.startNodeLeftSpace, ActivityDetailSizeConstants.taskContentTopSpace), endPoint, progress);
    // 延伸到小旗的右侧
    _addNode(endPoint, Offset(widget.screenWidth  - ActivityDetailSizeConstants.rightNodeCenterToRightSpace + ActivityDetailSizeConstants.pregressToFlagSpace, ActivityDetailSizeConstants.taskContentTopSpace), (progress == 1) ? 1 : 0);
  }

  // 处理两个节点的情况
  void _buildDoubleGift(List<LessonInProgressGiftModel> list) {
    LessonInProgressGiftModel firstModel = list.first;
    LessonInProgressGiftModel lastModel = list.last;

    // 起始节点点位
    double offset_x = ActivityDetailSizeConstants.leftNodeLeftSpace + ActivityDetailSizeConstants.nodeWidth/2.0;
    Offset endPoint = Offset(offset_x, ActivityDetailSizeConstants.taskContentTopSpace);
    _addNode(Offset(ActivityDetailSizeConstants.startNodeLeftSpace - 4.rdp, ActivityDetailSizeConstants.taskContentTopSpace), Offset(
        offset_x - ConfigSize.nodeItemWidth/2.0,
        ActivityDetailSizeConstants.taskContentTopSpace), firstModel.currentValue * 1.0 / firstModel.index);

    // 最终节点点位
    int denominator = lastModel.index - firstModel.index;
    double progress = denominator != 0 ? (lastModel.currentValue - firstModel.index) / denominator : 0.0;
    _addNode(endPoint, Offset(widget.screenWidth  - ActivityDetailSizeConstants.rightNodeCenterToRightSpace, ActivityDetailSizeConstants.taskContentTopSpace), progress);
    endPoint = Offset(widget.screenWidth  - ActivityDetailSizeConstants.rightNodeCenterToRightSpace, ActivityDetailSizeConstants.taskContentTopSpace);

    // 延伸到小旗的右侧
    _addNode(endPoint, Offset(widget.screenWidth  - ActivityDetailSizeConstants.rightNodeCenterToRightSpace + ActivityDetailSizeConstants.pregressToFlagSpace, ActivityDetailSizeConstants.taskContentTopSpace), (progress == 1) ? 1 : 0);
  }

  // 处理多个节点的情况
  void _buildMultiGift(List<LessonInProgressGiftModel> list) {
    LessonInProgressGiftModel? preElement;
    int preRow = 0;
    Offset preEndPoint = Offset.zero;
    for (var element in list) {
      int row = list.indexOf(element) ~/ 3;
      int column = list.indexOf(element) % 3;

      double offset_x = _getOffsetX(row, column);

      // 添加起点
      if (preElement == null) {
        double offset_start_x = ActivityDetailSizeConstants.leftNodeLeftSpace + ActivityDetailSizeConstants.nodeWidth/2.0;
        Offset endPoint = Offset(offset_start_x, ActivityDetailSizeConstants.taskContentTopSpace);
        _addNode(
            Offset(ActivityDetailSizeConstants.startNodeLeftSpace - 4.rdp, ActivityDetailSizeConstants.taskContentTopSpace),
            Offset(offset_start_x - ConfigSize.nodeItemWidth/2.0, ActivityDetailSizeConstants.taskContentTopSpace),
            element.currentValue * 1.0 / element.index);
        preElement = element;
        preEndPoint = endPoint;
        continue;
      }
      // 需要添加弯道贝塞尔曲线
      if (preRow < row) {
        Offset endPoint1;
        Offset endPoint2;
        Offset endPoint3;
        Offset controlPoint1;
        Offset controlPoint2;
        double right_height_y = ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * preRow;
        if (preRow % 2 == 0) {
          double right_offset_x = widget.screenWidth - ActivityDetailSizeConstants.progressLrSpace;
          double right_center_x = widget.screenWidth - ActivityDetailSizeConstants.rightNodeCenterToRightSpace + ConfigSize.nodeItemWidth/2.0;
          // 右侧半圆
          endPoint1 = Offset(right_offset_x, ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.nodeToControlSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * preRow);
          endPoint2 = Offset(right_offset_x, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace - ActivityDetailSizeConstants.nodeToControlSpace);
          endPoint3 = Offset(right_center_x, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace);
          controlPoint1 = Offset(right_offset_x, right_height_y);
          controlPoint2 = Offset(right_offset_x, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace);
        } else {
          // 左侧半圆
          endPoint1 = Offset(ActivityDetailSizeConstants.progressLrSpace, ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.nodeToControlSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * preRow);
          endPoint2 = Offset(ActivityDetailSizeConstants.progressLrSpace, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace - ActivityDetailSizeConstants.nodeToControlSpace);
          endPoint3 = Offset(ActivityDetailSizeConstants.leftNodeLeftSpace + ActivityDetailSizeConstants.nodeWidth/2.0, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace);
          controlPoint1 = Offset(ActivityDetailSizeConstants.progressLrSpace, right_height_y);
          controlPoint2 = Offset(ActivityDetailSizeConstants.progressLrSpace, right_height_y + ActivityDetailSizeConstants.rowProgressCenterSpace);
        }
        _addCircleNode(
            preEndPoint,
            endPoint1, endPoint2, endPoint3,
            controlPoint1, controlPoint2,
            (element.currentValue - preElement.index) * 1.0 / (element.index - preElement.index));
        preEndPoint = endPoint3;
        preRow = row;
        preElement = element;

        if (element == list.last) {
          Offset flagEndPoint = Offset(
              offset_x + ActivityDetailSizeConstants.flagSpace + (row % 2 == 0? 58.rdp : -ActivityDetailSizeConstants.progressLrSpace),
              ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * row);
          _addNode(preEndPoint, flagEndPoint, (element.currentValue == element.index) ? 1.0 : 0.0);
        }
        continue;
      }

      Offset endPoint = Offset(
          offset_x + ActivityDetailSizeConstants.nodeWidth/2.0,
          ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * row);
      _addNode(preEndPoint, endPoint, (element.currentValue - preElement.index) * 1.0 / (element.index - preElement.index));
      preEndPoint = endPoint;

      if (element == list.last) {
        Offset flagEndPoint = Offset(
            offset_x + ActivityDetailSizeConstants.flagSpace + (row % 2 == 0? 58.rdp : -ActivityDetailSizeConstants.progressLrSpace),
            ActivityDetailSizeConstants.taskContentTopSpace + ActivityDetailSizeConstants.rowProgressCenterSpace * row);
        _addNode(preEndPoint, flagEndPoint, (element.currentValue == element.index) ? 1.0 : 0.0);
      }
      preElement = element;
    }
  }

  double _getOffsetX(int row, int column) {
    const int maxColumn = 3;
    final double center = widget.screenWidth / 2.0 - ActivityDetailSizeConstants.nodeWidth/2.0;
    final double right = widget.screenWidth - ActivityDetailSizeConstants.rightNodeRightSpace - ActivityDetailSizeConstants.nodeWidth;

    // 定义位置数组
    final positions = [ActivityDetailSizeConstants.leftNodeLeftSpace, center, right];
    // 奇数行（折返）反转顺序
    final orderedPositions = row % 2 == 0 ? positions : positions.reversed.toList();

    return orderedPositions[column.clamp(0, maxColumn - 1)];
  }

  void _addCircleNode(Offset? startPoint, Offset endPoint1, Offset endPoint2, Offset endPoint3, Offset controlPoint1, Offset controlPoint2, double progress) {
    PlanActivityDetailProgressVo progressVo = PlanActivityDetailProgressVo();
    PlanActivityDetailProgressItemVo item1 = PlanActivityDetailProgressItemVo();
    item1.startPoint = startPoint;
    item1.endPoint = endPoint1;
    item1.controlPoint = controlPoint1;
    progressVo.itemList.add(item1);
    progressVo.progress = progress;

    PlanActivityDetailProgressItemVo item2 = PlanActivityDetailProgressItemVo();
    item2.endPoint = endPoint2;
    progressVo.itemList.add(item2);

    PlanActivityDetailProgressItemVo item3 = PlanActivityDetailProgressItemVo();
    item3.endPoint = endPoint3;
    item3.controlPoint = controlPoint2;
    progressVo.itemList.add(item3);
    _positions.add(progressVo);
  }

  void _addNode(Offset? startPoint, Offset endPoint, double progress) {
    PlanActivityDetailProgressVo progressVo = PlanActivityDetailProgressVo();
    PlanActivityDetailProgressItemVo lastItem = PlanActivityDetailProgressItemVo();
    lastItem.startPoint = startPoint;
    lastItem.endPoint = endPoint;
    progressVo.itemList.add(lastItem);
    progressVo.progress = progress;
    _positions.add(progressVo);
  }

  // 顶部背景图
  Widget _buildTopImageWidget() {
    final file = File(widget.activityVo?.topPicVo.localPath ?? "");
    return Image.file(
      file,
      height: 50.rdp,
      fit: BoxFit.fitWidth,
    );
  }

  // 底部背景图
  Widget _buildBottomImageWidget() {
    final file = File(widget.activityVo?.bottomPicVo.localPath ?? "");
    return Image.file(
      file,
      height: 70.rdp,
      fit: BoxFit.fitWidth,
    );
  }

  // 中间背景图
  Widget _buildContentBgWidget(int centerItemCount) {
    List<Widget> list = [];
    for (int i = 0; i < centerItemCount; i++) {
      list.add(_buildSingleCenterImage());
    }
    return Column(
        children: list,
    );
  }

// 指定高度的单个中间背景图片
  Widget _buildSingleCenterImage() {
    final file = File(widget.activityVo?.middlePicVo.localPath ?? "");
    return Image.file(
      file,
      height: 10.rdp,
      fit: BoxFit.fitWidth,
    );
  }

  // 中间内容
  Widget _buildContent(double itemHeight) {
    return Container(
      color: Colors.transparent,
      child: Column(
        children: [
          Expanded(child: _buildTaskContent(itemHeight)),
          _buildButtonContent()
        ],
      ),
    );
  }

  List<Widget> _buildTaskContentChildList(double itemHeight) {
    List<Widget> list = [];
    Color endColor = HexColor(widget.activityVo?.progressEndColor ?? "#81CA42");
    for (var element in _positions) {
      list.insert(0,ActivityProgressPage(height: itemHeight, gradientColors: [endColor,endColor], backgroundColor: Colors.white, item: element,));
    }
    list.add(ActivityNodePage(finishNodeIconColor: widget.activityVo?.progressEndColor, giftList: widget.activityVo?.giftList ?? [],finishModel: widget.activityVo?.promoteFinishModel,));
    return list;
  }

  // 中间任务
  Widget _buildTaskContent(double itemHeight) {
    return Container(
      color: Colors.transparent,
      child: Stack(
        children: _buildTaskContentChildList(itemHeight),
      ),
    );
  }

  // 中间按钮
  Widget _buildButtonContent() {
    return Container(
      height: 64.rdp,
      alignment: Alignment.bottomCenter,
      child: GestureDetector(
        onTap: () {
          debouncer.run(() {
            // 保存本地标记
            jojoNativeBridge.operationNativeValueSet(key: "activity_task_study_button_click", value: "1");
            // 退出当前页
            JoJoRouter.pop();
          });
        },
        child: Container(
          width: 180.rdp,
          height: 44.rdp,
          decoration: BoxDecoration(
            color: JoJoColors.yellow4,
            borderRadius: BorderRadius.circular(22.rdp),
            border: Border.all(
                color: Colors.white,
                width: 2.rdp),
          ),
          alignment: Alignment.center,
          child: Text(
            "去学习",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: JoJoColors.yellow6,
                fontSize: 18.rdp,
                fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    int length = widget.activityVo?.giftList.length ?? 0;
    int row = (length/3).ceil();
    double height = 127.rdp + row * 114.rdp + (row - 1) * 20.rdp;
    int centerItemCount = ((height - 120.rdp)/10.rdp).ceil();
    return Container(
      height: centerItemCount * 10.rdp + 120.rdp,
      color: Colors.transparent,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildTopImageWidget(),
          ),
          Positioned(
            top: 50.rdp,
            left: 0.rdp,
            right: 0.rdp,
            bottom: 70.rdp,
            child: _buildContentBgWidget(centerItemCount),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomImageWidget(),
          ),
          Positioned(
            top: ActivityDetailSizeConstants.progressLrSpace,
            left: 0.rdp,
            right: 0.rdp,
            bottom: 33.rdp,
            child: _buildContent(centerItemCount * 10.rdp - 7.rdp),
          ),
        ],
      ),
    );
  }
}
