import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';

class ActivityImageWidget extends StatefulWidget {

  final PlanActivityDetailDataPicVo? picVo;

  const ActivityImageWidget({
    super.key,
    required this.picVo,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityImageWidgetState();
  }
}

class _ActivityImageWidgetState extends State<ActivityImageWidget> {

  @override
  Widget build(BuildContext context) {
    return ImageNetworkCached(
      fit: BoxFit.fitWidth,
      imageUrl: widget.picVo?.url ?? "",
    );
  }
}
