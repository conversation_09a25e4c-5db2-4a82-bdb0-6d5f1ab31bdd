import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ActivityVideoWidget extends StatefulWidget {

  final PlanActivityDetailDataVideoVo? videoVo;

  const ActivityVideoWidget({
    super.key,
    required this.videoVo,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityVideoWidgetState();
  }
}

class _ActivityVideoWidgetState extends State<ActivityVideoWidget> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
        // 浏览埋点
        if (_ctrl.buriedString != null) {
          Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
          Map<String, dynamic> properties = {
            'c_element_name': "完课活动_活动页_展示视频",
            ...buriedMap,
          };
          RunEnv.sensorsTrack('ElementView',properties,);
        }
      } catch (e) {
        l.i("促完课活动", "落地页，视频组件浏览埋点异常：${e.toString()}");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ImageNetworkCached(
          fit: BoxFit.fitWidth,
          imageUrl: widget.videoVo?.picVo.url ?? "",
        ),
        Positioned.fill(
          child: Center(
            child: GestureDetector(
              onTap: () {
                if (!mounted) return;
                if (widget.videoVo?.videoUrl?.isNotEmpty == true) {
                  try {
                    PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
                    // 浏览埋点
                    if (_ctrl.buriedString != null) {
                      Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
                      Map<String, dynamic> properties = {
                        '\$element_name': "完课活动_活动页_点击查看视频",
                        ...buriedMap,
                      };
                      RunEnv.sensorsTrack("\$AppClick", properties);
                    }
                  } catch (e) {
                    l.i("促完课活动", "落地页，视频组件点击埋点异常");
                  }
                  RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/planHomeVideoIntroducePage?windowType=window&url=${widget.videoVo?.videoUrl}&isFromDetailPage=1");
                }
              },
              child: Container(
                margin: EdgeInsets.only(left: 20.rdp,right: 20.rdp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(20.rdp)),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.all(Radius.circular(20.rdp)),
                      child: ImageNetworkCached(
                        fit: BoxFit.cover,
                        imageUrl: widget.videoVo?.coverUrl ?? "",
                      ),
                    ),
                    Positioned.fill(
                        child: Center(
                            child: ImageAssetWeb(
                              assetName: AssetsImg.ICON_PLAY_BTN,
                              width: 70.rdp,
                              height: 70.rdp,
                              package: Config.package,
                            )
                        )
                    )
                  ],
                ),
              ),
            ),
          )
        )
      ],
    );
  }
}
