import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_node_item_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ActivityNodePage extends StatefulWidget {

  final List<LessonInProgressGiftModel> giftList;
  final PromoteLessonFinishModel? finishModel;
  final String? finishNodeIconColor;

  const ActivityNodePage({
    super.key,
    required this.giftList,
    required this.finishModel,
    required this.finishNodeIconColor
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityNodePageState();
  }
}

class _ActivityNodePageState extends State<ActivityNodePage> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: _buildChildList(),
    );
  }

  Widget _buildNodeItem(LessonInProgressGiftModel giftModel, bool isLastTargetNode) {
    return SizedBox(
      width: ActivityDetailSizeConstants.nodeWidth + ActivityDetailSizeConstants.nodeFinishRightSpace,
      height: ActivityDetailSizeConstants.nodeHeight,
      child: Stack(
        children: [
          Positioned(
              left: 0.rdp,
              child: ActivityNodeItemPage(finishNodeIconColor: widget.finishNodeIconColor, giftModel: giftModel, finishModel: widget.finishModel, isLastTargetNode: isLastTargetNode)
          ),
          Visibility(
            visible: giftModel.currentValue == giftModel.index,
            child: Positioned(
              top: -2.rdp,
              right: 0.rdp,
              child: ImageAssetWeb(
                assetName:  AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_NODE_FINISH,
                height: 40.rdp,
                width: 40.rdp,
                package: Config.package,
                fit: BoxFit.contain,
              ),),
          )
        ],
      ),
    );
  }

  List<Widget> _buildChildList() {
    List<Widget> list = [];
    // 起点图标
    list.add(Positioned(
        left: 20.rdp,
        top: ActivityDetailSizeConstants.nodeWidth,
        child: ImageAssetWeb(
          assetName:  AssetsImg.PLAN_ACTIVITY_TASK_BG_START,
          height: 38.rdp,
          package: Config.package,
          fit: BoxFit.cover,
        )
    ));

    double offset_x = ActivityDetailSizeConstants.leftNodeLeftSpace;
    double offset_y = 0;
    double screenWidth = MediaQuery.of(context).size.width;

    bool isLeft = false;  // 小旗帜的布局方向
    if (widget.giftList.length == 1) {
      offset_x = screenWidth - ActivityDetailSizeConstants.rightNodeRightSpace - ActivityDetailSizeConstants.nodeWidth;
      // 仅添加最终奖励节点
      list.add(Positioned(
          left: offset_x,
          top: 0,
          child: _buildNodeItem(widget.giftList.first, true)
      ));
    } else if (widget.giftList.length == 2) {
      offset_x = screenWidth - ActivityDetailSizeConstants.rightNodeRightSpace - ActivityDetailSizeConstants.nodeWidth;
      // 添加第一个节点
      list.add(Positioned(
          left: ActivityDetailSizeConstants.leftNodeLeftSpace,
          top: 0,
          child: _buildNodeItem(widget.giftList.first, false)
      ));
      // 添加最终奖励节点
      list.add(Positioned(
          left: offset_x,
          top: 0,
          child: _buildNodeItem(widget.giftList.last, true)
      ));
    } else {
      for (var element in widget.giftList) {
        int row = widget.giftList.indexOf(element) ~/ 3;
        int column = widget.giftList.indexOf(element) % 3;
        if (row % 2 == 0) {
          // 偶数行（0-2.....）正着添加
          if (column == 0) {
            offset_x = ActivityDetailSizeConstants.leftNodeLeftSpace;
          } else if (column == 1) {
            offset_x = screenWidth/2.0 - ActivityDetailSizeConstants.nodeWidth/2.0;
          } else {
            offset_x = screenWidth - ActivityDetailSizeConstants.rightNodeRightSpace - ActivityDetailSizeConstants.nodeWidth;
          }
        } else {
          // 奇数行（0-2.....）逆序添加
          if (column == 0) {
            offset_x = screenWidth - ActivityDetailSizeConstants.rightNodeRightSpace - ActivityDetailSizeConstants.nodeWidth;
          } else if (column == 1) {
            offset_x = screenWidth/2.0 - ActivityDetailSizeConstants.nodeWidth/2.0;
          } else {
            offset_x = ActivityDetailSizeConstants.leftNodeLeftSpace;
          }
        }
        offset_y = row * 20.rdp + row * ActivityDetailSizeConstants.nodeHeight;
        list.add(Positioned(
            left: offset_x,
            top: offset_y,
            child: _buildNodeItem(element, element == widget.giftList.last)
        ));
        // 如果是最终节点，那么标记小旗帜的位置
        if (element == widget.giftList.last) {
          isLeft = row % 2 != 0;
        }
      }
    }
    // 添加最终点的小旗帜
    list.add(Positioned(
        left: isLeft ? (offset_x - 38.rdp + 5.rdp) : (offset_x + ActivityDetailSizeConstants.nodeWidth - 5.rdp),
        top: offset_y + ActivityDetailSizeConstants.nodeHeight - 38.rdp - 6.rdp,
        child: ImageAssetWeb(
          assetName:  AssetsImg.PLAN_ACTIVITY_TASK_END,
          height: 38.rdp,
          package: Config.package,
          fit: BoxFit.cover,
        )
    ));
    return list;
  }
}
