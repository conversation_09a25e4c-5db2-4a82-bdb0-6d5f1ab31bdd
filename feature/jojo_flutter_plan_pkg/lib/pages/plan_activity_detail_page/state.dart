import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
class PlanActivityDetailState {
  PageStatus pageStatus;
  String? buriedString; // 埋点数据
  int? activityId;
  int? classId;
  int? courseId;
  int? pageId;
  Exception? exception;
  PlanActivityData? data;
  PlanActivityDetailData? detailData;

  PlanActivityDetailState({
    this.data,
    this.detailData,
    required this.pageStatus,
    required this.buriedString,
    required this.activityId,
    required this.classId,
    required this.courseId,
    required this.pageId,
  });

  PlanActivityDetailState copyWith() {
    return PlanActivityDetailState(
      pageStatus: pageStatus,
      buriedString: buriedString,
      activityId: activityId,
      classId: classId,
      courseId: courseId,
      pageId: pageId,
      data: data,
      detailData: detailData,
    );
  }
}
