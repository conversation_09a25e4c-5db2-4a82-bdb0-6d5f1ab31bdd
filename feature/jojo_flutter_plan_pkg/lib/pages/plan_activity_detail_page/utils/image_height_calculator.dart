import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_utils.dart';

/// 图片高度计算器
/// 用于计算列表中图片元素的实际显示高度
class ImageHeightCalculator {
  static final Map<String, Size> _sizeCache = {}; // 图片尺寸缓存
  static const Duration _cacheTimeout = Duration(hours: 1); // 缓存超时时间
  static final Map<String, DateTime> _cacheTimestamps = {}; // 缓存时间戳

  /// 计算指定元素列表的总高度
  /// [elements] 要计算的元素列表
  /// [screenWidth] 屏幕宽度
  /// [onComplete] 计算完成回调，返回总高度
  /// [onProgress] 进度回调，返回 (已完成数量, 总数量, 当前总高度)
  static Future<void> calculateTotalHeight({
    required List<dynamic> elements,
    required double screenWidth,
    required Function(double totalHeight) onComplete,
    Function(int completed, int total, double currentHeight)? onProgress,
  }) async {
    if (elements.isEmpty) {
      onComplete(0.0);
      return;
    }

    double totalHeight = 0.0;
    int completedCount = 0;
    final int totalCount = elements.length;

    for (int i = 0; i < elements.length; i++) {
      final element = elements[i];
      final String? imageUrl = _extractImageUrl(element);

      if (imageUrl != null && imageUrl.isNotEmpty) {
        try {
          final Size imageSize = await _getImageSize(imageUrl);
          if (imageSize != Size.zero) {
            final double actualHeight = _calculateActualHeight(imageSize, screenWidth);
            totalHeight += actualHeight;
          }
        } catch (e) {
          print(e.toString());
        }
      }

      completedCount++;
      onProgress?.call(completedCount, totalCount, totalHeight);
    }
    onComplete(totalHeight);
  }

  /// 计算 ActivityVo 之前元素的总高度
  static Future<void> calculateHeightBeforeActivity({
    required List<dynamic> itemList,
    required double screenWidth,
    required Function(double totalHeight) onComplete,
    Function(int completed, int total, double currentHeight)? onProgress,
  }) async {
    // 找到第一个 PlanActivityDetailDataActivityVo 的索引
    int activityIndex = -1;
    for (int i = 0; i < itemList.length; i++) {
      if (itemList[i] is PlanActivityDetailDataActivityVo) {
        activityIndex = i;
        break;
      }
    }

    if (activityIndex == -1) {
      onComplete(0.0);
      return;
    }

    // 获取 ActivityVo 之前的元素
    final elementsBeforeActivity = itemList.sublist(0, activityIndex);
    
    await calculateTotalHeight(
      elements: elementsBeforeActivity,
      screenWidth: screenWidth,
      onComplete: onComplete,
      onProgress: onProgress,
    );
  }

  /// 从元素中提取图片URL
  static String? _extractImageUrl(dynamic element) {
    if (element is PlanActivityDetailDataPicVo) {
      return element.url;
    } else if (element is PlanActivityDetailDataVideoVo) {
      return element.picVo.url;
    }
    return null;
  }

  /// 获取图片尺寸（带缓存）
  static Future<Size> _getImageSize(String imageUrl) async {
    // 检查缓存
    if (_sizeCache.containsKey(imageUrl)) {
      final cacheTime = _cacheTimestamps[imageUrl];
      if (cacheTime != null && DateTime.now().difference(cacheTime) < _cacheTimeout) {
        return _sizeCache[imageUrl]!;
      } else {
        // 缓存过期，清除
        _sizeCache.remove(imageUrl);
        _cacheTimestamps.remove(imageUrl);
      }
    }

    // 从网络获取
    final Size size = await _loadImageSizeFromNetwork(imageUrl);
    
    // 缓存结果
    if (size != Size.zero) {
      _sizeCache[imageUrl] = size;
      _cacheTimestamps[imageUrl] = DateTime.now();
    }

    return size;
  }

  /// 从网络加载图片尺寸
  static Future<Size> _loadImageSizeFromNetwork(String imageUrl) async {
    final completer = Completer<Size>();
    
    CourseUtils.getSizeByLoadImage(imageUrl, (Size size) {
      if (!completer.isCompleted) {
        completer.complete(size);
      }
    });

    // 设置超时
    Timer(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        completer.complete(Size.zero);
      }
    });

    return completer.future;
  }

  /// 根据图片原始尺寸和屏幕宽度计算实际显示高度
  static double _calculateActualHeight(Size imageSize, double screenWidth) {
    if (imageSize.width <= 0) return 0.0;
    
    final double aspectRatio = imageSize.width / imageSize.height;
    return screenWidth / aspectRatio;
  }

  /// 清除缓存
  static void clearCache() {
    _sizeCache.clear();
    _cacheTimestamps.clear();
  }

  /// 获取缓存统计信息
  static Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int validCacheCount = 0;
    int expiredCacheCount = 0;

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) < _cacheTimeout) {
        validCacheCount++;
      } else {
        expiredCacheCount++;
      }
    }

    return {
      'totalCached': _sizeCache.length,
      'validCache': validCacheCount,
      'expiredCache': expiredCacheCount,
    };
  }
}
