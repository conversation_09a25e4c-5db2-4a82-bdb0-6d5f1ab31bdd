
import 'dart:async';
import 'dart:io';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/spine_download_manager.dart';
import 'package:jojo_flutter_plan_pkg/service/plan_activity_detail_api.dart';

class PlanActivityDetailCtrl extends Cubit<PlanActivityDetailState> {
  final String? buriedString; // 埋点数据
  final String? subjectColor;
  final int? activityId;
  final int? classId;
  final int? courseId;
  final int? pageId;
  ActivityDetailApi? pageApi;
  AudioPlayer? audioPlayer;
  StreamSubscription<PlayerState>? playerStateSubscription; // 音频播放状态监听器
  bool isPlayAudio = false;

  PlanActivityDetailCtrl({
        ActivityDetailApi? api,
        required this.buriedString,
        required this.subjectColor,
        required this.activityId,
        required this.classId,
        required this.courseId,
        required this.pageId,}
      ) : super(PlanActivityDetailState(
      pageStatus: PageStatus.loading,
      buriedString: buriedString,
      activityId: activityId,
    classId: classId,
    courseId: courseId,
    pageId: pageId,
  ),) {
    pageApi = api ?? ActivityDetailApis;
  }

  // 请求数据
  Future<PlanActivityData?> requestData() async {
    if ((activityId ?? 0) == 0 || (classId ?? 0) == 0 || (courseId ?? 0) == 0) {
      l.e("促完课活动", "详情页请求，关键参数为空：$activityId $classId $courseId");
      return null;
    }
    return await pageApi?.getCourseSegmentsInfo(activityId: activityId ?? 0, classId: classId ?? 0, courseId: courseId ?? 0, pageId: pageId ?? 0);
  }

  Future<void> getDetailInfoData() async {
    try {
      PlanActivityData? data = await requestData();
      PlanActivityDetailData? detailData = PlanActivityDetailData.getDataModel(data, activityId ?? 0);
      downloadResource(data, detailData);
    } catch (e) {
      showErrorPage(Exception("接口请求失败"), "详情页请求失败");
    }
  }

  void refreshPage() {
    PlanActivityDetailState newState = state.copyWith();
    newState.pageStatus = PageStatus.loading;
    emit(newState);
    getDetailInfoData();
  }

  void showErrorPage(Exception exception, String? logString)  {
    l.e("促完课活动", logString ?? "");
    PlanActivityDetailState newState = state.copyWith();
    newState.pageStatus = PageStatus.error;
    newState.exception = exception;
    emit(newState);
  }

  /// 下载资源
  void downloadResource(PlanActivityData? data,PlanActivityDetailData? detailData) {
    if (detailData == null) {
      showErrorPage(Exception("数据组装失败"), "详情页请求，数据组装失败");
      return;
    }
    if (data == null) {
      showErrorPage(Exception("请求失败"), "详情页请求，数据返回为空");
      return;
    }

    // 下载图片资源
    _downloadRes(false, detailData.imageUrlList, successListener: (Map<String, String> map) {
      dealWithImageData(map, detailData, false);
    }, failedListener: () {
      showErrorPage(Exception("请求失败"), "图标资源下载失败");
    });
  }

  void dealWithImageData(Map<String, String> map, PlanActivityDetailData data, bool notNeedCheck) {
    // 处理下载好的图片数据（只处理任务的图片，其他图片自适应显示）
    for (var element in data.itemList) {
      if (element is PlanActivityDetailDataActivityVo) {
        updateLocalPath(element.topPicVo, map);
        updateLocalPath(element.middlePicVo, map);
        updateLocalPath(element.bottomPicVo, map);
      }
    }

    if (isEssentialPicResourcesDownloaded(data, false) || notNeedCheck) {

      // 下载节点资源
      _downloadRes(true, data.animationResourceList, successListener: (Map<String, String> map) {
        dealWithAnimationData(map, data, false);
      }, failedListener: () {
        showErrorPage(Exception("请求失败"), "动效资源下载失败");
      });
    }
  }

  void updateLocalPath(PlanActivityDetailDataPicVo? picVo, Map<String, String> map) {
    if (map.keys.contains(picVo?.url)) {
      picVo?.localPath = map[picVo.url];
    }
  }

  void dealWithAnimationData(Map<String, String> map, PlanActivityDetailData data, bool notNeedCheck) {
    for (var element in data.activityVo.giftList) {
      if (map.keys.contains(element.spineResourceVo?.resourceUrl)) {
        element.spineResourceVo?.localPath = map[element.spineResourceVo?.resourceUrl];
        element.spineResourceVo?.isDowned = true;
      }
    }
    String? badgeBgResource = data.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo?.badgeBgResource.resourceUrl;
    if (map.keys.contains(badgeBgResource)) {
      data.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo?.badgeBgResource.isDowned = true;
      data.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo?.badgeBgResource.localPath = map[badgeBgResource];
    }

    // 检查是否所有资源都下载完成
    if (isEssentialAnimationResourcesDownloaded(data, false) || notNeedCheck) {
      onAllResourcesDownloaded(data);
    }
  }

  /// 所有资源下载完成后的回调
  void onAllResourcesDownloaded(PlanActivityDetailData data) {
    PlanActivityDetailState newState = state.copyWith();
    newState.pageStatus = PageStatus.success;
    newState.detailData = data;
    emit(newState);
  }

  // 下载资源
  Future<void> _downloadRes(bool needUnzip, List<String> urlList,
      {Function(Map<String, String>)? successListener, Function()? failedListener}) async {
    try {
      GlobalDownloadManager downloadManager = GlobalDownloadManager();
      Map<String, String> result = await downloadManager.downloadResources(
        urlList,
        needUnzip: needUnzip, // ZIP资源需要解压
      );

      // 调用成功回调
      successListener?.call(result);
    } catch (e) {
      callBackFailedListener("落地页${needUnzip?"动效":"图片"}资源${needUnzip?"解压":"下载"}失败: $e",failedListener);
    }
  }

  void callBackFailedListener(String? logTxt, Function()? failedListener) {
    if (failedListener != null) {
      failedListener();
    }
    if (logTxt != null) l.e("促完课活动", logTxt);
  }

  bool isEssentialAnimationResourcesDownloaded(PlanActivityDetailData data, bool notNeedCheek) {
    // 只检查 giftList 中的动效资源
    bool allGiftsDownloaded = data.activityVo.giftList.every(
      (gift) => gift.spineResourceVo?.isDowned == true
    );

    // 检查背景动效是否下载完毕
    bool bgResourceDownloaded = data.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo?.badgeBgResource.isDowned ?? false;
    bool result = allGiftsDownloaded && bgResourceDownloaded;

    if (result || notNeedCheek) {
      l.i("促完课活动", "动效资源下载完成");
    }

    return result;
  }

  bool isEssentialPicResourcesDownloaded(PlanActivityDetailData data, bool notNeedCheek) {
    // 只检查 itemList 中的图片资源
    bool allImagesDownloaded = data.itemList.every((item) {
      if (item is PlanActivityDetailDataActivityVo) {
        bool topPicDownloaded = item.topPicVo.localPath != null && item.topPicVo.localPath!.isNotEmpty;
        bool middlePicDownloaded = item.middlePicVo.localPath != null && item.middlePicVo.localPath!.isNotEmpty;
        bool bottomPicDownloaded = item.bottomPicVo.localPath != null && item.bottomPicVo.localPath!.isNotEmpty;
        return topPicDownloaded && middlePicDownloaded && bottomPicDownloaded;
      }
      return true; // 其他类型的 item 不检查
    });

    if (allImagesDownloaded || notNeedCheek) {
      l.i("促完课活动", "图片资源下载完成");
    }

    return allImagesDownloaded;
  }
}
