import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/customization/landscape_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/new_subject_recm/page.dart';

class PlanRouterInterceptor extends JoJoRouteInterceptor {
  @override
  JoJoRouteInterceptorResponse intercept(String url) {
    print('PlanRouterInterceptor intercept $url');
    bool stopped = false;
    if (url.contains(AppPage.newSubjectRecommendedPage.path) &&
        !LandscapeUtils.isLandscape()) {
      // 竖版的更多科目底部弹窗形式展示
      stopped = true;
      Uri uri = Uri.parse(url);
      showNewSubjectRecommendedBottomSheet(uri.queryParameters);
    }
    return JoJoRouteInterceptorResponse(stopped: stopped);
  }
}
