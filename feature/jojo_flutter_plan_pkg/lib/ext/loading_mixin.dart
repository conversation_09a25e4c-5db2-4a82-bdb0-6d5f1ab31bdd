import 'dart:async';

import 'package:jojo_flutter_base/base.dart';

mixin LoadingMixin<T> on Cubit<T>{
  Timer? loadingTimer;

  void startLoadingTimer() {
    cancelLoadingTimer();
    loadingTimer ??= Timer(const Duration(milliseconds: 300), () {
      emitLoading();
    });
  }

  void cancelLoadingTimer() {
    if (loadingTimer != null && loadingTimer?.isActive == true) {
      loadingTimer?.cancel();
    }
    loadingTimer = null;
  }

  void emitLoading();
}