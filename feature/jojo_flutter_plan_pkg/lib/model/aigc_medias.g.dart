// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'aigc_medias.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_AigcMedias _$$_AigcMediasFromJson(Map<String, dynamic> json) =>
    _$_AigcMedias(
      sceneResults: (json['sceneResults'] as List<dynamic>?)
          ?.map((e) => SceneResult.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_AigcMediasToJson(_$_AigcMedias instance) =>
    <String, dynamic>{
      'sceneResults': instance.sceneResults,
    };

_$_SceneResult _$$_SceneResultFromJson(Map<String, dynamic> json) =>
    _$_SceneResult(
      sceneKey: json['sceneKey'] as String?,
      medias: (json['medias'] as List<dynamic>?)
          ?.map((e) => Media.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_SceneResultToJson(_$_SceneResult instance) =>
    <String, dynamic>{
      'sceneKey': instance.sceneKey,
      'medias': instance.medias,
    };

_$_Media _$$_MediaFromJson(Map<String, dynamic> json) => _$_Media(
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$_MediaToJson(_$_Media instance) => <String, dynamic>{
      'url': instance.url,
    };
