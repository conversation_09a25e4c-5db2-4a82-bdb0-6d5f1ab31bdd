// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_content_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ContactCustomerContentList _$ContactCustomerContentListFromJson(
    Map<String, dynamic> json) {
  return _ContactCustomerContentList.fromJson(json);
}

/// @nodoc
mixin _$ContactCustomerContentList {
  String? get color => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get linkUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactCustomerContentListCopyWith<ContactCustomerContentList>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactCustomerContentListCopyWith<$Res> {
  factory $ContactCustomerContentListCopyWith(ContactCustomerContentList value,
          $Res Function(ContactCustomerContentList) then) =
      _$ContactCustomerContentListCopyWithImpl<$Res,
          ContactCustomerContentList>;
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class _$ContactCustomerContentListCopyWithImpl<$Res,
        $Val extends ContactCustomerContentList>
    implements $ContactCustomerContentListCopyWith<$Res> {
  _$ContactCustomerContentListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_value.copyWith(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ContactCustomerContentListCopyWith<$Res>
    implements $ContactCustomerContentListCopyWith<$Res> {
  factory _$$_ContactCustomerContentListCopyWith(
          _$_ContactCustomerContentList value,
          $Res Function(_$_ContactCustomerContentList) then) =
      __$$_ContactCustomerContentListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? color, String? text, String? linkUrl});
}

/// @nodoc
class __$$_ContactCustomerContentListCopyWithImpl<$Res>
    extends _$ContactCustomerContentListCopyWithImpl<$Res,
        _$_ContactCustomerContentList>
    implements _$$_ContactCustomerContentListCopyWith<$Res> {
  __$$_ContactCustomerContentListCopyWithImpl(
      _$_ContactCustomerContentList _value,
      $Res Function(_$_ContactCustomerContentList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? color = freezed,
    Object? text = freezed,
    Object? linkUrl = freezed,
  }) {
    return _then(_$_ContactCustomerContentList(
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      linkUrl: freezed == linkUrl
          ? _value.linkUrl
          : linkUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ContactCustomerContentList implements _ContactCustomerContentList {
  const _$_ContactCustomerContentList({this.color, this.text, this.linkUrl});

  factory _$_ContactCustomerContentList.fromJson(Map<String, dynamic> json) =>
      _$$_ContactCustomerContentListFromJson(json);

  @override
  final String? color;
  @override
  final String? text;
  @override
  final String? linkUrl;

  @override
  String toString() {
    return 'ContactCustomerContentList(color: $color, text: $text, linkUrl: $linkUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ContactCustomerContentList &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.linkUrl, linkUrl) || other.linkUrl == linkUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, color, text, linkUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ContactCustomerContentListCopyWith<_$_ContactCustomerContentList>
      get copyWith => __$$_ContactCustomerContentListCopyWithImpl<
          _$_ContactCustomerContentList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ContactCustomerContentListToJson(
      this,
    );
  }
}

abstract class _ContactCustomerContentList
    implements ContactCustomerContentList {
  const factory _ContactCustomerContentList(
      {final String? color,
      final String? text,
      final String? linkUrl}) = _$_ContactCustomerContentList;

  factory _ContactCustomerContentList.fromJson(Map<String, dynamic> json) =
      _$_ContactCustomerContentList.fromJson;

  @override
  String? get color;
  @override
  String? get text;
  @override
  String? get linkUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ContactCustomerContentListCopyWith<_$_ContactCustomerContentList>
      get copyWith => throw _privateConstructorUsedError;
}
