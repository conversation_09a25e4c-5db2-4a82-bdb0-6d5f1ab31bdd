class ShareImgParams {
  String? backImg;
  String? cardImg;
  String? avatar;
  String? nickName;
  String? title;
  String? shareUrl;
  String? citeName;
  String? date;
  int? count;
  String? shareSubCopywriting;
  String? shareCopywriting;
  String? obtainReasonCopywriting;
  String? obtainCountCopywriting;

  ShareImgParams({
    this.avatar,
    this.backImg,
    this.cardImg,
    this.citeName,
    this.nickName,
    this.shareUrl,
    this.title,
    this.count,
    this.date,
    this.shareSubCopywriting,
    this.shareCopywriting,
    this.obtainReasonCopywriting,
    this.obtainCountCopywriting,
  });

  Map<String, dynamic> toJson(ShareImgParams instance) => <String, dynamic>{
        'avatar': instance.avatar,
        'backImg': instance.backImg,
        'cardImg': instance.cardImg,
        'citeName': instance.citeName,
        'nickName': instance.nickName,
        'shareUrl': instance.shareUrl,
        'title': instance.title,
        'count': instance.count.toString(),
        'date': instance.date,
        'shareSubCopywriting': instance.shareSubCopywriting,
        'shareCopywriting': instance.shareCopywriting,
        'obtainReasonCopywriting': instance.obtainReasonCopywriting,
        'obtainCountCopywriting': instance.obtainCountCopywriting,
      };
}
