// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'baby_info_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

GradeInfo _$GradeInfoFromJson(Map<String, dynamic> json) {
  return _GradeInfo.fromJson(json);
}

/// @nodoc
mixin _$GradeInfo {
  int? get code => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<GradeInfo>? get subDict => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GradeInfoCopyWith<GradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GradeInfoCopyWith<$Res> {
  factory $GradeInfoCopyWith(GradeInfo value, $Res Function(GradeInfo) then) =
      _$GradeInfoCopyWithImpl<$Res, GradeInfo>;
  @useResult
  $Res call({int? code, String? name, List<GradeInfo>? subDict});
}

/// @nodoc
class _$GradeInfoCopyWithImpl<$Res, $Val extends GradeInfo>
    implements $GradeInfoCopyWith<$Res> {
  _$GradeInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? name = freezed,
    Object? subDict = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subDict: freezed == subDict
          ? _value.subDict
          : subDict // ignore: cast_nullable_to_non_nullable
              as List<GradeInfo>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GradeInfoCopyWith<$Res> implements $GradeInfoCopyWith<$Res> {
  factory _$$_GradeInfoCopyWith(
          _$_GradeInfo value, $Res Function(_$_GradeInfo) then) =
      __$$_GradeInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, String? name, List<GradeInfo>? subDict});
}

/// @nodoc
class __$$_GradeInfoCopyWithImpl<$Res>
    extends _$GradeInfoCopyWithImpl<$Res, _$_GradeInfo>
    implements _$$_GradeInfoCopyWith<$Res> {
  __$$_GradeInfoCopyWithImpl(
      _$_GradeInfo _value, $Res Function(_$_GradeInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? name = freezed,
    Object? subDict = freezed,
  }) {
    return _then(_$_GradeInfo(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      subDict: freezed == subDict
          ? _value._subDict
          : subDict // ignore: cast_nullable_to_non_nullable
              as List<GradeInfo>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GradeInfo with DiagnosticableTreeMixin implements _GradeInfo {
  const _$_GradeInfo({this.code, this.name, final List<GradeInfo>? subDict})
      : _subDict = subDict;

  factory _$_GradeInfo.fromJson(Map<String, dynamic> json) =>
      _$$_GradeInfoFromJson(json);

  @override
  final int? code;
  @override
  final String? name;
  final List<GradeInfo>? _subDict;
  @override
  List<GradeInfo>? get subDict {
    final value = _subDict;
    if (value == null) return null;
    if (_subDict is EqualUnmodifiableListView) return _subDict;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'GradeInfo(code: $code, name: $name, subDict: $subDict)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'GradeInfo'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('subDict', subDict));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GradeInfo &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._subDict, _subDict));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, name, const DeepCollectionEquality().hash(_subDict));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GradeInfoCopyWith<_$_GradeInfo> get copyWith =>
      __$$_GradeInfoCopyWithImpl<_$_GradeInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GradeInfoToJson(
      this,
    );
  }
}

abstract class _GradeInfo implements GradeInfo {
  const factory _GradeInfo(
      {final int? code,
      final String? name,
      final List<GradeInfo>? subDict}) = _$_GradeInfo;

  factory _GradeInfo.fromJson(Map<String, dynamic> json) =
      _$_GradeInfo.fromJson;

  @override
  int? get code;
  @override
  String? get name;
  @override
  List<GradeInfo>? get subDict;
  @override
  @JsonKey(ignore: true)
  _$$_GradeInfoCopyWith<_$_GradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
