// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'aigc_medias.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AigcMedias _$AigcMediasFromJson(Map<String, dynamic> json) {
  return _AigcMedias.fromJson(json);
}

/// @nodoc
mixin _$AigcMedias {
  /// 场景结果集
  List<SceneResult>? get sceneResults => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AigcMediasCopyWith<AigcMedias> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AigcMediasCopyWith<$Res> {
  factory $AigcMediasCopyWith(
          AigcMedias value, $Res Function(AigcMedias) then) =
      _$AigcMediasCopyWithImpl<$Res, AigcMedias>;
  @useResult
  $Res call({List<SceneResult>? sceneResults});
}

/// @nodoc
class _$AigcMediasCopyWithImpl<$Res, $Val extends AigcMedias>
    implements $AigcMediasCopyWith<$Res> {
  _$AigcMediasCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sceneResults = freezed,
  }) {
    return _then(_value.copyWith(
      sceneResults: freezed == sceneResults
          ? _value.sceneResults
          : sceneResults // ignore: cast_nullable_to_non_nullable
              as List<SceneResult>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AigcMediasCopyWith<$Res>
    implements $AigcMediasCopyWith<$Res> {
  factory _$$_AigcMediasCopyWith(
          _$_AigcMedias value, $Res Function(_$_AigcMedias) then) =
      __$$_AigcMediasCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SceneResult>? sceneResults});
}

/// @nodoc
class __$$_AigcMediasCopyWithImpl<$Res>
    extends _$AigcMediasCopyWithImpl<$Res, _$_AigcMedias>
    implements _$$_AigcMediasCopyWith<$Res> {
  __$$_AigcMediasCopyWithImpl(
      _$_AigcMedias _value, $Res Function(_$_AigcMedias) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sceneResults = freezed,
  }) {
    return _then(_$_AigcMedias(
      sceneResults: freezed == sceneResults
          ? _value._sceneResults
          : sceneResults // ignore: cast_nullable_to_non_nullable
              as List<SceneResult>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AigcMedias implements _AigcMedias {
  _$_AigcMedias({final List<SceneResult>? sceneResults})
      : _sceneResults = sceneResults;

  factory _$_AigcMedias.fromJson(Map<String, dynamic> json) =>
      _$$_AigcMediasFromJson(json);

  /// 场景结果集
  final List<SceneResult>? _sceneResults;

  /// 场景结果集
  @override
  List<SceneResult>? get sceneResults {
    final value = _sceneResults;
    if (value == null) return null;
    if (_sceneResults is EqualUnmodifiableListView) return _sceneResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AigcMedias(sceneResults: $sceneResults)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AigcMedias &&
            const DeepCollectionEquality()
                .equals(other._sceneResults, _sceneResults));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_sceneResults));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AigcMediasCopyWith<_$_AigcMedias> get copyWith =>
      __$$_AigcMediasCopyWithImpl<_$_AigcMedias>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AigcMediasToJson(
      this,
    );
  }
}

abstract class _AigcMedias implements AigcMedias {
  factory _AigcMedias({final List<SceneResult>? sceneResults}) = _$_AigcMedias;

  factory _AigcMedias.fromJson(Map<String, dynamic> json) =
      _$_AigcMedias.fromJson;

  @override

  /// 场景结果集
  List<SceneResult>? get sceneResults;
  @override
  @JsonKey(ignore: true)
  _$$_AigcMediasCopyWith<_$_AigcMedias> get copyWith =>
      throw _privateConstructorUsedError;
}

SceneResult _$SceneResultFromJson(Map<String, dynamic> json) {
  return _SceneResult.fromJson(json);
}

/// @nodoc
mixin _$SceneResult {
  /// 场景 Key
  String? get sceneKey => throw _privateConstructorUsedError;

  /// 媒体信息列表
  List<Media>? get medias => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SceneResultCopyWith<SceneResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SceneResultCopyWith<$Res> {
  factory $SceneResultCopyWith(
          SceneResult value, $Res Function(SceneResult) then) =
      _$SceneResultCopyWithImpl<$Res, SceneResult>;
  @useResult
  $Res call({String? sceneKey, List<Media>? medias});
}

/// @nodoc
class _$SceneResultCopyWithImpl<$Res, $Val extends SceneResult>
    implements $SceneResultCopyWith<$Res> {
  _$SceneResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sceneKey = freezed,
    Object? medias = freezed,
  }) {
    return _then(_value.copyWith(
      sceneKey: freezed == sceneKey
          ? _value.sceneKey
          : sceneKey // ignore: cast_nullable_to_non_nullable
              as String?,
      medias: freezed == medias
          ? _value.medias
          : medias // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SceneResultCopyWith<$Res>
    implements $SceneResultCopyWith<$Res> {
  factory _$$_SceneResultCopyWith(
          _$_SceneResult value, $Res Function(_$_SceneResult) then) =
      __$$_SceneResultCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? sceneKey, List<Media>? medias});
}

/// @nodoc
class __$$_SceneResultCopyWithImpl<$Res>
    extends _$SceneResultCopyWithImpl<$Res, _$_SceneResult>
    implements _$$_SceneResultCopyWith<$Res> {
  __$$_SceneResultCopyWithImpl(
      _$_SceneResult _value, $Res Function(_$_SceneResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sceneKey = freezed,
    Object? medias = freezed,
  }) {
    return _then(_$_SceneResult(
      sceneKey: freezed == sceneKey
          ? _value.sceneKey
          : sceneKey // ignore: cast_nullable_to_non_nullable
              as String?,
      medias: freezed == medias
          ? _value._medias
          : medias // ignore: cast_nullable_to_non_nullable
              as List<Media>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SceneResult implements _SceneResult {
  _$_SceneResult({this.sceneKey, final List<Media>? medias}) : _medias = medias;

  factory _$_SceneResult.fromJson(Map<String, dynamic> json) =>
      _$$_SceneResultFromJson(json);

  /// 场景 Key
  @override
  final String? sceneKey;

  /// 媒体信息列表
  final List<Media>? _medias;

  /// 媒体信息列表
  @override
  List<Media>? get medias {
    final value = _medias;
    if (value == null) return null;
    if (_medias is EqualUnmodifiableListView) return _medias;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SceneResult(sceneKey: $sceneKey, medias: $medias)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SceneResult &&
            (identical(other.sceneKey, sceneKey) ||
                other.sceneKey == sceneKey) &&
            const DeepCollectionEquality().equals(other._medias, _medias));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, sceneKey, const DeepCollectionEquality().hash(_medias));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SceneResultCopyWith<_$_SceneResult> get copyWith =>
      __$$_SceneResultCopyWithImpl<_$_SceneResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SceneResultToJson(
      this,
    );
  }
}

abstract class _SceneResult implements SceneResult {
  factory _SceneResult({final String? sceneKey, final List<Media>? medias}) =
      _$_SceneResult;

  factory _SceneResult.fromJson(Map<String, dynamic> json) =
      _$_SceneResult.fromJson;

  @override

  /// 场景 Key
  String? get sceneKey;
  @override

  /// 媒体信息列表
  List<Media>? get medias;
  @override
  @JsonKey(ignore: true)
  _$$_SceneResultCopyWith<_$_SceneResult> get copyWith =>
      throw _privateConstructorUsedError;
}

Media _$MediaFromJson(Map<String, dynamic> json) {
  return _Media.fromJson(json);
}

/// @nodoc
mixin _$Media {
  /// 音频 URL
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MediaCopyWith<Media> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaCopyWith<$Res> {
  factory $MediaCopyWith(Media value, $Res Function(Media) then) =
      _$MediaCopyWithImpl<$Res, Media>;
  @useResult
  $Res call({String? url});
}

/// @nodoc
class _$MediaCopyWithImpl<$Res, $Val extends Media>
    implements $MediaCopyWith<$Res> {
  _$MediaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MediaCopyWith<$Res> implements $MediaCopyWith<$Res> {
  factory _$$_MediaCopyWith(_$_Media value, $Res Function(_$_Media) then) =
      __$$_MediaCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? url});
}

/// @nodoc
class __$$_MediaCopyWithImpl<$Res> extends _$MediaCopyWithImpl<$Res, _$_Media>
    implements _$$_MediaCopyWith<$Res> {
  __$$_MediaCopyWithImpl(_$_Media _value, $Res Function(_$_Media) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_$_Media(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Media implements _Media {
  _$_Media({this.url});

  factory _$_Media.fromJson(Map<String, dynamic> json) =>
      _$$_MediaFromJson(json);

  /// 音频 URL
  @override
  final String? url;

  @override
  String toString() {
    return 'Media(url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Media &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MediaCopyWith<_$_Media> get copyWith =>
      __$$_MediaCopyWithImpl<_$_Media>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MediaToJson(
      this,
    );
  }
}

abstract class _Media implements Media {
  factory _Media({final String? url}) = _$_Media;

  factory _Media.fromJson(Map<String, dynamic> json) = _$_Media.fromJson;

  @override

  /// 音频 URL
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$_MediaCopyWith<_$_Media> get copyWith =>
      throw _privateConstructorUsedError;
}
