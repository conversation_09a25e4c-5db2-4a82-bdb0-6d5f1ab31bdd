import 'package:flutter/foundation.dart';
import 'package:jojo_flutter_base/base.dart';

part 'baby_info_type.freezed.dart';
part 'baby_info_type.g.dart';

@freezed
class GradeInfo with _$GradeInfo {
  const factory GradeInfo({
    int? code,
    String? name,
    List<GradeInfo>? subDict,
  }) = _GradeInfo;

  factory GradeInfo.fromJson(Map<String, dynamic> json) =>
      _$GradeInfoFromJson(json);
}
