import 'package:freezed_annotation/freezed_annotation.dart';

part 'aigc_medias.freezed.dart';
part 'aigc_medias.g.dart';

/// AigcMedias 模型
@freezed
class AigcMedias with _$AigcMedias {
  factory AigcMedias({
    /// 场景结果集
    List<SceneResult>? sceneResults,
  }) = _AigcMedias;

  factory AigcMedias.fromJson(Map<String, dynamic> json) =>
      _$AigcMediasFromJson(json);
}

/// 场景结果模型
@freezed
class SceneResult with _$SceneResult {
  factory SceneResult({
    /// 场景 Key
    String? sceneKey,

    /// 媒体信息列表
    List<Media>? medias,
  }) = _SceneResult;

  factory SceneResult.fromJson(Map<String, dynamic> json) =>
      _$SceneResultFromJson(json);
}

/// 媒体信息模型
@freezed
class Media with _$Media {
  factory Media({
    /// 音频 URL
    String? url,
  }) = _Media;

  factory Media.fromJson(Map<String, dynamic> json) =>
      _$MediaFromJson(json);
}