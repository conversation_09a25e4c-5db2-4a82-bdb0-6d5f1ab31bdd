import 'package:jojo_flutter_base/base.dart';

part 'contact_content_type.freezed.dart';
part 'contact_content_type.g.dart';

@freezed
class ContactCustomerContentList with _$ContactCustomerContentList {
  const factory ContactCustomerContentList({
    String? color,
    String? text,
    String? linkUrl,
  }) = _ContactCustomerContentList;

  factory ContactCustomerContentList.fromJson(Map<String, dynamic> json) =>
      _$ContactCustomerContentListFromJson(json);
}
