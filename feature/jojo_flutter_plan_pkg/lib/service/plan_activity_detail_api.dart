import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/common/dio/use.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';

part 'plan_activity_detail_api.g.dart';

@RestApi()
abstract class ActivityDetailApi {
  factory ActivityDetailApi(Dio dio, {String baseUrl}) = _ActivityDetailApi;

  ///获取完课活动详情数据
  @GET("/api/college/incentive-activities/{activityId}")
  Future<PlanActivityData> getCourseSegmentsInfo({
    @Path('activityId') required int activityId,
    @Query('classId') required int classId,
    @Query('courseId') required int courseId,
    @Query('pageId') required int pageId,
  });
}

final ActivityDetailApis =
ActivityDetailApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
