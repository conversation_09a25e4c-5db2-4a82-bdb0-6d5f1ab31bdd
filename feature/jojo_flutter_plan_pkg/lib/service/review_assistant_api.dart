import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/common/dio/use.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:retrofit/dio.dart';

part 'review_assistant_api.g.dart';

@RestApi()
abstract class ReviewAssistantApi {
  factory ReviewAssistantApi(Dio dio, {String baseUrl}) = _ReviewAssistantApi;

  ///获取指课程下主题信息
  @GET("/api/pagani/courses/{courseKey}/segments")
  Future<CourseSegmentsData> getCourseSegmentsInfo({
    @Path('courseKey') required String courseKey,
    @Query('classKey') required String classKey,
    @Query('review') required int review,
  });

  /// 获取课程下的课时信息
  @GET("/api/pagani/courses/{courseKey}/lessons")
  Future<CourseLessonsData>  getCourseLessonsInfo({
    @Path('courseKey') required String courseKey,
    @Query('classKey') required String classKey,
    @Query('classId') required String classId,
    @Query('segmentId') required String segmentId,
    @Query('weekId') required String weekId,
  });

  /// 获取课程下的课时信息
  @GET("/api/pagani/courses/{courseKey}/lessons/search")
  Future<CourseLessonsSearchData>  searchCourseLessonsInfo({
    @Path('courseKey') required String courseKey,
    @Query('searchWord') required String searchWord,
    @Query('classId') required int classId,
    @Query('sceneId') required int sceneId,
    @Query('pageNum') required int pageNum,
    @Query('pageSize') required int pageSize,
    @DioOptions() RequestOptions? cancelToken
  });
}

final ReviewAssistantApis =
    ReviewAssistantApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
