import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/common/dio/use.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/model/milestone_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';

part 'learning_incentives_api.g.dart';

@RestApi()
abstract class LearningIncentivesApi {
  factory LearningIncentivesApi(Dio dio, {String baseUrl}) =
      _LearningIncentivesApi;

  @GET("/api/pagani/milestone-rewards?subjectType={type}")
  Future<MilestoneResponse> getMilestoneList(@Path("type") int subjectType);

  @GET("/api/pagani/classes/{classKey}/study-partners")
  Future<StudyBuddy> getStudyBuddyList(
    @Path("classKey") String classKey,
    @Query("size") int size,
    @Query("offset") int offset,
  );

  @POST('/api/pagani/user-like-records')
  Future<dynamic> postLikeRecord(
    @Field('targetId') int targetId,
    @Field('action') String targetType,
  );
}

LearningIncentivesApi learningIncentivesApi =
    LearningIncentivesApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
