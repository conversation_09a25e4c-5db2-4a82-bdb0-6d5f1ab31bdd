import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';

import '../common/dio/use.dart';
import '../pages/plan_lesson_change/change_list/model/lesson_change_list_data.dart';
import '../pages/plan_lesson_change/model/lesson_change_data.dart';
import '../pages/plan_lesson_change/model/lesson_change_result.dart';
import '../pages/plan_lesson_change/model/lesson_change_result_query.dart';

part 'home_lesson_change_api.g.dart';

@RestApi()
abstract class HomeLessonChangeApi {
  factory HomeLessonChangeApi(Dio dio, {String baseUrl}) = _HomeLessonChangeApi;

  ///获取交换课程的sku
  @GET("/api/college/swappable-courses")
  Future<LessonChangeData?> getUserSkus(
      @Query("courseId") int? courseId, @Query('classId') int? classId);

  ///交换课列表请求
  @GET("/api/pagani/courses/{courseKey}/lessons")
  Future<LessonChangeListData?> getLessonList(@Path("courseKey") int? courseKey,
      @Query('onlyQueryCourse') bool? onlyQueryCourse);

  ///交换课交换处理
  @PUT("/api/college/orders/{orderNo}")
  Future<LessonChangeResult?> lessonChange(
      @Path("orderNo") String? orderNo, @Body() Map<String, dynamic> data);

  ///查询更换课结果
  @GET("/api/college/user-courses")
  Future<LessonChangeResultQuery?> lessonChangeQuery(
      @Query("orderNo") String? orderNo,
      @Query("courseId") int? courseId);
}

final homeLessonChangeApiService =
    HomeLessonChangeApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
final homeLessonChangeApiServiceMock = HomeLessonChangeApi(pageDio,
    baseUrl:
        'https://mock.xjjj.co/mock/61b0921b755d4b0548f1e44c/example'); //mock
