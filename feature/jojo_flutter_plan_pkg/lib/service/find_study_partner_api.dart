import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';

import '../common/dio/use.dart';
import '../pages/find_study_partner/model/find_study_partner_model.dart';
import '../pages/plan_home_map/model/course_map_home_page_tab_data.dart';

part 'find_study_partner_api.g.dart';

@RestApi()
abstract class FindStudyPartnerApi {
  factory FindStudyPartnerApi(Dio dio, {String baseUrl}) = _FindStudyPartnerApi;

  /// 获取用户科目和课程
  @GET("/api/college/class-courses")
  Future<CourseSubjectTabData?> getUserSubjectClass();

  /// 获取发现学伴
  @GET("/api/pagani/subjects/{subjectType}/study-partners")
  Future<FindStudyPartnersModel?> getStudyPartners(
      @Path('subjectType') int subjectType,
      @Query('offset') int offset,
      @Query('size') int size);

  /// 添加学伴
  @POST("/api/pagani/learning-friends")
  Future<dynamic> addPartner(
    @Field("targetId") int targetId,
  );
}

final proFindStudyPartnerApi =
    FindStudyPartnerApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);

class FindStudyPartnerMockApi implements FindStudyPartnerApi {
  @override
  Future<FindStudyPartnersModel?> getStudyPartners(
      int subjectType, int offset, int size) async {
    if (subjectType == 4) {
      return Future.value(FindStudyPartnersModel.fromJson(json.decode(
          '{"partners":[{"nickName":"英语","continuousDays":11,"studyDays":1,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"英语1","continuousDays":22,"studyDays":2,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":1},{"nickName":"英语2","continuousDays":33,"studyDays":3,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":2}],"offset":0,"size":10,"emptyImg":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","buyCourseJumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/teacherAchievementTypes?subjectType=2","emptyText":"当前没有进行中课程，找不到没有学伴"}')));
    } else if (subjectType == 2) {
      return Future.value(FindStudyPartnersModel.fromJson(json.decode(
          '{"partners":[{"nickName":"益智","continuousDays":11,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":1},{"nickName":"萱*","continuousDays":999,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":2}],"offset":0,"size":10,"emptyImg":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","buyCourseJumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/teacherAchievementTypes?subjectType=2","emptyText":"当前没有进行中课程，找不到没有学伴"}')));
    } else if (subjectType == 3) {
      return Future.value(FindStudyPartnersModel.fromJson(json.decode(
          '{"partners":[],"offset":0,"size":10,"emptyImg":"https://imgs.699pic.com/images/600/588/691.jpg!detail.v1","buyCourseJumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/teacherAchievementTypes?subjectType=2"}')));
    } else {
      return Future.value(FindStudyPartnersModel.fromJson(json.decode(
          '{"partners":[{"nickName":"萱*","continuousDays":88,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":1},{"nickName":"萱*","continuousDays":999,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":2},{"nickName":"萱*","continuousDays":3000,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":99999,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0},{"nickName":"萱*","continuousDays":119,"studyDays":1414,"img":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","url":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000","partnerId":3287000,"partnerState":0}],"offset":0,"size":10,"emptyImg":"https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865","buyCourseJumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/teacherAchievementTypes?subjectType=2","emptyText":"当前没有进行中课程，找不到没有学伴"}')));
    }
  }

  @override
  Future<CourseSubjectTabData?> getUserSubjectClass() {
    return Future.value(CourseSubjectTabData.fromJson(json.decode('''
        {
            "subjectClassList": [
                {
                    "subjectType": 1
                },
                {
                    "subjectType": 2
                },
                {
                    "subjectType": 3
                },
                {
                    "subjectType": 4
                }
            ],
            "subjectList": [
                {
                    "subjectType": 2,
                    "subjectName": "阅读",
                    "subjectColor": "#FF9045",
                    "loadingScene": 2
                },
                {
                    "subjectType": 1,
                    "subjectName": "思维",
                    "subjectColor": "#33BBFF",
                    "loadingScene": 5
                },
                {
                    "subjectType": 3,
                    "subjectName": "创作",
                    "subjectColor": "#55CC3D",
                    "loadingScene": 6
                },
                {
                    "subjectType": 4,
                    "subjectName": "美育",
                    "subjectColor": "#AE84E3",
                    "loadingScene": 3
                },
                {
                    "subjectType": 6,
                    "subjectName": "英语",
                    "subjectColor": "#FF7A99",
                    "loadingScene": 4
                },
                {
                    "subjectType": 7,
                    "subjectName": "专题",
                    "subjectColor": "#FCDA00",
                    "loadingScene": 9
                },
                {
                    "subjectType": 13,
                    "subjectName": "综合",
                    "subjectColor": "#FCDA00",
                    "loadingScene": 1
                }
            ]
        }
        ''')));
  }

  @override
  Future<dynamic> addPartner(int id) {
    return Future.value(true);
  }
}

class FindStudyPartnerErrorMockApi implements FindStudyPartnerApi {
  @override
  Future addPartner(int targetId) {
    return Future.value(null);
  }

  @override
  Future<FindStudyPartnersModel?> getStudyPartners(
      int subjectType, int offset, int size) {
    return Future.value(null);
  }

  @override
  Future<CourseSubjectTabData?> getUserSubjectClass() {
    throw Exception("getUserSubjectClass error");
  }
}
