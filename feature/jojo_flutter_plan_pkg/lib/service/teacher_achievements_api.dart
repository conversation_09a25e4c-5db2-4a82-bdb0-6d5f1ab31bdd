import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';

import '../common/dio/use.dart';
import '../pages/achievements/teacher_mode/model/teacher_achievement_model.dart';

part 'teacher_achievements_api.g.dart';

@RestApi()
abstract class TeacherAchievementsApi {

  factory TeacherAchievementsApi(Dio dio, {String baseUrl}) = _TeacherAchievementsApi;

  ///获取 学科 - 阶段
  @GET("/api/pagani/segments")
  Future<TeacherAchievementModel> getSegments(@Query('subjectType') int subjectType);

  ///获取 学科 - 阶段 - 勋章
  @GET("/api/pagani/subjects/{subjectType}/segments/{segmentCode}/medals")
  Future<TeacherAchievementMedals> getMedals(@Path('subjectType') int subjectType, @Path('segmentCode') int segmentCode);
}

final teacherAchievementsApi = TeacherAchievementsApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);