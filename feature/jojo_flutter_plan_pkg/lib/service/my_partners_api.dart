import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';

import '../common/dio/use.dart';
import '../pages/my_partners/model/models.dart';

part 'my_partners_api.g.dart';

@RestApi()
abstract class MyPartnersApi {
  factory MyPartnersApi(Dio dio, {String baseUrl}) = _MyPartnersApi;

  /// 获取发现学伴列表
  @GET("/api/pagani/learning-group-users")
  Future<DiscoverPartnersData?> getDiscoverPartners();

  /// 获取动态列表
  @GET("/api/pagani/user-dynamics")
  Future<DynamicsListData?> getUserDynamics(
    @Query('minId') int minId,
    @Query('size') int size,
  );

  /// 获取学伴列表
  @GET("/api/pagani/learning-friends")
  Future<PartnersListData?> getLearningPartners(
    @Query('minId') int minId,
    @Query('size') int size,
  );

  /// 送花花或者戳一戳互动接口
  @POST("/api/pagani/dynamics-actions")
  Future<dynamic> sendDynamicsAction(@Body() DynamicsActionRequest request);
}

/// 动态互动请求模型
class DynamicsActionRequest {
  final String action; // flower, poke
  final int dynamicRelationId; // 动态消息ID
  final int partnerId; // 学伴ID

  DynamicsActionRequest({
    required this.action,
    required this.dynamicRelationId,
    required this.partnerId,
  });

  Map<String, dynamic> toJson() => {
        'action': action,
        'dynamicRelationId': dynamicRelationId,
        'partnerId': partnerId,
      };
}

final myPartnersApi =
    MyPartnersApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
final myPartnersMockApi = MyPartnersApiMock();

class MyPartnersApiMock implements MyPartnersApi {
  // 模拟分页状态
  static int _currentPage = 1;
  static const int _totalPages = 5; // 总共5页数据
  static const int _pageSize = 3; // 每页3条数据
  @override
  Future<DiscoverPartnersData?> getDiscoverPartners() async {
    String jsonString = """
    {
      "partners": [
        {
          "nickName" : "地方地方弟弟地方九",
          "continuousDays" : 119,
          "studyDays" : 1414,
          "img" : "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
          "url" : "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
          "partnerId" : 3287000
        },
        {
          "nickName" : "地方地方弟弟地方",
          "continuousDays" : 119,
          "studyDays" : 1414,
          "img" : "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
          "url" : "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
          "partnerId" : 3287000
        },
        {
          "nickName" : "萱*",
          "continuousDays" : 119,
          "studyDays" : 1414,
          "img" : "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
          "url" : "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
          "partnerId" : 3287000
        }
      ],
      "viewMore": "查看更多",
      "jumpRoute":"",
      "title":"发现学伴"
    }
    """;
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return DiscoverPartnersData.fromJson(jsonMap);
  }

  @override
  Future<DynamicsListData?> getUserDynamics(int page, int size) async {
    // 模拟网络延迟
    // await Future.delayed(const Duration(milliseconds: 500));

    // 重置分页状态（如果是第一页）
    if (page == 1) {
      _currentPage = 1;
    } else {
      _currentPage = page;
    }

    // 生成当前页的动态数据
    List<Map<String, dynamic>> dynamics = [];

    // 根据页码生成不同的数据
    for (int i = 0; i < size && i < _pageSize; i++) {
      int itemIndex = (_currentPage - 1) * _pageSize + i + 1;

      // 如果超过总数据量，停止生成
      if (itemIndex > _totalPages * _pageSize) break;

      dynamics.add({
        "subjectColor": _getSubjectColor(itemIndex),
        "type": itemIndex % 3, // 0, 1, 2 循环
        "nickName": "用户$itemIndex",
        "img":
            "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
        "url":
            "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
        "content": "第$itemIndex条动态内容：完成了阅读连胜${itemIndex * 5}天",
        "actionState": {
          "poke": itemIndex % 2, // 交替显示已戳/未戳
          "flower": (itemIndex + 1) % 2 // 交替显示已送花/未送花
        },
        "timeDesc": _getTimeDesc(itemIndex),
        "messageTime": 1750818602 + itemIndex * 3600, // 每条相差1小时
        "dynamicRelationId": 12312320 + itemIndex,
        "partnerId": 123120 + itemIndex
      });
    }

    Map<String, dynamic> response = {
      "dynamics": dynamics,
      "minId": page + 1, // 下一页的页码
      "size": size,
      "actionType": {"good": [], "bad": []}
    };

    return DynamicsListData.fromJson(response);
  }

  // 生成时间描述
  String _getTimeDesc(int index) {
    switch (index % 4) {
      case 0:
        return "刚刚";
      case 1:
        return "$index分钟前";
      case 2:
        return "$index小时前";
      case 3:
        return "$index天前";
      default:
        return "刚刚";
    }
  }

  // 生成学科颜色
  String _getSubjectColor(int index) {
    List<String> colors = ["#FF9045", "#33BBFF", "#AE84E3", "#FF7A99"];
    return colors[index % colors.length];
  }

  @override
  Future<PartnersListData?> getLearningPartners(int page, int size) async {
  //   {
  //     "nickName": "萱一",
  //   "img": "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
  //   "url": "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
  //   "partnerId": 1
  // },
  //   {
  //   "nickName": "雨桐",
  //   "img": "https://jojopublic.jojoread.com/vulcan/nuwa-admin/appicon/813044985201081345/1750485028140ndmhim.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
  //   "url": "tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=2&classKey=21526_25&partnerId=3287000",
  //   "partnerId": 2
  //   }
    String jsonString = """
    {
      "partners": [
      ],
      "page": 1,
      "size": 20
    }
    """;
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return PartnersListData.fromJson(jsonMap);
  }

  @override
  Future<dynamic> sendDynamicsAction(DynamicsActionRequest request) async {
    String jsonString = """
    {
      "code":"SUCCESS",
      "message": "ok",
      "data": null
    }
    """;
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return jsonMap;
  }
}
