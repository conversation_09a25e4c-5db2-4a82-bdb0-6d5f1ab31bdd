import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/common/dio/use.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/ai_frequency.dart';
import 'package:retrofit/retrofit.dart';

part 'ai_jiao_jiao_service_api.g.dart';

@RestApi()
abstract class AIJiaoJiaoServiceApi {
  factory AIJiaoJiaoServiceApi(Dio dio, {String baseUrl}) = _AIJiaoJiaoServiceApi;
  /// 获取会话次数
  @GET("/api/dourm/ai-conversation-configs")
  Future<AiFrequency> getFrequency(
    @Query('scene') int scene,
  );

  @POST("/api/dourm/ai-conversation-records")
  Future<void> deductFrequency(@Body() Map<String, dynamic> map);
}

AIJiaoJiaoServiceApi aiJiaoJiaoServiceApi =
AIJiaoJiaoServiceApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);