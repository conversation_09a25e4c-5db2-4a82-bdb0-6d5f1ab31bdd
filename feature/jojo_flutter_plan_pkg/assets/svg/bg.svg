<svg width="224" height="117" viewBox="0 0 224 117" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_170_5189" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="10" width="200" height="100">
<rect x="12" y="10" width="200" height="100" fill="white"/>
</mask>
<g mask="url(#mask0_170_5189)">
<g style="mix-blend-mode:multiply" opacity="0.2" filter="url(#filter0_f_170_5189)">
<path d="M181 33C245.617 33 298 14.8675 298 -7.49999C298 -29.8675 245.617 -48 181 -48C116.383 -48 64 -29.8675 64 -7.49999C64 14.8675 116.383 33 181 33Z" fill="#FCDA00"/>
</g>
</g>
<defs>
<filter id="filter0_f_170_5189" x="-20" y="-132" width="402" height="249" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="42" result="effect1_foregroundBlur_170_5189"/>
</filter>
</defs>
</svg>
