// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/page_controller.dart';


void main() {
  late VideoIntroduceCtrl controller;
  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
  });

  group('video_page_controller tests:', () {
    test('getDetailInfoData test', () async {

      controller = VideoIntroduceCtrl(url: "xxxx", isFromDetailPage: 1, dataString: "xxxx");
      expect(controller.isFromDetailPage is int , false);
    });
  });
}
