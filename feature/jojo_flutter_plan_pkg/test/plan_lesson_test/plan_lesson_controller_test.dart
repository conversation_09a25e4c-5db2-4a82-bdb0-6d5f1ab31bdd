// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_lesson_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_lesson_page_api.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';


class MockHomeDownManager extends Mock implements AbsDownloadManager {
  // Future<void> downloadUrl(List<String> urlList,
  //     {Function(double p1)? progressListener,
  //       Function(Map<String, String> p1)? successListener,
  //       Function(String p1)? failListener,
  //       bool isNeedCancel = true}) {
  //   return successListener?.call(
  //       {"url": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610", "path": "11.png"}
  //   );
  // }
}

/// 简单的可测试 Controller
class SimpleTestController extends PlanHomeLessonCtrl {
  bool shouldDownloadSucceed;
  Map<String, String>? mockDownloadResult;

  SimpleTestController({
    HomeMapLessonPageApi? api,
    this.shouldDownloadSucceed = true,
    this.mockDownloadResult,
  }) : super(api: api);

  @override
  Future<void> downloadZipRes(
      List<String> urlList, {
        Function(Map<String, String>)? successListener,
      }) async {
    if (shouldDownloadSucceed) {
      // 模拟下载成功
      Map<String, String> result = mockDownloadResult ?? _generateMockResult(urlList);
      successListener?.call(result);
    } else {
      // 模拟下载失败
      throw Exception("下载失败");
    }
  }

  /// 生成模拟的下载结果
  Map<String, String> _generateMockResult(List<String> urlList) {
    Map<String, String> result = {};
    for (String url in urlList) {
      result[url] = "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610";
    }
    return result;
  }
}

// 创建 Dio 的 Mock 类
class MockHomePageApiService extends Mock implements HomeMapLessonPageApi {
  final CourseLessonInfo _courseHomePageData;

  MockHomePageApiService(this._courseHomePageData);

  static CourseLessonInfo? mockHomePageData() {
    Map<String, dynamic> jsonDate =  {
      "classId": 56771,
      "courseId": 4116,
      "courseKey": "25012",
      "startClassTime": 1746028800000,
      "subjectColor": "#FF9045",
      "subjectType": 2,
      "subjectName": "阅读",
      "courseLabel": "全年系统包-6阶",
      "classStatus": 1,
      "courseSegment": "L6",
      "courseType": 3,
      "timetableNodeList": [{
        "showDateTime": 1746028800000,
        "showDate": "2025-05-01",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第1次 L5支持阿拉伯数字",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/249589854795208704/665d0120a84fee1ea5ce4efaac219d0b1616146891366.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/249589854795208704/665d0120a84fee1ea5ce4efaac219d0b1616146891366.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/249589854795208704/665d0120a84fee1ea5ce4efaac219d0b1616146891366.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9936&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 1,
        "lessonId": 3548,
        "lessonKey": "99999_9936",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746028800000,
        "showDate": "2025-05-01",
        "nodeStatus": 1,
        "nodeType": 10,
        "title": "能力测评",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545557357eyjle.png?checksumV2=crc64%3D16221437230010368799",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555924kxhknu.png?checksumV2=crc64%3D16221437230010368799",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545560061osruh.json?checksumV2=crc64%3D15765301495948931570",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555859t32y9u.png?checksumV2=crc64%3D16221437230010368799",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555802qrfwhw.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "",
        "finishVoice": "",
        "route": "https://jojoread.fat.tinman.cn/user/bnu-questionnaire?deviceOrientation=auto&fullScreen=true&windowType=normal&source=3&subjectType=2&scene=2&courseSegmentCode=5&phase=5",
        "toast": null,
        "nodeId": 809,
        "sortOrder": 15,
        "rewardVoList": null,
        "lessonOrder": 1,
        "lessonId": 3548,
        "lessonKey": "99999_9936",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去测评",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1746115200000,
        "showDate": "2025-05-02",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第2次 环节结束页1",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/1627022477424df49dcaa8ab0d6c37c50ce179103c2fb.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/1627022477424df49dcaa8ab0d6c37c50ce179103c2fb.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/1627022477424df49dcaa8ab0d6c37c50ce179103c2fb.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9940&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 2,
        "lessonId": 3614,
        "lessonKey": "99999_9940",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746115200000,
        "showDate": "2025-05-02",
        "nodeStatus": 1,
        "nodeType": 10,
        "title": "能力测评",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545557357eyjle.png?checksumV2=crc64%3D16221437230010368799",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555924kxhknu.png?checksumV2=crc64%3D16221437230010368799",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545560061osruh.json?checksumV2=crc64%3D15765301495948931570",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555859t32y9u.png?checksumV2=crc64%3D16221437230010368799",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354555802qrfwhw.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "",
        "finishVoice": "",
        "route": "https://jojoread.fat.tinman.cn/user/bnu-questionnaire?deviceOrientation=auto&fullScreen=true&windowType=normal&source=3&subjectType=2&scene=2&courseSegmentCode=5&phase=12",
        "toast": null,
        "nodeId": 809,
        "sortOrder": 15,
        "rewardVoList": null,
        "lessonOrder": 2,
        "lessonId": 3614,
        "lessonKey": "99999_9940",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去测评",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1746201600000,
        "showDate": "2025-05-03",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第3次 L6环节列表优化L6环节列表优化L6环节列表优化",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/16270220108553c260d2962a9f87b5eaa6358af2e9ee9.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/16270220108553c260d2962a9f87b5eaa6358af2e9ee9.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/295203551839619072/16270220108553c260d2962a9f87b5eaa6358af2e9ee9.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9956&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 3,
        "lessonId": 3644,
        "lessonKey": "99999_9956",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746288000000,
        "showDate": "2025-05-04",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第4次 环节结束页2",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617717762944.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617717762944.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617717762944.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9957&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 4,
        "lessonId": 3645,
        "lessonKey": "99999_9957",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746374400000,
        "showDate": "2025-05-05",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第5次 type999灵感收集--修改",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/489432864972622848/1673329887994bd11537f1bc31e334497ec5463fc575e.jpeg",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/489432864972622848/1673329887994bd11537f1bc31e334497ec5463fc575e.jpeg",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/489432864972622848/1673329887994bd11537f1bc31e334497ec5463fc575e.jpeg",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9982&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 5,
        "lessonId": 3766,
        "lessonKey": "99999_9982",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746374400000,
        "showDate": "2025-05-05",
        "nodeStatus": 4,
        "nodeType": 2,
        "title": "周奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545528922kuaw1.png?checksumV2=crc64%3D532912849087539630",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553181lsuib7.png?checksumV2=crc64%3D532912849087539630",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553327qjha70.json?checksumV2=crc64%3D2330939653377543349",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545530379jiscj.png?checksumV2=crc64%3D17189332019849071541",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552960payuta.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553257udrd2z.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545531012qewjl.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 11,
        "rewardVoList": [{
          "rewardCount": 10,
          "rewardType": 1,
          "actionType": 3,
          "actionTargetId": "56771:4972:1"
        }
        ],
        "lessonOrder": 5,
        "lessonId": 3766,
        "lessonKey": "99999_9982",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第一周",
        "weekId": 277,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1746460800000,
        "showDate": "2025-05-06",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第6次 环节结束页",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/258322082022833152/176189cfc8f87999ad080157644292f01618231947308.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/258322082022833152/176189cfc8f87999ad080157644292f01618231947308.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/258322082022833152/176189cfc8f87999ad080157644292f01618231947308.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9963&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 6,
        "lessonId": 3656,
        "lessonKey": "99999_9963",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第二周",
        "weekId": 157,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746547200000,
        "showDate": "2025-05-07",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第7次 第二单元环节为横屏",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273467020817310720/cc779fe8dcc58689818fe3442de098741621840590404.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273467020817310720/cc779fe8dcc58689818fe3442de098741621840590404.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273467020817310720/cc779fe8dcc58689818fe3442de098741621840590404.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9984&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 7,
        "lessonId": 3776,
        "lessonKey": "99999_9984",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": "第二周",
        "weekId": 157,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746633600000,
        "showDate": "2025-05-08",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第8次 名师视频",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617718705356.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617718705356.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/256178074068526080/176189cfc8f87999ad080157644292f01617718705356.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_9958&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 8,
        "lessonId": 3646,
        "lessonKey": "99999_9958",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746720000000,
        "showDate": "2025-05-09",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第9次 S5第21月第1个课时",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/378180249094333440/164680519820097be877346fa6b06403102ba2e17f99f.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/378180249094333440/164680519820097be877346fa6b06403102ba2e17f99f.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/378180249094333440/164680519820097be877346fa6b06403102ba2e17f99f.jpg?x-oss-process=image/quality,q_60/format,jpg",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=31338_0001&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 9,
        "lessonId": 12437,
        "lessonKey": "31338_0001",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746720000000,
        "showDate": "2025-05-09",
        "nodeStatus": 4,
        "nodeType": 2,
        "title": "周奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545528922kuaw1.png?checksumV2=crc64%3D532912849087539630",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553181lsuib7.png?checksumV2=crc64%3D532912849087539630",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553327qjha70.json?checksumV2=crc64%3D2330939653377543349",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545530379jiscj.png?checksumV2=crc64%3D17189332019849071541",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552960payuta.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553257udrd2z.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545531012qewjl.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 11,
        "rewardVoList": [{
          "rewardCount": 10,
          "rewardType": 1,
          "actionType": 3,
          "actionTargetId": "56771:4972:2"
        }
        ],
        "lessonOrder": 9,
        "lessonId": 12437,
        "lessonKey": "31338_0001",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1746720000000,
        "showDate": "2025-05-09",
        "nodeStatus": 4,
        "nodeType": 3,
        "title": "阶段奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/174435455339191yqrd.png?checksumV2=crc64%3D12679875747940268622",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553642l0q3xo.png?checksumV2=crc64%3D12679875747940268622",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553770fzs9m4.json?checksumV2=crc64%3D12130129839242141905",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545535232v0n6u.png?checksumV2=crc64%3D2463452523341464710",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545534575w194g.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545537040qbuk4.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553582l2azg8.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 12,
        "rewardVoList": [{
          "rewardCount": 20,
          "rewardType": 1,
          "actionType": 4,
          "actionTargetId": "4972"
        }
        ], 
        "lessonOrder": 9,
        "lessonId": 12437,
        "lessonKey": "31338_0001",
        "segmentName": "成语故事",
        "segmentId": 4972,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1746806400000,
        "showDate": "2025-05-10",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第10次 L6新阅读游戏",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/324987116043177985/16341229658039ccd35a7d1ce0bd8843730299005f919.png.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/324987116043177985/16341229658039ccd35a7d1ce0bd8843730299005f919.png.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/324987116043177985/16341229658039ccd35a7d1ce0bd8843730299005f919.png.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_16221&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 10,
        "lessonId": 5418,
        "lessonKey": "99999_16221",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746892800000,
        "showDate": "2025-05-11",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第11次 L6情景答题",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_16204&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 11,
        "lessonId": 5248,
        "lessonKey": "99999_16204",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 1,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1746979200000,
        "showDate": "2025-05-12",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第12次 叫叫魔法屋",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259169092624384.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259169092624384.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259169092624384.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=10202_04_01&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 12,
        "lessonId": 781,
        "lessonKey": "10202_04_01",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1747065600000,
        "showDate": "2025-05-13",
        "nodeStatus": 3,
        "nodeType": 1,
        "collectStatus" : 1,
        "title": "第13次 L6情景答题",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/319122456605684736/1632724802036ac81e2cf41776eae876d64ad32ac0832.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_16205&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 13,
        "lessonId": 5249,
        "lessonKey": "99999_16205",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1747152000000,
        "showDate": "2025-05-14",
        "nodeStatus": 3,
        "nodeType": 1,
        "title": "第14次 小小主持人",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259523070910464.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259523070910464.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259523070910464.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=10202_05_01&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 14,
        "lessonId": 782,
        "lessonKey": "10202_05_01",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1747152000000,
        "showDate": "2025-05-14",
        "nodeStatus": 4,
        "nodeType": 2,
        "title": "周奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545528922kuaw1.png?checksumV2=crc64%3D532912849087539630",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553181lsuib7.png?checksumV2=crc64%3D532912849087539630",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553327qjha70.json?checksumV2=crc64%3D2330939653377543349",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545530379jiscj.png?checksumV2=crc64%3D17189332019849071541",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552960payuta.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553257udrd2z.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545531012qewjl.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 11,
        "rewardVoList": [{
          "rewardCount": 10,
          "rewardType": 1,
          "actionType": 3,
          "actionTargetId": "56771:4973:1"
        }
        ],
        "lessonOrder": 14,
        "lessonId": 782,
        "lessonKey": "10202_05_01",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": "二月第一周",
        "weekId": 433,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1747238400000,
        "showDate": "2025-05-15",
        "nodeStatus": 3,
        "collectStatus" : 1,
        "nodeType": 1,
        "title": "第15次 情景答题",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/405315346805219329/1653274714897fc5248f217805447f772399921d32574.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/405315346805219329/1653274714897fc5248f217805447f772399921d32574.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/405315346805219329/1653274714897fc5248f217805447f772399921d32574.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_17074&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 15,
        "lessonId": 16981,
        "lessonKey": "99999_17074",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": null,
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": null
      }, {
        "showDateTime": 1747324800000,
        "showDate": "2025-05-16",
        "nodeStatus": 1,
        "nodeType": 1,
        "title": "第16次 情景答题2",
        "lockIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/458632777510156288/16659865754147f5769bf91361d7c9cb128fd1f1f5271.png",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/458632777510156288/16659865754147f5769bf91361d7c9cb128fd1f1f5271.png",
        "unfinishLottieIcon": null,
        "finishIcon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/458632777510156288/16659865754147f5769bf91361d7c9cb128fd1f1f5271.png",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552757yd28e1.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": null,
        "finishVoice": null,
        "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=25012_20&lessonKey=99999_17075&loadingScene=2&engineType=0",
        "toast": null,
        "nodeId": null,
        "sortOrder": 10,
        "rewardVoList": null,
        "lessonOrder": 16,
        "lessonId": 16982,
        "lessonKey": "99999_17075",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": null,
        "collectStatus" : 1,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": true,
        "position": true,
        "gif": true,
        "studyDesc": "去学习",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null,
        "studyStatus": 1,
        "studyTips": null,
        "studyTipsVoice": null,
        "today": true
      }, {
        "showDateTime": 1747324800000,
        "showDate": "2025-05-16",
        "nodeStatus": 4,
        "nodeType": 2,
        "title": "周奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545528922kuaw1.png?checksumV2=crc64%3D532912849087539630",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553181lsuib7.png?checksumV2=crc64%3D532912849087539630",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553327qjha70.json?checksumV2=crc64%3D2330939653377543349",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545530379jiscj.png?checksumV2=crc64%3D17189332019849071541",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354552960payuta.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553257udrd2z.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545531012qewjl.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 11,
        "rewardVoList": [{
          "rewardCount": 10,
          "rewardType": 1,
          "actionType": 3,
          "actionTargetId": "56771:4973:2"
        }
        ],
        "lessonOrder": 16,
        "lessonId": 16982,
        "lessonKey": "99999_17075",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }, {
        "showDateTime": 1747324800000,
        "showDate": "2025-05-16",
        "nodeStatus": 4,
        "nodeType": 3,
        "title": "阶段奖励",
        "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/174435455339191yqrd.png?checksumV2=crc64%3D12679875747940268622",
        "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553642l0q3xo.png?checksumV2=crc64%3D12679875747940268622",
        "unfinishLottieIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553770fzs9m4.json?checksumV2=crc64%3D12130129839242141905",
        "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545535232v0n6u.png?checksumV2=crc64%3D2463452523341464710",
        "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545534575w194g.mp3?checksumV2=crc64%3D8073808882569868043",
        "unfinishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443545537040qbuk4.mp3?checksumV2=crc64%3D17308299302295519150",
        "finishVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744354553582l2azg8.mp3?checksumV2=crc64%3D8958973771448503016",
        "route": null,
        "toast": null,
        "nodeId": null,
        "sortOrder": 12,
        "rewardVoList": [{
          "rewardCount": 20,
          "rewardType": 1,
          "actionType": 4,
          "actionTargetId": "4973"
        }
        ],
        "lessonOrder": 16,
        "lessonId": 16982,
        "lessonKey": "99999_17075",
        "segmentName": "第二月",
        "segmentId": 4973,
        "weekName": null,
        "weekId": 0,
        "introductory": null,
        "courseChildType": null,
        "focus": null,
        "position": null,
        "gif": null,
        "studyDesc": "去领奖",
        "lessonGrade": null,
        "lessonGradeResourceUrl": null,
        "lessonSubjectType": 2,
        "subjectNodeIcon": null
      }
      ],
      "classFunctionList": [],
      "courseStartPrepareVo": {
        "courseImg": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/286448556113575936/1624934664911b7174f85d5176e8d8e20f77bb152536b.png",
        "title": null,
        "startClassDateDesc": "已开始",
        "prepareNodeList": [{
          "showDateTime": null,
          "showDate": null,
          "nodeStatus": 1,
          "nodeType": 14,
          "title": "添加班班",
          "lockIcon": "https://jojopublicfat.jojoread.com/pc/luban/msg/611928474648145921/luban-1702534555503-1/src%3Dhttp___img.alicdn.com_imgextra_i4_2201429118037_O1CN01CHpx7r29Ezk5LMz5p_%21%212201429118037.jpg%26refer%3Dhttp___img.alicdn.webp",
          "unfinishIcon": "https://jojopublicfat.jojoread.com/pc/luban/msg/611928474648145921/luban-1702534555503-1/src%3Dhttp___img.alicdn.com_imgextra_i4_2201429118037_O1CN01CHpx7r29Ezk5LMz5p_%21%212201429118037.jpg%26refer%3Dhttp___img.alicdn.webp",
          "unfinishLottieIcon": null,
          "finishIcon": "https://jojopublicfat.jojoread.com/pc/luban/msg/611928474648145921/luban-1702534555503-1/src%3Dhttp___img.alicdn.com_imgextra_i4_2201429118037_O1CN01CHpx7r29Ezk5LMz5p_%21%212201429118037.jpg%26refer%3Dhttp___img.alicdn.webp",
          "lockVoice": "",
          "unfinishVoice": "",
          "finishVoice": "",
          "route": "tinman-router://cn.tinman.jojoread/webview?url=https%3A%2F%2Fact.fat.tinman.cn%2Fexchange%2Fsuccess%3ForderId%3D799643619171394561%26classTeacherIds%3D149184%26userId%3D100459480%26classKey%3D25012_20%26gudPgId%3D770%26qrPgId%3D119",
          "toast": null,
          "nodeId": 10654,
          "sortOrder": 0,
          "rewardVoList": null,
          "lessonOrder": null,
          "lessonId": null,
          "lessonKey": null,
          "segmentName": null,
          "segmentId": null,
          "weekName": null,
          "weekId": null,
          "introductory": null,
          "courseChildType": null,
          "focus": null,
          "position": null,
          "gif": null,
          "studyDesc": null,
          "lessonGrade": null,
          "lessonGradeResourceUrl": null,
          "lessonSubjectType": null,
          "subjectNodeIcon": null,
          "popupInfo": {
            "popupTitle": "专属班班",
            "introduceImageList": [
              "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/756547486278519809/lQLPJxO9ADu6XHHNBbrNAu6wSbO_vUda0swHbSc9pe7cAA_750_1466.png?checksumV2=md5Hex%3Dfbd0de3e0a88379bbca7b2b30edf95dd"
            ],
            "buttonRoute": "tinman-router://cn.tinman.jojoread/webview?url=https%3A%2F%2Fact.fat.tinman.cn%2Fexchange%2Fsuccess%3ForderId%3D799643619171394561%26classTeacherIds%3D149184%26userId%3D100459480%26classKey%3D25012_20%26gudPgId%3D770%26qrPgId%3D119",
            "buttonText": "加微信享服务",
            "closedText": null
          }
        }, {
          "showDateTime": null,
          "showDate": null,
          "nodeStatus": 1,
          "nodeType": 20,
          "title": "学习偏好设置-测试",
          "lockIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799689094801027073/cc.png?checksumV2=md5Hex%3Df8f6057c28bc8800355a19d16b310e14",
          "unfinishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799689128380624897/cc.png?checksumV2=md5Hex%3Df8f6057c28bc8800355a19d16b310e14",
          "unfinishLottieIcon": null,
          "finishIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799689128380624903/cc.png?checksumV2=md5Hex%3Df8f6057c28bc8800355a19d16b310e14",
          "lockVoice": "",
          "unfinishVoice": "",
          "finishVoice": "",
          "route": null,
          "toast": null,
          "nodeId": 4116,
          "sortOrder": 0,
          "rewardVoList": null,
          "lessonOrder": null,
          "lessonId": null,
          "lessonKey": null,
          "segmentName": null,
          "segmentId": null,
          "weekName": null,
          "weekId": null,
          "introductory": null,
          "courseChildType": null,
          "focus": null,
          "position": null,
          "gif": null,
          "studyDesc": null,
          "lessonGrade": null,
          "lessonGradeResourceUrl": null,
          "lessonSubjectType": null,
          "subjectNodeIcon": null,
          "popupInfo": {
            "popupTitle": "学习偏好设置",
            "introduce": "请选择您喜欢的学习节奏，班班为您提供针对性督学服务",
            "actionTitle": "偏好设置",
            "actionTip": "为了督促小朋友养成良好的学习习惯，建议您每日学习1节内容",
            "closedText": "学习偏好已保存",
            "hasSetting": 0,
            "buttonText": "确定",
            "options": [{
              "navTitle": "周一开始学",
              "navKey": 1,
              "hasChoice": 0,
              "contentImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799338026065883137/11.png?checksumV2=md5Hex%3D36b43be1ad6263d391ce6be7ca4a5207"
            }, {
              "navTitle": "周三开始学",
              "navKey": 3,
              "hasChoice": 0,
              "contentImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799338110056821761/11.png?checksumV2=md5Hex%3D36b43be1ad6263d391ce6be7ca4a5207"
            }, {
              "navTitle": "周五开始学",
              "navKey": 5,
              "hasChoice": 0,
              "contentImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/799338152045998081/11.png?checksumV2=md5Hex%3D36b43be1ad6263d391ce6be7ca4a5207"
            }
            ]
          }
        }
        ]
      },
      "renewalTimetableVo": null,
      "buyCourseVo": null,
      "addTeacherVo": null,
      "userGiftCourseVoList": [],
      "activities": {
        "activityId": 2581,
        "advanceStartTime": 1749225600000,
        "advanceEndTime": 1749484799000,
        "activityStartTime": 1749484800000,
        "activityEndTime": 1750089599000,
        "status": 3,
        "themeRes": {
          "themeHead": {
            "phoneBackgroundImg": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797465970529432579.png",
            "padBackgroundImg": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797466012497638401.png"
          },
          "themeActivityCard": {
            "icon": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797466054465844225.png",
            "progressRes": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696603439853569/1747302532760/star_second.zip.flutter",
            "backgroundRes": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696611853627393/1747302538831/bg_medal.zip.flutter",
            "progressHeadColor": "#e9f02d",
            "progressTailColor": "#4af3f0",
            "advanceText": "夏日连续学0天后开始",
            "advancePage": "https://jojoread.fat.tinman.cn/medal/active/config?activeId=173492&fullScreen=true",
            "activityText": "学8天赢奖章！邀你来战",
            "activityPage": "https://jojoread.fat.tinman.cn/medal/active/config?activeId=173492&fullScreen=true"
          },
          "themeLessonCard": {
            "collectRes": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696653851193345/1747302548093/star_first.zip.flutter"
          },
          "themeActivityPopup": {
            "mainTitle": "sadsdasdsaas",
            "subTitle": "sadasdSASA",
            "headImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/797131682764190725/17466910115249wnoeh.png?checksumV2=md5Hex%3D10604a7edef04b5f9d93ef72ba6a559d",
            "btnTitle": "我是按钮文字",
            "guidingAudio": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810100463668985867/audio_event_soundeffects_finish.mp3?checksumV2=md5Hex%3D1e7e0154520894dd2d198a175e825ddf"
          }
        },
        "madelGetPopup": {
          "mainTitle": "sadsdasdsaas",
          "subTitle": "sadasdSASA",
          "headImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/797131682764190725/17466910115249wnoeh.png?checksumV2=md5Hex%3D10604a7edef04b5f9d93ef72ba6a559d",
          "btnTitle": "我是按钮文字"
        },
        "taskVo": [
          {
            "taskId": 7680,
            "name": "暑期完课活动测试",
            "isFinish": 0,
            "isGet": 0,
            "conditions": [
              {
                "currentValue": 4,
                "targetValue": 5,
                "type": 49,
                "lessonIds": null
              }
            ],
            "rewards": [
              {
                "rewardId": 266,
                "type": 11,
                "lockImage": null,
                "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797465676697465857.png",
                "isGet": 0,
                "isPopup": 0,
                "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712833/1747302583229/medal_summer.zip.ios",
                "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696779827113985/1747302579683/medal_summer.zip.android",
                "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712836/1747302587211/medal_summer.zip.flutter",
                "bizId": "2581_7680_266",
                "rewardBizUrl": ""
              }
            ],
            "taskExtendResource" : {
              "rewardDisplayUrl" : "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810100209246699521/Physical_doll.zip.flutter.zip?checksumV2=md5Hex%3D4e9c1af5e0731154fcd9e2c8ffe9b24b",
              "mainText" : "一起来抽奖吧1",
              "subText" : "抽锤子的奖，看老子抽你一脸111"
            }
          },
          {
            "taskId": 7683,
            "name": "暑期完课活动测试",
            "isFinish": 0,
            "isGet": 0,
            "conditions": [
              {
                "currentValue": 4,
                "targetValue": 10,
                "type": 49,
                "lessonIds": null
              }
            ],
            "rewards": [
              {
                "rewardId": 266,
                "type": 11,
                "lockImage": null,
                "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797465676697465857.png",
                "isGet": 0,
                "isPopup": 0,
                "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712833/1747302583229/medal_summer.zip.ios",
                "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696779827113985/1747302579683/medal_summer.zip.android",
                "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712836/1747302587211/medal_summer.zip.flutter",
                "bizId": "2581_7680_266",
                "rewardBizUrl": ""
              }
            ],
            "taskExtendResource" : {
              "rewardDisplayUrl" : "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810100209246699521/Physical_doll.zip.flutter.zip?checksumV2=md5Hex%3D4e9c1af5e0731154fcd9e2c8ffe9b24b",
              "mainText" : "一起来抽奖吧2",
              "subText" : "抽锤子的奖，看老子抽你一脸222"
            }
          },
          {
            "taskId": 7685,
            "name": "暑期完课活动测试",
            "isFinish": 0,
            "isGet": 0,
            "conditions": [
              {
                "currentValue": 4,
                "targetValue": 15,
                "type": 49,
                "lessonIds": null
              }
            ],
            "rewards": [
              {
                "rewardId": 266,
                "type": 11,
                "lockImage": null,
                "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797465676697465857.png",
                "isGet": 0,
                "isPopup": 0,
                "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712833/1747302583229/medal_summer.zip.ios",
                "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696779827113985/1747302579683/medal_summer.zip.android",
                "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712836/1747302587211/medal_summer.zip.flutter",
                "bizId": "2581_7680_266",
                "rewardBizUrl": ""
              }
            ],
            "taskExtendResource" : {
              "rewardDisplayUrl" : "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810100209246699521/Physical_doll.zip.flutter.zip?checksumV2=md5Hex%3D4e9c1af5e0731154fcd9e2c8ffe9b24b",
              "mainText" : "一起来抽奖吧3",
              "subText" : "抽锤子的奖，看老子抽你一脸333"
            }
          },
          {
            "taskId": 7690,
            "name": "暑期完课活动测试",
            "isFinish": 0,
            "isGet": 0,
            "conditions": [
              {
                "currentValue": 4,
                "targetValue": 20,
                "type": 49,
                "lessonIds": null
              }
            ],
            "rewards": [
              {
                "rewardId": 266,
                "type": 11,
                "lockImage": null,
                "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/797465676697465857.png",
                "isGet": 0,
                "isPopup": 0,
                "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712833/1747302583229/medal_summer.zip.ios",
                "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696779827113985/1747302579683/medal_summer.zip.android",
                "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/799696813406712836/1747302587211/medal_summer.zip.flutter",
                "bizId": "2581_7680_266",
                "rewardBizUrl": ""
              }
            ],
            "taskExtendResource" : {
              "rewardDisplayUrl" : "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810100209246699521/Physical_doll.zip.flutter.zip?checksumV2=md5Hex%3D4e9c1af5e0731154fcd9e2c8ffe9b24b",
              "mainText" : "一起来抽奖吧4",
              "subText" : "抽锤子的奖，看老子抽你一脸444"
            }
          }
        ],
        "hasLesson": 2,
        "nodeRewardVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/797131565256569857/2..mp3?checksumV2=md5Hex%3Db840661a09d4b4677ac987bd46b96ec8",
        "hasInProcessPopups": 1,
        "postIcon": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/801507856357422081/bg.png?checksumV2=md5Hex%3D9953c2bc05b7236a41e3ce5d5b5eb703"
      },
      "friendCardInfo": {
        "icon": "https://devops.xjjj.co/thirdimg/2018101619325816259.gif?imageView2/1/w/80/h/80",
        "title": "你有一张好友卡快来领取",
        "validPeriod": 1747634717000,
        "jumpRoute":
            "tinman-router://cn.tinman.jojoread/webview?url=https%3A%2F%2Fact.fat.tinman.cn%2Fexchange%2Fsuccess%3ForderId%3D799643619171394561%26classTeacherIds%3D149184%26userId%3D100459480%26classKey%3D25012_20%26gudPgId%3D770%26qrPgId%3D119"
      },
      "pageCount": 12,
      "page": 1,
      "pageSize": 20
    };
    CourseLessonInfo test = CourseLessonInfo.fromJson(jsonDate);
    return test;
  }

  @override
  Future<CourseLessonInfo> getUserClass(String classKey,int pageIndex) {
    return Future.value(mockHomePageData());
  }
}

void main() {
  late PlanHomeLessonCtrl controller;
  late MockHomePageApiService mockApiService;
  late MockBuildContext mockContext;
  setUp(() {
    mockContext = MockBuildContext();
    mockApiService =
        MockHomePageApiService(MockHomePageApiService.mockHomePageData()!);
  });

  group('plan_lesson_controller tests:', () {
    WidgetsFlutterBinding.ensureInitialized();
    // 设置mock对象的行为
    test("onRefresh", () {
      mockContext = MockBuildContext();
      var controller = PlanHomeLessonCtrl(api: mockApiService);
      controller.isCurrentWidgetShow = true;
      controller.state.lessonInfo = MockHomePageApiService.mockHomePageData();
      controller.onRefresh(mockContext, "25012_20",
          color4: const Color(0xFF404040),
          color6: const Color(0xFF404040),
          colorVariant5: const Color(0xFF404040));
      assert(controller.state.lessonInfo?.classId == 56771);
    });

    test("emitFoldRefresh", () {
      var controller = PlanHomeLessonCtrl(api: mockApiService);
      controller.emitFoldRefresh(true);
      assert(controller.isFoldCourse == true);
    });

    test("testDealWithActivitySpineResources", () {
      var controller = SimpleTestController(api: mockApiService, shouldDownloadSucceed: true);
      MockHomeDownManager downManager = MockHomeDownManager();
      controller.resourceManager = downManager;
      controller.state.lessonInfo = MockHomePageApiService.mockHomePageData();
      PromoteLessonFinishModel? promoteFinishModel = PromoteLessonFinishModel.fromClassTimetableVo(null, "111", 2, controller.state.lessonInfo!, controller);
      controller.state.promoteFinishData = promoteFinishModel;
      PromoteLessonFinishModel model = PromoteLessonFinishModel();
      model.lessonInProgress = LessonInProgressModel();
      String downLoadUrl = "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610";
      model.lessonInProgress!.spineResourceInfo = PromoteLessonFinishSpineResourceInfo();
      model.lessonInProgress!.spineResourceInfo!.badgeBgResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.badgeBgResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.targetResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.targetResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.starResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.starResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.starFlyResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.starFlyResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.giftList = [];
      LessonInProgressGiftModel giftModel = LessonInProgressGiftModel();
      giftModel.giftSpineUrl = downLoadUrl;
      model.lessonInProgress!.giftList.add(giftModel);
      controller.testDealWithActivitySpineResources(model, true);
      assert(controller.state.promoteFinishData?.lessonInProgress?.spineResourceInfo != null);
    });

    test("updateResourceState", () {
      var controller = SimpleTestController(api: mockApiService, shouldDownloadSucceed: true);
      MockHomeDownManager downManager = MockHomeDownManager();
      controller.resourceManager = downManager;
      controller.state.lessonInfo = MockHomePageApiService.mockHomePageData();
      PromoteLessonFinishModel? promoteFinishModel = PromoteLessonFinishModel.fromClassTimetableVo(null, "111", 2, controller.state.lessonInfo!, controller);
      controller.state.promoteFinishData = promoteFinishModel;
      PromoteLessonFinishModel model = PromoteLessonFinishModel();
      model.lessonInProgress = LessonInProgressModel();
      String downLoadUrl = "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610";
      model.lessonInProgress!.spineResourceInfo = PromoteLessonFinishSpineResourceInfo();
      model.lessonInProgress!.spineResourceInfo!.badgeBgResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.badgeBgResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.targetResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.targetResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.starResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.starResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.spineResourceInfo!.starFlyResource = PromoteLessonFinishSpineResourceVo();
      model.lessonInProgress!.spineResourceInfo!.starFlyResource.resourceUrl = downLoadUrl;
      model.lessonInProgress!.giftList = [];
      controller.updateResourceState({}, true, (model, kMap) {
        return true;
      });
      assert(controller.state.promoteFinishData?.lessonInProgress?.spineResourceInfo != null);
    });

    test("searchFirstUnFinishLessonAndPosition", () async {
      var controller = PlanHomeLessonCtrl(api: mockApiService);
      await controller.onRefresh(mockContext, "25012_20",
          color4: const Color(0xFF404040),
          color6: const Color(0xFF404040),
          colorVariant5: const Color(0xFF404040));
      controller.state.lessonListResetPosition = -1;
      controller.searchFirstUnFinishLessonAndPosition(false);
      assert(controller.state.lessonListResetPosition != -1);
      controller.state.lessonListResetPosition = -1;
      controller.searchFirstUnFinishLessonAndPosition(true);
      assert(controller.state.lessonListResetPosition != -1);
    });
  });
}

class MockBuildContext extends Mock implements BuildContext {
  @override
  bool get mounted => true;

}
