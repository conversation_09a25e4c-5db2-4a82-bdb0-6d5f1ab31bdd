import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/course_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/course_lesson_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/question_books_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/segments_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/state.dart';
import 'package:jojo_flutter_plan_pkg/service/question_bank_list_api.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 简单的Mock类，不使用mockito
class MockQuestionBankListApi implements QuestionBankListApi {
  // 用于控制返回结果的标志
  bool shouldThrow = false;
  SegmentsData? segmentsDataToReturn;
  CourseLessonData? courseLessonDataToReturn;
  QuestionBooksModel? questionBooksDataToReturn;
  CourseInfoData? courseInfoDataToReturn;

  @override
  Future<CourseInfoData> getCourseInfoCommonData({
    required String courseKey,
    required String classId,
  }) async {
    if (shouldThrow) throw Exception('Mock error');
    return courseInfoDataToReturn ?? const CourseInfoData();
  }

  @override
  Future<SegmentsData> getSegments(String classKey, String sceneType) async {
    if (shouldThrow) throw Exception('Mock error');
    return segmentsDataToReturn ?? SegmentsData();
  }

  @override
  Future<CourseLessonData> getLessons(int classId, String type, int segmentId) async {
    if (shouldThrow) throw Exception('Mock error');
    return courseLessonDataToReturn ?? CourseLessonData();
  }

  @override
  Future<QuestionBooksModel> getQuestionBooks(
    String courseKey,
    String classKey,
    int? segmentId,
    String? offset,
    String? status,
    String type,
  ) async {
    if (shouldThrow) throw Exception('Mock error');
    return questionBooksDataToReturn ?? QuestionBooksModel();
  }
}

void main() {
  // 初始化Flutter binding，解决测试中的binding问题
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockQuestionBankListApi mockService;

  // Mock所有需要的channel调用
  setUpAll(() {
    // Mock SharedPreferences
    SharedPreferences.setMockInitialValues({});

    // Mock所有需要的MethodChannel调用，避免测试中的platform channel错误
    const channels = [
      'jojo_bridge_constraint',
      'JOJO_METHOD_CHANNEL',
      'xyz.luan/audioplayers',
      'sensors_track',
      'flutter/platform',
      'flutter/textinput',
      'flutter/keyboard',
      'flutter/restoration',
    ];

    for (final channelName in channels) {
      TestDefaultBinaryMessengerBinding.instance?.defaultBinaryMessenger
          .setMockMethodCallHandler(
        MethodChannel(channelName),
        (MethodCall methodCall) async {
          // 根据不同的方法返回适当的mock数据
          switch (methodCall.method) {
            case 'invokeMethod':
            case 'JoJoNativeBridge:printNativeLog':
            case 'printNativeLog':
              return {}; // 返回Map而不是String
            case 'play':
            case 'pause':
            case 'stop':
            case 'seek':
              return true;
            case 'SystemChrome.setSystemUIOverlayStyle':
            case 'SystemNavigator.pop':
              return null;
            case 'track': // sensors_track方法
              return {};
            case 'getData':
              return 'false'; // 返回假的数据
            case 'saveData':
              return true;
            default:
              return {}; // 默认返回空Map
          }
        },
      );
    }
  });

  late QuestionBankListCtrl controller;

  group('QuestionBankListCtrl', () {
    setUp(() {
      mockService = MockQuestionBankListApi();
    });

    group('初始化测试', () {
      test('应该初始化为loading状态', () {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );

        expect(controller.state.pageStatus, equals(PageStatus.loading));
        expect(controller.state.lessonInfo, isEmpty);
        expect(controller.state.radioGroup, isEmpty);
        expect(controller.state.checkedLessonIds, isEmpty);
        expect(controller.state.guideStep, equals(-1));
        expect(controller.state.wrongQuestionStatus,
            equals(WrongQuestionStatus.remaining));
        expect(controller.state.isQuestionLoading, equals(false));
        expect(controller.state.currTabIndex, equals(0));
        expect(controller.state.visibleOptionModal, equals(false));
        expect(controller.state.isLoading, equals(false));
        
        controller.close();
      });

      test('构造函数参数应该正确设置', () {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course_key',
          classId: '456',
          classKey: '',
          mockApi: mockService,
        );

        expect(controller.courseKey, equals('test_course_key'));
        expect(controller.classId, equals('456'));
        expect(controller.classKey, equals(''));
        
        controller.close();
      });

      test('当classKey为空时应该设置为error状态', () async {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );

        // 等待initState完成
        await Future.delayed(const Duration(milliseconds: 100));

        expect(controller.state.pageStatus, equals(PageStatus.error));
        
        controller.close();
      });

      test('initState成功流程应该正确设置状态', () async {
        // 准备测试数据
        final segmentsData = SegmentsData(
          courseInfo: CourseInfoModel(
            courseKey: 'test_course',
            courseName: 'Test Course',
          ),
          classInfo: ClassInfoModel(
            classId: 123,
            classKey: 'test_class',
          ),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
            ),
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
            ),
          ],
        );

        final courseLessonData = CourseLessonData(
          lessonList: [
            LessonInfo(
              lessonId: 1,
              lessonTitle: 'Lesson 1',
              weekId: 1,
              segmentId: 1,
              studyStage: 1,
            ),
          ],
        );

        mockService.segmentsDataToReturn = segmentsData;
        mockService.courseLessonDataToReturn = courseLessonData;

        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: 'test_class',
          mockApi: mockService,
        );

        // 等待initState完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state.pageStatus, equals(PageStatus.success));
        expect(controller.state.segmentsData, equals(segmentsData));
        expect(controller.state.courseLessonData, equals(courseLessonData));
        
        controller.close();
      });

      test('initState失败应该设置error状态', () async {
        mockService.shouldThrow = true;

        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: 'test_class',
          mockApi: mockService,
        );

        // 等待initState完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state.pageStatus, equals(PageStatus.error));
        
        controller.close();
      });

      test('initState应该处理segmentsData为null的情况', () async {
        mockService.segmentsDataToReturn = SegmentsData(
          classInfo: null,
          itemList: [],
        );

        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: 'test_class',
          mockApi: mockService,
        );

        // 等待initState完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state.pageStatus, equals(PageStatus.error));
        
        controller.close();
      });

      test('initState应该处理引导步骤显示', () async {
        // 设置多个segments来触发引导
        final segmentsData = SegmentsData(
          courseInfo: CourseInfoModel(
            courseKey: 'test_course',
            courseName: 'Test Course',
          ),
          classInfo: ClassInfoModel(
            classId: 123,
            classKey: 'test_class',
          ),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
            ),
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
            ),
          ],
        );

        final courseLessonData = CourseLessonData(
          lessonList: [
            LessonInfo(
              lessonId: 1,
              lessonTitle: 'Lesson 1',
              weekId: 1,
              segmentId: 1,
              studyStage: 1,
            ),
          ],
        );

        mockService.segmentsDataToReturn = segmentsData;
        mockService.courseLessonDataToReturn = courseLessonData;

        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: 'test_class',
          mockApi: mockService,
        );

        // 等待initState和引导步骤完成
        await Future.delayed(const Duration(milliseconds: 500));

        // 验证引导步骤被触发
        expect(controller.state.guideStep, greaterThanOrEqualTo(0));
        
        controller.close();
      });
    });

    group('getLessonsData测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('成功获取课程数据', () async {
        final courseLessonData = CourseLessonData(
          lessonList: [
            LessonInfo(
              lessonId: 1,
              lessonTitle: 'Lesson 1',
              weekId: 1,
              segmentId: 1,
              studyStage: 1,
            ),
          ],
        );

        mockService.courseLessonDataToReturn = courseLessonData;

        await controller.getLessonsData(
          classId: 123,
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
        );

        expect(controller.state.pageStatus, equals(PageStatus.success));
        expect(controller.state.courseLessonData, equals(courseLessonData));
        expect(controller.state.lessonInfo, isNotEmpty);
        expect(controller.state.isLoading, equals(false));
        expect(controller.state.isCourseLessonError, equals(false));
      });

      test('获取课程数据失败', () async {
        mockService.shouldThrow = true;

        await controller.getLessonsData(
          classId: 123,
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
        );

        expect(controller.state.pageStatus, equals(PageStatus.error));
        expect(controller.state.isLoading, equals(false));
        expect(controller.state.isCourseLessonError, equals(true));
      });

      test('使用默认参数获取课程数据', () async {
        // 设置segmentsData
        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classId: 456),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 2),
            ),
          ],
        );

        controller.emit(controller.state.copyWith()
          ..segmentsData = segmentsData
          ..selectSegmentInfoModel = segmentsData.itemList?.first);

        mockService.courseLessonDataToReturn = CourseLessonData(
          lessonList: [
            LessonInfo(
              lessonId: 2,
              lessonTitle: 'Lesson 2',
              weekId: 1,
              segmentId: 2,
              studyStage: 1,
            ),
          ],
        );

        // 不传参数，使用默认值
        await controller.getLessonsData();

        expect(controller.state.pageStatus, equals(PageStatus.success));
        expect(controller.state.courseLessonData?.lessonList?.isNotEmpty, equals(true));
      });

      test('应该设置loading状态', () async {
        mockService.courseLessonDataToReturn = CourseLessonData(
          lessonList: [
            LessonInfo(
              lessonId: 1,
              lessonTitle: 'Lesson 1',
              weekId: 1,
              segmentId: 1,
              studyStage: 1,
            ),
          ],
        );

        // 调用时设置isShowLoading为true
        controller.getLessonsData(
          classId: 123,
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
          isShowLoading: true,
        );

        // 短暂等待确保loading状态被设置
        await Future.delayed(const Duration(milliseconds: 10));

        expect(controller.state.isLoading, equals(true));

        // 等待完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state.isLoading, equals(false));
      });
    });

    group('getQuestionBookLessonData测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('成功获取错题本数据', () async {
        final questionBooksData = QuestionBooksModel(
          courseKey: 'test_course',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        mockService.questionBooksDataToReturn = questionBooksData;

        await controller.getQuestionBookLessonData(
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
          wrongQuestionStatus: WrongQuestionStatus.remaining,
        );

        expect(controller.state.questionBooksData, equals(questionBooksData));
        expect(controller.state.questionBookLessonInfo, isNotEmpty);
        expect(controller.state.isQuestionLoading, equals(false));
        expect(controller.state.isQuestionBookError, equals(false));
      });

      test('获取错题本数据失败', () async {
        mockService.shouldThrow = true;

        await controller.getQuestionBookLessonData(
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
          wrongQuestionStatus: WrongQuestionStatus.remaining,
        );

        expect(controller.state.isQuestionLoading, equals(false));
        expect(controller.state.isQuestionBookError, equals(true));
      });

      test('使用默认参数获取错题本数据', () async {
        // 设置必要的状态
        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classKey: 'test_class'),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1),
            ),
          ],
        );

        controller.emit(controller.state.copyWith()
          ..segmentsData = segmentsData
          ..selectSegmentInfoModel = segmentsData.itemList?.first
          ..wrongQuestionStatus = WrongQuestionStatus.eliminated);

        mockService.questionBooksDataToReturn = QuestionBooksModel(
          courseKey: 'test_course',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        // 不传参数，使用默认值
        await controller.getQuestionBookLessonData();

        expect(controller.state.questionBooksData?.units?.isNotEmpty, equals(true));
      });

      test('应该设置loading状态', () async {
        mockService.questionBooksDataToReturn = QuestionBooksModel(
          courseKey: 'test_course',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        // 调用时设置isShowLoading为true
        controller.getQuestionBookLessonData(
          selectSegmentInfoModel: ItemModel(
            segmentInfo: SegmentInfoModel(segmentId: 1),
          ),
          isShowLoading: true,
        );

        // 短暂等待确保loading状态被设置
        await Future.delayed(const Duration(milliseconds: 10));

        expect(controller.state.isQuestionLoading, equals(true));

        // 等待完成
        await Future.delayed(const Duration(milliseconds: 200));

        expect(controller.state.isQuestionLoading, equals(false));
      });
    });

    group('loadMoreQuestionList测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('没有offset时应该不加载', () async {
        // 设置没有offset的数据
        controller.emit(controller.state.copyWith()
          ..questionBooksData = QuestionBooksModel(offset: null));

        await controller.loadMoreQuestionList();

        // 验证状态保持不变
        expect(controller.state.questionBooksData?.offset, isNull);
      });

      test('成功加载更多数据', () async {
        // 设置初始数据
        final initialData = QuestionBooksModel(
          offset: 'next_offset',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        final moreData = QuestionBooksModel(
          offset: 'final_offset',
          units: [UnitModel(unitId: 'unit2', unitName: 'Unit 2')],
          lessons: [LessonModel(id: 2, name: 'Lesson 2', unitId: 'unit2')],
        );

        mockService.questionBooksDataToReturn = moreData;

        // 设置必要的状态
        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classKey: 'test_class'),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1),
            ),
          ],
        );

        controller.emit(controller.state.copyWith()
          ..questionBooksData = initialData
          ..segmentsData = segmentsData
          ..selectSegmentInfoModel = segmentsData.itemList?.first
          ..wrongQuestionStatus = WrongQuestionStatus.remaining);

        await controller.loadMoreQuestionList();

        // 验证数据合并
        expect(controller.state.questionBooksData?.units?.length, equals(2));
        expect(controller.state.questionBooksData?.lessons?.length, equals(2));
        expect(controller.state.questionBooksData?.offset, equals('final_offset'));
      });

      test('加载更多数据失败', () async {
        mockService.shouldThrow = true;

        // 设置初始数据
        final initialData = QuestionBooksModel(
          offset: 'next_offset',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        // 设置必要的状态
        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classKey: 'test_class'),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1),
            ),
          ],
        );

        controller.emit(controller.state.copyWith()
          ..questionBooksData = initialData
          ..segmentsData = segmentsData
          ..selectSegmentInfoModel = segmentsData.itemList?.first
          ..wrongQuestionStatus = WrongQuestionStatus.remaining);

        await controller.loadMoreQuestionList();

        // 验证错误处理
        expect(controller.state.questionBooksData?.offset, equals('next_offset'));
      });

      test('使用eliminated状态加载更多数据', () async {
        // 设置初始数据
        final initialData = QuestionBooksModel(
          offset: 'next_offset',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        final moreData = QuestionBooksModel(
          offset: 'final_offset',
          units: [UnitModel(unitId: 'unit2', unitName: 'Unit 2')],
          lessons: [LessonModel(id: 2, name: 'Lesson 2', unitId: 'unit2')],
        );

        mockService.questionBooksDataToReturn = moreData;

        // 设置必要的状态
        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classKey: 'test_class'),
          itemList: [
            ItemModel(
              segmentInfo: SegmentInfoModel(segmentId: 1),
            ),
          ],
        );

        controller.emit(controller.state.copyWith()
          ..questionBooksData = initialData
          ..segmentsData = segmentsData
          ..selectSegmentInfoModel = segmentsData.itemList?.first
          ..wrongQuestionStatus = WrongQuestionStatus.eliminated);

        await controller.loadMoreQuestionList();

        // 验证数据合并
        expect(controller.state.questionBooksData?.units?.length, equals(2));
        expect(controller.state.questionBooksData?.lessons?.length, equals(2));
        expect(controller.state.questionBooksData?.offset, equals('final_offset'));
      });
    });

    group('选择逻辑测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('toggleSegment 应该切换段的选择状态', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
              LessonInfo(
                lessonId: 3,
                lessonTitle: 'Lesson 3',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Act
        controller.toggleSegment(
          weekIndex: 0,
          segmentIndex: 0,
          isChecked: true,
        );

        // Assert
        final segment = controller.state.lessonInfo[0][0];
        expect(segment[0].checked, equals(true));
        expect(segment[1].checked, equals(true));
        expect(segment[2].checked, equals(true));
      });

      test('toggleSegment 应该能取消选择', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
              LessonInfo(
                lessonId: 3,
                lessonTitle: 'Lesson 3',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Arrange - 先选中
        controller.toggleSegment(
          weekIndex: 0,
          segmentIndex: 0,
          isChecked: true,
        );

        // Act - 取消选择
        controller.toggleSegment(
          weekIndex: 0,
          segmentIndex: 0,
          isChecked: false,
        );

        // Assert
        final segment = controller.state.lessonInfo[0][0];
        expect(segment[0].checked, equals(false));
        expect(segment[1].checked, equals(false));
        expect(segment[2].checked, equals(false));
      });

      test('toggleLesson 应该切换单个课的选择状态', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
              LessonInfo(
                lessonId: 3,
                lessonTitle: 'Lesson 3',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Act
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );

        // Assert
        expect(controller.state.lessonInfo[0][0][0].checked, equals(true));
        expect(controller.state.lessonInfo[0][0][1].checked, equals(false));
        expect(controller.state.lessonInfo[0][0][2].checked, equals(false));
      });

      test('toggleLesson 应该能取消单个课的选择', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Arrange
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );

        // Act
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: false,
        );

        // Assert
        expect(controller.state.lessonInfo[0][0][0].checked, equals(false));
      });

      test('getSelectedLessons 应该返回所有选中的课程', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
              LessonInfo(
                lessonId: 3,
                lessonTitle: 'Lesson 3',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Arrange
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 2,
          isChecked: true,
        );

        // Act
        final selectedLessons = controller.getSelectedLessons();

        // Assert
        expect(selectedLessons.length, equals(2));
        expect(selectedLessons[0].lessonId, equals(1));
        expect(selectedLessons[1].lessonId, equals(3));
      });

      test('getSelectedLessons 当没有选择时应该返回空列表', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Act
        final selectedLessons = controller.getSelectedLessons();

        // Assert
        expect(selectedLessons, isEmpty);
      });

      test('updateSegmentChecked 应该正确判断段是否全选', () {
        // 手动设置测试数据
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
              LessonInfo(
                lessonId: 3,
                lessonTitle: 'Lesson 3',
                checked: false,
              ),
            ]
          ]
        ];

        // 通过emit更新状态
        controller.emit(controller.state.copyWith()
          ..lessonInfo = lessonInfo);

        // Arrange - 选中第一个课程
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );

        // Act & Assert - 应该返回false，因为不是全选
        expect(controller.updateSegmentChecked(0, 0), equals(false));

        // Arrange - 选中所有课程
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 1,
          isChecked: true,
        );
        controller.toggleLesson(
          weekIndex: 0,
          segmentIndex: 0,
          lessonIndex: 2,
          isChecked: true,
        );

        // Act & Assert - 现在应该返回true，因为全选了
        expect(controller.updateSegmentChecked(0, 0), equals(true));
      });
    });

    group('错题本选择逻辑测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('toggleQuestionSegment 应该切换错题段的选择状态', () {
        // 设置错题本测试数据
        final questionBookLessonInfo = [
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [
              LessonModel(
                id: 1,
                name: 'Question Lesson 1',
                checked: false,
              ),
              LessonModel(
                id: 2,
                name: 'Question Lesson 2',
                checked: false,
              ),
            ],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        // Act
        controller.toggleQuestionSegment(
          segmentIndex: 0,
          isChecked: true,
        );

        // Assert
        final lessons = controller.state.questionBookLessonInfo![0].lessons!;
        expect(lessons[0].checked, equals(true));
        expect(lessons[1].checked, equals(true));
      });

      test('toggleQuestionSegment 应该能处理空课程列表', () {
        // Arrange - 创建空课程列表的单元
        final emptyQuestionBookLessonInfo = [
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: null,
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = emptyQuestionBookLessonInfo);

        // Act & Assert - 不应该抛出异常
        expect(
            () => controller.toggleQuestionSegment(
                  segmentIndex: 0,
                  isChecked: true,
                ),
            returnsNormally);
      });

      test('toggleQuestionLesson 应该切换单个错题课的选择状态', () {
        // 设置错题本测试数据
        final questionBookLessonInfo = [
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [
              LessonModel(
                id: 1,
                name: 'Question Lesson 1',
                checked: false,
              ),
              LessonModel(
                id: 2,
                name: 'Question Lesson 2',
                checked: false,
              ),
            ],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        // Act
        controller.toggleQuestionLesson(
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );

        // Assert
        final lessons = controller.state.questionBookLessonInfo![0].lessons!;
        expect(lessons[0].checked, equals(true));
        expect(lessons[1].checked, equals(false));
      });

      test('getQuestionSelectedLessons 应该返回所有选中的错题课程', () {
        // 设置错题本测试数据
        final questionBookLessonInfo = [
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [
              LessonModel(
                id: 1,
                name: 'Question Lesson 1',
                checked: false,
              ),
            ],
          ),
          GroupQuestionBookModal(
            unitId: '2',
            unitName: 'Unit 2',
            lessons: [
              LessonModel(
                id: 3,
                name: 'Question Lesson 3',
                checked: false,
              ),
            ],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        // Arrange
        controller.toggleQuestionLesson(
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );
        controller.toggleQuestionLesson(
          segmentIndex: 1,
          lessonIndex: 0,
          isChecked: true,
        );

        // Act
        final selectedLessons = controller.getQuestionSelectedLessons();

        // Assert
        expect(selectedLessons.length, equals(2));
        expect(selectedLessons[0].id, equals(1));
        expect(selectedLessons[1].id, equals(3));
      });

      test('getQuestionSelectedLessons 当没有错题数据时应该返回空列表', () {
        // Arrange - 清空错题数据
        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = null);

        // Act
        final selectedLessons = controller.getQuestionSelectedLessons();

        // Assert
        expect(selectedLessons, isEmpty);
      });

      test('updateQuestionSegmentChecked 应该正确判断错题段是否全选', () {
        // 设置错题本测试数据
        final questionBookLessonInfo = [
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [
              LessonModel(
                id: 1,
                name: 'Question Lesson 1',
                checked: false,
              ),
              LessonModel(
                id: 2,
                name: 'Question Lesson 2',
                checked: false,
              ),
            ],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        // Arrange - 选中第一个课程
        controller.toggleQuestionLesson(
          segmentIndex: 0,
          lessonIndex: 0,
          isChecked: true,
        );

        // Act & Assert - 应该返回false，因为不是全选
        expect(controller.updateQuestionSegmentChecked(0), equals(false));

        // Arrange - 选中所有课程
        controller.toggleQuestionLesson(
          segmentIndex: 0,
          lessonIndex: 1,
          isChecked: true,
        );

        // Act & Assert - 现在应该返回true，因为全选了
        expect(controller.updateQuestionSegmentChecked(0), equals(true));
      });
    });

    group('状态管理测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('setTabIndex 应该更新当前tab索引', () {
        // Act
        controller.setTabIndex(1);

        // Assert
        expect(controller.state.currTabIndex, equals(1));

        // Act - 切换到第二个tab
        controller.setTabIndex(0);

        // Assert
        expect(controller.state.currTabIndex, equals(0));
      });

      test('setQuestionStatus 应该更新错题状态', () {
        // Act
        controller.setQuestionStatus(WrongQuestionStatus.eliminated);

        // Assert
        expect(controller.state.wrongQuestionStatus,
            equals(WrongQuestionStatus.eliminated));

        // Act - 切换回remaining状态
        controller.setQuestionStatus(WrongQuestionStatus.remaining);

        // Assert
        expect(controller.state.wrongQuestionStatus,
            equals(WrongQuestionStatus.remaining));
      });

      test('showOptionModal 应该显示选项模态框', () {
        // Act
        controller.showOptionModal();

        // Assert
        expect(controller.state.visibleOptionModal, equals(true));
      });

      test('hiddenOptionModal 应该隐藏选项模态框', () {
        // Arrange - 先显示
        controller.showOptionModal();

        // Act
        controller.hiddenOptionModal();

        // Assert
        expect(controller.state.visibleOptionModal, equals(false));
      });
    });

    group('onChooseSegmentLabel测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('当选择相同segment时应该隐藏模态框', () {
        // Arrange
        final segment = ItemModel(
          segmentInfo: SegmentInfoModel(
            segmentId: 1,
            segmentName: 'Test Segment',
          ),
        );

        controller.emit(controller.state.copyWith()
          ..selectSegmentInfoModel = segment
          ..visibleOptionModal = true);

        // Act
        controller.onChooseSegmentLabel(segment);

        // Assert
        expect(controller.state.visibleOptionModal, equals(false));
      });

      test('当选择不同segment时应该更新选中的segment', () {
        // Arrange
        final originalSegment = ItemModel(
          segmentInfo: SegmentInfoModel(
            segmentId: 1,
            segmentName: 'Original Segment',
          ),
        );

        final newSegment = ItemModel(
          segmentInfo: SegmentInfoModel(
            segmentId: 2,
            segmentName: 'New Segment',
          ),
        );

        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classId: 123),
        );

        controller.emit(controller.state.copyWith()
          ..selectSegmentInfoModel = originalSegment
          ..segmentsData = segmentsData
          ..currTabIndex = 0);

        // Act
        controller.onChooseSegmentLabel(newSegment);

        // Assert
        expect(controller.state.selectSegmentInfoModel?.segmentInfo?.segmentId,
            equals(2));
        expect(controller.state.visibleOptionModal, equals(false));
      });

      test('在tab1下选择不同segment应该触发getQuestionBookLessonData', () {
        // Arrange
        final originalSegment = ItemModel(
          segmentInfo: SegmentInfoModel(
            segmentId: 1,
            segmentName: 'Original Segment',
          ),
        );

        final newSegment = ItemModel(
          segmentInfo: SegmentInfoModel(
            segmentId: 2,
            segmentName: 'New Segment',
          ),
        );

        final segmentsData = SegmentsData(
          classInfo: ClassInfoModel(classId: 123, classKey: 'test_class'),
        );

        mockService.questionBooksDataToReturn = QuestionBooksModel(
          courseKey: 'test_course',
          units: [UnitModel(unitId: 'unit1', unitName: 'Unit 1')],
          lessons: [LessonModel(id: 1, name: 'Lesson 1', unitId: 'unit1')],
        );

        controller.emit(controller.state.copyWith()
          ..selectSegmentInfoModel = originalSegment
          ..segmentsData = segmentsData
          ..currTabIndex = 1);

        // Act
        controller.onChooseSegmentLabel(newSegment);

        // Assert
        expect(controller.state.selectSegmentInfoModel?.segmentInfo?.segmentId,
            equals(2));
        expect(controller.state.visibleOptionModal, equals(false));
      });
    });

    group('引导步骤测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('showGuideStep 应该从-1开始第一步', () async {
        // 设置初始状态
        controller.emit(controller.state.copyWith()
          ..guideStep = -1);

        // 执行第一步引导
        await controller.showGuideStep();

        // 验证第一步状态
        expect(controller.state.guideStep, equals(1));
      });

      test('showGuideStep 应该正确执行第二步引导', () async {
        // 设置初始状态和lessonInfo
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
            ]
          ]
        ];

        controller.emit(controller.state.copyWith()
          ..guideStep = 1
          ..lessonInfo = lessonInfo);

        // 执行第二步引导
        await controller.showGuideStep();

        // 验证第二步状态
        expect(controller.state.guideStep, equals(2));
        expect(controller.state.lessonInfo[0][0][0].checked, equals(true));
      });

      test('showGuideStep 应该正确执行第三步引导', () async {
        // 设置初始状态和lessonInfo
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: false,
              ),
              LessonInfo(
                lessonId: 2,
                lessonTitle: 'Lesson 2',
                checked: false,
              ),
            ]
          ]
        ];

        controller.emit(controller.state.copyWith()
          ..guideStep = 2
          ..lessonInfo = lessonInfo);

        // 执行第三步引导
        await controller.showGuideStep();

        // 验证第三步状态
        expect(controller.state.guideStep, equals(3));
        expect(controller.state.lessonInfo[0][0][0].checked, equals(true));
        expect(controller.state.lessonInfo[0][0][1].checked, equals(true));
      });

      test('showGuideStep 应该正确执行第四步引导', () async {
        // 设置初始状态
        controller.emit(controller.state.copyWith()
          ..guideStep = 3
          ..checkedLessonIds = []);

        // 执行第四步引导
        await controller.showGuideStep();

        // 验证第四步状态
        expect(controller.state.guideStep, equals(4));
        expect(controller.state.checkedLessonIds, equals([1]));
      });

      test('showGuideStep 应该正确结束引导流程', () async {
        // 设置初始状态和lessonInfo
        final lessonInfo = <List<List<LessonInfo>>>[
          [
            [
              LessonInfo(
                lessonId: 1,
                lessonTitle: 'Lesson 1',
                checked: true,
              ),
            ]
          ]
        ];

        controller.emit(controller.state.copyWith()
          ..guideStep = 4
          ..checkedLessonIds = [1]
          ..lessonInfo = lessonInfo);

        // 执行结束引导
        await controller.showGuideStep();

        // 验证引导结束状态
        expect(controller.state.guideStep, equals(-1));
        expect(controller.state.checkedLessonIds, isEmpty);
        expect(controller.state.lessonInfo[0][0][0].checked, equals(false));
      });
    });

    group('数据分组测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('groupLessons 应该正确分组课程数据', () {
        final lessons = <LessonInfo>[
          LessonInfo(
            lessonId: 1,
            lessonTitle: 'Lesson 1',
            studyStage: 1,
            weekId: 1,
            segmentId: 1,
          ),
          LessonInfo(
            lessonId: 2,
            lessonTitle: 'Lesson 2',
            studyStage: 1,
            weekId: 2,
            segmentId: 1,
          ),
          LessonInfo(
            lessonId: 3,
            lessonTitle: 'Lesson 3',
            studyStage: 2,
            weekId: 1,
            segmentId: 1,
          ),
        ];

        // Act
        final result = controller.groupLessons(lessons);

        // Assert
        expect(result.length, greaterThan(0)); // 应该有分组
        expect(result.first.length, greaterThan(0)); // 每个学习阶段应该有数据
      });

      test('groupLessons 应该处理空列表', () {
        // Act
        final result = controller.groupLessons([]);

        // Assert
        expect(result, isEmpty);
      });

      test('groupLessons 应该处理null studyStage', () {
        final lessons = [
          LessonInfo(lessonId: 1, studyStage: null, weekId: 1),
          LessonInfo(lessonId: 2, studyStage: 0, weekId: 1),
        ];

        final result = controller.groupLessons(lessons);

        expect(result.length, equals(1)); // 只有一个stage 0
        expect(result[0].length, equals(1)); // 有1个week组
      });

      test('groupLessons 应该正确处理weekId和segmentId的优先级', () {
        final lessons = [
          LessonInfo(lessonId: 1, studyStage: 1, weekId: 1, segmentId: 1),
          LessonInfo(lessonId: 2, studyStage: 1, weekId: 0, segmentId: 1),
          LessonInfo(lessonId: 3, studyStage: 1, weekId: null, segmentId: 1),
        ];

        final result = controller.groupLessons(lessons);

        expect(result.length, equals(1)); // 一个studyStage
        expect(result[0].length, equals(2)); // 1个week组和1个segment组
      });

      test('groupLessonsByUnit 应该正确分组错题数据', () {
        final units = [
          UnitModel(unitId: '1', unitName: 'Unit 1'),
          UnitModel(unitId: '2', unitName: 'Unit 2'),
        ];

        final lessons = <LessonModel>[
          LessonModel(
            id: 1,
            name: 'Question Lesson 1',
            unitId: '1',
          ),
          LessonModel(
            id: 2,
            name: 'Question Lesson 2',
            unitId: '2',
          ),
          LessonModel(
            id: 3,
            name: 'Question Lesson 3',
            unitId: '1',
          ),
        ];

        // Act
        final result = controller.groupLessonsByUnit(
          units: units,
          lessons: lessons,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result[0].unitId, equals('1'));
        expect(result[0].unitName, equals('Unit 1'));
        expect(result[0].lessons?.length, equals(2));
        expect(result[1].unitId, equals('2'));
        expect(result[1].unitName, equals('Unit 2'));
        expect(result[1].lessons?.length, equals(1));
      });

      test('groupLessonsByUnit 应该处理空数据', () {
        // Act
        final result = controller.groupLessonsByUnit(
          units: [],
          lessons: [],
        );

        // Assert
        expect(result, isEmpty);
      });

      test('groupLessonsByUnit 应该处理null unitId', () {
        final units = <UnitModel>[];
        final lessons = [
          LessonModel(id: 1, name: 'Lesson 1', unitId: null),
          LessonModel(id: 2, name: 'Lesson 2', unitId: ''),
        ];

        final result = controller.groupLessonsByUnit(
          units: units,
          lessons: lessons,
        );

        expect(result.length, equals(2)); // '0' 和 ''
      });

      test('analysisData 应该正确分组数据', () {
        final lessonList = <LessonInfo>[
          LessonInfo(
            lessonId: 1,
            lessonTitle: 'Lesson 1',
            weekId: 1,
            segmentId: 1,
          ),
          LessonInfo(
            lessonId: 2,
            lessonTitle: 'Lesson 2',
            weekId: 0,
            segmentId: 2,
          ),
        ];

        // Act
        final result = controller.analysisData(lessonList);

        // Assert
        expect(result.length, equals(2));
      });

      test('analysisData 应该处理null数据', () {
        // Act
        final result = controller.analysisData(null);

        // Assert
        expect(result, isEmpty);
      });

      test('analysisData 应该正确处理包含weekId的数据', () {
        final lessonList = [
          LessonInfo(lessonId: 1, weekId: 1, segmentId: null),
          LessonInfo(lessonId: 2, weekId: 2, segmentId: null),
        ];

        final result = controller.analysisData(lessonList);

        expect(result.length, equals(2)); // 两个week组
      });

      test('analysisData 应该正确处理包含segmentId的数据', () {
        final lessonList = [
          LessonInfo(lessonId: 1, weekId: 0, segmentId: 1),
          LessonInfo(lessonId: 2, weekId: 0, segmentId: 2),
        ];

        final result = controller.analysisData(lessonList);

        expect(result.length, equals(2)); // 两个segment组
      });

      test('analysisData 应该处理混合weekId和segmentId的数据', () {
        final lessonList = [
          LessonInfo(lessonId: 1, weekId: 1, segmentId: 1),
          LessonInfo(lessonId: 2, weekId: 0, segmentId: 1),
          LessonInfo(lessonId: 3, weekId: null, segmentId: 2),
        ];

        final result = controller.analysisData(lessonList);

        expect(result.length, equals(3)); // 1个week组 + 2个segment组
      });
    });

    group('埋点数据测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('sensorsParams 应该返回正确的埋点参数', () {
        // 设置segmentsData
        final segmentsData = SegmentsData(
          courseInfo: CourseInfoModel(
            courseKey: 'test_course',
            courseName: 'Test Course',
          ),
          classInfo: ClassInfoModel(
            classId: 123,
            classKey: 'test_class',
          ),
        );

        controller.emit(controller.state.copyWith()
          ..segmentsData = segmentsData);

        final params = controller.sensorsParams();

        expect(params['course_key'], equals('test_course'));
        expect(params['class_id'], equals(123));
        expect(params['class_key'], equals('test_class'));
      });

      test('sensorsParams 应该处理null segmentsData', () {
        final params = controller.sensorsParams();

        expect(params['course_key'], isNull);
        expect(params['class_id'], isNull);
        expect(params['class_key'], isNull);
      });
    });

    group('打印功能测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('onClickPrint 应该处理tab0的打印状态', () {
        // 设置tab0
        controller.setTabIndex(0);

        // 调用onClickPrint不应该抛出异常
        expect(() => controller.onClickPrint('test_ids'), returnsNormally);
        expect(controller.state.currTabIndex, equals(0));
      });

      test('onClickPrint 应该处理tab1 remaining状态的打印', () {
        // 设置tab1和remaining状态
        controller.setTabIndex(1);
        controller.setQuestionStatus(WrongQuestionStatus.remaining);

        expect(() => controller.onClickPrint('test_ids'), returnsNormally);
        expect(controller.state.currTabIndex, equals(1));
        expect(controller.state.wrongQuestionStatus, equals(WrongQuestionStatus.remaining));
      });

      test('onClickPrint 应该处理tab1 eliminated状态的打印', () {
        // 设置tab1和eliminated状态
        controller.setTabIndex(1);
        controller.setQuestionStatus(WrongQuestionStatus.eliminated);

        expect(() => controller.onClickPrint('test_ids'), returnsNormally);
        expect(controller.state.currTabIndex, equals(1));
        expect(controller.state.wrongQuestionStatus, equals(WrongQuestionStatus.eliminated));
      });
    });

    group('边界情况测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('当没有lessonInfo时getSelectedLessons应该返回空列表', () {
        controller.emit(controller.state.copyWith()
          ..lessonInfo = []);

        final selectedLessons = controller.getSelectedLessons();

        expect(selectedLessons, isEmpty);
      });

      test('当没有questionBookLessonInfo时getQuestionSelectedLessons应该返回空列表', () {
        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = null);

        final selectedLessons = controller.getQuestionSelectedLessons();

        expect(selectedLessons, isEmpty);
      });

      test('toggleQuestionSegment应该处理空lessons的情况', () {
        final questionBookLessonInfo = <GroupQuestionBookModal>[
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: null,
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        // Act & Assert - 不应该抛出异常
        expect(
            () => controller.toggleQuestionSegment(
                  segmentIndex: 0,
                  isChecked: true,
                ),
            returnsNormally);
      });

      test('updateQuestionSegmentChecked应该处理空lessons', () {
        final questionBookLessonInfo = <GroupQuestionBookModal>[
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: null,
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        final result = controller.updateQuestionSegmentChecked(0);
        expect(result, equals(true)); // 空lessons应该返回true
      });

      test('updateQuestionSegmentChecked应该处理空lessons列表', () {
        final questionBookLessonInfo = <GroupQuestionBookModal>[
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        final result = controller.updateQuestionSegmentChecked(0);
        expect(result, equals(true)); // 空lessons列表应该返回true
      });

      test('getQuestionSelectedLessons应该处理空lessons列表', () {
        final questionBookLessonInfo = <GroupQuestionBookModal>[
          GroupQuestionBookModal(
            unitId: '1',
            unitName: 'Unit 1',
            lessons: [],
          ),
        ];

        controller.emit(controller.state.copyWith()
          ..questionBookLessonInfo = questionBookLessonInfo);

        final selectedLessons = controller.getQuestionSelectedLessons();
        expect(selectedLessons, isEmpty);
      });
    });

    group('getSegmentId方法测试', () {
      setUp(() {
        controller = QuestionBankListCtrl(
          courseKey: 'test_course',
          classId: '123',
          classKey: '',
          mockApi: mockService,
        );
      });

      tearDown(() {
        controller.close();
      });

      test('当segmentsData为null时应该返回null', () {
        // Act
        final result = controller.getSegmentId(null);

        // Assert
        expect(result, isNull);
      });

      test('当itemList为null时应该返回null', () {
        // Arrange
        final segmentsData = SegmentsData(
          itemList: null,
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, isNull);
      });

      test('当itemList为空时应该返回null', () {
        // Arrange
        final segmentsData = SegmentsData(
          itemList: [],
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, isNull);
      });

      test('当locationInfo为null时应该返回第一个item', () {
        // Arrange
        final item1 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
        );
        final item2 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1, item2],
          locationInfo: null,
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item1));
        expect(result?.segmentInfo?.segmentId, equals(1));
      });

      test('当locationInfo.segmentId为null时应该返回第一个item', () {
        // Arrange
        final item1 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
        );
        final item2 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1, item2],
          locationInfo: LocationInfoModel(segmentId: null),
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item1));
        expect(result?.segmentInfo?.segmentId, equals(1));
      });

      test('当找到匹配的segmentId时应该返回对应的item', () {
        // Arrange
        final item1 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
        );
        final item2 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
        );
        final item3 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 3, segmentName: 'Segment 3'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1, item2, item3],
          locationInfo: LocationInfoModel(segmentId: 2),
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item2));
        expect(result?.segmentInfo?.segmentId, equals(2));
        expect(result?.segmentInfo?.segmentName, equals('Segment 2'));
      });

      test('当找不到匹配的segmentId时应该返回第一个item', () {
        // Arrange
        final item1 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 1, segmentName: 'Segment 1'),
        );
        final item2 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1, item2],
          locationInfo: LocationInfoModel(segmentId: 999), // 不存在的ID
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item1));
        expect(result?.segmentInfo?.segmentId, equals(1));
      });

      test('当itemList中的segmentInfo为null时应该正确处理', () {
        // Arrange
        final item1 = ItemModel(segmentInfo: null);
        final item2 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 2, segmentName: 'Segment 2'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1, item2],
          locationInfo: LocationInfoModel(segmentId: 2),
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item2));
        expect(result?.segmentInfo?.segmentId, equals(2));
      });

      test('当所有item的segmentInfo都为null时应该返回第一个item', () {
        // Arrange
        final item1 = ItemModel(segmentInfo: null);
        final item2 = ItemModel(segmentInfo: null);

        final segmentsData = SegmentsData(
          itemList: [item1, item2],
          locationInfo: LocationInfoModel(segmentId: 1),
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item1));
        expect(result?.segmentInfo, isNull);
      });

      test('当itemList只有一个元素时应该返回该元素', () {
        // Arrange
        final item1 = ItemModel(
          segmentInfo: SegmentInfoModel(segmentId: 5, segmentName: 'Only Segment'),
        );

        final segmentsData = SegmentsData(
          itemList: [item1],
          locationInfo: LocationInfoModel(segmentId: 999), // 不存在的ID
        );

        // Act
        final result = controller.getSegmentId(segmentsData);

        // Assert
        expect(result, equals(item1));
        expect(result?.segmentInfo?.segmentId, equals(5));
      });
    });
  });
}
