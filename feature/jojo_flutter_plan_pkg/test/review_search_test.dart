// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/review_assistant_api.dart';
import 'package:mockito/mockito.dart';

// 创建 Dio 的 Mock 类
class MockReviewSearchPageApiService extends Mock implements ReviewAssistantApi {
  final CourseLessonsSearchData searchData;

  MockReviewSearchPageApiService(this.searchData);

  @override
  Future<CourseLessonsSearchData> searchCourseLessonsInfo({required String courseKey, required String searchWord, required int classId, required int sceneId, required int pageNum, required int pageSize, RequestOptions? cancelToken}) {
    return Future.value(
        MockReviewSearchPageApiService.mockPageSearchData()
    );
  }

  static CourseLessonsSearchData mockPageSearchData() {
    Map<String, dynamic> jsonDate = {
      "pageNum": 1,
      "pageSize": 30,
      "total": 70,
      "data": [
        {
          "lessonName": "第1次 课时副标题课时副标题",
          "lessonId": 10388,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 1,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0005&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第2次 课时1副本2",
          "lessonId": 10390,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 2,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0007&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第4次 1234567！@#¥%……&*（）大大阿达啊但是迭代撒的的",
          "lessonId": 10391,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/508289744293615616/16778257180827697475bd49e20a347027530329388e5.png",
          "lessonOrder": 4,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0008&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第6次 课时1副本71-预下载",
          "lessonId": 10459,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 6,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0076&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第9次 10+10+21",
          "lessonId": 10395,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 9,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0012&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第10次 花里胡哨",
          "lessonId": 10396,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 10,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0013&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第11次 花里胡哨十大十大所多多所所多",
          "lessonId": 10397,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 11,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0014&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第12次 花里胡哨",
          "lessonId": 10398,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 12,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0015&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第13次 地图课程",
          "lessonId": 16657,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/443366858504529921/16623468965205903303b6006fd6acbcdebf87d037910.png",
          "lessonOrder": 13,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=99999_17040&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第14次 副标题哈哈哈哈哈哈",
          "lessonId": 10410,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 14,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0027&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第15次 花里胡哨啊哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈",
          "lessonId": 10409,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 15,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0026&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第16次 花里胡哨",
          "lessonId": 10408,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 16,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0025&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第17次 type23环节外",
          "lessonId": 10407,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 17,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0024&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第18次 花里胡哨",
          "lessonId": 10406,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 18,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0023&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第19次 花里胡哨23232323 4GFGDF GDf规范发个个好就",
          "lessonId": 10405,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 19,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0022&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第21次 花里胡哨",
          "lessonId": 10403,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 21,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0020&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第30次 课时1副本84",
          "lessonId": 10472,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 30,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0089&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第31次 课时1副本93课时1副本93课时1副本93课时1副本93课时",
          "lessonId": 10481,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 31,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0098&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第33次 课时1副本50",
          "lessonId": 10438,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 33,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0055&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第34次 课时1副本49",
          "lessonId": 10437,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 34,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0054&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第41次 花里胡哨",
          "lessonId": 10429,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 41,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0046&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第47次 课时1副本95副本7",
          "lessonId": 16428,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/386153285386937344/1648706117073a1fa4dbb171a0e7876e0ecadffefad0b.jpg",
          "lessonOrder": 47,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0182&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第51次 花里胡哨",
          "lessonId": 16421,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/504654327174420482/167695896864845a56e796ceef1fbf4a6c637cb4b5ab3.png",
          "lessonOrder": 51,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0175&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第61次 花里胡哨",
          "lessonId": 16440,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 61,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0194&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第71次 zy滚动排期测试副本4",
          "lessonId": 11748,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/375702363347231744/16462144673271daf9c39806ca426cd65151747932f6c.png",
          "lessonOrder": 71,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31292_1316&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第74次 zy滚动排期测试副本14",
          "lessonId": 11755,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/375702363347231744/16462144673271daf9c39806ca426cd65151747932f6c.png",
          "lessonOrder": 74,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31292_1323&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第77次 zy滚动排期测试副本13",
          "lessonId": 11754,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/375702363347231744/16462144673271daf9c39806ca426cd65151747932f6c.png",
          "lessonOrder": 77,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31292_1322&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第79次 zy滚动排期测试副本21",
          "lessonId": 11762,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/375702363347231744/16462144673271daf9c39806ca426cd65151747932f6c.png",
          "lessonOrder": 79,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31292_1330&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第80次 zy滚动排期测试副本17",
          "lessonId": 11758,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/375702363347231744/16462144673271daf9c39806ca426cd65151747932f6c.png",
          "lessonOrder": 80,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31292_1326&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        },
        {
          "lessonName": "第81次 花里胡哨11",
          "lessonId": 10448,
          "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
          "lessonOrder": 81,
          "studyStatus": 1,
          "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
          "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
          "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0065&loadingScene=5&engineType=0",
          "finish": false,
          "unlock": true,
          "makeup": true
        }
      ]
    };
    CourseLessonsSearchData test = CourseLessonsSearchData.fromJson(jsonDate);
    return test;
  }
}

void main() {
  late ReviewSearchCtrl controller;
  late MockReviewSearchPageApiService mockApiService;
  setUp(() {
    mockApiService =
        MockReviewSearchPageApiService(MockReviewSearchPageApiService.mockPageSearchData()!);
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
    controller = ReviewSearchCtrl(
        subjectColor: "",
        courseKey: "",
        classId: "",
        buriedString: "",
        api: mockApiService);
    controller.inputTxt = "搜索关键字";
  });

  group('plan_review_search_controller tests:', ()
  {
    test('search history save', () async {
      WidgetsFlutterBinding.ensureInitialized();

      // 验证历史搜索内容是否保存成功
      for (int i = 0; i < 12; i++) {
        controller.inputTxt = "搜索$i";
        await controller.saveHistoryData();
      }
      // 最大保存十条本地记录
      expect(controller.historyList.length == 10, true);
      String? firstText = controller.historyList.first;
      // 最新一条数据是否是最后保存的数据
      expect(firstText == "搜索11", true);
    });

    test('search history get', () async {
      WidgetsFlutterBinding.ensureInitialized();
      await controller.getHistoryData();
      // 验证本地标记是否获取成功
      expect(controller.historyList.isNotEmpty, true);
    });

    test('search history delete', () async {
      WidgetsFlutterBinding.ensureInitialized();
      await controller.deleteHandle();
      // 验证本地标记是否删除成功
      expect(controller.historyList.isEmpty, true);
    });

    test('search api request test', () async {
      controller.searchHandle();
      // 验证本地标记是否删除成功
      expect(controller.historyList.isEmpty, true);
    });

    test('search page searchHandle', () async {
      controller.isRequesting = false;
      controller.inputTxt = "111";
      await controller.searchHandle();
      // 验证是否请求到了数据
      expect(controller.dataList.isNotEmpty, true);
      // 验证是否得到了上一次的搜索结果
      expect(controller.preInputTxt.isNotEmpty, true);
    });

    test('search page refreshData', () async {
      controller.preInputTxt = "111";
      await controller.refreshData();
      // 验证是否请求到了数据
      expect(controller.dataList.isNotEmpty, true);
      // 验证是否重置了搜索码
      expect(controller.currentPageIndex == 1, true);
    });

    test('search page loadMoreData', () async {
      controller.preInputTxt = "111";
      await controller.refreshData();
      // 验证是否请求到了数据
      expect(controller.dataList.isNotEmpty, true);
      // 验证是否得到了上一次的搜索结果
      expect(controller.preInputTxt.isNotEmpty, true);
    });

    test('search page transformList test', () async {
      List<CourseCard> list = controller.transformList(mockApiService.searchData.data);
      // 验证搜索数据对象是否转化成功
      expect(list.isNotEmpty, true);
    });

    test('search refresh', () async {
      // 页面刷新
      controller.refresh();
    });
  });
}
