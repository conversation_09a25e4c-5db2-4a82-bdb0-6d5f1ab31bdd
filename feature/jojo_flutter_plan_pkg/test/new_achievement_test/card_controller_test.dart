import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievement_detail_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievements_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/achievements_api.dart';


class AchievementsApiErrorMock implements AchievementsApi {
  @override
  Future<AchievementsModel> getSubjectMedals(int subjectType, int? partnerId) async {
    String jsonString = """
   """;
    return AchievementsModel.fromJson(json.decode(jsonString));
  }

  @override
  Future<AchievementDetailModel> getMedalDetail(int medalId) async {
    String jsonString = """
    {
    "onlyTrainingCamp": 0,
    "positionImg": "https://jojopublicfat.jojoread.com/111",
    "commonRes": "https://jojopublicfat.jojoread.com/222",
    "title": "我的成就",
    "latestMedals": [
      
    ],
    "segmentMedals": [
      
    ]
  }
    """;
    return AchievementDetailModel.fromJson(json.decode(jsonString));
  }
}

void main() {
  late MyAchievementsController controller;
  late MyAchievementsController errorController;

  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());

    controller = MyAchievementsController(subjectType: 2, achievementsApi: AchievementsApiMock());
    errorController = MyAchievementsController(subjectType: 2, achievementsApi: AchievementsApiErrorMock());
  });

  test('update refreshData', () async {
    try {
      await controller.refreshData();
    } catch (e) {
      expect(e, isNull);
    }

    try {
      await errorController.refreshData();
    } catch (e) {
      expect(e, isNotNull);
    }
  });
}