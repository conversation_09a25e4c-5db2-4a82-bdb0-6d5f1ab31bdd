import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievement_detail_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/card_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:jojo_flutter_plan_pkg/service/achievements_api.dart';

void main() {
  late CardController controller;

  setUp(() async {
    final AchievementDetailModel model = await AchievementsApiMock().getMedalDetail(0);
    AchievementCard card = AchievementCard(0, "", model.medalGroup?.groupMedals?.first, null);

    controller = CardController(card, "");
  });

  test('update updateState', () async {
    try {
      await controller.updateState();
    } catch (e) {
      expect(e, isNull);
    }
  });
}