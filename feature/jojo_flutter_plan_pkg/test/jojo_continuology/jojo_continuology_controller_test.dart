import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/calendar_utils.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/util/study_guide_audio_impl.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/util/countdown_timer_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_api_data.dart';

@GenerateMocks([StudyGuideAudioImpl, CountdownTimerHelper])
import 'jojo_continuology_controller_test.mocks.dart';

void main() {
  group('JoJoContinuologyController Audio and Countdown Tests', () {
    late JoJoContinuologyController controller;
    late MockStudyGuideAudioImpl mockStudyGuideAudioImpl;
    late MockCountdownTimerHelper mockCountdownTimerHelper;

    setUp(() {
      mockStudyGuideAudioImpl = MockStudyGuideAudioImpl();
      controller = JoJoContinuologyController(mockStudyGuideAudioImpl);
      mockCountdownTimerHelper = MockCountdownTimerHelper();
      // 替换控制器中的倒计时器为mock对象
      controller.guideGountDown = mockCountdownTimerHelper;
    });

    tearDown(() {
      controller.close();
    });

    group('playStudyGuideAudio tests', () {
      test('should play audio when valid URL is found', () {
        // Arrange
        const scene = 1;
        final resourceList = [
          GuideResource(scene: 1, audio: 'https://example.com/audio.mp3')
        ];
        const expectedUrl = 'https://example.com/audio.mp3';
        
        when(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).thenReturn(expectedUrl);

        // Act
        controller.playStudyGuideAudio(
          scene: scene,
          resourceList: resourceList,
        );

        // Assert
        verify(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).called(1);
        verify(mockStudyGuideAudioImpl.playAudioWithUrl(expectedUrl)).called(1);
      });

      test('should not play audio when URL is empty', () {
        // Arrange
        const scene = 1;
        final resourceList = <GuideResource>[];
        
        when(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).thenReturn('');

        // Act
        controller.playStudyGuideAudio(
          scene: scene,
          resourceList: resourceList,
        );

        // Assert
        verify(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).called(1);
        verifyNever(mockStudyGuideAudioImpl.playAudioWithUrl(any));
      });

      test('should handle multiple resources and find correct scene', () {
        // Arrange
        const scene = 2;
        final resourceList = [
          GuideResource(scene: 1, audio: 'https://example.com/audio1.mp3'),
          GuideResource(scene: 2, audio: 'https://example.com/audio2.mp3'),
          GuideResource(scene: 3, audio: 'https://example.com/audio3.mp3'),
        ];
        const expectedUrl = 'https://example.com/audio2.mp3';
        
        when(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).thenReturn(expectedUrl);

        // Act
        controller.playStudyGuideAudio(
          scene: scene,
          resourceList: resourceList,
        );

        // Assert
        verify(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).called(1);
        verify(mockStudyGuideAudioImpl.playAudioWithUrl(expectedUrl)).called(1);
      });
    });

    group('stopStudyGuideAudio tests', () {
      test('should call stopAudio on studyGuideAudioImpl', () {
        // Act
        controller.stopStudyGuideAudio();

        // Assert
        verify(mockStudyGuideAudioImpl.stopAudio()).called(1);
      });
    });

    group('disposeGuideAudio tests', () {
      test('should call dispose on studyGuideAudioImpl', () {
        // Act
        controller.disposeGuideAudio();

        // Assert
        verify(mockStudyGuideAudioImpl.dispose()).called(1);
      });
    });

    group('startGuideCountDown tests', () {
      test('should start countdown with 8 seconds', () {
        // Act
        controller.startGuideCountDown();

        // Assert
        verify(mockCountdownTimerHelper.startCountdown(8)).called(1);
      });
    });

    group('studyGuideCountDownEnd tests', () {
      test('should emit state with canGuideShow set to true', () {
        // Arrange
        final initialState = controller.state;
        expect(initialState.canGuideShow, isFalse);

        // Act
        controller.studyGuideCountDownEnd();

        // Assert
        expect(controller.state.canGuideShow, isTrue);
      });

      test('should preserve other state properties when updating canGuideShow', () {
        // Arrange
        final initialState = controller.state;
        
        // Act
        controller.studyGuideCountDownEnd();
        
        // Assert
        final newState = controller.state;
        expect(newState.canGuideShow, isTrue);
        // 验证其他属性保持不变
        expect(newState.data, equals(initialState.data));
        expect(newState.pageStatus, equals(initialState.pageStatus));
      });
    });

    group('tickGuideCountDown tests', () {
      test('should log countdown seconds', () {
        // Arrange
        const seconds = 5;
        
        // Act
        controller.tickGuideCountDown(seconds);
        
        // Assert
        // 这个方法主要是日志记录，我们可以验证它不会抛出异常
        expect(() => controller.tickGuideCountDown(seconds), returnsNormally);
      });

      test('should handle zero seconds', () {
        // Act & Assert
        expect(() => controller.tickGuideCountDown(0), returnsNormally);
      });

      test('should handle negative seconds', () {
        // Act & Assert
        expect(() => controller.tickGuideCountDown(-1), returnsNormally);
      });
    });

    group('disposeGuideCountDown tests', () {
      test('should call dispose on guideGountDown', () {
        // Act
        controller.disposeGuideCountDown();

        // Assert
        verify(mockCountdownTimerHelper.dispose()).called(1);
      });
    });

    group('restartGuideCountDown tests', () {
      test('should emit state with canGuideShow set to false', () {
        // Arrange
        // 先设置为true
        controller.studyGuideCountDownEnd();
        expect(controller.state.canGuideShow, isTrue);

        // Act
        controller.restartGuideCountDown();

        // Assert
        expect(controller.state.canGuideShow, isFalse);
      });

      test('should preserve other state properties when restarting countdown', () {
        // Arrange
        controller.studyGuideCountDownEnd();
        final stateBeforeRestart = controller.state;
        
        // Act
        controller.restartGuideCountDown();
        
        // Assert
        final newState = controller.state;
        expect(newState.canGuideShow, isFalse);
        // 验证其他属性保持不变
        expect(newState.data, equals(stateBeforeRestart.data));
        expect(newState.pageStatus, equals(stateBeforeRestart.pageStatus));
      });
    });

    group('Integration tests', () {
      test('should handle complete audio guide flow', () {
        // Arrange
        const scene = 1;
        final resourceList = [
          GuideResource(scene: 1, audio: 'https://example.com/audio.mp3')
        ];
        const expectedUrl = 'https://example.com/audio.mp3';
        
        when(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).thenReturn(expectedUrl);

        // Act - 完整的引导流程
        controller.playStudyGuideAudio(scene: scene, resourceList: resourceList);
        controller.startGuideCountDown();
        controller.studyGuideCountDownEnd();
        controller.stopStudyGuideAudio();
        controller.disposeGuideAudio();
        controller.disposeGuideCountDown();

        // Assert
        verify(mockStudyGuideAudioImpl.findPlayAudioUrl(
          scene: scene,
          guideResourceList: resourceList,
        )).called(1);
        verify(mockStudyGuideAudioImpl.playAudioWithUrl(expectedUrl)).called(1);
        verify(mockCountdownTimerHelper.startCountdown(8)).called(1);
        verify(mockStudyGuideAudioImpl.stopAudio()).called(1);
        verify(mockStudyGuideAudioImpl.dispose()).called(1);
        verify(mockCountdownTimerHelper.dispose()).called(1);
        expect(controller.state.canGuideShow, isTrue);
      });

      test('should handle restart countdown flow', () {
        // Act
        controller.startGuideCountDown();
        controller.studyGuideCountDownEnd();
        expect(controller.state.canGuideShow, isTrue);
        
        controller.restartGuideCountDown();
        expect(controller.state.canGuideShow, isFalse);
        
        controller.startGuideCountDown();
        controller.studyGuideCountDownEnd();
        expect(controller.state.canGuideShow, isTrue);

        // Assert
        verify(mockCountdownTimerHelper.startCountdown(8)).called(2);
      });
    });
    group('getAnimaStatus tests', () {
      test('should return null when no matching month is found', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 5, 1);
        calendarMonthVo.dayItemList = [];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, isNull);
      });

      test('should return unLight when unLight status is found', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 6, 1);
        
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.finish;
        dayItem1.dateTime = DateTime(2023, 6, 1);
        
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.unLight;
        dayItem2.dateTime = DateTime(2023, 6, 2);
        
        final dayItem3 = CalendarDayItemVo();
        dayItem3.status = CalendarStudyStatus.freezed;
        dayItem3.dateTime = DateTime(2023, 6, 3);
        
        calendarMonthVo.dayItemList = [dayItem1, dayItem2, dayItem3];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, equals(CalendarStudyStatus.unLight));
      });

      test('should return freezed when only freezed status is found', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 6, 1);
        
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.finish;
        dayItem1.dateTime = DateTime(2023, 6, 1);
        
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.freezed;
        dayItem2.dateTime = DateTime(2023, 6, 2);
        
        final dayItem3 = CalendarDayItemVo();
        dayItem3.status = CalendarStudyStatus.rest;
        dayItem3.dateTime = DateTime(2023, 6, 3);
        
        calendarMonthVo.dayItemList = [dayItem1, dayItem2, dayItem3];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, equals(CalendarStudyStatus.freezed));
      });

      test('should return null when neither unLight nor freezed status is found', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 6, 1);
        
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.finish;
        dayItem1.dateTime = DateTime(2023, 6, 1);
        
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.rest;
        dayItem2.dateTime = DateTime(2023, 6, 2);
        
        final dayItem3 = CalendarDayItemVo();
        dayItem3.status = CalendarStudyStatus.lock;
        dayItem3.dateTime = DateTime(2023, 6, 3);
        
        calendarMonthVo.dayItemList = [dayItem1, dayItem2, dayItem3];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, isNull);
      });

      test('should return unLight when both unLight and freezed exist (unLight has priority)', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 6, 1);
        
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.freezed;
        dayItem1.dateTime = DateTime(2023, 6, 1);
        
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.unLight;
        dayItem2.dateTime = DateTime(2023, 6, 2);
        
        final dayItem3 = CalendarDayItemVo();
        dayItem3.status = CalendarStudyStatus.finish;
        dayItem3.dateTime = DateTime(2023, 6, 3);
        
        calendarMonthVo.dayItemList = [dayItem1, dayItem2, dayItem3];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, equals(CalendarStudyStatus.unLight));
      });

      test('should handle empty dayItemList', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        final calendarMonthVo = CalendarMonthVo();
        calendarMonthVo.dateTime = DateTime(2023, 6, 1);
        calendarMonthVo.dayItemList = [];
        calendarVo.monthList = [calendarMonthVo];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, isNull);
      });

      test('should handle empty monthList', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        calendarVo.monthList = [];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, isNull);
      });

      test('should match correct month and year', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        
        final calendarMonthVo1 = CalendarMonthVo();
        calendarMonthVo1.dateTime = DateTime(2023, 5, 1);
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.unLight;
        dayItem1.dateTime = DateTime(2023, 5, 1);
        calendarMonthVo1.dayItemList = [dayItem1];
        
        final calendarMonthVo2 = CalendarMonthVo();
        calendarMonthVo2.dateTime = DateTime(2023, 6, 1);
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.freezed;
        dayItem2.dateTime = DateTime(2023, 6, 1);
        calendarMonthVo2.dayItemList = [dayItem2];
        
        final calendarMonthVo3 = CalendarMonthVo();
        calendarMonthVo3.dateTime = DateTime(2024, 6, 1);
        final dayItem3 = CalendarDayItemVo();
        dayItem3.status = CalendarStudyStatus.unLight;
        dayItem3.dateTime = DateTime(2024, 6, 1);
        calendarMonthVo3.dayItemList = [dayItem3];
        
        calendarVo.monthList = [calendarMonthVo1, calendarMonthVo2, calendarMonthVo3];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, equals(CalendarStudyStatus.freezed));
      });

      test('should handle multiple months with same year but different month', () {
        // Arrange
        final calendarVo = CalendarContentVo();
        
        final calendarMonthVo1 = CalendarMonthVo();
        calendarMonthVo1.dateTime = DateTime(2023, 5, 1);
        final dayItem1 = CalendarDayItemVo();
        dayItem1.status = CalendarStudyStatus.unLight;
        dayItem1.dateTime = DateTime(2023, 5, 1);
        calendarMonthVo1.dayItemList = [dayItem1];
        
        final calendarMonthVo2 = CalendarMonthVo();
        calendarMonthVo2.dateTime = DateTime(2023, 7, 1);
        final dayItem2 = CalendarDayItemVo();
        dayItem2.status = CalendarStudyStatus.freezed;
        dayItem2.dateTime = DateTime(2023, 7, 1);
        calendarMonthVo2.dayItemList = [dayItem2];
        
        calendarVo.monthList = [calendarMonthVo1, calendarMonthVo2];
        final currentTime = DateTime(2023, 6, 15);

        // Act
        final result = controller.getAnimaStatus(calendarVo, currentTime);

        // Assert
        expect(result, isNull);
      });
    });
  });
}