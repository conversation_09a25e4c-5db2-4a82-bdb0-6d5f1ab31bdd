import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_lesson_page_api.dart';
import 'package:mockito/mockito.dart';

/// Mock API 服务
class MockHomeMapLessonPageApi extends Mock implements HomeMapLessonPageApi {}

/// Mock 全局下载管理器
class MockGlobalDownloadManager {
  final bool shouldSucceed;
  final Map<String, String>? mockResult;
  final String? errorMessage;

  MockGlobalDownloadManager({
    this.shouldSucceed = true,
    this.mockResult,
    this.errorMessage,
  });

  Future<Map<String, String>> downloadResources(
    List<String> urlList, {
    bool needUnzip = false,
  }) async {
    // 模拟异步下载过程
    await Future.delayed(const Duration(milliseconds: 10));

    if (shouldSucceed) {
      // 返回模拟的成功结果
      if (mockResult != null) {
        return mockResult!;
      } else {
        // 生成默认的成功结果
        Map<String, String> result = {};
        for (String url in urlList) {
          String fileName = url.split('/').last.replaceAll('.zip', '');
          if (needUnzip) {
            result[url] = "/mock/unzipped/path/$fileName/";
          } else {
            result[url] = "/mock/download/path/$fileName.zip";
          }
        }
        return result;
      }
    } else {
      throw Exception(errorMessage ?? "下载失败");
    }
  }
}

/// 可测试的 Controller
class TestablePlanHomeLessonCtrl extends PlanHomeLessonCtrl {
  MockGlobalDownloadManager? mockDownloadManager;

  TestablePlanHomeLessonCtrl({
    HomeMapLessonPageApi? api,
    bool needShowCourseHeader = true,
    bool needShowTodayGuide = true,
    this.mockDownloadManager,
  }) : super(
          api: api,
          needShowCourseHeader: needShowCourseHeader,
          needShowTodayGuide: needShowTodayGuide,
        );

  /// 重写 _downloadZipRes 方法以使用 Mock 下载管理器
  @override
  Future<void> _downloadZipRes(
    List<String> urlList, {
    Function(Map<String, String>)? successListener,
  }) async {
    try {
      if (mockDownloadManager != null) {
        Map<String, String> result = await mockDownloadManager!.downloadResources(
          urlList,
          needUnzip: true,
        );
        successListener?.call(result);
      } else {
        // 调用原有方法
        await super._downloadZipRes(urlList, successListener: successListener);
      }
    } catch (e) {
      // 保持原有的错误处理逻辑
      rethrow;
    }
  }
}

void main() {
  group('_downloadZipRes Success Tests', () {
    late TestablePlanHomeLessonCtrl controller;
    late MockHomeMapLessonPageApi mockApi;

    setUp(() {
      mockApi = MockHomeMapLessonPageApi();
      controller = TestablePlanHomeLessonCtrl(
        api: mockApi,
        needShowCourseHeader: true,
        needShowTodayGuide: true,
      );
    });

    test('_downloadZipRes should call successListener with correct data when download succeeds', () async {
      // 准备测试数据
      List<String> testUrls = [
        "https://example.com/animation1.zip",
        "https://example.com/animation2.zip",
      ];

      Map<String, String> expectedResult = {
        "https://example.com/animation1.zip": "/mock/unzipped/path/animation1/",
        "https://example.com/animation2.zip": "/mock/unzipped/path/animation2/",
      };

      // 设置成功的下载管理器
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
        mockResult: expectedResult,
      );

      // 验证回调
      bool successCalled = false;
      Map<String, String>? receivedResult;

      // 调用 _downloadZipRes
      await controller._downloadZipRes(
        testUrls,
        successListener: (result) {
          successCalled = true;
          receivedResult = result;
        },
      );

      // 验证结果
      expect(successCalled, true);
      expect(receivedResult, isNotNull);
      expect(receivedResult, equals(expectedResult));
      expect(receivedResult!.length, equals(2));
      expect(receivedResult!.containsKey("https://example.com/animation1.zip"), true);
      expect(receivedResult!.containsKey("https://example.com/animation2.zip"), true);
    });

    test('_downloadZipRes should handle single URL download successfully', () async {
      // 测试单个URL的情况
      List<String> singleUrl = ["https://example.com/single_animation.zip"];
      
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
      );

      bool successCalled = false;
      Map<String, String>? receivedResult;

      await controller._downloadZipRes(
        singleUrl,
        successListener: (result) {
          successCalled = true;
          receivedResult = result;
        },
      );

      expect(successCalled, true);
      expect(receivedResult, isNotNull);
      expect(receivedResult!.length, equals(1));
      expect(receivedResult!.containsKey(singleUrl.first), true);
      expect(receivedResult![singleUrl.first], contains("single_animation"));
    });

    test('_downloadZipRes should handle empty URL list', () async {
      // 测试空URL列表
      List<String> emptyUrls = [];
      
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
        mockResult: {},
      );

      bool successCalled = false;
      Map<String, String>? receivedResult;

      await controller._downloadZipRes(
        emptyUrls,
        successListener: (result) {
          successCalled = true;
          receivedResult = result;
        },
      );

      expect(successCalled, true);
      expect(receivedResult, isNotNull);
      expect(receivedResult!.isEmpty, true);
    });

    test('_downloadZipRes should work without successListener', () async {
      // 测试不传 successListener 的情况
      List<String> testUrls = ["https://example.com/test.zip"];
      
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
      );

      // 不应该抛出异常
      expect(() async {
        await controller._downloadZipRes(testUrls);
      }, returnsNormally);
    });

    test('_downloadZipRes should pass needUnzip=true to downloadManager', () async {
      // 验证 needUnzip 参数被正确传递
      List<String> testUrls = ["https://example.com/test.zip"];
      
      bool needUnzipCalled = false;
      
      // 创建一个特殊的 Mock 来验证参数
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
      );

      // 重写方法来验证参数
      controller._downloadZipRes = (List<String> urlList, {Function(Map<String, String>)? successListener}) async {
        try {
          Map<String, String> result = await controller.mockDownloadManager!.downloadResources(
            urlList,
            needUnzip: true, // 验证这个参数
          );
          needUnzipCalled = true;
          successListener?.call(result);
        } catch (e) {
          rethrow;
        }
      };

      await controller._downloadZipRes(testUrls, successListener: (result) {});

      expect(needUnzipCalled, true);
    });

    test('_downloadZipRes should handle large URL list', () async {
      // 测试大量URL的情况
      List<String> largeUrlList = List.generate(
        10,
        (index) => "https://example.com/animation_$index.zip",
      );
      
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
      );

      bool successCalled = false;
      Map<String, String>? receivedResult;

      await controller._downloadZipRes(
        largeUrlList,
        successListener: (result) {
          successCalled = true;
          receivedResult = result;
        },
      );

      expect(successCalled, true);
      expect(receivedResult, isNotNull);
      expect(receivedResult!.length, equals(10));
      
      // 验证所有URL都有对应的结果
      for (String url in largeUrlList) {
        expect(receivedResult!.containsKey(url), true);
      }
    });

    test('_downloadZipRes should handle special characters in URLs', () async {
      // 测试包含特殊字符的URL
      List<String> specialUrls = [
        "https://example.com/动画_资源.zip",
        "https://example.com/animation%20with%20spaces.zip",
        "https://example.com/animation-with-dashes.zip",
      ];
      
      controller.mockDownloadManager = MockGlobalDownloadManager(
        shouldSucceed: true,
      );

      bool successCalled = false;
      Map<String, String>? receivedResult;

      await controller._downloadZipRes(
        specialUrls,
        successListener: (result) {
          successCalled = true;
          receivedResult = result;
        },
      );

      expect(successCalled, true);
      expect(receivedResult, isNotNull);
      expect(receivedResult!.length, equals(3));
    });
  });
}

/// 运行测试命令：
/// flutter test test/download_zip_res_test.dart
/// 
/// 运行特定测试：
/// flutter test test/download_zip_res_test.dart --name "should call successListener with correct data"
/// flutter test test/download_zip_res_test.dart --name "should handle single URL"
