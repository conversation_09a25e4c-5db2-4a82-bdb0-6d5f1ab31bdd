import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/after_class_service_Controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_ads_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_page_api.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'plan_home_map_test/plan_home_map_test.dart';

@GenerateMocks([HomeMapPageApi])
void main() {
  late AfterSchoolListCtrl controller;
  late MockHomeMapPageApi mockApi;

  setUp(() {
    mockApi = MockHomeMapPageApi();
  });

  group('getAdsInfoKey', () {
    test('should return correct ad key for valid subject types', () {
      final controller = AfterSchoolListCtrl(api: null, subjectType: 1);

      expect(controller.getAdsInfoKey(1), equals("SW_service_AD"));
      expect(controller.getAdsInfoKey(2), equals("YD_service_AD"));
      expect(controller.getAdsInfoKey(3), equals("CZ_service_AD"));
      expect(controller.getAdsInfoKey(4), equals("MY_service_AD"));
      expect(controller.getAdsInfoKey(6), equals("YY_service_AD"));
      expect(controller.getAdsInfoKey(13), equals("YY_service_AD"));
    });

    test('should return empty string for invalid subject types', () {
      final controller = AfterSchoolListCtrl(api: null, subjectType: 1);

      expect(controller.getAdsInfoKey(0), isEmpty);
      expect(controller.getAdsInfoKey(5), isEmpty);
      expect(controller.getAdsInfoKey(null), isEmpty);
    });
  });

  group('getPageDateInfo', () {
    test('should update state with data when both requests succeed', () async {
      // Arrange
      const subjectType = 1;
      final mockServersData = CourseAfterServersData();
      final mockAdInfo = CourseAfterAdsInfoData();

      when(mockApi.requestAfterCourseServers(subjectType, null))
          .thenAnswer((_) async => mockServersData);
      when(mockApi.requestAfterCourseServersAdvertisements(
              key: "SW_service_AD", queryType: 'KEY'))
          .thenAnswer((_) async => mockAdInfo);

      controller = AfterSchoolListCtrl(api: mockApi, subjectType: subjectType);

      // Act
      await controller.getPageDateInfo();

      // Assert
      expect(controller.state.pageStatus, equals(PageStatus.success));
      expect(controller.state.afterSchoolListBean, equals(mockServersData));
      expect(controller.state.adInfo, equals(mockAdInfo));
    });

    test('should update state with error when requestAfterCourseServers fails',
        () async {
      // Arrange
      const subjectType = 1;
      final exception = Exception('Failed to load data');

      when(mockApi.requestAfterCourseServers(subjectType, null))
          .thenThrow(exception);

      controller = AfterSchoolListCtrl(api: mockApi, subjectType: subjectType);

      // Act
      await controller.getPageDateInfo();

      // Assert
      expect(controller.state.pageStatus, equals(PageStatus.error));
      expect(controller.state.exception, equals(exception));
      expect(controller.state.adInfo, isNull);
    });

    test('should not request ads when getAdsInfoKey returns empty', () async {
      // Arrange
      const subjectType = 0; // Invalid type
      final mockServersData = CourseAfterServersData();

      when(mockApi.requestAfterCourseServers(subjectType, null))
          .thenAnswer((_) async => mockServersData);

      controller = AfterSchoolListCtrl(api: mockApi, subjectType: subjectType);

      // Act
      await controller.getPageDateInfo();

      // Assert
      verifyNever(mockApi.requestAfterCourseServersAdvertisements(
        key: anyNamed('key'),
        queryType: anyNamed('queryType') ?? '',
      ));
      expect(controller.state.adInfo, isNull);
    });

    test(
        'should still update state when requestAfterCourseServers succeeds but ads request fails',
        () async {
      // Arrange
      const subjectType = 1;
      final mockServersData = CourseAfterServersData();
      final exception = Exception('Failed to load ads');

      when(mockApi.requestAfterCourseServers(subjectType, null))
          .thenAnswer((_) async => mockServersData);
      when(mockApi.requestAfterCourseServersAdvertisements(
              key: "SW_service_AD", queryType: 'KEY'))
          .thenThrow(exception);

      controller = AfterSchoolListCtrl(api: mockApi, subjectType: subjectType);

      // Act
      await controller.getPageDateInfo();

      // Assert
      expect(controller.state.pageStatus, equals(PageStatus.success));
      expect(controller.state.afterSchoolListBean, equals(mockServersData));
      expect(controller.state.adInfo, isNull);
    });
  });

  group('refreshDate', () {
    test('should reset state to loading and call getPageDateInfo', () async {
      // Arrange
      const subjectType = 1;
      final mockServersData = CourseAfterServersData();

      when(mockApi.requestAfterCourseServers(subjectType, null))
          .thenAnswer((_) async => mockServersData);

      controller = AfterSchoolListCtrl(api: mockApi, subjectType: subjectType);

      // Act
      controller.refreshDate();

      // Assert
      expect(controller.state.pageStatus, equals(PageStatus.loading));
      await untilCalled(mockApi.requestAfterCourseServers(subjectType, null));
      verify(mockApi.requestAfterCourseServers(subjectType, null)).called(1);
    });
  });
}
