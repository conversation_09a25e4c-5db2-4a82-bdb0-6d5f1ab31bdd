import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_utils.dart';

void main() {
  // 判断是否是同一天
  group('courseUtils tests:', () {
    test('小于24小时显示剩余hh:mm', () {
      final result = CourseUtils.testFormatRemainingTime(3600 * 1000);
      expect(result, '剩余1:00');
    });

    test('刚好24小时显示剩余0:00', () {
      final result = CourseUtils.testFormatRemainingTime(86400 * 1000 * 100);
      expect(result, '剩余99天24小时');
    });

    test('超过24小时显示剩余dd天hh小时', () {
      final result = CourseUtils.testFormatRemainingTime(90060 * 1000);
      expect(result, '剩余1天1小时');
    });

    test('跨两天的情况', () {
      final result = CourseUtils.testFormatRemainingTime(176400 * 1000);
      expect(result, '剩余2天1小时');
    });
  });
}
