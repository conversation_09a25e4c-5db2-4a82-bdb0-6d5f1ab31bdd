import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/converter/study_buddy_converter.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';

void main() {
  group('StudyBuddyConverter', () {
    late StudyBuddyConverter converter;

    setUp(() {
      converter = StudyBuddyConverter();
    });

    group('convertToItemStates', () {
      test('should convert empty list correctly', () {
        // Arrange
        final List<StudyBuddyModel> models = [];

        // Act
        final result = converter.convertToItemStates(models);

        // Assert
        expect(result, isEmpty);
      });

      test('should convert single model correctly', () {
        // Arrange
        final model = StudyBuddyModel(
          nickName: '测试用户',
          studyDays: 30,
          continuousDays: 15,
          medal: 5,
          like: 100,
          img: 'https://example.com/avatar.jpg',
          url: 'https://example.com/profile',
          canLike: 1,
          targetId: 123,
        );
        final models = [model];

        // Act
        final result = converter.convertToItemStates(models);

        // Assert
        expect(result, hasLength(1));
        final uiState = result.first;
        expect(uiState.name, equals('测试用户'));
        expect(uiState.studyAllCount, equals(30));
        expect(uiState.studyCount, equals(15));
        expect(uiState.achievementCount, equals(5));
        expect(uiState.kudosCount, equals(100));
        expect(uiState.personalImage, equals('https://example.com/avatar.jpg'));
        expect(uiState.router, equals('https://example.com/profile'));
        expect(uiState.canLike, equals(1));
        expect(uiState.targetId, equals(123));
      });

      test('should handle null values with defaults', () {
        // Arrange
        final model = StudyBuddyModel();
        final models = [model];

        // Act
        final result = converter.convertToItemStates(models);

        // Assert
        expect(result, hasLength(1));
        final uiState = result.first;
        expect(uiState.name, equals(''));
        expect(uiState.studyAllCount, equals(0));
        expect(uiState.studyCount, equals(0));
        expect(uiState.achievementCount, equals(0));
        expect(uiState.kudosCount, equals(1)); // 默认值为1
        expect(uiState.personalImage, equals(''));
        expect(uiState.router, equals(''));
        expect(uiState.canLike, equals(0));
        expect(uiState.targetId, equals(0));
      });

      test('should convert multiple models correctly', () {
        // Arrange
        final models =  [
          StudyBuddyModel(
            nickName: '测试用户 1',
            studyDays: 10,
            continuousDays: 5,
            medal: 2,
            like: 50,
            targetId: 1,
          ),
          StudyBuddyModel(
            nickName: '测试用户 2',
            studyDays: 20,
            continuousDays: 10,
            medal: 3,
            like: 75,
            targetId: 2,
          ),
        ];

        // Act
        final result = converter.convertToItemStates(models);

        // Assert
        expect(result, hasLength(2));
        expect(result[0].name, equals('测试用户 1'));
        expect(result[0].studyAllCount, equals(10));
        expect(result[0].targetId, equals(1));
        expect(result[1].name, equals('测试用户 2'));
        expect(result[1].studyAllCount, equals(20));
        expect(result[1].targetId, equals(2));
      });
    });
  });
}