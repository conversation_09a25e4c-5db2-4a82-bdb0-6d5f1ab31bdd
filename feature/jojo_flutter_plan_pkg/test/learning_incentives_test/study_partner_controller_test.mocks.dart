// Mocks generated by Mockito 5.4.0 from annotations
// in jojo_flutter_plan_pkg/test/learning_incentives_test/study_partner_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart'
    as _i3;
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/service/study_buddy_sevice.dart'
    as _i4;
import 'package:jojo_flutter_plan_pkg/service/learning_incentives_api.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLearningIncentivesApi_0 extends _i1.SmartFake
    implements _i2.LearningIncentivesApi {
  _FakeLearningIncentivesApi_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStudyBuddy_1 extends _i1.SmartFake implements _i3.StudyBuddy {
  _FakeStudyBuddy_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [StudyBuddySevice].
///
/// See the documentation for Mockito's code generation for more information.
class MockStudyBuddySevice extends _i1.Mock implements _i4.StudyBuddySevice {
  MockStudyBuddySevice() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.LearningIncentivesApi get api => (super.noSuchMethod(
        Invocation.getter(#api),
        returnValue: _FakeLearningIncentivesApi_0(
          this,
          Invocation.getter(#api),
        ),
      ) as _i2.LearningIncentivesApi);

  @override
  _i5.Future<_i3.StudyBuddy> getStudyBuddyList({
    required String? classKey,
    required int? size,
    required int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getStudyBuddyList,
          [],
          {
            #classKey: classKey,
            #size: size,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<_i3.StudyBuddy>.value(_FakeStudyBuddy_1(
          this,
          Invocation.method(
            #getStudyBuddyList,
            [],
            {
              #classKey: classKey,
              #size: size,
              #offset: offset,
            },
          ),
        )),
      ) as _i5.Future<_i3.StudyBuddy>);

  @override
  _i5.Future<void> postLikeRecords({
    required int? targetId,
    required String? action,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #postLikeRecords,
          [],
          {
            #targetId: targetId,
            #action: action,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}
