import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/model/study_buddy_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/service/study_buddy_sevice.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/studyPartner/state.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'study_partner_controller_test.mocks.dart';

@GenerateMocks([StudyBuddySevice])
void main() {
  group('StudyPartnerController', () {
    late MockStudyBuddySevice mockService;
    late StudyPartnerController controller;
    const classKey = 'test_class';

    setUp(() {
      mockService = MockStudyBuddySevice();
    });

    tearDown(() {
      controller.close();
    });

    group('generateItemUiState', () {
      test('should generate UI states from models correctly', () {
        // Arrange
        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        final models = [
          StudyBuddyModel(
            nickName: '测试用户',
            studyDays: 30,
            continuousDays: 15,
            medal: 5,
            like: 100,
            targetId: 123,
          ),
        ];

        // Act
        final result = controller.generateItemUiState(models);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.name, equals('测试用户'));
        expect(result.first.studyAllCount, equals(30));
        expect(result.first.studyCount, equals(15));
        expect(result.first.achievementCount, equals(5));
        expect(result.first.kudosCount, equals(100));
        expect(result.first.targetId, equals(123));
      });

      test('should handle empty model list', () {
        // Arrange
        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        final List<StudyBuddyModel> models = [];

        // Act
        final result = controller.generateItemUiState(models);

        // Assert
        expect(result, isEmpty);
      });
    });

    group('getKudos', () {
      test('should return number as string when less than 1000', () {
        // Arrange
        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        // Act & Assert
        expect(controller.getKudos(0), equals('0'));
        expect(controller.getKudos(1), equals('1'));
        expect(controller.getKudos(999), equals('999'));
      });

      test('should return formatted string when 1000 or more', () {
        // Arrange
        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        // Act & Assert
        expect(controller.getKudos(1000), equals('1k'));
        expect(controller.getKudos(1500), equals('1k'));
        expect(controller.getKudos(2000), equals('2k'));
        expect(controller.getKudos(10000), equals('10k'));
      });
    });

    // 新增的动画相关测试
    group('Animation methods', () {
      test('startClickLikeAnima should toggle canLike state', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 1,
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        // 验证初始状态
        expect(controller.state.buddyItemUiStates[0].canLike, equals(1));

        // Act
        controller.startClickLikeAnima(0);

        // Assert - canLike 状态应该被切换
        expect(controller.state.buddyItemUiStates[0].canLike, equals(0));
      });

      test('startClickLikeAnima should toggle from unlike to like', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 0, // 已经点过赞
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        // 验证初始状态
        expect(controller.state.buddyItemUiStates[0].canLike, equals(0));

        // Act
        controller.startClickLikeAnima(0);

        // Assert - canLike 状态应该被切换为 1
        expect(controller.state.buddyItemUiStates[0].canLike, equals(1));
      });

      test('generateUiStateWithAnimaInitEnd should toggle canLike correctly', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 1,
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        final itemList = List<BuddyItemUiState>.from(controller.state.buddyItemUiStates);

        // Act
        final result = controller.generateUiStateWithAnimaInitEnd(itemList, 0);

        // Assert
        expect(result.buddyItemUiStates[0].canLike, equals(0)); // 1 -> 0
      });
    });

    // 修改后的 generateUiSateWithLikeAction 测试
    group('generateUiSateWithLikeAction - updated logic', () {
      test('should set playClickLikeAnima to true when liking (canLike=1)', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 1, // 可以点赞
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        final itemList = List<BuddyItemUiState>.from(controller.state.buddyItemUiStates);

        // Act
        final result = controller.generateUiSateWithLikeAction(itemList, 0);

        // Assert
        expect(result.buddyItemUiStates[0].kudosCount, equals(101)); // 100 + 1
        expect(result.buddyItemUiStates[0].canLike, equals(1)); // 保持为 1，因为动画还没完成
        expect(result.buddyItemUiStates[0].playClickLikeAnima, equals(true)); // 播放动画
      });

      test('should set playClickLikeAnima to false when unliking (canLike=0)', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 0, // 已经点过赞，现在要取消
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        final itemList = List<BuddyItemUiState>.from(controller.state.buddyItemUiStates);

        // Act
        final result = controller.generateUiSateWithLikeAction(itemList, 0);

        // Assert
        expect(result.buddyItemUiStates[0].kudosCount, equals(99)); // 100 - 1
        expect(result.buddyItemUiStates[0].canLike, equals(1)); // 取消点赞时主动修改为 1
        expect(result.buddyItemUiStates[0].playClickLikeAnima, equals(false)); // 不播放动画
      });
    });

    group('postLikeRecords integration', () {
      test('should update UI optimistically and call service', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 1,
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        when(mockService.postLikeRecords(
          targetId: 123,
          action: '1',
        )).thenAnswer((_) async {});

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        // 等待初始数据加载完成
        await controller.loadData();
        
        // 验证初始状态
        expect(controller.state.buddyItemUiStates[0].kudosCount, equals(100));
        expect(controller.state.buddyItemUiStates[0].canLike, equals(1));

        // Act
        await controller.postLikeRecords(0);

        // Assert - 验证 UI 状态已更新（乐观更新）
        expect(controller.state.buddyItemUiStates[0].kudosCount, equals(101));
        expect(controller.state.buddyItemUiStates[0].canLike, equals(1)); // 修改后的逻辑：保持为 1
        expect(controller.state.buddyItemUiStates[0].playClickLikeAnima, equals(true)); // 播放动画
        
        // 验证 service 被正确调用
        verify(mockService.postLikeRecords(
          targetId: 123,
          action: '1',
        )).called(1);
      });

      test('should handle unlike action correctly', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '测试用户',
              like: 100,
              canLike: 0, // 已经点过赞，现在要取消
              targetId: 123,
            ),
          ],
          offset: -1,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        when(mockService.postLikeRecords(
          targetId: 123,
          action: '0',
        )).thenAnswer((_) async {});

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );
        
        await controller.loadData();
        
        // 验证初始状态
        expect(controller.state.buddyItemUiStates[0].kudosCount, equals(100));
        expect(controller.state.buddyItemUiStates[0].canLike, equals(0));

        // Act
        await controller.postLikeRecords(0);

        // Assert - 验证取消点赞后的状态
        expect(controller.state.buddyItemUiStates[0].kudosCount, equals(99));
        expect(controller.state.buddyItemUiStates[0].canLike, equals(1)); // 修改后的逻辑：主动修改为 1
        expect(controller.state.buddyItemUiStates[0].playClickLikeAnima, equals(false)); // 不播放动画
        
        // 验证 service 被正确调用
        verify(mockService.postLikeRecords(
          targetId: 123,
          action: '0',
        )).called(1);
      });
    });

    group('loadData and loadMore', () {
      test('should load initial data successfully', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [
            StudyBuddyModel(
              nickName: '用户 1',
              targetId: 1,
            ),
          ],
          offset: 10,
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        // Act
        await controller.loadData();

        // Assert
        expect(controller.state.pageStatus, equals(PageStatus.success));
        expect(controller.state.buddyItemUiStates, hasLength(1));
        expect(controller.state.hasNext, isTrue);
        expect(controller.state.offset, equals(10));
        
        verify(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).called(2);  //环节初始化是会调用 loadData 这儿会调用两次
      });

      test('should handle loadData error', () async {
        // Arrange
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenThrow(Exception('Network error'));

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        // Act
        await controller.loadData();

        // Assert
        expect(controller.state.pageStatus, equals(PageStatus.error));
        expect(controller.state.exception, isA<Exception>());
      });

      test('should load more data when hasNext is true', () async {
        // Arrange
        final firstBatch = StudyBuddy(
          list: [StudyBuddyModel(nickName: '用户 1', targetId: 1)],
          offset: 10,
        );
        final secondBatch = StudyBuddy(
          list: [StudyBuddyModel(nickName: '用户 2', targetId: 2)],
          offset: 20,
        );

        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => firstBatch);

        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 10,
          size: 10,
        )).thenAnswer((_) async => secondBatch);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        // Act
        await controller.loadData();
        await controller.loadMore();

        // Assert
        expect(controller.state.buddyItemUiStates, hasLength(2));
        expect(controller.state.buddyItemUiStates[0].name, equals('用户 1'));
        expect(controller.state.buddyItemUiStates[1].name, equals('用户 2'));
        expect(controller.state.offset, equals(20));
        
        verify(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).called(2);

        verify(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 10,
          size: 10,
        )).called(1);
      });

      test('should not load more when hasNext is false', () async {
        // Arrange
        final studyBuddy = StudyBuddy(
          list: [StudyBuddyModel(nickName: '用户 1', targetId: 1)],
          offset: -1, // -1 表示没有更多数据
        );
        
        when(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).thenAnswer((_) async => studyBuddy);

        controller = StudyPartnerController(
          classKey: classKey,
          service: mockService,
        );

        await controller.loadMore(); // 这次调用应该不会触发网络请求

        // Assert
        expect(controller.state.hasNext, isFalse);
        
        verify(mockService.getStudyBuddyList(
          classKey: classKey,
          offset: 0,
          size: 10,
        )).called(2);
        verifyNoMoreInteractions(mockService);
      });
    });
  });
}