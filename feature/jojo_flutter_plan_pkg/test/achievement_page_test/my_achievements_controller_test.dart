import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievements_model.dart';

import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/state.dart';
import 'package:jojo_flutter_plan_pkg/service/achievements_api.dart';

void main() {
  late MyAchievementsController controller;
  final api = AchievementsApiMock();

  setUp(() {
    controller = MyAchievementsController(
        subjectType: 2, achievementsApi: AchievementsApiMock(), partnerId: 1);
  });

  test('update state', () async {
    AchievementsModel model = await api.getSubjectMedals(2, 1);
    MyAchievementsState state = controller.buildState(model);
    expect(state.title, '成就一览');
  });
}
