import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/model/teacher_achievement_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/teacher_achievement_types/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/teacher_achievements_api.dart';
import 'package:mockito/mockito.dart';

void main() {
  late TeacherAchievementTypesController controller;

  setUp(() {
    controller = TeacherAchievementTypesController(
        api: TeacherAchievementDetailMockApi(), partnerId: 2, subjectType: 2);
  });

  test('getSegmentData', () async {
    await controller.getSegmentData(2);
    expect(controller.state.segmentForSubjects, isNotNull);
    expect(controller.state.subjects, isNotNull);
  });
}

class TeacherAchievementDetailMockApi extends Mock
    implements TeacherAchievementsApi {
  @override
  Future<TeacherAchievementModel> getSegments(int subjectType) {
    return Future.value(TeacherAchievementModel.fromJson(json.decode(
        '{"segments":[{"segmentCode":0,"segmentName":"L0"},{"segmentCode":1,"segmentName":"L1"},{"segmentCode":2,"segmentName":"L2"},{"segmentCode":3,"segmentName":"L3"},{"segmentCode":4,"segmentName":"L4"},{"segmentCode":47,"segmentName":"L5"},{"segmentCode":5,"segmentName":"L6"},{"segmentCode":6,"segmentName":"L7"},{"segmentCode":7,"segmentName":"L8"},{"segmentCode":9,"segmentName":"L9"},{"segmentCode":10,"segmentName":"L10"},{"segmentCode":55,"segmentName":"L系列"}],"subjects":[{"subjectName":"思维","subjectType":1,"selectedType":0},{"subjectName":"阅读","subjectType":2,"selectedType":1},{"subjectName":"创作","subjectType":3,"selectedType":0},{"subjectName":"美育","subjectType":4,"selectedType":0},{"subjectName":"识字","subjectType":5,"selectedType":0},{"subjectName":"英语","subjectType":6,"selectedType":0},{"subjectName":"专题","subjectType":7,"selectedType":0},{"subjectName":"初中语文","subjectType":8,"selectedType":0},{"subjectName":"初中数学","subjectType":9,"selectedType":0},{"subjectName":"初中英语","subjectType":10,"selectedType":0},{"subjectName":"高中语文","subjectType":11,"selectedType":0},{"subjectName":"综合","subjectType":12,"selectedType":0},{"subjectName":"综合","subjectType":13,"selectedType":0}]}')));
  }
}
