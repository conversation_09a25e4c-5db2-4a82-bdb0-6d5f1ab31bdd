import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/achievement_detail_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/medal_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/model/teacher_achievement_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/teacher_achievement_detail/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/teacher_achievements/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/achievements_api.dart';
import 'package:jojo_flutter_plan_pkg/service/teacher_achievements_api.dart';
import 'package:mockito/mockito.dart';

void main() {
  late TeacherAchievementsController controller;
  late TeacherAchievementDetailController detailController;

  setUp(() {
    controller = TeacherAchievementsController(
        subjectType: 2, segmentCode: 1, api: MockTeacherAchievementsApi());
  });

  group('TeacherAchievementsController test', () {
    late TeacherAchievementMedals _model;
    test('test getMedals:', () async {
      var model = await controller.getMedals();
      _model = model;
      expect(model.medals, isNotEmpty);
    });

    late List<List<MedalModel>> _medalGroups;
    test('test getMedalGroups:', () async {
      var medalGroups = controller.getMedalGroups(_model);
      _medalGroups = medalGroups;
      expect(medalGroups, isNotEmpty);
    });

    late List<MedalModel> _medals;
    test('test getMedals:', () async {
      var models = controller.getAllMedals(_medalGroups);
      _medals = models;
      expect(models, isNotEmpty);
    });

    late List<MedalModel> _medalGroup;
    test('test getMedals:', () async {
      var medal = _medals.first;
      controller.medalGroups = _medalGroups;
      var models = controller.getMedalGroup(medal);
      _medalGroup = models;
      expect(models, isNotEmpty);
    });

    test("TeacherAchievementDetailController initialIndex", () {
      var medal = _medals.first;
      detailController = TeacherAchievementDetailController(
          selectedMedal: medal,
          medals: _medalGroup,
          commonResZip: "",
          dao: TeacherAchievementDetailMockApi());
      expect(detailController.state.initialIndex == 0, true);
    });

    test("TeacherAchievementDetailController updateDisplayMedalAtIndex", () {
      detailController.updateDisplayMedalAtIndex(1);
      expect(detailController.state.initialIndex == 1, true);
    });
  });
}

class TeacherAchievementDetailMockApi extends Mock implements AchievementsApi {
  @override
  Future<AchievementDetailModel> getMedalDetail(int medalId) {
    return Future.value(AchievementDetailModel.fromJson(json.decode(
        '{"onlyTrainingCamp":0,"subjectType":2,"segmentName":"L4","commonRes":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/787279104307277825/commonRes%281%29.zip?checksumV2=md5Hex%3D0f0ca77d9b3bb4c6d45f20e73c241f4c","userInfo":{"nickname":"宝*","avatar":"https://appfiles.beta.jojoread.tinman.cn/server-static-resource-file/avatar_default_boy_v1.png"},"shareInfo":{"introduceUrl":"https://mp6b.cn/BMe86ZeczH?r_token=tmosoy"},"medalGroup":{"upgrade":1,"groupKey":"E5B326014CBD4262A23D5FDA6E3589FD","tips":"再坚持1天升级！","groupMedals":[{"id":70,"collectionId":"796408165105983489","title":"1学习小兵","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617700454294529/1744184272719/medal_persist_day1.zip.flutter"},"isGet":1,"getTime":1746518505859,"isView":1,"remark":"L4-累计学习天数-可升级","detailRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/achievementDetail?medalId=70","shareRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/shareAchievement?medalId=70","progress":{"total":1,"current":1,"unit":"天"},"tags":[{"name":"稀有"},{"name":"仅10%获得"}]},{"id":71,"collectionId":"796408165231812609","title":"2学习小将","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617729671816193/1744184279684/medal_persist_day2.zip.flutter"},"isGet":1,"getTime":1747731488119,"isView":1,"remark":"L4-累计学习天数-可升级","detailRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/achievementDetail?medalId=71","shareRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/shareAchievement?medalId=71","progress":{"total":2,"current":2,"unit":"天"},"tags":[{"name":"稀有"},{"name":"仅10%获得"}]}]}}')));
  }
}

class MockTeacherAchievementsApi extends Mock
    implements TeacherAchievementsApi {
  @override
  Future<TeacherAchievementMedals> getMedals(int subject, int code) {
    return Future.value(TeacherAchievementMedals.fromJson(json.decode(
        '{"medals":[{"groupKey":"E5B326014CBD4262A23D5FDA6E3589FD","groupMedals":[{"id":70,"title":"1学习小兵","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617700454294529/1744184272719/medal_persist_day1.zip.flutter"},"remark":"累计学习天数123"},{"id":71,"title":"2学习小将","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617729671816193/1744184279684/medal_persist_day2.zip.flutter"},"remark":"累计学习天数123"},{"id":72,"title":"3进步小达人","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617762286724097/1744184288505/medal_persist_day3.zip.flutter"},"remark":"累计学习天数123"},{"id":73,"title":"4勤奋小院士","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786617796818428929/1744184295687/medal_persist_day4.zip.flutter"},"remark":"累计学习天数123"},{"id":152,"title":"阅读天数五级","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786618939900486657/1744184568178/medal.zip.flutter"},"remark":"累计学习天数123"},{"id":153,"title":"阅读天数六级","resource":{"flutterRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/786618974331528193/1744184576461/medal.zip.flutter"},"remark":"累计学习天数123"}]}],"commonRes":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/787279104307277825/commonRes%281%29.zip?checksumV2=md5Hex%3D0f0ca77d9b3bb4c6d45f20e73c241f4c"}')));
  }
}
