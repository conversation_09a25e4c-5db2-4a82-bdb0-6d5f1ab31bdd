import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/helper/env_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/model/place_order_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/model/subject_info_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/service/pure_enjoy_api_service.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/widget/place_order/controller.dart';
import 'package:mockito/mockito.dart';

import '../plan_pure_enjoy/mocker.dart';

// Mock 依赖项
class MockPureEnjoyApiService extends Mock implements PureEnjoyApi {}

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  JoJoNativeBridge.registerMocker(PureEnjoyTestJoJoBridgeCommonMocker());
  customEnv = MockRunEnv();

  final MockPureEnjoyApiService mockPureMapPageApi = MockPureEnjoyApiService();
  late PlaceOrderController placeOrderController;
  final random = Random();
  final showBack = random.nextDouble() < 0.5;
  const configId = 442;
  const taskId = 166;

  final controller = PlanPureEnjoyController({
    "configId": configId.toString(),
    "taskId": taskId.toString(),
  }, showBack);
  setUp(() {
    placeOrderController = PlaceOrderController(
      controller,
      api: mockPureMapPageApi,
    );
  });

  group("plan_pure_enjoy_controller tests", () {
    // WidgetsFlutterBinding.ensureInitialized();
    // JoJoNativeBridge.registerMocker(PureEnjoyTestJoJoBridgeCommonMocker());
    // customEnv = MockRunEnv();

    test('should return null when selectCourse is null', () async {
      controller.setSelectCourse(null);

      final result = await placeOrderController.handlePlaceOrder();

      expect(result, isNull);
    });

    test('测试handlePlaceOrder接口携带channelNo参数', () async {
      controller.setSelectCourse(const CourseInfoModel(
        linkId: '11', // 确保与实际调用一致
        skuId: '22', // 确保与实际调用一致
        orderId: null, // 触发 placeOrder 调用
        retainPopupOrderId: null,
      ));

      const mockCourseResponse = PlaceOrderModel(orderId: 'mockOrderId');
      when(mockPureMapPageApi.placeOrder(
        'DRAINAGE_COURSE_LINK',
        argThat(equals(11)), // 匹配 dynamic 的 int 11 或 String "11"
        argThat(equals(22)),
        'testChannelNo',
      )).thenAnswer((_) async => mockCourseResponse);

      await placeOrderController.handlePlaceOrder();

      verify(mockPureMapPageApi.placeOrder(
              'DRAINAGE_COURSE_LINK',
              argThat(equals(11)), // 匹配 dynamic 的 int 11 或 String "11"
              argThat(equals(22)),
              'testChannelNo'))
          .called(1);
    });
  });
}
