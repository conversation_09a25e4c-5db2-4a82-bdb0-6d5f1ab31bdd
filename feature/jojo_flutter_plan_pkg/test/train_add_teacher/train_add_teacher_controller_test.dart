import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/train_add_teacher_controller.dart';
import 'package:jojo_flutter_plan_pkg/service/teacher_service_api.dart';
import 'package:mockito/mockito.dart';

import '../plan_pure_enjoy/mocker.dart';

class TestDownloadManager with AbsDownloadManager {
  @override
  cancelDownload() {}

  @override
  Future<void> download(String obj, {Function(double p1)? progressListener, Function(Map<String, String> p1)? successListener, Function(UnifiedExceptionData p1)? failListener}) {
    return Future.value();
  }

  @override
  Future<void> downloadUrl(List<String> urlList, {Function(double p1)? progressListener, Function(Map<String, String> p1)? successListener, Function(UnifiedExceptionData p1)? failListener, bool isNeedCancel = true}) {
    return Future.value();
  }

  @override
  Future<String> getFileLocalPath(String url, {bool isOnlyName = false}) {
    return Future.value(url);
  }

  @override
  Future<String> getFileLocalPathByUrl(String url) {
    return Future.value(url);
  }
}

class _MockApi extends Mock implements TeacherServiceApi {

  @override
  Future doUnReadTip({required String serviceKey, required String classId, required String courseKey}) async {
    return {"success": 1};
  }
}

void main() {
  late TrainAddTeacherController controller;
  late MockRunEnv mockHostEnv;
  late TestPrinter mockPrinter;
  late AbsDownloadManager mockDownloadManager;

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
    mockHostEnv = MockRunEnv();
    mockPrinter = TestPrinter();
    mockDownloadManager = TestDownloadManager();
  });

  group('TrainAddTeacherController', () {
    test('initial state is correctly set from params', () {
      final params = {
        'materialType': 'type1',
        'courseStage': 'stage1',
        'customState': 'state1',
        'classId': 'class1',
        'courseKey': 'course1',
        'materialId': 'material1',
        'serviceKey': 'service1',
        'addRoute': '/add',
        'closeRoute': '/close',
        'imageUrl': 'url',
        'backgroundColor': 'color',
      };

      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      expect(controller.state.materialType, 'type1');
      expect(controller.state.courseStage, 'stage1');
      expect(controller.state.customState, 'state1');
      expect(controller.state.classId, 'class1');
      expect(controller.state.courseKey, 'course1');
      expect(controller.state.materialId, 'material1');
      expect(controller.state.serviceKey, 'service1');
      expect(controller.state.addRoute, '/add');
      expect(controller.state.closeRoute, '/close');
      expect(controller.state.imageUrl, 'url');
      expect(controller.state.backgroundColor, 'color');
      expect(controller.state.pageStatus, PageStatus.loading);
      expect(controller.state.exception, isNull);
    });

    test('show()', () {
      final params = {'materialType': 'type1'};
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      controller.show();
      assert(true);
    });

    test('back()', () {
      final params = {'materialType': 'type1'};
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      controller.back();
      assert(true);
    });

    test('jumpToAddTeacher()', () {
      final params = {
        'materialType': 'type1',
        'addRoute': '/add',
      };
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      controller.jumpToAddTeacher();
      assert(true);
    });

    test('jumpToWatchReport()', () {
      final api = _MockApi();
      var params = {
        'materialType': 'type1',
        'closeRoute': '/close',
        'serviceKey': 'service1',
        'classId': 'class1',
      };
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        teacherApi: api,
        downloadManager: mockDownloadManager,
      );

      controller.jumpToWatchReport();
      assert(true);

      params = {
        'materialType': 'type1',
        'closeRoute': '/close',
        'classId': 'class1',
      };
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        teacherApi: api,
        downloadManager: mockDownloadManager,
      );

      controller.jumpToWatchReport();
      assert(true);
    });

    test('_getEnv() returns injected hostEnv', () {
      final Map<String, String> params = {};
      controller = TrainAddTeacherController(params,
        hostEnv: mockHostEnv,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      expect(controller.test_getEnv(), mockHostEnv);
    });

    test('_getEnv() returns RunEnv when hostEnv is null', () {
      final Map<String, String> params = {};
      controller = TrainAddTeacherController(params,
        printer: mockPrinter,
        downloadManager: mockDownloadManager,
      );

      expect(controller.test_getEnv(), isNotNull);
    });
  });
}
