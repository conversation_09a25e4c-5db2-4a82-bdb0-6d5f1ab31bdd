import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/common/config/address.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/ext.dart';

class MockPrinter extends Printer {
  List<String> errors = [];

  void e(String tag, String message) {
    errors.add('$tag: $message');
  }
}

void main() {
  group('getTrainAddTeacherRoute', () {
    final baseUrl = Address.appFlutter; // Replace with actual
    final path = AppPage.trainAddTeacherPage.path; // Replace with actual

    late MockPrinter mockPrinter;
    const String testTag = trainAddTeacherTag;
    final Map<String, String> testParams = {
      'key1': 'value1',
      'key2': '',
      'key3': 'value3',
    };

    setUp(() {
      mockPrinter = MockPrinter();
    });

    test('should return value when key exists and value is not empty', () {
      final result = getValue(testParams, 'key1', mockPrinter);
      expect(result, equals('value1'));
      expect(mockPrinter.errors, isEmpty);
    });

    test(
        'should return empty string and log error when key exists but value is empty',
        () {
      final result = getValue(testParams, 'key2', mockPrinter);
      expect(result, isEmpty);
      expect(mockPrinter.errors.length, equals(1));
      expect(
          mockPrinter.errors[0], equals('$testTag: _getValue 出现空值 key=key2'));
    });

    test('should return empty string and log error when key does not exist',
        () {
      final result = getValue(testParams, 'nonexistent', mockPrinter);
      expect(result, isEmpty);
      expect(mockPrinter.errors.length, equals(1));
      expect(mockPrinter.errors[0],
          equals('$testTag: _getValue 出现空值 key=nonexistent'));
    });

    test(
        'should handle null params map by returning empty string and logging error',
        () {
      final result = getValue(null, 'key1', mockPrinter);
      expect(result, isEmpty);
      expect(mockPrinter.errors.length, equals(1));
      expect(
          mockPrinter.errors[0], equals('$testTag: _getValue 出现空值 key=key1'));
    });

    test('should return URL with all parameters when all are provided', () {
      final result = getTrainAddTeacherRoute(
        materialType: 'video',
        courseStage: 'intro',
        customState: 'custom',
        classId: 'class123',
        courseKey: 'course456',
        materialId: 'material789',
        serviceKey: 'service012',
        addBtnText: 'addBtnText',
        closeBtnText: 'closeBtnText',
        addRoute: '/add',
        closeRoute: '/close',
        imageUrl: 'https://example.com/image.png',
        backgroundColor: 'FFFFFF',
      );

      expect(
          result,
          equals('$baseUrl$path?'
              'materialType=video&'
              'courseStage=intro&'
              'customState=custom&'
              'classId=class123&'
              'courseKey=course456&'
              'materialId=material789&'
              'serviceKey=service012&'
              'addBtnText=addBtnText&'
              'closeBtnText=closeBtnText&'
              'addRoute=%2Fadd&'
              'closeRoute=%2Fclose&'
              'imageUrl=https%3A%2F%2Fexample.com%2Fimage.png&'
              'backgroundColor=FFFFFF&'
              'windowType=window'));
    });

    test('should return URL with only non-empty parameters', () {
      final result = getTrainAddTeacherRoute(
        materialType: '',
        courseStage: 'intro',
        customState: '',
        classId: 'class123',
        courseKey: '',
        materialId: 'material789',
        serviceKey: 'service012',
        addBtnText: 'addBtnText',
        closeBtnText: 'closeBtnText',
        addRoute: '/add',
        closeRoute: '',
        imageUrl: 'https://example.com/image.png',
        backgroundColor: '',
      );

      expect(
          result,
          equals('$baseUrl$path?'
              'courseStage=intro&'
              'classId=class123&'
              'materialId=material789&'
              'serviceKey=service012&'
              'addBtnText=addBtnText&'
              'closeBtnText=closeBtnText&'
              'addRoute=%2Fadd&'
              'imageUrl=https%3A%2F%2Fexample.com%2Fimage.png&'
              'windowType=window'));
    });

    test('should return base URL when all parameters are empty', () {
      final result = getTrainAddTeacherRoute(
        materialType: '',
        courseStage: '',
        customState: '',
        classId: '',
        courseKey: '',
        materialId: '',
        serviceKey: '',
        addBtnText: '',
        closeBtnText: '',
        addRoute: '',
        closeRoute: '',
        imageUrl: '',
        backgroundColor: '',
      );

      expect(
          result,
          equals('$baseUrl$path?'
              'windowType=window'));
    });

    test('should properly encode URL parameters', () {
      final result = getTrainAddTeacherRoute(
        materialType: 'video lecture',
        courseStage: 'introduction',
        customState: 'custom state',
        classId: 'class 123',
        courseKey: 'course/key',
        materialId: 'material@789',
        serviceKey: 'service=012',
        addBtnText: 'addBtnText',
        closeBtnText: 'closeBtnText',
        addRoute: '%2Fadd%20route',
        closeRoute: '%2Fclose%3Froute',
        imageUrl: 'https%3A%2F%2Fexample.com%2Fimage.png%3Fparam%3Dvalue',
        backgroundColor: 'FF FF FF',
      );

      expect(
          result,
          equals('$baseUrl$path?'
              'materialType=video+lecture&'
              'courseStage=introduction&'
              'customState=custom+state&'
              'classId=class+123&'
              'courseKey=course%2Fkey&'
              'materialId=material%40789&'
              'serviceKey=service%3D012&'
              'addBtnText=addBtnText&'
              'closeBtnText=closeBtnText&'
              'addRoute=%252Fadd%2520route&'
              'closeRoute=%252Fclose%253Froute&'
              'imageUrl=https%253A%252F%252Fexample.com%252Fimage.png%253Fparam%253Dvalue&'
              'backgroundColor=FF+FF+FF&'
              'windowType=window'));
    });
  });
}
