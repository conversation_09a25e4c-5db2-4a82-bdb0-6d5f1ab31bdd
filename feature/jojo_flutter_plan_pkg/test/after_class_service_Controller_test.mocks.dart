// Mocks generated by Mockito 5.4.0 from annotations
// in jojo_flutter_plan_pkg/test/after_class_service_Controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;

import 'package:jojo_flutter_base/base.dart' as _i11;
import 'package:jojo_flutter_plan_pkg/pages/drainage_course_dialog/model/receive_order_result.dart'
    as _i7;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_advertisements_data.dart'
    as _i5;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_ads_data.dart'
    as _i8;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart'
    as _i6;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart'
    as _i2;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart'
    as _i4;
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/pure_new_user_page_show_data.dart'
    as _i3;
import 'package:jojo_flutter_plan_pkg/service/home_map_page_api.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCourseSubjectTabData_0 extends _i1.SmartFake
    implements _i2.CourseSubjectTabData {
  _FakeCourseSubjectTabData_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePureNewUserPageShowData_1 extends _i1.SmartFake
    implements _i3.PureNewUserPageShowData {
  _FakePureNewUserPageShowData_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTopUserInfo_2 extends _i1.SmartFake implements _i4.TopUserInfo {
  _FakeTopUserInfo_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCourseAdvertisementsData_3 extends _i1.SmartFake
    implements _i5.CourseAdvertisementsData {
  _FakeCourseAdvertisementsData_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCourseAfterServersData_4 extends _i1.SmartFake
    implements _i6.CourseAfterServersData {
  _FakeCourseAfterServersData_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeReceiveOrderResult_5 extends _i1.SmartFake
    implements _i7.ReceiveOrderResult {
  _FakeReceiveOrderResult_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCourseAfterAdsInfoData_6 extends _i1.SmartFake
    implements _i8.CourseAfterAdsInfoData {
  _FakeCourseAfterAdsInfoData_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [HomeMapPageApi].
///
/// See the documentation for Mockito's code generation for more information.
class MockHomeMapPageApi extends _i1.Mock implements _i9.HomeMapPageApi {
  MockHomeMapPageApi() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i10.Future<_i2.CourseSubjectTabData> getUserSubjectClass() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserSubjectClass,
          [],
        ),
        returnValue: _i10.Future<_i2.CourseSubjectTabData>.value(
            _FakeCourseSubjectTabData_0(
          this,
          Invocation.method(
            #getUserSubjectClass,
            [],
          ),
        )),
      ) as _i10.Future<_i2.CourseSubjectTabData>);

  @override
  _i10.Future<_i3.PureNewUserPageShowData> getPureNewUserPageShowData() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPureNewUserPageShowData,
          [],
        ),
        returnValue: _i10.Future<_i3.PureNewUserPageShowData>.value(
            _FakePureNewUserPageShowData_1(
          this,
          Invocation.method(
            #getPureNewUserPageShowData,
            [],
          ),
        )),
      ) as _i10.Future<_i3.PureNewUserPageShowData>);

  @override
  _i10.Future<_i4.TopUserInfo> requestTopUserInfo(int? subjectType) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestTopUserInfo,
          [subjectType],
        ),
        returnValue: _i10.Future<_i4.TopUserInfo>.value(_FakeTopUserInfo_2(
          this,
          Invocation.method(
            #requestTopUserInfo,
            [subjectType],
          ),
        )),
      ) as _i10.Future<_i4.TopUserInfo>);

  @override
  _i10.Future<_i4.TopUserInfo> requestTopUserInfoDialogCallback(
    List<String>? bizIdList,
    int? type,
    String? userId,
    int? classId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestTopUserInfoDialogCallback,
          [
            bizIdList,
            type,
            userId,
            classId,
          ],
        ),
        returnValue: _i10.Future<_i4.TopUserInfo>.value(_FakeTopUserInfo_2(
          this,
          Invocation.method(
            #requestTopUserInfoDialogCallback,
            [
              bizIdList,
              type,
              userId,
              classId,
            ],
          ),
        )),
      ) as _i10.Future<_i4.TopUserInfo>);

  @override
  _i10.Future<_i5.CourseAdvertisementsData> requestAdvertisements({
    int? subjectId,
    int? courseId,
    String? scene = r'subjectRmd',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestAdvertisements,
          [],
          {
            #subjectId: subjectId,
            #courseId: courseId,
            #scene: scene,
          },
        ),
        returnValue: _i10.Future<_i5.CourseAdvertisementsData>.value(
            _FakeCourseAdvertisementsData_3(
          this,
          Invocation.method(
            #requestAdvertisements,
            [],
            {
              #subjectId: subjectId,
              #courseId: courseId,
              #scene: scene,
            },
          ),
        )),
      ) as _i10.Future<_i5.CourseAdvertisementsData>);

  @override
  _i10.Future<_i6.CourseAfterServersData> requestAfterCourseServers(
    int? subjectType,
    _i11.RequestOptions? cancelToken,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestAfterCourseServers,
          [
            subjectType,
            cancelToken,
          ],
        ),
        returnValue: _i10.Future<_i6.CourseAfterServersData>.value(
            _FakeCourseAfterServersData_4(
          this,
          Invocation.method(
            #requestAfterCourseServers,
            [
              subjectType,
              cancelToken,
            ],
          ),
        )),
      ) as _i10.Future<_i6.CourseAfterServersData>);

  @override
  _i10.Future<_i7.ReceiveOrderResult> receiveDrainageCourse({
    String? sceneType = r'SUBJECT_RMD',
    int? linkId,
    String? skuId,
    String? channel,
    String? channelNo,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #receiveDrainageCourse,
          [],
          {
            #sceneType: sceneType,
            #linkId: linkId,
            #skuId: skuId,
            #channel: channel,
            #channelNo: channelNo,
          },
        ),
        returnValue:
            _i10.Future<_i7.ReceiveOrderResult>.value(_FakeReceiveOrderResult_5(
          this,
          Invocation.method(
            #receiveDrainageCourse,
            [],
            {
              #sceneType: sceneType,
              #linkId: linkId,
              #skuId: skuId,
              #channel: channel,
              #channelNo: channelNo,
            },
          ),
        )),
      ) as _i10.Future<_i7.ReceiveOrderResult>);

  @override
  _i10.Future<_i8.CourseAfterAdsInfoData>
      requestAfterCourseServersAdvertisements({
    String? key,
    required String? queryType,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #requestAfterCourseServersAdvertisements,
              [],
              {
                #key: key,
                #queryType: queryType,
              },
            ),
            returnValue: _i10.Future<_i8.CourseAfterAdsInfoData>.value(
                _FakeCourseAfterAdsInfoData_6(
              this,
              Invocation.method(
                #requestAfterCourseServersAdvertisements,
                [],
                {
                  #key: key,
                  #queryType: queryType,
                },
              ),
            )),
          ) as _i10.Future<_i8.CourseAfterAdsInfoData>);
}
