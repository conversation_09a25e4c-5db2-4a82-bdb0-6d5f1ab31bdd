import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/model/find_study_partner_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/service/find_study_partner_api.dart';

void main() {
  late FindStudyPartnerController controller;

  setUp(() {
    controller = FindStudyPartnerController(FindStudyPartnerErrorMockApi());
  });

  group("FindStudyPartnerController Error Tests:", () {
    test('Error requestSubjectTabData', () async {
      var data = await controller.requestSubjectTabData();
      expect(data, isNull);
    });

    test('Error initData', () async {
      await controller.initData();
      expect(controller.state.partnersModel, isNull);
    });

    test('Error refreshLatestData', () async {
      await controller.refreshLatestData();
      expect(controller.state.partnersModel, isNull);
    });
  });
}
