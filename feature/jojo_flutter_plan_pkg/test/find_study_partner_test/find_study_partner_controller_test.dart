import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/model/find_study_partner_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/service/find_study_partner_api.dart';

void main() {
  late FindStudyPartnerController controller;

  setUp(() {
    controller = FindStudyPartnerController(FindStudyPartnerMockApi());
  });

  group("FindStudyPartnerController Tests:", () {
    late List<SubjectList> subjectList;
    late CourseSubjectTabData? tabData;

    test('requestSubjectTabData', () async {
      tabData = await controller.requestSubjectTabData();
      expect(tabData, isNotNull);
    });

    test('getCleanSubjectList', () async {
      List<SubjectList> list = controller
          .getCleanSubjectList(tabData ?? const CourseSubjectTabData());
      subjectList = list;
      expect(list.length, 4);
    });

    test('getSubjectTypeAtIndex', () {
      int? subjectType = controller.getSubjectTypeAtIndex(subjectList, 1);
      expect(subjectType, 1);
    });

    test('requestStudyPartner', () async {
      FindStudyPartnersModel? model =
          await controller.requestStudyPartner(4, false);
      expect(model, isNotNull);
    });

    test('refreshDataAtIndex', () async {
      controller.setSubjectList(subjectList);
      await controller.refreshDataAtIndex(1);
      expect(controller.state.partnersModel, isNotNull);
    });

    test('preRequestPartner', () async {
      controller.setSubjectList(subjectList);
      await controller.preRequestPartner(2);
      var model = controller.getPartnerModel(2);
      expect(model, isNotNull);
    });

    test('findPreRequestPartner', () async {
      controller.setSubjectList(subjectList);
      await controller.findPreRequestPartner(2);
      var model = controller.getPartnerModel(3);
      expect(model, isNotNull);
    });

    test('addPartner', () async {
      bool success = await controller.addPartner(1);
      expect(success, true);
    });

    test('refreshLatestData', () async {
      await controller.refreshLatestData();
      expect(controller.state.partnersModel, isNotNull);
    });

    test('forceRefreshLatestPage', () async {
      controller.setSubjectList(subjectList);
      await controller.forceRefreshLatestPage();
      expect(controller.state.partnersModel, isNotNull);
    });
  });
}
