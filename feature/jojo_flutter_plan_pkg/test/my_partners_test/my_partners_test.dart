import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/enums/dynamic_action_type.dart';
import 'package:jojo_flutter_plan_pkg/service/my_partners_api.dart';

void main() {
  group('MyPartnersCtrl tests:', () {
    late MyPartnersCtrl myPartnersCtrl;
    TestWidgetsFlutterBinding.ensureInitialized(); // 确保 WidgetsBinding 已经初始化

    setUp(() {
      myPartnersCtrl = MyPartnersCtrl(api: MyPartnersApiMock());
      myPartnersCtrl.partnersPageSize = 2;
    });

    test('changeTab should update selectedTabIndex', () {
      myPartnersCtrl.changeTab(1);
      expect(myPartnersCtrl.state.selectedTabIndex, 1);
    });

    test('loadPartners should update partnersList', () async {
      await myPartnersCtrl.loadPartners(refresh: true);
      expect(myPartnersCtrl.state.partnersList, isNotEmpty);
    });

    test('loadPartners should update partnersList', () async {
      await myPartnersCtrl.loadDiscoverPartners();
      expect(
          myPartnersCtrl.state.discoverPartnersData?.partnerList, isNotEmpty);
    });

    test('loadDynamics should update dynamicsList', () async {
      await myPartnersCtrl.loadDynamics(refresh: true);
      expect(myPartnersCtrl.state.dynamicsList, isNotEmpty);
    });

    test('loadDynamics should update dynamicsList', () async {
      await myPartnersCtrl.loadDynamics(refresh: true);
      expect(myPartnersCtrl.state.dynamicsList, isNotEmpty);
    });

    test('sendAction should update actionState', () async {
      final dynamicId = myPartnersCtrl.state.dynamicsList.first.dynamicId;
      await myPartnersCtrl.sendAction(DynamicActionType.flower, dynamicId, 2);
      expect(myPartnersCtrl.state.dynamicsList.first.actionState.hasGivenFlower,
          true);
    });

    test('sendAction should update actionState', () async {
      final dynamicId = myPartnersCtrl.state.dynamicsList.first.dynamicId;
      await myPartnersCtrl.sendAction(DynamicActionType.poke, dynamicId, 2);
      expect(
          myPartnersCtrl.state.dynamicsList.first.actionState.hasPoked, true);
    });

    test('loadDynamics should update dynamicsList', () async {
      await myPartnersCtrl.onDynamicsRefresh();
      expect(myPartnersCtrl.state.dynamicsList, isNotEmpty);
    });
    
    test('onDynamicsLoadMore', () async {
      myPartnersCtrl.state.hasMoreDynamics = true;
      await myPartnersCtrl.onDynamicsLoadMore();
      expect(true, true);

      myPartnersCtrl.state.hasMoreDynamics = false;
      await myPartnersCtrl.onDynamicsLoadMore();
      expect(true, true);
    });

    test('Partners onPartnersRefresh', () async {
      await myPartnersCtrl.onPartnersRefresh();
      expect(myPartnersCtrl.state.partnersList, isNotEmpty);
    });

    test('Partners deletePartner', () async {
      myPartnersCtrl.deletePartner(1);
      expect(myPartnersCtrl.state.partnersList.length, 1);
    });

    test('Partners onPartnersRefresh1', () async {
      myPartnersCtrl.partnersPageSize = 20;
      await myPartnersCtrl.onPartnersRefresh();
      expect(myPartnersCtrl.state.partnersList, isNotEmpty);
    });

    test('Partners onPartnersLoadMore', () async {
      myPartnersCtrl.partnersPageSize = 20;
      await myPartnersCtrl.onPartnersLoadMore();
      expect(myPartnersCtrl.state.partnersList, isNotEmpty);
    });
  });
}
