import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_bridge_storage.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/util/hm_game_container.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/model/route_params.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/model/hm_game_container_gray_config.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/model/course_session_list_info.dart';

import '../plan_pure_enjoy/mocker.dart';
@GenerateMocks([ABTesting])
import 'course_session_list_controller_test.mocks.dart';

void main() {
  group('CourseSessionListController Tests', () {
    late CourseSessionListController controller;
    late MockABTesting mockABTesting;
    late CourseSessionRouteParams routeParams;

    setUp(() {
      routeParams = CourseSessionRouteParams(
          deviceOrientation: 'portrait', loadingScene: 1, engineType: 1);
      controller = CourseSessionListController(
        classKey: 'testClass',
        lessonKey: 'testLesson',
        routeParams: routeParams,
      );
      mockABTesting = MockABTesting();
      HmGameContainerGary.resetForTesting();
    });
    tearDown(() {
      // 清理静态变量
      HmGameContainerGary.resetForTesting();
    });

    group('teachTool test', () {
      test("调用测试", () {
        final courseInfoData = {"courseKey": "25852", "courseName": "单元测试"};
        final lessonInfoData = {"lessonKey": "25852_123", "name": "测试课程"};
        final testData = CourseSessionListInfo(
          courseInfo: CourseInfo.fromJson(courseInfoData),
          lessonInfo: LessonInfo.fromJson(lessonInfoData),
        );

        controller.openTeachToolForTesting(testData);
        controller.closeTeachToolForTesting();
        controller.pageDispose();
      });
    });

    group('hmContanireAbTesting tests', () {
      test(
          'should successfully call initConfig and set config when API succeeds',
          () async {
        // Arrange
        final params = {
          'classKey': 'testClass',
          'lessonKey': 'testLesson',
          'courseKey': 'testCourse',
        };

        final expectedConfig = HmGameContainerGrayConfig(
          enabled: true,
        );

        final apiResult = ApiResult<HmGameContainerGrayConfig>(
          data: BaseEvaluation(value: expectedConfig),
          error: null,
        );

        // Mock ABTesting 调用
        when(mockABTesting.getObject<HmGameContainerGrayConfig>(
          'jojo_flutter_harmony_unity_and_pure_container',
          any,
          context: params,
        )).thenAnswer((_) async => apiResult);

        // 设置 Mock 实例
        HmGameContainerGary.setABTestingInstance(mockABTesting);

        // Act
        await controller.hmContanireAbTesting(params);

        // Assert
        expect(HmGameContainerGary.abSwitchConfig, equals(expectedConfig));
        expect(HmGameContainerGary.enabledHmGameContainer(), isTrue);

        verify(mockABTesting.getObject<HmGameContainerGrayConfig>(
          'jojo_flutter_harmony_unity_and_pure_container',
          any,
          context: params,
        )).called(1);
      });
    });

    group('isPuzzleTeachMode() 测试', () {
      test('默认返回false', () {
        expect(controller.isPuzzleTeachMode(), false); // ✅ 验证预期
      });
    });

    group('shouldShowGuide tests', () {
      StorageTestJoJoBridgeCommonMocker bridgeCommonMocker=StorageTestJoJoBridgeCommonMocker();
      JoJoNativeBridge.registerMocker(bridgeCommonMocker);
      test('first in lesson_list return true', () async {
        bridgeCommonMocker.clear();
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, true);
        await controller.updateShouldShowGuide();
      });

      test('second in lesson_list  return true', () async {
        controller= CourseSessionListController(
          classKey: 'testClass',
          lessonKey: 'testLesson',
          routeParams: routeParams,
        );
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, true);
        await controller.updateShouldShowGuide();
      });

      test('第三次进入 lesson_list 应该显示引导', () async {
        controller= CourseSessionListController(
          classKey: 'testClass',
          lessonKey: 'testLesson',
          routeParams: routeParams,
        );
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, true);
        await controller.updateShouldShowGuide();
      });

      test('第四次进入 lesson_list 不应显示引导', () async {
        controller= CourseSessionListController(
          classKey: 'testClass',
          lessonKey: 'testLesson',
          routeParams: routeParams,
        );
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, false);
        await controller.updateShouldShowGuide();
      });

      test('超过三次进入 lesson_list 不应显示引导', () async {
        controller= CourseSessionListController(
          classKey: 'testClass',
          lessonKey: 'testLesson',
          routeParams: routeParams,
        );
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, false);
      });

      test('one time in lesson_list click more item return true', () async {
        bridgeCommonMocker.clear();
        controller= CourseSessionListController(
          classKey: 'testClass',
          lessonKey: 'testLesson',
          routeParams: routeParams,
        );
        await controller.checkShouldShowGuide();
        await controller.updateShouldShowGuide();
        await controller.updateShouldShowGuide();
        await controller.updateShouldShowGuide();
        await controller.updateShouldShowGuide();
        await controller.updateShouldShowGuide();
        await controller.updateShouldShowGuide();
        bool shouldShow = await controller.checkShouldShowGuide();
        expect(shouldShow, true);
      });

    });


    group('showEyeProtection', () {
      test('should return false if data is null', () {
        expect(controller.showEyeProtection(null), false);
      });

      test('should return false if shouldShow is false', () {
        const data = EyeProtectionData(shouldShow: false, webURL: 'https://example.com');
        expect(controller.showEyeProtection(data), false);
      });

      test('should return false if webURL is null', () {
        const data = EyeProtectionData(shouldShow: true, webURL: null);
        expect(controller.showEyeProtection(data), false);
      });

      test('should return false if webURL is empty', () {
        const data = EyeProtectionData(shouldShow: true, webURL: '');
        expect(controller.showEyeProtection(data), false);
      });

      test('should return true if shouldShow is true and webURL is not empty', () {
        const data = EyeProtectionData(shouldShow: true, webURL: 'https://example.com');
        expect(controller.showEyeProtection(data), true);
      });
    });

  });


}


