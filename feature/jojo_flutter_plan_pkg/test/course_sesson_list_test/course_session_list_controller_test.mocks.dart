// Mocks generated by Mockito 5.4.0 from annotations
// in jojo_flutter_plan_pkg/test/course_sesson_list_test/course_session_list_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dio/dio.dart' as _i2;
import 'package:flutter_ab_testing/model/api_result.dart' as _i3;
import 'package:flutter_ab_testing/src/ab_testing.dart' as _i4;
import 'package:json_annotation/json_annotation.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDio_0 extends _i1.SmartFake implements _i2.Dio {
  _FakeDio_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeApiResult_1<T> extends _i1.SmartFake implements _i3.ApiResult<T> {
  _FakeApiResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ABTesting].
///
/// See the documentation for Mockito's code generation for more information.
class MockABTesting extends _i1.Mock implements _i4.ABTesting {
  MockABTesting() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set baseContext(Map<String, Object>? _baseContext) => super.noSuchMethod(
        Invocation.setter(
          #baseContext,
          _baseContext,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.Dio get dioClient => (super.noSuchMethod(
        Invocation.getter(#dioClient),
        returnValue: _FakeDio_0(
          this,
          Invocation.getter(#dioClient),
        ),
      ) as _i2.Dio);

  @override
  set dioClient(_i2.Dio? _dioClient) => super.noSuchMethod(
        Invocation.setter(
          #dioClient,
          _dioClient,
        ),
        returnValueForMissingStub: null,
      );

  @override
  void Function(Object) get log => (super.noSuchMethod(
        Invocation.getter(#log),
        returnValue: (Object object) {},
      ) as void Function(Object));

  @override
  set log(void Function(Object)? _log) => super.noSuchMethod(
        Invocation.setter(
          #log,
          _log,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<void> release() => (super.noSuchMethod(
        Invocation.method(
          #release,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i3.ApiResult<dynamic>> get<T>(
    String? key, {
    T? defaultValue,
    Map<String, Object>? context = const {},
    T? obj,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [key],
          {
            #defaultValue: defaultValue,
            #context: context,
            #obj: obj,
          },
        ),
        returnValue:
            _i5.Future<_i3.ApiResult<dynamic>>.value(_FakeApiResult_1<dynamic>(
          this,
          Invocation.method(
            #get,
            [key],
            {
              #defaultValue: defaultValue,
              #context: context,
              #obj: obj,
            },
          ),
        )),
      ) as _i5.Future<_i3.ApiResult<dynamic>>);

  @override
  _i5.Future<_i3.ApiResult<bool>> getBool(
    String? key, {
    Map<String, Object>? context = const {},
    bool? defaultValue,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBool,
          [key],
          {
            #context: context,
            #defaultValue: defaultValue,
          },
        ),
        returnValue:
            _i5.Future<_i3.ApiResult<bool>>.value(_FakeApiResult_1<bool>(
          this,
          Invocation.method(
            #getBool,
            [key],
            {
              #context: context,
              #defaultValue: defaultValue,
            },
          ),
        )),
      ) as _i5.Future<_i3.ApiResult<bool>>);

  @override
  _i5.Future<_i3.ApiResult<String>> getString(
    String? key, {
    Map<String, Object>? context = const {},
    String? defaultValue = r'',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getString,
          [key],
          {
            #context: context,
            #defaultValue: defaultValue,
          },
        ),
        returnValue:
            _i5.Future<_i3.ApiResult<String>>.value(_FakeApiResult_1<String>(
          this,
          Invocation.method(
            #getString,
            [key],
            {
              #context: context,
              #defaultValue: defaultValue,
            },
          ),
        )),
      ) as _i5.Future<_i3.ApiResult<String>>);

  @override
  _i5.Future<_i3.ApiResult<int>> getInt(
    String? key, {
    Map<String, Object>? context = const {},
    int? defaultValue = -1,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getInt,
          [key],
          {
            #context: context,
            #defaultValue: defaultValue,
          },
        ),
        returnValue: _i5.Future<_i3.ApiResult<int>>.value(_FakeApiResult_1<int>(
          this,
          Invocation.method(
            #getInt,
            [key],
            {
              #context: context,
              #defaultValue: defaultValue,
            },
          ),
        )),
      ) as _i5.Future<_i3.ApiResult<int>>);

  @override
  _i5.Future<_i3.ApiResult<double>> getDouble(
    String? key, {
    Map<String, Object>? context = const {},
    double? defaultValue = 0.0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getDouble,
          [key],
          {
            #context: context,
            #defaultValue: defaultValue,
          },
        ),
        returnValue:
            _i5.Future<_i3.ApiResult<double>>.value(_FakeApiResult_1<double>(
          this,
          Invocation.method(
            #getDouble,
            [key],
            {
              #context: context,
              #defaultValue: defaultValue,
            },
          ),
        )),
      ) as _i5.Future<_i3.ApiResult<double>>);

  @override
  _i5.Future<_i3.ApiResult<T>>
      getObject<T extends _i6.JsonConverter<dynamic, dynamic>>(
    String? key,
    T? obj, {
    Map<String, Object>? context = const {},
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getObject,
              [
                key,
                obj,
              ],
              {#context: context},
            ),
            returnValue: _i5.Future<_i3.ApiResult<T>>.value(_FakeApiResult_1<T>(
              this,
              Invocation.method(
                #getObject,
                [
                  key,
                  obj,
                ],
                {#context: context},
              ),
            )),
          ) as _i5.Future<_i3.ApiResult<T>>);
}
