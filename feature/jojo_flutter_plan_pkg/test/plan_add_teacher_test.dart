// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/controller.dart';

void main() {
  late PlanAddTeacherCtrl controller;
  setUp(() {
    String json = "%7B%22popupTitle%22%3A%22%E4%B8%93%E5%B1%9E%E7%8F%AD%E7%8F%AD%22%2C%22introduceImageList%22%3A%5B%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F756547486278519809%2FlQLPJxO9ADu6XHHNBbrNAu6wSbO_vUda0swHbSc9pe7cAA_750_1466.png%3FchecksumV2%3Dmd5Hex%253Dfbd0de3e0a88379bbca7b2b30edf95dd%22%5D%2C%22buttonRoute%22%3A%22tinman-router%3A%2F%2Fcn.tinman.jojoread%2Fwebview%3Furl%3Dhttps%253A%252F%252Fact.fat.tinman.cn%252Fexchange%252Fsuccess%253ForderId%253D0%2526classTeacherIds%253D141485%2526userId%253D100459480%2526classKey%253D20021_2285%2526gudPgId%253D770%2526qrPgId%253D119%22%2C%22buttonText%22%3A%22%E5%8A%A0%E5%BE%AE%E4%BF%A1%E4%BA%AB%E6%9C%8D%E5%8A%A1%22%2C%22closedText%22%3Anull%2C%22introduce%22%3Anull%2C%22actionTitle%22%3Anull%2C%22actionTip%22%3Anull%2C%22hasSetting%22%3Anull%2C%22options%22%3Anull%7D";
    controller = PlanAddTeacherCtrl(
      dataJsonString: Uri.decodeComponent(json)
    );
  });

  group('plan_add_teacher_controller tests:', () {
    test('setPopInfo', () async {
      controller.setPopInfo();
      expect(controller.state.popInfo != null, true);
    });

  });
}
