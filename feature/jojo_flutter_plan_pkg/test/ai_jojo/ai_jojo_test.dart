

import 'package:flutter_cache_manager/file.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/ai_frequency.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_dialog_dismiss_reason.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/aijojo/ai_jojo_state.dart';
import 'package:jojo_flutter_plan_pkg/service/ai_jiao_jiao_service_api.dart';
import 'package:mockito/mockito.dart';
class MockCacheManager extends Mock implements CacheManager {
 @override
  Future<File> getSingleFile(String url, {String? key, Map<String, String>? headers}) {
    throw UnimplementedError();
  }
}
// Mock Service
class MockAIJiaoJiaoServiceApi extends Mock implements AIJiaoJiaoServiceApi {
  final bool mockException;
  final bool mockShow;

  MockAIJiaoJiaoServiceApi(this.mockShow, {required this.mockException});

  @override
  Future<AiFrequency> getFrequency(scene){
    if(mockException){
      throw Exception("mockException");
    }
    if(mockShow){
      return Future.value( AiFrequency(
        encouragementConversationParamsVo: EncouragementConversationParamsVo(
          canPopupAiCall: true,
          encouragementConversationIcon: "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812372011658414081/lQLPJwTZTxDXLMHMl8yXsFy8LwvJUOsACDNOjwYi6QA_151_151.png?checksumV2=md5Hex%3D86dc265987f9056adebdef33e93fa56b&ossUrl=tinman-oss%3A%2F%2Fvulcan-nuwa-admin-appicon%2F812372011658414081%2FlQLPJwTZTxDXLMHMl8yXsFy8LwvJUOsACDNOjwYi6QA_151_151.png%3FchecksumV2%3Dmd5Hex%253D86dc265987f9056adebdef33e93fa56b%26supportExt%3D%257B%2522src%2522%253A%2522.png%2522%252C%2522rplc%2522%253A%255B%255D%257D%26props%3D%257B%2522w%2522%253A151%252C%2522h%2522%253A151%257D"
        ),
          grayInfo: GrayInfo(
            grayScaleId: "test_gray_scale_id",
            testValue: 1,
          )
      ));
    }
    return Future.value(const AiFrequency(
        grayInfo: GrayInfo(
          grayScaleId: "test_gray_scale_id",
          testValue: 1,
        )
    ));
  }

  @override
  Future<void> reportAiDialogDismiss() async {}
}

class AiControllerBridgeCommonMocker extends JoJoBridgeCommonMocker {
  Map<String, String> result = {};

  @override
  Future<JoJoBridgeResponse<NativeValue>> operationNativeValueGet(
      {required String key, bool? isUserData}) {
    return Future.value(
        JoJoBridgeResponse(200, 'ok', NativeValue(value: result[key] ?? "")));
  }

  @override
  Future<JoJoBridgeResponse<void>> operationNativeValueSet(
      {required String key, required String value, bool? isUserData}) {
    result[key] = value;
    return Future.value(JoJoBridgeResponse(200, 'ok', null));
  }

  @override
  Future<JoJoBridgeResponse<void>> addPubcliAbV2GrayScale({required String grayScaleId, required int testValue}) {
    return Future.value(JoJoBridgeResponse(200, 'ok', null));
  }

  @override
  Future<JoJoBridgeResponse<NativeValue>> showAlertInManager({required String pagePath, required String dialogKey}) {
    return Future.value(
        JoJoBridgeResponse(200, 'ok', NativeValue(value: "1")));
  }
}

void main() {
  late AiJoJoController controller;
  late MockAIJiaoJiaoServiceApi mockServiceApi;

  setUp(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    JoJoNativeBridge.registerMocker(AiControllerBridgeCommonMocker());

    mockServiceApi = MockAIJiaoJiaoServiceApi(true,mockException: false);

    // 默认可展示弹窗
    await jojoNativeBridge.operationNativeValueSet(
      key: 'ai_jojo_phone_dialog_show_today',
      value: "",
    );

    controller = AiJoJoController(AiJoJoState(), mockServiceApi,MockCacheManager());
  });

  tearDown(() {
    controller.close();
  });

  group('reportAiDialogDismiss()', () {
    test('调用 reportAiDialogDismiss 应触发服务层上报', () async {
      AiJoJoController localController =  AiJoJoController(AiJoJoState(), MockAIJiaoJiaoServiceApi(false,mockException: true),MockCacheManager());
      await localController.refresh();
      expect(localController.state.aiFrequency == null, true);
    });
  });

  group('reportAiDialogDismiss()', () {
    test('调用 reportAiDialogDismiss 应触发服务层上报', () async {
      await controller.reportAiDialogDismiss(AiDialogDismissReason.callStart, "roomTaskId");

    });
  });

  group('refresh()', () {
    test('今天不可展示时不请求接口', () async {
      final now = DateTime.now();
      final today = '${now.year}-${now.month}-${now.day}';

      await jojoNativeBridge.operationNativeValueSet(
        key: 'ai_jojo_phone_dialog_show_today',
        value: "test_uid_${today}_123",
      );

      await controller.refresh();
    });
  });

  group('safeEmit()', () {
    test('当 Cubit 关闭后不再 emit 新状态', () async {
      controller.close();
      final state = AiJoJoState().copyWith(canShow: true);
      controller.safeEmit(state);
      expect(controller.state != state, true);
    });

    test('未关闭时正常 emit 新状态', () async {
      final state = AiJoJoState().copyWith(canShow: true);
      controller.safeEmit(state);
      expect(controller.state, equals(state));
    });
  });

  group('dismiss()', () {
    test('调用 dismiss 应隐藏弹窗', () async {
      controller.emit(AiJoJoState().copyWith(canShow: true));
      await controller.dismiss();
      expect(controller.state.canShow, false);
    });
  });

  group('collectNeedDownAudios()', () {
    test('收集音频 URL 列表', () {
      final paramsVo = EncouragementConversationParamsVo()
        ..encouragementConversationBgm = 'https://bgm.mp3'
        ..encouragementConversationVoice = 'https://voice.mp3';

      final aiFrequency =
      AiFrequency(encouragementConversationParamsVo: paramsVo);

      final urls = controller.collectNeedDownAudios(aiFrequency);
      expect(urls, contains('https://bgm.mp3'));
      expect(urls, contains('https://voice.mp3'));
    });

    test('空值时返回空列表', () {
      final aiFrequency = AiFrequency(
          encouragementConversationParamsVo:
          EncouragementConversationParamsVo());

      final urls = controller.collectNeedDownAudios(aiFrequency);
      expect(urls, isEmpty);
    });
  });
}
