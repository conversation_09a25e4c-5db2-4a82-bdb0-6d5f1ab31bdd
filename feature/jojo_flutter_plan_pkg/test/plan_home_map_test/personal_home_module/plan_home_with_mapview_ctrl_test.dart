import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/growth_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/personal_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/subject_type_modules_info_data.dart';
import 'package:jojo_flutter_plan_pkg/service/find_study_partner_api.dart';
import 'package:mockito/annotations.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/controller.dart';
import 'package:jojo_flutter_plan_pkg/service/personal_home_info_api.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_page_api.dart';
import 'package:mockito/mockito.dart';

class MockPersonalHomeApi extends Mock implements PersonalHomeApi {
  @override
  Future<PersonalInfo> requestPersonalHomeInfo(
      int? partnerId, String? classKey) {
    Map<String, dynamic> personalInfo = {
      "nickname": "测试kijjik",
      "onlyTrainingCamp": 0,
      "studyDays": 203,
      "dressImg":
          "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812727199036508163/17504092746817tidf9.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
      "like": 1,
      "likeDescription": "1",
      "newDressUp": 1,
      "dressUpIcon": "https://jojostorage.tinman.cn/jojo/dress_up_image.png",
      "newDressUpIcon":
          "https://jojostorage.tinman.cn/jojo/study_page_new_dress_up.png",
      "messageInfo": {
        "unreadNum": 0,
        "jumpRoute":
            "tinman-router://cn.tinman.jojoread/flutter/plan/partnerMessagePage"
      },
      "entranceList": [
        {
          "type": "flower",
          "icon":
              "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/816670509807647745/20230206212029.jpg?checksumV2=md5Hex%3D770c5ad941afdb94fdf445554a11d82e",
          "name": "花花",
          "desc": "2222",
          "num": 0,
          "lastNum": 0
        }
      ],
      "partnerStatus": -1
    };
    return Future.value(PersonalInfo.fromJson(personalInfo));
  }

  @override
  Future<CourseSubjectTabData> getUserSubjectClass(int? partnerId) {
    Map<String, dynamic> courseSubjectTabData = {
      "subjectClassList": [
        {
          "subjectType": 1,
          "selected": true,
          "subjectStatus": 1,
          "bubbleList": null,
          "userClassVoList": [
            {
              "tabTitle": "思维全年系统包G5暑",
              "classId": 49822,
              "classKey": "27346_3247",
              "courseId": 6458,
              "courseKey": "27346",
              "classStatus": 1,
              "bubbleList": null,
              "startClassTime": 1635696000000,
              "courseSegment": "益智的系列",
              "courseSegmentCode": 37,
              "courseType": 3
            }
          ],
          "own": true,
          "specialCourseVo": null,
          "giftCourseVo": null,
          "afterServerEntranceVo": {
            "title": "学后服务",
            "icon": null,
            "route":
                "tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=1&loadingScene=5",
            "toastList": ["报告更新啦"],
            "configKey": null
          }
        },
        {
          "subjectType": 2,
          "selected": null,
          "subjectStatus": 1,
          "bubbleList": null,
          "userClassVoList": [
            {
              "tabTitle": "阅读全年系统包-7阶",
              "classId": 50860,
              "classKey": "20074_5482",
              "courseId": 74,
              "courseKey": "20074",
              "classStatus": 1,
              "bubbleList": null,
              "startClassTime": 1730995200000,
              "courseSegment": "L7",
              "courseSegmentCode": 6,
              "courseType": 3
            }
          ],
          "own": true,
          "specialCourseVo": null,
          "giftCourseVo": null,
          "afterServerEntranceVo": {
            "title": "学后服务",
            "icon": null,
            "route":
                "tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=2&loadingScene=2",
            "toastList": ["报告更新啦"],
            "configKey": null
          }
        },
        {
          "subjectType": 6,
          "selected": null,
          "subjectStatus": 2,
          "bubbleList": null,
          "userClassVoList": [
            {
              "tabTitle": "英语全年系统包-K2",
              "classId": 61973,
              "classKey": "27028_1418",
              "courseId": 6139,
              "courseKey": "27028",
              "classStatus": 4,
              "bubbleList": null,
              "startClassTime": 1730563200000,
              "courseSegment": "K2",
              "courseSegmentCode": 42,
              "courseType": 3
            }
          ],
          "own": true,
          "specialCourseVo": null,
          "giftCourseVo": null,
          "afterServerEntranceVo": null
        },
        {
          "subjectType": 3,
          "selected": null,
          "subjectStatus": 1,
          "bubbleList": null,
          "userClassVoList": [
            {
              "tabTitle": "创作全年系统包-W系列",
              "classId": 61977,
              "classKey": "27448_5",
              "courseId": 6571,
              "courseKey": "27448",
              "classStatus": 3,
              "bubbleList": null,
              "startClassTime": 1719504000000,
              "courseSegment": "W系列",
              "courseSegmentCode": 41,
              "courseType": 3
            }
          ],
          "own": true,
          "specialCourseVo": null,
          "giftCourseVo": null,
          "afterServerEntranceVo": null
        },
        {
          "subjectType": 4,
          "selected": null,
          "subjectStatus": 1,
          "bubbleList": [
            {
              "bubbleType": 1,
              "bubble": "新获得",
              "callBackType": 67,
              "order": 1,
              "classIdList": null
            },
            {
              "bubbleType": 2,
              "bubble": "新计划",
              "callBackType": 68,
              "order": 2,
              "classIdList": [66248]
            }
          ],
          "userClassVoList": [
            {
              "tabTitle": "美育全年系统包-A3",
              "classId": 66248,
              "classKey": "23614_595",
              "courseId": 2683,
              "courseKey": "23614",
              "classStatus": 1,
              "bubbleList": [
                {
                  "bubbleType": 4,
                  "bubble": "新获得",
                  "callBackType": 69,
                  "order": 4,
                  "classIdList": null
                }
              ],
              "startClassTime": 1735660800000,
              "courseSegment": "A3",
              "courseSegmentCode": 25,
              "courseType": 3
            }
          ],
          "own": true,
          "specialCourseVo": null,
          "giftCourseVo": null,
          "afterServerEntranceVo": null
        }
      ],
      "subjectList": [
        {
          "subjectType": 2,
          "subjectName": "阅读",
          "subjectColor": "#FF9045",
          "loadingScene": 2
        },
        {
          "subjectType": 1,
          "subjectName": "思维",
          "subjectColor": "#33BBFF",
          "loadingScene": 5
        },
        {
          "subjectType": 3,
          "subjectName": "创作",
          "subjectColor": "#55CC3D",
          "loadingScene": 6
        },
        {
          "subjectType": 4,
          "subjectName": "美育",
          "subjectColor": "#AE84E3",
          "loadingScene": 3
        },
        {
          "subjectType": 6,
          "subjectName": "英语",
          "subjectColor": "#FF7A99",
          "loadingScene": 4
        },
        {
          "subjectType": 7,
          "subjectName": "专题",
          "subjectColor": "#FCDA00",
          "loadingScene": 9
        },
        {
          "subjectType": 13,
          "subjectName": "综合",
          "subjectColor": "#F5C400",
          "loadingScene": 1
        }
      ],
      "popupList": [],
      "subjectExtendVo": {
        "bufferText": "本课程已结束学习\\n收纳至：我的-全部课程>",
        "bufferRouter":
            "tinman-router://cn.tinman.jojoread/flutter/plan/allCourses?windowType=normal&deviceOrientation=auto",
        "noServiceText": "全部课程已结束学习\\n为您收纳至：我的-全部课程>",
        "noServiceTitle": "开启新阶段",
        "noServiceRouter":
            "tinman-router://cn.tinman.jojoread/flutter/plan/allCourses?windowType=normal&deviceOrientation=auto",
        "buttonText": "查看"
      }
    };
    return Future.value(CourseSubjectTabData.fromJson(courseSubjectTabData));
  }

  @override
  Future<SubjectTypeModulesInfo> requestSubjectTypeModulesInfo(
      int subjectType, int? partnerId) {
    Map<String, dynamic> subjectTypeModulesInfo = {
      "subjectType": 1,
      "subjectName": "思维",
      "modules": [
        {
          "type": "studyPartners",
          "name": "学伴圈子",
          "img": "",
          "jumpRoute":
              "tinman-router://cn.tinman.jojoread/flutter/plan/jojoStudyPartnerPage?subjectType=1&loadingScene=5&classKey=27346_3247",
          "partners": []
        },
        {
          "type": "continuousLearn",
          "name": "连续坚持学习",
          "icon":
              "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/811911087562588161/1750214690062t6j03i.png?checksumV2=md5Hex%3D623f5f7f1f7009167e1d7978ff7d9b44",
          "days": 0,
          "bestDays": 1,
          "jumpRoute":
              "tinman-router://cn.tinman.jojoread/flutter/plan/joContinuology?subjectType=1"
        },
        {
          "type": "growthData",
          "name": "成长曲线",
          "growthData": {"classId": 49822, "courseSegmentCode": 37}
        }
      ]
    };
    return Future.value(
        SubjectTypeModulesInfo.fromJson(subjectTypeModulesInfo));
  }

  @override
  Future<dynamic> getSingleAdContent(String configKey, String ciphertext,
      String deviceUniqueIdentifier, String appKey) {
    Map<String, dynamic> adContent = {
      "configValue": {
        "statisticInfoList": [
          {
            "statisticKey": "READ_WORD_COUNT",
            "statisticName": "阅读字数",
            "statisticType": 4,
            "statisticUnit": "字",
            "courseSegmentCodeList": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "9",
              "47"
            ]
          },
          {
            "statisticKey": "READ_EXPRESSION_FREQUENCY",
            "statisticName": "开口表达次数",
            "statisticType": 4,
            "statisticUnit": "次",
            "courseSegmentCodeList": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "9",
              "47"
            ]
          },
          {
            "statisticKey": "READ_INTERACTION_TIMES",
            "statisticName": "互动次数",
            "statisticType": 4,
            "statisticUnit": "次",
            "courseSegmentCodeList": ["1", "2", "3", "7"]
          },
          {
            "statisticKey": "MATH_G_ACCUMULATED_QUESTION_TYPES",
            "statisticName": "累计获得题型数",
            "statisticType": 5,
            "statisticUnit": "个",
            "courseSegmentCodeList": ["31", "32", "33", "34", "35", "36", "37"]
          },
          {
            "statisticKey": "MATH_S_ACCUMULATED_KNOWLEDGE_POINTS",
            "statisticName": "累计获得知识点数",
            "statisticType": 5,
            "statisticUnit": "个",
            "courseSegmentCodeList": ["18", "19", "20", "21", "32"]
          },
          {
            "statisticKey": "READ_WRITING_NUMBER",
            "statisticName": "创作篇数",
            "statisticType": 4,
            "statisticUnit": "篇",
            "courseSegmentCodeList": ["4", "5", "7", "9", "47", "6", "1"]
          },
          {
            "statisticKey": "MATH_LESSON_GRADE",
            "statisticName": "益智课时完成评级",
            "statisticType": 1,
            "statisticUnit": "次",
            "courseSegmentCodeList": ["35", "20", "32", "37"]
          },
          {
            "statisticKey": "ENGLISH_WORD_AMOUNT",
            "statisticName": "英语阅读量",
            "statisticType": 4,
            "statisticUnit": "字",
            "courseSegmentCodeList": ["42", "56", "51"]
          },
          {
            "statisticKey": "ENGLISH_WORD_COUNT",
            "statisticName": "英语单词数",
            "statisticType": 4,
            "statisticUnit": "个",
            "courseSegmentCodeList": ["42", "56", "51"]
          },
          {
            "statisticKey": "ENGLISH_SPEAK_TIMES",
            "statisticName": "英语开口次数",
            "statisticType": 4,
            "statisticUnit": "次",
            "courseSegmentCodeList": ["42", "56", "51"]
          },
          {
            "statisticKey": "WRITE_WRITING_TIMES",
            "statisticName": "写作创作次数",
            "statisticType": 3,
            "statisticUnit": "次",
            "courseSegmentCodeList": ["38", "39", "40", "41", "48"]
          },
          {
            "statisticKey": "WRITE_MATERIALS_AMOUNT",
            "statisticName": "写作素材积累量",
            "statisticType": 3,
            "statisticUnit": "个",
            "courseSegmentCodeList": ["38", "39", "40", "41", "48"]
          },
          {
            "statisticKey": "ART_WRITING_TIMES",
            "statisticName": "美育创作次数",
            "statisticType": 4,
            "statisticUnit": "次",
            "courseSegmentCodeList": ["25"]
          }
        ]
      },
      "name": "家长端阶段成长报告指标V1",
      "configKey": "GrowthReportStatisticKey",
      "channel": "",
      "adId": "2024121700003",
      "configTemplateKey": "GrowthReportStatisticKeyTemp"
    };
    return Future.value(adContent);
  }

  @override
  Future<ScheduleViewsData> getScheduleViews(
    int classId,
    int viewType,
    int? partnerId,
  ) {
    Map<String, dynamic> scheduleViewsData = {
      "periodLearningGrowthList": [
        {"startTime": 1635696000000, "endTime": 1636127999999},
        {"startTime": 1636128000000, "endTime": 1636559999999},
        {"startTime": 1636560000000, "endTime": 1636991999999},
        {"startTime": 1636992000000, "endTime": 1637423999999},
        {"startTime": 1637424000000, "endTime": 1637855999999},
        {"startTime": 1637856000000, "endTime": 1638287999999},
        {"startTime": 1638288000000, "endTime": 1638719999999},
        {"startTime": 1638720000000, "endTime": 1639151999999},
        {"startTime": 1639152000000, "endTime": 1639583999999},
        {"startTime": 1639584000000, "endTime": 1640015999999},
        {"startTime": 1640016000000, "endTime": 1640447999999},
        {"startTime": 1640448000000, "endTime": 1640879999999},
        {"startTime": 1640880000000, "endTime": 1641311999999},
        {"startTime": 1641312000000, "endTime": 1641743999999},
        {"startTime": 1641744000000, "endTime": 1642175999999},
        {"startTime": 1642176000000, "endTime": 1642607999999},
        {"startTime": 1642608000000, "endTime": 1643039999999},
        {"startTime": 1643040000000, "endTime": 1643471999999},
        {"startTime": 1643472000000, "endTime": 1643903999999},
        {"startTime": 1643904000000, "endTime": 1644335999999},
        {"startTime": 1644336000000, "endTime": 1644767999999},
        {"startTime": 1644768000000, "endTime": 1645199999999},
        {"startTime": 1645200000000, "endTime": 1645631999999},
        {"startTime": 1645632000000, "endTime": 1646063999999},
        {"startTime": 1646064000000, "endTime": 1646495999999},
        {"startTime": 1646496000000, "endTime": 1646927999999},
        {"startTime": 1646928000000, "endTime": 1647359999999},
        {"startTime": 1647360000000, "endTime": 1647791999999},
        {"startTime": 1647792000000, "endTime": 1648223999999},
        {"startTime": 1648224000000, "endTime": 1648655999999},
        {"startTime": 1648656000000, "endTime": 1649087999999},
        {"startTime": 1649088000000, "endTime": 1649519999999},
        {"startTime": 1649520000000, "endTime": 1649951999999},
        {"startTime": 1649952000000, "endTime": 1650383999999},
        {"startTime": 1650384000000, "endTime": 1650815999999},
        {"startTime": 1650816000000, "endTime": 1651247999999},
        {"startTime": 1651248000000, "endTime": 1651679999999},
        {"startTime": 1651680000000, "endTime": 1652111999999},
        {"startTime": 1652112000000, "endTime": 1652543999999},
        {"startTime": 1652544000000, "endTime": 1652975999999},
        {"startTime": 1652976000000, "endTime": 1653407999999},
        {"startTime": 1653408000000, "endTime": 1653839999999},
        {"startTime": 1653840000000, "endTime": 1654271999999},
        {"startTime": 1654272000000, "endTime": 1654703999999},
        {"startTime": 1654704000000, "endTime": 1655135999999},
        {"startTime": 1655136000000, "endTime": 1655567999999},
        {"startTime": 1655568000000, "endTime": 1655999999999},
        {"startTime": 1656000000000, "endTime": 1656431999999},
        {"startTime": 1656432000000, "endTime": 1656863999999},
        {"startTime": 1656864000000, "endTime": 1657295999999},
        {"startTime": 1657296000000, "endTime": 1657727999999},
        {"startTime": 1657728000000, "endTime": 1658159999999},
        {"startTime": 1658160000000, "endTime": 1658591999999},
        {"startTime": 1658592000000, "endTime": 1659023999999},
        {"startTime": 1659024000000, "endTime": 1659455999999},
        {"startTime": 1659456000000, "endTime": 1659887999999},
        {"startTime": 1659888000000, "endTime": 1660319999999},
        {"startTime": 1660320000000, "endTime": 1660751999999},
        {"startTime": 1660752000000, "endTime": 1661183999999},
        {"startTime": 1661184000000, "endTime": 1661615999999},
        {"startTime": 1661616000000, "endTime": 1662047999999},
        {"startTime": 1662048000000, "endTime": 1662479999999},
        {"startTime": 1662480000000, "endTime": 1662911999999},
        {"startTime": 1662912000000, "endTime": 1663343999999},
        {"startTime": 1663344000000, "endTime": 1663775999999},
        {"startTime": 1663776000000, "endTime": 1664207999999},
        {"startTime": 1664208000000, "endTime": 1664639999999},
        {"startTime": 1664640000000, "endTime": 1665071999999},
        {"startTime": 1665072000000, "endTime": 1665503999999},
        {"startTime": 1665504000000, "endTime": 1665935999999},
        {"startTime": 1665936000000, "endTime": 1666367999999},
        {"startTime": 1666368000000, "endTime": 1666799999999},
        {"startTime": 1666800000000, "endTime": 1667231999999},
        {"startTime": 1667232000000, "endTime": 1667663999999},
        {"startTime": 1667664000000, "endTime": 1668095999999},
        {"startTime": 1668096000000, "endTime": 1668527999999},
        {"startTime": 1668528000000, "endTime": 1668959999999},
        {"startTime": 1668960000000, "endTime": 1669391999999},
        {"startTime": 1669392000000, "endTime": 1669823999999},
        {"startTime": 1669824000000, "endTime": 1670255999999},
        {"startTime": 1670256000000, "endTime": 1670687999999},
        {"startTime": 1670688000000, "endTime": 1671119999999},
        {"startTime": 1671120000000, "endTime": 1671551999999},
        {"startTime": 1671552000000, "endTime": 1671983999999},
        {"startTime": 1671984000000, "endTime": 1672415999999},
        {"startTime": 1672416000000, "endTime": 1672847999999},
        {"startTime": 1672848000000, "endTime": 1673279999999},
        {"startTime": 1673280000000, "endTime": 1673711999999},
        {"startTime": 1673712000000, "endTime": 1674143999999},
        {"startTime": 1674144000000, "endTime": 1674575999999},
        {"startTime": 1674576000000, "endTime": 1675007999999},
        {"startTime": 1675008000000, "endTime": 1675439999999},
        {"startTime": 1675440000000, "endTime": 1675871999999},
        {"startTime": 1675872000000, "endTime": 1676303999999},
        {"startTime": 1676304000000, "endTime": 1676735999999},
        {"startTime": 1676736000000, "endTime": 1677167999999},
        {"startTime": 1677168000000, "endTime": 1677599999999},
        {"startTime": 1677600000000, "endTime": 1678031999999},
        {"startTime": 1678032000000, "endTime": 1678463999999},
        {"startTime": 1678464000000, "endTime": 1678895999999},
        {"startTime": 1678896000000, "endTime": 1679327999999},
        {"startTime": 1679328000000, "endTime": 1679759999999},
        {"startTime": 1679760000000, "endTime": 1680191999999},
        {"startTime": 1680192000000, "endTime": 1680623999999},
        {"startTime": 1680624000000, "endTime": 1681055999999},
        {"startTime": 1681056000000, "endTime": 1681487999999},
        {"startTime": 1681488000000, "endTime": 1681919999999},
        {"startTime": 1681920000000, "endTime": 1682351999999},
        {"startTime": 1682352000000, "endTime": 1682783999999},
        {"startTime": 1682784000000, "endTime": 1683215999999},
        {"startTime": 1683216000000, "endTime": 1683647999999},
        {"startTime": 1683648000000, "endTime": 1684079999999},
        {"startTime": 1684080000000, "endTime": 1684511999999},
        {"startTime": 1684512000000, "endTime": 1684943999999},
        {"startTime": 1684944000000, "endTime": 1685375999999},
        {"startTime": 1685376000000, "endTime": 1685807999999},
        {"startTime": 1685808000000, "endTime": 1686239999999},
        {"startTime": 1686240000000, "endTime": 1686671999999},
        {"startTime": 1686672000000, "endTime": 1687103999999},
        {"startTime": 1687104000000, "endTime": 1687535999999},
        {"startTime": 1687536000000, "endTime": 1687967999999},
        {"startTime": 1687968000000, "endTime": 1688399999999},
        {"startTime": 1688400000000, "endTime": 1688831999999},
        {"startTime": 1688832000000, "endTime": 1689263999999},
        {"startTime": 1689264000000, "endTime": 1689695999999},
        {"startTime": 1689696000000, "endTime": 1690127999999},
        {"startTime": 1690128000000, "endTime": 1690559999999},
        {"startTime": 1690560000000, "endTime": 1690991999999},
        {"startTime": 1690992000000, "endTime": 1691423999999},
        {"startTime": 1691424000000, "endTime": 1691855999999},
        {"startTime": 1691856000000, "endTime": 1692287999999},
        {"startTime": 1692288000000, "endTime": 1692719999999},
        {"startTime": 1692720000000, "endTime": 1693151999999},
        {"startTime": 1693152000000, "endTime": 1693583999999},
        {"startTime": 1693584000000, "endTime": 1694015999999},
        {"startTime": 1694016000000, "endTime": 1694447999999},
        {"startTime": 1694448000000, "endTime": 1694879999999},
        {"startTime": 1694880000000, "endTime": 1695311999999},
        {"startTime": 1695312000000, "endTime": 1695743999999},
        {"startTime": 1695744000000, "endTime": 1696175999999},
        {"startTime": 1696176000000, "endTime": 1696607999999},
        {"startTime": 1696608000000, "endTime": 1697039999999},
        {"startTime": 1697040000000, "endTime": 1697471999999},
        {"startTime": 1697472000000, "endTime": 1697903999999},
        {"startTime": 1697904000000, "endTime": 1698335999999},
        {"startTime": 1698336000000, "endTime": 1698767999999},
        {"startTime": 1698768000000, "endTime": 1699199999999},
        {"startTime": 1699200000000, "endTime": 1699631999999},
        {"startTime": 1699632000000, "endTime": 1700063999999},
        {"startTime": 1700064000000, "endTime": 1700495999999},
        {"startTime": 1700496000000, "endTime": 1700927999999},
        {"startTime": 1700928000000, "endTime": 1701359999999},
        {"startTime": 1701360000000, "endTime": 1701791999999},
        {"startTime": 1701792000000, "endTime": 1702223999999},
        {"startTime": 1702224000000, "endTime": 1702655999999},
        {"startTime": 1702656000000, "endTime": 1703087999999},
        {"startTime": 1703088000000, "endTime": 1703519999999},
        {"startTime": 1703520000000, "endTime": 1703951999999},
        {"startTime": 1703952000000, "endTime": 1704383999999},
        {"startTime": 1704384000000, "endTime": 1704815999999},
        {"startTime": 1704816000000, "endTime": 1705247999999},
        {"startTime": 1705248000000, "endTime": 1705679999999},
        {"startTime": 1705680000000, "endTime": 1706111999999},
        {"startTime": 1706112000000, "endTime": 1706543999999},
        {"startTime": 1706544000000, "endTime": 1706975999999},
        {"startTime": 1706976000000, "endTime": 1707407999999},
        {"startTime": 1707408000000, "endTime": 1707839999999},
        {"startTime": 1707840000000, "endTime": 1708271999999},
        {"startTime": 1708272000000, "endTime": 1708703999999},
        {"startTime": 1708704000000, "endTime": 1709135999999},
        {"startTime": 1709136000000, "endTime": 1709567999999},
        {"startTime": 1709568000000, "endTime": 1709999999999},
        {"startTime": 1710000000000, "endTime": 1710431999999},
        {"startTime": 1710432000000, "endTime": 1710863999999},
        {"startTime": 1710864000000, "endTime": 1711295999999},
        {"startTime": 1711296000000, "endTime": 1711727999999},
        {"startTime": 1711728000000, "endTime": 1712159999999},
        {"startTime": 1712160000000, "endTime": 1712591999999},
        {"startTime": 1712592000000, "endTime": 1713023999999},
        {"startTime": 1713024000000, "endTime": 1713455999999},
        {"startTime": 1713456000000, "endTime": 1713887999999},
        {"startTime": 1713888000000, "endTime": 1714319999999},
        {"startTime": 1714320000000, "endTime": 1714751999999},
        {"startTime": 1714752000000, "endTime": 1715183999999},
        {"startTime": 1715184000000, "endTime": 1715615999999},
        {"startTime": 1715616000000, "endTime": 1716047999999},
        {"startTime": 1716048000000, "endTime": 1716479999999},
        {"startTime": 1716480000000, "endTime": 1716911999999},
        {"startTime": 1716912000000, "endTime": 1717343999999},
        {"startTime": 1717344000000, "endTime": 1717775999999},
        {"startTime": 1717776000000, "endTime": 1718207999999},
        {"startTime": 1718208000000, "endTime": 1718639999999},
        {"startTime": 1718640000000, "endTime": 1719071999999},
        {"startTime": 1719072000000, "endTime": 1719503999999},
        {"startTime": 1719504000000, "endTime": 1719935999999},
        {"startTime": 1719936000000, "endTime": 1720367999999},
        {"startTime": 1720368000000, "endTime": 1720799999999},
        {"startTime": 1720800000000, "endTime": 1721231999999},
        {"startTime": 1721232000000, "endTime": 1721663999999},
        {"startTime": 1721664000000, "endTime": 1722095999999},
        {"startTime": 1722096000000, "endTime": 1722527999999},
        {"startTime": 1722528000000, "endTime": 1722959999999},
        {"startTime": 1722960000000, "endTime": 1723391999999},
        {"startTime": 1723392000000, "endTime": 1723823999999},
        {"startTime": 1723824000000, "endTime": 1724255999999},
        {"startTime": 1724256000000, "endTime": 1724687999999},
        {"startTime": 1724688000000, "endTime": 1725119999999},
        {"startTime": 1725120000000, "endTime": 1725551999999},
        {"startTime": 1725552000000, "endTime": 1725983999999},
        {"startTime": 1725984000000, "endTime": 1726415999999},
        {"startTime": 1726416000000, "endTime": 1726847999999},
        {"startTime": 1726848000000, "endTime": 1727279999999},
        {"startTime": 1727280000000, "endTime": 1727711999999},
        {"startTime": 1727712000000, "endTime": 1728143999999},
        {"startTime": 1728144000000, "endTime": 1728575999999},
        {"startTime": 1728576000000, "endTime": 1729007999999},
        {"startTime": 1729008000000, "endTime": 1729439999999},
        {"startTime": 1729440000000, "endTime": 1729871999999},
        {"startTime": 1729872000000, "endTime": 1730303999999},
        {"startTime": 1730304000000, "endTime": 1730735999999},
        {"startTime": 1730736000000, "endTime": 1731167999999},
        {"startTime": 1731168000000, "endTime": 1731599999999},
        {"startTime": 1731600000000, "endTime": 1732031999999},
        {"startTime": 1732032000000, "endTime": 1732463999999},
        {"startTime": 1732464000000, "endTime": 1732895999999},
        {"startTime": 1732896000000, "endTime": 1733327999999},
        {"startTime": 1733328000000, "endTime": 1733759999999},
        {"startTime": 1733760000000, "endTime": 1734191999999},
        {"startTime": 1734192000000, "endTime": 1734623999999},
        {"startTime": 1734624000000, "endTime": 1735055999999},
        {"startTime": 1735056000000, "endTime": 1735487999999},
        {"startTime": 1735488000000, "endTime": 1735919999999},
        {"startTime": 1735920000000, "endTime": 1736351999999},
        {"startTime": 1736352000000, "endTime": 1736783999999},
        {"startTime": 1736784000000, "endTime": 1737215999999},
        {"startTime": 1737216000000, "endTime": 1737647999999},
        {"startTime": 1737648000000, "endTime": 1738079999999},
        {"startTime": 1738080000000, "endTime": 1738511999999},
        {"startTime": 1738512000000, "endTime": 1738943999999},
        {"startTime": 1738944000000, "endTime": 1739375999999},
        {"startTime": 1739376000000, "endTime": 1739807999999},
        {"startTime": 1739808000000, "endTime": 1740239999999},
        {"startTime": 1740240000000, "endTime": 1740671999999},
        {"startTime": 1740672000000, "endTime": 1741103999999},
        {"startTime": 1741104000000, "endTime": 1741535999999},
        {"startTime": 1741536000000, "endTime": 1741967999999},
        {"startTime": 1741968000000, "endTime": 1742399999999},
        {"startTime": 1742400000000, "endTime": 1742831999999},
        {"startTime": 1742832000000, "endTime": 1743263999999},
        {"startTime": 1743264000000, "endTime": 1743695999999},
        {"startTime": 1743696000000, "endTime": 1744127999999},
        {"startTime": 1744128000000, "endTime": 1744559999999},
        {"startTime": 1744560000000, "endTime": 1744991999999},
        {"startTime": 1744992000000, "endTime": 1745423999999},
        {"startTime": 1745424000000, "endTime": 1745855999999},
        {"startTime": 1745856000000, "endTime": 1746287999999},
        {"startTime": 1746288000000, "endTime": 1746719999999},
        {"startTime": 1746720000000, "endTime": 1747151999999},
        {"startTime": 1747152000000, "endTime": 1747583999999},
        {"startTime": 1747584000000, "endTime": 1748015999999},
        {"startTime": 1748016000000, "endTime": 1748447999999},
        {"startTime": 1748448000000, "endTime": 1748879999999},
        {"startTime": 1748880000000, "endTime": 1749311999999},
        {"startTime": 1749312000000, "endTime": 1749743999999},
        {"startTime": 1749744000000, "endTime": 1750175999999},
        {"startTime": 1750176000000, "endTime": 1750607999999},
        {"startTime": 1750608000000, "endTime": 1751039999999},
        {"startTime": 1751040000000, "endTime": 1751471999999}
      ]
    };
    return Future.value(ScheduleViewsData.fromJson(scheduleViewsData));
  }

  @override
  Future<PeriodDataListData> getGrowthReportData(
      int classId,
      String statisticKey,
      int statisticType,
      int startTime,
      int endTime,
      int? partnerId) {
    Map<String, dynamic> growThteportData = {
      "periodDataList": [
        {
          "periodName": "累计获得题型数",
          "periodPerformance": 5,
          "performanceType": "MATH_G_ACCUMULATED_QUESTION_TYPES"
        }
      ]
    };
    return Future.value(PeriodDataListData.fromJson(growThteportData));
  }
}

class MockHomeMapPageApi extends Mock implements HomeMapPageApi {}

class MockFindPartnerApi extends Mock implements FindStudyPartnerApi {
  @override
  Future<dynamic> addPartner(int targetId) {
    return Future.value({});
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late PersonalHomePageCtrl controller;
  late MockPersonalHomeApi mockPersonalHomeApi;
  late MockHomeMapPageApi mockHomeMapPageApi;
  late MockFindPartnerApi mockFindPartnerApi;

  const testPartnerId = 123456;
  const testClassKey = 'test_class_key_123';
  const testSubjectType = 1;
  const testClassId = 12;

  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
    mockPersonalHomeApi = MockPersonalHomeApi();
    mockHomeMapPageApi = MockHomeMapPageApi();
    mockFindPartnerApi = MockFindPartnerApi();
  });

  tearDown(() {
    reset(mockPersonalHomeApi);
    reset(mockHomeMapPageApi);
  });

  group('PersonalHomePageCtrl Tests', () {
    group('Constructor tests', () {
      test('should initialize with provided classKey and partnerId', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );

        expect(controller.classKey, equals(testClassKey));
        expect(controller.partnerId, equals(testPartnerId));
        expect(controller.defaultSubjectType, equals(testSubjectType));
      });

      test('should initialize with null classKey', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: null,
        );

        expect(controller.classKey, isNull);
        expect(controller.partnerId, equals(testPartnerId));
      });

      test('should initialize with null partnerId', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: null,
          classkey: testClassKey,
        );

        expect(controller.classKey, equals(testClassKey));
        expect(controller.partnerId, isNull);
      });

      test('should initialize with zero partnerId', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: 0,
          classkey: testClassKey,
        );

        expect(controller.classKey, equals(testClassKey));
        expect(controller.partnerId, equals(0));
      });
    });

    group('onGrowthRetry method tests', () {
      test('should return early when partnerId is not null', () async {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );

        controller.onGrowthRetry(testSubjectType, null);
        await Future.delayed(Duration(milliseconds: 10));
        expect(controller.state.periodLearningGrowthList, isNotNull);
      });

      test('should proceed when partnerId is null', () async {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: null,
          classkey: testClassKey,
        );

        controller.onGrowthRetry(testSubjectType, null);
        await Future.delayed(Duration(milliseconds: 10));

        // 验证方法被调用（因为partnerId为null，方法会继续执行）
        expect(controller.partnerId, isNull);
      });
    });

    group('getClassSchedule Tests', () {
      test(
          'should update state with periodLearningGrowthList when API call is successful',
          () async {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        await controller.getClassSchedule(testSubjectType, testClassId);
        expect(controller.state.periodLearningGrowthList, isNotNull);
      });
    });

    group('addPartner Tests', () {
      test(
          'should update state with addPartner when API call is successful',
          () async {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          apiPartner: mockFindPartnerApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        await controller.addPartner();
        expect(controller.state.personalInfo?.partnerStatus, isNull);
      });
       test(
          'should update state with addPartner when partnerId call is successful',
          () async {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          apiPartner: mockFindPartnerApi,
          subjectype: testSubjectType,
          partnerid: null,
          classkey: testClassKey,
        );
        await controller.addPartner();
        expect(controller.partnerId, isNull);
      });
    });
    group('getRealNum method tests', () {
      test('should return currentNum when lastNum equals currentNum', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试 lastNum == currentNum 的情况
        final info = EntranceInfo(
          type: "flower",
          num: 5,
          lastNum: 5,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(5));
      });

      test('should return currentNum when type is not flower', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试 type != "flower" 的情况
        final info = EntranceInfo(
          type: "medal",
          num: 10,
          lastNum: 8,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(10));
      });

      test('should return currentNum when type is flower and lastNum > currentNum', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试 type == "flower" 且 lastNum > currentNum 的情况
        final info = EntranceInfo(
          type: "flower",
          num: 3,
          lastNum: 7,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(3)); // 返回较小的 currentNum
      });

      test('should return lastNum when type is flower and lastNum < currentNum', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试 type == "flower" 且 lastNum < currentNum 的情况
        final info = EntranceInfo(
          type: "flower",
          num: 12,
          lastNum: 8,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(8)); // 返回较小的 lastNum
      });

      test('should handle null values correctly', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试 null 值的处理
        final info = EntranceInfo(
          type: "flower",
          num: null,
          lastNum: null,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(0)); // null 值被处理为 0
      });

      test('should handle zero values', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试零值
        final info = EntranceInfo(
          type: "flower",
          num: 0,
          lastNum: 0,
        );
        
        final result = controller.getRealNum(info);
        
        expect(result, equals(0)); // lastNum == currentNum，返回 currentNum
      });

      test('should handle zero values', () {
        controller = PersonalHomePageCtrl(
          apiPersonalHome: mockPersonalHomeApi,
          apiHome: mockHomeMapPageApi,
          subjectype: testSubjectType,
          partnerid: testPartnerId,
          classkey: testClassKey,
        );
        // 测试零值
        final info = EntranceInfo(
          type: "flower",
          num: 0,
          lastNum: 0,
        );
        controller.requestTabsubjectInfoWithSubjectType(2);
      });
    });
  });
}
