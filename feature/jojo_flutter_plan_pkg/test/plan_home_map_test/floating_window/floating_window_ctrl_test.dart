import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/course_float_window/controller.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([])
void main() {
  late CourseFloatWindowCtrl controller;

  setUp(() {
    controller = CourseFloatWindowCtrl();
  });

  group('CourseFloatWindowCtrl  tests', () {
    test('updateData test', () {
      controller.updateData();
    });
  });
}
