import 'dart:async';
import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/dailytask/dialog/course_dialog_helper.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_advertisements_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_page_tab_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_subject_clean_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/home_personal_image_guide_dialog.dart';
import 'package:mockito/mockito.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/state.dart';
import 'package:jojo_flutter_plan_pkg/service/home_map_page_api.dart';

// Mock 依赖项
class MockHomeMapPageApi extends Mock implements HomeMapPageApi {
  @override
  Future<CourseSubjectTabData> getUserSubjectClass() {
    return Future.value(CourseSubjectTabData.fromJson(json.decode(
        '{"subjectClassList":[{"subjectType":4,"selected":null,"subjectStatus":3,"bubbleList":null,"userClassVoList":[],"own":true,"specialCourseVo":null,"afterServerEntranceVo":null},{"subjectType":2,"selected":null,"subjectStatus":1,"bubbleList":null,"userClassVoList":[],"own":true,"specialCourseVo":{"tabTitle":"专项提升包","courseInfoList":[{"courseName":"阅读全年系统包-8阶","courseIcon":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/400646049726119936/165216146095400015e057a50294e3e3c8230d2c0fea8.png","finishLessonNum":0,"totalLessonNum":68,"courseId":3459,"courseKey":"24357","classId":58059,"classKey":"24357_366","route":"tinman-router://cn.tinman.jojoread/flutter/plan/lessonList?windowType=normal&deviceOrientation=auto&classKey=24357_366&loadingScene=2","classStatus":1}],"subjectStatus":1,"bufferDesc":"阅读全年系统包-3阶已收纳至：我的—全部课程"},"afterServerEntranceVo":{"title":"学后服务","icon":null,"route":"tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=2&loadingScene=2","toastList":["报告更新啦"],"configKey":null}},{"subjectType":1,"selected":null,"subjectStatus":3,"bubbleList":null,"userClassVoList":[],"own":true,"specialCourseVo":null,"afterServerEntranceVo":{"title":"学后服务","icon":null,"route":"tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=1&loadingScene=5","toas tList":[],"configKey":null}},{"subjectType":6,"selected":true,"subjectStatus":1,"bubbleList":null,"userClassVoList":[{"tabTitle":"E1 英语年课","classId":59985,"classKey":"50696_861","courseId":7356,"courseKey":"50696","classStatus":1,"bubbleList":null,"startClassTime":1743609600000,"courseSegment":"E1","courseSegmentCode":51,"courseType":3}],"own":true,"specialCourseVo":null,"afterServerEntranceVo":{"title":"学后服务","icon":null,"route":"tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=6&loadingScene=4","toastList":[],"configKey":null}},{"subjectType":3,"selected":null,"subjectStatus":3,"bubbleList":null,"userClassVoList":[],"own":true,"specialCourseVo":null,"afterServerEntranceVo":{"title":"学后服务","icon":null,"route":"tinman-router://cn.tinman.jojoread/flutter/plan/afterClassListPage?deviceOrientation=auto&windowType=normal&subjectType=3&loadingScene=6","toastList":[],"configKey":null}}],"subjectList":[{"subjectType":2,"subjectName":"阅读","subjectColor":"#FF9045","loadingScene":2},{"subjectType":1,"subjectName":"思维","subjectColor":"#33BBFF","loadingScene":5},{"subjectType":3,"subjectName":"创作","subjectColor":"#55CC3D","loadingScene":6},{"subjectType":4,"subjectName":"美育","subjectColor":"#AE84E3","loadingScene":3},{"subjectType":6,"subjectName":"英语","subjectColor":"#FF7A99","loadingScene":4},{"subjectType":7,"subjectName":"专题","subjectColor":"#FCDA00","loadingScene":9},{"subjectType":13,"subjectName":"综合","subjectColor":"#FCDA00","loadingScene":1}],"popupList":[],"subjectExtendVo":{"bufferText":"本课程已结束学习\\n收纳至：我的-全部课程>","bufferRouter":"tinman-router://cn.tinman.jojoread/flutter/plan/allCourses?windowType=normal&deviceOrientation=auto","noServiceText":"全部课程已结束学习\\n为您收纳至：我的-全部课程>","noServiceTitle":"开启新阶段","noServiceRouter":"tinman-router://cn.tinman.jojoread/flutter/plan/allCourses?windowType=normal&deviceOrientation=auto","buttonText":"查看"}}')));
  }

  @override
  Future<CourseAdvertisementsData> requestAdvertisements(
      {int? subjectId, int? courseId, String scene = 'subjectRmd'}) {
    return Future.value(const CourseAdvertisementsData());
  }

  @override
  Future<TopUserInfo> requestTopUserInfo(int userId) {
    return Future.value(TopUserInfo.fromJson(json.decode(
        '{"dress":{"img":"https://userfiles.beta.jojoread.tinman.cn/avatar/baby-avatar/personal-image/807205713567707137.png?Expires=1749199261&OSSAccessKeyId=LTAI4GKMoWSvaXqCF6NWYyGz&Signature=lZEqUT3Ie3i3sCQJQY%2BCTYOmAEU%3D&checksumV2=md5Hex%3D92a56b412353c42607afb4bcccb54c05%2Ccrc64%3D17093049102559558165","jumpRoute":"tinman-router://cn.tinman.jojoread/flutter/personal/home?subjectType=4"},"continuous":{"days":0,"isStart":1,"isFinishLesson":1,"isFinishLessonPopup":0,"notStartText":"连胜之旅待开启","icon":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/787355161324597249/1744360097447i8jif2.png?checksumV2=md5Hex%3Dae12c3d4c7a8194ab30f40a0784dd341","resource":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/802274979186897923/jiaojiao.zip?checksumV2=md5Hex%3D66d7d6a852c5973aab12486935371653","clientDynamic":"trance","status":3,"jumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/joContinuology?subjectType=4"},"learnBean":{"type":1,"isShow":1,"amount":9722,"icon":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/801933251510694913/1747835794707m0ezje.png?checksumV2=md5Hex%3Db352d0191c409774fa0ddada74467c14","jumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/joJoShop?subjectType=4"},"medal":{"isShow":0,"amount":0,"icon":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/801933209521516545/1747835783164yscgfj.png?checksumV2=md5Hex%3D1e5175a8021358646a99419b3d42043f","jumpRoute":"tinman-router://cn.tinman.jojoread/flutter/plan/myAchievements?subjectType=4"},"newDressUpNotice":{"newDressUp":1,"newDressUpIcon":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/mock/806241244267429889/1748862892124.png"}}')));
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late PlanHomeWithMapviewCtrl controller;
  late MockHomeMapPageApi mockHomeMapPageApi;

  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
    mockHomeMapPageApi = MockHomeMapPageApi();
    controller = PlanHomeWithMapviewCtrl(api: mockHomeMapPageApi);
  });

  tearDown(() {
    controller.dispose();
  });

  group('PlanHomeWithMapviewCtrl', () {
    test('onRefresh 成功加载数据', () async {
      // 调用刷新方法
      await controller.onRefresh();
      // 验证状态更新
      expect(controller.state.pageStatus.name, PageStatus.success.name);
    });

    test('personalImageRect eventBus', () async {
      final controller = PlanHomeWithMapviewCtrl();
      final testEvent = PersonalImageRectEvent(Rect.zero,
          isLandSpace: false, personImageKey: GlobalKey());
      // 添加监听
      controller.testableListenPersonalImageRect();
      jojoEventBus.fire(testEvent);
      // 等待事件触发完成
      await Future.delayed(const Duration(milliseconds: 10));

      expect(controller.personalImageRect, isNotNull);
      expect(controller.personalImageRect!.personImageKey,
          testEvent.personImageKey);
    });

    test('showAddTeacherDialog 返回预期结果', () async {
      final controller = PlanHomeWithMapviewCtrl();
      final isShow = await controller.showAddTeacherDialog(
          4, CourseTrainAddTeacherDialogHelper.readingReportManual);
      expect(isShow, isTrue);
    });

    test('refreshTopIncentiveData 返回预期结果', () async {
      await controller.refreshTopIncentiveData();
      expect(controller.state.showIncentiveModule, isTrue);
    });
  });
}

class JoJoEventBus {
}
