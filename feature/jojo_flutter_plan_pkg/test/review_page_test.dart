// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/model/review_assistant_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page_controller.dart';
import 'package:jojo_flutter_plan_pkg/service/review_assistant_api.dart';
import 'package:mockito/mockito.dart';

// 创建 Dio 的 Mock 类
class MockReviewPageApiService extends Mock implements ReviewAssistantApi {
  final CourseSegmentsData segmentsData;
  final CourseLessonsData lessonsData;

  MockReviewPageApiService(this.segmentsData, this.lessonsData);

  @override
  Future<CourseLessonsData> getCourseLessonsInfo({required String courseKey, required String classKey, required String classId, required String segmentId, required String weekId}) {
    return Future.value(MockReviewPageApiService.mockPageLessonData());
  }

  static CourseSegmentsData? mockPageSegmentData() {
    Map<String, dynamic> jsonDate = {
    "segments": [
      {
        "segmentId": 3262,
        "segmentName": "G1上",
        "unFinishLessonNum": 13,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 505,
            "weekName": "第一单元测试",
            "unFinishLessonNum": 5
          },
          {
            "weekId": 875,
            "weekName": "第2单元",
            "unFinishLessonNum": 7
          }
        ]
      },
      {
        "segmentId": 3263,
        "segmentName": "G1寒-第二阶段",
        "unFinishLessonNum": 10,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 507,
            "weekName": "第三周",
            "unFinishLessonNum": 5
          },
          {
            "weekId": 508,
            "weekName": "第四周",
            "unFinishLessonNum": 5
          }
        ]
      },
      {
        "segmentId": 3264,
        "segmentName": "G1下-阶段三",
        "unFinishLessonNum": 39,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 864,
            "weekName": "第十周第十周",
            "unFinishLessonNum": 4
          },
          {
            "weekId": 853,
            "weekName": "第7周",
            "unFinishLessonNum": 2
          },
          {
            "weekId": 870,
            "weekName": "第三月第七个阶段测",
            "unFinishLessonNum": 5
          },
          {
            "weekId": 871,
            "weekName": "第三个月第八个阶段测",
            "unFinishLessonNum": 6
          },
          {
            "weekId": 872,
            "weekName": "第三月第九个阶段测",
            "unFinishLessonNum": 6
          },
          {
            "weekId": 509,
            "weekName": "第五周",
            "unFinishLessonNum": 3
          },
          {
            "weekId": 510,
            "weekName": "小小主持人",
            "unFinishLessonNum": 2
          },
          {
            "weekId": 862,
            "weekName": "这个测试的第八周",
            "unFinishLessonNum": 5
          },
          {
            "weekId": 863,
            "weekName": "第九周第九周第九周",
            "unFinishLessonNum": 6
          }
        ]
      },
      {
        "segmentId": 3265,
        "segmentName": "G1暑-阶段4",
        "unFinishLessonNum": 17,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 511,
            "weekName": "读三国故事",
            "unFinishLessonNum": 4
          }
        ]
      },
      {
        "segmentId": 3354,
        "segmentName": "G2上-阶段5作业上传",
        "unFinishLessonNum": 40,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 867,
            "weekName": "这个主题19个课时",
            "unFinishLessonNum": 19
          },
          {
            "weekId": 670,
            "weekName": "作业上传作业上传作业上传作业上传作业",
            "unFinishLessonNum": 10
          },
          {
            "weekId": 671,
            "weekName": "的",
            "unFinishLessonNum": 10
          }
        ]
      },
      {
        "segmentId": 3784,
        "segmentName": "G2寒-错题本",
        "unFinishLessonNum": 1,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 857,
            "weekName": "单独一个课时一个周",
            "unFinishLessonNum": 1
          }
        ]
      },
      {
        "segmentId": 4558,
        "segmentName": "G2下-阶段7",
        "unFinishLessonNum": 16,
        "courseChildType": 1,
        "weekList": [
          {
            "weekId": 868,
            "weekName": "这个单元有15个课时",
            "unFinishLessonNum": 15
          }
        ]
      }
    ]
  };
    CourseSegmentsData test = CourseSegmentsData.fromJson(jsonDate);
    return test;
  }

  static CourseLessonsData? mockPageLessonData() {
    Map<String, dynamic> jsonDate = {
    "list": [
      {
        "segmentId": 3262,
        "lessonInfos": [
          {
            "lessonName": "课时1",
            "lessonId": 10388,
            "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
            "lessonOrder": 1,
            "studyStatus": 1,
            "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
            "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
            "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0005&loadingScene=5&engineType=0",
            "finish": false
          },
          {
            "lessonName": "课时1副本2",
            "lessonId": 10390,
            "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
            "lessonOrder": 2,
            "studyStatus": 1,
            "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
            "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
            "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0007&loadingScene=5&engineType=0",
            "finish": false
          },
          {
            "lessonName": "type19形式4",
            "lessonId": 10389,
            "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/327107409893199872/16346284857644dc29973bd630e6349fad2432e24c894.png",
            "lessonOrder": 3,
            "studyStatus": 1,
            "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
            "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
            "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0006&loadingScene=5&engineType=0",
            "finish": false
          },
          {
            "lessonName": "配置大封面并且名称很长很长哈哈哈哈啦啦啦",
            "lessonId": 10391,
            "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/508289744293615616/16778257180827697475bd49e20a347027530329388e5.png",
            "lessonOrder": 4,
            "studyStatus": 1,
            "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
            "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
            "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0008&loadingScene=5&engineType=0",
            "finish": false
          },
          {
            "lessonName": "课时1副本4",
            "lessonId": 10392,
            "icon": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/377843017116427264/1646724845251de329816cb28da3cb1f9efad768110e3.png",
            "lessonOrder": 5,
            "studyStatus": 1,
            "studyTipsVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/1744341512698fc8ddt.mp3?checksumV2=crc64%3D13577210142955901011",
            "lockVoice": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/17443415126064bf8hq.mp3?checksumV2=crc64%3D8073808882569868043",
            "route": "tinman-router://cn.tinman.jojoread/flutter/plan/joCourseSessionList?deviceOrientation=landscape&windowType=normal&classKey=24086_267&lessonKey=31315_0009&loadingScene=5&engineType=0",
            "finish": false
          }
        ]
      }
    ]
  };
    CourseLessonsData test = CourseLessonsData.fromJson(jsonDate);
    return test;
  }
}

void main() {
  late ReviewDetailCtrl controller;
  late MockReviewPageApiService mockApiService;
  setUp(() {
    mockApiService =
        MockReviewPageApiService(MockReviewPageApiService.mockPageSegmentData()!,MockReviewPageApiService.mockPageLessonData()!);
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
  });
  
  group('review_controller tests:', () {
    test('emits error state when courseKey, classKey, or classId is missing', () async {

      controller = ReviewDetailCtrl(
        api: mockApiService,
        segmentId: 1,
        weekId: 1,
        subjectColor: null,
        courseKey: "",
        classKey: "",
        classId: "",
        buriedString: null,
      );

      await controller.getSegmentsInfo();
      expect(controller.state.pageStatus, PageStatus.error);
      expect(controller.state.exception.toString(), contains('请求参数为空'));
    });

    test('emits success when API returns valid CourseSegmentsData', () async {
      controller = ReviewDetailCtrl(
        api: mockApiService,
        segmentId: 1,
        weekId: 0,
        subjectColor: null,
        courseKey: "24086",
        classKey: "24086_267",
        classId: "18010",
        buriedString: null,
      );

      await controller.getSegmentsInfo();
    });

    test('changePageToPre does not change page when index is 0', () {
      controller.currentIndex = 0;
      controller.changePageToPre();
      expect(controller.currentIndex, 0);
    });

    test('changePageToNext increments page index when valid', () {
      controller.state.segmentList = [
        CourseSegmentData(segmentId: 1),
        CourseSegmentData(segmentId: 2)
      ];
      controller.currentIndex = 0;
      controller.changePageToNext();
      expect(controller.currentIndex, 1);
    });

    test('getLessonCourseCardInfo', () {
      CourseLessonData? data = mockApiService.lessonsData.list?.first;
      if (data != null) {
        CourseLessonItem item = data.lessonInfos?.first ?? CourseLessonItem(); 
        controller.getLessonCourseCardInfo(item);
      }
    });
  
     test('findCurrentLesson', () {
      CourseSegmentsData? data = mockApiService.segmentsData;
      controller = ReviewDetailCtrl(
        api: mockApiService,
        segmentId: 1,
        weekId: 0,
        subjectColor: null,
        courseKey: "24086",
        classKey: "24086_267",
        classId: "18010",
        buriedString: null,
      );
      
      List<CourseSegmentData> segments = [];
      data.segments?.forEach((element) {
        if (element != null) {
          segments.add(element);
        }
      });
      controller.dealWithData(data, controller.state);
      controller.findSegment(controller.state);
      controller.refresh();
      expect(controller.currentSegment != null, true);
    });

     test("getLessonsInfo", () async {
       controller = ReviewDetailCtrl(
         api: mockApiService,
         segmentId: 3262,
         weekId: 222,
         subjectColor: null,
         courseKey: "24086",
         classKey: "24086_267",
         classId: "18010",
         buriedString: null,
       );
       await controller.getLessonsInfo(3262, 222);
       expect(controller.state.lessonsMap != null, true);
     });

    test('updateCurrentSegment', () {
      CourseSegmentsData? data = mockApiService.segmentsData;
      controller = ReviewDetailCtrl(
        api: mockApiService,
        segmentId: 1,
        weekId: 0,
        subjectColor: null,
        courseKey: "24086",
        classKey: "24086_267",
        classId: "18010",
        buriedString: null,
      );
      
      List<CourseSegmentData> segments = [];
      data.segments?.forEach((element) {
        if (element != null) {
          segments.add(element);
        }
      });
      controller.dealWithData(data, controller.state);
      controller.state.segmentList = segments;
      controller.currentSegment = controller.state.segmentList?[2];
      controller.currentIndex = 0;
      controller.currentWeekId = 0;
      controller.findSegment(controller.state);
      controller.refresh();
      expect(controller.currentIndex != 0, true);
    });
  });
}
