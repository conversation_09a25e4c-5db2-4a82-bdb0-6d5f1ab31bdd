// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/controller.dart';

void main() {
  late PlanStudyPreferenceCtrl controller;
  setUp(() {
    String json = "%7B%22nodeId%22%3A21%2C%22classId%22%3A68191%2C%22classKey%22%3A%2220021_2752%22%2C%22toast%22%3Anull%2C%22serviceIcon%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F799689128380624897%2Fcc.png%3FchecksumV2%3Dmd5Hex%253Df8f6057c28bc8800355a19d16b310e14%22%2C%22serviceName%22%3A%22%E5%AD%A6%E4%B9%A0%E6%97%B6%E9%97%B4%E8%AE%BE%E7%BD%AE%EF%BC%88%E5%BF%85%E5%81%9A%EF%BC%89%22%2C%22serviceRouter%22%3Anull%2C%22serviceType%22%3A20%2C%22serviceStatue%22%3A1%2C%22popupInfo%22%3A%7B%22popupTitle%22%3A%22%E5%AD%A6%E4%B9%A0%E6%97%B6%E9%97%B4%E8%AE%BE%E7%BD%AE%22%2C%22introduceImageList%22%3Anull%2C%22buttonRoute%22%3Anull%2C%22buttonText%22%3A%22%E7%A1%AE%E5%AE%9A%22%2C%22closedText%22%3A%22%E5%AD%A6%E4%B9%A0%E6%97%B6%E9%97%B4%E5%B7%B2%E4%BF%9D%E5%AD%98%22%2C%22introduce%22%3A%22%E6%AF%8F%E5%91%A8%E4%B8%800%E7%82%B9%E8%A7%A3%E9%94%813%E8%8A%82%E6%96%B0%E5%86%85%E5%AE%B9%20%E3%80%82%E4%B8%BA%E4%BF%9D%E8%AF%81%E5%AD%A6%E4%B9%A0%E8%A7%84%E5%BE%8B%E9%9C%80%E8%AE%BE3%E4%B8%AA%E5%AD%A6%E4%B9%A0%E6%97%A5%EF%BC%8C%E6%9C%8D%E5%8A%A1%E5%9B%A2%E9%98%9F%E5%B0%86%EF%BC%9A%E2%9C%85%20%E6%8C%89%E6%97%B6%E6%8E%A8%E9%80%81%E5%AD%A6%E4%B9%A0%E6%8F%90%E9%86%92%EF%BC%9B%E2%9C%85%20%E5%B8%AE%E5%AD%A9%E5%AD%90%E5%BB%BA%E7%AB%8B%E3%80%8C%E8%A7%84%E5%BE%8B%E5%AD%A6%20-%20%E7%9F%A5%E8%AF%86%E6%B2%89%E6%B7%80%E3%80%8D%E9%97%AD%E7%8E%AF%E3%80%82%22%2C%22actionTitle%22%3A%22%E8%AF%B7%E8%AE%BE%E7%BD%AE%E6%AF%8F%E5%91%A8%E7%9A%84%E5%AD%A6%E4%B9%A0%E6%97%B6%E9%97%B4%22%2C%22actionTip%22%3A%22%E4%B8%BA%E4%BA%86%E7%9D%A3%E4%BF%83%E5%B0%8F%E6%9C%8B%E5%8F%8B%E5%85%BB%E6%88%90%E8%89%AF%E5%A5%BD%E7%9A%84%E5%AD%A6%E4%B9%A0%E4%B9%A0%E6%83%AF%EF%BC%8C%E5%BB%BA%E8%AE%AE%E6%82%A8%E6%AF%8F%E6%97%A5%E5%AD%A6%E4%B9%A01%E8%8A%82%E5%86%85%E5%AE%B9%22%2C%22hasSetting%22%3A0%2C%22options%22%3A%5B%7B%22navTitle%22%3A%22%E5%91%A8%E4%B8%80%E5%BC%80%E5%A7%8B%E5%AD%A6%22%2C%22navKey%22%3A1%2C%22hasChoice%22%3A0%2C%22contentImg%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F801105727474003969%2F1747638496642vw50rf.png%3FchecksumV2%3Dmd5Hex%253Ddf3ab1d25fbd4f7af85e408b207bc733%22%7D%2C%7B%22navTitle%22%3A%22%E5%91%A8%E4%B8%89%E5%BC%80%E5%A7%8B%E5%AD%A6%22%2C%22navKey%22%3A3%2C%22hasChoice%22%3A0%2C%22contentImg%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F801105803051168769%2F1747638509197tquyon.png%3FchecksumV2%3Dmd5Hex%253D617aa2c77d8507696f87083eeefcbd0b%22%7D%2C%7B%22navTitle%22%3A%22%E5%91%A8%E4%BA%94%E5%BC%80%E5%A7%8B%E5%AD%A6%22%2C%22navKey%22%3A5%2C%22hasChoice%22%3A0%2C%22contentImg%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F801105845044539393%2F1747638518640ubkwko.png%3FchecksumV2%3Dmd5Hex%253D25dd9978ccfb8bd2010d48341fb66bad%22%7D%5D%7D%2C%22userGifLessonCardListData%22%3A%5B%7B%22courseKey%22%3A%2220051%22%2C%22courseType%22%3A1%2C%22icon%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fcc%2Fcc-admin%2Fcourse%2F162258993410007040.png%22%2C%22title%22%3Anull%2C%22subTitle%22%3A%22%E3%80%8AL4-3%E6%97%A5%E8%AE%AD%E7%BB%83%E8%90%A5%E3%80%8B%22%2C%22bgImage%22%3A%22https%3A%2F%2Fjojopublicfat.jojoread.com%2Fvulcan%2Fnuwa-admin%2Fappicon%2F755798826334850049%2F173683648232126egfp.png%3FchecksumV2%3Dmd5Hex%253D66fc5dc6aeb7dd5f76e6360acc79d51f%22%2C%22buttonText%22%3A%22%E8%81%94%E7%B3%BB%E5%AE%A2%E6%9C%8D%22%2C%22unactivatedReason%22%3A%22%E6%9C%AC%E6%9C%9F%E6%8A%A5%E5%90%8D%E5%B7%B2%E7%BB%93%E6%9D%9F%22%2C%22jumpRouter%22%3A%22tinman-router%3A%2F%2Fcn.tinman.jojoread%2Fflutter%2Fplan%2FcustomerCenter%3FwindowType%3Dnormal%26deviceOrientation%3Dauto%22%2C%22bgColor%22%3A%22%23FCDA00%22%2C%22newClassIcon%22%3Anull%2C%22statusIcon%22%3Anull%2C%22taskStatus%22%3Anull%2C%22reportPointed%22%3Anull%2C%22newGetFlag%22%3Afalse%2C%22subjectType%22%3A2%2C%22showDateTime%22%3Anull%2C%22lessonId%22%3A0%2C%22classId%22%3Anull%2C%22segmentId%22%3Anull%2C%22weekId%22%3Anull%2C%22unlockType%22%3Anull%2C%22videoInfo%22%3Anull%2C%22finishInfo%22%3Anull%2C%22newGetInfo%22%3Anull%2C%22sensorData%22%3Anull%2C%22trialCourse%22%3Anull%2C%22lessonServiceList%22%3Anull%2C%22startClassTime%22%3Anull%2C%22vipMessage%22%3Anull%2C%22vipJumpLink%22%3Anull%2C%22clientJumpUrl%22%3Anull%2C%22clientCoverUrl%22%3Anull%2C%22activateStatus%22%3A0%7D%5D%7D";
    controller = PlanStudyPreferenceCtrl(
      classId: 68191,
        courseId: 21,
        dataJsonString: Uri.decodeComponent(json)
    );
  });

  group('plan_study_preference_controller tests:', () {

    WidgetsFlutterBinding.ensureInitialized();

    test('setPopInfo', () {
      controller.setPopInfo();
      expect(controller.state.serviceItem != null, true);
    });

  });
}
